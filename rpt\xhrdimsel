#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::Reports;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $rptID = $q->param('rptID');
  $visID = $q->param('v');
  $dim = $q->param('d');
  $item = $q->param('item');
  $state = $q->param('state');

  #if the user is a viewer, update the viewer visual selection table
  if ($acctType == 0)
  {
    $visualSelTable = "app.visuals_viewers";
  }
  else
  {
    $visualSelTable = "app.visuals";
  }

  #get the cube column name for the dimension we're working with
  if ($dim eq "p")
  {
    $dimItemsDB = "products";
    $selItemsDB = "selProducts";
    $dimName = "Products";
    $multiSelectIdx = 0;
    $lockStr = ",lockProducts:1,";
  }
  elsif ($dim eq "g")
  {
    $dimItemsDB = "geographies";
    $selItemsDB = "selGeographies";
    $dimName = "Geographies";
    $multiSelectIdx = 1;
    $lockStr = ",lockGeographies:1,";
  }
  elsif ($dim eq "t")
  {
    $dimItemsDB = "timeperiods";
    $selItemsDB = "selTimeperiods";
    $dimName = "Time Periods";
    $multiSelectIdx = 2;
    $lockStr = ",lockTimes:1,";
  }
  elsif ($dim eq "m")
  {
    $dimItemsDB = "measures";
    $selItemsDB = "selMeasures";
    $dimName = "Measures";
    $multiSelectIdx = 3;
    $lockStr = ",lockMeasures:1,";
  }

  #connect to the database
  $db = KAPutil_connect_to_database();

  #if we're being told to update the selection for a specific visualization
  undef(@visIDs);
  if ($visID > 0)
  {
    $query = "SELECT design FROM app.visuals WHERE ID=$visID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($design) = $dbOutput->fetchrow_array;

    #if the visual's data selection isn't locked for this dimension, add it
    if (!($design =~ m/$lockStr/))
    {
      push(@visIDs, $visID);
    }
  }

  #else we're updating the selection for all visualizations
  else
  {
    $query = "SELECT ID, design FROM app.visuals WHERE cubeID=$rptID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($visID, $design) = $dbOutput->fetchrow_array)
    {

      #if the visual's data selection isn't locked for this dimension, add it
      if (!($design =~ m/$lockStr/))
      {
        push(@visIDs, $visID);
      }
    }
  }

  #foreach visual we're updating selections for
  foreach $visID (@visIDs)
  {

    #handle full-dimension selects/deselects (e.g., checkbox next to "Products")
    if ($item eq "Dimension")
    {

      #if the user checked the check-all checkbox
      if ($state eq "true")
      {
        $query = "SELECT $dimItemsDB FROM app.cubes WHERE ID=$rptID";
        $dbOutput = $db->prepare($query);
        $status = $dbOutput->execute;
        KAPutil_handle_db_err($db, $status, $query);
        ($itemStr) = $dbOutput->fetchrow_array;

        $query = "UPDATE $visualSelTable SET $selItemsDB = '$itemStr' \
            WHERE ID=$visID";
        $status = $db->do($query);
        KAPutil_handle_db_err($db, $status, $query);
      }

      #elsif they unchecked the checl-all checkbox
      elsif ($state eq "false")
      {
        $query = "UPDATE $visualSelTable SET $selItemsDB = '' WHERE ID=$visID";
        $status = $db->do($query);
        KAPutil_handle_db_err($db, $status, $query);
      }

      next;
    }


    #            -----------------------------------

    #NB: Everything after this point handles a single checkbox being clicked

    #get multi-select state for the dimension we're working with
    @multiStates = reports_multiselect($db, $rptID, $visID);
    $multistate = $multiStates[$multiSelectIdx];

    #get current selected item list
    $query = "SELECT $selItemsDB FROM $visualSelTable WHERE ID=$visID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($selItemsStr) = $dbOutput->fetchrow_array;

    #split selected items into an array
    @selItems = split(',', $selItemsStr);

    #if we're adding the item to the selection string
    if ($state eq "true")
    {

      #if multistate is 1, only 1 item from this dimension can be selected
      if ($multistate == 1)
      {
        @selItems = ($item);
      }

      #else we're adding to a multiselection
      else
      {

        #make sure the item isn't already in the selection array
        @tmp = grep{/^$item$/} @selItems;
        if (scalar(@tmp) < 1)
        {
          #add specified item to selection array
          push(@selItems, $item);
        }
      }
    }

    #if we're removing the item from the selection string
    if ($state eq "false")
    {

      #find and remove the specified item
      $idx = 0;
      foreach $selID (@selItems)
      {
        if ($selID eq $item)
        {
          splice(@selItems, $idx, 1);
        }

        $idx++;
      }
    }

    #don't let a dimension get set to nothing
    #NB: this only comes up when a slightly rogue browser sends the
    #    uncheck/check requests in reverse order for a single-state dimension
    if (($multistate == 1) && (@selItems < 1))
    {
      next;
    }


    #convert selection array back into a string, and store in database
    $selectionStr = join(',', @selItems);
    $q_selbaseitems = $db->quote($selectionStr);
    $query = "UPDATE $visualSelTable SET $selItemsDB = $q_selbaseitems \
        WHERE ID=$visID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);
  }

  print("OK\n");


#EOF
