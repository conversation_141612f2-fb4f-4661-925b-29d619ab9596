#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;
use Text::CSV;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($kapDB);

  print <<END_HTML;
<DOCCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Exporting Compressed CSV</TITLE>
<LINK REL="SHORTCUT ICON" HREF="$Lib::KoalaConfig::kapHostURL/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>

let statusTimer = setInterval(function(){displayStatus()}, 1000);
let statCount = 0;

function displayStatus()
{
  const url = 'ajaxAPI.cld?svc=job_op_status&j=$jobID';

  \$.get(url, function(data, status)
  {
    let statusText = data;
    let statElements = statusText.split('|');

    if (statusText.search('DONE') == 0)
    {
      \$('#progress-bar').css('width', '100%');
      clearInterval(statusTimer);
      document.getElementById('download_ready').style.visibility = 'visible';
      document.getElementById('progress-div').style.visibility = 'hidden';
      document.getElementById('div-op-title').innerHTML = "";
      document.getElementById('div-op-details').innerHTML = "";
      document.getElementById('div-op-extra').innerHTML = "";
      return;
    }

    let opTitle = statElements[0];
    let opPct = statElements[1];
    let opDetails = statElements[2];
    let opTimeEstimate = statElements[3];
    let opExtra = statElements[4];
    document.getElementById('div-op-title').innerHTML = opTitle;
    if (opPct.length > 0)
    {
      document.getElementById('progress-bar').innerHTML = opPct + '%';
      \$('#progress-bar').css('width', opPct+'%');
    }
    document.getElementById('div-op-details').innerHTML = opDetails;
    document.getElementById('div-op-extra').innerHTML = opExtra;

    if (statCount > 5)
    {
      clearInterval(statusTimer);
      statusTimer = setInterval(function(){displayStatus()}, 5000);
    }
    statCount++;
  });
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($kapDB, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="$Lib::KoalaConfig::kapHostURL/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?f=$flowID">Data Prep</A></LI>
    <LI CLASS="breadcrumb-item">$flowName</LI>
    <LI CLASS="breadcrumb-item active">Export to CSV</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------
#
# Output the HTML for the web page that displays the status dialog
#

sub print_status_html
{
  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-11 col-md-8 col-lg-6 col-xl-5"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Exporting Data to Compressed CSV</DIV>
        <DIV CLASS="card-body">

          <H5 ID="div-op-title"></H5>
          <DIV CLASS="progress" ID="progress-div" style="height:25px;">
            <DIV ID="progress-bar" CLASS="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" STYLE="width:0%;">
            </DIV>
          </DIV>

          <P>
          <DIV ID="div-op-details"></DIV>
          <DIV ID="div-op-extra"></DIV>

          <DIV ID="download_ready" STYLE="visibility: hidden;">
            <P>
            <DIV CLASS="text-center">
              The data has been exported as a compressed CSV file.
            </DIV>

            <P>
            <DIV CLASS="text-center">
              <BUTTON CLASS="btn btn-success" TYPE="button" onClick="location.href='$zipURL'"><SPAN CLASS="bi bi-download"></SPAN> Download</BUTTON>
            </DIV>
            </P>

            <DIV CLASS="text-center">
              <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='flowViewData.cld?f=$flowID&j=$jobID'"><SPAN CLASS="bi bi-check-lg"></SPAN> Done</BUTTON>
            </DIV>

          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get the CGI input variables
  $exportDest = $q->param('dest');
  $flowID = $q->param('f');
  $jobID = $q->param('j');

  #connect to the database
  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  $flowName = prep_flow_id_to_name($prepDB, $flowID);

  print_html_header();

  #make sure we have read privs for this data flow
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to export this data flow.");
  }

  prep_audit($prepDB, $userID, "Exported data to CSV", $flowID);
  utils_slack("PREP: $first $last exported data to CSV in $flowName");

  #build file names
  $tmp = $flowName;
  $tmp =~ s/\s/_/g;
  $tmp =~ s/\W//g;
  $tmp = substr($tmp, 0, 50);
  $filename = "koala-prep-export_" . $tmp . "_$jobID" . ".csv";
  $zipFile = "koala-prep-export_" . $tmp . "_$jobID" . ".zip";
  $zipURL = "/tmp/$zipFile";

  #fork a new process to do the actual data dumping in the background
  $SIG{CHLD} = "IGNORE";
  if ($pid = fork())
  {
    #parent process

    print_status_html();
  }
  else
  {
    #child process

    #let Apache know not to wait on the child process
    close(STDIN);
    close(STDOUT);

    #redirect STDERR to the Koala error log
    close(STDERR);
    open(STDERR, ">>/opt/apache/htdocs/tmp/koala_error.log");
    select(STDERR);
    $| = 1;

    chdir("/opt/apache/htdocs/tmp/");

    #reconnect to the database
    $kapDB = KAPutil_connect_to_database();
    $prepDB = PrepUtils_connect_to_database();

    flow_telemetry($prepDB, $jobID, "Started export of data to compressed CSV");

    $query = "UPDATE prep.jobs SET PID=$$, opInfo='0|Exporting data' WHERE ID=$jobID";
    $prepDB->do($query);
    PrepUtils_set_job_op_title($prepDB, $jobID, "Exporting data to CSV");
    PrepUtils_set_job_op_pct($prepDB, $jobID, "0");

    $csv = Text::CSV->new( {binary => 1} );

    #build master table name strings
    $masterTable = "prep_data.$jobID" . "_master";
    $masterColTable = "prep_data.$jobID" . "_master_cols";

    #open CSV output file that we're going to dump data to
    open(OUTPUT, ">$filename");

    #get IDs of columns in display order
    @orderedCols = prep_flow_order_columns($prepDB, $flowID, $jobID);
    $colOrder = join(',', @orderedCols);

    #get the list of data columns we're exporting
    $query = "SELECT ID, name, type FROM $masterColTable ORDER BY FIELD(ID, $colOrder)";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;

    undef(@headerCols);
    undef($subQ);
    while (($colID, $colName, $type) = $dbOutput->fetchrow_array)
    {
      $subQ .= "column_$colID,";

      #use our data tags to explicitly label column types
      if (($type eq "pseg") || ($type eq "gseg") || ($type eq "tseg"))
      {
        $colName = $type . ":" . $colName;
      }
      elsif (($type eq "palias") || ($type eq "galias") || ($type eq "talias"))
      {
        $colName = $type . ":" . $colName;
      }
      elsif (($type eq "pattr") || ($type eq "gattr") || ($type eq "tattr"))
      {
        $colName = $type . ":" . $colName;
      }
      elsif ($type eq "upc")
      {
        $colName = "UPC";
      }
      elsif ($type eq "product")
      {
        $colName = "Product";
      }
      elsif ($type eq "geography")
      {
        $colName = "Geography";
      }
      elsif ($type eq "time")
      {
        $colName = "Time";
      }

      push(@headerCols, $colName);
    }
    chop($subQ);

    #output the header line to the output CSV file
    $csv->print(OUTPUT, \@headerCols);
    print OUTPUT "\n";

    #get the number of rows to be exprted
    $query = "SELECT rowCount FROM prep.jobs WHERE ID=$jobID";
    $dbOutput = $prepDB->prepare($query);
    $status = $dbOutput->execute;
    ($totalRows) = $dbOutput->fetchrow_array;

    #get the highest ID number we're going to use for exporting
    $query = "SELECT MAX(ID) FROM $masterTable";
    $dbOutput = $prepDB->prepare($query);
    $status = $dbOutput->execute;
    ($maxID) = $dbOutput->fetchrow_array;

    $rowsDone = 0;
    $start = 0;
    PrepUtils_set_job_op_details($prepDB, $jobID, "Writing data to CSV file");
    while ($start <= $maxID)
    {
      $end = $start + 100_000;

      #select the data from the data file
      $query = "SELECT $subQ FROM $masterTable WHERE ID > $start AND ID <=$end";
      $dbOutput = $prepDB->prepare($query);
      $status = $dbOutput->execute;

      #output every line as CSV
      while (@dataVals = $dbOutput->fetchrow_array)
      {

        if (($rowsDone % 5000) == 0)
        {
          $pct = ($rowsDone / $totalRows) * 100;
          $pct = int($pct);
          $query = "UPDATE prep.jobs SET opInfo='$pct|Exporting data' WHERE ID=$jobID";
          $prepDB->do($query);
          $rowsRemaining = $totalRows - $rowsDone;
          $rowsRemaining = prep_autoscale_number($rowsRemaining);
          PrepUtils_set_job_op_extra($prepDB, $jobID, "<B>Records remaining:</B> $rowsRemaining");
          PrepUtils_set_job_op_pct($prepDB, $jobID, $pct);
       }

        $csv->print(OUTPUT, \@dataVals);
        print OUTPUT "\n";

        $rowsDone++;
      }

      $start = $end;
    }

    #close the CSV output file
    close(OUTPUT);

    #compress the output file for download
    unlink("$zipFile");
    `/usr/bin/zip $zipFile $filename`;
    unlink("$filename");

    flow_telemetry($prepDB, $jobID, "Finished export of data to compressed CSV");

    $query = "UPDATE prep.jobs SET PID=NULL, opInfo='DONE', lastAction=NOW() WHERE ID=$jobID";
    $prepDB->do($query);

    #clear detailed op stats
    PrepUtils_set_job_op_title($prepDB, $jobID, "");
    PrepUtils_set_job_op_details($prepDB, $jobID, "");
    PrepUtils_set_job_op_extra($prepDB, $jobID, "");
    PrepUtils_set_job_op_pct($prepDB, $jobID, "");
  }

#EOF
