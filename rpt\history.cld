#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Report History</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function showModal(resource)
{
  \$('#modal-telemetry .modal-content').html('');
  \$('#modal-telemetry').modal('show');
  \$('#modal-telemetry .modal-content').load(resource);
}
</SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/rpt/main">Reports</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?rpt=$rptID">$rptName</A></LI>
    <LI CLASS="breadcrumb-item active">History</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser and get the forecast to be deleted
  $q = new CGI;
  $rptID = $q->param('rptID');

  if ($rptID =~ m/^(\d+)\,.*/)
  {
    $rptID = $1;
  }

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  $db = KAPutil_connect_to_database();

  #get the report's name and last update from the database
  $query = "SELECT dsID, name, lastUpdate, lastViewed, lastCloudBacked, lastExcelExport \
      FROM app.cubes WHERE ID=$rptID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($dsID, $name, $lastUpdate, $lastViewed, $lastCloudBacked, $lastExcelExport) = $dbOutput->fetchrow_array;

  #get info about the report's backing data source
  $query = "SELECT lastUpdate, lastModified FROM dataSources WHERE ID=$dsID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($lastDSUpdate, $lastModified) = $dbOutput->fetchrow_array;

  $rptName = cube_id_to_name($db, $rptID);

  print_html_header();

  #make sure we have read privs for this data cube
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to view this report's history.");
  }

  %userNameHash = utils_get_user_hash($db);
  $dsName = ds_id_to_name($db, $dsID);

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-12 col-lg-11 col-xl-9"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Report History</DIV>
        <DIV CLASS="card-body">

          <P>
          <TABLE CLASS="table table-striped table-sm table-bordered w-50">
            <TR>
              <TD STYLE='text-align:right; font-weight:bold;'>
                Last Report Refresh:
              </TD>
              <TD STYLE='text-align:left;'>
                $lastUpdate
              </TD>
            </TR>
            <TR>
              <TD STYLE='text-align:right; font-weight:bold;'>
                Last Data Source Update:
              </TD>
              <TD STYLE='text-align:left;'>
                $lastDSUpdate
              </TD>
            </TR>
            <TR>
              <TD STYLE='text-align:right; font-weight:bold;'>
                Last Data Source Modification:
              </TD>
              <TD STYLE='text-align:left;'>
                $lastModified
              </TD>
            </TR>
          </TABLE>

          <P>
          <TABLE CLASS="table table-striped table-sm table-bordered w-50">
            <TR>
              <TD STYLE='text-align:right; font-weight:bold;'>
                Last Viewed in Koala:
              </TD>
              <TD NOWRAP STYLE='text-align:left;'>
                $lastViewed
              </TD>
            </TR>
            <TR>
              <TD STYLE='text-align:right; font-weight:bold;'>
                Last Viewed by Cloud-Backed Report:
              </TD>
              <TD STYLE='text-align:left;'>
                $lastCloudBacked
              </TD>
            </TR>
            <TR>
              <TD STYLE='text-align:right; font-weight:bold;'>
                Last Exported to Excel:
              </TD>
              <TD STYLE='text-align:left;'>
                $lastExcelExport
              </TD>
            </TR>
          </TABLE>

          <P>
          <DIV CLASS="table-responsive" STYLE="height:400px; overflow:auto;">
            <TABLE CLASS="table table-striped table-sm">
END_HTML

  #grab all of the activity info related to this report
  $query = "SELECT timestamp, userID, action FROM audit.userActions \
      WHERE rptID=$rptID ORDER BY timestamp DESC";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  #output HTML row for every line in activity audit log
  while (($timestamp, $userID, $action) = $dbOutput->fetchrow_array)
  {
    if ($action =~ m/Refreshed report\|(\d+)$/)
    {
      $action = "Refreshed report <A HREF='#' CLASS='text-decoration-none' onClick=\"showModal('xhrCubeTelemetry?c=$rptID&i=$1')\">View Log</A>";
    }

    print <<END_HTML;
              <TR>
                <TD NOWRAP>$timestamp</TD>
                <TD NOWRAP>$userNameHash{$userID}</TD>
                <TD>$action</TD>
              </TR>
END_HTML
  }

  print <<END_HTML;
            </TABLE>
          </DIV>

          <P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='display.cld?rpt=$rptID'"><I CLASS="bi bi-check-lg"></I> Done</BUTTON>
          </DIV>

        </DIV>
      </DIV>

      <DIV id="modal-telemetry" class="modal" role="dialog">
        <DIV CLASS="modal-dialog modal-lg" STYLE="overflow-y: initial !important;">
          <DIV CLASS="modal-content">
          </DIV>
        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
<P>
END_HTML

  print_html_footer();

  $activity = "$first $last viewed history for $rptName in $dsName";
  utils_slack($activity);


#EOF
