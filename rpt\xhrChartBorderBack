#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::Reports;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $rptID = $q->param('rptID');
  $visID = $q->param('v');
  $bgColor = $q->param('bgColor');
  $showBorder = $q->param('showBorder');
  $borderColor = $q->param('borderColor');
  $borderThickness = $q->param('borderThickness');

  #fix up the CGI parameters from the submitted form
  if (defined($showBorder))
  {
    $showBorder = ($showBorder eq "false") ? "0" : "1";
  }
  $bgColor = "#" . $bgColor;
  $borderColor = "#" . $borderColor;

  $db = KAPutil_connect_to_database();

  #make sure we have edit privs for this report
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this report.");
  }

  $cubeName = cube_id_to_name($db, $rptID);
  $dsID = cube_get_ds_id($db, $rptID);
  $dsName = ds_id_to_name($db, $dsID);

  #get the chart border/background details from the database
  $query = "SELECT design FROM visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($graphDesign) = $dbOutput->fetchrow_array;


  ########################################################################
  #
  # This code block is called on submit to save the changes
  #

  #if we're being called to save updated chart titles
  if (defined($showBorder))
  {
    if (lc($bgColor) ne reports_chart_design_default("bgColor"))
    {
      $graphDesign = reports_set_style($graphDesign, "bgColor", $bgColor);
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "bgColor");
    }

    if ($showBorder ne reports_chart_design_default("showBorder"))
    {
      $graphDesign = reports_set_style($graphDesign, "showBorder", $showBorder);
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "showBorder");
    }

    if (lc($borderColor) ne reports_chart_design_default("borderColor"))
    {
      $graphDesign = reports_set_style($graphDesign, "borderColor", $borderColor);
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "borderColor");
    }

    if ($borderThickness ne reports_chart_design_default("borderThickness"))
    {
      $graphDesign = reports_set_style($graphDesign, "borderThickness", $borderThickness);
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "borderThickness");
    }

    $q_graphDesign = $db->quote($graphDesign);
    $query = "UPDATE visuals SET design = $q_graphDesign WHERE ID=$visID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    utils_audit($db, $userID, "Changed chart border/background", $dsID, $rptID, 0);
    $activity = "$first $last changed chart border/background for $cubeName in $dsName";
    utils_slack($activity);

    exit;
  }


  ########################################################################
  #
  # Everything after this point is called to display the chart layout dialog
  #

  #extract graph captions from design string
  $graphBgColor = reports_get_style($graphDesign, "bgColor");
  $graphShowBorder = reports_get_style($graphDesign, "showBorder");
  $graphBorderColor = reports_get_style($graphDesign, "borderColor");
  $graphBorderThickness = reports_get_style($graphDesign, "borderThickness");

  if (!(defined($graphBgColor)))
  {
    $graphBgColor = "#ffffff";
  }
  if (!(defined($graphBorderColor)))
  {
    $graphBorderColor = "#cccccc";
  }
  if (!(defined($graphBorderThickness)))
  {
    $graphBorderThickness = "1";
  }

  $graphShowBorder = ($graphShowBorder eq "1") ? "CHECKED" : "";

  print <<END_HTML;
<SCRIPT>
function submitForm()
{
  let bgColor = document.getElementById('bgColor').value;
  let showBorder = \$("#showBorder").prop("checked");
  let borderColor = document.getElementById('borderColor').value;
  let borderThickness = document.getElementById('borderThickness').value;

  //knock # off of color strings
  bgColor = bgColor.substr(1);
  borderColor = borderColor.substr(1);

  let url = "xhrChartBorderBack?rptID=$rptID&v=$visID&bgColor=" + bgColor +
      "&showBorder=" + showBorder + "&borderColor=" + borderColor +
      "&borderThickness=" + borderThickness;

  \$.get(url, function(data, status)
  {
    location.href = "display.cld?rpt=$rptID&v=$visID";
  });
}



function revertDefaults()
{
  document.getElementById('bgColor').value = "#ffffff";
  document.getElementById('showBorder').checked = false;
  document.getElementById('borderColor').value = "#cccccc";
  document.getElementById('borderThickness').value = 1;
}
</SCRIPT>

<DIV CLASS="modal-dialog" STYLE="overflow-y: initial !important;">
  <DIV CLASS="modal-content">
    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Chart Background & Border</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">
      <FORM>
      <TABLE CLASS="mx-auto">
        <TR>
          <TD STYLE="text-align:right;">
            Background color:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="color" NAME="bgColor" ID="bgColor" VALUE="$graphBgColor"></DIV>
          </TD>
        </TR>

        <TR>
          <TD>&nbsp;</TD><TD>&nbsp;</TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Show a border around the chart:&nbsp;
          </TD>
          <TD>
            <DIV CLASS="form-check form-switch">
              <INPUT TYPE="checkbox" CLASS="form-check-input" NAME="showBorder" ID="showBorder" data-offstyle="secondary" $graphShowBorder>
              <LABEL CLASS="form-check-label" FOR="showBorder">&nbsp;</LABEL>
            </DIV>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Border color:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="color" NAME="borderColor" ID="borderColor" VALUE="$graphBorderColor"></DIV>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Border thickness:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="number" NAME="borderThickness" ID="borderThickness" STYLE="width:50px;" VALUE="$graphBorderThickness" min=0>
          </TD>
        </TR>

        <TR>
          <TD COLSPAN="2" STYLE="text-align:center;">
            <P></P>
            <A CLASS="text-decoration-none" HREF="#" onClick="revertDefaults()">Revert to default</A>
          </TD>
        </TR>
      </TABLE>
      </FORM>

    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="submitForm()"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML

#EOF
