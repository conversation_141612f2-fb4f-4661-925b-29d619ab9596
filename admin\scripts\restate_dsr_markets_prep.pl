#!/usr/bin/perl

#
# Restates all specified geographies in DSR data sources that are fed by
# Data Prep (specifically, anything that's being fed with data from a
# Nielsen AOD Intelligent Data Warehouse).
#

use lib "/opt/apache/app/";

use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::PrepUtils;


#hash of geographies to be restated
%restateGeosHash = (
  "ALBSCO Acme Div Rem" => "ALBSCO Acme Rem",
  "ALBSCO Acme Div TA" => "ALBSCO Acme TA",
  "ALBSCO Acme Div xAOC Rem" => "ALBSCO Acme xAOC Rem",
  "ALBSCO Eastern Div Rem" => "ALBSCO Eastern Rem",
  "ALBSCO Eastern Div TA" => "ALBSCO Eastern TA",
  "ALBSCO Eastern Div xAOC Rem" => "ALBSCO Eastern xAOC Rem",
);

  #connect to the database
  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #grab the ID and name of every data source being fed by Prep from the AOD
  #IDW on the system
  $query = "SELECT DISTINCT dsID FROM prep.flows \
      WHERE sourceInfo LIKE 'FTP=nielsen|%' AND dsID > 0";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  $dsStr = "";
  while (($dsID) = $dbOutput->fetchrow_array)
  {
    $dsStr .= "$dsID,";
  }
  chop($dsStr);

  $query = "SELECT ID, name FROM app.dataSources WHERE ID IN ($dsStr)";
  $dbOutput = $kapDB->prepare($query);
  $dbOutput->execute;
  while (($dsID, $dsName) = $dbOutput->fetchrow_array)
  {
    $dsNameHash{$dsID} = $dsName;
  }

  #cycle through every data source, looking for geographies that match
  foreach $dsID (sort keys %dsNameHash)
  {
    $dsSchema = "datasource_" . $dsID;
    print("\n\n$dsID     $dsNameHash{$dsID}\n");
    print("--------------------------------------------------------------\n");

    #do the actual restatement for each entry we have in our restatement hash
    foreach $oldGeo (keys %restateGeosHash)
    {
      $newGeo = $restateGeosHash{$oldGeo};
      $q_oldGeo = $kapDB->quote($oldGeo);
      $q_newGeo = $kapDB->quote($newGeo);

      #start by finding a matching old geography in the data source
      $query = "SELECT ID FROM $dsSchema.geographies WHERE name = $q_oldGeo";
      $dbOutput = $kapDB->prepare($query);
      $dbOutput->execute;
      ($oldGeoID) = $dbOutput->fetchrow_array;

      #if we didn't find a matching old geography, dump it and move on
      if ($oldGeoID < 1)
      {
        #print("$oldGeo not found, skipped\n");
        next;
      }

      #see if we already have a matching new geography
      #NB: have to do this before renaming the old geography, otherwise we get
      #    its ID as a result. Don't be tempted to move this if refactoring code.
      $query = "SELECT ID FROM $dsSchema.geographies WHERE name = $q_newGeo";
      $dbOutput = $kapDB->prepare($query);
      $dbOutput->execute;
      ($newGeoID) = $dbOutput->fetchrow_array;

      #rename the old geography
      $query = "UPDATE $dsSchema.geographies SET name=$q_newGeo WHERE ID = $oldGeoID";
      $kapDB->do($query);
      print("$oldGeo restated as $newGeo\n");

      #merge in the data if the new geography has already been created
      if ($newGeoID > 0)
      {

        #NB: we're going to ignore duplicate key issues, and then delete anything
        #    that still has the new ID so only the old (restated) ID remains
        $query = "UPDATE IGNORE $dsSchema.facts SET geographyID=$oldGeoID \
            WHERE geographyID=$newGeoID";
        $kapDB->do($query);

        $query = "DELETE FROM $dsSchema.facts WHERE geographyID=$newGeoID";
        $kapDB->do($query);

        #remove the (now duplicate) geography
        $query = "DELETE FROM $dsSchema.geographies WHERE ID=$newGeoID";
        $kapDB->do($query);

        print("Found and merged in existing geography $newGeo\n");
      }
    }
  }

#EOF
