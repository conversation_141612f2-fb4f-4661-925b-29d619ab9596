#!/usr/bin/perl

use lib "/opt/apache/app/";

use DBI;

use Lib::KoalaConfig;
use Lib::PrepFlows;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::PrepUtils;
use Lib::WebUtils;



#
# Output detailed usage statistics for every data flow on the system based on
# the telemetry logs,
#


  $db = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  %prepFlowNameHash = prep_flow_get_name_hash($prepDB);
  %dsNameHash = ds_get_name_hash($db);
  %userNameHash = utils_get_user_hash($db);

  #build a hash of all orgs on the cloud
  $query = "SELECT ID, name FROM app.orgs";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  while (($id, $name) = $dbOutput->fetchrow_array)
  {
    $orgNameHash{$id} = $name;
  }

  #build hash of user org membership
  foreach $orgID (keys %orgNameHash)
  {
    $query = "SELECT ID FROM app.users WHERE orgID=$orgID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    while (($userID) = $dbOutput->fetchrow_array)
    {
      $userOrgHash{$userID} = $orgID;
    }
  }

  #build a hash of who owns each data flow
  $query = "SELECT ID, userID FROM prep.flows";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  while (($flowID, $userID) = $dbOutput->fetchrow_array)
  {
    $flowOwnerHash{$flowID} = $userID;
  }

  #grab a list of every data flow that was run on the system during the
  #target time period
  $query = "SELECT action, flowID FROM prep.audit \
      WHERE action LIKE 'Ran data flow%' AND timestamp LIKE '2021-11-%'";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  while (($action, $flowID) = $dbOutput->fetchrow_array)
  {
    $action =~ m/Ran data flow\|(\d+)/;
    $jobID = $1;
    $ranJobsHash{$jobID} = $flowID;
  }

  #run through the list of jobs, extracting time info
  print("Job ID,Organization,Analyst,Data Flow,Data Source,Start Time,Total Time,Flow Time,Data Source Update Time\n");
  foreach $jobID (keys %ranJobsHash)
  {
    $flowID = $ranJobsHash{$jobID};
    $flowName = $prepFlowNameHash{$flowID};
    $userID = $flowOwnerHash{$flowID};
    $orgID = $userOrgHash{$userID};
    $orgName = $orgNameHash{$orgID};

    $query = "SELECT startTime, telemetry FROM prep.telemetry WHERE jobID=$jobID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($startTime, $telemetry) = $dbOutput->fetchrow_array;

    #split the text block into individual lines
    @lines = split('\n', $telemetry);

    #grab the timestamp off the last line
    $line = $lines[-1];
    if ($line =~ m/cleared job/)
    {
      $line = $lines[-2];
    }
    if ($line =~ m/^(.*?)\:\s/)
    {
      $endTime = $1;

      $query = "SELECT UNIX_TIMESTAMP('$endTime') - UNIX_TIMESTAMP('$startTime')";
      $db_time = $prepDB->prepare($query);
      $db_time->execute;
      $wallTime = $db_time->fetchrow_array;
      $wallTime = $wallTime / 60;
      $wallTime = sprintf("%0.2f", $wallTime);

      #try to find a matching telemetry log in KAP for the update, using the
      #name of the data flow and the date that it looks like the update would
      #have started
      $endTime =~ m/^(\d+\-\d+\-\d+) /;
      $endDay = $1;
      $query = "SELECT dsID, startTime, endTime FROM audit.telemetry_data \
          WHERE startTime LIKE '$endDay%' AND telemetry LIKE '%$flowName%'";
      $db_dsTelemetry = $db->prepare($query);
      $db_dsTelemetry->execute;
      ($dsID, $dsStartTime, $dsEndTime) = $db_dsTelemetry->fetchrow_array;

      $query = "SELECT UNIX_TIMESTAMP('$dsEndTime') - UNIX_TIMESTAMP('$dsStartTime')";
      $db_time = $prepDB->prepare($query);
      $db_time->execute;
      $DSwallTime = $db_time->fetchrow_array;
      $DSwallTime = $DSwallTime / 60;
      $DSwallTime = sprintf("%0.2f", $DSwallTime);

      $dsName = $dsNameHash{$dsID};
      $totalTime = $wallTime + $DSwallTime;

      print("$jobID,$orgName,$userNameHash{$userID},\"$flowName\",\"$dsName\",$startTime,$totalTime,$wallTime,$DSwallTime\n");
    }
  }


#EOF
