#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Export Expanded Report</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$("#btn-submit").prop('disabled', true);
  \$("#btn-submit").text("Please Wait...");
  return(true);
}
</SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/rpt/main">Reports</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?rpt=$rptID">$rptName</A></LI>
   <LI CLASS="breadcrumb-item active">Export Expanded Report</LI>
   </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser and get the report to be expanded
  $q = new CGI;
  $rptID = $q->param('rpt');

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  $db = KAPutil_connect_to_database();

  $rptName = cube_id_to_name($db, $rptID);

  print_html_header();

  #make sure we have write privs for this data cube
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to export this report.");
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-11 col-lg-8 col-xl-7"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Export Expanded Report</DIV>
        <DIV CLASS="card-body">

          <P>
          <FORM METHOD="post" ACTION="exportExpandedItems.cld" onsubmit="return checkForm(this);">
          <INPUT TYPE="hidden" NAME="rpt" VALUE="$rptID">

          <DIV CLASS="row">

            <DIV CLASS="col-auto mt-2">
              I want to expand the
            </DIV>

            <DIV CLASS="col-auto">
              <SELECT CLASS="form-select my-1" NAME="d">
                <OPTION VALUE="p">Products</OPTION>
                <OPTION VALUE="g">Geographies</OPTION>
                <OPTION VALUE="t">Time Periods</OPTION>
                <OPTION VALUE="m">Measures</OPTION>
              </SELECT>
            </DIV>

            <DIV CLASS="col-auto mt-2">
              dimension of my report, and
            </DIV>
          </DIV>

          <DIV CLASS="row">
            <DIV CLASS="col-auto">
              <SELECT CLASS="form-select" NAME="a">
                <OPTION VALUE="d">download it as an Excel file</OPTION>
                <OPTION VALUE="ppt">download it as a PowerPoint deck</OPTION>
              </SELECT>
            </DIV>
          </DIV>

          <P>&nbsp;</P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="location.href='/app/rpt/display.cld?rpt=$rptID'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-submit">Next <I CLASS="bi bi-arrow-right"></I></BUTTON>
          </DIV>
          </FORM>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
