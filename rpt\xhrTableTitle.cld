#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DSRUtils;
use Lib::DataSources;
use Lib::Reports;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $rptID = $q->param('rptID');
  $visID = $q->param('v');
  $tableTitle = $q->param('tableTitle');
  $subcaption = $q->param('subcaption');
  $captionFontColor = $q->param('captionFontColor');
  $captionBgColor = $q->param('captionBgColor');
  $captionAlignment = $q->param('captionAlignment');
  $captionFontSize = $q->param('captionFontSize');
  $captionFont = $q->param('captionFont');

  $captionFontColor = "#" . $captionFontColor;
  $captionBgColor = "#" . $captionBgColor;

  $db = KAPutil_connect_to_database();

  #make sure we have edit privs for this report
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this report.");
  }

  #get the table title details from the database
  $query = "SELECT design FROM visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($tableDesign) = $dbOutput->fetchrow_array;


  ########################################################################
  #
  # This code block is called on submit to save the changes
  #

  if (defined($tableTitle))
  {
    $tableDesign = reports_set_captions($tableDesign, "title", $tableTitle);
    $tableDesign = reports_set_captions($tableDesign, "subcaption", $subcaption);
    $tableDesign = reports_set_style($tableDesign, "captionFontColor", $captionFontColor);
    $tableDesign = reports_set_style($tableDesign, "captionBgColor", $captionBgColor);
    $tableDesign = reports_set_style($tableDesign, "captionAlignment", $captionAlignment);
    $tableDesign = reports_set_style($tableDesign, "captionFontSize", $captionFontSize);
    if ($captionFont eq "Helvetica")
    {
      $tableDesign = reports_remove_style($tableDesign, "captionFont");
    }
    else
    {
      $tableDesign = reports_set_style($tableDesign, "captionFont", $captionFont);
    }

    $q_tableDesign = $db->quote($tableDesign);

    $query = "UPDATE visuals SET design = $q_tableDesign WHERE ID=$visID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    $cubeName = cube_id_to_name($db, $rptID);
    $dsID = cube_get_ds_id($db, $rptID);
    $dsName = ds_id_to_name($db, $dsID);

    utils_audit($db, $userID, "Changed table title to $t", $dsID, $rptID, 0);
    $activity = "$first $last changed table title for $cubeName in $dsName";
    utils_slack($activity);

    exit;
  }


  #########################################################################
  #
  # Everything after this point is called to display the table title dialog
  #

  #extract table title from design string
  if ($tableDesign =~ m/,title:"(.*?)",/)
  {
    $tableTitle = $1;
  }
  if ($tableDesign =~ m/,subcaption:"(.*?)",/)
  {
    $subcaption = $1;
  }

  $captionFontColor = reports_get_style($tableDesign, "captionFontColor");
  $captionBgColor = reports_get_style($tableDesign, "captionBgColor");
  $captionAlignment = reports_get_style($tableDesign, "captionAlignment");
  $captionFontSize = reports_get_style($tableDesign, "captionFontSize");
  $captionFont = reports_get_style($tableDesign, "captionFont");

  #set appropriate defaults
  if (length($captionFontColor) < 7)
  {
    $captionFontColor = "#333333";
  }
  if (length($captionBgColor) < 7)
  {
    $captionBgColor = "#ffffff";
  }
  if (length($captionAlignment) < 2)
  {
    $captionAlignment = "center";
  }
  if ($captionFontSize < 3)
  {
    $captionFontSize = "18";
  }
  if (length($captionFont) < 3)
  {
    $captionFont = "Helvetica";
  }

  print <<END_HTML;
<SCRIPT>
captionAlignment = "$captionAlignment";

function submitForm()
{
  let tableTitle = document.getElementById('tableTitle').value;
  let subcaption = document.getElementById('subcaption').value;
  let captionFontColor = document.getElementById('captionFontColor').value;
  let captionBgColor = document.getElementById('captionBgColor').value;
  let captionFontSize = document.getElementById('captionFontSize').value;
  let captionFont = document.getElementById('captionFont').value;

  //knock # off of color strings
  captionFontColor = captionFontColor.substr(1);
  captionBgColor = captionBgColor.substr(1);

  let url = "xhrTableTitle.cld?rptID=$rptID&v=$visID&tableTitle=" + tableTitle +
      "&subcaption=" + subcaption + "&captionFontColor=" + captionFontColor +
      "&captionBgColor=" + captionBgColor +
      "&captionAlignment=" + captionAlignment +
      "&captionFontSize=" + captionFontSize + "&captionFont=" + captionFont;
  url = encodeURI(url);

  \$.get(url, function(data, status)
  {
    location.href = "display.cld?rpt=$rptID&v=$visID";
  });
}



function alignUI(alignment)
{
  let titleAlignLeft = document.getElementById('titleAlignLeft');
  let titleAlignCenter = document.getElementById('titleAlignCenter');
  let titleAlignRight = document.getElementById('titleAlignRight');

  if (alignment == "left")
  {
    titleAlignLeft.style.color = "blue";
    titleAlignCenter.style.color = "darkgray";
    titleAlignRight.style.color = "darkgray";
  }
  else if (alignment == "center")
  {
    titleAlignLeft.style.color = "darkgray";
    titleAlignCenter.style.color = "blue";
    titleAlignRight.style.color = "darkgray";
  }
  else if (alignment == "right")
  {
    titleAlignLeft.style.color = "darkgray";
    titleAlignCenter.style.color = "darkgray";
    titleAlignRight.style.color = "blue";
  }
  captionAlignment = alignment;
}
</SCRIPT>

<DIV CLASS="modal-dialog" STYLE="overflow-y: initial !important;">
  <DIV CLASS="modal-content">
    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Titles</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">
      <FORM>
      <TABLE>
        <TR>
          <TD STYLE="text-align:right;">Title text:&nbsp;</TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="text" NAME="tableTitle" ID="tableTitle" MAXLENGTH="128" STYLE="width:300px;" VALUE="$tableTitle">
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">Subtitle text:&nbsp;</TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="text" NAME="subcaption" ID="subcaption" MAXLENGTH="128" STYLE="width:300px;" VALUE="$subcaption">
          </TD>
        </TR>

        <TR>
          <TD>&nbsp;</TD>
          <TD>&nbsp;</TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Color:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="color" NAME="captionFontColor" ID="captionFontColor" VALUE="$captionFontColor" STYLE="width:3em;"></DIV>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Background color:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="color" NAME="captionBgColor" ID="captionBgColor" VALUE="$captionBgColor" STYLE="width:3em;"></DIV>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">Alignment:&nbsp;</TD>
          <TD STYLE="text-align:left;">
            <SPAN ID="titleAlignLeft" CLASS="bi bi-text-left" STYLE="font-size:24px; color:darkgray;" onClick="alignUI('left');"></SPAN>
            &nbsp;
            <SPAN ID="titleAlignCenter" CLASS="bi bi-text-center" STYLE="font-size:24px; color:darkgray;" onClick="alignUI('center');"></SPAN>
            &nbsp;
            <SPAN ID="titleAlignRight" CLASS="bi bi-text-right" STYLE="font-size:24px; color:darkgray;" onClick="alignUI('right');"></SPAN>
            <SCRIPT>
              alignUI("$captionAlignment");
            </SCRIPT>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Text size:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="number" NAME="captionFontSize" ID="captionFontSize" STYLE="width:5em;" VALUE="$captionFontSize" min=3>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Font family:&nbsp;
          </TD>
          <TD>
            <SELECT CLASS="form-select" NAME="captionFont" ID="captionFont">
              <OPTION VALUE="Arial">Arial</OPTION>
              <OPTION VALUE="Arial Black">Arial Black</OPTION>
              <OPTION VALUE="Comic Sans MS">Comic Sans MS</OPTION>
              <OPTION VALUE="Courier New">Courier New</OPTION>
              <OPTION VALUE="Helvetica">Helvetica</OPTION>
              <OPTION VALUE="Impact">Impact</OPTION>
              <OPTION VALUE="Tahoma">Tahoma</OPTION>
              <OPTION VALUE="Times New Roman">Times New Roman</OPTION>
              <OPTION VALUE="Verdana">Verdana</OPTION>
            </SELECT>
            <SCRIPT>
              \$("select#captionFont").val("$captionFont");
            </SCRIPT>
          </TD>
        </TR>
      </TABLE>
      </FORM>

    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="submitForm()"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML

#EOF
