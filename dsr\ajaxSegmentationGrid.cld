#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;


#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');
  $dim = $q->param('dim');

  #sanitize parameters
  $dsID = utils_sanitize_integer($dsID);
  if (!defined($dsID))
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
  $dim = utils_sanitize_dim($dim);
  if (!defined($dim))
  {
    print("Status: 403 Not authorized\n");
    exit;
  }

  print("Expires: -1\n");
  print("Cache-Control: private, max-age=0");
  print("Content-type: application/json\n\n");

  $dsSchema = "datasource_" . $dsID;

  $dbStub = KAPutil_get_dim_stub_name($dim);
  $dbName = $dbStub . "seg_rules";

  #connect to the database
  $db = KAPutil_connect_to_database();

  #make sure we have read privs for this data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to view this data source.");
  }

  #get all of the base items in the specified dimension
  %itemNameHash = dsr_get_base_item_name_hash($db, $dsSchema, $dim);
  $itemCount = keys %itemNameHash;

  #get all of the segmentations in the DS for table field purposes
  %segHash = DSRsegmentation_get_segmentations_hash($db, $dsSchema, $dim);

  #build up a hash of each item's segmentation memberships
  foreach $segID (sort {$segHash{$a} cmp $segHash{$b}} keys %segHash)
  {

    $segmentationName = $segHash{$segID};

    #get the segment each product is a member of for the current segmentation
    %segMembershipHash = DSRseg_get_segment_membership_hash($db, $dsSchema, $dim, $segID);

    #get the name of the segments in this segmentation, hashed by ID
    %segmentsHash = DSRseg_get_segments_hash($db, $dsSchema, $dim, $segID);

    #run through each product in the DS, and hash its membership
    foreach $itemID (keys %segMembershipHash)
    {
      $segmentID = $segMembershipHash{$itemID};
      $gridLines{$itemID} .= "  \"$segmentationName\":\"$segmentsHash{$segmentID}\",\n";
    }

    $json .= "{name: '$segHash{$segID}', type: 'text', width:150},\n";
  }

  #trim trailing ,\n from grid lines JSON
  foreach $id (keys %gridLines)
  {
    chop($gridLines{$id});
    chop($gridLines{$id});
  }

  print("[\n");

  $count = 0;
  foreach $itemID (sort {$itemNameHash{$a} cmp $itemNameHash{$b}} keys %itemNameHash)
  {
    print <<JSON_LABEL;
    {
      "ID": $itemID,
      "Item": "$itemNameHash{$itemID}",
      $gridLines{$itemID}
    }
JSON_LABEL

    $count++;
    if ($count < $itemCount)
    {
      print(",");
    }
  }

  print <<JSON_LABEL;
]
JSON_LABEL

#EOF
