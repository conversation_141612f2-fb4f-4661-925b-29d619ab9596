#!/usr/bin/perl

use lib "/opt/apache/app/";

use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#
# Determine if the specified user has any data sources that contain unused
# measures that can possibly be deleted.
#

$USERID=1;

  #connect to the database
  $db = KAPutil_connect_to_database();

  #build a hash of all non-ODBC exported data sources on the cloud
#  $query = "SELECT ID, name FROM app.dataSources WHERE ODBCexport=0 AND userID=$USERID";
  $query = "SELECT ID, name FROM app.dataSources WHERE userID=$USERID";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  while (($id, $name) = $dbOutput->fetchrow_array)
  {
    $dsHash{$id} = $name;
  }

  foreach $dsID (keys %dsHash)
  {
    $dsSchema = "datasource_" . $dsID;
    print("\n\n-------------------------------------------------------\n");
    print("$dsID $dsHash{$dsID}\n");

    #add any measures explicitly used in reports to the used hash
    $query = "SELECT measures FROM app.cubes WHERE dsID=$dsID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    while (($measureStr) = $dbOutput->fetchrow_array)
    {
      @measIDs = split(',', $measureStr);
      foreach $measID (@measIDs)
      {
        $usedMeasHash{$measID} = 1;
      }
    }

    #build a hash of every calculated measure in the data source
    $query = "SELECT ID, name, calculation FROM $dsSchema.measures \
        WHERE NOT ISNULL(calculation)";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    while (($id, $name, $calculation) = $dbOutput->fetchrow_array)
    {
      $unusedMeasHash{$id} = $name;
      $measCalcHash{$id} = $calculation;
    }

    #remove any measures from the unused hash that are explicitly included in reports
    $query = "SELECT measures FROM app.cubes WHERE dsID=$dsID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    while (($measureStr) = $dbOutput->fetchrow_array)
    {
      @measIDs = split(',', $measureStr);
      foreach $measID (@measIDs)
      {
        print STDERR "delete $unusedMeasHash{$measID} ($measID)\n";
        delete($unusedMeasHash{$measID});
      }
    }

    #remove any measures that are used by other calculated measures
    foreach $measID (keys %unusedMeasHash)
    {

      #scan through all of the calculations, seeing if the measure is used by another calculation
    }

    #output list of (possibly) unused measures
    foreach $measID (keys %unusedMeasHash)
    {
      print("Unused calculated measure $unusedMeasHash{$measID} ($measID)\n");
    }
  }

#EOF
