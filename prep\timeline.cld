#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($kapDB);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Data Prep User Timeline</TITLE>
<LINK REL="SHORTCUT ICON" HREF="$Lib::KoalaConfig::kapHostURL/favicon.png">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($kapDB, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="$Lib::KoalaConfig::kapHostURL/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">Data Prep</A></LI>
    <LI CLASS="breadcrumb-item active">My Timeline</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $displayUser = $q->param('u');

  if ($displayUser < 1)
  {
    $displayUser = $userID;
  }

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  print_html_header();

  %flowNameHash = prep_flow_get_name_hash($prepDB);

  #output the panel header HTML
  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col-12"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">My Timeline</DIV>
        <DIV CLASS="card-body">

          <DIV CLASS="table-responsive">
            <TABLE CLASS="table table-sm">
END_HTML

  $query = "SELECT timestamp, DAYNAME(timestamp), MONTHNAME(timestamp), TIME_FORMAT(timestamp, '%h:%i %p'), action, flowID \
      FROM prep.audit \
      WHERE userID=$displayUser AND DATEDIFF(NOW(), timestamp) < 90 \
      ORDER BY timestamp DESC";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;

  while (($timestamp, $dayName, $monthName, $HRtime, $action, $flowID) = $dbOutput->fetchrow_array)
  {

    #don't display records for deleted data sources (except for their actual
    #deletion)
    if (!defined($flowNameHash{$flowID}))
    {
      if ($action =~ m/Deleted data flow (.*)/)
      {
        $HRdataFlow = $1;
      }
      else
      {
        next;
      }
    }
    else
    {
      $HRdataFlow = $flowNameHash{$flowID};
    }

    $timestamp =~ m/(.*) (.*)/;
    $curDate = $1;
    $curTime = $2;

    #if we're starting a new day's output
    if ($lastDate ne $curDate)
    {
      $lastDate = $curDate;
      $curDate =~ m/(\d+)\-(\d+)\-(\d+)/;
      $year = $1;
      $day = $3;
      print <<END_HTML;
      <TR CLASS="table-primary">
        <TD COLSPAN=5><STRONG>$dayName, $monthName $day, $year</STRONG></TD>
      </TR>
END_HTML

      #clear out flow and time blanking code
      $lastHRtime = "";
      $lastHRdataFlow = "";
    }

    #condense duplicate time info
    if ($lastHRtime ne $HRtime)
    {
      $lastHRtime = $HRtime;
      $HRtime = "<TD NOWRAP STYLE='border-right:2px solid #17A2B8; text-align:right; padding-right:0px;'>$HRtime <SPAN STYLE='color:#17A2B8;'><B>&mdash;</B></SPAN></TD>";
    }
    else
    {
      $HRtime = "<TD NOWRAP STYLE='border-right:2px solid #17A2B8;'></TD>";
    }

    #if we're starting output for a new DS, output it as its own row
    if ($lastHRdataFlow ne $HRdataFlow)
    {
      print <<END_HTML;
      <TR>
        <TD>&nbsp;</TD>
        <TD STYLE="border-right:2px solid #17A2B8;"></TD>
        <TD COLSPAN=3 STYLE="text-align:left; padding-left:0px;"><SPAN STYLE='color:#17A2B8;'><B>&mdash;</B></SPAN> <A CLASS="text-decoration-none" HREF="history.cld?f=$flowID">$HRdataFlow</A></TD>
      </TR>
END_HTML
      $lastHRdataFlow = $HRdataFlow;
    }
    else
    {
      $HRdataFlow = "";
    }

    #trim internal job info off actions
    if ($action =~ m/^(.*)\|\d+$/)
    {
      $action = $1;
    }

    print <<END_HTML;
              <TR>
                <TD>&nbsp;</TD>
                $HRtime
                <TD></TD>
                <TD STYLE="width:75px;"></TD>
                <TD>$action</TD>
              </TR>
END_HTML
  }

  print <<END_HTML;
            </TABLE>
          </DIV>

          <DIV CLASS="text-center">
            <BUTTON TYPE="button" CLASS="btn btn-primary" onclick="location.href='main.cld'"><I CLASS="bi bi-check-lg"></I> Done</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
<P>&nbsp;</P>
END_HTML

  print_html_footer();

  utils_slack("PREP: $first $last viewed their Data Prep timeline");

#EOF
