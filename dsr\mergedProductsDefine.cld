#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Merge Products</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/jquery-ui/jquery-ui.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<SCRIPT SRC="/jsgrid/jsgrid.min.js"></SCRIPT>

<LINK REL="stylesheet" HREF="/jquery-ui/jquery-ui.css">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/jsgrid/jsgrid.min.css" REL="stylesheet">
<LINK HREF="/jsgrid/jsgrid-theme.min.css" REL="stylesheet">

<STYLE>
div.grid th
{
  background: #e2e3e5 !important;
}

.selected-row > td
{
  background: #c4e2ff !important;
  border-color: #c4e2ff;
}
</STYLE>
END_HTML

  #determine if the data source has a UPC attribute, and load a hash of it if so
  $query = "SELECT ID from $dsSchema.product_attributes WHERE name='UPC'";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($id) = $dbOutput->fetchrow_array;
  if ($id > 0)
  {
    %upcHash = DSRattr_get_values_hash($db, $dsSchema, "p", $id);
  }

  print <<END_HTML;
<SCRIPT>
let gridData = [
END_HTML

  #grab list of products that aren't currently part of a merged set
  #NB: we're doing this here and directly xmitting to browser for perfomrnace
  #   reasons in data sources with very large numbers of products
  $query = "SELECT ID, IFNULL(alias, name) AS name FROM $dsSchema.products \
      WHERE merged = 0 ORDER BY name";
  $dbOutput = $db->prepare($query);
  $prodCount = $dbOutput->execute;
  $count = 0;
  while (($id, $name) = $dbOutput->fetchrow_array)
  {
    $count++;
    $name =~ s/\'/\\'/g;
    print("{id: '$id', product: '$name', upc: '$upcHash{$id}'}");
    if ($count < $prodCount)
    {
      print(",\n");
    }
  }

  print <<END_HTML;
 ];

\$(document).ready(function()
{
  \$('#mergeGrid').jsGrid(
  {
    width: '100%',
    height: '200px',
    sorting: true,
    autoload: true,
    loadIndication: true,
    multiselect: true,
    confirmDeleting: false,

    data: gridData,

    rowClick: function(args)
    {

      //Shift + selection
      if (args.event.shiftKey)
      {
        document.getSelection().removeAllRanges();

        let i = 0;
        let firstSelection = -1;
        while ((i < this.data.length) && (firstSelection < 0))
        {
          if (this.data[i].selected == 1)
          {
            firstSelection = i;
          }
          i++;
        }

        i = 0;
        let curSelection = -1;
        while ((i < this.data.length) && (curSelection < 0))
        {
          if (args.item.id == this.data[i].id)
          {
            curSelection = i;
          }
          i++;
        }

        clearAllSelections();

        let start, stop;
        if (curSelection > firstSelection)
        {
          start = firstSelection;
          end = curSelection;
        }
        else
        {
          end = firstSelection;
          start = curSelection;
        }

        for (i = start; i <= end; i++)
        {
          this.data[i].selected = 1;
          \$selectedRow = \$('#mergeGrid').jsGrid('rowByItem', this.data[i]).closest('tr');
          \$selectedRow.addClass('selected-row');
        }
      }

      //Ctrl+selection
      else if (event.ctrlKey || event.altKey || event.metaKey)
      {
        args.item.selected = 1;
        \$selectedRow = \$(args.event.target).closest('tr');
        \$selectedRow.addClass('selected-row');
      }

      //single selection
      else
      {
        clearAllSelections();
        args.item.selected = 1;
        \$selectedRow = \$(args.event.target).closest('tr');
        \$selectedRow.addClass('selected-row');
      }
    },

    rowDoubleClick: function(args)
    {
      addItem();
    },

    fields: [
      {name: 'id', type: 'number', visible: false},
      {name: 'product', title: '$dimName', type: 'text', width: 390},
      {name: 'upc', title: 'UPC', type: 'text', width: 120}
    ]

  });
});



function clearAllSelections()
{
  let grid = \$('#mergeGrid').jsGrid('option', 'data');

  for (let i = 0; i < grid.length; i++)
  {
    grid[i].selected = 0;
  }

  \$('#mergeGrid tr').removeClass('selected-row');
}



function getSelectionStr()
{
  let grid = \$('#mergeGrid').jsGrid('option', 'data');
  let selStr = '';

  for (let i = 0; i < grid.length; i++)
  {
    if (grid[i].selected == 1)
    {
      selStr = selStr + grid[i].id + ',';
    }
  }

  return(selStr);
}



function getSelectionIdx()
{
  let grid = \$('#mergeGrid').jsGrid('option', 'data');
  let selStr = '';

  for (let i = 0; i < grid.length; i++)
  {
    if (grid[i].selected == 1)
    {
      selStr = selStr + i + ',';
    }
  }

  return(selStr);
}



function addItem()
{
  let grid = \$('#mergeGrid').jsGrid('option', 'data');
  let mergedObj = document.getElementById('mergedProducts');
  let primaryObj = document.getElementById('primaryProd');
  let i, item, opt, option;

  for (i = grid.length-1; i >= 0; i--)
  {
    if (grid[i].selected == 1)
    {
      itemID = grid[i].id;
      itemName = grid[i].product;

      opt = document.createElement('option');
      opt.text = itemName;
      opt.value = itemID;
      mergedObj.add(opt);

      opt = document.createElement('option');
      opt.text = itemName;
      opt.value = itemID;
      primaryObj.add(opt);

      \$('#mergeGrid').jsGrid('deleteItem', grid[i]);
    }
  }

  if (primaryObj.options.length > 0)
  {
    for (i = 0; i < primaryObj.options.length; i++)
    {
      if (primaryObj.options[i].value == 0)
      {
        primaryObj.remove(i);
      }
    }
  }

  sortSelect(mergedObj);
  sortSelect(primaryObj);
}



function sortSelect(selElem)
{
  let tmpAry = new Array();

  for (let i = 0; i < selElem.options.length; i++)
  {
    tmpAry[i] = new Array();
    tmpAry[i][0] = selElem.options[i].text;
    tmpAry[i][1] = selElem.options[i].value;
  }
  tmpAry.sort();
  while (selElem.options.length > 0)
  {
    selElem.options[0] = null;
  }
  for (let i = 0; i < tmpAry.length; i++)
  {
    let op = new Option(tmpAry[i][0], tmpAry[i][1]);
    selElem.options[i] = op;
  }
}



function removeItem()
{
  let grid = \$('#mergeGrid').jsGrid('option', 'data');
  let mergedObj = document.getElementById('mergedProducts');
  let primaryObj = document.getElementById('primaryProd');
  let isSelected = [];
  let i, j;

  for (i = 0; i < mergedObj.options.length; i++)
  {
    isSelected[i] = mergedObj.options[i].selected;
    if (isSelected[i])
    {
      itemID = mergedObj.options[i].value;
      itemName = mergedObj.options[i].label;

      for (j = 0; j < primaryObj.options.length; j++)
      {
        if (primaryObj.options[j].value == itemID)
        {
          primaryObj.remove(j);
        }
      }

      \$('#mergeGrid').jsGrid('insertItem', {id: itemID, product: itemName});
    }
  }

  i = mergedObj.options.length;
  while (i--)
  {
    if (isSelected[i])
    {
      mergedObj.remove(i);
    }
  }
}



function submitForm()
{
  let form = document.getElementById('mergeForm');
  let mergedObj = document.getElementById('mergedProducts');

  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Merging...');

  for (let i = 0; i < mergedObj.options.length; i++)
  {
    mergedObj.options[i].selected = 'selected';
  }

  form.submit();
}
</SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Merge Products</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');
  $mergedID = $q->param('mergedID');

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsSchema = "datasource_" . $dsID;

  #get the name of the data source
  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  #make sure we have write privs for this data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to merge products in this data source.");
  }

  #make sure the data source isn't locked by another background process
  $ok = DSRutil_operation_ok($db, $dsID, "MERGE-ITEMS");

  if ($ok != 1)
  {
    exit_warning("Another job is currently using this data source - please try again later.")
  }

  #if we're editing an existing merged product
  undef($primaryProd);
  undef($mergedIDsStr);
  if ($mergedID > 0)
  {
    $query = "SELECT primaryProd, mergedProds FROM $dsSchema.products_merged \
        WHERE mergedID = $mergedID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($primaryProd, $mergedIDsStr) = $dbOutput->fetchrow_array;

    @mergedIDs = split(',', $mergedIDsStr);
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-12 col-lg-10 col-xl-8"> <!-- content -->

      <FORM METHOD="post" ID="mergeForm" ACTION="mergedProductsSave.cld">
      <INPUT TYPE="hidden" NAME="ds" VALUE="$dsID">
      <INPUT TYPE="hidden" NAME="mergedID" VALUE="$mergedID">

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Merged Products</DIV>
        <DIV CLASS="card-body">

          <LABEL FOR="primaryProd">Primary product:</LABEL>
          <SELECT CLASS="form-select" NAME="primaryProd" ID="primaryProd" VALUE="$primaryProd" required>
END_HTML

  #if we're editing an existing merged item, let user choose from list of items
  if ($mergedID > 0)
  {
    $query = "SELECT ID, IFNULL(alias, name) AS name FROM $dsSchema.products \
        WHERE ID IN ($mergedIDsStr) ORDER BY name";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;

    while (($id, $name) = $dbOutput->fetchrow_array)
    {
      if ($id == $primaryProd)
      {
        print(" <OPTION SELECTED VALUE=\"$id\">$name</OPTION>\n");
      }
      else
      {
        print(" <OPTION VALUE=\"$id\">$name</OPTION>\n");
      }
    }
  }

  #else if we're new, let the user know they need to add items first
  else
  {
    print(" <OPTION VALUE='0' DISABLED=1>(Add items to be merged, then pick a primary product)\n");
  }

  print <<END_HTML;
          </SELECT>

          <P></P>
          <LABEL FOR="mergedProducts">Merged Products:</LABEL>
          <SELECT CLASS="form-select" ID="mergedProducts" NAME="mergedProducts" SIZE="5" MULTIPLE onDblClick="removeItem()" required>
END_HTML

  #if we're editing an existing item, load up the current merged products
  foreach $id (@mergedIDs)
  {
    $name = KAPutil_get_item_ID_name($db, $dsSchema, "p", $id);
    print(" <OPTION VALUE='$id'>$name</OPTION>\n");
  }

  print <<END_HTML;
          </SELECT>

          <P>
          <CENTER>
            <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="addItem()" TITLE="Add the selected items to the merged product"><I CLASS="bi bi-chevron-up"></I></BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="removeItem()" TITLE="Remove the selected items from the merged product"><I CLASS="bi bi-chevron-down"></I></BUTTON>
          </CENTER>

          <P>
          <DIV id="mergeGrid" CLASS="grid" STYLE="margin:auto; font-size:13px;"></DIV>


          <P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="location.href='display.cld?ds=$dsID&dim=p'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="button" ID="btn-submit" onClick="submitForm()"><I CLASS="bi bi-link"></I> Merge</BUTTON>
          </DIV>

        </DIV>
      </DIV>

      <P>
      </FORM>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
