#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $rptID = $q->param('rptID');

  $db = KAPutil_connect_to_database();

  #make sure we have view privs for this report
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to export this report.");
  }

  #get report's overall geo and time selections
  $query = "SELECT selGeographies, selTimePeriods FROM app.visuals \
      WHERE cubeID=$rptID LIMIT 1";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($geoIDs, $timeIDs) = $dbOutput->fetchrow_array;
  @geoIDs = split(',', $geoIDs);
  $geoID = $geoIDs[0];
  @timeIDs = split(',', $timeIDs);
  $timeID = $timeIDs[0];

  $cubeName = cube_id_to_name($db, $rptID);
  $dsID = cube_get_ds_id($db, $rptID);
  $dsName = ds_id_to_name($db, $dsID);

  utils_audit($db, $userID, "Exported Excel live data link", $dsID, $rptID, 0);
  $activity = "$first $last exported Excel live data link for $cubeName in $dsName";
  utils_slack($activity);

  print <<END_HTML;
<DIV CLASS="modal-dialog" STYLE="overflow-y: initial !important;">
  <DIV CLASS="modal-content">
    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Excel Data Link</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">
      Copy one or more of these links into Excel's Data From Web function to have Excel extract (and automatically update) the data from this report into a worksheet.

      <P>
      <DIV CLASS="text-center">
        <A CLASS="text-decoration-none" HREF="http://$Lib::KoalaConfig::hostname/app/rpt/excel.cld?id=$rptID">All Data In This Report</A>
        <P>&nbsp;</P>
        <A CLASS="text-decoration-none" HREF="http://$Lib::KoalaConfig::hostname/app/rpt/excel.cld?id=$rptID&g=$geoID&t=$timeID">Data Slice</A><BR>
        <A CLASS="text-decoration-none" HREF="http://$Lib::KoalaConfig::hostname/app/rpt/excel.cld?id=$rptID&items=p">Product Dimension Items</A><BR>
        <A CLASS="text-decoration-none" HREF="http://$Lib::KoalaConfig::hostname/app/rpt/excel.cld?id=$rptID&items=g">Geography Dimension Items</A><BR>
        <A CLASS="text-decoration-none" HREF="http://$Lib::KoalaConfig::hostname/app/rpt/excel.cld?id=$rptID&items=t">Time Period Dimension Items</A><BR>
        <P>&nbsp;</P>
        Report ID: <B>$rptID</B>
      </DIV>

    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-primary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-check-lg"></I> Done</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML

#EOF
