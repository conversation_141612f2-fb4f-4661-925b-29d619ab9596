#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepRecipes;
use Lib::PrepUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($kapDB);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Transfer Recipe Steps</TITLE>
<LINK REL="SHORTCUT ICON" HREF="$Lib::KoalaConfig::kapHostURL/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function toggleCheckboxes(source)
{
  let state = source.checked;

  \$('.cbAll').prop('checked', state);
}



function checkForm(form)
{
  \$("#btn-submit").prop('disabled', true);
  \$("#btn-submit").text("Please Wait...");
  return(true);
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($kapDB, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="$Lib::KoalaConfig::kapHostURL/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?f=$flowID">Data Prep</A></LI>
    <LI CLASS="breadcrumb-item">$flowName</LI>
    <LI CLASS="breadcrumb-item active">Transfer Recipe Steps</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $flowID = $q->param('f');

  #connect to the database
  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  $flowName = prep_flow_id_to_name($prepDB, $flowID);

  print_html_header();

  #make sure we have read privs for this data source
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You aren't allowed to access this data flow.");
  }

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #output the panel header HTML
  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-12 col-lg-10 col-xl-8"> <!-- content -->

      <FORM METHOD="POST" ACTION="transferRecipeDo.cld" onsubmit="return checkForm(this);">
      <INPUT TYPE="hidden" NAME="f" VALUE="$flowID">

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Transfer Data Transform Recipe Steps</DIV>
        <DIV CLASS="card-body">

          <P>
          <DIV CLASS="row">
            <DIV CLASS="col-auto">
              Choose the recipe steps you want to transfer from the $flowName data flow into the
            </DIV>
          </DIV>
          <DIV CLASS="row">
            <DIV CLASS="col-9">
              <SELECT CLASS="form-select" NAME="destFlowID" required>
                <OPTION VALUE=""></OPTION>
END_HTML

  @flows = prep_flow_list($prepDB, $kapDB, $userID, $acctType);
  %flowNameHash = prep_flow_get_name_hash($prepDB);
  %flowOwnerHash = prep_flow_get_owner_hash($prepDB);
  foreach $destFlowID (@flows)
  {
    $rights = prep_flow_rights($prepDB, $kapDB, $userID, $destFlowID, $acctType);
    if ($rights eq "W")
    {
      $CSSstyle = "";
      if ($userID == $flowOwnerHash{$destFlowID})
      {
        $CSSstyle = "background:lightcyan;";
      }
      print(" <OPTION STYLE='$CSSstyle' VALUE='$destFlowID'>$flowNameHash{$destFlowID}</OPTION>\n");
    }
  }

  print <<END_HTML;
              </SELECT>
            </DIV>
            <DIV CLASS="col-auto mt-2">
              data flow.
            </DIV>
          </DIV>

          <P>&nbsp;</P>
          <DIV CLASS="table-responsive">
            <TABLE CLASS="table table-sm table-striped table-hover">
              <TR>
                <TD>
                  <DIV CLASS="form-check" STYLE="margin-top:0px; margin-bottom:0px;">
                    <INPUT CLASS="form-check-input" TYPE='checkbox' ID="checkAll" onclick="toggleCheckboxes(this)">
                    <LABEL CLASS="form-check-label" FOR="checkAll">&nbsp;</LABEL>
                  </DIV>
                </TD>
                <TD>
                  <B>All</B>
                </TD>
              </TR>
END_HTML

  #grab every recipe step for this flow
  $query = "SELECT step, action FROM prep.recipes WHERE flowID=$flowID ORDER BY step";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;

  while (($step, $action) = $dbOutput->fetchrow_array)
  {

    #get a human-readable version of the recipe step action
    $readable = prep_recipe_step_text($action, 1);

    print <<END_HTML;
              <TR>
                <TD>
                  <DIV CLASS="form-check" STYLE="margin-top:0px; margin-bottom:0px;">
                    <INPUT CLASS="form-check-input cbAll" NAME="S $step" ID="S $step" TYPE="checkbox">
                    <LABEL CLASS="form-check-label" FOR="S $step">&nbsp;</LABEL>
                  </DIV>
                </TD>
                <TD>
                  $readable
                </TD>
              </TR>
END_HTML
  }

  print <<END_HTML;
            </TABLE>
          </DIV>

          <P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="location.href='main.cld?f=$flowID'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-submit">Transfer <I CLASS="bi bi-arrow-right"></I></BUTTON>
          </DIV>

          </FORM>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
<P>
END_HTML

  print_html_footer();


#EOF
