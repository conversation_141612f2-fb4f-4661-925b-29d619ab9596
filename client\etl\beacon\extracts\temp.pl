#!/usr/bin/perl

use lib "/opt/apache/app/";

use Text::CSV_XS;
use DateTime::Duration;
use DBI;

use Lib::KoalaConfig;


#
# Global variables controlling how large we're allowed to scale
#

#NB: On 0.25 IOPS endurance, 2 seems like the best balance of CPU/IO use
$MAXPROCS = 7;



#---------------------------------------------------------------------------
#
# Output log info to console and log file
#


sub DBG
{
  my ($date);

  my ($str) = @_;

  $date = localtime();
  print "$date: $str\n";
  print("$str\n");
}



#---------------------------------------------------------------------------
#
#

sub clear_old_dates
{

  my ($fileStub) = @_;

  $deleteHash{'1 W/E 12/07/19'} = 1;
  $deleteHash{'1 W/E 12/14/19'} = 1;
  $deleteHash{'1 W/E 12/21/19'} = 1;
  $deleteHash{'1 W/E 12/28/19'} = 1;

  $zipFilename = $fileStub . ".zip";
  $factsFilename = $fileStub . ".csv";
  $lookupFilename = $fileStub . "-prodlookup.csv";
  $tempFilename = $fileStub . "-work.csv";

  DBG("Clearing old dates for for $fileStub ($fileCount/$totalFiles)");


  #uncompress the existing facts data
  DBG("Uncompressing $zipFilename");
  `/usr/bin/unzip -o $zipFilename $factsFilename`;

  #rewrite the file, removing any lines that match the list of date strings
  #we want to pull from the history because they're over 108 weeks old or
  #being replaced
  DBG("Removing old dates from $factsFilename");
  open(INPUT, "/data2/beacon/$factsFilename") or die("Unable to open $factsFilename, $!");
  open(OUTPUT, ">/data2/beacon/$tempFilename") or die("Unable to open $tempFilename, $!");

  while ($line = <INPUT>)
  {
    $csv->parse($line);
    @columns = $csv->fields();

    $dateStr = $columns[2];
    if ($deleteHash{$dateStr} < 1)
    {
      print OUTPUT $line;
    }
  }

  close(INPUT);
  close(OUTPUT);

  unlink("/data2/beacon/$factsFilename");
  rename("/data2/beacon/$tempFilename", "/data2/beacon/$factsFilename");

  #compress the updated history file and new product characteristics file
  DBG("Compressing updated history file for $fileStub");
  `/usr/bin/zip $zipFilename $factsFilename`;

  #delete our temporary working files
  unlink("/data2/beacon/$factsFilename");
}



#---------------------------------------------------------------------------


  #get a list of our per-category CSV file root names (we'll use this to
  #construct filenames from this point forward)
  undef(@catFiles);
  opendir(DIRHANDLE, "/data2/beacon");
  while (defined($filename = readdir(DIRHANDLE)))
  {
    if (($filename =~ m/^(.*)\.zip$/i) && (!($filename =~ m/update/i)))
    {
      push(@catFiles, $1);
    }
  }
  $csv = Text::CSV_XS->new( {binary => 1, eol => $/} );



  #
  # Run through each of the historical category files, deleting old time
  # periods, concatenating new data, and recompressing. We're going to do
  # this mostly in-place to avoid messing with any potentially running
  # updates in Data Prep.
  #

  #cycle through each category we have an update for
  $fileCount = 0;
  $totalFiles = scalar(@catFiles);
  $processCount = 0;

  chdir("/data2/beacon");
  foreach $fileStub (@catFiles)
  {

    $fileCount++;

    #if we haven't hit our maximum process limit
    if ($processCount < $MAXPROCS)
    {

      #fire off the child process
      if ($pid = fork)
      {
        #parent process

        #increment count of active processes
        $processCount++;

      }

      #else we're the child process
      else
      {

        clear_old_dates($fileStub);

        exit;
      }

    }

    #wait here until an empty process slot opens up
    if ($processCount >= $MAXPROCS)
    {
      wait();
      $processCount--;
    }

  }

  #wait here until the last rewrite processes finish
  while ($processCount > 0)
  {
    wait();
    $processCount--;
  }


  DBG("Done");


#EOF
