#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;
use Text::CSV;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::WebUtils;


#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($kapDB);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Trim Data</TITLE>
<LINK REL="SHORTCUT ICON" HREF="$Lib::KoalaConfig::kapHostURL/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($kapDB, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="$Lib::KoalaConfig::kapHostURL/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">Data Prep</A></LI>
    <LI CLASS="breadcrumb-item">$flowName</LI>
    <LI CLASS="breadcrumb-item active">Normalize Weights & Measures</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser and get the forecast to be deleted
  $q = new CGI;

  #get the CGI input variables
  $flowID = $q->param('f');
  $jobID = $q->param('j');
  $colID = $q->param('col');
  $action = $q->param('a');
  $step = $q->param('s');

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #get the name of the data source
  $flowName = prep_flow_id_to_name($prepDB, $flowID);

  #determine where the "Cancel" button goes (if the user is editing a recipe from
  #the list of data flows, take them back there)
  if ($jobID < 1)
  {
    $cancelAction = "recipeEdit.cld?f=$flowID";
  }
  else
  {
    $cancelAction = "flowViewData.cld?f=$flowID&j=$jobID";
  }

  print_html_header();

  #make sure we have write privs for this data flow
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this data flow.");
  }

  #if the system is overloaded, have the user try again later
  if ($jobID < 1)
  {
    #don't load restrict if the user is just editing an existing recipe
  }
  else
  {
    $okToRun = prep_flow_available_job_slot($prepDB, $kapDB, $jobID, $userID);
    if (!($okToRun))
    {
      exit_warning("Whoa! It looks like your Data Prep cloud is being overused. Wait a little bit, and then try again.")
    }
  }

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #build name of column containing the text
  $column = "column_" . $colID;

  #if we're creating this recipe trim step inside a job
  if ($jobID > 0)
  {
    #get the current column name for the basis of the split col names
    $query = "SELECT name FROM $masterColTable WHERE ID=$colID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($colName) = $dbOutput->fetchrow_array;
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-11 col-lg-8 col-xl-7"> <!-- content -->

      <FORM METHOD="post" ACTION="transformProgress.cld" onsubmit="return checkForm(this);">
      <INPUT TYPE="hidden" NAME="f" VALUE="$flowID">
      <INPUT TYPE="hidden" NAME="j" VALUE="$jobID">
      <INPUT TYPE="hidden" NAME="col" VALUE="$colID">
      <INPUT TYPE="hidden" NAME="a" VALUE="TRANS-CELL-WEIGHTS">

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Normalize Weights & Measures</DIV>
        <DIV CLASS="card-body">

          <DIV CLASS="row">
            <DIV CLASS="col-auto mt-2">
              Convert any
            </DIV>
            <DIV CLASS="col-auto">
              <SELECT NAME="system" ID="system" CLASS="form-select" onChange="flipPrefs();">
                <OPTION VALUE="standard">metric values to standard values</OPTION>
                <OPTION VALUE="metric">standard values to metric values</OPTION>
              </SELECT>
            </DIV>
          </DIV>

          <P>&nbsp;</P>
          <DIV ID="prefsStd">
            <DIV CLASS="row">
              <DIV CLASS="col-auto mt-2">
                For volume, prefer
              </DIV>
              <DIV CLASS="col-auto">
                <SELECT NAME="volPrefStd" ID="volPrefStd" CLASS="form-select">
                  <OPTION VALUE="floz">fluid ounces</OPTION>
                  <OPTION VALUE="gal">gallons</OPTION>
                </SELECT>
              </DIV>
            </DIV>
            <DIV CLASS="row">
              <DIV CLASS="col-auto mt-2">
                For weight, prefer
              </DIV>
              <DIV CLASS="col-auto">
                <SELECT NAME="wghtPrefStd" ID="wghtPrefStd" CLASS="form-select">
                  <OPTION VALUE="oz">ounces</OPTION>
                  <OPTION VALUE="lbs">pounds</OPTION>
                </SELECT>
              </DIV>
            </DIV>
            <DIV CLASS="row">
              <DIV CLASS="col-auto mt-2">
                For dimensions, prefer
              </DIV>
              <DIV CLASS="col-auto">
                <SELECT NAME="dimPrefStd" ID="dimPrefStd" CLASS="form-select">
                  <OPTION VALUE="in">inches</OPTION>
                  <OPTION VALUE="ft">feet</OPTION>
                </SELECT>
              </DIV>
            </DIV>
          </DIV>
          <DIV ID="prefsMetric">
            <DIV CLASS="row">
              <DIV CLASS="col-auto mt-2">
                For volume, prefer
              </DIV>
              <DIV CLASS="col-auto">
                <SELECT NAME="volPrefMetric" ID="volPrefMetric" CLASS="form-select">
                  <OPTION VALUE="ml">milliliters</OPTION>
                  <OPTION VALUE="l">liters</OPTION>
                </SELECT>
              </DIV>
            </DIV>
            <DIV CLASS="row">
              <DIV CLASS="col-auto mt-2">
                For weight, prefer
              </DIV>
              <DIV CLASS="col-auto">
                <SELECT NAME="wghtPrefMetric" ID="wghtPrefMetric" CLASS="form-select">
                  <OPTION VALUE="g">grams</OPTION>
                  <OPTION VALUE="kg">kilograms</OPTION>
                </SELECT>
              </DIV>
            </DIV>
            <DIV CLASS="row">
              <DIV CLASS="col-auto mt-2">
                For dimensions, prefer
              </DIV>
              <DIV CLASS="col-auto">
                <SELECT NAME="dimPrefMetric" ID="dimPrefMetric" CLASS="form-select">
                  <OPTION VALUE="mm">millimeters</OPTION>
                  <OPTION VALUE="cm">centimeters</OPTION>
                </SELECT>
              </DIV>
            </DIV>
          </DIV>


 <SCRIPT>
 function flipPrefs()
 {
   let system = document.getElementById('system').value;

   if (system == 'standard')
   {
      document.getElementById('prefsStd').style.display = 'block';
      document.getElementById('prefsMetric').style.display = 'none';
   }
   else
   {
     document.getElementById('prefsStd').style.display = 'none';
     document.getElementById('prefsMetric').style.display = 'block';
   }
 }



\$(document).ready(function()
{
  flipPrefs();
});
 </SCRIPT>

          <P>&nbsp;</P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onclick="location.href='$cancelAction'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="submit" ID="btn-submit"><I CLASS="bi bi-pencil-square"></I> Apply</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
