#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  #output Content-type header
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  #output HTML
  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Fix Data Source</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Fix Data Source</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser and get the forecast to be deleted
  $q = new CGI;
  $dsID = $q->param('ds');
  $dim = $q->param('d');
  $action = $q->param('a');

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #sanitize parameters
  $dsID = utils_sanitize_integer($dsID);
  $dim = utils_sanitize_dim($dim);
  $action = utils_sanitize_string($action);

  $dsSchema = "datasource_" . $dsID;

  #set human-readable strings for various actions we perform
  if ($action eq "dedupeprod")
  {
    $actionStr = "de-duplicated products";
    $statusStr = "Products have been de-duplicated";
  }

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  #make sure we have write privs for the data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to modify the data source.");
  }

  $activity = "$first $last fixed data source ($actionStr) $dsName";
  utils_audit($db, $userID, "Fixed data source ($actionStr) $dsName", $dsID, 0, 0);

  if ($action eq "dedupeprod")
  {

    #identify duplicate products
    %prodHash = dsr_get_base_item_name_hash($db, $dsSchema, "p");
    undef(%dupes);
    foreach $prodID (keys %prodHash)
    {
      $name = $prodHash{$prodID};
      $dupes{$name}++;
    }
    foreach $tmp (keys %dupes)
    {
      if ($dupes{$tmp} < 2)
      {
        delete($dupes{$tmp});
      }
    }

    #run through duplicates and merge them
    foreach $prodName (keys %dupes)
    {
      $q_prodName = $db->quote($prodName);
      $query = "SELECT ID FROM $dsSchema.products WHERE name=$q_prodName ORDER BY ID";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      KAPutil_handle_db_err($db, $status, $query);

      #oldest product is the master product by default
      ($parentID) = $dbOutput->fetchrow_array;

      #now merge in each of the children
      while (($childID) = $dbOutput->fetchrow_array)
      {
        $query = "UPDATE IGNORE $dsSchema.facts SET productID=$parentID \
            WHERE productID=$childID";
        $status = $db->do($query);
        KAPutil_handle_db_err($db, $status, $query);

        $query = "DELETE FROM $dsSchema.facts WHERE productID=$childID";
        $status = $db->do($query);
        KAPutil_handle_db_err($db, $status, $query);

        $query = "DELETE FROM $dsSchema.products WHERE ID=$childID";
        $status = $db->do($query);
        KAPutil_handle_db_err($db, $status, $query);
      }
    }
  }

  #let everything know it may need to update
  $query = "UPDATE dataSources SET lastModified=NOW() WHERE ID=$dsID";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-11 col-md-8 col-lg-6 col-xl-5"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Fix Data Source</DIV>
        <DIV CLASS="card-body">

          $statusStr in data source $dsName.

          <P>

          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" onClick="location.href='display.cld?ds=$dsID'"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

  utils_slack($activity);

#EOF
