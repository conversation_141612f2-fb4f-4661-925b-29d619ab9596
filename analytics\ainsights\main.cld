#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName AInsights</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/jsgrid/jsgrid.min.css" REL="stylesheet">
<LINK HREF="/jsgrid/jsgrid-theme.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<SCRIPT SRC="/jsgrid/jsgrid.min.js"></SCRIPT>

<STYLE>
div.grid th
{
  background: #e2e3e5 !important;
}

.selected-row > td
{
  background: #c4e2ff !important;
  border-color: #c4e2ff;
}

.active-row > td
{
  background: #dadada !important;
  border-color: #dadada;
}

</STYLE>

<SCRIPT>
let selectedModel = 0;

let gridHeight = window.innerHeight - 200;

if (gridHeight < 250)
{
  gridHeight = 250;
}

\$(document).ready(function()
{
  \$('#modelGrid').jsGrid(
  {
    width: '100%',
    height: gridHeight,
    sorting: true,
    autoload: true,
    loadIndication: true,

    controller:
    {
      loadData: function (filter)
      {
        let data = \$.Deferred();
        \$.ajax(
        {
          type: 'GET',
          contentType: 'application/json; charset=utf-8',
          url: 'ajaxModelList.cld?$filterURI',
          dataType: 'json'
        }).done(function(response)
        {
          data.resolve(response);
        });
        return data.promise();
      }
    },

    rowClick: function(args)
    {
      selectedModel = args.item.ID;

      \$('#modelGrid tr').removeClass('selected-row');

      \$selectedRow = \$(args.event.target).closest('tr');
      \$selectedRow.addClass('selected-row');
    },

    rowDoubleClick: function(args)
    {
      selectedModel = args.item.ID;

      location.href='insights.cld?pm=' + selectedModel;
    },

    rowClass: function(item, itemIndex)
    {
      if (item.active == 1)
      {
        return 'active-row';
      }
    },

    fields: [
      {name: 'ID', type: 'number', visible: false},
      {name: 'AInsights Model', type: 'text', width: 250},
      {name: 'Data Source', type: 'text', width: 180},
      {name: 'Last Run', type: 'text', width: 100},
      {name: 'Owner', type: 'text', width:125}
    ]

  });
});



function closeWarning()
{
  document.getElementById('warn-select-model').style.display = 'none';
}



function open_model()
{
  if (selectedModel < 1)
  {
    document.getElementById('warn-select-model').style.display = 'block';
    return;
  }

  location.href='insights.cld?pm=' + selectedModel;
}



function edit_model()
{
  if (selectedModel < 1)
  {
    document.getElementById('warn-select-model').style.display = 'block';
    return;
  }

  location.href='modelDataSource.cld?a=e&pm=' + selectedModel;
}



function properties()
{
  if (selectedModel < 1)
  {
    document.getElementById('warn-select-model').style.display = 'block';
    return;
  }

  location.href='modelProperties.cld?pm=' + selectedModel;
}



function refresh_model()
{
  if (selectedModel < 1)
  {
    document.getElementById('warn-select-model').style.display = 'block';
    return;
  }

  location.href='modelRefresh.cld?pm=' + selectedModel;
}



function sharing()
{
  if (selectedModel < 1)
  {
    document.getElementById('warn-select-model').style.display = 'block';
    return;
  }

  location.href='accessControl.cld?pm=' + selectedModel;
}



function delete_model()
{
  if (selectedModel < 1)
  {
    document.getElementById('warn-select-model').style.display = 'block';
    return;
  }

  location.href='deleteModelConfirm.cld?pm=' + selectedModel;
}



function export_insights()
{
  if (selectedModel < 1)
  {
    document.getElementById('warn-select-model').style.display = 'block';
    return;
  }

  location.href='exportInsightsPPTSelections.cld?pm=' + selectedModel;
}



function show_history()
{
  if (selectedModel < 1)
  {
    document.getElementById('warn-select-model').style.display = 'block';
    return;
  }

  location.href='history.cld?pm=' + selectedModel;
}



function show_statistics()
{
  if (selectedModel < 1)
  {
    document.getElementById('warn-select-model').style.display = 'block';
    return;
  }

  location.href='statistics.cld?pm=' + selectedModel;
}
</SCRIPT>

</HEAD>

<BODY>

<DIV ID="warn-select-model" CLASS="alert alert-warning alert-dismissible fade show" role="alert" STYLE="display:none; width:50%; position:absolute; margin-left:25%;">
  <DIV CLASS="text-center"><STRONG>Select an AInsights model to perform this operation on.</STRONG></DIV>
  <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="alert"></BUTTON>
</DIV>

END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-0">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item active">AInsights</LI>
  </OL>
</NAV>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  $filter = $q->param('f');

  #if we weren't passed a filter on the command line, pull the session filter
  if (!defined($filter))
  {
    $filter = $session->param(dsr_UserFilter);
  }

  #if there isn't one in the cookie, either, the default is to filter
  if (!defined($filter))
  {
    $filter = 1;
  }

  if ($filter == 1)
  {
    $filterCbox = "?f=0";
    $filterURI = "user=1";
    $filterChecked = "CHECKED";
    $session->param(dsr_UserFilter, "1");
  }
  else
  {
    $filterCbox = "?f=1";
    $filterURI = "";
    $filterChecked = "";
    $session->param(dsr_UserFilter, "0");
  }

  #connect to user login database
  $db = KAPutil_connect_to_database();

  print_html_header();

  print <<END_HTML;

      <NAV CLASS="navbar navbar-expand-md navbar-light bg-light border">

        <BUTTON CLASS="navbar-toggler" TYPE="button" data-bs-toggle="collapse" data-bs-target="#navbarNavDropdown">
          <SPAN CLASS="navbar-toggler-icon"></SPAN>
        </BUTTON>

        <DIV CLASS="collapse navbar-collapse justify-content-center" ID="navbarNavDropdown">
          <UL CLASS="navbar-nav">
            <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="open_model()" TITLE="Open the selected AInsights model"><I CLASS="bi bi-folder2-open"></I> Open</A></LI>

            <LI CLASS="nav-item"><A CLASS="nav-link" HREF="modelDataSource.cld" TITLE="Create a new AInsights model"><I CLASS="bi bi-plus-lg"></I> New</A></LI>

            <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="edit_model()" TITLE="Edit the selected AInsights model"><I CLASS="bi bi-pencil"></I> Edit</A></LI>

            <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="sharing()" TITLE="Share a model with other users"><I CLASS="bi bi-people"></I> Sharing</A></LI>

            <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="properties()" TITLE="Edit the properties of the selected model"><I CLASS="bi bi-gear"></I> Properties</A></LI>

            <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="delete_model()" TITLE="Delete the selected AInsights model"><I CLASS="bi bi-trash"></I> Delete</A></LI>

            <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="refresh_model()" TITLE="Refresh the models and values for the selected model"><I CLASS="bi bi-arrow-clockwise"></I> Refresh</A></LI>

            <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="export_insights()" TITLE="Export insights to PowerPoint"><I CLASS="bi bi-cloud-download"></I> Export</A></LI>

            <LI CLASS="nav-item dropdown">
              <A CLASS="nav-link dropdown-toggle" HREF="#" ID="navbarDropdownMenuLink" ROLE="button" data-bs-toggle="dropdown">
                <I CLASS="bi bi-three-dots-vertical"></I> More
              </A>
              <UL CLASS="dropdown-menu">
                <LI><A CLASS="dropdown-item" HREF="#" onclick="show_history()">History</A></LI>
                <LI><A CLASS="dropdown-item" HREF="#" onclick="show_statistics()">Statistics</A></LI>
              </UL>
            </LI>
          </UL>
        </DIV>
      </NAV>

<DIV CLASS="container">
  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 col-xxl-12"> <!-- content -->

      <P>
      <DIV CLASS="form-check">
        <INPUT CLASS="form-check-input" NAME="userSources" ID="userSources" TYPE="checkbox" $filterChecked onChange="location.href='$filterCbox'">
        <LABEL CLASS="form-check-label" FOR="userSources">Only show my AInsights models</LABEL>
      </DIV>

      <DIV ID="modelGrid" CLASS="grid" STYLE="margin:auto; font-size:13px;"></DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

  #flush the CGI session info out to storage
  $session->flush();

#EOF
