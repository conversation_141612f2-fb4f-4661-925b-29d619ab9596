#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::AInsights::AInsights;
use Lib::AInsights::Utils;

#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/plain\n\n");

  #get the CGI input variables
  $priceModelID = $q->param('pm');
  $chart = $q->param('c');
  $geoID = $q->param('g');

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsID = AInsights_get_dsID($db, $priceModelID);

  $dsSchema = "datasource_" . $dsID;

  AInsights_Utils_initialize_constants($priceModelID);

  #get displayable names for our DSR dimensions
  %timeperiodNames = dsr_get_item_name_hash($db, $dsSchema, "t");

  $ownBrandID = AInsights_Utils_get_own_brand_id($db, $priceModelID);
  $brandSegID = AInsights_Utils_get_brand_seg_id($db, $priceModelID);

  %brandNameHash = DSRseg_get_segments_hash($db, $dsSchema, "p", $brandSegID);
  $ownBrandName = $brandNameHash{$ownBrandID};

  #get competitive brands
  ($compID1, $compID2) = AInsights_Utils_get_brand_comp_ids($db, $dsSchema, $geoID);
  $compBrand1Name = $brandNameHash{$compID1};
  $compBrand2Name = $brandNameHash{$compID2};


  #----------------- Own Brand/Key Comps Trends -------------------

  #if we're doing the overview line chart
  if ($chart eq "trnd")
  {

    #figure out the chrono-order of the time periods we want to graph
    $query = "SELECT ID, endDate FROM $dsSchema.timeperiods \
        WHERE duration=1 AND type=30 ORDER BY endDate DESC LIMIT 52";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($timeID, $endDate) = $dbOutput->fetchrow_array)
    {
      $timeIDStr .= "'$timeID',";
      push(@orderedTimeIDs, $timeID);

      $endDate =~ m/^(.*?) /;
      $endDateHash{$timeID} = $1;
    }
    chop($timeIDStr);
    @orderedTimeIDs = reverse(@orderedTimeIDs);

    $jsonData = <<JSON_LABEL;
{
  "chart":
  {
    "theme": "zune",
    "animation": "0",
    "drawanchors": "0",
    "yaxisname": "Promoted Units",
    "yaxisnamefontsize": "14",
    "numvdivlines": "10",
    "divlinealpha": "30",
    "labelpadding": "10",
    "labelstep": "4",
    "labelfontsize": "12",
    "labeldisplay": "rotate",
    "slantlabel": "1",
    "legendfontsize": "14",
    "useEllipsesWhenOverflow": "0",
    "yaxisvaluespadding": "10",
    "showvalues": "0"
  },
JSON_LABEL

    #output time periods (X axis values)
    $jsonData .= <<JSON_LABEL;
  "categories": [
    {
      "category": [
JSON_LABEL

    foreach $timeID (@orderedTimeIDs)
    {
    $jsonData .= <<JSON_LABEL;
        {
          "label": "$endDateHash{$timeID}"
        },
JSON_LABEL
    }
    chop($jsonData);  chop($jsonData);

    $jsonData .= <<JSON_LABEL;
      ]
    }
  ],
  "dataset": [

JSON_LABEL

    #output the data set for the category
    undef(%promoValueHash);
    $query = "SELECT timeID, promoUnits FROM $dsSchema.$AInsightsBrandCube \
        WHERE brandID=0 AND geographyID=$geoID AND timeID IN ($timeIDStr)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($timeID, $promoVal) = $dbOutput->fetchrow_array)
    {
      $promoValueHash{$timeID} = $promoVal;
    }

    if ($status > 0)
    {

      $jsonData .= <<JSON_LABEL;
  {
    "seriesname": "Category",
    "data": [
JSON_LABEL

      foreach $timeID (@orderedTimeIDs)
      {
        $promoVal = $promoValueHash{$timeID};
        $jsonData .= "{ \"value\": \"$promoVal\"},\n";
      }
      chop($jsonData);  chop($jsonData);

      $jsonData .= <<JSON_LABEL;
    ]
  }
JSON_LABEL
    }

    #output the data set for our brand (if there is data)
    undef(%promoValueHash);
    $query = "SELECT timeID, promoUnits FROM $dsSchema.$AInsightsBrandCube \
        WHERE brandID=$ownBrandID AND geographyID=$geoID AND timeID IN ($timeIDStr)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($timeID, $promoVal) = $dbOutput->fetchrow_array)
    {
      $promoValueHash{$timeID} = $promoVal;
    }

    if ($status > 0)
    {
      $jsonData .= <<JSON_LABEL;
  ,{
    "seriesname": "$ownBrandName",
    "data": [
JSON_LABEL

      foreach $timeID (@orderedTimeIDs)
      {
        $promoVal = $promoValueHash{$timeID};
        $jsonData .= "{ \"value\": \"$promoVal\"},\n";
      }
      chop($jsonData);  chop($jsonData);

      $jsonData .= <<JSON_LABEL;
    ]
  }
JSON_LABEL
    }

    #output the data set for our first key competitor
    undef(%promoValueHash);
    $query = "SELECT timeID, promoUnits FROM $dsSchema.$AInsightsBrandCube \
        WHERE brandID=$compID1 AND geographyID=$geoID AND timeID IN ($timeIDStr)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($timeID, $promoVal) = $dbOutput->fetchrow_array)
    {
      $promoValueHash{$timeID} = $promoVal;
    }

    if ($status > 0)
    {

      $jsonData .= <<JSON_LABEL;
  ,{
    "seriesname": "$compBrand1Name",
    "data": [
JSON_LABEL

      foreach $timeID (@orderedTimeIDs)
      {
        $promoVal = $promoValueHash{$timeID};
        $jsonData .= "{ \"value\": \"$promoVal\"},\n";
      }
      chop($jsonData);  chop($jsonData);

      $jsonData .= <<JSON_LABEL;
    ]
  }
JSON_LABEL
    }

    #output the data set for our second key competitor
    undef(%promoValueHash);
    $query = "SELECT timeID, promoUnits FROM $dsSchema.$AInsightsBrandCube \
        WHERE brandID=$compID2 AND geographyID=$geoID AND timeID IN ($timeIDStr)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($timeID, $promoVal) = $dbOutput->fetchrow_array)
    {
      $promoValueHash{$timeID} = $promoVal;
    }

    if ($status > 0)
    {

      $jsonData .= <<JSON_LABEL;
  ,{
    "seriesname": "$compBrand2Name",
    "data": [
JSON_LABEL

      foreach $timeID (@orderedTimeIDs)
      {
        $promoVal = $promoValueHash{$timeID};
        $jsonData .= "{ \"value\": \"$promoVal\"},\n";
      }
      chop($jsonData);  chop($jsonData);

      $jsonData .= <<JSON_LABEL;
    ]
  }
JSON_LABEL
    }

    $jsonData .= <<JSON_LABEL;
  ]
}
JSON_LABEL

    print $jsonData;
  }



  #----------------- Own Brand Promo Vehicle Breakdown -------------------

  #if we're doing the vehicle breakdown for our own brand
  if ($chart eq "own_vehicle")
  {

    #figure out the chrono-order of the time periods we want to graph
    $query = "SELECT ID, endDate FROM $dsSchema.timeperiods \
        WHERE duration=1 AND type=30 ORDER BY endDate DESC LIMIT 52";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($timeID, $endDate) = $dbOutput->fetchrow_array)
    {
      $timeIDStr .= "'$timeID',";
      push(@orderedTimeIDs, $timeID);

      $endDate =~ m/^(.*?) /;
      $endDateHash{$timeID} = $1;
    }
    chop($timeIDStr);
    @orderedTimeIDs = reverse(@orderedTimeIDs);

    $jsonData = <<JSON_LABEL;
{
  "chart":
  {
    "theme": "zune",
    "labelstep": "4",
    "labelfontsize": "12",
    "labeldisplay": "rotate",
    "slantlabel": "1",
    "showvalues": "0"
  },
JSON_LABEL

    #output time periods (X axis values)
    $jsonData .= <<JSON_LABEL;
  "categories": [
    {
      "category": [
JSON_LABEL

    foreach $timeID (@orderedTimeIDs)
    {
    $jsonData .= <<JSON_LABEL;
        {
          "label": "$endDateHash{$timeID}"
        },
JSON_LABEL
    }
    chop($jsonData);  chop($jsonData);

    $jsonData .= <<JSON_LABEL;
      ]
    }
  ],
  "dataset": [

JSON_LABEL

    #output the data set for display-only promotions
    undef(%promoValueHash);
    $query = "SELECT timeID, promoDispUnits FROM $dsSchema.$AInsightsBrandCube \
        WHERE brandID=$ownBrandID AND geographyID=$geoID AND timeID IN ($timeIDStr)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($timeID, $promoVal) = $dbOutput->fetchrow_array)
    {
      $promoValueHash{$timeID} = $promoVal;
    }

    if ($status > 0)
    {

      $jsonData .= <<JSON_LABEL;
  {
    "seriesname": "Display Promotion",
    "data": [
JSON_LABEL

      foreach $timeID (@orderedTimeIDs)
      {
        $promoVal = $promoValueHash{$timeID};
        $jsonData .= "{ \"value\": \"$promoVal\"},\n";
      }
      chop($jsonData);  chop($jsonData);

      $jsonData .= <<JSON_LABEL;
    ]
  }
JSON_LABEL
    }

    #output the data set for display and feature promotions
    undef(%promoValueHash);
    $query = "SELECT timeID, promoFeatDispUnits FROM $dsSchema.$AInsightsBrandCube \
        WHERE brandID=$ownBrandID AND geographyID=$geoID AND timeID IN ($timeIDStr)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($timeID, $promoVal) = $dbOutput->fetchrow_array)
    {
      $promoValueHash{$timeID} = $promoVal;
    }

    if ($status > 0)
    {
      $jsonData .= <<JSON_LABEL;
  ,{
    "seriesname": "Display & Feature Promotion",
    "data": [
JSON_LABEL

      foreach $timeID (@orderedTimeIDs)
      {
        $promoVal = $promoValueHash{$timeID};
        $jsonData .= "{ \"value\": \"$promoVal\"},\n";
      }
      chop($jsonData);  chop($jsonData);

      $jsonData .= <<JSON_LABEL;
    ]
  }
JSON_LABEL
    }

    #output the data set for feature promotions
    undef(%promoValueHash);
    $query = "SELECT timeID, promoFeatUnits FROM $dsSchema.$AInsightsBrandCube \
        WHERE brandID=$ownBrandID AND geographyID=$geoID AND timeID IN ($timeIDStr)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($timeID, $promoVal) = $dbOutput->fetchrow_array)
    {
      $promoValueHash{$timeID} = $promoVal;
    }

    if ($status > 0)
    {

      $jsonData .= <<JSON_LABEL;
  ,{
    "seriesname": "Feature Promotion",
    "data": [
JSON_LABEL

      foreach $timeID (@orderedTimeIDs)
      {
        $promoVal = $promoValueHash{$timeID};
        $jsonData .= "{ \"value\": \"$promoVal\"},\n";
      }
      chop($jsonData);  chop($jsonData);

      $jsonData .= <<JSON_LABEL;
    ]
  }
JSON_LABEL
    }

    #output the data set for price reduction promotions
    undef(%promoValueHash);
    $query = "SELECT timeID, promoPriceDecrUnits FROM $dsSchema.$AInsightsBrandCube \
        WHERE brandID=$ownBrandID AND geographyID=$geoID AND timeID IN ($timeIDStr)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($timeID, $promoVal) = $dbOutput->fetchrow_array)
    {
      $promoValueHash{$timeID} = $promoVal;
    }

    if ($status > 0)
    {

      $jsonData .= <<JSON_LABEL;
  ,{
    "seriesname": "Price Reduction Promotion",
    "data": [
JSON_LABEL

      foreach $timeID (@orderedTimeIDs)
      {
        $promoVal = $promoValueHash{$timeID};
        $jsonData .= "{ \"value\": \"$promoVal\"},\n";
      }
      chop($jsonData);  chop($jsonData);

      $jsonData .= <<JSON_LABEL;
    ]
  }
JSON_LABEL
    }

    $jsonData .= <<JSON_LABEL;
  ]
}
JSON_LABEL

    print $jsonData;
  }



  #----------------- Promos for all brands in geo -------------------

  if ($chart eq "brands_promo")
  {

    #figure out the order of the brands to be graphed
    $query = "SELECT brandID FROM $dsSchema.$AInsightsBrandTable \
        WHERE geographyID=$geoID AND promoUnits52 > 0 AND brandID > 0 \
        ORDER BY promoUnits52 DESC";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($brandID) = $dbOutput->fetchrow_array)
    {
      $brandIDStr .= "'$brandID',";
      push(@orderedBrandIDs, $brandID);
    }
    chop($brandIDStr);

    $jsonData = <<JSON_LABEL;
{
  "chart":
  {
    "theme": "zune",
    "labelFontSize": "12",
    "valueFontSize": "12",
    "decimals": "0"
  },
JSON_LABEL

    #output brands
    $jsonData .= <<JSON_LABEL;
  "categories": [
    {
      "category": [
JSON_LABEL

    foreach $brandID (@orderedBrandIDs)
    {
    $jsonData .= <<JSON_LABEL;
        {
          "label": "$brandNameHash{$brandID}"
        },
JSON_LABEL
    }
    chop($jsonData);  chop($jsonData);

    $jsonData .= <<JSON_LABEL;
      ]
    }
  ],
  "dataset": [

JSON_LABEL

    #output the data set for display-only promotions
    undef(%promoValueHash);
    $query = "SELECT brandID, promoDispUnits52 FROM $dsSchema.$AInsightsBrandTable \
        WHERE geographyID=$geoID AND brandID IN ($brandIDStr)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($brandID, $promoVal) = $dbOutput->fetchrow_array)
    {
      $promoValueHash{$brandID} = $promoVal;
    }

    if ($status > 0)
    {

      $jsonData .= <<JSON_LABEL;
  {
    "seriesname": "Display Promotion",
    "data": [
JSON_LABEL

      foreach $brandID (@orderedBrandIDs)
      {
        $promoVal = $promoValueHash{$brandID};
        $jsonData .= "{ \"value\": \"$promoVal\"},\n";
      }
      chop($jsonData);  chop($jsonData);

      $jsonData .= <<JSON_LABEL;
    ]
  }
JSON_LABEL
    }

    #output the data set for display and feature promotions
    undef(%promoValueHash);
    $query = "SELECT brandID, promoFeatDispUnits52 FROM $dsSchema.$AInsightsBrandTable \
        WHERE geographyID=$geoID AND brandID IN ($brandIDStr)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($brandID, $promoVal) = $dbOutput->fetchrow_array)
    {
      $promoValueHash{$brandID} = $promoVal;
    }

    if ($status > 0)
    {
      $jsonData .= <<JSON_LABEL;
  ,{
    "seriesname": "Display & Feature Promotion",
    "data": [
JSON_LABEL

      foreach $brandID (@orderedBrandIDs)
      {
        $promoVal = $promoValueHash{$brandID};
        $jsonData .= "{ \"value\": \"$promoVal\"},\n";
      }
      chop($jsonData);  chop($jsonData);

      $jsonData .= <<JSON_LABEL;
    ]
  }
JSON_LABEL
    }

    #output the data set for feature promotions
    undef(%promoValueHash);
    $query = "SELECT brandID, promoFeatUnits52 FROM $dsSchema.$AInsightsBrandTable \
        WHERE geographyID=$geoID AND brandID IN ($brandIDStr)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($brandID, $promoVal) = $dbOutput->fetchrow_array)
    {
      $promoValueHash{$brandID} = $promoVal;
    }

    if ($status > 0)
    {

      $jsonData .= <<JSON_LABEL;
  ,{
    "seriesname": "Feature Promotion",
    "data": [
JSON_LABEL

      foreach $brandID (@orderedBrandIDs)
      {
        $promoVal = $promoValueHash{$brandID};
        $jsonData .= "{ \"value\": \"$promoVal\"},\n";
      }
      chop($jsonData);  chop($jsonData);

      $jsonData .= <<JSON_LABEL;
    ]
  }
JSON_LABEL
    }

    #output the data set for price reduction promotions
    undef(%promoValueHash);
    $query = "SELECT brandID, promoPriceDecrUnits52 FROM $dsSchema.$AInsightsBrandTable \
        WHERE geographyID=$geoID AND brandID IN ($brandIDStr)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($brandID, $promoVal) = $dbOutput->fetchrow_array)
    {
      $promoValueHash{$brandID} = $promoVal;
    }

    if ($status > 0)
    {

      $jsonData .= <<JSON_LABEL;
  ,{
    "seriesname": "Price Reduction Promotion",
    "data": [
JSON_LABEL

      foreach $brandID (@orderedBrandIDs)
      {
        $promoVal = $promoValueHash{$brandID};
        $jsonData .= "{ \"value\": \"$promoVal\"},\n";
      }
      chop($jsonData);  chop($jsonData);

      $jsonData .= <<JSON_LABEL;
    ]
  }
JSON_LABEL
    }

    $jsonData .= <<JSON_LABEL;
  ]
}
JSON_LABEL

    print $jsonData;
  }



  #----------------- Promos for our brand in all geos -------------------

  if ($chart eq "brand_geos")
  {

    %geoBaseNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "g");

    #figure out the order of the geographies to be graphed
    $query = "SELECT geographyID FROM $dsSchema.$AInsightsBrandTable \
        WHERE brandID=$ownBrandID AND promoUnits52 > 0 \
        ORDER BY promoUnits52 DESC";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($geoID) = $dbOutput->fetchrow_array)
    {
      $geoIDStr .= "'$geoID',";
      push(@orderedGeoIDs, $geoID);
    }
    chop($geoIDStr);

    $jsonData = <<JSON_LABEL;
{
  "chart":
  {
    "theme": "zune",
    "labelFontSize": "12",
    "valueFontSize": "12",
    "decimals": "0"
  },
JSON_LABEL

    #output brands
    $jsonData .= <<JSON_LABEL;
  "categories": [
    {
      "category": [
JSON_LABEL

    foreach $geoID (@orderedGeoIDs)
    {
    $jsonData .= <<JSON_LABEL;
        {
          "label": "$geoBaseNameHash{$geoID}"
        },
JSON_LABEL
    }
    chop($jsonData);  chop($jsonData);

    $jsonData .= <<JSON_LABEL;
      ]
    }
  ],
  "dataset": [

JSON_LABEL

    #output the data set for display-only promotions
    undef(%promoValueHash);
    $query = "SELECT geographyID, promoDispUnits52 FROM $dsSchema.$AInsightsBrandTable \
        WHERE brandID=$ownBrandID AND geographyID IN ($geoIDStr)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($geoID, $promoVal) = $dbOutput->fetchrow_array)
    {
      $promoValueHash{$geoID} = $promoVal;
    }

    if ($status > 0)
    {

      $jsonData .= <<JSON_LABEL;
  {
    "seriesname": "Display Promotion",
    "data": [
JSON_LABEL

      foreach $geoID (@orderedGeoIDs)
      {
        $promoVal = $promoValueHash{$geoID};
        $jsonData .= "{ \"value\": \"$promoVal\"},\n";
      }
      chop($jsonData);  chop($jsonData);

      $jsonData .= <<JSON_LABEL;
    ]
  }
JSON_LABEL
    }

    #output the data set for display and feature promotions
    undef(%promoValueHash);
    $query = "SELECT geographyID, promoFeatDispUnits52 FROM $dsSchema.$AInsightsBrandTable \
        WHERE brandID=$ownBrandID AND geographyID IN ($geoIDStr)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($geoID, $promoVal) = $dbOutput->fetchrow_array)
    {
      $promoValueHash{$geoID} = $promoVal;
    }

    if ($status > 0)
    {
      $jsonData .= <<JSON_LABEL;
  ,{
    "seriesname": "Display & Feature Promotion",
    "data": [
JSON_LABEL

      foreach $geoID (@orderedGeoIDs)
      {
        $promoVal = $promoValueHash{$geoID};
        $jsonData .= "{ \"value\": \"$promoVal\"},\n";
      }
      chop($jsonData);  chop($jsonData);

      $jsonData .= <<JSON_LABEL;
    ]
  }
JSON_LABEL
    }

    #output the data set for feature promotions
    undef(%promoValueHash);
    $query = "SELECT geographyID, promoFeatUnits52 FROM $dsSchema.$AInsightsBrandTable \
        WHERE brandID=$ownBrandID AND geographyID IN ($geoIDStr)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($geoID, $promoVal) = $dbOutput->fetchrow_array)
    {
      $promoValueHash{$geoID} = $promoVal;
    }

    if ($status > 0)
    {

      $jsonData .= <<JSON_LABEL;
  ,{
    "seriesname": "Feature Promotion",
    "data": [
JSON_LABEL

      foreach $geoID (@orderedGeoIDs)
      {
        $promoVal = $promoValueHash{$geoID};
        $jsonData .= "{ \"value\": \"$promoVal\"},\n";
      }
      chop($jsonData);  chop($jsonData);

      $jsonData .= <<JSON_LABEL;
    ]
  }
JSON_LABEL
    }

    #output the data set for price reduction promotions
    undef(%promoValueHash);
    $query = "SELECT geographyID, promoPriceDecrUnits52 FROM $dsSchema.$AInsightsBrandTable \
        WHERE brandID=$ownBrandID AND geographyID IN ($geoIDStr)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($geoID, $promoVal) = $dbOutput->fetchrow_array)
    {
      $promoValueHash{$geoID} = $promoVal;
    }

    if ($status > 0)
    {

      $jsonData .= <<JSON_LABEL;
  ,{
    "seriesname": "Price Reduction Promotion",
    "data": [
JSON_LABEL

      foreach $geoID (@orderedGeoIDs)
      {
        $promoVal = $promoValueHash{$geoID};
        $jsonData .= "{ \"value\": \"$promoVal\"},\n";
      }
      chop($jsonData);  chop($jsonData);

      $jsonData .= <<JSON_LABEL;
    ]
  }
JSON_LABEL
    }

    $jsonData .= <<JSON_LABEL;
  ]
}
JSON_LABEL

    print $jsonData;
  }



  #----------------- Promos for all of our brand's items -------------------

  if ($chart eq "brand_items")
  {

    %prodBaseNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "p");

    #figure out the order of the items to be graphed
    $query = "SELECT productID FROM $dsSchema.$AInsightsItemTable \
        WHERE brandID=$ownBrandID AND promoUnits52 > 0 AND geographyID = $geoID \
        ORDER BY promoUnits52 DESC";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($prodID) = $dbOutput->fetchrow_array)
    {
      $prodIDStr .= "'$prodID',";
      push(@orderedProdIDs, $prodID);
    }
    chop($prodIDStr);

    $jsonData = <<JSON_LABEL;
{
  "chart":
  {
    "theme": "zune",
    "labelFontSize": "12",
    "valueFontSize": "12",
    "decimals": "0"
  },
JSON_LABEL

    #output brands
    $jsonData .= <<JSON_LABEL;
  "categories": [
    {
      "category": [
JSON_LABEL

    foreach $prodID (@orderedProdIDs)
    {
    $jsonData .= <<JSON_LABEL;
        {
          "label": "$prodBaseNameHash{$prodID}"
        },
JSON_LABEL
    }
    chop($jsonData);  chop($jsonData);

    $jsonData .= <<JSON_LABEL;
      ]
    }
  ],
  "dataset": [

JSON_LABEL

    #output the data set for display-only promotions
    undef(%promoValueHash);
    $query = "SELECT productID, promoDispUnits52 FROM $dsSchema.$AInsightsItemTable \
        WHERE geographyID=$geoID AND productID IN ($prodIDStr)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($prodID, $promoVal) = $dbOutput->fetchrow_array)
    {
      $promoValueHash{$prodID} = $promoVal;
    }

    if ($status > 0)
    {

      $jsonData .= <<JSON_LABEL;
  {
    "seriesname": "Display Promotion",
    "data": [
JSON_LABEL

      foreach $prodID (@orderedProdIDs)
      {
        $promoVal = $promoValueHash{$prodID};
        $jsonData .= "{ \"value\": \"$promoVal\"},\n";
      }
      chop($jsonData);  chop($jsonData);

      $jsonData .= <<JSON_LABEL;
    ]
  }
JSON_LABEL
    }

    #output the data set for display and feature promotions
    undef(%promoValueHash);
    $query = "SELECT productID, promoFeatDispUnits52 FROM $dsSchema.$AInsightsItemTable \
        WHERE geographyID=$geoID AND productID IN ($prodIDStr)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($prodID, $promoVal) = $dbOutput->fetchrow_array)
    {
      $promoValueHash{$prodID} = $promoVal;
    }

    if ($status > 0)
    {
      $jsonData .= <<JSON_LABEL;
  ,{
    "seriesname": "Display & Feature Promotion",
    "data": [
JSON_LABEL

      foreach $prodID (@orderedProdIDs)
      {
        $promoVal = $promoValueHash{$prodID};
        $jsonData .= "{ \"value\": \"$promoVal\"},\n";
      }
      chop($jsonData);  chop($jsonData);

      $jsonData .= <<JSON_LABEL;
    ]
  }
JSON_LABEL
    }

    #output the data set for feature promotions
    undef(%promoValueHash);
    $query = "SELECT productID, promoFeatUnits52 FROM $dsSchema.$AInsightsItemTable \
        WHERE geographyID=$geoID AND productID IN ($prodIDStr)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($prodID, $promoVal) = $dbOutput->fetchrow_array)
    {
      $promoValueHash{$prodID} = $promoVal;
    }

    if ($status > 0)
    {

      $jsonData .= <<JSON_LABEL;
  ,{
    "seriesname": "Feature Promotion",
    "data": [
JSON_LABEL

      foreach $prodID (@orderedProdIDs)
      {
        $promoVal = $promoValueHash{$prodID};
        $jsonData .= "{ \"value\": \"$promoVal\"},\n";
      }
      chop($jsonData);  chop($jsonData);

      $jsonData .= <<JSON_LABEL;
    ]
  }
JSON_LABEL
    }

    #output the data set for price reduction promotions
    undef(%promoValueHash);
    $query = "SELECT productID, promoPriceDecrUnits52 FROM $dsSchema.$AInsightsItemTable \
        WHERE geographyID=$geoID AND productID IN ($prodIDStr)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($prodID, $promoVal) = $dbOutput->fetchrow_array)
    {
      $promoValueHash{$prodID} = $promoVal;
    }

    if ($status > 0)
    {

      $jsonData .= <<JSON_LABEL;
  ,{
    "seriesname": "Price Reduction Promotion",
    "data": [
JSON_LABEL

      foreach $prodID (@orderedProdIDs)
      {
        $promoVal = $promoValueHash{$prodID};
        $jsonData .= "{ \"value\": \"$promoVal\"},\n";
      }
      chop($jsonData);  chop($jsonData);

      $jsonData .= <<JSON_LABEL;
    ]
  }
JSON_LABEL
    }

    $jsonData .= <<JSON_LABEL;
  ]
}
JSON_LABEL

    print $jsonData;
  }


#EOF
