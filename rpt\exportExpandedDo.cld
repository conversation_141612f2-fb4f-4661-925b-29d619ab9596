#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;
use Excel::Writer::XLSX;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::ExcelReports;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Export Expanded Report</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
let statusTimer = setInterval(function(){displayStatus()}, 5000);

function displayStatus()
{
  const url = "/app/rpt/xhrPPTexportStatus.cld?d=$dsID";

  \$.get(url, function(data, status)
  {
    let statusText = data;

    if (statusText.length < 2)
    {
      statusText = "Exporting reports to PowerPoint";
    }

    if (statusText.length == 5)  //DONE\n
    {
      \$('#progress-bar-container').hide();
      \$('#progressDiv').hide();
      document.getElementById('download-btn').style.visibility = 'visible';
      document.getElementById('ok-btn').style.visibility = 'visible';
      clearInterval(statusTimer);
    }
    else
    {
      document.getElementById('progressDiv').innerHTML = statusText;
    }
  });
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/rpt/main">Reports</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?rpt=$rptID">$cubeName</A></LI>
    <LI CLASS="breadcrumb-item active">Export Expanded Report</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------
#
# Output the HTML for the web page that displays the status dialog
#

sub print_status_html
{

  if ($action eq "ppt")
  {
    $tmpBase = $last . $userID;
    $tmpBase =~ s/\s+//g;
    $tmpBase .= "_" . $rptID;
    $filename = "$tmpBase.pptx";
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-10 col-lg-7 col-xl-6"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Exporting Expanded Reports</DIV>
        <DIV CLASS="card-body">

          <DIV CLASS="progress" ID="progress-bar-container">
            <DIV ID="progress-bar" CLASS="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" STYLE="width:100%;">
            </DIV>
          </DIV>

          <P>
          <DIV CLASS="text-center">
            <DIV ID="progressDiv">Exporting expanded reports</DIV>
          </DIV>

          <P>&nbsp;</P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-success" TYPE="button" ID="download-btn" STYLE="visibility:hidden;" onClick="location.href='/tmp/$filename'"><SPAN CLASS="bi bi-download"></SPAN> Download Expanded Report</BUTTON>
            <P>&nbsp;</P>
            <BUTTON CLASS="btn btn-primary" TYPE="button" ID="ok-btn" STYLE="visibility:hidden;" onClick="location.href='/app/rpt/main'"><I CLASS="bi bi-check-lg"></I> Done</BUTTON>
          </DIV>
        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser and get the report to be expanded
  $q = new CGI;
  $rptID = $q->param('rpt');
  $dim = $q->param('d');
  $action = $q->param('a');
  @itemIDs = $q->param('items');
  $itemString = $q->param('i');

  $itemIDstr = join(',', @itemIDs);

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  $db = KAPutil_connect_to_database();

  $cubeName = cube_id_to_name($db, $rptID);
  $dsID = cube_get_ds_id($db, $rptID);
  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  #make sure we have write privs for this data cube
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to export this report.");
  }


  ########################################################################

  #if we're creating a printable version of the expanded report
  if ($action eq "dp")
  {
    #open the printable version in a new tab in the same browser
    print($session->header());
    print <<END_HTML;
<HTML>
 <HEAD>
<STYLE TYPE="text/css">
 P.breakhere {page-break-before: always}
</STYLE>
 </HEAD>
 <BODY>
END_HTML

  @itemIDs = split(',', $itemString);
  foreach $itemID (@itemIDs)
  {
    print("<IFRAME SRC='display.cld?rpt=$rptID&c=1&$dim=$itemID' frameborder='0' scrolling='no' height='100%' width='100%'></IFRAME>\n");
    print("<P CLASS='breakhere'>\n");
  }

  print <<END_HTML;
 </BODY>
</HTML>

END_HTML

    $activity = "$first $last exported expanded printable report for $cubeName in $dsName";
    utils_audit($db, $userID, "Exported as expanded printable report", $dsID, $rptID, 0);
    utils_slack($activity);

    exit;
  }


  #######################################################################

  #if we're creating a downloadable Excel version of the expanded report
  if ($action eq "d")
  {

    #get the data cube details from the database
    $query = "SELECT name, dsID FROM cubes WHERE ID=$rptID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($name, $dsID) = $dbOutput->fetchrow_array;

    #make sure our sheet name isn't going to be too long
    $name = substr($name, 0, 21);

    #build up the temp filename
    $tmpDSName = substr($dsName, 0, 32);
    $tmpCubeName = substr($cubeName, 0, 32);
    $filename = "Koala $tmpDSName" . " - " . $tmpCubeName . "_" . $userID . "_" . "$dsID.xlsx";

    #get rid of special characters we don't want in the file name
    $filename =~ s/\s+/ /g;
    $filename =~ s/\\//g;
    $filename =~ s/\///g;
    $filename =~ s/\*//g;
    $filename =~ s/\$//g;
    $filename =~ s/\&//g;
    $filename =~ s/\|//g;
    $filename =~ s/\?//g;
    $filename =~ s/\://g;
    $filename =~ s/\"//g;
    $filename =~ s/\'//g;
    $filename =~ s/\%//g;
    $filename =~ s/\#//g;

    #fork a new process to do the actual PPT export in the background
    $SIG{CHLD} = "IGNORE";
    if ($pid = fork())
    {
      #parent process

      print_status_html();

      $activity = "$first $last exporting reports in $dsName to Excel";
      utils_slack($activity);

      exit;
    }

    else
    {
      #child process

      #let Apache know not to wait on the child process
      close(STDIN);
      close(STDOUT);

      #redirect STDERR to the Koala error log
      close(STDERR);
      open(STDERR, ">>/opt/apache/htdocs/tmp/koala_error.log");
      select(STDERR);
      $| = 1;

      #reconnect to the database
      $db = KAPutil_connect_to_database();

      #set our initial state in the jobs table
      KAPutil_job_store_status($db, $userID, $dsID, 0, "REPORT-EXPORT", "Exporting expanded reports to Excel");

      $query = "INSERT INTO audit.stats_cubes (cubeID, year, month, exportExcel) \
          VALUES ($rptID, YEAR(NOW()), MONTH(NOW()), 1) \
          ON DUPLICATE KEY UPDATE exportExcel = exportExcel + 1";
      $status = $db->do($query);
      KAPutil_handle_db_err($db, $status, $query);

      #create a new Excel workbook
      $workbook = Excel::Writer::XLSX->new("/opt/apache/htdocs/tmp/$filename");

      #if we're just outputting one giant table, enable possible optimizations
      $query = "SELECT type, tableColDims FROM visuals WHERE cubeID=$rptID";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      KAPutil_handle_db_err($db, $status, $query);
      ($reportType, $tableColDims) = $dbOutput->fetchrow_array;

      #if we can use the output optimization (can only write once top to bottom)
      if (($status == 1) && ($reportType eq "table") && (length($tableColDims) == 0))
      {
        $workbook->set_optimization();
      }

      $idx = 1;
      $reportCount = @itemIDs;
      foreach $itemID (@itemIDs)
      {
        $query = "UPDATE jobs \
            SET status='Creating Excel worksheet $idx of $reportCount' \
            WHERE PID=$$";
        $status = $db->do($query);
        KAPutil_handle_db_err($db, $status, $query);

        #add worksheet with same name as report
        $sheetName = "$name $idx";
        $worksheet = $workbook->add_worksheet($sheetName);

        excel_insert_visuals($db, $rptID, $userID, $acctType, $workbook, $worksheet, $dim, $itemID);

        $idx++;
      }

      $workbook->close();

      #remove this task from the jobs table
      DSRutil_clear_status($db);

      $activity = "$first $last exported expanded Excel report for $cubeName in $dsName";
      utils_audit($db, $userID, "Exported as expanded Excel report", $dsID, $rptID, 0);
      utils_slack($activity);

      exit;
    }

  }


  #######################################################################

  #if we're creating a downloadable PowerPoint version of the expanded report
  if ($action eq "ppt")
  {

    #fork a new process to do the actual PPT export in the background
    $SIG{CHLD} = "IGNORE";
    if ($pid = fork())
    {
      #parent process

      print_status_html();

      $activity = "$first $last exporting reports in $dsName to PowerPoint";
      utils_slack($activity);

      exit;
    }

    else
    {
      #child process

      #let Apache know not to wait on the child process
      close(STDIN);
      close(STDOUT);

      #redirect STDERR to the Koala error log
      close(STDERR);
      open(STDERR, ">>/opt/apache/htdocs/tmp/koala_error.log");
      select(STDERR);
      $| = 1;

      #reconnect to the database
      $db = KAPutil_connect_to_database();

      #set our initial state in the jobs table
      KAPutil_job_store_status($db, $userID, $dsID, 0, "REPORT-EXPORT", "Exporting reports to PowerPoint deck");

      #
      # Export PowerPoint Deck
      #

      $query = "INSERT INTO audit.stats_cubes (cubeID, year, month, exportPPT) \
          VALUES ($rptID, YEAR(NOW()), MONTH(NOW()), 1) \
          ON DUPLICATE KEY UPDATE exportPPT=exportPPT+1";
      $status = $db->do($query);
      KAPutil_handle_db_err($db, $status, $query);

      #decide how long we should wait for PhantomJS to render the report based on
      #number of items in the data source
      $dsSchema = "datasource_" . $dsID;
      %prodNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "p");
      $prodCount = scalar(keys %prodNameHash);
      $waitTime = 5000;
      if ($prodCount > 5000)
      {
        $waitTime = 10000;
      }

      #build up the temp filenames
      $tmpBase = $last . $userID;
      $tmpBase =~ s/\s+//g;
      $tmpBase = $tmpBase . "_" . $rptID;
      $filename = "$tmpBase.pptx";
      $scriptName = "$tmpBase.js";
      $imgName = "$tmpBase.png";

      #make a copy of the template PPT file to the tmp directory
      system("cp deck_template.pptx /opt/apache/htdocs/tmp/$filename");

      #change our working directory to the platform web-accessible tmp
      chdir("/opt/apache/htdocs/tmp");

      #unzip the PPTX file (OpenDocument XML format expected)
      system("unzip -qq /opt/apache/htdocs/tmp/$filename -d /opt/apache/htdocs/tmp/$tmpBase");

      #delete the template file
      system("rm $filename");

      $idx = 1;
      $slideCount = @itemIDs;
      foreach $itemID (@itemIDs)
      {
        $query = "UPDATE jobs SET status='Creating slide $idx of $slideCount' \
            WHERE PID=$$";
        $status = $db->do($query);
        KAPutil_handle_db_err($db, $status, $query);

        #get the screenshot of the report
        system("/opt/google/chrome/google-chrome --headless --run-all-compositor-stages-before-draw --virtual-time-budget=5000 --screenshot=/opt/apache/htdocs/tmp/$imgName --window-size=1280,720 --user-data-dir=/opt/apache/htdocs/tmp/ \"http://$Lib::KoalaConfig::hostname/app/rpt/display.cld?rpt=$rptID&c=1&$dim=$itemID&l=1&u=$userID\"");

        #copy the screenshot of the report into the PPTX structure
        $imageName = "image" . $idx . ".png";
        system("cp $imgName $tmpBase/ppt/media/$imageName");

        #create a copy of the standard slide definition XML file for each slide
        $XMLname = "slide" . $idx . ".xml";
        system("cp $tmpBase/ppt/slides/slide1.xml $tmpBase/ppt/slides/$XMLname");

        #create the slide relationship XML file
        $XMLname = "slide" .  $idx . ".xml.rels";
        $replaceStr = "image" . $idx;
        open(INPUT, "$tmpBase/ppt/slides/_rels/template");
        open(OUTPUT, ">$tmpBase/ppt/slides/_rels/$XMLname");
        while ($line = <INPUT>)
        {
          if ($line =~ m/(.*)KOALA(.*)/)
          {
            $line = $1 . $replaceStr . $2;
          }
          print OUTPUT $line;
        }
        close(INPUT);
        close(OUTPUT);

        $idx++;
      }

      #update the master presentation relationship file
      open(INPUT, "$tmpBase/ppt/_rels/template");
      open(OUTPUT, ">$tmpBase/ppt/_rels/presentation.xml.rels");
      while ($line = <INPUT>)
      {
        if ($line =~ m/KOALA/)
        {
          $idx = 1;
          foreach $itemID (@itemIDs)
          {
            $rId = $idx + 8;
            $rId = "rId" . $rId;
            $replStr = "slide" . $idx . ".xml";
            print OUTPUT "<Relationship Id=\"$rId\" Type=\"http://schemas.openxmlformats.org/officeDocument/2006/relationships/slide\" Target=\"slides/$replStr\"/>\n";
            $idx++;
          }
        }
        else
        {
          print OUTPUT $line;
        }
      }

      #update the master presentation file
      open(INPUT, "$tmpBase/ppt/template");
      open(OUTPUT, ">$tmpBase/ppt/presentation.xml");
      while ($line = <INPUT>)
      {
        if ($line =~ m/KOALA/)
        {
          $idx = 1;
          foreach $itemID (@itemIDs)
          {
            $rId = $idx + 8;
            $rId = "rId" . $rId;
            $id = $idx + 255;
            print OUTPUT "<p:sldId id=\"$id\" r:id=\"$rId\"/>";
            $idx++;
          }
        }
        else
        {
          print OUTPUT $line;
        }
      }

      #update the master Content Types file
      open(INPUT, "$tmpBase/template");
      open(OUTPUT, ">$tmpBase/[Content_Types].xml");
      while ($line = <INPUT>)
      {
        if ($line =~ m/KOALA/)
        {
          $idx = 1;
          foreach $itemID (@itemIDs)
          {
            $replStr = "slide" . $idx . ".xml";
            print OUTPUT "<Override PartName=\"/ppt/slides/$replStr\" ContentType=\"application/vnd.openxmlformats-officedocument.presentationml.slide+xml\"/>";
            $idx++;
          }
        }
        else
        {
          print OUTPUT $line;
        }
      }

      #remove our temporary template files so PowerPoint doesn't throw a fit
      system("rm $tmpBase/ppt/slides/_rels/template");
      system("rm $tmpBase/ppt/_rels/template");
      system("rm $tmpBase/ppt/template");
      system("rm $tmpBase/template");

      #re-zip the updated PPTX structure into the PPTX file
      chdir("$tmpBase");
      system("zip -qr ../$filename *");
      chdir("..");

      #delete the screenshot image, and PPTX structure
      system("rm -rf $tmpBase");
      system("rm $imgName");

      #remove this task from the jobs table
      DSRutil_clear_status($db);

      $activity = "$first $last exported expanded static PowerPoint report for $cubeName in $dsName";
      utils_audit($db, $userID, "Exported as expanded static PowerPoint report", $dsID, $rptID, 0);
      utils_slack($activity);

      exit;
    }
  }


  #######################################################################

  #assign button names and actions depending on user request
  if (($action eq "d") || ($action eq "ppt"))
  {
    $buttonText = "Download Expanded Report";
    $buttonAction = "location.href='/tmp/$filename'";
  }
  elsif ($action eq "p")
  {
    $buttonText = "Open Printable Report";
    $buttonAction = "window.open('?a=dp&d=$dim&rpt=$rptID&i=$itemIDstr', '_blank')";
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-10 col-lg-7 col-xl-6"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Export Expanded Report</DIV>
        <DIV CLASS="card-body">

          <P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-success" TYPE="button" onClick="$buttonAction"><SPAN CLASS="bi bi-download"></SPAN> $buttonText</BUTTON>
            <P>&nbsp;</P>
            <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='/app/rpt/display.cld?rpt=$rptID'"><I CLASS="bi bi-check-lg"></I> Done</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
