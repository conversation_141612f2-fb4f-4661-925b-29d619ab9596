#!/usr/bin/perl

use Text::CSV;

#Import Continental Concessions SPINS shipping data for BBI

  #instantiate our CSV object
  $csv = Text::CSV->new( {binary => 1} );

  open(INPUT, "$ARGV[0]");
  open(OUTPUT, ">$ARGV[1]");

  #burn the first two garbage lines
  $line = <INPUT>;
  $line = <INPUT>;

  #read & parse the line containing date info
  $line = <INPUT>;
  if ($line =~ m/.* to (.*?)\,/)
  {
    $timePeriod = "3 months ending $1";
  }
  else
  {
    $timePeriod = "UNKNOWN";
  }

  #burn next 4 lines of garbage
  $line = <INPUT>;
  $line = <INPUT>;
  $line = <INPUT>;
  $line = <INPUT>;

  print OUTPUT "Product,UPC,Geography,Time,pseg:ItemID,Unit Type,Qty Shipped\n";

  while ($line = <INPUT>)
  {
    $csv->parse($line);
    @columns = $csv->fields();

    #if it's a geography line (geo in column A)
    if (length($columns[0]) > 5)
    {
      $geography = $columns[0];
      $line = <INPUT>;	#burn next line of duplicate info
    }

    elsif (length($columns[4]) > 3)
    {
      $itemID = $columns[4];
      $product = $columns[9];
      $qtyShipped = $columns[20];
      $unitType = $columns[21];
      $upc = $columns[23];

      @tmp = ("$product", "$upc", "$geography", "$timePeriod", "$itemID", $unitType, $qtyShipped);
      $csv->combine(@tmp);
      $line = $csv->string();

      print OUTPUT "$line\n";
    }

  }

  close(INPUT);
  close(OUTPUT);

#EOF
