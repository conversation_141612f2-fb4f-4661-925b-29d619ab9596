#!/usr/bin/perl

use lib "/opt/apache/app/";

use DBI;
use Email::Valid;

use Lib::KoalaConfig;
use Lib::BuildCube;
use Lib::DataSources;
use Lib::DSRMeasures;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::WebUtils;


my $debug;



#-------------------------------------------------------------------------
#
# Output debug data, if enabled
#

sub DBG
{
  my ($str) = @_;


  if ($debug == 1)
  {
    print STDERR "$str\n";
  }
}



#-------------------------------------------------------------------------

  #determine if we're being run manually
  $debug = 0;
  if ($ARGV[0] eq "debug")
  {
    $debug = 1;
  }

  #redirect STDERR to the Koala error log
  if ($debug == 0)
  {
    close(STDOUT);
    close(STDERR);
    open(STDERR, ">>/opt/apache/htdocs/tmp/koala_error.log");
  }

  #connect to the master database
  $db = KAPutil_connect_to_database();

  #remove any data files from the Koala tmp folder that are more than 24hrs old
  DBG("Removing source data files from app/tmp that are older than 24 hours");
  chdir("/opt/apache/app/tmp");
  @files = glob("*.work *.csv *.xls *.xlsx");
  foreach $file (@files)
  {
    if (-M $file > 1)
    {
      unlink($file);
      DBG("***** Unlinked $file");
    }
  }

  #remove any DS update logs that are more than a week old
  DBG("Removing DS update error logs more than a week old");
  chdir("/opt/apache/htdocs/tmp");
  @files = glob("dsimport*.log");
  foreach $file (@files)
  {
    if (-M $file > 7)
    {
      unlink($file);
      DBG("***** Unlinked $file");
    }
  }

  #remove any user export files that are more than a day old
  DBG("Removing user export files more than a day old");
  chdir("/opt/apache/htdocs/tmp");
  @files = glob("*.zip *.pptx *.xlsx");
  foreach $file (@files)
  {
    if (-M $file > 1)
    {
      unlink($file);
      DBG("***** Unlinked $file");
    }
  }

  #remove Apache log files that are more than a week old
  DBG("Removing Apache logs files more than a week old");
  chdir("/opt/apache/logs");
  @files = glob("access_log* error_log*");
  foreach $file (@files)
  {
    if (-M $file > 7)
    {
      unlink($file);
      DBG("***** Unlinked $file");
    }
  }

  DBG("Removing data integrity backups for deleted data sources");
  @dataDirs = ('/opt/koala_backup');
  $query = "SELECT dataStorage FROM app.orgs WHERE NOT ISNULL(dataStorage)";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  while (($dataDir) = $dbOutput->fetchrow_array)
  {
    $dataDir .= "/koala_backup/";
    push(@dataDirs, $dataDir);
  }
  %dsNameHash = ds_get_name_hash($db);
  foreach $dataDir (@dataDirs)
  {
    chdir("$dataDir");
    @files = glob("*.zip *.sql");
    foreach $file (@files)
    {
      if ($file =~ m/^datasource_(\d+).zip/)
      {
        $dsID = $1;

        #if the data source has been deleted, it won't have a name in the name hash
        if (length($dsNameHash{$dsID}) < 1)
        {
          unlink($file);
          DBG("***** Unlinked $file")
        }
      }
    }
  }

  #remove any leftover restore points more than 45 days old
  DBG("Removing orphaned data source restore points");
  chdir("/opt/apache/app/logs");
  @files = glob("datasource*.zip");
  foreach $file (@files)
  {
    if (-M $file > 45)
    {
      unlink($file);
      DBG("***** Unlinked $file");
    }
  }

  #increment the delete counter for any data source marked as delete-ready
  #NB: The idea is that the delete counter (set to 1 when the user "deletes" a
  #    a data source) is incremented every time we run. When the count hits 3,
  #    we actually delete the data source and everything in it.
  DBG("Incrementing counters for delete-ready data sources");
  $query = "UPDATE dataSources SET deleted = deleted + 1 WHERE deleted > 0";
  $db->do($query);

  #delete any data source with a deleted counter value of 3 or higher
  DBG("Deleting data sources that have been 'deleted' for over 2 days");
  $query = "SELECT ID, name FROM dataSources WHERE deleted > 2";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute();

  while (($dsID, $name) = $dbOutput->fetchrow_array)
  {
    DBG("Deleting data source $name ($dsID)");

    $dsSchema = "datasource_" . $dsID;

    #drop the data source and associated components
    $query = "DROP DATABASE $dsSchema";
    $db->do($query);

    #remove the data source from the master list of data sources
    $query = "DELETE FROM dataSources WHERE ID=$dsID";
    $db->do($query);

    #remove any cubes that depended on the data source
    $query = "DELETE FROM cubes WHERE dsID=$dsID";
    $db->do($query);

    #remvoe any visual elements that depend on the data source
    $query = "DELETE FROM visuals WHERE dsID=$dsID";
    $db->do($query);

    #remove any forecasts that depended on the data source
    $query = "DELETE FROM analytics.forecasts WHERE dsID=$dsID";
    $db->do($query);

    #remove any elasticity models that depended on the data source
    $query = "DELETE FROM analytics.pricing WHERE dsID=$dsID";
    $db->do($query);

    #remove any user feed items related to the data source
    $query = "DELETE FROM app.feed WHERE dsID=$dsID";
    $db->do($query);
  }

  #remove any rollback restore points more than 31 days old
  DBG("Removing rollback restore points more than 31 days old");
  %dsHash = ds_get_name_hash($db);
  foreach $dsID (keys %dsHash)
  {
    $query = "SELECT ID, filename FROM datasource_$dsID.update_history \
        WHERE DATE_SUB(NOW(), INTERVAL 31 DAY) > timestamp AND !isnull(filename)";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;

    while (($updateID, $filename) = $dbOutput->fetchrow_array)
    {
      $query = "UPDATE datasource_$dsID.update_history SET filename = NULL \
          WHERE ID=$updateID";
      $db->do($query);
      if (!($filename =~ m/^\//))
      {
        $filename = "/opt/apache/app/logs/$filename";
      }
      unlink("$filename");
      DBG("***** Unlinked $filename");
    }
  }

  #remove user audit records older than a year
  DBG("Removing audit records over a year old");
  $query = "DELETE FROM audit.userActions \
      WHERE DATE_SUB(NOW(), INTERVAL 156 WEEK) > timestamp";
  $db->do($query);

  $query = "DELETE FROM audit.telemetry_odbc \
      WHERE DATE_SUB(NOW(), INTERVAL 156 WEEK) > startTime";
  $db->do($query);

  $query = "DELETE FROM audit.telemetry_cubes \
      WHERE DATE_SUB(NOW(), INTERVAL 156 WEEK) > startTime";
  $db->do($query);

  #drop any unneeded ODBC tables that might be hanging around
  DBG("Removing orphaned ODBC export tables");
  $query = "SELECT table_schema FROM information_schema.TABLES \
      WHERE table_name = 'export' OR table_name = '_export'";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;

  while (($dsSchema) = $dbOutput->fetchrow_array)
  {
    if ($dsSchema =~ m/datasource_(\d+)/)
    {
      $dsID = $1;
    }

    $query = "SELECT name, ODBCexport FROM dataSources WHERE ID=$dsID";
    $dbOutput1 = $db->prepare($query);
    $dbOutput1->execute;
    ($name, $ODBCexport) = $dbOutput1->fetchrow_array;

    #if the data source isn't exported but there are ODBC tables
    if ($ODBCexport == 0)
    {
      DBG("Dropping orphaned ODBC export tables from $name ($dsID)");
      KAPutil_db_delete_table($db, $dsSchema, "export");
      KAPutil_db_delete_table($db, $dsSchema, "export_product");
      KAPutil_db_delete_table($db, $dsSchema, "export_geography");
      KAPutil_db_delete_table($db, $dsSchema, "export_time");
      KAPutil_db_delete_table($db, $dsSchema, "_export");
    }
  }

  #prune old time periods from data sources that need it
  DBG("Pruning time periods from data sources");
  $query = "SELECT ID FROM app.dataSources
      WHERE !ISNULL(timePruning) OR !ISNULL(timePruningAdvanced)
      ORDER BY ID";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  while (($dsID) = $dbOutput->fetchrow_array)
  {
    ds_prune_time_periods($db, $dsID);
  }

  #calculate the cloud storage usage for every analyst on the system
  DBG("Calculating per-analyst storage usage");
  undef(%analystHash);
  $query = "SELECT ID FROM app.users WHERE acctType > 0";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  while (($userID) = $dbOutput->fetchrow_array)
  {
    $analystHash{$userID} = 1;
  }

  #create a hash of all users and orgIDs that have dedicated storage
  $query = "SELECT ID, dataStorage FROM app.orgs WHERE !isnull(dataStorage)";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  while (($orgID, $dir) = $dbOutput->fetchrow_array)
  {
    $orgHash{$orgID} = $dir;
  }

  foreach $orgID (keys %orgHash)
  {
    $query = "SELECT ID FROM app.users WHERE orgID=$orgID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    while (($userID) = $dbOutput->fetchrow_array)
    {
      $userDirHash{$userID} = $orgHash{$orgID};
    }
  }

  foreach $userID (keys %analystHash)
  {
    $totalSize = 0;
    $query = "SELECT ID FROM dataSources WHERE userID=$userID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;

    while (($dsID) = $dbOutput->fetchrow_array)
    {
      $dsSchema = "datasource_" . $dsID;

      #get the various sizes for the data sources
      $query = "SELECT SUM(data_length), SUM(index_length), SUM(data_free) \
          FROM information_schema.TABLES \
          WHERE information_schema.TABLES.table_schema = '$dsSchema'";
      $dbOutput1 = $db->prepare($query);
      $dbOutput1->execute;
      ($sizeData, $sizeIndex, $sizeDataFree) = $dbOutput1->fetchrow_array;
      $totalSize += $sizeData + $sizeIndex + $sizeDataFree;

      #get size of snapshots
      $userDir = $userDirHash{$userID};
      if (length($userDir) < 1)
      {
        $userDir = "/opt/apache/app";
      }
      $userDir = "$userDir/logs";
      chdir($userDir);
      opendir(DIRHANDLE, $userDir);
      while (defined($filename = readdir(DIRHANDLE)))
      {
        $fileStub = "datasource_$dsID" . "_";
        if ($filename =~ m/^$fileStub.*.zip/i)
        {
          $size = -s $filename;
          $totalSize = $totalSize + $size;
        }
      }

      #get size of backup
      $userDir = $userDirHash{$userID};
      if (length($userDir) < 1)
      {
        $userDir = "/opt";
      }
      $filename = "$userDir/koala_backup/datasource_$dsID.zip";
      $size = -s $filename;
      $totalSize = $totalSize + $size;
    }

    $query = "UPDATE app.users SET storage=$totalSize WHERE ID=$userID";
    $db->do($query);
  }

  #data source consistency checking
  DBG("Starting data source consistency checking");
  foreach $dsID (keys %dsHash)
  {
    $dsSchema = "datasource_$dsID";
    %productNameHash = dsr_get_item_name_hash($db, $dsSchema, "p", 0);
    %measureNameHash = dsr_get_item_name_hash($db, $dsSchema, "m", 0);

    #remove deleted products from segmentations they might still be assigned to
    $query = "SELECT DISTINCT itemID FROM $dsSchema.product_segment_item";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;

    while (($itemID) = $dbOutput->fetchrow_array)
    {

      #if the item in the segmentation doesn't exist in the DS
      if (!defined($productNameHash{$itemID}))
      {
        $query = "DELETE FROM $dsSchema.product_segment_item WHERE itemID=$itemID";
        $db->do($query);
        DBG("Removing deleted item $itemID from segmentations in DS $dsID");
      }
    }

    #remove any measure columns in facts table that have been deleted
    $query = "SELECT column_name FROM information_schema.columns \
        WHERE table_schema='$dsSchema' AND table_name='facts'";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;

    while (($columnName) = $dbOutput->fetchrow_array)
    {
      if ($columnName =~ m/^measure_(\d+)$/)
      {
        $measureID = $1;

        if (!defined($measureNameHash{$measureID}))
        {
          DBG("Dropping unused measure column $columnName from DS $dsID");
          $query = "ALTER TABLE $dsSchema.facts DROP COLUMN $columnName";
          $db->do($query);
          $query = "UPDATE app.dataSources SET lastModified=NOW() WHERE ID=$dsID";
          $db->do($query);
        }
      }
    }
  }




#EOF
