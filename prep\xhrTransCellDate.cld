#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepRecipes;
use Lib::PrepUtils;
use Lib::WebUtils;


#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $flowID = $q->param('f');
  $jobID = $q->param('j');
  $colID = $q->param('col');

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #make sure we have write privs for this data flow
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this data flow.");
  }

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #build name of column containing the text
  $column = "column_" . $colID;

  #get the name of the column to be discarded
  $query = "SELECT name FROM $masterColTable WHERE ID=$colID";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($colName) = $dbOutput->fetchrow_array;

  print <<END_HTML;
<SCRIPT>
function checkForm(form)
{
  \$("#btn-submit").prop('disabled', true);
  \$("#btn-submit").text("Please Wait...");
  return(true);
}
</SCRIPT>

<FORM METHOD="post" ACTION="transformProgress.cld" onsubmit="return checkForm(this);">
<INPUT TYPE="hidden" NAME="f" VALUE="$flowID">
<INPUT TYPE="hidden" NAME="j" VALUE="$jobID">
<INPUT TYPE="hidden" NAME="col" VALUE="$colID">
<INPUT TYPE="hidden" NAME="a" VALUE="TRANS-CELL-DATE">

<DIV CLASS="modal-dialog">
  <DIV CLASS="modal-content">

    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Date Formatting</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">
      Change the format of any dates in the selected column to:

      <P>
      <TABLE STYLE="width:100%;">
        <TR>
          <TD>
            <LABEL FOR="duration">Duration Formatting:</LABEL><BR>
            <SELECT NAME="durationFormat" ID="duration" CLASS="form-select">
              <OPTION VALUE="1">N Weeks Ending</OPTION>
              <OPTION VALUE="2">N Weeks</OPTION>
              <OPTION VALUE="3">N WE</OPTION>
              <OPTION VALUE="4">(None)</OPTION>
            </SELECT>
          </TD>
          <TD>
            <LABEL FOR="tformat">Time Formatting:</LABEL><BR>
            <SELECT NAME="timeFormat" ID="tformat" CLASS="form-select">
              <OPTION VALUE="1">3/27/2020</OPTION>
              <OPTION VALUE="7">03/27/2019</OPTION>
              <OPTION VALUE="8">03/27/19</OPTION>
              <OPTION VALUE="2">3/27/20</OPTION>
              <OPTION VALUE="4">Mar 27, 2020</OPTION>
              <OPTION VALUE="5">March 27, 2020</OPTION>
              <OPTION VALUE="6">27-MAR-2020</OPTION>
            </SELECT>
          </TD>
        </TR>
      </TABLE>
    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="submit" ID="btn-submit"><I CLASS="bi bi-pencil-square"></I> Apply</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML

#EOF
