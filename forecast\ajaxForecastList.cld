#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;


#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $userOnly = $q->param('user');

  print("Expires: -1\n");
  print("Cache-Control: private, max-age=0");
  print("Content-type: application/json\n\n");

  $db = KAPutil_connect_to_database();

  %dsNameHash = ds_get_name_hash($db);

  $query = "SELECT ID, name, forecastType, dsID, description \
      FROM analytics.forecasts WHERE userID = $userID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;

  print <<JSON_LABEL;
[
JSON_LABEL

  $count = 1;
  while (($fcID, $name, $type, $dsID, $description) = $dbOutput->fetchrow_array)
  {
    print <<JSON_LABEL;
  {
    "ID": $fcID,
    "Forecast": "$name",
    "Type": "$type",
    "Data Source": "$dsNameHash{$dsID}",
    "Description": "$description"
  }
JSON_LABEL
    if ($count < $status)
    {
      print(",");
    }

    $count++;
  }

  print <<JSON_LABEL;
]
JSON_LABEL


#EOF
