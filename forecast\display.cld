#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#format number for pretty HTML output
sub html_format_number
{
  ($value, $decimals) = @_;

  if (!defined($value))
  {
    return("");
  }

  #go down to specified decimal places
  $formatStr = "%." . $decimals . "f";
  $value = sprintf($formatStr, $value);

  if ($value < 0)
  {
    $value = abs($value);
    $formatStr = "<SPAN STYLE='color:red;'>($value)</SPAN>";
  }
  else
  {
    $formatStr = "$value";
  }

  return($formatStr);
}



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: KCast</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/fontawesome-5.10.2/css/all.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<SCRIPT SRC="/fusioncharts-3.18.0/js/fusioncharts.js"></SCRIPT>
<SCRIPT SRC="/fusioncharts-3.18.0/js/themes/fusioncharts.theme.zune.js"></SCRIPT>

<SCRIPT>
let vpHeight = window.innerHeight - 50;
if (vpHeight < 400)
{
  vpHeight = 400;
}



\$(function ()
{
  \$(`[data-bs-toggle='popover']`).popover()
})



function reloadPage()
{
  let newproduct = document.getElementById('prodsel').value;
  let newgeo = document.getElementById('geosel').value;

  location.href = '/app/forecast/display.cld?fcID=$fcID&prod=' + newproduct + '&geo=' + newgeo;
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">KCast</A></LI>
    <LI CLASS="breadcrumb-item">$dsName</LI>
    <LI CLASS="breadcrumb-item active">$name</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get the CGI input variables
  $fcID = $q->param('fcID');
  $dispProduct = $q->param('prod');
  $dispGeography = $q->param('geo');

  $db = KAPutil_connect_to_database();

  #if the forecast is currently being rebuilt
  $query = "SELECT PID FROM app.jobs \
      WHERE analyticsID=$fcID AND operation='FORECAST'";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($pid) = $dbOutput->fetchrow_array;
  if ($pid > 0)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: /app/forecast/calculateFcast.cld?f=$fcID\n\n");
    exit;
  }

  #get basic info about the forecast from the main database
  $query = "SELECT name, description, dsID, forecastType, periodType, timeperiods, products, geographies, measureID \
      FROM analytics.forecasts WHERE ID=$fcID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;

  ($name, $description, $dsID, $type, $periodType, $timeIDstring, $productIDstring, $geoIDstring, $measureID) = $dbOutput->fetchrow_array;

  #assemble datasource and cube names
  $dsSchema = "datasource_" . $dsID;
  $fcCube = "_fcastcube_" . $fcID;
  $fcMeta = "_fcast_" . $fcID;

  #get our data source name
  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  #convert the ID strings into arrays
  @timeIDs = split(/,/, $timeIDstring);
  @productIDs = split(/,/, $productIDstring);
  @geoIDs = split(/,/, $geoIDstring);

  #convert dimension DSR IDs into human-readable names
  %geoNames = dsr_get_base_item_name_hash($db, $dsSchema, "g");
  %productNames = dsr_get_base_item_name_hash($db, $dsSchema, "p");
  %timeperiodNames = dsr_get_base_item_name_hash($db, $dsSchema, "t");
  $fcastMeasureName = KAPutil_get_item_ID_name($db, $dsSchema, "m", $measureID);

  #if we weren't given a specific product/geo combo to display, choose the 1st
  if ((!defined($dispProduct)) || (!defined($dispGeography)))
  {
    $dispProduct = $productIDs[0];
    $dispGeography = $geoIDs[0];
  }

  #print page header
  print <<END_HTML;
<DIV CLASS="container-fluid">

  <DIV CLASS="row">
    <DIV CLASS="col">
      <STRONG>Forecast Method: </STRONG>$type
    </DIV>
  </DIV>

END_HTML

  #output the data selector
  print <<END_HTML;
  <DIV CLASS="row">
    <DIV CLASS="col text-center">
      <P>&nbsp;</P>
      <B>Product:</B><BR>
      <SELECT CLASS="form-select" ID="prodsel" onChange="reloadPage()">
END_HTML

  foreach $product (@productIDs)
  {
    if ($product == $dispProduct)
    {
      print("<OPTION SELECTED VALUE=\"$product\">$productNames{$product}</OPTION>\n");
    }
    else
    {
      print("<OPTION VALUE=\"$product\">$productNames{$product}</OPTION>\n");
    }
  }

  print <<END_HTML;
      </SELECT>
    </DIV>
    <DIV CLASS="col text-center">
      <P>&nbsp;</P>
      <B>Geography:</B><BR>
      <SELECT CLASS="form-select" ID="geosel" onChange="reloadPage()">
END_HTML

  foreach $geography (@geoIDs)
  {
    if ($geography == $dispGeography)
    {
      print("<OPTION SELECTED VALUE=\"$geography\">$geoNames{$geography}</OPTION>\n");
    }
    else
    {
      print("<OPTION VALUE=\"$geography\">$geoNames{$geography}</OPTION>\n");
    }
  }

  print <<END_HTML;
      </SELECT>
    </DIV>
  </DIV>
END_HTML


  #------------------------------------------------------------------------
  #
  # Determine AI commentary and display modification based on issues the
  # forecasting engine noticed during processing.
  #

  #get commentary and status left by forecasting engine
  $query = "SELECT status, commentary FROM $dsSchema.$fcMeta \
      WHERE product = $dispProduct AND geography = $dispGeography";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($fcStatus, $fcCommentary) = $dbOutput->fetchrow_array;

  if ($fcCommentary =~ m/SES\-Truncated/)
  {
    $AI_warning = "KCast wasn't able to create a forecast for all of the \
        requested time periods. The selected forecasting method is often only \
        valid for a small number of time periods in the future.";
  }

  if ($fcCommentary =~ m/HWmult\-zeroes/)
  {
    $AI_warning = "KCast wasn't able to create a forecast for this \
        product/geography combination using the multiplicative Holt-Winters \
        method because the data contains zeroes.";
  }


  #------------------------------------------------------------------------
  #
  # Output any warnings/errors the user needs to know about.
  #

  if (length($AI_warning) > 0)
  {
    print <<END_HTML;
  <P>&nbsp;</P>
  <DIV ID="alert-results-warning" CLASS="alert alert-warning alert-dismissible fade show" role="alert" STYLE="width:75%; margin-left:12.5%;">
    <DIV CLASS="text-center"><STRONG>WARNING: </STRONG>$AI_warning</DIV>
    <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="alert"></BUTTON>
  </DIV>
  </P>&nbsp;</P>
END_HTML
  }


  #------------------------------------------------------------------------
  #
  # Output forecast value graph
  #

  #determine the date at which we start displaying forecasted values
  $query = "SELECT DATE(endDate) FROM $dsSchema.$fcCube \
      WHERE source='forecast' ORDER BY endDate ASC LIMIT 1";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($fcastStartDate) = $dbOutput->fetchrow_array;

  print <<END_HTML;
  <DIV CLASS="row">
    <DIV CLASS="col">

      <P>&nbsp;</P>
      <DIV CLASS="card">
        <DIV CLASS="card-body">

          <H4>Forecast</H4>
          <DIV ID="forecast-chart"></DIV>

<SCRIPT>
const URL_DATA = 'ajaxForecastGraph.cld?f=$fcID&ds=$dsID&p=$dispProduct&g=$dispGeography';
const URL_SCHEMA = 'ajaxForecastGraph.cld?f=$fcID&ds=$dsID&p=$dispProduct&g=$dispGeography&s=1';

const jsonify = res => res.json();
const dataFetch = fetch(URL_DATA).then(jsonify);
const schemaFetch = fetch(URL_SCHEMA).then(jsonify);

Promise.all([dataFetch, schemaFetch]).then(([data, schema]) =>
{
  let fusionTable = new FusionCharts.DataStore().createDataTable(data, schema);

  new FusionCharts(
  {
    type: 'timeseries',
    renderAt: 'forecast-chart',
    width: '95%',
    height: 400,
    dataSource:
    {
      extensions:
      {
        prediction:
        {
          date: '$fcastStartDate',
          style:
          {
            plot: 'line'
          }
        }
      },
      data: fusionTable,
      chart:
      {
        exportEnabled: 0
      },
      yAxis: [
      {
        plot:
        {
          value: 'Forecasted Measure',
          type: 'line'
        },
        title: `$fcastMeasureName`
      }
      ]
    }
  }).render();
});
</SCRIPT>

        </DIV>
      </DIV>

    </DIV>
  </DIV>
END_HTML

  print <<END_HTML;
<DIV CLASS="row">
  <DIV CLASS="col">

    <P>&nbsp;</P>
    <DIV CLASS="card">
      <DIV CLASS="card-body">

        <H4>External Factors</H4>
        <DIV ID="elasticity-charts"></DIV>

<SCRIPT>
const URL1_DATA = 'ajaxExternalsGraph.cld?f=$fcID&ds=$dsID&p=$dispProduct&g=$dispGeography';
const URL1_SCHEMA = 'ajaxExternalsGraph.cld?f=$fcID&ds=$dsID&p=$dispProduct&g=$dispGeography&s=1';

const jsonify1 = res => res.json();
const dataFetch1 = fetch(URL1_DATA).then(jsonify1);
const schemaFetch1 = fetch(URL1_SCHEMA).then(jsonify1);

Promise.all([dataFetch1, schemaFetch1]).then(([data, schema]) =>
{
  let fusionTable1 = new FusionCharts.DataStore().createDataTable(data, schema);

  new FusionCharts(
  {
    type: 'timeseries',
    renderAt: 'elasticity-charts',
    width: '95%',
    height: vpHeight,
    dataSource:
    {
      extensions:
      {
        prediction:
        {
          date: `$fcastStartDate`,
          style:
          {
            plot: 'line'
          }
        }
      },
      data: fusionTable1,
      chart:
      {
        exportEnabled: 0
      },
      yAxis: [
      {
        plot:
        {
          value: 'Forecast',
          type: 'line'
        },
        title: `$fcastMeasureName`
      },
      {
        plot:
        {
          value: 'Display Promotion',
          type: 'column'
        },
        title: 'Display Promotion'
      },
      {
        plot:
        {
          value: 'Feature Promotion',
          type: 'column'
        },
        title: 'Feature Promotion',
      },
      {
        plot:
        {
          value: 'Display & Feature Promotion',
          type: 'column'
        },
        title: 'Display & Feature Promotion'
      },
      {
        plot:
        {
          value: 'Price Promotion',
          type: 'column'
        },
        title: 'Price Promotion'
      }
      ]
    }
  }).render();
});
</SCRIPT>

      </DIV>
    </DIV>

  </DIV>
</DIV>
END_HTML

  #output error summary chart
  $query = "SELECT bias, mad, mse, mape FROM $dsSchema.$fcMeta \
      WHERE geography=$dispGeography AND product=$dispProduct";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ($bias, $mad, $mse, $mape) = $dbOutput->fetchrow_array;

  $displayBias = sprintf("%.1f", $bias);
  if ($displayBias > 0)
  {
    $displayBiasInfo = "higher";
  }
  else
  {
    $displayBiasInfo = "lower";
  }

  $displayMAD = sprintf("%.1f", $mad);

  $displayMSE = sprintf("%.0f", $mse);

  $displayMAPE = $mape * 100;
  $displayMAPE = sprintf("%.1f", $displayMAPE);

  print <<END_HTML;
  <DIV CLASS="row">
    <DIV CLASS="col">
      <P>&nbsp;</P>
      <TABLE CLASS="table table-striped table-hover table-sm table-bordered w-50">
        <THEAD><TR>
          <TH>Accuracy Measure</TH>
          <TH>Value</TH>
        </TR></THEAD>
        <TR>
          <TD>Bias</TD>
          <TD>
            $displayBias
            <A HREF="#" data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
              data-bs-content="This model is biased to forecast values that are $displayBiasInfo than actual values by an average of $displayBias">
              <I CLASS="bi bi-info-circle"></I>
            </A>
          </TD>
        </TR>
        <TR>
          <TD>Mean Absolute Deviation (MAD)</TD>
          <TD>
            $displayMAD
            <A HREF="#" data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
              data-bs-content="On average, this model forecasts values that are off actual values by $displayMAD">
              <I CLASS="bi bi-info-circle"></I>
            </A>
          </TD>
        </TR>
        <TR>
          <TD>Mean Squared Error (MSE)</TD>
          <TD>
            $displayMSE
            <A HREF="#" data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
              data-bs-content="The average of the squared total forecasting error is $displayMSE. This indicates how accurate the forecasting model is over the entire data history.">
              <I CLASS="bi bi-info-circle"></I>
            </A>
          </TD>
        </TR>
        <TR>
          <TD>Mean Absolute Percent Error</TD>
          <TD>
            $displayMAPE\%
            <A HREF="#" data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
              data-bs-content="The average absolute error percentage over all data used to verify the model is $displayMAPE\%.">
              <I CLASS="bi bi-info-circle"></I>
            </A>
          </TD>
        </TR>
      </TABLE>

      <P>&nbsp;</P>

      <DIV ID="data-collapse">
        <DIV CLASS="card border-primary mx-auto">
          <DIV CLASS="card-header bg-primary text-white">
            <A CLASS="card-link text-white text-decoration-none" data-bs-toggle="collapse" HREF="#collapse-data-table">Forecast Data Table</A>
            <I CLASS="fas fa-caret-down"></I>
          </DIV>
          <DIV ID="collapse-data-table" CLASS="collapse" data-bs-parent="#data-collapse">
            <DIV CLASS="card-body">

              <DIV CLASS="row">
                <DIV CLASS="col text-center">

                  <P>&nbsp;</P>
                  <DIV CLASS="card">
                    <DIV CLASS="card-body">
                      <DIV ID="chartdiv"></DIV>
<SCRIPT>
  let chart = new FusionCharts({'type': 'MSLine', 'width': '99%', 'height': '400', 'dataFormat': 'json'});
  chart.setJSONUrl('/app/forecast/ajaxForecastActualGraph.cld?fcID=$fcID&prod=$dispProduct&geo=$dispGeography');
  chart.render('chartdiv');
</SCRIPT>
                    </DIV>
                  </DIV>

                </DIV>
              </DIV>

              <P>&nbsp;</P>

              <TABLE CLASS="table table-striped table-bordered table-sm table-hover">
                <THEAD><TR>
                  <TH>Time Period</TH>
                  <TH>Actual</TH>
                  <TH>Forecast</TH>
                  <TH>Error</TH>
                  <TH>MAD</TH>
                  <TH>MSE</TH>
                  <TH>MAPE</TH>
                </TR></THEAD>
END_HTML

  #Output data chart with error measures
  foreach $time (@timeIDs)
  {
    $query = "SELECT measure, forecast, error, mad, mse, mape \
        FROM $dsSchema.$fcCube \
        WHERE time=$time AND geography=$dispGeography AND product=$dispProduct";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;

    ($actualValue, $forecast, $error, $mad, $mse, $mape) = $dbOutput->fetchrow_array;

    $actualValue = html_format_number($actualValue, 2);
    $forecast = html_format_number($forecast, 2);
    $error = html_format_number($error, 2);
    $mad = html_format_number($mad, 2);
    $mse = html_format_number($mse, 2);
    $displayMAPE = $mape * 100;
    $displayMAPE = html_format_number($displayMAPE, 2);

    print <<END_HTML
                <TR>
                  <TD>$timeperiodNames{$time}</TD>
                  <TD CLASS="text-right">$actualValue</TD>
                  <TD CLASS="text-right">$forecast</TD>
                  <TD CLASS="text-right">$error</TD>
                  <TD CLASS="text-right">$mad</TD>
                  <TD CLASS="text-right">$mse</TD>
                  <TD CLASS="text-right">$displayMAPE\%</TD>
                </TR>
END_HTML
  }

  if ($periodType == 52)
  {
    $periodPrefix = "1 W/E";
  }
  elsif ($periodType == 12)
  {
    $periodPrefix = "1 M/E";
  }
  elsif ($periodType == 4)
  {
    $periodPrefix = "1 Q/E";
  }
  elsif ($periodType == 1)
  {
    $periodPrefix = "1 Y/E";
  }

  #output forecast values for future periods
  $query = "SELECT DATE_FORMAT(endDate, '%m/%d/%y'), forecast \
      FROM $dsSchema.$fcCube \
      WHERE geography=$dispGeography AND product=$dispProduct AND source='forecast' \
      ORDER BY endDate";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  while (($endDate, $forecast) = $dbOutput->fetchrow_array)
  {
    $forecast = html_format_number($forecast, 2);

    print <<END_HTML;
                <TR CLASS="table-success">
                  <TD>$periodPrefix $endDate</TD>
                  <TD></TD>
                  <TD CLASS="text-right">$forecast</TD>
                  <TD></TD>
                  <TD></TD>
                  <TD></TD>
                  <TD></TD>
                </TR>
END_HTML
  }

  print <<END_HTML;
              </TABLE>

            </DIV>
          </DIV>
        </DIV>
      </DIV>
END_HTML

  if (!$AI_noForecast)
  {
    print <<END_HTML;
      <P>&nbsp;</P>

      <DIV ID="tracking-collapse">
        <DIV CLASS="card border-primary mx-auto">
          <DIV CLASS="card-header bg-primary text-white">
            <A CLASS="card-link text-white text-decoration-none" data-bs-toggle="collapse" HREF="#collapse-tracking-table">Tracking Signals</A>
            <I CLASS="fas fa-caret-down"></I>
          </DIV>
          <DIV ID="collapse-tracking-table" CLASS="collapse" data-bs-parent="#tracking-collapse">
            <DIV CLASS="card-body">

              <DIV ID="trackingsignaldiv"></DIV>
              <SCRIPT>
                let trackingChart = new FusionCharts({'type': 'MSLine', 'width': '99%', 'height': '300', 'dataFormat': 'json'});
                trackingChart.setJSONUrl('/app/forecast/ajaxTrackingSignalGraph.cld?fcID=$fcID&prod=$dispProduct&geo=$dispGeography');
                trackingChart.render('trackingsignaldiv');
              </SCRIPT>

              <P>
              A positive tracking signal indicates that demand is greater than forecast,
              while a negative tracking signal indicates that demand is less than forecast.
              An accurate forecast should have approximately equal amounts of positive and
              negative error.

              <P>&nbsp;</P>

              <TABLE CLASS="table table-bordered table-striped table-sm table-hover">
                <THEAD><TR>
                  <TH>Time Period</TH>
                  <TH>Actual</TH>
                  <TH>Forecast</TH>
                  <TH>RSFE</TH>
                  <TH>MAD</TH>
                  <TH>Tracking Signal</TH>
                </TR></THEAD>
END_HTML

  #output tracking signal data grid
  foreach $time (@timeIDs)
  {
    $query = "SELECT measure, forecast, rsfe, cummad, trackingsignal \
        FROM $dsSchema.$fcCube \
        WHERE time=$time AND geography=$dispGeography AND product=$dispProduct";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;

    ($value, $forecast, $rsfe, $mad, $trackingsignal) = $dbOutput->fetchrow_array;

    print <<END_HTML;
                <TR>
                  <TD>$timeperiodNames{$time}</TD>
                  <TD>$value</TD>
                  <TD>$forecast</TD>
                  <TD>$rsfe</TD>
                  <TD>$mad</TD>
                  <TD>$trackingsignal</TD>
                </TR>
END_HTML
  }

  print <<END_HTML;
              </TABLE>

            </DIV>
          </DIV>
        </DIV>
      </DIV>
END_HTML
  }

  print <<END_HTML;
    </DIV>
  </DIV>
</DIV>
<P>
END_HTML

  print_html_footer();

#EOF
