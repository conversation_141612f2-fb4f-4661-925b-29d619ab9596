#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;


#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');
  $dim = $q->param('dim');
  $segID = $q->param('s');

  #sanitize parameters
  $dsID = utils_sanitize_integer($dsID);
  if (!defined($dsID))
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
  $dim = utils_sanitize_dim($dim);
  if (!defined($dim))
  {
    print("Status: 403 Not authorized\n");
    exit;
  }

  print("Expires: -1\n");
  print("Cache-Control: private, max-age=0");
  print("Content-type: application/json\n\n");

  $dsSchema = "datasource_" . $dsID;

  $dbStub = KAPutil_get_dim_stub_name($dim);
  $dbName = $dbStub . "seg_rules";

  #connect to the database
  $db = KAPutil_connect_to_database();

  #make sure we have read privs for this data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to view this data source.");
  }

  #get a hash of the segmentations, segment, & attribute names in this segmentation
  %segNameHash = DSRsegmentation_get_segmentations_hash($db, $dsSchema, $dim);
  %segmentHash = DSRseg_get_segments_hash($db, $dsSchema, $dim);
  %attrHash = DSRattr_get_attributes_hash($db, $dsSchema, $dim);

  #get the segmentation rules for the specified segmentation
  $query = "SELECT ID, segmentID, rule, filter1, filter2 \
      FROM $dsSchema.$dbName WHERE segmentationID=$segID ORDER BY step";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

    print <<JSON_LABEL;
[
JSON_LABEL

  $count = 1;
  while (($ruleID, $segmentID, $rule, $filter1, $filter2) = $dbOutput->fetchrow_array)
  {
    $specialRule = 0;

    #convert the rule to something human-readable
    if ($rule =~ m/^TEXT (.*?) (.*?) (.*)$/)
    {
      $matchSide = $1;
      $matchType = $2;
      $matchStr = $3;
      $rule = "Assign $1 items that $matchType $matchStr";
    }
    elsif ($rule =~ m/^ATTR (.*?) (.*?) (.*?) (.*)$/)
    {
      $matchSide = $1;
      $attrVal = $2;
      $attrName = $attrHash{$attrVal};
      $matchType = $3;
      $matchStr = $4;
      $rule = "Assign $matchSide items with a $attrName attribute that $matchType $matchStr";
    }
    elsif ($rule =~ m/^SEGMATCH (.*?) (.*?) (.*?) (.*)$/)
    {
      $matchSide = $1;
      $segID = $2;
      $segName = $segNameHash{$segID};
      $matchType = $3;
      $matchStr = $4;
      $rule = "Assign $matchSide items that are members of segments in $segName that $matchType $matchStr";
    }
    elsif ($rule =~ m/^SEG (.*?) (.*?) (.*)$/)
    {
      $matchSide = $1;
      $matchSeg = $2;
      $matchSegment = $3;

      @matchSegments = split(',', $matchSegment);
      $matchSegmentStr = "";
      foreach $matchSegmentID (@matchSegments)
      {
        $segmentName = $segmentHash{$matchSegmentID};
        if (!defined($segmentName))
        {
          $segmentName = "<SPAN CLASS='bg-danger text-light'>DELETED SEGMENT</SPAN>";
        }
        $matchSegmentStr .= $segmentName . ", ";
      }
      chop($matchSegmentStr);  chop($matchSegmentStr);

      $matchSeg = $segNameHash{$matchSeg};
      $rule = "Assign $matchSide items that are members of the $matchSegmentStr segment(s) of $matchSeg";
    }
    elsif ($rule =~ m/^SEGVAL (.*?) (.*?) (.*?) (.*?) (.*)$/)
    {
      $matchSide = $1;
      $matchSeg = $2;
      $matchOp = $3;
      $matchNumVal = $4;
      $matchNumValUpper = $5;

      $matchSeg = $segNameHash{$matchSeg};

      if ($matchOp eq "gt")
      {
        $matchOp = "greater than";
      }
      elsif ($matchOp eq "lt")
      {
        $matchOp = "less than";
      }
      elsif ($matchOp eq "eq")
      {
        $matchOp = "equal to";
      }

      if ($matchOp eq "bt")
      {
        $rule = "Assign $matchSide items that have a segment value between $matchNumVal and $matchNumValUpper in the $matchSeg segmentation";
      }
      else
      {
        $rule = "Assign $matchSide items that have a segment value $matchOp $matchNumVal in the $matchSeg segmentation";
      }
    }

    elsif ($rule =~ m/^SEGFILLDOWN (.*)$/)
    {
      $matchSeg = $1;

      $matchSeg = $segNameHash{$matchSeg};
      $rule = "Assign unsegmented items to the same segment they're assigned to in the $matchSeg segmentation";
      $specialRule = 1;
    }

    elsif ($rule =~ m/^CATCHALL$/)
    {
      $rule = "Assign all remaining unsegmented items";
      $specialRule = 1;
    }

    if ($filter1 =~ m/(\d+) (.*)/)
    {
      $filter1SegName = $segNameHash{$1};

      @filter1Segments = split(',', $2);
      $filter1SegNames = "";
      foreach $filter1SegmentID (@filter1Segments)
      {
        $filter1SegNames .= "$segmentHash{$filter1SegmentID}, ";
      }
      chop($filter1SegNames);  chop($filter1SegNames);

      $rule .= " when the items are members of the $filter1SegNames segment(s) of $filter1SegName";
    }

    if ($filter2 =~ m/(\d+) (.*)/)
    {
      $filter2SegName = $segNameHash{$1};

      @filter2Segments = split(' ', $2);
      $filter2SegNames = "";
      foreach $filter2SegmentID (@filter2Segments)
      {
        $filter2SegNames .= "$segmentHash{$filter2SegmentID}, ";
      }
      chop($filter2SegNames);  chop($filter2SegNames);

      $rule .= "; and are also members of the $filter2SegNames segment(s) of $filter2SegName";
    }

    if ((length($segmentHash{$segmentID}) < 1) && ($specialRule eq 0))
    {
      $segmentHash{$segmentID} = "<SPAN STYLE='background-color:salmon;'>(DELETED)</SPAN>";
    }

    $segName = $segmentHash{$segmentID};
    $segName  =~ s/\"/\\"/g;

    print <<JSON_LABEL;
    {
      "ID": $ruleID,
      "Segment": "$segName",
      "Rule": "$rule"
    }
JSON_LABEL
    if ($count < $status)
    {
      print(",");
    }

    $count++;
  }

  print <<JSON_LABEL;
]
JSON_LABEL

#EOF
