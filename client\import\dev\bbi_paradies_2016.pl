#!/usr/bin/perl

use Text::CSV;

#Import Paradies Spin data for BBI

  #instantiate our CSV object
  $csv = Text::CSV->new( {binary => 1} );

  #define header mappings
  my %headerMappings = (
  "Customer ID" => "gattr:Customer ID",
  "Name" => "Geography",
  "City" => "gattr:City",
  "St  " => "gattr:St",
  "Item ID" => "pattr:Item ID",
  "Qty Shp" => "Qty Ship",
  "Description" => "Product");

  open(INPUT, "$ARGV[0]");
  open(OUTPUT, ">$ARGV[1]");

  #grab the first line, which contains our date info
  $line = <INPUT>;
  $csv->parse($line);
  @columns = $csv->fields();
  $timeperiod = $columns[0];

  #if we're a quarter-ending date ("1Q 2017 ABCDEF") into a CPG date
  if ($timeperiod =~ m/^Q(\d) (\d+) .*$/)
  {
    if ($1 == 1)
    {
      $timeperiod = "3 months ending 03/31/$2";
    }
    elsif ($1 == 2)
    {
      $timeperiod = "3 months ending 06/30/$2";
    }
    elsif ($1 == 3)
    {
      $timeperiod = "3 months ending 09/30/$2";
    }
    elsif ($1 == 4)
    {
      $timeperiod = "3 months ending 12/31/$2";
    }
  }

  #else if we're a month-ending date ("FEBRUARY 2018 ABCDEF")
  elsif ($timeperiod =~ m/^(.*?) (\d+) .*$/)
  {
    $year = $2;
    if ($1 =~ m/^jan/i)
    {
      $timeperiod = "1 month ending 01/31/$year";
    }
    elsif ($1 =~ m/^feb/i)
    {
      $timeperiod = "1 month ending 02/28/$year";
    }
    elsif ($1 =~ m/^mar/i)
    {
      $timeperiod = "1 month ending 03/31/$year";
    }
    elsif ($1 =~ m/^apr/i)
    {
      $timeperiod = "1 month ending 04/30/$year";
    }
    elsif ($1 =~ m/^may/i)
    {
      $timeperiod = "1 month ending 05/31/$year";
    }
    elsif ($1 =~ m/^jun/i)
    {
      $timeperiod = "1 month ending 06/30/$year";
    }
    elsif ($1 =~ m/^jul/i)
    {
      $timeperiod = "1 month ending 07/31/$year";
    }
    elsif ($1 =~ m/^aug/i)
    {
      $timeperiod = "1 month ending 08/31/$year";
    }
    elsif ($1 =~ m/^sep/i)
    {
      $timeperiod = "1 month ending 09/30/$year";
    }
    elsif ($1 =~ m/^oct/i)
    {
      $timeperiod = "1 month ending 10/31/$year";
    }
    elsif ($1 =~ m/^nov/i)
    {
      $timeperiod = "1 month ending 11/30/$year";
    }
    elsif ($1 =~ m/^dec/i)
    {
      $timeperiod = "1 month ending 12/31/$year";
    }
  }

  #burn line(s) containing rebate %
  $line = <INPUT>;
  $csv->parse($line);
  @columns = $csv->fields();
  while (length($columns[0]) < 1)
  {
    $line = <INPUT>;
    $csv->parse($line);
    @columns = $csv->fields();
  }

  #parse the primary header line
  $csv->parse($line);
  @columns = $csv->fields();

  $idx = 0;
  foreach $header (@columns)
  {
    if (length($headerMappings{$header}) > 0)
    {
      $header = $headerMappings{$header};

      if ($header eq "Geography")
      {
        $geoIdx = $idx;
      }

    }
    $idx++;
  }

  @tmp = ('Time Period');
  push(@tmp, @columns);

  #output the headers
  $csv->combine(@tmp);
  $line = $csv->string();
  print OUTPUT "$line\n";

  while ($line = <INPUT>)
  {
    $csv->parse($line);
    @columns = $csv->fields();

    #if there isn't a valid product in the line, ignore it
    if (length($columns[0]) < 1)
    {
      next;
    }

    #normalize the geography name (yank "Rtl")
    $columns[$geoIdx] =~ s/Rtl//;;

    @tmp = ("$timeperiod");
    push(@tmp, @columns);

    $csv->combine(@tmp);
    $line = $csv->string();

    print OUTPUT "$line\n";
  }

  close(OUTPUT);
