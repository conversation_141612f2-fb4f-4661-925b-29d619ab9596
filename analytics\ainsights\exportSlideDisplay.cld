#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::DataSources;
use Lib::AInsights::AInsights;
use Lib::AInsights::Utils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  #output Content-type header
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  #output HTML
  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: AInsights</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<SCRIPT SRC="/fusioncharts-3.18.0/js/fusioncharts.js"></SCRIPT>
<SCRIPT SRC="/fusioncharts-3.18.0/js/themes/fusioncharts.theme.zune.js"></SCRIPT>

</HEAD>

<BODY>
END_HTML
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  $session = CGI::Session->new();

  #get CGI parameters
  $modelID = $q->param('m');
  $geoID = $q->param('g');
  $area = $q->param('a');
  $level = $q->param('l');
  $block = $q->param('b');
  $credentials = $q->param('auth');

  #connect to the database
  $db = KAPutil_connect_to_database();

  print_html_header();

  if ($area eq "top")
  {
    $cardScript = "insightsCards.cld";
  }
  elsif ($area eq "dist")
  {
    $cardScript = "distBrandCards.cld";
  }

  print <<END_HTML;
<DIV CLASS="container-fluid">


  <DIV CLASS="row">
    <DIV CLASS="col">

      <P>
      <DIV ID="card-insights">
      </DIV>
      <SCRIPT>
        \$('#card-insights').load('$cardScript?m=$modelID&b=$block&g=$geoID&auth=$credentials');
      </SCRIPT>


    </DIV> <!-- col -->
  </DIV>  <!-- row -->
</DIV>
<P>
END_HTML

  print_html_footer();


#EOF
