#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $prompt = $q->param('p');
  $token = $q->param('t');

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  print("Content-type: application/html\n\n");

  $workingFlowID = $session->param('prepAIflowID');
  $tokenString = $session->param('prepAItokens');
  $lastConvoTime = $session->param('prepAIconvoTime');

  #if our last "conversation" was more than 12 hours ago, wipe out the token
  #string so we start fresh
  if (length($lastConvoTime) > 5)
  {
    $query = "SELECT 1 WHERE DATE_SUB(NOW(), INTERVAL 12 HOUR) > '$lastConvoTime'";
    $dbOutput = $prepDB->prepare($query);
    $status = $convoExpired = $dbOutput->execute();
    PrepUtils_handle_db_err($prepDB, $status, $query);
    if ($convoExpired)
    {
      $workingFlowID = 0;
      $tokenString = "";
    }
  }


  #
  #-----------------------------------------------------------------------------
  #


  #help prompt
  if ($prompt =~ m/help(?:\.?)$/i)
  {
    print <<END_HTML;
    Help.
END_HTML
  }


  #--------------------- ABOUT ----------------------------

  elsif ($prompt =~ m/about you/i)
  {
    print <<END_HTML;
<P>
Fantastic - I'm glad you're interested in learning more about me!
</P>

<P>
<B>What I am:</B>
<UL>
  <LI>A specialized language model developed by Koala to help with common data preparation tasks.
  <LI>Trained on a large set of common tasks and requests from users like you.
  <LI>Still under development, but constantly learning and improving.
</UL>
</P>

<P>
<B>What I can do:</B>
<UL>
  <LI>Perform common data preparation tasks for you.
  <LI>Answer questions about your data, your data flows, and your data jobs.
  <LI>Make suggestions for cleansing, harmonizing, and enriching your data sets.
  <LI>Help track down data problems and provide fixes.
  <LI>Remind you about routine tasks.
</UL>
</P>

<P>
<B>I'm excited to learn more about your data and how I can assist you. Feel free to ask me anything!</B>
</P>

END_HTML
  }


  #--------------------- WHAT CAN YOU DO -----------------------------

  elsif ($prompt =~ m/what can (?:you |I )?do/i)
  {
    print <<END_HTML;
<P>
Some of the things I can do include:
</P>
<P>
<BUTTON CLASS="btn btn-primary btn-sm" onclick="generateAI('How many data flows do I have?');">How many data flows do I have?</BUTTON>
</P>
END_HTML
  }


  #-------------------- HOW MANY DATA FLOWS ------------------------------

  elsif (($prompt =~ m/how many/i) && ($prompt =~ m/flows/i))
  {

    $query = "SELECT COUNT(ID) FROM prep.flows WHERE userID=$userID";
    $dbOutput = $prepDB->prepare($query);
    $status = $dbOutput->execute();
    ($flowCount) = $dbOutput->fetchrow_array;

    $query = "SELECT COUNT(ID) FROM prep.jobs
        WHERE (userID=$userID OR ownerID=$userID) AND state NOT IN ('LOADED', 'ERROR')";
    $dbOutput = $prepDB->prepare($query);
    $status = $dbOutput->execute();
    PrepUtils_handle_db_err($prepDB, $status, $query);
    ($runCount) = $dbOutput->fetchrow_array;
    if ($runCount < 1)
    {
      $runStatement = "You don't have any jobs running in your data flows right now.";
    }
    elsif ($runCount == 1)
    {
      $runStatement = "Right now you have one job running in your data flows.";
    }
    else
    {
      $runStatement = "Right now, you have $runCount jobs running in your data flows.";
    }

    print <<END_HTML;
<P>
You have $flowCount data flows.
$runStatement
</P>
END_HTML
  }


  #-------------------- HOW MANY JOBS ------------------------------

  elsif ((($prompt =~ m/how many/i) || ($prompt =~ m/running/i)) &&
      (($prompt =~ m/jobs/i) || ($prompt =~ m/flows/i)))
  {

    $query = "SELECT COUNT(ID) FROM prep.jobs
        WHERE (userID=$userID OR ownerID=$userID) AND state NOT IN ('LOADED', 'ERROR')";
    $dbOutput = $prepDB->prepare($query);
    $status = $dbOutput->execute();
    PrepUtils_handle_db_err($prepDB, $status, $query);
    ($runCount) = $dbOutput->fetchrow_array;
    if ($runCount < 1)
    {
      $runStatement = "You don't have any jobs running right now.";
    }
    elsif ($runCount == 1)
    {
      $runStatement = "Right now you have one job running in your data flows.";
    }
    else
    {
      $runStatement = "Right now, you have $runCount jobs running in your data flows.";
    }

    print <<END_HTML;
<P>
$runStatement
</P>
END_HTML
  }


  #-------------------LARGEST DATA FLOWS ----------------------------

  #user asking which of their data flows is the largest
  elsif (($prompt =~ m/data flow/i) && ($prompt =~ m/largest/i))
  {
    $query = "SELECT DISTINCT flowID, dataProcessed FROM prep.job_history
        WHERE DATE_SUB(NOW(), INTERVAL 52 WEEK) < startTime
        ORDER BY dataProcessed DESC LIMIT 3";
    $dbOutput = $prepDB->prepare($query);
    $status = $dbOutput->execute();

    if ($status < 1)
    {
    print <<END_HTML;
<P>
Unfortunately, it doesn't look like you've worked with enough data for me to generate
an answer at the moment. Let's run some data flows, and then I can give you a
better answer.
</P>
END_HTML
    }

    else
    {
      print <<END_HTML;
<P>
Your largest data flows are:
<TABLE CLASS="table table-striped table-bordered table-sm">
END_HTML

      while (($flowID, $dataProcessed) = $dbOutput->fetchrow_array)
      {
        $flowName = prep_flow_id_to_name($prepDB, $flowID);
        $dataProcessed = PrepUtils_autoscale_data_size($dataProcessed);
        print("<TR><TD>$flowName</TD><TD>$dataProcessed</TD></TR>\n");
      }

      print <<END_HTML;
</TABLE>
</P>
END_HTML
    }
  }


  #----------------------- HOW MANY TIMES IN MOST RECENT JOB -------------------

  elsif (($prompt =~ m/how many/i) && ($prompt =~ m/time/i) && ($prompt =~ m/recent job/i))
  {
    $query = "SELECT ID, flowID FROM prep.jobs
        WHERE (userID=$userID OR ownerID=$userID) AND state='LOADED'
        ORDER BY lastAction DESC LIMIT 1";
    $dbOutput = $prepDB->prepare($query);
    $status = $dbOutput->execute();
    ($mostRecentJobID, $flowID) = $dbOutput->fetchrow_array;

    if ($mostRecentJobID < 1)
    {
    print <<END_HTML;
<P>
You don't currently have any jobs in your data flows.
</P>
END_HTML
    }

    $flowName = prep_flow_id_to_name($prepDB, $flowID);

    #get name of column containing time periods
    $masterColTable = "prep_data.$mostRecentJobID" . "_master_cols";
    $masterTable = "prep_data.$mostRecentJobID" . "_master";

    $query = "SELECT ID FROM $masterColTable WHERE type='time'";
    $dbOutput = $prepDB->prepare($query);
    $status = $dbOutput->execute();
    ($timeColumnID) = $dbOutput->fetchrow_array;
    $timeColumnName = "column_" . $timeColumnID;

    $query = "SELECT DISTINCT $timeColumnName FROM $masterTable";
    $dbOutput = $prepDB->prepare($query);
    $status = $timeCount = $dbOutput->execute();
    PrepUtils_handle_db_err($prepDB, $status, $query);

    print <<END_HTML;
<P>
There were $timeCount distinct time periods in the most recent data job you ran in the
<CODE>$flowName</CODE> data flow, including:
<TABLE CLASS="table table-striped table-bordered table-sm">
END_HTML

    while (($time) = $dbOutput->fetchrow_array)
    {
      print <<END_HTML;
  <TR><TD>$time</TD></TR>
END_HTML
    }

  print <<END_HTML;
</TABLE>
</P>
END_HTML
  }



  #
  #-----------------------------------------------------------------------------
  # action requests


  #------------------------ RUN DATA FLOW -------------------------------------

  #if we're being asked to run a data flow
  elsif ($prompt =~ /run (?:my |the )?(.*?) (?:data )?flow/i)
  {
    $flowName = $1;

    #see if we were given an explicit data flow name
    $q_flowName = $prepDB->quote($flowName);
    $query = "SELECT ID FROM prep.flows WHERE name=$q_flowName";
    $dbOutput = $prepDB->prepare($query);
    $status = $dbOutput->execute();
    PrepUtils_handle_db_err($prepDB, $status, $query);
    ($flowID) = $dbOutput->fetchrow_array;
    if ($flowID > 0)
    {
      print <<END_HTML;
<P>
Would you like me to run the $flowName data flow right now?
</P>
<P>
  <BUTTON CLASS="btn btn-secondary btn-sm" onclick="generateAI('No.', 'runx')">No</BUTTON>
  <BUTTON CLASS="btn btn-primary btn-sm" onclick="window.open('runFlow.cld?f=$flowID', '_blank')">Yes</BUTTON>
</P>
END_HTML
    }

    #else we just got a stub of a name - look for possibilities and ask
    else
    {
      $fuzzyFlowName = "%$flowName%";
      $q_flowName = $prepDB->quote($fuzzyFlowName);
      $query = "SELECT ID, name FROM prep.flows WHERE name LIKE $q_flowName ORDER BY name";
      $dbOutput = $prepDB->prepare($query);
      $status = $flowCount = $dbOutput->execute();
      PrepUtils_handle_db_err($prepDB, $status, $query);

      #if we found one matching data flow
      if ($flowCount == 1)
      {
        ($flowID, $flowName) = $dbOutput->fetchrow_array;
        print <<END_HTML;
I think you're asking me to run the $flowName data flow?
END_HTML
      }

      #else if we found several matching data flows
      elsif ($flowCount > 1)
      {
        print <<END_HTML;
<P>
I found $status $flowName data flows. Which one are you interested in running?
</P>
END_HTML
        while (($flowID, $flowName) = $dbOutput->fetchrow_array)
        {
        print <<END_HTML;
  <BUTTON CLASS="btn btn-primary btn-sm mb-1" onclick="generateAI('Run my $flowName data flow.')">$flowName</BUTTON><BR>
END_HTML
        }

        print <<END_HTML;
  <BUTTON CLASS="btn btn-secondary btn-sm mb-1" onclick="generateAI('None of these.', 'runxmult')">None of these</BUTTON><BR>
END_HTML
      }

      #else we didn't find any matches
      else
      {
        print <<END_HTML;
Hmm. I wasn't able to find any data flows for $flowName. Can you try giving me
a more explicit data flow name?
END_HTML
      }
    }
  }

  #token telling us user doesn't want to run the proposed data flow
  elsif ($token eq "runx")
  {
    print <<END_HTML;
<P>
OK! We'll save it for later. How else I can help?
</P>
END_HTML
  }
  elsif ($token eq "runxmult")
  {
    print <<END_HTML;
<P>
OK!
</P>
END_HTML
  }


  #
  #-----------------------------------------------------------------------------
  #

  #admin-only requests for testing/demo purposes
  elsif (($prompt =~ /pretend to be (.*)$/i) && ($acctType == 10))
  {
    print <<END_HTML;
I thought that was you, Michael! I'll pretend to be $1 until you tell me to stop.
END_HTML
  }


  #-------------------------------------------------------------

  #default catch-all for a prompt we didn't understand
  else
  {
    print <<END_HTML;
Sorry, I didn't understand that one.
END_HTML
  }

  $query = "SELECT NOW() FROM prep.flows";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute();
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($mostRecentJobID, $flowID) = $dbOutput->fetchrow_array;

  #write out any session specific state info we want to hold onto
  $session->param('prepAIconvoTime', "0");
  $session->param('prepAItokens', "$tokenString");
  $session->flush();


#EOF
