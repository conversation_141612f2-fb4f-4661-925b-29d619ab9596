#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::AInsights::AInsights;
use Lib::AInsights::Utils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Export Insights to PowerPoint</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Please Wait...');
  return(true);
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">AInsights</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="insights.cld?pm=$priceModelID">$pricingName</A></LI>
    <LI CLASS="breadcrumb-item active">Export Insights to PowerPoint</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser and get the forecast to be deleted
  $q = new CGI;
  $priceModelID = $q->param('pm');
  $geoID = $q->param('g');

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #connect to the database
  $db = KAPutil_connect_to_database();

  #if we weren't passed a geography to display
  if ($geoID < 1)
  {

    #see if we have one to try in the analyst's cookie
    $geoID = $session->param("priceModelGeoSelection.$priceModelID");

    #if still nothing, use first available geography
    if ($geoID < 1)
    {
      @geoIDs = AInsights_Utils_get_model_geo_ids($db, $priceModelID);
      $geoID = $geoIDs[0];
    }
  }
  else
  {
    $session->param("priceModelGeoSelection.$priceModelID", "$geoID");
  }


  #get the name of the pricing model
  $pricingName = AInsights_ID_to_name($db, $priceModelID);

  print_html_header();

  #make sure we have at least read privs for this pricing model
  $privs = AInsights_rights($db, $userID, $priceModelID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to view this pricing model.");
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-11 col-lg-8 col-xl-7"> <!-- content -->


      <DIV CLASS="accordion mx-auto" ID="accordion">

        <DIV CLASS="accordion-item border-primary">
          <H2 CLASS="accordion-header">
            <BUTTON CLASS="accordion-button bg-primary text-white icon-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
              Export AInsights Presentation
            </BUTTON>
          </H2>
          <DIV ID="collapse1" CLASS="accordion-collapse collapse show" data-bs-parent="#accordion">
            <DIV CLASS="accordion-body">

              <FORM METHOD="post" ACTION="exportInsightsPPT.cld">
              <INPUT TYPE="hidden" NAME="m" VALUE="$priceModelID">
              <INPUT TYPE="hidden" NAME="g" VALUE="$geoID">
              <INPUT TYPE="hidden" NAME="a" VALUE="auto">
              <INPUT TYPE="hidden" NAME="auth" VALUE="sdkjdskjldskjdsk-sdkjdsjkds">

              <DIV CLASS="text-center">
                <BUTTON TYPE="button" CLASS="btn btn-secondary" onclick="location.href='main.cld'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
                <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-submit"><I CLASS="bi bi-cloud-download"></I> Export Presentation</BUTTON>
              </DIV>

              </FORM>

            </DIV>
          </DIV>
        </DIV>

        <DIV CLASS="accordion-item border-primary">
          <H2 CLASS="accordion-header">
            <BUTTON CLASS="accordion-button collapsed bg-primary text-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
              Export Specific AInsights Slides
            </BUTTON>
          </H2>
          <DIV ID="collapse2" CLASS="accordion-collapse collapse" data-bs-parent="#accordion">
            <DIV CLASS="accordion-body">

              <FORM METHOD="post" ACTION="exportInsightsPPT.cld">
              <INPUT TYPE="hidden" NAME="pm" VALUE="$priceModelID">

              <DIV CLASS="text-center">
                <BUTTON TYPE="button" CLASS="btn btn-secondary" onclick="location.href='main.cld'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
                <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-submit"><I CLASS="bi bi-cloud-download"></I> Export Slides</BUTTON>
              </DIV>

              </FORM>

            </DIV>
          </DIV>
        </DIV>

      </DIV>  <!-- accordion -->

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
