#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Export Expanded Report</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$("#btn-submit").prop('disabled', true);
  \$("#btn-submit").text("Please Wait...");
  return(true);
}
</SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/rpt/main">Reports</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?rpt=$rptID">$rptName</A></LI>
    <LI CLASS="breadcrumb-item active">Export Expanded Report</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser and get the report to be expanded
  $q = new CGI;
  $rptID = $q->param('rpt');
  $dim = $q->param('d');
  $action = $q->param('a');

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  $db = KAPutil_connect_to_database();

  $rptName = cube_id_to_name($db, $rptID);

  print_html_header();

  #make sure we have write privs for this data cube
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to export this report.");
  }

  #get the ID of the data source containing the report
  $query = "SELECT dsID FROM cubes WHERE ID=$rptID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($dsID) = $dbOutput->fetchrow_array;

  #assemble data source schema name
  $dsSchema = "datasource_" . $dsID;

  #figure out our database column and human-readable dimension names
  if ($dim eq "p")
  {
    $dimCol = "products";
    $dimName = "products";
  }
  elsif ($dim eq "g")
  {
    $dimCol = "geographies";
    $dimName = "geographies";
  }
  elsif ($dim eq "t")
  {
    $dimCol = "timeperiods";
    $dimName = "time periods";
  }
  elsif ($dim eq "m")
  {
    $dimCol = "measures";
    $dimName = "measures";
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-10 col-lg-7 col-xl-6"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Export Expanded Report</DIV>
        <DIV CLASS="card-body">

          <FORM METHOD="post" ACTION="exportExpandedDo.cld" onsubmit="return checkForm(this);">
          <INPUT TYPE="hidden" NAME="rpt" VALUE="$rptID">
          <INPUT TYPE="hidden" NAME="a" VALUE="$action">
          <INPUT TYPE="hidden" NAME="d" VALUE="$dim">

          <P>
          Which $dimName do you want to include in the expanded report? (By default, all items are included.)<BR>
          <SELECT CLASS="form-select w-100" NAME="items" MULTIPLE SIZE="10">
END_HTML

  #get the list of products in our cube, and display them
  %itemNameHash = dsr_get_item_name_hash($db, $dsSchema, $dim);
  $query = "SELECT $dimCol FROM app.cubes WHERE ID=$rptID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($itemIDStr) = $dbOutput->fetchrow_array;

  @itemIDs = split(',', $itemIDStr);
  foreach $itemID (@itemIDs)
  {
    print(" <OPTION SELECTED VALUE='$itemID'>$itemNameHash{$itemID}</OPTION>\n");
  }

  print <<END_HTML;
          </SELECT>

          <P>&nbsp;</P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="location.href='/app/rpt/display.cld?rpt=$rptID'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-submit">Next <I CLASS="bi bi-arrow-right"></I></BUTTON>
          </DIV>
          </FORM>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
