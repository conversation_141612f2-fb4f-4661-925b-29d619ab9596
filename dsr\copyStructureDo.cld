#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::DSRstructures;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  #output Content-type header
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  #output HTML
  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Copy $structType</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Copy $structType</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');
  $dim = $q->param('dim');
  $segID = $q->param('seg');
  $listID = $q->param('list');
  $aggID = $q->param('agg');
  $segHierID = $q->param('seghier');
  $name = $q->param('name');
  $link = $q->param('link');

  #sanitize parameters
  $dsID = utils_sanitize_integer($dsID);
  if (!defined($dsID))
  {
    exit_early_error($session, "Invalid data source");
  }
  $dim = utils_sanitize_dim($dim);
  if (!defined($dim))
  {
    exit_early_error($session, "Invalid dimension");
  }
  $name = utils_sanitize_string($name);

  #connect to the database
  $db = KAPutil_connect_to_database();

  #make sure we have write privs for this data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to copy structures in this data source.");
  }

  $dsSchema = "datasource_" . $dsID;

  $dsName = ds_id_to_name($db, $dsID);

  $dimName = KAPutil_get_dim_name_singular($dim, 1);
  $structDB = KAPutil_get_dim_stub_name($dim);

  #make a copy of the specified segmentation with the specified new name
  if ($segID > 0)
  {

    $activity = "$first $last created segmentation copy $name in $dsName";
    utils_audit($db, $userID, "Created segmentation copy $name", $dsID, 0, 0);

    $structType = "segmentation";
    $dbName = $structDB . "segmentation";

    #if we're linking to the source segmentation
    if ($link eq "on")
    {
      $link = $segID;
    }
    else
    {
      $link = "NULL";
    }

    #create the new segmentation table entry, and grab its unique ID
    $q_name = $db->quote($name);
    $query = "INSERT INTO $dsSchema.$dbName (name, parentID) VALUES ($q_name, $link)";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);
    $newSegID = $db->{q{mysql_insertid}};

    #grab all of the segments from the original segmentation, and re-create them
    #for the new segmentation (hash the old->new IDs so we can use them in the
    #next step)
    undef(%segmentHash);
    $dbName = $structDB . "segment";
    $query = "SELECT ID, name FROM $dsSchema.$dbName WHERE segmentationID=$segID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);

    while (($segmentID, $segmentName) = $dbOutput->fetchrow_array)
    {
      $q_segmentName = $db->quote($segmentName);
      $query = "INSERT INTO $dsSchema.$dbName (segmentationID, name) \
          VALUES ($newSegID, $q_segmentName)";
      $status = $db->do($query);
      KAPutil_handle_db_err($db, $status, $query);

      $newSegmentID = $db->{q{mysql_insertid}};
      $segmentHash{$segmentID} = $newSegmentID;
    }

    #run through all of the segments in the original segmentation, re-creating
    #their item memberships in the new segments
    $dbName = $structDB . "segment_item";
    $query = "SELECT segmentID, itemID FROM $dsSchema.$dbName \
        WHERE segmentationID=$segID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);

    while (($segmentID, $itemID) = $dbOutput->fetchrow_array)
    {

      #NB: for SQL performance reasons, we're batching up 100 INSERTs at a time
      push(@segItemValuesArray, "($newSegID, $segmentHash{$segmentID}, $itemID), ");

      #if we have 100 records ready for bulk insertion, build & run the SQL
      if (scalar(@segItemValuesArray) > 99)
      {
        $query = "INSERT INTO $dsSchema.$dbName \
            (segmentationID, segmentID, itemID) VALUES ";
        foreach $valSet (@segItemValuesArray)
        {
          $query .= "$valSet";
        }
        chop($query);  chop($query);
        $status = $db->do($query);
        KAPutil_handle_db_err($db, $status, $query);

        undef(@segItemValuesArray);
      }
    }

    #INSERT any remaining item segment assignments left on the array
    if (scalar(@segItemValuesArray) > 0)
    {
      $query = "INSERT INTO $dsSchema.$dbName \
          (segmentationID, segmentID, itemID) VALUES ";
      foreach $valSet (@segItemValuesArray)
      {
        $query .= "$valSet";
      }
      chop($query);  chop($query);
      $status = $db->do($query);
      KAPutil_handle_db_err($db, $status, $query);

      undef(@segItemValuesArray);
    }

    #copy any segmentation rules that exist for the original segmentation
    $dbName = $structDB . "seg_rules";
    $query = "SELECT step, segmentID, rule FROM $dsSchema.$dbName \
        WHERE segmentationID=$segID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);

    while (($step, $segmentID, $rule) = $dbOutput->fetchrow_array)
    {
      $q_rule = $db->quote($rule);
      $query = "INSERT INTO $dsSchema.$dbName (step, segmentationID, segmentID, rule) \
          VALUES ($step, $newSegID, $segmentHash{$segmentID}, $q_rule)";
      $status = $db->do($query);
      KAPutil_handle_db_err($db, $status, $query);
    }
  }

  #copy a list
  elsif ($listID > 0)
  {
    $activity = "$first $last created list copy $name in $dsName";
    utils_audit($db, $userID, "Created list copy $name", $dsID, 0, 0);

    $structType = "list";
    $dbName = $structDB . "list";

    #get the source list information
    $query = "SELECT script, members FROM $dsSchema.$dbName WHERE ID=$listID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute();
    KAPutil_handle_db_err($db, $status, $query);
    ($script, $members) = $dbOutput->fetchrow_array;

    #insert the new list copy
    $q_name = $db->quote($name);
    $query = "INSERT INTO $dsSchema.$dbName (name, script, members) \
        VALUES ($q_name, '$script', '$members')";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);
  }

  #copy an aggregate
  elsif ($aggID > 0)
  {
    $activity = "$first $last created aggregate copy $name in $dsName";
    utils_audit($db, $userID, "Created aggregate copy $name", $dsID, 0, 0);

    $structType = "aggregate";
    $dbName = $structDB . "aggregate";

    #if we're copying a time aggregate
    if ($dim eq "t")
    {
      #get the source aggregate information
      $query = "SELECT addScript, addMembers, appendEndDate \
          FROM $dsSchema.$dbName WHERE ID=$aggID";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute();
      KAPutil_handle_db_err($db, $status, $query);
      ($script, $members, $appendEndDate) = $dbOutput->fetchrow_array;

      #insert the new aggregate copy
      $q_name = $db->quote($name);
      $query = "INSERT INTO $dsSchema.$dbName \
          (name, addScript, addMembers, appendEndDate) \
          VALUES ($q_name, '$script', '$members', $appendEndDate)";
      $status = $db->do($query);
      KAPutil_handle_db_err($db, $status, $query);
    }
    else
    {
      #get the source aggregate information
      $query = "SELECT addScript, addMembers FROM $dsSchema.$dbName WHERE ID=$aggID";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute();
      KAPutil_handle_db_err($db, $status, $query);
      ($script, $members) = $dbOutput->fetchrow_array;

      #insert the new aggregate copy
      $q_name = $db->quote($name);
      $query = "INSERT INTO $dsSchema.$dbName (name, addScript, addMembers) \
          VALUES ($q_name, '$script', '$members')";
      $status = $db->do($query);
      KAPutil_handle_db_err($db, $status, $query);
    }
  }

  #copy a segment hierarchy
  elsif ($segHierID > 0)
  {
    $activity = "$first $last created segmentation hierarchy copy $name in $dsName";
    utils_audit($db, $userID, "Created segmentation hierarchy copy $name", $dsID, 0, 0);

    $structType = "segmentation hierarchy";
    $dbName = $structDB . "seghierarchy";

    #get the source seghier information
    $query = "SELECT segmentations FROM $dsSchema.$dbName WHERE ID=$segHierID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute();
    KAPutil_handle_db_err($db, $status, $query);
    ($segmentations) = $dbOutput->fetchrow_array;

    #insert the new segmentation hierarchy copy
    $q_name = $db->quote($name);
    $query = "INSERT INTO $dsSchema.$dbName (name, segmentations) \
        VALUES ($q_name, '$segmentations')";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);
  }

  print_html_header();

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-10 col-lg-7 col-xl-6"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Copy $structType</DIV>
        <DIV CLASS="card-body">

          The $name $structType has been successfully created.

          <P>&nbsp;<P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" onClick="location.href='display.cld?ds=$dsID&dim=$dim'"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
          </DIV>

        </DIV>
      </DIV>
    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

  utils_slack($activity);

#EOF
