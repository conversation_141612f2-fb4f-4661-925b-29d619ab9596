#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepRecipes;
use Lib::PrepUtils;
use Lib::WebUtils;


#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $flowID = $q->param('f');
  $jobID = $q->param('j');
  $colID = $q->param('col');
  $action = $q->param('a');
  $searchType = $q->param('t');
  $searchStr = $q->param('str');
  $invalid = $q->param('invalid');

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #make sure we have read privs for this data flow
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to search this data flow.");
  }

  $flowName = prep_flow_id_to_name($prepDB, $flowID);

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #build name of column containing the text
  $column = "column_" . $colID;

  #get the currently defined column type
  $query = "SELECT name, type FROM $masterColTable WHERE ID=$colID";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($colName, $type) = $dbOutput->fetchrow_array;


  ########################################################################
  #
  # This code block is called on submit to save the search filter
  #

  if ($action eq "apply")
  {
    $searchFilter = "$searchType|$column|$invalid|$searchStr";
    $q_searchFilter = $prepDB->quote($searchFilter);
    $query = "UPDATE prep.jobs SET searchFilter=$q_searchFilter WHERE ID=$jobID";
    $prepDB->do($query);

    prep_audit($prepDB, $userID, "Set search filter to $searchStr in $colName", $flowID);
    utils_slack("PREP: $first $last set search filter to $searchStr in $colName in $flowName");

    exit;
  }

  if ($action eq "clear")
  {
    $query = "UPDATE prep.jobs SET searchFilter=NULL WHERE ID=$jobID";
    $prepDB->do($query);

    prep_audit($prepDB, $userID, "Cleared search filter from $colName", $flowID);
    utils_slack("PREP: $first $last cleared search filter from $colName in $flowName");

    exit;
  }


  #########################################################################

  #get the current search term (if one exists)
  $query = "SELECT searchFilter FROM prep.jobs WHERE ID=$jobID";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($searchFilter) = $dbOutput->fetchrow_array;

  #parse the search filter; set defaults if there is no existing filter
  if ($searchFilter =~ m/^(.*?)\|(.*?)\|(.*?)\|(.*?)$/)
  {
    $searchType = $1;
    $searchColumn = $2;
    $invalid = $3;
    $searchStr = $4;
  }

  if ($invalid eq "true")
  {
    $invalid = "CHECKED";
  }
  else
  {
    $invalid = "";
  }

  #if there's no filter for this column, set defaults
  if ($searchColumn ne $column)
  {
    $searchType = "c";
    $searchStr = "";
  }

  print <<END_HTML;
<SCRIPT>
function submitForm(operation)
{
  let searchType = document.getElementById('search-type').value;
  let searchStr = document.getElementById('search-str').value;
  let invalid = document.getElementById('invalid').checked;
  let url = "xhrSearch.cld?f=$flowID&j=$jobID&col=$colID&a=apply&t=" + searchType + "&str=" + searchStr + "&invalid=" + invalid;

  \$("#btn-submit").prop('disabled', true);
  \$("#btn-submit").text("Please Wait...");
  \$("#btn-clear").prop('disabled', true);
  \$("#btn-clear").text("Please Wait...");

  if (operation == "clear")
  {
    url = "xhrSearch.cld?f=$flowID&j=$jobID&col=$colID&a=clear&t=" + searchType + "&str=&invalid=";
  }

  \$('#modal-search').modal('hide');

  \$.get(url, function(data, status)
  {
    location.href = "flowViewData.cld?f=$flowID&j=$jobID";
  });
}
</SCRIPT>

<DIV CLASS="modal-dialog modal-lg">
  <DIV CLASS="modal-content">

    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Search Filter</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">
      <P>
      <DIV CLASS="row">
        <DIV CLASS="col-auto mt-2">
          Display data where &nbsp;<STRONG>$colName</STRONG>
        </DIV>

<SCRIPT>
function handleMatchChange()
{
  var matchOp = document.getElementById('search-type').value;

  if (matchOp == "n")
    document.getElementById('search-str').style.visibility = 'hidden';
  else
    document.getElementById('search-str').style.visibility = 'visible';
}
</SCRIPT>

        <DIV CLASS="col-auto">
          <SELECT ID="search-type" CLASS="form-select" onChange="handleMatchChange();">
            <OPTION VALUE="b">begins with</OPTION>
            <OPTION VALUE="c" SELECTED>contains</OPTION>
            <OPTION VALUE="e">ends with</OPTION>
            <option VALUE="n">is blank</OPTION>
          </SELECT>
          <SCRIPT>
            \$("select#search-type").val("$searchType");
            handleMatchChange();
          </SCRIPT>
        </DIV>

        <DIV CLASS="col-auto">
          <INPUT TYPE="text" ID="search-str" CLASS="form-control" VALUE="$searchStr">
        </DIV>
      </DIV>

      <P>
      <DIV CLASS="form-check">
        <INPUT CLASS="form-check-input" name="invalid" ID="invalid" TYPE="checkbox" $invalid>
        <LABEL CLASS="form-check-label" FOR="invalid">Only display invalid data</LABEL>
      </DIV>
    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="button" ID="btn-clear" onClick="submitForm('clear')"><I CLASS="bi bi-eraser"></I> Clear Filters</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="button" ID="btn-submit" onClick="submitForm()"><I CLASS="bi bi-search"></I> Search</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML

  $query = "UPDATE prep.jobs SET lastAction=NOW() WHERE ID=$jobID";
  $prepDB->do($query);

#EOF
