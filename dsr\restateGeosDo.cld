#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Data Source Properties</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Restate Geographies</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');
  $csv = $q->param('csv');
  $oldGeo1 = $q->param('oldgeo1');
  $newGeo1 = $q->param('newgeo1');
  $oldGeo2 = $q->param('oldgeo2');
  $newGeo2 = $q->param('newgeo2');
  $oldGeo3 = $q->param('oldgeo3');
  $newGeo3 = $q->param('newgeo3');
  $oldGeo4 = $q->param('oldgeo4');
  $newGeo4 = $q->param('newgeo4');
  $oldGeo5 = $q->param('oldgeo5');
  $newGeo5 = $q->param('newgeo5');

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsSchema = "datasource_" . $dsID;
  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  #make sure we have write privs for the data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to modify this data source.");
  }

  utils_audit($db, $userID, "Restated geographies", $dsID, 0, 0);
  $activity = "$first $last restated geographies in data source $dsName";

  #undef the hash we're eventually going to use to restate the geos
  undef(%restateGeosHash);

  #push manually specified geos onto arrays for simpler code below
  @oldGeos = ($oldGeo1, $oldGeo2, $oldGeo3, $oldGeo4, $oldGeo5);
  @newGeos = ($newGeo1, $newGeo2, $newGeo3, $newGeo4, $newGeo5);

  #run through the manually specified geos, and save any that are valid
  $idx = 0;
  foreach $oldGeo (@oldGeos)
  {
    if ((length($oldGeo) > 0) && (length($newGeos[$idx]) > 0))
    {
      $restateGeosHash{$oldGeo} = $newGeos[$idx];
    }
    $idx++;
  }

  #cycle through the CSV data block looking for valid restatements
  @csvLines = split(/\n/, $csv);
  foreach $line (@csvLines)
  {
    if ($line =~ m/^(.*),(.*)$/)
    {
      $oldGeo = $1;
      $newGeo = $2;

      #trim off any trailing/leading whitespace
      $oldGeo =~ s/^\s+//;
      $oldGeo =~ s/\s+$//;
      $newGeo =~ s/^\s+//;
      $newGeo =~ s/\s+$//;

      #add to hash to be restated if they're valid
      if ((length($oldGeo) > 0) && (length($newGeo) > 0))
      {
        $restateGeosHash{$oldGeo} = $newGeo;
      }
    }
  }

  #do the actual restatement for each entry we have in our restatement hash
  foreach $oldGeo (keys %restateGeosHash)
  {
    $newGeo = $restateGeosHash{$oldGeo};
    $q_oldGeo = $db->quote($oldGeo);
    $q_newGeo = $db->quote($newGeo);

    #start by finding a matching old geography in the data source
    $query = "SELECT ID FROM $dsSchema.geographies WHERE name = $q_oldGeo";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($oldGeoID) = $dbOutput->fetchrow_array;

    #if we didn't find a matching old geography, dump it and move on
    if ($oldGeoID < 1)
    {
      next;
    }
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-11 col-lg-8 col-xl-7"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Restate Geographies</DIV>
        <DIV CLASS="card-body">

          The specified geographies have been restated:
          <P>
          <DIV CLASS="table-responsive">
            <TABLE CLASS="table">
END_HTML

  #do the actual restatement for each entry we have in our restatement hash
  foreach $oldGeo (keys %restateGeosHash)
  {
    $newGeo = $restateGeosHash{$oldGeo};
    $q_oldGeo = $db->quote($oldGeo);
    $q_newGeo = $db->quote($newGeo);

    #start by finding a matching old geography in the data source
    $query = "SELECT ID FROM $dsSchema.geographies WHERE name = $q_oldGeo";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($oldGeoID) = $dbOutput->fetchrow_array;

    #if we didn't find a matching old geography, dump it and move on
    if ($oldGeoID < 1)
    {
      print("<TR><TD>$oldGeo</TD><TD>No matching geography found, skipped</TD></TR>\n");
      next;
    }

    #see if we already have a matching new geography
    #NB: have to do this before renaming the old geography, otherwise we get
    #    its ID as a result. Don't be tempted to move this if refactoring code.
    $query = "SELECT ID FROM $dsSchema.geographies WHERE name = $q_newGeo";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($newGeoID) = $dbOutput->fetchrow_array;

    #rename the old geography
    $query = "UPDATE $dsSchema.geographies SET name=$q_newGeo WHERE ID = $oldGeoID";
    $db->do($query);
    print("<TR><TD>$oldGeo</TD><TD>Restated as $newGeo</TD></TR>\n");

    #merge in the data if the new geography has already been created
    if ($newGeoID > 0)
    {

      #NB: we're going to ignore duplicate key issues, and then delete anything
      #    that still has the new ID so only the old (restated) ID remains
      $query = "UPDATE IGNORE $dsSchema.facts SET geographyID=$oldGeoID \
          WHERE geographyID=$newGeoID";
      $db->do($query);

      $query = "DELETE FROM $dsSchema.facts WHERE geographyID=$newGeoID";
      $db->do($query);

      #remove the (now duplicate) geography
      $query = "DELETE FROM $dsSchema.geographies WHERE ID=$newGeoID";
      $db->do($query);

      print("<TR><TD></TD><TD>Found and merged in existing geography $newGeo</TD></TR>\n");
    }
  }

  #let everything know it may need to update
  $query = "UPDATE dataSources SET lastModified=NOW() WHERE ID=$dsID";
  $db->do($query);

  print <<END_HTML;
            </TABLE>
          </DIV>

          <P>&nbsp;<P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='/app/dsr/display.cld?ds=$dsID'"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();
  utils_slack($activity);

#EOF
