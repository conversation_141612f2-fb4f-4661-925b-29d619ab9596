#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------


  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $service = $q->param('svc');
  $itemID = $q->param('item');
  $dsID = $q->param('ds');
  $dim = $q->param('dim');
  $segID = $q->param('seg');

  $dsID = utils_sanitize_integer($dsID);
  $dim = utils_sanitize_dim($dim);

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsSchema = "datasource_" . $dsID;

  #------------------------------------------------------------------------

  #if we're being asked to remove an item from the user's feed
  if ($service eq "feed_del_item")
  {
    $query = "DELETE FROM app.feed WHERE ID=$itemID AND userID=$userID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    print("Content-type: application/json\n\n");
    print("OK\n");
  }


  #------------------------------------------------------------------------

  #if we're being asked to look for new items in the user's feed
  if ($service eq "feed_new_items")
  {
    $query = "SELECT COUNT(ID) FROM app.feed WHERE ID > $itemID AND userID=$userID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($newItems) = $dbOutput->fetchrow_array;
    if ($newItems < 1)
    {
      $newItems = 0;
    }

    print("Content-type: application/text\n\n");
    print("$newItems\n");
  }


  #------------------------------------------------------------------------

  #if we're being asked to return all of the segments in the specified segmentation
  if ($service eq "segments")
  {
    print("Content-type: application/json\n\n");
    $output = "[\n";

    %segmentHash = DSRseg_get_segments_hash($db, $dsSchema, $dim, $segID);
    foreach $segmentID (sort {$segmentHash{$a} cmp $segmentHash{$b}} keys %segmentHash)
    {
      $output .= " {\n";
      $output .= "  \"id\": $segmentID,\n";
      $output .= "  \"name\": \"$segmentHash{$segmentID}\"\n";
      $output .= " },\n";
    }
    chop($output); chop($output);

    $output .= "\n]\n";

    print("$output");
    exit;
  }


  #------------------------------------------------------------------------

  #if we're being asked for a current cloud performance setting
  if ($service =~ m/^perf_/)
  {
    if ($service eq "perf_jobs")
    {
      $field = "jobs";
    }
    elsif ($service eq "perf_cpu")
    {
      $field = "cpu";
    }
    elsif ($service eq "perf_memory")
    {
      $field = "memory";
    }
    elsif ($service eq "perf_throughput")
    {
      $field = "storageThroughput";
    }
    elsif ($service eq "perf_storage")
    {
      $field = "storage";
    }
    else
    {
      print("Status: 403 Not authorized\n");
      exit;   #bad request, we're out
    }

    $query = "SELECT $field FROM app.performance WHERE instance='analytics'";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($value) = $dbOutput->fetchrow_array;

    print("Content-type: application/text\n\n");
    print("&value=$value");
    exit;
  }


  #------------------------------------------------------------------------


#EOF
