#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::AInsights::AInsights;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: AInsights Model Properties</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">AInsights</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="insights.cld?pm=$priceModelID">$modelName</A></LI>
    <LI CLASS="breadcrumb-item active">Properties</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $priceModelID = $q->param('pm');
  $name = $q->param('name');
  $dsDescription = $q->param('dsDescription');
  $dsOwner = $q->param('dsOwner');
  $oldOwnerID = $q->param('oldOwnerID');
  $pushToDS = $q->param('push');

  if (length($pushToDS) < 1)
  {
    $pushToDS = 0;
  }
  else
  {
    $pushToDS = 1;
  }

  #connect to the database
  $db = KAPutil_connect_to_database();

  #get the name of the pricing model
  $modelName = AInsights_ID_to_name($db, $priceModelID);

  print_html_header();

  #make sure we have privs to at least view this model
  $privs = AInsights_rights($db, $userID, $priceModelID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this model.");
  }

  #update name and description
  $q_name = $db->quote($name);
  $q_desc = $db->quote($dsDescription);
  $query = "UPDATE analytics.pricing \
      SET name=$q_name, description=$q_desc, push=$pushToDS WHERE ID=$priceModelID";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);

  #if we're changing the model's owner
  if (($dsOwner > 0) && ($dsOwner != $oldOwnerID))
  {

    #update the data flow owner to the new selection
    $query = "UPDATE analytics.pricing SET userID=$dsOwner WHERE ID=$priceModelID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    #grant the original flow owner read/write privs on the data source
    $query = "SELECT RWusers FROM analytics.pricing WHERE ID=$priceModelID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($rwusers) = $dbOutput->fetchrow_array;

    if (length($rwusers) > 0)
    {
      $rwusers .= ",";
    }
    $rwusers .= $userID;

    $query = "UPDATE analytics.pricing SET RWusers='$rwusers' WHERE ID=$priceModelID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    AInsights_audit($db, $userID, $priceModelID, "Changed pricing model ownership");
    utils_slack("PRICING: $first $last changed ownership of model $modelName");
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-10 col-lg-7 col-xl-6"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">AInsights Model Properties</DIV>
        <DIV CLASS="card-body">

          Your changes to the $name model have been saved.

          <P>&nbsp;<P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='main.cld'"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

  AInsights_audit($db, $userID, $priceModelID, "Changed pricing model properties");
  utils_slack("PRICING: $first $last changed properties of model $modelName");

#EOF
