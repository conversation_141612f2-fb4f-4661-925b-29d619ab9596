#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepRecipes;
use Lib::PrepUtils;
use Lib::WebUtils;


#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $flowID = $q->param('f');
  $jobID = $q->param('j');
  $colID = $q->param('col');

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #make sure we have write privs for this data flow
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this data flow.");
  }

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #build name of column containing the text
  $column = "column_" . $colID;

  #get the currently defined column type
  $query = "SELECT name, type FROM $masterColTable WHERE ID=$colID";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($colName, $type) = $dbOutput->fetchrow_array;

  print <<END_HTML;
<SCRIPT>
function checkForm(form)
{
  \$("#btn-submit").prop('disabled', true);
  \$("#btn-submit").text("Please Wait...");
  return(true);
}
</SCRIPT>

<FORM METHOD="post" ACTION="transformProgress.cld" onsubmit="return checkForm(this);">
<INPUT TYPE="hidden" NAME="f" VALUE="$flowID">
<INPUT TYPE="hidden" NAME="j" VALUE="$jobID">
<INPUT TYPE="hidden" NAME="col" VALUE="$colID">
<INPUT TYPE="hidden" NAME="a" VALUE="TRANS-COL-TYPE">

<DIV CLASS="modal-dialog">
  <DIV CLASS="modal-content">

    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Column Type</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">
      <LABEL FOR="colType">Change the type of data contained in the selected column to:</LABEL>
      <SELECT CLASS="form-select" NAME="colType" ID="colType">
        <OPTION VALUE="product">Product Dimension</OPTION>
        <OPTION VALUE="geography">Geography Dimension</OPTION>
        <OPTION VALUE="time">Time Dimension</OPTION>
        <OPTION VALUE="upc">UPC/SKU/EAN</OPTION>
        <OPTION VALUE="measure">Measure</OPTION>
        <OPTION VALUE="pseg">Product Segmentation</OPTION>
        <OPTION VALUE="gseg">Geography Segmentation</OPTION>
        <OPTION VALUE="tseg">Time Segmentation</OPTION>
        <OPTION VALUE="pattr">Product Attribute</OPTION>
        <OPTION VALUE="gattr">Geography Attribute</OPTION>
        <OPTION VALUE="tattr">Time Attribute</OPTION>
        <OPTION VALUE="palias">Product Alias</OPTION>
        <OPTION VALUE="galias">Geography Alias</OPTION>
        <OPTION VALUE="talias">Time Alias</OPTION>
      </SELECT>
      <SCRIPT>
        \$("select#colType").val("$type");
      </SCRIPT>
    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-submit"><I CLASS="bi bi-pencil-square"></I> Apply</BUTTON>
    </DIV>

  </DIV>
</DIV>

</FORM>
END_HTML

#EOF
