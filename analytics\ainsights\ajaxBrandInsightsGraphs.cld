#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::AInsights::AInsights;
use Lib::AInsights::Utils;



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get the CGI input variables
  $priceModelID = $q->param('pm');
  $dsID = $q->param('ds');
  $geoID = $q->param('g');
  $schema = $q->param('s');

  $dsSchema = "datasource_" . $dsID;

  AInsights_Utils_initialize_constants($priceModelID);

  #connect to the database
  $db = KAPutil_connect_to_database();

  print("Content-type: text/plain\n\n");

  #make sure we have privs to at least view this model
  $privs = AInsights_rights($db, $userID, $priceModelID, $acctType);
  if ($privs eq "N")
  {
    print("ERROR: User doesn't have view privileges on this model.");
    exit;
  }

  $ownBrandID = AInsights_Utils_get_own_brand_id($db, $priceModelID);

  if ($schema == 1)
  {
    print <<JSON_LABEL;
[{
    "name": "Date",
    "type": "date",
    "format": "%Y-%m-%d"
},
{
    "name": "Dollars",
    "type": "number"
},
{
    "name": "Units",
    "type": "number"
},
{
    "name": "Avg Price",
    "type": "number"
},
{
    "name": "Promotion",
    "type": "number"
},
{
    "name": "Distribution",
    "type": "number"
}]
JSON_LABEL
  }

  #else we're writing out the multi-variate data to be graphed
  else
  {

    #figure out the chrono-order of the time periods
    $query = "SELECT ID, endDate FROM $dsSchema.timeperiods \
        WHERE duration=1 AND type=30 ORDER BY endDate ASC";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($timeID, $endDate) = $dbOutput->fetchrow_array)
    {
      $orderedTimeIDStr .= "$timeID,";

      $endDate =~ m/^(.*?) /;
      $endDateHash{$timeID} = $1;
    }
    chop($orderedTimeIDStr);

    #pull the data from the stored model info
    $query = "SELECT timeID, dollars, units, avgPrice, avgDist, promoDispUnits + promoFeatDispUnits + promoFeatUnits + promoPriceDecrUnits \
        FROM $dsSchema.$AInsightsBrandCube \
        WHERE brandID=$ownBrandID AND geographyID=$geoID \
        ORDER BY FIELD(timeID, '$orderedTimeIDStr')";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);

    #run through cube data and output to user
    while (($timeID, $dollars, $units, $price, $dist, $promo) = $dbOutput->fetchrow_array)
    {
      $dollars = sprintf("%.2f", $dollars);
      $units = sprintf("%.1f", $units);
      $price = sprintf("%.2f", $price);
      $dist = sprintf("%.1f", $dist);
      $promo = sprintf("%.1f", $promo);

      $json .= "[\"$endDateHash{$timeID}\", $dollars, $units, $price, $promo, $dist],\n";
    }
    chop($json);  chop($json);
    print("[$json]\n");
  }

#EOF
