
package Lib::Cubes;

use lib "/opt/apache/app/";

use Exporter;
use Lib::DataSources;
use Lib::DSRMeasures;
use Lib::WebUtils;

our @ISA = ('Exporter');

our @EXPORT = qw(&cube_id_to_name
    &cube_name_to_id
    &cube_get_name_hash
    &cube_get_ds_id
    &cube_list
    &cube_rights
    &cube_refresh_ok);



#-------------------------------------------------------------------------
#
# Handle a database error of some kind during a cube API call
#

sub cubes_db_err
{
  my ($date);

  my ($db, $status, $text) = @_;

  if (!defined($status))
  {
    $date = localtime();
    print STDERR "$date: $text\n";
    if ($db->errstr =~ m/^MySQL server has gone away/)
    {
      die("Lost connection to database, terminating");
    }
  }
}



#-------------------------------------------------------------------------
#
# Return the text name of the cube with the supplied ID
#

sub cube_id_to_name
{
  my ($query, $dbOutput, $name, $status);

  my ($db, $cubeID) = @_;

  #validity check parameters
  if (!($cubeID =~ m/^\d+$/))
  {
    return("");
  }

  $query = "SELECT name FROM app.cubes WHERE ID=$cubeID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  cubes_db_err($db, $status, $query);
  ($name) = $dbOutput->fetchrow_array;

  return($name);
}



#-------------------------------------------------------------------------
#
# Return the ID of the cube with the supplied name in the supplied data source
#

sub cube_name_to_id
{
  my ($query, $dbOutput, $id, $status);

  my ($db, $cubeName, $dsID) = @_;

  $query = "SELECT ID FROM app.cubes WHERE dsID = $dsID AND name = '$cubeName'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  cubes_db_err($db, $status, $query);
  ($id) = $dbOutput->fetchrow_array;

  return($id);
}



#-------------------------------------------------------------------------
#
# Build and return a hash of all data cube names, indexed by ID
#

sub cube_get_name_hash
{
  my ($query, $dbOutput, $id, $name, $status);
  my (%cubeNames);

  my ($db) = @_;

  undef(%cubeNames);

  #add user names to hash
  $query = "SELECT ID, name FROM app.cubes";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  cubes_db_err($db, $status, $query);

  while (($id, $name) = $dbOutput->fetchrow_array)
  {
    $cubeNames{$id} = $name;
  }

  return(%cubeNames);
}



#-------------------------------------------------------------------------
#
# Get the data source ID for the specified cube
#

sub cube_get_ds_id
{
  my ($query, $dbOutput, $dsID, $status);

  my ($db, $cubeID) = @_;

  #get cube's backing data source ID
  $query = "SELECT dsID FROM app.cubes WHERE ID=$cubeID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  cubes_db_err($db, $status, $query);
  ($dsID) = $dbOutput->fetchrow_array;

  return($dsID);
}



#-------------------------------------------------------------------------
#
# Return a hash of data cube IDs/names that the specified user has some level
# of privilege for (used to display lists of cubes in UI)
#

sub cube_list
{
  my ($query, $dbOutput, $privs, $ownerID, $Rusers, $RWusers, $ruser, $cubeID);
  my ($DSrights, $cubeDS, $ownsDS, $cubeName, $status);
  my (%userCubes, @rusers, @rwusers);

  my ($db, $userID, $acctType, $dsID) = @_;

  #if we were passed a specific data source, see if the user has rights on it
  if ($dsID > 0)
  {
    $DSrights = ds_rights($db, $userID, $dsID, $acctType);
  }

  #get the list of cubes stored on the system, with priv info
  $query = "SELECT ID, name, dsID, userID, Rusers, RWusers FROM app.cubes \
      WHERE dsID=$dsID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  cubes_db_err($db, $status, $query);

  #run through the list of data cubes
  while (($cubeID, $cubeName, $cubeDS, $ownerID, $Rusers, $RWusers) = $dbOutput->fetchrow_array)
  {

    #split the user lists into arrays, and combine
    @rusers = split(',', $Rusers);
    @rwusers = split(',', $RWusers);
    push(@rusers, @rwusers);

    #push the data cube owner's ID onto the combined array
    push(@rusers, $ownerID);

    #see if the user has privs, and add the data cube ID to the returned
    #array if so
    foreach $ruser (@rusers)
    {
      if (($ruser == $userID) || ($acctType > 4))
      {
        $userCubes{$cubeID} = $cubeName;
      }

      #if the user has read/write privs on the underlying data source, they
      #should also be able to view the reports it backs
      if ($DSrights ne "N")
      {
        $userCubes{$cubeID} = $cubeName;
      }
    }
  }

  return(%userCubes);
}



#-------------------------------------------------------------------------
#
# Determine what rights the specified user has to the specified data cube.
# Returns a single character: "N" for no rights, "R" for read-only, "W" for
# write.
#

sub cube_rights
{
  my ($query, $dbOutput, $privs, $ownerID, $Rusers, $RWusers, $ruser, $rwuser);
  my ($DSownerID, $DSRusers, $DSRWusers, $cubeDS, $status);
  my (@rusers, @rwusers);

  my ($db, $userID, $cubeID, $acctType) = @_;

  #validity check parameters
  if (!($cubeID =~ m/^\d+$/))
  {
    return("N");
  }

  #admins can access everything
  if ($acctType > 4)
  {
    return("W");
  }

  #get the list of read and read/write users for the specified data cube
  $query = "SELECT userID, Rusers, RWusers, dsID FROM app.cubes \
      WHERE ID=$cubeID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  cubes_db_err($db, $status, $query);
  ($ownerID, $Rusers, $RWusers, $cubeDS) = $dbOutput->fetchrow_array;

  #if the user owns the data source, they have full rights
  $query = "SELECT userID, Rusers, RWusers FROM app.dataSources \
      WHERE ID=$cubeDS";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  cubes_db_err($db, $status, $query);
  ($DSownerID, $DSRusers, $DSRWusers) = $dbOutput->fetchrow_array;
  if ($DSownerID == $userID)
  {
    return("W");
  }

  #if the user owns the data cube, they have full rights
  if ($userID == $ownerID)
  {
    return("W");
  }

  #split the user lists into arrays
  $Rusers = "$Rusers,$DSRusers";
  $RWusers = "$RWusers,$DSRWusers";
  @rusers = split(',', $Rusers);
  @rwusers = split(',', $RWusers);

  #see if the user has read/write privs
  foreach $rwuser (@rwusers)
  {
    if ($rwuser == $userID)
    {
      return("W");
    }
  }

  #see if the user has read privs
  foreach $ruser (@rusers)
  {
    if ($ruser == $userID)
    {
      return("R");
    }
  }

  #if we made it this far, the user has no privs on the data source
  return("N");
}



#-------------------------------------------------------------------------
#
# Uses data from the currently running jobs table to determine if a refresh
# attempt for the specified cube would go into a wait state or not. Returns
# 1 if the process would run without waiting, 0 otherwise.
#

sub cube_refresh_ok
{
  my ($query, $dbOutput, $cubeID, $status);

  my ($db, $dsID) = @_;

  $query = "SELECT cubeID FROM app.jobs \
      WHERE dsID=$dsID AND operation='CUBE-UPDATE'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  cubes_db_err($db, $status, $query);
  ($cubeID) = $dbOutput->fetchrow_array;

  #if at least one cube in the data source is already being refreshed
  if ($cubeID > 0)
  {
    return(0);
  }

  return(1);
}


#-------------------------------------------------------------------------


1;
