#!/usr/bin/perl

use lib "/opt/apache/app/";

use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



  $off = 0;
  $on = 1;

  #if we're only optimizing the specified data source
  $dsID = $ARGV[0];

  #connect to the database
  $db = KAPutil_connect_to_database();

  #get all ODBC exported data sources
  $query = "SELECT ID, ODBCuser, ODBCpassword FROM dataSources WHERE ODBCexport = 1";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;

  while (($dsID, $user, $password) = $dbOutput->fetchrow_array)
  {
    $dsSchema = "datasource_" . $dsID;
    $q_ODBCuser = $db->quote($user);
    $q_ODBCpassword = $db->quote($password);

    if ($off == 1)
    {
      print("Revoking privs for $user on $dsID\n");
      $query = "REVOKE ALL ON $dsSchema.export FROM $q_ODBCuser";
      $db->do($query);
    }

    if ($on == 1)
    {
      print("Granting privs for $user identified by $password on $dsID\n");
      $query = "GRANT SELECT ON $dsSchema.export TO $q_ODBCuser IDENTIFIED BY $q_ODBCpassword";
      $db->do($query);
    }
  }

#EOF
