#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;
use File::Copy;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;



  #connect to the database
  $db = KAPutil_connect_to_database();

  $query = "SELECT ID, name FROM app.dataSources";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  while (($dsID, $dsName) = $dbOutput->fetchrow_array)
  {
    $dsHash{$dsID} = $dsName;
  }

  foreach $dsID (keys %dsHash)
  {
    print("DS: $dsHash{$dsID} - $dsID\n");

    $dsSchema = "datasource_$dsID";

    #get list of all segmentations in data source
    %segHash = DSRsegmentation_get_segmentations_hash($db, $dsSchema, "p");

    #get a hash of segments used in rules that we shouldn't touch
    undef(%rulesSegmentHash);
    $query = "SELECT segmentID, rule, filter1, filter2 FROM $dsSchema.product_seg_rules";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    while (($segmentID, $rule, $filter1, $filter2) = $dbOutput->fetchrow_array)
    {
      $rulesSegmentHash{$segmentID} = 1;

      if ($rule =~ m/^SEG .*? \d+ (.*)$/)
      {
        @tmp = split(',', $1);
        foreach $id (@tmp)
        {
          $rulesSegmentHash{$segmentID} = 1;
        }
      }

      if ($filter1 =~ m/^\d+ (.*)$/)
      {
        @tmp = split(',', $1);
        foreach $id (@tmp)
        {
          $rulesSegmentHash{$segmentID} = 1;
        }
      }
      if ($filter2 =~ m/^\d+ (.*)$/)
      {
        @tmp = split(',', $1);
        foreach $id (@tmp)
        {
          $rulesSegmentHash{$segmentID} = 1;
        }
      }

    }

    foreach $segID (keys %segHash)
    {

      #get any duplicate segments
      $query = "SELECT name, COUNT(name) FROM $dsSchema.product_segment \
          WHERE segmentationID=$segID GROUP BY name";
      $dbOutput = $db->prepare($query);
      $dbOutput->execute;
      while (($segmentName, $count) = $dbOutput->fetchrow_array)
      {
        if ($count == 1)
        {
          next;
        }

        print("$count $segmentName\n");

        $q_name = $db->quote($segmentName);
        $query = "SELECT ID, name FROM $dsSchema.product_segment \
            WHERE segmentationID=$segID AND name=$q_name ORDER BY ID";
        $dbOutput1 = $db->prepare($query);
        $dbOutput1->execute;
        undef(@dupeSegs);
        while (($segmentID, $segmentName) = $dbOutput1->fetchrow_array)
        {
          push(@dupeSegs, $segmentID);
        }

        $parentSegment = shift(@dupeSegs);

        foreach $segmentID (@dupeSegs)
        {

          #skip segments used in rules
          if ($rulesSegmentHash{$segmentID} == 1)
          {
            print("Skipping segment $segmentID used in rules\n");
            next;
          }

          print("Merging $segmentID into $parentSegment\n");
          $query = "UPDATE $dsSchema.product_segment_item \
              SET segmentID=$parentSegment WHERE segmentID=$segmentID";
          $db->do($query);

          print("Deleting duplicate segment\n");
          $query = "DELETE FROM $dsSchema.product_segment WHERE ID=$segmentID";
          $db->do($query);
        }

        $query = "UPDATE app.dataSources SET lastModified=NOW() WHERE ID=$dsID";
        $db->do($query);
      }
    }

    print("--------------------------------------------------------------\n\n");
  }
