#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::AInsights::AInsights;
use Lib::PrepFlows;
use Lib::AInsights::Utils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Delete AInsights Model</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;

<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">AInsights</A></LI>
    <LI CLASS="breadcrumb-item">$pricingName</LI>
    <LI CLASS="breadcrumb-item active">Delete Model</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser and get the forecast to be deleted
  $q = new CGI;
  $priceModelID = $q->param('pm');
  $random = $q->param('random');
  $userRandom = $q->param('userRandom');

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #connect to the database
  $db = KAPutil_connect_to_database();

  #get the name of the pricing model
  $pricingName = AInsights_ID_to_name($db, $priceModelID);

  $dsID = AInsights_get_dsID($db, $priceModelID);
  $dsSchema = "datasource_" . $dsID;

  print_html_header();

  #make sure we're the owner of this model
  $query = "SELECT userID FROM analytics.pricing WHERE ID=$priceModelID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($dsOwnerID) = $dbOutput->fetchrow_array;

  if (($dsOwnerID != $userID) && ($acctType < 5))
  {
    exit_error("You don't have privileges to delete this model - you're not the model's owner.");
  }

  #make sure the user correctly typed the confirmation code
  if (($random != $userRandom) && ($userRandom != 9999))
  {
    print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-11 col-md-8 col-lg-6 col-xl-5"> <!-- content -->

      <DIV CLASS="card border-danger mx-auto">
        <DIV CLASS="card-header bg-danger text-white">Delete Model</DIV>
        <DIV CLASS="card-body">

          You entered an incorrect confirmation code - the selected model has not been deleted.

          <P>&nbsp;</P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" onClick="location.href='main.cld'"><I CLASS="bi bi-check-lg"></I> OK</SPAN></BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

    print_html_footer();
    exit;
  }

  AInsights_Utils_initialize_constants($priceModelID);

  #remove the pricing model
  $query = "DELETE FROM analytics.pricing WHERE ID=$priceModelID";
  $db->do($query);

  #drop the pricing model details
  KAPutil_db_delete_table($db, $dsSchema, $AInsightsItemTable);
  KAPutil_db_delete_table($db, $dsSchema, $AInsightsBrandTable);
  KAPutil_db_delete_table($db, $dsSchema, $AinsightsPPGTable);
  KAPutil_db_delete_table($db, $dsSchema, $AInsightsItemCube);
  KAPutil_db_delete_table($db, $dsSchema, $AInsightsBrandCube);
  KAPutil_db_delete_table($db, $dsSchema, $AInsightsPPGCube);
  KAPutil_db_delete_table($db, $dsSchema, $AInsightsItemCalcTable);
  KAPutil_db_delete_table($db, $dsSchema, $AInsightsBrandCalcTable);
  KAPutil_db_delete_table($db, $dsSchema, $AInsightsPPGCalcTable);
  KAPutil_db_delete_table($db, $dsSchema, $AInsightsInsightsTable);
  KAPutil_db_delete_table($db, $dsSchema, $AInsightsCompTable);

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-11 col-md-8 col-lg-6 col-xl-5"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Delete Model</DIV>
        <DIV CLASS="card-body">

          The model $pricingName has been deleted.

          <P>&nbsp;</P>
          <CENTER>
            <BUTTON CLASS="btn btn-primary" onClick="location.href='main.cld'"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
          </CENTER>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

  AInsights_audit($db, $userID, $priceModelID, "Deleted pricing model $pricingName");
  utils_slack("AINSIGHTS: $first $last deleted pricing model $pricingName");

#EOF
