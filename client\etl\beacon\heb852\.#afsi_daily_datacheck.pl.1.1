#!/usr/bin/perl

use lib "/opt/apache/app/";

use Text::CSV_XS;
use DateTime::Duration;
use DBI;

use Lib::KoalaConfig;



#---------------------------------------------------------------------------
#
# Output log info to console and log file
#


sub DBG
{
  my ($date);

  my ($str) = @_;

  $date = localtime();
  print STDERR "$date: $str\n";
  print("$str\n");
}


#---------------------------------------------------------------------------


  #get the mtime timestamp from the current "most recent" file
  @fileInfo = stat("/data/afsi/most_recent.csv");
  $currentTimeStamp = $originalTimeStamp = $fileInfo[9];

  #cycle through the CSV files in the data dir, looking for the newest one
  opendir(DIRHANDLE, "/data/afsi");
  while (defined($filename = readdir(DIRHANDLE)))
  {

    #make sure we're only lookng at CSV files that fit our expected name pattern
    if ($filename =~ m/ibm852_.*csv$/i)
    {

      #get the file's mtime
      @fileInfo = stat("/data/afsi/$filename");
      $newTimeStamp = $fileInfo[9];

      #if the file is the newest we've found, store its name and timestamp
      if ($newTimeStamp > $currentTimeStamp)
      {
        $currentTimeStamp = $newTimeStamp;
        $newFilename = $filename;
      }

    }
  }

  #if we found a newer file, copy it into place
  if ($currentTimeStamp > $originalTimeStamp)
  {
    `/usr/bin/cp -a /data/afsi/$newFilename /data/afsi/most_recent.csv`
  }



#EOF
