#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/plain\n\n");

  #print the table header row
  print <<END_HTML;
 <TR CLASS="table-info">
  <TH>Time</TH>
  <TH>User</TH>
  <TH>Action</TH>
  <TH>Data Source</TH>
  <TH>Report</TH>
 </TR>
END_HTML

  #connect to the database
  $db = KAPutil_connect_to_database();

  #get a hash of all the usernames on the system for display purposes
  %userNames = utils_get_user_hash($db);

  #get a hash of all the data source names on the system for display purposes
  %dsNames = ds_get_name_hash($db);

  #get a hash of all the data cube names on the system for display purposes
  %cubeNames = cube_get_name_hash($db);

  #get the most recent 50 entries from the audit table
  $query = "SELECT timestamp, userID, action, dsID, rptID \
      FROM audit.userActions ORDER BY timestamp DESC LIMIT 50";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  while (($timestamp, $auditUserID, $action, $dsID, $rptID) = $dbOutput->fetchrow_array)
  {
    print <<END_HTML;
 <TR>
  <TD NOWRAP>$timestamp</TD>
  <TD NOWRAP>$userNames{$auditUserID}</TD>
  <TD>$action</TD>
  <TD>$dsNames{$dsID}</TD>
  <TD>$cubeNames{$rptID}</TD>
 </TR>
END_HTML

    print("$DSstatus\n");
  }


#EOF
