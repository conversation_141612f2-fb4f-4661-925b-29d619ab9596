#!/usr/bin/perl

use lib "/opt/apache/app/";

use Lib::KoalaConfig;


  #if we're only loading the specified data source
  $dsID = $ARGV[0];

  #load all data source backups into local DB
  opendir(DIRHANDLE, "/opt/koala_backup");
  chdir("/opt/koala_backup");
  while (defined($filename = readdir(DIRHANDLE)))
  {
    if ($filename =~ m/^datasource_(.*)\.zip$/)
    {
      $dsID = $1;
      print("Loading $filename\n");

      `unzip $filename`;

      #import the source data into the new data source
      `/usr/bin/mysql -h$Lib::KoalaConfig::dbServerName -u app -p$Lib::KoalaConfig::password < /opt/koala_backup/datasource_$dsID.sql`;

      #delete the data source dump file from disk
      unlink("/opt/koala_backup/datasource_$dsID.sql");
    }
  }

#EOF
