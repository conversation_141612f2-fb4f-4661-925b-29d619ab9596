#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;
use Excel::Writer::XLSX;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($kapDB);

  print <<END_HTML;
<DOCCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Exporting to Excel</TITLE>
<LINK REL="SHORTCUT ICON" HREF="$Lib::KoalaConfig::kapHostURL/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>

let statusTimer = setInterval(function(){displayStatus()}, 1000);
let statCount = 0;

function displayStatus()
{
  const url = 'ajaxAPI.cld?svc=job_op_status&j=$jobID';

  \$.get(url, function(data, status)
  {
    let statusText = data;
    let statElements = statusText.split('|');

    if (statusText.search('DONE') == 0)
    {
      \$('#progress-bar').css('width', '100%');
      clearInterval(statusTimer);
      document.getElementById('download_ready').style.visibility = 'visible';
      document.getElementById('progress-div').style.visibility = 'hidden';
      document.getElementById('div-op-title').innerHTML = "";
      document.getElementById('div-op-details').innerHTML = "";
      document.getElementById('div-op-extra').innerHTML = "";
      return;
    }

    let opTitle = statElements[0];
    let opPct = statElements[1];
    let opDetails = statElements[2];
    let opTimeEstimate = statElements[3];
    let opExtra = statElements[4];
    document.getElementById('div-op-title').innerHTML = opTitle;
    if (opPct.length > 0)
    {
      document.getElementById('progress-bar').innerHTML = opPct + '%';
      \$('#progress-bar').css('width', opPct+'%');
    }
    document.getElementById('div-op-details').innerHTML = opDetails;
    document.getElementById('div-op-extra').innerHTML = opExtra;

    if (statCount > 5)
    {
      clearInterval(statusTimer);
      statusTimer = setInterval(function(){displayStatus()}, 5000);
    }
    statCount++;
  });
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($kapDB, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="$Lib::KoalaConfig::kapHostURL/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?f=$flowID">Data Prep</A></LI>
    <LI CLASS="breadcrumb-item">$flowName</LI>
    <LI CLASS="breadcrumb-item active">Export to Excel</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------
#
# Output the HTML for the web page that displays the status dialog
#

sub print_status_html
{
  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-11 col-md-8 col-lg-6 col-xl-5"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Exporting Data to Excel</DIV>
        <DIV CLASS="card-body">

          <H5 ID="div-op-title"></H5>
          <DIV CLASS="progress" ID="progress-div" style="height:25px;">
            <DIV ID="progress-bar" CLASS="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" STYLE="width:0%;">
            </DIV>
          </DIV>

          <P>
          <DIV ID="div-op-details"></DIV>
          <DIV ID="div-op-extra"></DIV>

          <DIV ID="download_ready" STYLE="visibility: hidden;">
            <P>
            <DIV CLASS="text-center">
              The data has been exported as an Excel file.
            </DIV>

            <P>
            <DIV CLASS="text-center">
              <BUTTON CLASS="btn btn-success" TYPE="button" onClick="location.href='/tmp/$filename'"><SPAN CLASS="bi bi-download"></SPAN> Download</BUTTON>
            </DIV>
            </P>

            <DIV CLASS="text-center">
              <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='flowViewData.cld?f=$flowID&j=$jobID'"><SPAN CLASS="bi bi-check-lg"></SPAN> Done</BUTTON>
            </DIV>
            <P>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get the CGI input variables
  $exportDest = $q->param('dest');
  $flowID = $q->param('f');
  $jobID = $q->param('j');

  #connect to the database
  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  $flowName = prep_flow_id_to_name($prepDB, $flowID);

  print_html_header();

  #make sure we have read privs for this data source
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to export this data flow.");
  }

  prep_audit($prepDB, $userID, "Exported data to Excel", $flowID);
  utils_slack("PREP: $first $last exported data to Excel in $flowName");

  #build file names
  $tmp = $flowName;
  $tmp =~ s/\s/_/g;
  $tmp =~ s/\W//g;
  $tmp = substr($tmp, 0, 50);
  $filename = "koala-prep-export_" . $tmp . "_$jobID" . ".xlsx";

  #fork a new process to do the actual data dumping in the background
  $SIG{CHLD} = "IGNORE";
  if ($pid = fork())
  {
    #parent process

    print_status_html();
  }
  else
  {
    #child process

    #let Apache know not to wait on the child process
    close(STDIN);
    close(STDOUT);

    #redirect STDERR to the Koala error log
    close(STDERR);
    open(STDERR, ">>/opt/apache/htdocs/tmp/koala_error.log");
    select(STDERR);
    $| = 1;

    chdir("/opt/apache/htdocs/tmp/");

    #reconnect to the database
    $kapDB = KAPutil_connect_to_database();
    $prepDB = PrepUtils_connect_to_database();

    flow_telemetry($prepDB, $jobID, "Started export of data to Excel");

    $query = "UPDATE prep.jobs SET PID=$$, opInfo='0|Exporting data' \
        WHERE ID=$jobID";
    $prepDB->do($query);
    PrepUtils_set_job_op_title($prepDB, $jobID, "Exporting data to Excel");
    PrepUtils_set_job_op_pct($prepDB, $jobID, "0");

    #create a new Excel workbook
    $workbook = Excel::Writer::XLSX->new("/opt/apache/htdocs/tmp/$filename");
    $workbook->set_optimization();

    #add worksheet
    $worksheet1 = $workbook->add_worksheet("Exported Data");

    #define header format
    $formatHeadBlue = $workbook->add_format();
    $formatHeadBlue->set_bold(1);
    $formatHeadBlue->set_bg_color('blue');
    $formatHeadBlue->set_color('white');

    $formatWarn = $workbook->add_format();
    $formatWarn->set_bold(1);
    $formatWarn->set_color("FFFFFF");
    $formatWarn->set_size(12);

    #build master table name strings
    $masterTable = "prep_data.$jobID" . "_master";
    $masterColTable = "prep_data.$jobID" . "_master_cols";

    #get IDs of columns in display order
    @orderedCols = prep_flow_order_columns($prepDB, $flowID, $jobID);
    $colOrder = join(',', @orderedCols);

    #get the list of data columns we're exporting
    $query = "SELECT ID, name, type FROM $masterColTable \
        ORDER BY FIELD(ID, $colOrder)";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;

    undef(@headerCols);
    undef($subQ);
    $col = 0;
    while (($colID, $colName, $type) = $dbOutput->fetchrow_array)
    {
      $subQ = $subQ . "column_$colID,";

      #use our data tags to explicitly label column types
      if (($type eq "pseg") || ($type eq "gseg") || ($type eq "tseg"))
      {
        $colName = $type . ":" . $colName;
      }
      elsif (($type eq "palias") || ($type eq "galias") || ($type eq "talias"))
      {
        $colName = $type . ":" . $colName;
      }
      elsif (($type eq "pattr") || ($type eq "gattr") || ($type eq "tattr"))
      {
        $colName = $type . ":" . $colName;
      }
      elsif ($type eq "upc")
      {
        $colName = "UPC";
      }
      elsif ($type eq "product")
      {
        $colName = "Product";
      }
      elsif ($type eq "geography")
      {
        $colName = "Geography";
      }
      elsif ($type eq "time")
      {
        $colName = "Time";
      }

      $worksheet1->write(0, $col, $colName, $formatHeadBlue);
      $col++;
    }
    chop($subQ);

    #get the number of rows to be exprted
    $query = "SELECT rowCount FROM prep.jobs WHERE ID=$jobID";
    $dbOutput = $prepDB->prepare($query);
    $status = $dbOutput->execute;
    ($totalRows) = $dbOutput->fetchrow_array;

    #if we're over Excel's limit, warn the user that we're truncating
    if ($totalRows > 999_998)
    {
      $shape = $workbook->add_shape(type=> 'octagon', scale_x=>3, scale_y=>3, text=> "Data too large for Excel\nTruncated");
      $shape->set_fill("FF0000");
      $shape->set_format($formatWarn);
      $worksheet1->insert_shape('C5', $shape);
      $totalRows = 999_999;
    }

    #get the highest ID number we're going to use for exporting
    $query = "SELECT MAX(ID) FROM $masterTable";
    $dbOutput = $prepDB->prepare($query);
    $status = $dbOutput->execute;
    ($maxID) = $dbOutput->fetchrow_array;

    #select the data from the data flow job
    $rowIndex = 1;
    $rowsDone = 0;
    $start = 0;
    PrepUtils_set_job_op_details($prepDB, $jobID, "Writing data to Excel workbook");
    while (($start <= $maxID) && ($rowsDone < 999_998))
    {
      $end = $start + 100_000;

      $query = "SELECT $subQ FROM $masterTable WHERE ID > $start && ID <= $end";
      $dbOutput = $prepDB->prepare($query);
      $status = $dbOutput->execute;

      #output every row of data to the Excel workbook
      while (@rowData = $dbOutput->fetchrow_array)
      {
        if (($rowsDone % 5_000) == 0)
        {
          $pct = ($rowsDone / $totalRows) * 100;
          $pct = int($pct);
          $opInfo = "$pct|Exporting data";
          $q_opInfo = $prepDB->quote($opInfo);
          $query = "UPDATE prep.jobs SET opInfo=$q_opInfo WHERE ID=$jobID";
          $prepDB->do($query);
          $rowsRemaining = $totalRows - $rowsDone;
          $rowsRemaining = prep_autoscale_number($rowsRemaining);
          PrepUtils_set_job_op_extra($prepDB, $jobID, "<B>Records remaining:</B> $rowsRemaining");
          PrepUtils_set_job_op_pct($prepDB, $jobID, $pct);
        }

        $worksheet1->write_row($rowIndex, 0, \@rowData);
        $rowIndex++;
        $rowsDone++;
      }

      $start = $end;
    }

    $workbook->close();

    flow_telemetry($prepDB, $jobID, "Finished export of data to Excel");

    $query = "UPDATE prep.jobs SET PID=NULL, opInfo='DONE', lastAction=NOW() \
        WHERE ID=$jobID";
    $prepDB->do($query);

    #clear detailed op stats
    PrepUtils_set_job_op_title($prepDB, $jobID, "");
    PrepUtils_set_job_op_details($prepDB, $jobID, "");
    PrepUtils_set_job_op_extra($prepDB, $jobID, "");
    PrepUtils_set_job_op_pct($prepDB, $jobID, "");
  }

#EOF
