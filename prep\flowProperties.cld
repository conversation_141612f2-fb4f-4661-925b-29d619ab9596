#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::PrepClientTrim;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::Users;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($kapDB);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Data Flow Properties</TITLE>
<LINK REL="SHORTCUT ICON" HREF="$Lib::KoalaConfig::kapHostURL/favicon.png">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<STYLE>
.accordion-button.collapsed {
  background: blue
}

.accordion-button.collapsed::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}
</STYLE>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="$Lib::KoalaConfig::kapHostURL/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?f=$flowID">Data Prep</A></LI>
    <LI CLASS="breadcrumb-item">$flowName</LI>
    <LI CLASS="breadcrumb-item active">Properties</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $flowID = $q->param('f');

  #connect to the database
  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #make sure we have write privs for this data source
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to view this data flow's properties.");
  }

  $flowName = prep_flow_id_to_name($prepDB, $flowID);

  print_html_header();

  $query = "SELECT userID, name, description, sourceInfo, parseOptions \
      FROM prep.flows WHERE ID=$flowID";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($ownerID, $name, $description, $sourceInfo, $parseOptions) = $dbOutput->fetchrow_array;

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-11 col-lg-8 col-xl-7"> <!-- content -->

      <FORM METHOD="post" ACTION="flowPropertiesSave.cld">
      <INPUT TYPE="hidden" NAME="f" VALUE="$flowID">
      <INPUT TYPE="hidden" NAME="oldOwnerID" VALUE="$ownerID">

      <DIV CLASS="accordion mx-auto" ID="accordion">

      <DIV CLASS="accordion-item border-primary">
        <H2 CLASS="accordion-header">
          <BUTTON CLASS="accordion-button bg-primary text-white icon-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
            Data Flow Properties
          </BUTTON>
        </H2>
        <DIV ID="collapse1" CLASS="accordion-collapse collapse show" data-bs-parent="#accordion">
        <DIV CLASS="accordion-body">

          <TABLE>
            <TR>
              <TD STYLE="text-align:right;">
                <LABEL FOR="name">Name:&nbsp;</LABEL>
              </TD>
              <TD>
                <INPUT CLASS="form-control" TYPE="text" NAME="name" ID="name" VALUE="$name" STYLE="width:300px;" required>
              </TD>
            </TR>

            <TR>
              <TD>
                <LABEL FOR="dsDescription">Description:&nbsp;</LABEL>
              </TD>
              <TD>
                <INPUT CLASS="form-control" TYPE="text" NAME="dsDescription" ID="dsDescription" VALUE="$description" MAXLENGTH="1023">
              </TD>
            </TR>
          </TABLE>

          <P>
          <DIV CLASS="row">
            <DIV CLASS="col-auto mt-2">
              <LABEL FOR="dsOwner">Data Flow Owner:&nbsp;</LABEL>
            </DIV>
            <DIV CLASS="col-auto">
              <SELECT CLASS="form-select" NAME="dsOwner" ID="dsOwner">
END_HTML

  #get a list of all users in the private cloud, and display
  $query = "SELECT ID, first, last, orgID FROM users WHERE acctType > 0 \
      ORDER BY orgID, last";
  $dbOutput = $kapDB->prepare($query);
  $dbOutput->execute;

  while (($id, $userFirst, $userLast, $userOrg) = $dbOutput->fetchrow_array)
  {
    $userOrg = Users_orgID_to_name($kapDB, $userOrg);
    print(" <OPTION VALUE='$id'>$userFirst $userLast ($userOrg)</OPTION>\n");
  }

  print <<END_HTML;
              </SELECT>
              <SCRIPT>
                \$('select#dsOwner').val('$ownerID');
              </SCRIPT>
            </DIV>
          </DIV>

          <P></P>
          (If you change the owner of this data flow to another user, you'll still retain read and write privileges.)

          </DIV>
        </DIV>
        </DIV>

END_HTML

  #if we're pulling from a Nielsen IDW, determine the data set we're using
  if ($sourceInfo =~ m/^FTP=nielsen\|.*PATH=(.*)\.zip/i)
  {

    #deteremine which data set we're currently using
    $idwDataSet = $1;

    $clientTrim = "";
    if ($parseOptions =~ m/\|MKTTRIM=(.*)/)
    {
      $clientTrimName = prep_client_org_name_hash($1);
      if (length($clientTrimName) > 0)
      {
        $clientTrim = "The $clientTrimName pre-trim is being applied.";
      }
    }

    print <<END_HTML;
    <DIV CLASS="accordion-item border-primary">
      <H2 CLASS="accordion-header">
        <BUTTON CLASS="accordion-button collapsed bg-primary text-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
          Intelligent Data Warehouse Settings
        </BUTTON>
      </H2>
      <DIV ID="collapse2" CLASS="accordion-collapse collapse" data-bs-parent="#accordion">
        <DIV CLASS="accordion-body">

          <DIV CLASS="row">
            <DIV CLASS="col-auto">
              This data flow pulls data from the <CODE>$idwDataSet</CODE> data set. $clientTrim
            </DIV>
          </DIV>

          </DIV>
        </DIV>
      </DIV>

END_HTML
  }

  print <<END_HTML;

      </DIV>

      <P>
      <DIV CLASS="text-center">
        <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="location.href='main.cld?f=$flowID'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
        <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT"><I CLASS="bi bi-save"></I> Save</BUTTON>
      </DIV>

      </FORM>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
