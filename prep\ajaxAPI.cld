#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $service = $q->param('svc');
  $flowID = $q->param('f');
  $jobID = $q->param('j');
  $itemID = $q->param('item');
  $allSteps = $q->param('as');

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #if we're being asked for a job's detailed operation status
  if ($service eq "job_op_status")
  {
    $query = "SELECT opInfo, opTitle, opPctComplete, opDetails, opTimeEstimate, opExtra
        FROM prep.jobs WHERE ID=$jobID";
    $dbOutput = $prepDB->prepare($query);
    $status = $dbOutput->execute;
    PrepUtils_handle_db_err($prepDB, $status, $query);
    ($opInfo, $opTitle, $opPctComplete, $opDetails, $opTimeEstimate, $opExtra) = $dbOutput->fetchrow_array;

    if ($opInfo =~ m/^DONE/)
    {
      $content = "DONE";
    }
    elsif ($opInfo =~ m/^ERR/)
    {
      $content = $opInfo;
    }
    else
    {

      #if we're running all of the steps in a recipe, adjust % complete
      if ($allSteps == 1)
      {
        $query = "SELECT MAX(step) FROM prep.recipes WHERE flowID=$flowID";
        $dbOutput = $prepDB->prepare($query);
        $status = $dbOutput->execute;
        PrepUtils_handle_db_err($prepDB, $status, $query);
        ($numSteps) = $dbOutput->fetchrow_array;

        $opInfo =~ m/^(\d+)\|.*/;
        $overallPct = $1;

        if ($numSteps == 0)
        {
          $opPctComplete = 100;
        }
        else
        {
          $opPctComplete = $overallPct + ($opPctComplete / $numSteps);
        }
      }

      $content = "$opTitle|$opPctComplete|$opDetails|$opTimeEstimate|$opExtra";
    }

    print("Content-type: application/text\n\n");
    print("$content");
    exit;
  }


  #if we're being asked for the current number of jobs on the system
  if ($service eq "perf_jobs")
  {
    $query = "SELECT jobs FROM app.performance WHERE instance='prep'";
    $dbOutput = $kapDB->prepare($query);
    $status = $dbOutput->execute;
    PrepUtils_handle_db_err($prepDB, $status, $query);
    ($jobs) = $dbOutput->fetchrow_array;

    print("Content-type: application/text\n\n");
    print("&value=$jobs");
    exit;
  }


  #if we're being asked for the current cpu usage levels
  if ($service eq "perf_cpu")
  {
    $query = "SELECT cpu FROM app.performance WHERE instance='prep'";
    $dbOutput = $kapDB->prepare($query);
    $status = $dbOutput->execute;
    PrepUtils_handle_db_err($prepDB, $status, $query);
    ($cpu) = $dbOutput->fetchrow_array;

    print("Content-type: application/text\n\n");
    print("&value=$cpu");
    exit;
  }


  #if we're being asked for the current memory usage levels
  if ($service eq "perf_memory")
  {
    $query = "SELECT memory FROM app.performance WHERE instance='prep'";
    $dbOutput = $kapDB->prepare($query);
    $status = $dbOutput->execute;
    PrepUtils_handle_db_err($prepDB, $status, $query);
    ($memory) = $dbOutput->fetchrow_array;

    print("Content-type: application/text\n\n");
    print("&value=$memory");
    exit;
  }


  #if we're being asked for the current storage throughput level
  if ($service eq "perf_throughput")
  {
    $query = "SELECT storageThroughput FROM app.performance WHERE instance='prep'";
    $dbOutput = $kapDB->prepare($query);
    $status = $dbOutput->execute;
    PrepUtils_handle_db_err($prepDB, $status, $query);
    ($throughput) = $dbOutput->fetchrow_array;

    print("Content-type: application/text\n\n");
    print("&value=$throughput");
    exit;
  }


  #if we're being asked for the current storage usage levels
  if ($service eq "perf_storage")
  {
    $query = "SELECT storage FROM app.performance WHERE instance='prep'";
    $dbOutput = $kapDB->prepare($query);
    $status = $dbOutput->execute;
    PrepUtils_handle_db_err($prepDB, $status, $query);
    ($storage) = $dbOutput->fetchrow_array;

    print("Content-type: application/text\n\n");
    print("&value=$storage");
    exit;
  }


#EOF
