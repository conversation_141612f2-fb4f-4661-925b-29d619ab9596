#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Restate Geographies</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Please Wait...');
  return(true);
}
</SCRIPT>

<STYLE>
.accordion-button.collapsed {
  background: blue
}

.accordion-button.collapsed::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}
</STYLE>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Restate Geographies</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsSchema = "datasource_" . $dsID;
  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  #make sure we have write privs for the data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to modify this data source.");
  }

  #get hash of geo names for use in drop-downs
  %geoNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "g");

  #build up the HTML option list used for the select boxes
  $geoOptionsHTML = "";
  foreach $id (sort {$geoNameHash{$a} cmp $geoNameHash{$b}} keys %geoNameHash)
  {
    $geoOptionsHTML .= "<OPTION>$geoNameHash{$id}</OPTION>\n";
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-12 col-lg-10 col-xl-8"> <!-- content -->

      <FORM METHOD="post" ACTION="restateGeosDo.cld" onsubmit="return checkForm(this);">
      <INPUT TYPE="hidden" NAME="ds" VALUE="$dsID">

      <DIV CLASS="accordion mx-auto" ID="accordion">
        <DIV CLASS="accordion-item border-primary">
          <H2 CLASS="accordion-header">
            <BUTTON CLASS="accordion-button bg-primary text-white icon-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
              Restate Geographies
            </BUTTON>
          </H2>
          <DIV ID="collapse1" CLASS="accordion-collapse collapse show" data-bs-parent="#accordion">
            <DIV CLASS="accordion-body">

              <TABLE CLASS="table">
                <THEAD><TR>
                  <TH>Old Geography Name</TH>
                  <TH>New Geography Name</TH>
                </TR></THEAD>
                <TR>
                  <TD>
                    <SELECT CLASS="form-select" NAME="oldgeo1">
                      $geoOptionsHTML
                    </SELECT>
                  </TD>
                  <TD><INPUT CLASS="form-control" NAME="newgeo1"></TD>
                </TR>
                <TR>
                  <TD>
                    <SELECT CLASS="form-select" NAME="oldgeo2">
                      $geoOptionsHTML
                    </SELECT>
                  </TD>
                  <TD><INPUT CLASS="form-control" NAME="newgeo2"></TD>
                </TR>
                <TR>
                  <TD>
                    <SELECT CLASS="form-select" NAME="oldgeo3">
                      $geoOptionsHTML
                    </SELECT>
                  </TD>
                  <TD><INPUT CLASS="form-control" NAME="newgeo3"></TD>
                </TR>
                <TR>
                  <TD>
                    <SELECT CLASS="form-select" NAME="oldgeo4">
                      $geoOptionsHTML
                    </SELECT>
                  </TD>
                  <TD><INPUT CLASS="form-control" NAME="newgeo4"></TD>
                </TR>
                <TR>
                  <TD>
                    <SELECT CLASS="form-select" NAME="oldgeo5">
                      $geoOptionsHTML
                    </SELECT>
                  </TD>
                  <TD><INPUT CLASS="form-control" NAME="newgeo5"></TD>
                </TR>
              </TABLE>

            </DIV>
          </DIV>
        </DIV>

        <DIV CLASS="accordion-item border-primary">
          <H2 CLASS="accordion-header">
            <BUTTON CLASS="accordion-button collapsed bg-primary text-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
              Bulk Restatement
            </BUTTON>
          </H2>
          <DIV ID="collapse2" CLASS="accordion-collapse collapse" data-bs-parent="#accordion">
            <DIV CLASS="accordion-body">
              If you need to restate more than few geographies, you can paste a comma-separated list of the geographies to restate in this box. Make sure the old name is on the left, and the new name is on the right.

              <P>&nbsp;</P>
              <TEXTAREA NAME="csv" CLASS="form-control w-100" STYLE="height:250px;""></TEXTAREA>

            </DIV>
          </DIV>
        </DIV>

      </DIV>

      <P>
      <DIV CLASS="text-center">
        <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="location.href='display.cld?ds=$dsID'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
        <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-submit">Restate <I CLASS="bi bi-arrow-right"></I></BUTTON>
      </DIV>

      </FORM>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
