#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;
use Text::CSV;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepClientTrim;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::Social;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($kapDB);

  print <<END_HTML;
<DOCCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Loading Data</TITLE>
<LINK REL="SHORTCUT ICON" HREF="$Lib::KoalaConfig::kapHostURL/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
let statusTimer = setInterval(function(){displayStatus()}, 1000);
let statCount = 0;

function displayStatus()
{
  const url = 'ajaxAPI.cld?svc=job_op_status&j=$jobID';

  \$.get(url, function(data, status)
  {
    let statusText = data;

    if (statusText.search('DONE') == 0)
    {
      \$('#progress-bar').css('width', '100%');
      clearInterval(statusTimer);
      location.href='flowDataType.cld?f=$flowID&j=$jobID&a=$action';
      return;
    }

    let statElements = statusText.split('|');

    let opTitle = statElements[0];
    let opPct = statElements[1];
    let opDetails = statElements[2];
    let opTimeEstimate = statElements[3];
    let opExtra = statElements[4];
    document.getElementById('div-op-title').innerHTML = opTitle;
    if (opPct.length > 0)
    {
      document.getElementById('progress-bar').innerHTML = opPct + '%';
      \$('#progress-bar').css('width', opPct+'%');
    }
    document.getElementById('div-op-details').innerHTML = opDetails;
    document.getElementById('div-op-extra').innerHTML = opExtra;
  });

  if (statCount > 5)
  {
    clearInterval(statusTimer);
    statusTimer = setInterval(function(){displayStatus()}, 5000);
  }
  statCount++;
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($kapDB, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="$Lib::KoalaConfig::kapHostURL/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">Data Prep</A></LI>
    <LI CLASS="breadcrumb-item">$flowName</LI>
    <LI CLASS="breadcrumb-item active">Loading Data</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------
#
# Output the HTML for the web page that displays the status dialog
#

sub print_status_html
{
  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-11 col-md-8 col-lg-6 col-xl-5"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Loading Data</DIV>
        <DIV CLASS="card-body">

          <H5 ID="div-op-title"></H5>
          <DIV CLASS="progress" style="height:25px;">
            <DIV ID="progress-bar" CLASS="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" STYLE="width:0%;">
            </DIV>
          </DIV>

          <P>
          <DIV ID="div-op-details"></DIV>
          <DIV ID="div-op-extra"></DIV>

          <P>&nbsp;</P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" ID="btnAsync" onClick="location.href='main.cld'"><I CLASS="bi bi-speedometer"></I> Be More Productive</BUTTON>
          </DIV>
        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get the CGI input variables
  $flowID = $q->param('f');
  $jobID = $q->param('j');
  $action = $q->param('a');

  #get parse options specified by the user
  $separator = $q->param('separator');
  $sepChar = $q->param('sepchar');

  $skiptop = $q->param('skiptop');
  $skiptoprows = $q->param('skiptoprows');

  $headers = $q->param('headers');
  $headerrows = $q->param('headerrows');

  $compressWS = $q->param('compressWS');
  $trimWS = $q->param('trimWS');

  $blanks = $q->param('blanks');

  $mktTrim = $q->param('mktTrim');

  if ($separator eq "comma")
  {
    $sepChar = ",";
  }
  elsif ($separator eq "tab")
  {
    $sepChar = "\t";
  }
  elsif ($separator eq "space")
  {
    $sepChar = " ";
  }
  elsif (($separator eq "other") && (length($sepChar) == 1))
  {
    #NOOP - keeping the user-specified value for sepChar
  }
  else
  {
    $sepChar = ",";
  }

  $blanksZeroes = 0;
  if ($blanks eq "zeroes")
  {
    $blanksZeroes = 1;
  }

  if (!defined($mktTrim))
  {
    $mktTrim = 0;
  }

  #connect to the database
  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  $flowName = prep_flow_id_to_name($prepDB, $flowID);

  #if we're rejoining an existing job
  if ($jobID > 0)
  {
    $query = "SELECT state FROM prep.jobs WHERE ID=$jobID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($state) = $dbOutput->fetchrow_array;
    if ($state =~ m/^LOAD-DATA/)
    {
      print_html_header();
      print_status_html();
      exit;
    }

    #else we've already moved on, and need to have the user try again
    elsif ($state ne "PARSE-WAIT")
    {
      print("Status: 302 Moved temporarily\n");
      print("Location: flowOpen.cld?f=$flowID\n\n");

      exit;
    }
  }

  #clear any item we might have put in the user's feed about waiting for info
  Social_clear_prep_parse_wait_items($kapDB, $flowID);

  print_html_header();

  #make sure we have write privs for this data flow
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to work with jobs in this data flow.");
  }

  #make sure the prep cloud isn't out of storage
  $usagePct = prep_flow_storage($acctType);
  if ($usagePct > 99)
  {
    exit_error("Your Koala Data Prep cloud is out of storage - please contact your Koala account manager to order more.")
  }

  #store the parsing options selected by the user
  $parseOptions = "SEPARATOR=$separator|SEPCHAR=$sepChar|SKIPTOP=$skiptop|SKIPTOPROWS=$skiptoprows|HEADERS=$headers|HEADERROWS=$headerrows|COMPRESSWS=$compressWS|TRIMWS=$trimWS|BLANKSZEROES=$blanksZeroes|MKTTRIM=$mktTrim";
  $q_parseOptions = $prepDB->quote($parseOptions);
  $query = "UPDATE prep.flows SET parseOptions=$q_parseOptions WHERE ID=$flowID";
  $prepDB->do($query);

  if ($mktTrim ne "0")
  {
    $clientTrimName = prep_client_org_name_hash($mktTrim);
    $auditStr = "Set data parsing options (using $clientTrimName pre-trim)";
  }
  else
  {
    $auditStr = "Set data parsing options"
  }
  prep_audit($prepDB, $userID, $auditStr, $flowID);
  utils_slack("PREP: $first $last set data parsing options in $flowName");

  #fork a new process to do the actual data loading in the background
  $SIG{CHLD} = "IGNORE";
  if ($pid = fork())
  {
    #parent process

    print_status_html();
  }
  else
  {
    #child process

    #let Apache know not to wait on the child process
    close(STDIN);
    close(STDOUT);

    #redirect STDERR to the Koala error log
    close(STDERR);
    open(STDERR, ">>/opt/apache/htdocs/tmp/koala_error.log");
    select(STDERR);
    $| = 1;

    #reconnect to the database
    $prepDB = PrepUtils_connect_to_database();
    $kapDB = KAPutil_connect_to_database();

    prep_flow_load_raw_data($prepDB, $kapDB, $flowID, $jobID, $userID);
  }

#EOF
