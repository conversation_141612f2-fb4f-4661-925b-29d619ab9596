#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  #output Content-type header
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  #output HTML
  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Calculated Measure</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Please Wait...');
  return(true);
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">New Calculated Measure</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');
  $measureID = $q->param('measID');

  #sanitize parameters
  $dsID = utils_sanitize_integer($dsID);
  if (!defined($dsID))
  {
    exit_early_error($session, "Invalid data source");
  }
  $measureID = utils_sanitize_integer($measureID);

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsName = ds_id_to_name($db, $dsID);

  $dsSchema = "datasource_" . $dsID;

  #if we're editing an existing measure, grab the info we need
  if ($measureID > 0)
  {
    $query = "SELECT name, calculation, calcBeforeAgg FROM $dsSchema.measures \
        WHERE ID=$measureID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($measName, $calculation, $calcBeforeAgg) = $dbOutput->fetchrow_array;

    #extract the measure type from the calculation string
    $calculation =~ m/^(.*?)\|/;
    $measType = $1;

    $action = "Edit";

    if ($calcBeforeAgg == 1)
    {
      $calcBeforeAgg = "CHECKED";
    }
    else
    {
      $calcBeforeAgg = "";
    }
  }

  print_html_header();

  #make sure we have write privs for this data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to create calculated measures in this data source.");
  }

  #make sure the data source isn't locked by another background process
  $ok = DSRutil_operation_ok($db, $dsID, "ADD-MEASURE");

  if ($ok != 1)
  {
    exit_warning("Another job is currently using this data source - please try again later.")
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-11 col-md-8 col-lg-6 col-xl-5"> <!-- content -->

      <FORM METHOD="post" ACTION="calcMeasureDefine.cld" onsubmit="return checkForm(this);">
      <INPUT TYPE="hidden" NAME="ds" VALUE="$dsID">
      <INPUT TYPE="hidden" NAME="measID" VALUE="$measureID">

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Calculated Measure</DIV>
        <DIV CLASS="card-body">

          <LABEL FOR="measureName">Measure Name:</LABEL>
          <INPUT CLASS="form-control" NAME="measureName" ID="measureName" VALUE="$measName" required>

          <SELECT CLASS="form-select my-3" NAME="measType" ID="measType" SIZE="10" required>
            <OPTION DISABLED>----- Arithmetic Measures -----</OPTION>
            <OPTION VALUE="sum">Sum</OPTION>
            <OPTION VALUE="difference">Difference</OPTION>
            <OPTION $selRatio VALUE="ratio">Ratio</OPTION>
            <OPTION $selMult VALUE="multiplication">Multiplication</OPTION>
            <OPTION $selPctChgMeas VALUE="pct_change_meas">% Change Between Measures</OPTION>
            <OPTION DISABLED>----- Time Measures -----</OPTION>
            <OPTION $selChange VALUE="change">Change YA</OPTION>
            <OPTION $selPctChange VALUE="pct_change">% Change YA</OPTION>
            <OPTION $selLag VALUE="lag">Lag</OPTION>
            <OPTION $selLead VALUE="lead">Lead</OPTION>
            <OPTION $selMovAvg VALUE="mov_avg">Moving Average</OPTION>
            <OPTION $selMovTotal VALUE="mov_total">Moving Total</OPTION>
            <OPTION $selYtd VALUE="ytd">Year To Date</OPTION>
            <OPTION DISABLED>----- Retail Measures -----</OPTION>
            <OPTION $selCount VALUE="count">Count</OPTION>
            <OPTION $selCount VALUE="index">Index</OPTION>
            <OPTION $selShare VALUE="share">Share</OPTION>
            <OPTION DISABLED>----- Manual -----</OPTION>
            <OPTION $selCalc VALUE="calc">Calculator</OPTION>
          </SELECT>
          <SCRIPT>
            \$('select#measType').val('$measType');
          </SCRIPT>

          <DIV CLASS="form-check">
            <INPUT CLASS="form-check-input" NAME="calcBeforeAgg" ID="calcBeforeAgg" TYPE="checkbox" $calcBeforeAgg>
            <LABEL CLASS="form-check-label" FOR="calcBeforeAgg">Calculate the value of this measure for each item before aggregation</LABEL>
          </DIV>

          <P>&nbsp;<P>

          <DIV CLASS="text-center">
            <BUTTON TYPE="button" CLASS="btn btn-secondary" onclick="location.href='display.cld?ds=$dsID&dim=m'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-submit">Next <I CLASS="bi bi-arrow-right"></I></BUTTON>
          </DIV>

        </DIV>
      </DIV>

      </FORM>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
