#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::Reports;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $rptID = $q->param('rptID');
  $visID = $q->param('v');
  $minColor = $q->param('minColor');
  $maxColor = $q->param('maxColor');

  #handle changed colors if we're saving data colors
  @names = $q->param;
  foreach $name (@names)
  {
    if ($name =~ m/^color_/)
    {
      $savingColors = 1;

      $value = $q->param($name);
      $saveColors{$name} = $value;
    }
  }

  $db = KAPutil_connect_to_database();

  #make sure we have edit privs for this report
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this report.");
  }

  $cubeName = cube_id_to_name($db, $rptID);
  $dsID = cube_get_ds_id($db, $rptID);
  $dsName = ds_id_to_name($db, $dsID);

  $dsSchema = "datasource_" . $dsID;

  #get the chart title details from the database
  $query = "SELECT design FROM visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($graphDesign) = $dbOutput->fetchrow_array;

  #extract graph captions from design string
  $graphSeriesColor = reports_get_style($graphDesign, "seriesColor");
  $chartType = reports_get_style($graphDesign, "type");


  ########################################################################
  #
  # This code block is called on submit to save the changes
  #

  #if we're being called to save updated treemap color gradient points
  if (defined($minColor))
  {
    $graphDesign = reports_set_style($graphDesign, "minColor", $minColor);
    $graphDesign = reports_set_style($graphDesign, "maxColor", $maxColor);

    $q_design = $db->quote($graphDesign);
    $query = "UPDATE visuals SET design = $q_design WHERE ID=$visID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    utils_audit($db, $userID, "Changed treemap color gradients", $dsID, $rptID, 0);
    $activity = "$first $last changed treemap color gradients for $cubeName in $dsName";
    utils_slack($activity);

    exit;
  }

  #if we're being called to save updated chart series colors
  if ($savingColors == 1)
  {

    #save any color that's been changed
    foreach $name (keys %saveColors)
    {
      $value = $saveColors{$name};
      $graphDesign = reports_set_style($graphDesign, $name, $value);
    }

    $q_graphDesign = $db->quote($graphDesign);
    $query = "UPDATE visuals SET design = $q_graphDesign WHERE ID=$visID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    utils_audit($db, $userID, "Changed series colors", $dsID, $rptID, 0);
    $activity = "$first $last changed series colors for $cubeName in $dsName";
    utils_slack($activity);

    exit;
  }


  #########################################################################
  #
  # Everything after this point is called to display the chart layout dialog
  #

  #extract map gradient colors from design string
  $minColor = reports_get_style($graphDesign, "minColor");
  $maxColor = reports_get_style($graphDesign, "maxColor");

  if (defined($minColor))
  {
    $minColor = "#" . $minColor;
  }
  if (defined($maxColor))
  {
    $maxColor = "#" . $maxColor;
  }

  if (!(defined($minColor)))
  {
    $minColor = "#fd625e";
  }
  if (!(defined($maxColor)))
  {
    $maxColor = "#01b8aa";
  }

  #get ends of the color gradient if we're a tree map
  if ($chartType eq "TreeMap")
  {
    print <<END_HTML;
<SCRIPT>
function submitForm()
{
  let min = document.getElementById('minColor').value;
  let max = document.getElementById('maxColor').value;

  //knock # off of color strings
  min = min.substr(1);
  max = max.substr(1);

  let url = "xhrChartSeriesColor?rptID=$rptID&v=$visID&minColor=" + min +
  "&maxColor=" + max;

  \$.get(url, function(data, status)
  {
    location.href = "display.cld?rpt=$rptID&v=$visID";
  });
}
</SCRIPT>

<DIV CLASS="modal-dialog" STYLE="overflow-y: initial !important;">
  <DIV CLASS="modal-content">
    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Tree Map Gradient Colors</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">
      <FORM>
      <TABLE CLASS="mx-auto">
        <TR>
          <TD STYLE="text-align:right;">
            Minimum color:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="color" NAME="minColor" ID="minColor" VALUE="$minColor" STYLE="width:3em;">
          </TD>
        </TR>

        <TR>
          <TD>&nbsp;</TD><TD>&nbsp;</TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Maximum color:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="color" NAME="maxColor" ID="maxColor" VALUE="$maxColor">
          </TD>
        </TR>
      </TABLE>
      </FORM>

    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="submitForm()"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
   </DIV>

  </DIV>
</DIV>
END_HTML
  }


  # --------------- Get series colors for all other charts -----------------

  else
  {
    print <<END_HTML;
<SCRIPT>
function submitForm()
{
  let url = "xhrChartSeriesColor?rptID=$rptID&v=$visID";

  for (let item in currentColors)
  {
    let setColor = document.getElementById(item).value;

    if (setColor != currentColors[item].toLowerCase())
    {
      setColor = encodeURIComponent(setColor);
      url = url + "&" + item + "=" + setColor;
    }
  }

  \$.get(url, function(data, status)
  {
    location.href = "display.cld?rpt=$rptID&v=$visID";
  });
}



function revertDefaults()
{
  for (let item in defaultColors)
  {
    document.getElementById(item).value = defaultColors[item];
  }
}
</SCRIPT>

<DIV CLASS="modal-dialog" STYLE="overflow-y: initial !important;">
  <DIV CLASS="modal-content">
    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Data Colors</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">
      <FORM>
END_HTML

  #get the type of graph and what it's backing data looks like
  $graphType = reports_get_style($graphDesign, "type");
  $script = reports_data_script($graphType);
  if ($script =~ m/Multi/)
  {
    $family = "multi";
  }
  elsif ($script =~ m/XYZ/)
  {
    $family = "xyz";
  }
  else
  {
    $family = "single";
  }

  #now, get the series dimension and members of that dimension
  $query = "SELECT graph_x, graph_y FROM visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($graph_x, $graph_y) = $dbOutput->fetchrow_array;

  #if graph_y is blank, it implies the measure dimension
  if (length($graph_y) < 1)
  {
    $graph_y = "m";
  }

  if ($family eq "multi")
  {
    $seriesDim = $graph_y;
  }
  elsif ($family eq "xyz")
  {
    $seriesDim = $graph_x;
  }
  elsif ($family eq "single")
  {
    $seriesDim = $graph_x;
  }

  #now that we know the series dimension, get that dim's members in the cube
  if ($seriesDim eq "p")
  {
    $seriesName = "products";
  }
  elsif ($seriesDim eq "g")
  {
    $seriesName = "geographies";
  }
  elsif ($seriesDim eq "t")
  {
    $seriesName = "timeperiods";
  }
  else
  {
    $seriesName = "measures";
  }
  $query = "SELECT $seriesName FROM app.cubes WHERE ID=$rptID";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($seriesMemberStr) = $dbOutput->fetchrow_array;
  @seriesMembers = split(',', $seriesMemberStr);

  #get the names of the series items
  %seriesNames = dsr_get_item_name_hash($db, $dsSchema, $seriesDim);

  #as we cycle through the colors, we're going to build a JS hashmap of the
  #default/already selected colors so we only change things the user actually
  #changed
  $JShashMap = "currentColors = {};\n";
  $JSdefaultColors = "defaultColors = {};\n";

  #get the default color palette
  @colors = reports_graph_color_array;

  #output a color definition block for each member of the series
  $idx = 0;
  print("<TABLE>\n");
  foreach $item (@seriesMembers)
  {
    $currentColor = $colors[$idx];
    $name = "color_" . $item;
    $JSdefaultColors .= "defaultColors['$name'] = '$currentColor'\n";

    #see if we have a custom color, and use it in preference to the default
    $customColor = reports_get_style($graphDesign, $name);
    if (length($customColor) > 6)
    {
      $currentColor = $customColor;
    }

    $JShashMap .= "currentColors['$name'] = '$currentColor'\n";

    print <<END_HTML;
        <TR>
          <TD STYLE="text-align:right;">$seriesNames{$item} &nbsp;</TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="color" NAME="$name" ID="$name" VALUE="$currentColor" STYLE="width:50px;">
          </TD>
        </TR>
END_HTML

    $idx++;
    if ($idx > 47)
    {
      $idx = 0;
    }
  }

  print <<END_HTML;
        <TR>
          <TD COLSPAN="2" STYLE="text-align:center;">
            <P></P>
            <A CLASS="text-decoration-none" HREF="#" onClick="revertDefaults()">Revert to default</A>
          </TD>
        </TR>
      </TABLE>
      <SCRIPT>
$JShashMap

$JSdefaultColors
      </SCRIPT>
      </FORM>

    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="submitForm()"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML
  }

#EOF
