#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  #output Content-type header
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  #output HTML
  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Data Source Statistics</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Statistics</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

sub commify
{
  my $text = reverse $_[0];
  $text =~ s/(\d\d\d)(?=\d)(?!\d*\.)/$1,/g;
  return scalar reverse $text;
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');
  $refLink = $q->param('r');

  if ($refLink eq "m")
  {
    $refLink = "main.cld?ds=$dsID";
  }
  else
  {
    $refLink = "display.cld?ds=$dsID";
  }

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsSchema = "datasource_" . $dsID;
  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  #make sure we have read privs for this data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to view statistics for this data source.");
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-11 col-lg-8 col-xl-7"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Data Source Statistics</DIV>
        <DIV CLASS="card-body">
END_HTML

  #get the size of the data source's tables and indexes on disk
  $query = "SELECT SUM(data_length), SUM(index_length) \
      FROM information_schema.TABLES \
      WHERE information_schema.TABLES.table_schema = '$dsSchema'";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($sizeData, $sizeIndex) = $dbOutput->fetchrow_array;
  $size = $sizeData + $sizeIndex;

  #turn the size into MB
  #NB: throughout this code, you'll see size calculations using 1000 instead of
  #   the "correct" 1024. Why? Because storage manufacturers use the correct
  #   order-of-magnitude values since it lets them sell less disk for more
  #   money. And that carries over even into massive cloud storage arrays
  #   like we use.
  $size = $size / 1000;
  $size = $size / 1000;

  #if we're into GB range, convert to GB
  if ($size > 1000)
  {
    $size = $size / 1000;
    $size = sprintf("%0.2f GB", $size);
  }
  else
  {
    $size = sprintf("%0.2f MB", $size);
  }

  #get the size of the data source's fact table and index on disk
  $tableName = $dsSchema . ".facts";
  $query = "SELECT data_length, index_length, table_rows \
      FROM information_schema.TABLES \
      WHERE information_schema.TABLES.table_schema = '$dsSchema' AND information_schema.TABLES.table_name = 'facts'";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($sizeData, $sizeIndex, $recordCount) = $dbOutput->fetchrow_array;
  $factSize = $sizeData + $sizeIndex;

  #turn the size into MB
  $factSize = $factSize / 1000;
  $factSize = $factSize / 1000;

  #if we're into GB range, convert to GB
  if ($factSize > 1000)
  {
    $factSize = $factSize / 1000;
    $factSize = sprintf("%0.2f GB", $factSize);
  }
  else
  {
    $factSize = sprintf("%0.2f MB", $factSize);
  }

  #get the size of the data source's reports
  $query = "SELECT COUNT(data_length), SUM(data_length), SUM(index_length) \
      FROM information_schema.TABLES \
      WHERE information_schema.TABLES.table_schema = '$dsSchema' AND information_schema.TABLES.table_name LIKE '_rptcube_%'";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($rptCount, $sizeData, $sizeIndex) = $dbOutput->fetchrow_array;
  $rptSize = $sizeData + $sizeIndex;

  #turn the size into MB
  $rptSize = $rptSize / 1000;
  $rptSize = $rptSize / 1000;

  #if we're into GB range, convert to GB
  if ($rptSize > 1000)
  {
    $rptSize = $rptSize / 1000;
    $rptSize = sprintf("%0.2f GB", $rptSize);
  }
  else
  {
    $rptSize = sprintf("%0.2f MB", $rptSize);
  }

  #get the number of base items in each dimension
  $query = "SELECT COUNT(ID) FROM $dsSchema.products";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($prodCount) = $dbOutput->fetchrow_array;
  $query = "SELECT COUNT(ID) FROM $dsSchema.geographies";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($geoCount) = $dbOutput->fetchrow_array;
  $query = "SELECT COUNT(ID) FROM $dsSchema.timeperiods";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($timeCount) = $dbOutput->fetchrow_array;
  $query = "SELECT COUNT(ID) FROM $dsSchema.measures";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($measCount) = $dbOutput->fetchrow_array;
  $query = "SELECT COUNT(ID) FROM $dsSchema.measures WHERE !ISNULL(calculation)";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($calcMeasCount) = $dbOutput->fetchrow_array;
  $baseMeasCount = $measCount - $calcMeasCount;

  #total number of data points contained in data source
  $dataPoints = $prodCount * $geoCount * $timeCount * $measCount;

  #get the number of lists in each dimension
  $query = "SELECT COUNT(ID) FROM $dsSchema.product_list";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($prodListCount) = $dbOutput->fetchrow_array;
  $query = "SELECT COUNT(ID) FROM $dsSchema.geography_list";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($geoListCount) = $dbOutput->fetchrow_array;
  $query = "SELECT COUNT(ID) FROM $dsSchema.time_list";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($timeListCount) = $dbOutput->fetchrow_array;
  $query = "SELECT COUNT(ID) FROM $dsSchema.measure_list";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($measListCount) = $dbOutput->fetchrow_array;

  #get the number of aggregates in each dimension
  $query = "SELECT COUNT(ID) FROM $dsSchema.product_aggregate";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($prodAggCount) = $dbOutput->fetchrow_array;
  $query = "SELECT COUNT(ID) FROM $dsSchema.geography_aggregate";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($geoAggCount) = $dbOutput->fetchrow_array;
  $query = "SELECT COUNT(ID) FROM $dsSchema.time_aggregate";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($timeAggCount) = $dbOutput->fetchrow_array;

  #get the number of segments and segmentations in each dimension
  $query = "SELECT COUNT(ID) FROM $dsSchema.product_segmentation";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($prodSegCount) = $dbOutput->fetchrow_array;
  $query = "SELECT COUNT(ID) FROM $dsSchema.product_segment";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($prodSegmentCount) = $dbOutput->fetchrow_array;
  $query = "SELECT COUNT(ID) FROM $dsSchema.geography_segmentation";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($geoSegCount) = $dbOutput->fetchrow_array;
  $query = "SELECT COUNT(ID) FROM $dsSchema.geography_segment";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($geoSegmentCount) = $dbOutput->fetchrow_array;
  $query = "SELECT COUNT(ID) FROM $dsSchema.time_segmentation";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($timeSegCount) = $dbOutput->fetchrow_array;
  $query = "SELECT COUNT(ID) FROM $dsSchema.time_segment";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($timeSegmentCount) = $dbOutput->fetchrow_array;

  #get the date of the last DS update
  $query = "SELECT lastUpdate FROM app.dataSources WHERE ID=$dsID";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($lastUpdate) = $dbOutput->fetchrow_array;

  #get the number of times the data source has been updated
  $query = "SELECT COUNT(ID) FROM $dsSchema.update_history";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($lifetimeUpdates) = $dbOutput->fetchrow_array;
  if ($lifetimeUpdates < 1)
  {
    $lifetimeUpdates = 1;
  }

  #how many months ago was the oldest update
  $query = "SELECT UNIX_TIMESTAMP(NOW()) - UNIX_TIMESTAMP(timestamp) AS dsAge \
      FROM $dsSchema.update_history ORDER BY ID LIMIT 1";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($dsAge) = $dbOutput->fetchrow_array;

  #convert dsAge to months
  $dsAge = $dsAge / 2_592_000;

  #calculate update frequency as a per-month value
  if ($dsAge == 0)
  {
    $updateFreq = 0;
  }
  else
  {
    $updateFreq = $lifetimeUpdates / $dsAge;
  }
  $updateFreq = sprintf("%.1f", $updateFreq);

  #if the data source is exported via ODBC
  $query = "SELECT ODBCexport FROM app.dataSources WHERE ID=$dsID";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($ODBCexport) = $dbOutput->fetchrow_array;

  if ($ODBCexport > 0)
  {

    #get the size of the data source's ODBC export table and index on disk
    $query = "SELECT data_length, index_length, table_rows \
        FROM information_schema.TABLES \
        WHERE information_schema.TABLES.table_schema = '$dsSchema' AND information_schema.TABLES.table_name = 'export'";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($sizeData, $sizeIndex, $ODBCrows) = $dbOutput->fetchrow_array;
    $ODBCSize = $sizeData + $sizeIndex;

    #turn the size into MB (using disk manufacturers' definition of MB)
    $ODBCSize = $ODBCSize / 1000;
    $ODBCSize = $ODBCSize / 1000;

    #if we're into GB range, convert to GB
    if ($ODBCSize > 1000)
    {
      $ODBCSize = $ODBCSize / 1000;
      $ODBCSize = sprintf("%0.2f GB", $ODBCSize);
    }
    else
    {
      $ODBCSize = sprintf("%0.2f MB", $ODBCSize);
    }
  }

  #add magnitude commas to potentially large numbers
  $dataPoints = commify($dataPoints);
  $prodCount = commify($prodCount);
  $recordCount = commify($recordCount);

  print <<END_HTML;
          <DIV CLASS="table-responsive">
            <TABLE CLASS="table table-sm table-borderless">
              <TR>
                <TD STYLE="text-align:right; font-weight:bold;">Data source size:</TD>
                <TD>$size</TD>
              </TR>
              <TR>
                <TD STYLE="text-align:right; font-weight:bold;">Data points:</TD>
                <TD>$dataPoints</TD>
              </TR>
              <TR>
                <TD>&nbsp;</TD><TD>&nbsp;</TD>
              </TR>
              <TR>
                <TD STYLE="text-align:right; font-weight:bold;">Products:</TD>
                <TD>$prodCount</TD>
              </TR>
              <TR>
                <TD>&nbsp;</TD>
                <TD>$prodListCount lists</TD>
              </TR>
              <TR>
                <TD>&nbsp;</TD>
                <TD>$prodAggCount aggregates</TD>
              </TR>
              <TR>
                <TD>&nbsp;</TD>
                <TD>$prodSegmentCount segments in $prodSegCount segmentations</TD>
              </TR>
              <TR>
                <TD STYLE="text-align:right; font-weight:bold;">Geographies:</TD>
                <TD>$geoCount</TD>
              </TR>
              <TR>
                <TD>&nbsp;</TD>
                <TD>$geoListCount lists</TD>
              </TR>
              <TR>
                <TD>&nbsp;</TD>
                <TD>$geoAggCount aggregates</TD>
              </TR>
              <TR>
                <TD>&nbsp;</TD>
                <TD>$geoSegmentCount segments in $geoSegCount segmentations</TD>
              </TR>
              <TR>
                <TD STYLE="text-align:right; font-weight:bold;">Time Periods:</TD>
                <TD>$timeCount</TD>
              </TR>
              <TR>
                <TD>&nbsp;</TD>
                <TD>$timeListCount lists</TD>
              </TR>
              <TR>
                <TD>&nbsp;</TD>
                <TD>$timeAggCount aggregates</TD>
              </TR>
              <TR>
                <TD>&nbsp;</TD>
                <TD>$timeSegmentCount segments in $timeSegCount segmentations</TD>
              </TR>
              <TR>
                <TD STYLE="text-align:right; font-weight:bold;">Measures:</TD>
                <TD>$measCount ($calcMeasCount calculated)</TD>
              </TR>
              <TR>
                <TD>&nbsp;</TD>
                <TD>$measListCount lists</TD>
              </TR>
              <TR>
                <TD>&nbsp;</TD><TD>&nbsp;</TD>
              </TR>
              <TR>
                <TD STYLE="text-align:right; font-weight:bold;">Measure data records:</TD>
                <TD>$recordCount</TD>
              </TR>
              <TR>
                <TD STYLE="text-align:right; font-weight:bold;">Measure data size:</TD>
                <TD>$factSize</TD>
              </TR>
              <TR>
                <TD>&nbsp;</TD><TD>&nbsp;</TD>
              </TR>
              <TR>
                <TD STYLE="text-align:right; font-weight:bold;">Reports:</TD>
                <TD>$rptCount</TD>
              </TR>
              <TR>
                <TD STYLE="text-align:right; font-weight:bold;">Reports size:</TD>
                <TD>$rptSize</TD>
              </TR>
END_HTML

  if ($ODBCexport > 0)
  {
    print <<END_HTML
              <TR>
                <TD>&nbsp;</TD><TD>&nbsp;</TD>
              </TR>
              <TR>
                <TD STYLE="text-align:right; font-weight:bold;">ODBC data size:</TD>
                <TD>$ODBCSize</TD>
              </TR>
END_HTML
  }

  print <<END_HTML;
              <TR>
                <TD>&nbsp;</TD><TD>&nbsp;</TD>
              </TR>
              <TR>
                <TD STYLE="text-align:right; font-weight:bold;">Last data update:</TD>
                <TD>$lastUpdate</TD>
              </TR>
              <TR>
                <TD STYLE="text-align:right; font-weight:bold;">Lifetime updates:</TD>
                <TD>$lifetimeUpdates</TD>
              </TR>
              <TR>
                <TD STYLE="text-align:right; font-weight:bold;">Average update frequency:</TD>
                <TD>$updateFreq per month</TD>
              </TR>
            </TABLE>
          </DIV>

          <P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" onClick="location.href='$refLink'"><I CLASS="bi bi-check-lg"></I> Done</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
<P>
END_HTML

  print_html_footer();

  $activity = "$first $last viewed statistics for $dsName";
  utils_slack($activity);


#EOF
