#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSel;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::Social;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Edit List</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<SCRIPT SRC="/jsgrid/jsgrid.min.js"></SCRIPT>
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/jsgrid/jsgrid.min.css" REL="stylesheet">
<LINK HREF="/jsgrid/jsgrid-theme.min.css" REL="stylesheet">
<LINK HREF="/fontawesome-5.10.2/css/all.min.css" REL="stylesheet">

<STYLE>
div.grid th
{
  background: #e2e3e5 !important;
}

.selected-row > td
{
  background: #c4e2ff !important;
  border-color: #c4e2ff;
}

.accordion-button.collapsed {
  background: blue
}

.accordion-button.collapsed::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}
</STYLE>

END_HTML

  if (!defined($listID))
  {
    $listID = $structID;
  }

  $dataBlock = datasel_get_script_json($db, $listID, "l", $dim, $dsID);

  $dimItems = datasel_expand_script($db, $dsSchema, $listID, $dim, "l", 1);
  $dimItemsCount = $dimItems =~ tr/,//;
  if ((length($dimItems) > 0) && ($dimItemsCount == 0))
  {
    $dimItemsCount = 1;
  }

  print <<END_HTML;
<SCRIPT>
let gridHeight = window.innerHeight - 400;
if (gridHeight < 200)
{
  gridHeight = 200;
}

let gridData = [ $dataBlock ];

\$(document).ready(function()
{
  \$('#listGrid').jsGrid(
  {
    width: '95%',
    height: gridHeight,
    sorting: true,
    autoload: true,
    loadIndication: true,
    multiselect: true,

    data: gridData,

    rowClick: function(args)
    {

      //Shift + selection
      if (args.event.shiftKey)
      {
        document.getSelection().removeAllRanges();

        let i = 0;
        let firstSelection = -1;
        while ((i < this.data.length) && (firstSelection < 0))
        {
          if (this.data[i].selected == 1)
          {
            firstSelection = i;
          }
          i++;
        }

        i = 0;
        let curSelection = -1;
        while ((i < this.data.length) && (curSelection < 0))
        {
          if (args.item.id == this.data[i].id)
          {
            curSelection = i;
          }
          i++;
        }

        clearAllSelections();

        let start, stop;
        if (curSelection > firstSelection)
        {
          start = firstSelection;
          end = curSelection;
        }
        else
        {
          end = firstSelection;
          start = curSelection;
        }

        for (i = start; i <= end; i++)
        {
          this.data[i].selected = 1;
          \$selectedRow = \$('#listGrid').jsGrid('rowByItem', this.data[i]).closest('tr');
          \$selectedRow.addClass('selected-row');
        }
      }

      //Ctrl+selection
      else if (event.ctrlKey || event.altKey || event.metaKey)
      {
        args.item.selected = 1;
        \$selectedRow = \$(args.event.target).closest('tr');
        \$selectedRow.addClass('selected-row');
      }

      //single selection
      else
      {
        clearAllSelections();
        args.item.selected = 1;
        \$selectedRow = \$(args.event.target).closest('tr');
        \$selectedRow.addClass('selected-row');
      }
    },

    rowDoubleClick: function(args)
    {
      item = args.item.id;

      location.href='/app/datasel/selectionMethod.cld?ds=$dsID&dim=$dim&st=l&sid=$listID&modItem=' + item;
    },

    fields: [
      {name: 'id', type: 'number', visible: false},
      {name: 'product', title: '$dimName ($dimItemsCount)', type: 'text', width: 500},
      {name: 'type', title: 'Type', type: 'text', width: 100}
    ]
  });


  //init selections, if needed
  let idx = $itemIndex;
  if (idx.length > 0)
  {
    let grid = \$('#listGrid').jsGrid('option', 'data');
    let i = 0;

    \$selectedRow = \$('#listGrid').jsGrid('rowByItem', grid[idx[0]]).closest('tr');
    \$selectedRow[0].scrollIntoView();

    while (i < idx.length)
    {
      grid[idx[i]].selected = 1;
      \$selectedRow = \$('#listGrid').jsGrid('rowByItem', grid[idx[i]]).closest('tr');
      \$selectedRow.addClass('selected-row');
      i++;
    }
  }
});



function clearAllSelections()
{
  let grid = \$('#listGrid').jsGrid('option', 'data');

  for (let i = 0; i < grid.length; i++)
  {
    grid[i].selected = 0;
  }

  \$('#listGrid tr').removeClass('selected-row');
}



function getSelectionStr()
{
  let grid = \$('#listGrid').jsGrid('option', 'data');
  let selStr = '';

  for (let i = 0; i < grid.length; i++)
  {
    if (grid[i].selected == 1)
    {
      selStr = selStr + grid[i].id + ',';
    }
  }

  return(selStr);
}



function getSelectionIdx()
{
  let grid = \$('#listGrid').jsGrid('option', 'data');
  let selStr = '';

  for (let i = 0; i < grid.length; i++)
  {
    if (grid[i].selected == 1)
    {
      selStr = selStr + i + ',';
    }
  }

  return(selStr);
}



function modifySelection(dim)
{
  let grid = \$('#listGrid').jsGrid('option', 'data');
  let item = null;

  for (let i = 0; i < grid.length; i++)
  {
    if ((grid[i].selected == 1) && (item == null))
    {
      item = grid[i].id;
    }
  }

  if (item == null)
  {
    return;
  }

  location.href='/app/datasel/selectionMethod.cld?ds=$dsID&dim=$dim&st=l&sid=$listID&modItem=' + item;
}



function removeSelection()
{
  let val = getSelectionStr();

  if (val.length == 0)
  {
    return;
  }

  location.href='?ds=$dsID&dim=$dim&l=$listID&action=r&item=' + val;
}



function topItem()
{
  let val = getSelectionStr();

  if (val.length == 0)
  {
    return;
  }

  location.href='?ds=$dsID&dim=$dim&l=$listID&action=t&item=' + val;
}



function upItem()
{
  let val = getSelectionStr();

  if (val.length == 0)
  {
    return;
  }

  let selIdx = getSelectionIdx();

  location.href='?ds=$dsID&dim=$dim&l=$listID&action=u&item=' + val + '&i=' + selIdx;
}



function downItem()
{
  let val = getSelectionStr();

  if (val.length == 0)
  {
    return;
  }

  let selIdx = getSelectionIdx();

  location.href='?ds=$dsID&dim=$dim&l=$listID&action=d&item=' + val + '&i=' + selIdx;
}



function bottomItem()
{
  let val = getSelectionStr();

  if (val.length == 0)
  {
    return;
  }

  location.href='?ds=$dsID&dim=$dim&l=$listID&action=b&item=' + val;
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Edit List $name</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');
  $dim = $q->param('dim');
  $listID = $q->param('l');
  $action = $q->param('action');
  $selection = $q->param('selection');
  $itemIndex = $q->param('i');
  $segmentID = $q->param('segment');
  @segmentIDs = $q->param('segment');
  $segmentationID = $q->param('segmentation');
  $segHierLevel = $q->param('segHierLevel');
  $name = $q->param('name');
  $selMethod = $q->param('method');
  $structID = $q->param('sid');
  $item = $q->param('item');
  $modifyItem = $q->param('modItem');

  $selOp = $q->param('selOp');

  $selType = $q->param('type');
  $selDuration = $q->param('duration');
  $selStarting = $q->param('starting');
  $selEnding = $q->param('ending');

  $matchType = $q->param('matchType');

  #if we're being called to create a list from an aggregate, this contains
  #the source aggregate's ID
  $aggID = $q->param('aggID');

  #build up our schema name
  $dsSchema = "datasource_$dsID";

  #if we got a fully-qualified list ID, strip it to the numerical ID
  if ($listID =~ m/LIS_(\d+)/)
  {
    $listID = $1;
  }

  #if we got an item index (used to maintain grid selection state), convert it
  #into a JS array
  if (length($itemIndex) > 0)
  {
    @tmp = split(',', $itemIndex);
    $itemIndex = "[";
    foreach $idx (@tmp)
    {
      if (($idx > 0) && ($action eq "u"))
      {
        $idx--;
      }
      elsif ($action eq "d")
      {
        $idx++;
      }
      $itemIndex .= "$idx,";
    }
    chop($itemIndex);
    $itemIndex .= "]";
  }
  else
  {
    $itemIndex = "[]";
  }

  $dbStub = KAPutil_get_dim_stub_name($dim);
  $dimName = KAPutil_get_dim_name($dim, 1);
  $dbName = $dbStub . "list";

  $db = KAPutil_connect_to_database();

  $activity = "";

  $dsName = ds_id_to_name($db, $dsID);

  if (length($name) < 1)
  {
    $query = "SELECT name FROM $dsSchema.$dbName WHERE ID=$listID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($name) = $dbOutput->fetchrow_array;
  }

  #make sure we have write privs for the data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    print_html_header();
    exit_error("You don't have privileges to edit lists in the data source.");
  }

  Social_clear_agg_items($db, $dsID, $name);

  #if we're being called to create a new list based on an aggregate
  if (defined($aggID))
  {

    #get the aggregate's additive definition from the DSR
    $aggDBName = $dbStub . "aggregate";
    $query = "SELECT name, addScript, addMembers FROM $dsSchema.$aggDBName \
        WHERE ID=$aggID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($aggName, $aggAddScript, $aggAddMembers) = $dbOutput->fetchrow_array;

    #insert the new list into the DSR
    $query = "INSERT INTO $dsSchema.$dbName (name, script, members) \
        VALUES ('$aggName', '$aggAddScript', '$aggAddMembers')";
    $db->do($query);

    #get the ID of our newly created list, then feed it to the rest of this
    #script for display purposes
    $structID = $db->{q{mysql_insertid}};
    $listID = $structID;
    $name = $aggName;

    $activity = "$first $last created a list from aggregate $aggName in $dsName";
    utils_audit($db, $userID, "Created list from aggregate $aggName", $dsID, 0, 0);
  }

  if (length($activity) < 1)
  {
    utils_audit($db, $userID, "Edited list $name", $dsID, 0, 0);
    $activity = "$first $last editing list $name in $dsName";
  }

  #if we're being called to edit a list, update the (possibly changed) name
  if ($action eq "e")
  {
    $q_name = $db->quote($name);
    $query = "UPDATE $dsSchema.$dbName SET name=$q_name WHERE ID=$listID";
    $db->do($query);
  }

  #if we're being called to sort base items in the list
  if ($action eq "sort")
  {
    %itemNameHash = dsr_get_item_name_hash($db, $dsSchema, $dim);

    #grab the script that generates the list
    $query = "SELECT script FROM $dsSchema.$dbName WHERE ID=$listID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($oldScript) = $dbOutput->fetchrow_array;

    #create a sortable hash of list item names indexed by their step
    @steps = split(',', $oldScript);
    foreach $step (@steps)
    {
      if ($step =~ m/^M:(\d+)$/)
      {
        $itemOrder{$step} = $itemNameHash{$1};
      }
    }

    #everything that isn't a base item goes to the top of the new sorted script
    foreach $step (@steps)
    {
      if (!($step =~ m/^M:\d+$/))
      {
        push(@newSteps, $step);
      }
    }

    #now, sort the base items and add them to the bottom of the list
    foreach $step (sort {$itemOrder{$a} cmp $itemOrder{$b}} keys %itemOrder)
    {
      push(@newSteps, $step);
    }

    $newLine = join(',', @newSteps);
    $q_newLine = $db->quote($newLine);

    #store the new script
    $query = "UPDATE $dsSchema.$dbName SET script=$q_newLine WHERE ID=$listID";
    $db->do($query);

    #re-expand the script into the list members
    datasel_expand_script($db, $dsSchema, $listID, $dim, "l");

    #update the data source's and this list's last modified time stamps
    $db->do("UPDATE dataSources SET lastModified=NOW() WHERE ID=$dsID");
    $db->do("UPDATE $dsSchema.$dbName SET lastUpdated=NOW() WHERE ID=$listID");
  }

  #if we're being called to remove an item from the list
  if ($action eq "r")
  {

    #grab the script that generates the list
    $query = "SELECT script FROM $dsSchema.$dbName WHERE ID=$listID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($oldScript) = $dbOutput->fetchrow_array;

    $newLine = datasel_delete_items($oldScript, $item);

    $q_newLine = $db->quote($newLine);

    #store the new script
    $query = "UPDATE $dsSchema.$dbName SET script=$q_newLine WHERE ID=$listID";
    $db->do($query);

    #re-expand the script into the list members
    datasel_expand_script($db, $dsSchema, $listID, $dim, "l");

    #update the data source's and this list's last modified time stamps
    $db->do("UPDATE dataSources SET lastModified=NOW() WHERE ID=$dsID");
    $db->do("UPDATE $dsSchema.$dbName SET lastUpdated=NOW() WHERE ID=$listID");
  }


  #--------------------------

  #if we're being called to move item(s) to the top of the list
  if ($action eq "t")
  {

    #grab the script that generates the list
    $query = "SELECT script FROM $dsSchema.$dbName WHERE ID=$listID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($oldScript) = $dbOutput->fetchrow_array;

    $newScript = datasel_move_items_top($oldScript, $item);

    $q_newScript = $db->quote($newScript);

    #store the new script
    $query = "UPDATE $dsSchema.$dbName SET script=$q_newScript WHERE ID=$listID";
    $db->do($query);

    #re-expand the script into the list members
    datasel_expand_script($db, $dsSchema, $listID, $dim, "l");
  }

  #if we're being called to move up an item's position in the list
  if ($action eq "u")
  {

    #grab the script that generates the list
    $query = "SELECT script FROM $dsSchema.$dbName WHERE ID=$listID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($oldScript) = $dbOutput->fetchrow_array;

    $newScript = datasel_move_items_up($oldScript, $item);

    $q_newScript = $db->quote($newScript);

    #store the new script
    $query = "UPDATE $dsSchema.$dbName SET script=$q_newScript WHERE ID=$listID";
    $db->do($query);

    #re-expand the script into the list members
    datasel_expand_script($db, $dsSchema, $listID, $dim, "l");
  }

  #if we're being called to move down an item's position in the list
  if ($action eq "d")
  {

    #grab the script that generates the list
    $query = "SELECT script FROM $dsSchema.$dbName WHERE ID=$listID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($oldScript) = $dbOutput->fetchrow_array;

    $newScript = datasel_move_items_down($oldScript, $item);

    $q_newScript = $db->quote($newScript);

    #store the new script
    $query = "UPDATE $dsSchema.$dbName SET script=$q_newScript WHERE ID=$listID";
    $db->do($query);

    #re-expand the script into the list members
    datasel_expand_script($db, $dsSchema, $listID, $dim, "l");
  }

  #if we're being called to move item(s) to the bottom of the list
  if ($action eq "b")
  {

    #grab the script that generates the list
    $query = "SELECT script FROM $dsSchema.$dbName WHERE ID=$listID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($oldScript) = $dbOutput->fetchrow_array;

    $newScript = datasel_move_items_bottom($oldScript, $item);

    $q_newScript = $db->quote($newScript);

    #store the new script
    $query = "UPDATE $dsSchema.$dbName SET script=$q_newScript WHERE ID=$listID";
    $db->do($query);

    #re-expand the script into the list members
    datasel_expand_script($db, $dsSchema, $listID, $dim, "l");
  }

  #if we're being called to create a new list or save selection changes to an
  #existing list
  if ($action eq "s")
  {

    #if there's an extra comma on the end of the selection string, chop it
    if ($selection =~ m/,$/)
    {
      chop($selection);
    }

    #if we're being called to create a new list, insert it into the appropriate
    #lists table
    if (length($structID) < 1)
    {
      $q_name = $db->quote($name);
      $query = "INSERT INTO $dsSchema.$dbName (name) VALUES ($q_name)";
      $db->do($query);

      #get the unique ID for this list
      $structID = $db->{q{mysql_insertid}};
      $listID = $structID;
    }

    $dataSelOpts{'db'} = $db;
    $dataSelOpts{'dim'} = $dim;
    $dataSelOpts{'dbName'} = "$dsSchema.$dbName";
    $dataSelOpts{'structType'} = "l";
    $dataSelOpts{'structID'} = $structID;
    $dataSelOpts{'selMethod'} = $selMethod;
    $dataSelOpts{'selection'} = $selection;
    $dataSelOpts{'modifyItem'} = $modifyItem;
    $dataSelOpts{'segHierLevel'} = $segHierLevel;
    $dataSelOpts{'segmentationID'} = $segmentationID;
    $dataSelOpts{'selOp'} = $selOp;
    $dataSelOpts{'selDuration'} = $selDuration;
    $dataSelOpts{'selType'} = $selType;
    $dataSelOpts{'selEnding'} = $selEnding;
    $dataSelOpts{'selStarting'} = $selStarting;
    $dataSelOpts{'matchType'} = $matchType;

    datasel_add_item(\%dataSelOpts, @segmentIDs);

    #expand the list out to its members, and store them
    datasel_expand_script($db, $dsSchema, $structID, $dim, "l");

    #update the data source's and this list's last modified time stamps
    $db->do("UPDATE dataSources SET lastModified=NOW() WHERE ID=$dsID");
    $db->do("UPDATE $dsSchema.$dbName SET lastUpdated=NOW() WHERE ID=$structID");
  }

  print_html_header();

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-lg-10 col-xl-10"> <!-- content -->

      <P>
      <DIV CLASS="accordion mx-auto" ID="accordion">
        <DIV CLASS="accordion-item border-primary">
          <H2 CLASS="accordion-header">
            <BUTTON CLASS="accordion-button bg-primary text-white icon-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
              Edit List
            </BUTTON>
          </H2>
          <DIV ID="collapse1" CLASS="accordion-collapse collapse show" data-bs-parent="#accordion">
            <DIV CLASS="accordion-body">

              <DIV CLASS="row g-0">

                <DIV CLASS="col-11 overflow-auto">
                  <DIV id="listGrid" CLASS="grid" STYLE="margin:auto; font-size:13px;"></DIV>
                </DIV>

                <DIV CLASS="col-1">
                  <DIV CLASS="position-relative top-50 start-50 translate-middle">
                    <BUTTON CLASS="btn btn-primary" onClick="topItem()" TITLE="Move selected items to the top of the list"><I CLASS="bi bi-chevron-bar-up"></I></BUTTON>
                    <P></P>
                    <BUTTON CLASS="btn btn-primary" onClick="upItem()" TITLE="Move the selected items up"><I CLASS="bi bi-chevron-up"></I></BUTTON>
                    <P></P>
                    <BUTTON CLASS="btn btn-primary" onClick="downItem()" TITLE="Move the selected items down"><I CLASS="bi bi-chevron-down"></I></BUTTON>
                    <P></P>
                    <BUTTON CLASS="btn btn-primary" onClick="bottomItem()" TITLE="Move selected items to the bottom of the list"><I CLASS="bi bi-chevron-bar-down"></I></BUTTON>
                  </DIV>
                </DIV>
              </DIV>

              <P>
              <DIV CLASS="text-center">
                <BUTTON CLASS="btn btn-primary mx-1" TYPE="button" onClick="location.href='/app/datasel/selectionMethod.cld?ds=$dsID&dim=$dim&st=l&sid=$listID'" TITLE="Add a data selection to this list"><I CLASS="bi bi-plus-lg"></I> Add</BUTTON>
                <BUTTON CLASS="btn btn-primary mx-1" TYPE="button" onClick="modifySelection()" TITLE="Edit the selected list member"><I CLASS="bi bi-pencil"></I> Modify</BUTTON>
                <BUTTON CLASS="btn btn-primary mx-1" TYPE="button" onClick="removeSelection()" TITLE="Delete the selected list members"><I CLASS="bi bi-trash"></I> Remove</BUTTON>
                <BUTTON CLASS="btn btn-primary mx-1" TYPE="button" onClick="location.href='?ds=$dsID&dim=$dim&l=$listID&action=sort'" TITLE="Sort base items in alphabetical order"><I CLASS="bi bi-sort-alpha-down"></I> Sort</BUTTON>
              </DIV>

            </DIV>
          </DIV>
        </DIV>

        <DIV CLASS="accordion-item border-primary">
          <H2 CLASS="accordion-header">
            <BUTTON CLASS="accordion-button collapsed bg-primary text-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
              Advanced Options
            </BUTTON>
          </H2>
          <DIV ID="collapse2" CLASS="accordion-collapse collapse" data-bs-parent="#accordion">
            <DIV CLASS="accordion-body">

              Create an aggregate based on this list:
              <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='aggEdit.cld?ds=$dsID&dim=$dim&listID=$listID'">Create Aggregate</BUTTON>

            </DIV>
          </DIV>
        </DIV>
      </DIV>

      <P>
      <DIV CLASS="text-center">
        <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='display.cld?ds=$dsID&dim=$dim'"><I CLASS="bi bi-check-lg"></I> Done</BUTTON>
      </DIV>
      <P>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

  utils_slack($activity);

#EOF
