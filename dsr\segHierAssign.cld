#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: $readableAction Segmentation Hierarchy</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function addItem()
{
  let i;

  let segObj = document.getElementById('segs');
  let hierObj = document.getElementById('hier');

  let isSelected = [];
  for (i = 0; i < segObj.options.length; i++)
  {
    isSelected[i] = segObj.options[i].selected;
    if (isSelected[i])
    {
      let itemID = segObj.options[i].value;
      let itemName = segObj.options[i].label;

      let opt = document.createElement('option');
      opt.text = itemName;
      opt.value = itemID;
      hierObj.add(opt);
    }
  }

  i = segObj.options.length;
  while (i--)
  {
    if (isSelected[i])
    {
      segObj.remove(i);
    }
  }
}



function sortSelect(selElem)
{
  let tmpAry = new Array();

  for (let i = 0; i<selElem.options.length; i++)
  {
    tmpAry[i] = new Array();
    tmpAry[i][0] = selElem.options[i].text;
    tmpAry[i][1] = selElem.options[i].value;
  }
  tmpAry.sort();
  while (selElem.options.length > 0)
  {
    selElem.options[0] = null;
  }
  for (let i = 0; i < tmpAry.length; i++)
  {
    let op = new Option(tmpAry[i][0], tmpAry[i][1]);
    selElem.options[i] = op;
  }
}



function removeItem()
{
  let i;

  let segObj = document.getElementById('segs');
  let hierObj = document.getElementById('hier');

  let isSelected = [];
  for (i = 0; i < hierObj.options.length; i++)
  {
    isSelected[i] = hierObj.options[i].selected;
    if (isSelected[i])
    {
      let itemID = hierObj.options[i].value;
      let itemName = hierObj.options[i].label;

      let opt = document.createElement('option');
      opt.text = itemName;
      opt.value = itemID;
      segObj.add(opt);
    }
  }

  i = hierObj.options.length;
  while (i--)
  {
    if (isSelected[i])
    {
      hierObj.remove(i);
    }
  }
  sortSelect(segObj);
}



function upItem()
{
  let i;
  let sel = document.getElementById('hier');
  let selOptions = sel.getElementsByTagName('option');

  for (i = 1; i < selOptions.length; i++)
  {
    let opt = selOptions[i];
    if (opt.selected)
    {
      sel.removeChild(opt);
      sel.insertBefore(opt, selOptions[i - 1]);
    }
  }
}



function downItem()
{
  let i;
  let sel = document.getElementById('hier');
  let selOptions = sel.getElementsByTagName('option');

  for (i = selOptions.length - 2; i >= 0; i--)
  {
    let opt = selOptions[i];
    if (opt.selected)
    {
      let nextOpt = selOptions[i + 1];
      opt = sel.removeChild(opt);
      nextOpt = sel.replaceChild(opt, nextOpt);
      sel.insertBefore(nextOpt, opt);
    }
  }
}



function submitForm()
{
  let i;
  let form = document.getElementById('segHierForm');
  let sel = document.getElementById('hier');
  let hiddenField = document.createElement('input');

  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Please Wait...');

  let val = '';
  for (i = 0; i < sel.options.length; i++)
  {
    val = val + sel.options[i].value + ',';
  }

  hiddenField.setAttribute('type', 'hidden');
  hiddenField.setAttribute('name', 'hierarchy');
  hiddenField.setAttribute('value', val);
  form.appendChild(hiddenField);

  form.submit();
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">$readableAction Segmentation Hierarchy $segHierName</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');
  $dim = $q->param('dim');
  $segHierName = $q->param('segHierName');
  $segHierID = $q->param('segHier');

  if (defined($segHierID))
  {
    $readableAction = "Edit";
  }
  else
  {
    $readableAction = "New";
  }

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  #make sure we have write privs for the data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to modify this data source.");
  }

  $dsSchema = "datasource_" . $dsID;

  #get our dimension name for display purposes/db table name for SELECT
  $dimName = KAPutil_get_dim_name_singular($dim, 1);
  $dimDB = KAPutil_get_dim_stub_name($dim);

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col"> <!-- content -->

      <FORM METHOD="post" ID="segHierForm" ACTION="/app/dsr/segHierNamePattern.cld">
      <INPUT TYPE="hidden" NAME="dsID" VALUE="$dsID">
      <INPUT TYPE="hidden" NAME="dim" VALUE="$dim">
      <INPUT TYPE="hidden" NAME="segHierName" VALUE="$segHierName">
      <INPUT TYPE="hidden" NAME="segHier" VALUE="$segHierID">

      <DIV CLASS="card border-primary" STYLE="width:800px; margin:auto;">
        <DIV CLASS="card-header bg-primary text-white">$readableAction $dimName Segmentation Hierarchy</DIV>
        <DIV CLASS="card-body">

          <TABLE>
            <TR>
              <TD>
                <B>Segmentations</B><BR>
                <SELECT CLASS="form-select" ID="segs" NAME="segs" STYLE="width:300px; overflow-y:auto;" SIZE="15" onDblClick="addItem()">
END_HTML

  #retrieve list of available segmentations for this dimension
  %segmentations = DSRsegmentation_get_segmentations_hash($db, $dsSchema, $dim);

  #if we're editing an existing hierarchy, get in-order array of segmentations
  undef(@segHierIDs);
  undef(%segHierIDHash);
  if ($segHierID > 0)
  {
    $dbName = $dimDB . "seghierarchy";
    $query = "SELECT segmentations FROM $dsSchema.$dbName WHERE ID=$segHierID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    ($segStr) = $dbOutput->fetchrow_array;
    @segHierIDs = split(',', $segStr);
    foreach $id (@segHierIDs)
    {
      $segHierIDHash{$id} = 1;
    }
  }

  foreach $id (sort {$segmentations{$a} cmp $segmentations{$b}} keys %segmentations)
  {
    if ($segHierIDHash{$id} != 1)
    {
      print("<OPTION VALUE=\"$id\">$segmentations{$id}</OPTION>\n");
    }
  }

  print <<END_HTML;
                </SELECT>
              </TD>
              <TD>&nbsp;&nbsp;&nbsp;</TD>
              <TD STYLE="vertical-align: center;">
                <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="addItem();" TITLE="Add the selected segmentation to the hierarchy"><I CLASS="bi bi-chevron-right" STYLE="font-size:48px;"></I></BUTTON>
                <P>&nbsp;</P>
                <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="removeItem();" TITLE="Remove the selected segmentation from the hierarchy"><I CLASS="bi bi-chevron-left" STYLE="font-size:48px;"></I></BUTTON>
              </TD>
              <TD>&nbsp;&nbsp;&nbsp;</TD>
              <TD>
                <B>Hierarchy</B><BR>
                <SELECT CLASS="form-select" ID="hier" name="hier" STYLE="width:300px; overflow-y:auto;" SIZE="15" onDblClick="removeItem()">
END_HTML

  foreach $id (@segHierIDs)
  {
    print("<OPTION VALUE=\"$id\">$segmentations{$id}</OPTION>\n");
  }

  print <<END_HTML;
                </SELECT>
              </TD>
              <TD>&nbsp</TD>
              <TD STYLE="vertical-align: center;">
                <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="upItem();" TITLE="Move the selected segmentation higher in the hierarchy"><I CLASS="bi bi-chevron-up"></I></BUTTON>
                <P></P>
                <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="downItem();" TITLE="Move the selected segmentation lower in the hierarchy"><I CLASS="bi bi-chevron-down"></I></BUTTON>
              </TD>
            </TR>
          </TABLE>

          <P>&nbsp;</P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="location.href='display.cld?ds=$dsID&dim=$dim'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="button" ID="btn-submit" onClick="submitForm()">Next <I CLASS="bi bi-arrow-right"></I></BUTTON>
          </DIV>

        </DIV>
      </DIV>
      </FORM>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
