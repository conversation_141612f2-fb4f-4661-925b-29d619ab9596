#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  $cubeID = $q->param('c');
  $operation = $q->param('o');

  if (length($operation) < 1)
  {
    $operation = "CUBE-UPDATE";
  }

  print("Content-type: text/plain\n\n");

  $db = KAPutil_connect_to_database();

  #get the data source's current status
  $query = "SELECT status FROM app.jobs \
      WHERE cubeID=$cubeID AND operation='$operation'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($jobStatus) = $dbOutput->fetchrow_array;

  if ($status < 1)
  {
    $jobStatus = "DONE";
  }

  print("$jobStatus\n");


#EOF
