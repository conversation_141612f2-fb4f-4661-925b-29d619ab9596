#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DSRUtils;
use Lib::Reports;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $rptID = $q->param('rptID');
  $visID = $q->param('e');
  $width = $q->param('w');
  $height = $q->param('h');
  $xPct = $q->param('x');
  $yPct = $q->param('y');
  $zindex = $q->param('z');

  $db = KAPutil_connect_to_database();

  #get the element's current design string
  $query = "SELECT design FROM app.visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute();
  KAPutil_handle_db_err($db, $status, $query);
  ($design) = $dbOutput->fetchrow_array;

  #if we're saving sizing information
  if (defined($width))
  {
    $width = sprintf("%.2f", $width);
    $height = sprintf("%.2f", $height);

    $design = reports_set_style($design, "width", $width);
    $design = reports_set_style($design, "height", $height);
  }

  #if we're saving position information
  if (defined($xPct))
  {
    $xPct = sprintf("%.2f", $xPct);
    $yPct = sprintf("%.2f", $yPct);

    $design = reports_set_style($design, "xpct", $xPct);
    $design = reports_set_style($design, "ypct", $yPct);
  }

  #if we're saving zindex information
  if (defined($zindex))
  {
    $design = reports_set_style($design, "zindex", $zindex);
  }

  $q_design = $db->quote($design);

  $query = "UPDATE app.visuals SET design = $q_design WHERE ID=$visID";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);


#EOF
