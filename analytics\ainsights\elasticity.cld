#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::DataSources;
use Lib::AInsights::AInsights;
use Lib::AInsights::Utils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#format currency for pretty HTML output
sub html_format_currency
{
  ($value) = @_;


  #go down to 2 decimal places
  $value = sprintf("%.2f", $value);

  if ($value < 0)
  {
    $value = abs($value);
    $formatStr = "<SPAN STYLE='color:red;'>(\$$value)</SPAN>";
  }
  else
  {
    $formatStr = "\$$value";
  }

  return($formatStr);
}



#-------------------------------------------------------------------------

#format number for pretty HTML output
sub html_format_number
{
  ($value, $decimals, $redNegative) = @_;


  if (!defined($value))
  {
    return;
  }

  #go down to 2 decimal places
  $formatStr = "%." . $decimals . "f";
  $value = sprintf($formatStr, $value);

  if (($redNegative == 1) && ($value < 0))
  {
    $value = abs($value);
    $formatStr = "<SPAN STYLE='color:red;'>($value)</SPAN>";
  }
  else
  {
    $formatStr = "$value";
  }

  return($formatStr);
}



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: AInsights Elasticities</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/fontawesome-5.10.2/css/all.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-select-1.14.0-b2/css/bootstrap-select.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-select-1.14.0-b2/js/bootstrap-select.min.js"></SCRIPT>

<SCRIPT>
function showElasticityDlg(request)
{
  let url = request + '?pm=$priceModelID&ds=$dsID';
  \$('#wait-spinner').css('display', 'block');
  \$('#modal-elasticity').load(url, function (response, status, xhr)
  {
    if (status == 'success')
    {
      let myModal = new bootstrap.Modal(document.getElementById('modal-elasticity'));
      myModal.show();

      \$('#wait-spinner').css('display', 'none');
    }
  });
}
</SCRIPT>

<STYLE>
th {position: sticky; top:0; background: white;}
</STYLE>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-0">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">AInsights</A></LI>
    <LI CLASS="breadcrumb-item active"><A CLASS="text-decoration-none" HREF="insights.cld?pm=$priceModelID">$modelName</A></LI>
  </OL>
</NAV>

<DIV ID="wait-spinner" STYLE="display:none; z-index:1001; width:100%; height:100%; position:absolute; top:0; left:0; background-color: rgba(0,0,0,0.15);">
 <DIV STYLE="position:absolute; top:50%; left:50%;">
  <I CLASS="fas fa-circle-notch fa-spin" STYLE="font-size:100px; color:blue;"></I>
 </DIV>
</DIV>

<DIV ID="modal-elasticity" CLASS="modal">
</DIV>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');
  $priceModelID = $q->param('pm');
  $displayMode = $q->param('dm');

  AInsights_Utils_initialize_constants($priceModelID);

  #if we weren't passed a display mode on the command line, check session info
  if (!defined($displayMode))
  {
    $displayMode = $session->param(elasticity_displayMode);
  }

  #if there isn't one in the cookie, either, the default is to show the PPGs
  if (!defined($displayMode))
  {
    $displayMode = 'ppg';
  }
  $session->param(elasticity_displayMode, $displayMode);

  #connect to the database
  $db = KAPutil_connect_to_database();

  if ($dsID < 1)
  {
    $query = "SELECT dsID FROM analytics.pricing WHERE ID=$priceModelID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($dsID) = $dbOutput->fetchrow_array;
  }

  $dsSchema = "datasource_" . $dsID;

  $modelName = AInsights_ID_to_name($db, $priceModelID);
  $dsName = ds_id_to_name($db, $dsID);

  #if the model is currently being rebuilt
  $query = "SELECT PID FROM app.jobs \
      WHERE analyticsID=$priceModelID AND operation='ANALYTICS-PRICE'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($pid) = $dbOutput->fetchrow_array;
  if ($pid > 0)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: /app/analytics/pricing/modelRefresh.cld?ds=$dsID&pm=$priceModelID\n\n");
    exit;
  }

  %prodNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "p");
  %geoNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "g");

  #grab info about the analysis we're going to need for display
  $query = "SELECT name, ppgHierID, geographies FROM analytics.pricing \
      WHERE ID=$priceModelID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($modelName, $ppgHierID, $geoIDStr) = $dbOutput->fetchrow_array;

  @geographies = split(',', $geoIDStr);

  #grab the list of products in the model
  if ($displayMode eq "ppg")
  {
    $query = "SELECT DISTINCT ppgID, name FROM $dsSchema.$AinsightsPPGTable \
        ORDER BY name";
  }
  else
  {

    #get an alpha-sorted list of products in the data source
    $query = "SELECT ID FROM $dsSchema.products ORDER BY name";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($prodID) = $dbOutput->fetchrow_array)
    {
      $prodIDStr .= "$prodID,";
    }
    chop($prodIDStr);

    $query = "SELECT DISTINCT productID, name FROM $dsSchema.$AInsightsItemTable \
        WHERE productID NOT LIKE 'SHS_%' ORDER BY FIELD (productID, $prodIDStr)";
  }

  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  while (($prodID, $name) = $dbOutput->fetchrow_array)
  {
    push(@products, $prodID);

    if (length($name) > 0)
    {
      $ppgNameHash{$prodID} = $name;
    }
  }

  print_html_header();

  #make sure we have at least read privs for this model
  $privs = AInsights_rights($db, $userID, $priceModelID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to view this pricing model.");
  }

  print <<END_HTML;
<NAV CLASS="navbar navbar-expand-md navbar-light bg-light border">

  <BUTTON CLASS="navbar-toggler" TYPE="button" data-bs-toggle="collapse" data-bs-target="#navbarNavDropdown">
    <SPAN CLASS="navbar-toggler-icon"></SPAN>
  </BUTTON>

  <DIV CLASS="collapse navbar-collapse justify-content-center" ID="navbarNavDropdown">
    <UL CLASS="navbar-nav">
END_HTML

  if ($displayMode eq 'ppg')
  {
    print <<END_HTML;
      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="elasticity.cld?dm=sku&ds=$dsID&pm=$priceModelID" TITLE="Show elasticities for individual products"><I CLASS="bi bi-upc"></I> Show Items</A></LI>
END_HTML
  }
  else
  {
    print <<END_HTML;
      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="elasticity.cld?dm=ppg&ds=$dsID&pm=$priceModelID" TITLE="Show elasticities for product promotion groups"><I CLASS="bi bi-cart4"></I> Show Product Groups</A></LI>

      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onclick="showElasticityDlg('xhrFilter.cld')" TITLE="Filter the displayed values"><I CLASS="bi bi-funnel"></I> Filter</A></LI>

      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onclick="showElasticityDlg('xhrHighlight.cld')" TITLE="Highlight specific products based on their segmentation"><I CLASS="bi bi-pen"></I> Highlight</A></LI>
END_HTML
  }

  print <<END_HTML;
      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="exportExcel.cld?pm=$priceModelID&ds=$dsID" TITLE="Download an Excel spreadsheet of elasticities"><I CLASS="bi bi-cloud-download"></I> Download</A></LI>

      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="exportDS.cld?pm=$priceModelID&ds=$dsID" TITLE="Export elasticity values to the data source"><I CLASS="bi bi-cloud-upload"></I> <SPAN STYLE="white-space:nowrap;">Push to Data Source</SPAN></A></LI>

    </UL>
  </DIV>
</NAV>

<DIV CLASS="container-fluid">
  <DIV CLASS="row">
    <DIV CLASS="col">
      <P>
        <TABLE CLASS="table table-sm table-bordered" STYLE="font-size:14px;">
          <THEAD><TR>
          <TH>
            <TABLE CLASS="table table-sm table-borderless text-white">
              <TR>
                <TD STYLE="background:#00AEEF; font-weight:normal; color:white;" NOWRAP>
                  <SPAN data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                    data-bs-content="You have a lot of pricing power, but be cautious of increasing prices too much.">
                    Inelastic
                  </SPAN>
                </TD>
                <TD STYLE="background:#8DC63F; font-weight:normal; color:white;" NOWRAP>
                  <SPAN data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                    data-bs-content="You have some pricing power, but be sure to consider your competitive situation and external factors.">
                    Moderately Inelastic
                  </SPAN>
                </TD>
              </TR>
              <TR>
                <TD STYLE="background:#FFB100; font-weight:normal; color:white" NOWRAP>
                  <SPAN data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                    data-bs-content="Increasing prices tends to increase profits, but at the expense of a noticeable amount of volume.">
                    Moderately Elastic
                  </SPAN>
                </TD>
                <TD STYLE="background:#B21DAC; font-weight:normal; color:white;" NOWRAP>
                  <SPAN data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                    data-bs-content="Be very careful about increasing prices, since they'll lead to significant volume loss.">
                    Elastic
                  </SPAN>
                </TD>
              </TR>
              <TR>
                <TD STYLE="background:#DC0015; font-weight:normal; color:white;" NOWRAP>
                  <SPAN data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                    data-bs-content="Any price increase is going to lead to substantial volume loss - a price decrease might be an option.">
                    Very Elastic
                  </SPAN>
                </TD>
                <TD>
                </TD>
              </TR>

            </TABLE>
          </TH>
END_HTML

  #output geographies in the elasticity study as column headers
  foreach $geoID (@geographies)
  {
    print("<TH CLASS='bg-white'>$geoNameHash{$geoID}</TH>\n");
  }

  print("</TR></THEAD>");

  ########### FILTERING ################

  #get row-level filtering settings
  $query = "SELECT filterNA, filterConfidence, filter FROM analytics.pricing \
      WHERE ID=$priceModelID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($filterNA, $filterConfidence, $filterStr) = $dbOutput->fetchrow_array;

  #XXX kludge to keep item filtering from messing up PPG display
  if ($displayMode eq "ppg")
  {
    undef($filterStr);
    undef($segmentFilter);
  }

  #set up a hash of product IDs that don't have any calculateable elasticities
  if ($filterNA == 1)
  {
    if ($displayMode eq "ppg")
    {
      $query = "SELECT DISTINCT ppgID FROM $dsSchema.$AinsightsPPGTable \
          WHERE elasticity IS NOT NULL";
    }
    else
    {
      $query = "SELECT DISTINCT productID FROM $dsSchema.$AInsightsItemTable \
          WHERE elasticity IS NOT NULL AND modelConfidence >= $filterConfidence";
    }
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($productID) = $dbOutput->fetchrow_array)
    {
      $prodNotNAHash{$productID} = 1;
    }
  }

  #set up a hash of product IDs that belong to the user's requested segment
  #filters
  if ($filterStr =~ m/(.*?)\:(.*)/)
  {
    $segmentIDstr = $2;
    $segmentFilter = 1;

    #get a list of all items belonging to the selected segments
    $query = "SELECT itemID FROM $dsSchema.product_segment_item \
        WHERE segmentID IN ($segmentIDstr)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($productID) = $dbOutput->fetchrow_array)
    {
      $prodSegmentFilterHash{$productID} = 1;
    }
  }

  ########## END FILTERING ###################



  ########## HIGHLIGHTING ####################

  #get row-level highlighting settings
  $query = "SELECT highlight FROM analytics.pricing WHERE ID=$priceModelID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($highlight) = $dbOutput->fetchrow_array;

  #set up a hash of product IDs to be highlighted
  if ($highlight =~ m/(.*?)\:(.*)/)
  {
    $segmentIDstr = $2;

    #get a list of all items belonging to the selected segments
    $query = "SELECT itemID FROM $dsSchema.product_segment_item \
        WHERE segmentID IN ($segmentIDstr)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($productID) = $dbOutput->fetchrow_array)
    {
      $prodSegmentHighlightHash{$productID} = 1;
    }
  }

  ########## END HIGHLIGHTING ###############


  #output elasticity for each product/geo combination
  foreach $prodID (@products)
  {

    #don't show products that have no elasticities if the user doesn't want them
    if (($filterNA == 1) && ($prodNotNAHash{$prodID} != 1))
    {
      next;
    }

    #don't show products that don't match the user's segment filter, if present
    if ((defined($segmentFilter)) && ($prodSegmentFilterHash{$prodID} != 1))
    {
      next;
    }

    #handle row-level highlighting
    if ($prodSegmentHighlightHash{$prodID} == 1)
    {
      $prodCellClass = "table-info";
    }
    else
    {
      $prodCellClass = "";
    }

    if ($prodID =~ m/\_/)
    {
      $prodName = $ppgNameHash{$prodID};
    }
    else
    {
      $prodName = $prodNameHash{$prodID};
    }
    print("<TR>\n");
    print("<TD CLASS='$prodCellClass' NOWRAP>$prodName</TD>\n");

    if ($displayMode eq "ppg")
    {
      $query = "SELECT elasticity, 3, geographyID, NULL \
          FROM $dsSchema.$AinsightsPPGTable WHERE ppgID='$prodID' \
          ORDER BY FIELD(geographyID, $geoIDStr)";
    }
    else
    {
      $query = "SELECT elasticity, modelConfidence, geographyID, status \
          FROM $dsSchema.$AInsightsItemTable WHERE productID='$prodID' \
          ORDER BY FIELD(geographyID, $geoIDStr)";
    }
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($elasticity, $modelConfidence, $geoID, $statusText) = $dbOutput->fetchrow_array)
    {

      #don't show elasticities that have a lower confidence than the user trusts
      if ($modelConfidence < $filterConfidence)
      {
        undef($elasticity);
      }

      #decide how to color background of cell
      $tableColorCode = "";
      $linkColorClass = "";
      if (($elasticity <= 0) && ($elasticity >= -0.75))
      {
        $linkColorClass = "text-white";
        $tableColorCode = "background-color:#00AEEF;";
      }
      elsif (($elasticity <= -0.75) && ($elasticity >= -1.25))
      {
        $linkColorClass = "text-white";
        $tableColorCode = "background-color:#8DC63F;";
      }
      elsif (($elasticity <= -1.25) && ($elasticity >= -1.75))
      {
        $linkColorClass = "text-white";
        $tableColorCode = "background-color:#FFB100;";
      }
      elsif (($elasticity <= -1.75) && ($elasticity >= -2.25))
      {
        $linkColorClass = "text-white";
        $tableColorCode = "background-color:#B21DAC;";
      }
      elsif ($elasticity <= -2.25)
      {
        $linkColorClass = "text-white";
        $tableColorCode = "background-color:#DC0015;";
      }

      $elasticity = html_format_number($elasticity, 2);

      #set our display function for SKU or PPG
      if ($prodID =~ m/^SHS_/)
      {
        $displayDetails = "elasticityPPG.cld";
      }
      else
      {
        $displayDetails = "elasticityItem.cld";
      }

      #display NA for fields where there wasn't any sales data
      if ($statusText eq "No sales")
      {
        print("<TD></TD>\n");
      }
      elsif ($statusText =~ m/^Not enough data points/)
      {
        print("<TD></TD>\n");
      }
      elsif (!defined($elasticity))
      {
        print("<TD></TD>\n");
      }
      elsif ($statusText =~ m/^Invalid model/)
      {
        print("<TD TITLE='No valid model'><A CLASS='text-decoration-none' HREF='$displayDetails?ds=$dsID&pm=$priceModelID&p=$prodID&g=$geoID'>NA</A></TD>\n");
      }
      elsif ($statusText =~ m/^No relationship between/)
      {
        print("<TD TITLE='$statusText'><A CLASS='text-decoration-none' HREF='$displayDetails?ds=$dsID&pm=$priceModelID&p=$prodID&g=$geoID'>NA</A></TD>\n");
      }
      else
      {
        print("<TD STYLE='$tableColorCode' CLASS='text-right'><A CLASS='$linkColorClass text-decoration-none' HREF='$displayDetails?ds=$dsID&pm=$priceModelID&p=$prodID&g=$geoID'>$elasticity</A></TD>\n");
      }
    }

    print("</TR>\n");
  }

  print <<END_HTML;

        </TABLE>

    </DIV>
  </DIV>

</DIV>

<SCRIPT>
\$(function()
{
  \$('[data-bs-toggle="popover"]').popover()
})
</SCRIPT>

END_HTML

  print_html_footer();

  #flush the CGI session info out to storage
  $session->flush();

  AInsights_audit($db, $userID, $priceModelID, "Viewed elasticities");
  $activity = "PRICING: $first $last viewed elasticities in model $modelName in $dsName";
  utils_slack($activity);

#EOF
