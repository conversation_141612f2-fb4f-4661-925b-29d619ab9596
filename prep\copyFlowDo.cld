#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($kapDB);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Copy Data Flow</TITLE>
<LINK REL="SHORTCUT ICON" HREF="$Lib::KoalaConfig::kapHostURL/favicon.png">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($kapDB, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="$Lib::KoalaConfig::kapHostURL/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none"  HREF="main.cld?f=$flowID">Data Prep</A></LI>
    <LI CLASS="breadcrumb-item">$flowName</LI>
    <LI CLASS="breadcrumb-item active">Copy Data Flow</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser and get the forecast to be deleted
  $q = new CGI;
  $flowID = $q->param('f');
  $name = $q->param('name');
  $copyRecipe = $q->param('copyRecipe');

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #connect to the database
  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #get the name of the data source
  $flowName = prep_flow_id_to_name($prepDB, $flowID);

  print_html_header();

  #make sure we have read privs for this data source
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to copy this data flow.");
  }

  #start by grabbing all of the source flow's primary info
  $query = "SELECT description, source, sourceInfo, parseOptions, appendUPC, compressWS, dontOverwriteNames \
      FROM prep.flows WHERE ID=$flowID";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($description, $source, $sourceInfo, $parseOptions, $appendUPC, $compressWS, $dontOverwriteNames) = $dbOutput->fetchrow_array;

  if (!defined($appendUPC))
  {
    $appendUPC = 0;
  }
  if (!defined($compressWS))
  {
    $compressWS = 0;
  }
  if (!defined($dontOverwriteNames))
  {
    $dontOverwriteNames = 0;
  }

  #insert the entry for the copy of the flow
  $q_name = $prepDB->quote($name);
  $q_description = $prepDB->quote($description);
  $q_sourceInfo = $prepDB->quote($sourceInfo);
  $q_parseOptions = $prepDB->quote($parseOptions);
  $query = "INSERT INTO prep.flows \
      (userID, name, description, source, sourceInfo, parseOptions, appendUPC, compressWS, dontOverwriteNames) \
      VALUES ($userID, $q_name, $q_description, '$source', $q_sourceInfo, $q_parseOptions, $appendUPC, $compressWS, $dontOverwriteNames)";
  $prepDB->do($query);
  $newFlowID = $prepDB->{q{mysql_insertid}};

  #re-create the file type hints entries for the new flow
  $query = "SELECT name, tabName, type FROM prep.file_types WHERE flowID=$flowID";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;

  while (($fileName, $tabName, $type) = $dbOutput->fetchrow_array)
  {
    $q_fileName = $prepDB->quote($fileName);
    $q_tabName = $prepDB->quote($tabName);
    $query = "INSERT INTO prep.file_types (flowID, name, tabName, type) \
        VALUES ($newFlowID, $q_fileName, $q_tabName, '$type')";
    $prepDB->do($query);
  }

  #if the user requested it, also copy the recipe steps
  if ($copyRecipe eq "on")
  {
    $query = "SELECT step, action FROM prep.recipes WHERE flowID=$flowID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;

    while (($step, $action) = $dbOutput->fetchrow_array)
    {
      $q_action = $prepDB->quote($action);
      $query = "INSERT INTO prep.recipes (flowID, step, action) VALUES ($newFlowID, $step, $q_action)";
      $prepDB->do($query);
    }

    #copy any trim edit fields (lets user immediately adjust geos and so on)
    $query = "SELECT name, `values` FROM prep.trim_values WHERE flowID=$flowID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    while (($colName, $values) = $dbOutput->fetchrow_array)
    {
      $q_col = $prepDB->quote($colName);
      $q_values = $prepDB->quote($values);
      $query = "INSERT INTO prep.trim_values (flowID, name, `values`) VALUES ($newFlowID, $q_col, $q_values)";
      $prepDB->do($query);
    }
  }

  prep_audit($prepDB, $userID, "Copied data flow to $name", $flowID);
  prep_audit($prepDB, $userID, "Created this data flow by copying $flowName", $newFlowID);

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-10 col-lg-7 col-xl-6"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Copy Data Flow</DIV>
        <DIV CLASS="card-body">

        <P>
        The data flow $flowName has been copied to $name.

        <P>&nbsp;</P>
        <DIV CLASS="text-center">
          <BUTTON CLASS="btn btn-primary" onClick="location.href='main.cld?f=$flowID'"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
        </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

  utils_slack("PREP: $first $last copied data flow $flowName to $name");

#EOF
