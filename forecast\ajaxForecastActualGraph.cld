#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;


#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/plain\n\n");

  #get the CGI input variables
  $fcID = $q->param('fcID');
  $product = $q->param('prod');
  $geography = $q->param('geo');

  $db = KAPutil_connect_to_database();

  #get the list of forecast time periods from the database
  $query = "SELECT dsID, measureID, timeperiods, futureperiods \
      FROM analytics.forecasts WHERE ID=$fcID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;

  ($dsID, $measureID, $timeIDstring, $futureperiods) = $dbOutput->fetchrow_array;

  #assemble forecast cube names
  $dsSchema = "datasource_" . $dsID;
  $fcCube = "_fcastcube_" . $fcID;
  $fcMeta = "_fcast_" . $fcID;

  #convert the time ID strings into arrays
  @timeIDs = split(/,/, $timeIDstring);

  #get displayable names for our DSR dimensions
  $measureName = KAPutil_get_item_ID_name($db, $dsSchema, "m", $measureID);
  %timeperiodNames = dsr_get_item_name_hash($db, $dsSchema, "t");

  #trim down the time names to maximize the graph area
  while(($key, $value) = each(%timeperiodNames))
  {
    if ($value =~ m/(.*?) Weeks Ending (.*)/i)
    {
      $timeperiodNames{$key} = "$1 WE $2";
    }
  }

  #get the "details" string from the forecast's meta cube
  $query = "SELECT details FROM $dsSchema.$fcMeta \
      WHERE geography=$geography AND product=$product";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ($details) = $dbOutput->fetchrow_array;

  #determine the periodicity of the forecast (we need this to calc future
  #time periods)
  $details =~ m/period=(.*?),/;
  $periodType = $1;

  $lastTime = $timeIDs[-1];
  $query = "SELECT endDate from $dsSchema.timeperiods WHERE ID=$lastTime";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ($endDate) = $dbOutput->fetchrow_array;

  if (scalar(@timeIDs) > 52)
  {
    $labelStep = 8
  }
  elsif (scalar(@timeIDs) > 12)
  {
    $labelStep = 4;
  }
  else
  {
    $labelStep = 1;
  }

  #create chart cosmetics/definition JSON
  $jsonData = <<JSON_LABEL;
{
  "chart":
  {
    "theme": "zune",
    "animation": "0",
    "caption": "Forecast vs Actual",
    "captionfontcolor": "#333333",
    "captionfontsize": 24,
    "yaxisname": "$measureName",
    "yaxisnamefontsize": "14",
    "numvdivlines": "10",
    "divlinealpha": "30",
    "labelpadding": "10",
    "labelstep": "$labelStep",
    "labelfontsize": "14",
    "labeldisplay": "rotate",
    "slantlabel": "1",
    "yaxisvaluespadding": "10",
    "legendItemFontSize": "14",
    "showvalues": "0"
  },
JSON_LABEL

  #output categories (X axis values)
  $jsonData .= <<JSON_LABEL;
  "categories": [
  {
    "category": [
JSON_LABEL

  foreach $time (@timeIDs)
  {
    $jsonData .= <<JSON_LABEL;
    {
      "label": "$timeperiodNames{$time}"
    },
JSON_LABEL
  }

  $index = 1;
  while ($index <= $futureperiods)
  {
    if ($periodType eq "Yearly")
    {
      $query = "SELECT ";
    }
    elsif ($periodType eq "Quarterly")
    {
    }
    elsif ($periodType eq "Monthly")
    {
      $query = "SELECT DATE(DATE_ADD('$endDate', INTERVAL 1 MONTH)) \
          FROM $dsSchema.timeperiods";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      ($lDate) = $dbOutput->fetchrow_array;

      $label = "Month Ending $endDate";

    }
    elsif ($periodType eq "Weekly")
    {
    }
    else
    {
      $label = "Future Period $index";
    }

    $jsonData .= <<JSON_LABEL;
    {
      "label": "$label"
    },
JSON_LABEL

    $index++;
  }

  chop($jsonData);  chop($jsonData);

  $jsonData .= <<JSON_LABEL;
      ]
    }
  ],
JSON_LABEL

  #output the actual values data set
  $jsonData .= <<JSON_LABEL;
  "dataset": [
  {
    "seriesname": "Actual",
    "color": "A66EDD",
    "data": [
JSON_LABEL

  foreach $time (@timeIDs)
  {
    $query = "SELECT measure FROM $dsSchema.$fcCube \
        WHERE time=$time AND geography=$geography AND product=$product";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    ($value) = $dbOutput->fetchrow_array;
    $jsonData .= "{ \"value\": \"$value\"},\n";
  }
  chop($jsonData);  chop($jsonData);

  $jsonData .= <<JSON_LABEL;
    ]
JSON_LABEL


  #output the forecast values data set
  $jsonData .= <<JSON_LABEL;
  },
  {
    "seriesname": "Forecast",
    "color": "F6BD0F",
    "data": [
JSON_LABEL

  foreach $time (@timeIDs)
  {
    $query = "SELECT forecast FROM $dsSchema.$fcCube \
        WHERE time=$time AND geography=$geography AND product=$product";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    ($forecast) = $dbOutput->fetchrow_array;
    $jsonData .= "{ \"value\": \"$forecast\"},\n";
  }

  #now append on any forecasted values
  $query = "SELECT forecast FROM $dsSchema.$fcCube \
      WHERE geography=$geography AND product=$product AND source='forecast' \
      ORDER BY endDate";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;

  #output the forecasts for future time periods
  while (($forecast) = $dbOutput->fetchrow_array)
  {
    $jsonData .= "{ \"value\": \"$forecast\"},\n";
  }
  chop($jsonData);  chop($jsonData);

  $jsonData .= <<JSON_LABEL;
    ]
  }]
}
JSON_LABEL

  print "$jsonData";


#EOF
