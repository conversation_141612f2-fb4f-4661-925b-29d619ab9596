#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;
use File::Copy;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($kapDB);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Data Flow Export</TITLE>
<LINK REL="SHORTCUT ICON" HREF="$Lib::KoalaConfig::kapHostURL/favicon.png">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-editable-select-2.2.5/dist/jquery-editable-select.min.js"></SCRIPT>
<LINK HREF="/jquery-editable-select-2.2.5/dist/jquery-editable-select.min.css" REL="stylesheet">


<SCRIPT>
function checkForm(form)
{
  \$('#btn-create').prop('disabled', true);
  \$('#btn-create').text('Please Wait...');
  \$('#btn-update').prop('disabled', true);
  \$('#btn-update').text('Please Wait...');
  \$('#btn-excel').prop('disabled', true);
  \$('#btn-excel').text('Please Wait...');
  \$('#btn-csv').prop('disabled', true);
  \$('#btn-csv').text('Please Wait...');
  return(true);
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($kapDB, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="$Lib::KoalaConfig::kapHostURL/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?f=$flowID">Data Prep</A></LI>
    <LI CLASS="breadcrumb-item">$flowName</LI>
    <LI CLASS="breadcrumb-item active">Data Export</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $flowID = $q->param('f');
  $jobID = $q->param('j');

  #connect to the database
  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #make sure we have write privs for this data source
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to work with jobs in this data flow.");
  }

  $flowName = prep_flow_id_to_name($prepDB, $flowID);

  print_html_header();

  #make sure the prep cloud isn't out of storage
  $usagePct = prep_flow_storage($acctType);
  if ($usagePct > 99)
  {
    exit_error("Your Koala Data Prep cloud is out of storage - please contact your Koala account manager to order more.")
  }

  #if the system is overloaded, have the user try again later
  $runningJobs = prep_running_jobs($prepDB);
  if (($runningJobs > ($Lib::KoalaConfig::prepCores * 1.5)) && ($acctType < 10))
  {
    exit_warning("Whoa! It looks like your Data Prep cloud is being overused. Wait a little bit, and then try again.")
  }

  @userSources = ds_list($kapDB, $userID, $acctType, "W");
  %dsNames = ds_get_name_hash($kapDB);
  %dsOwnerHash = ds_get_owner_hash($kapDB);

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-11 col-lg-8 col-xl-7"> <!-- content -->

      <DIV CLASS="accordion mx-auto" ID="accordion">

        <DIV CLASS="accordion-item border-primary">
          <H2 CLASS="accordion-header">
            <BUTTON CLASS="accordion-button bg-primary text-white icon-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
              Export to Existing Koala Data Source
            </BUTTON>
          </H2>
          <DIV ID="collapse1" CLASS="accordion-collapse collapse show" data-bs-parent="#accordion">
            <DIV CLASS="accordion-body">
              <FORM METHOD="post" ACTION="exportKoalaDS.cld" onsubmit="return checkForm(this);">
              <INPUT TYPE="hidden" NAME="f" VALUE="$flowID">
              <INPUT TYPE="hidden" NAME="j" VALUE="$jobID">

              Choose the data source you want to load this data set into:

              <P>
              <SELECT required CLASS="form-select" NAME="ds" ID="ds" data-width="auto">
                <OPTION VALUE=""></OPTION>
END_HTML

 foreach $dsID (@userSources)
 {
   $CSSstyle = "";
   if ($userID == $dsOwnerHash{$dsID})
   {
     $CSSstyle = "background:lightcyan;";
   }

   print("<OPTION STYLE='$CSSstyle' VALUE=\"$dsID\">$dsNames{$dsID}</OPTION>\n");
 }

 #grab the ID of the data source the user previously selected, and DS settings
 $query = "SELECT dsID, appendUPC, compressWS, dontOverwriteNames, pmatch, gmatch, tmatch \
    FROM prep.flows WHERE ID=$flowID";
 $dbOutput = $prepDB->prepare($query);
 $dbOutput->execute;
 ($dsID, $appendUPC, $compressWS, $dontOverwriteNames, $pmatch, $gmatch, $tmatch) = $dbOutput->fetchrow_array;

 if ($appendUPC > 0)
 {
   $appendUPC = "CHECKED";
 }
 else
 {
   $appendUPC = "";
 }
 if ($compressWS > 0)
 {
   $compressWS = "CHECKED";
 }
 else
 {
   $compressWS = "";
 }
 if ($dontOverwriteNames > 0)
 {
   $dontOverwriteNames = "CHECKED";
 }
 else
 {
   $dontOverwriteNames = "";
 }

 if (length($pmatch) < 1)
 {
   $pmatch = "auto";
 }
 if (length($gmatch) < 1)
 {
   $gmatch = "auto";
 }
 if (length($tmatch) < 1)
 {
   $tmatch = "auto";
 }

 print <<END_HTML;
              </SELECT>
              <SCRIPT>
                \$("select#ds").val("$dsID");
              </SCRIPT>

              <P>
              <DIV CLASS="accordion" ID="adv-update-accordion">
                <DIV CLASS="accordion-item">
                  <H2 CLASS="accordion-header">
                    <BUTTON CLASS="accordion-button bg-secondary bg-opacity-10" TYPE="button" data-bs-toggle="collapse" data-bs-target="#adv-update-collapse">
                      Advanced Options
                    </BUTTON>
                  </H2>

                  <DIV ID="adv-update-collapse" CLASS="collapse">
                    <DIV CLASS="accordion-body">

                      <DIV CLASS="form-check">
                        <INPUT CLASS="form-check-input" NAME="appendUPC" ID="update-appendUPC" TYPE="checkbox" $appendUPC>
                        <LABEL CLASS="form-check-label" FOR="update-appendUPC">Append UPC/EAN to product names</LABEL>
                      </DIV>
                      <DIV CLASS="form-check">
                        <INPUT CLASS="form-check-input" NAME="compressWS" ID="update-compressWS" TYPE="checkbox" $compressWS>
                        <LABEL CLASS="form-check-label" FOR="update-compressWS">Compress extra whitespace in item names</LABEL>
                      </DIV>
                      <DIV CLASS="form-check">
                        <INPUT CLASS="form-check-input" NAME="dontOverwriteNames" ID="update-dontOverwriteNames" TYPE="checkbox" $dontOverwriteNames>
                        <LABEL CLASS="form-check-label" FOR="update-dontOverwriteNames">Don't update product names to match this data set</LABEL>
                      </DIV>

                      <P>&nbsp;</P>
                      Match items using these data fields:
                      <P>
                      <TABLE>
                        <TR>
                          <TD STYLE="text-align:right;">
                            <LABEL FOR="pmatch">Product:</LABEL>
                          </TD>
                          <TD STYLE="text-align:left;">
                            <SELECT CLASS="form-select mx-1" NAME="pmatch" ID="pmatch">
                              <OPTION VALUE="auto">Automatic</OPTION>
                              <OPTION VALUE="upc">UPC</OPTION>
                              <OPTION VALUE="name">Name</OPTION>
                            </SELECT>
                            <SCRIPT>
                              \$("select#pmatch").val("$pmatch");
                            </SCRIPT>
                          </TD>
                        </TR>
                          <TD STYLE="text-align:right;">
                            <LABEL FOR="gmatch">Geography:</LABEL>
                          </TD>
                          <TD STYLE="text-align:left;">
                            <SELECT CLASS="form-select mx-1" NAME="gmatch" ID="gmatch">
                              <OPTION VALUE="auto">Automatic</OPTION>
                              <OPTION VALUE="name">Name</OPTION>
                            </SELECT>
                            <SCRIPT>
                              \$("select#gmatch").val("$gmatch");
                            </SCRIPT>
                          </TD>
                        </TR>
                          <TD STYLE="text-align:right;">
                            <LABEL FOR="tmatch">Time Period:</LABEL>
                          </TD>
                          <TD STYLE="text-align:left;">
                            <SELECT CLASS="form-select mx-1" NAME="tmatch" ID="tmatch">
                              <OPTION VALUE="auto">Automatic</OPTION>
                              <OPTION VALUE="name">Name</OPTION>
                            </SELECT>
                            <SCRIPT>
                              \$("select#tmatch").val("$tmatch");
                            </SCRIPT>
                          </TD>
                        </TR>
                      </TABLE>

                    </DIV>
                  </DIV>
                </DIV>
              </DIV>

              <P>
              <DIV CLASS="text-center">
                <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-update" NAME="dest"><I CLASS="bi bi-cloud-upload"></I> Export</BUTTON>
              </DIV>

              </FORM>
            </DIV>
          </DIV>
        </DIV>

        <DIV CLASS="accordion-item border-primary">
          <H2 CLASS="accordion-header">
            <BUTTON CLASS="accordion-button collapsed bg-primary text-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
              Export to New Koala Data Source
            </BUTTON>
          </H2>
          <DIV ID="collapse2" CLASS="accordion-collapse collapse" data-bs-parent="#accordion">
            <DIV CLASS="accordion-body">
              <FORM METHOD="post" ACTION="exportKoalaDS.cld" onsubmit="return checkForm(this);">
              <INPUT TYPE="hidden" NAME="f" VALUE="$flowID">
              <INPUT TYPE="hidden" NAME="j" VALUE="$jobID">

              <P>
              Name:
              <INPUT CLASS="form-control" TYPE="text" NAME="dsName" ID="dsName" required>

              <P>
              Type:
              <SELECT CLASS="form-select" NAME="dsType" ID="dsType" required>
END_HTML

  #initialize our data source type hash with the old standbys
  %dsTypes = (
      "Data Prep" => 1,
      "IRI" => 1,
      "Nielsen" => 1,
      "POS" => 1,
      "RetailLink" => 1,
      "Shipping" => 1,
  );

  #grab all of the data source types from the customer's cloud
  $query = "SELECT type FROM dataSources";
  $dbOutput = $kapDB->prepare($query);
  $dbOutput->execute;
  while (($type) = $dbOutput->fetchrow_array)
  {
    $dsTypes{$type} = 1;
  }

  foreach $type (sort keys %dsTypes)
  {
    print("<OPTION>$type</OPTION>\n");
  }

  print <<END_HTML;
              </SELECT>
              <SCRIPT>
                \$('#dsType').editableSelect({filter:false});
                \$('#dsType').val('Data Prep');
              </SCRIPT>

              <P>
              Description:
              <INPUT CLASS="form-control" TYPE="text" NAME="desc" ID="desc">

              <P>
              <DIV CLASS="accordion" ID="adv-create-accordion">
                <DIV CLASS="accordion-item">
                  <H2 CLASS="accordion-header">
                    <BUTTON CLASS="accordion-button bg-secondary bg-opacity-10" TYPE="button" data-bs-toggle="collapse" data-bs-target="#adv-create-collapse">
                      Advanced Options
                    </BUTTON>
                  </H2>
                  <DIV ID="adv-create-collapse" CLASS="collapse">
                    <DIV CLASS="accordion-body">

                      <DIV CLASS="form-check">
                        <INPUT CLASS="form-check-input" NAME="appendUPC" ID="create-appendUPC" TYPE="checkbox" $appendUPC>
                        <LABEL CLASS="form-check-label" FOR="create-appendUPC">Append UPC/EAN to product names</LABEL>
                      </DIV>
                      <DIV CLASS="form-check">
                        <INPUT CLASS="form-check-input" NAME="compressWS" ID="create-compressWS" TYPE="checkbox" $compressWS>
                        <LABEL CLASS="form-check-label" FOR="create-compressWS">Compress extra whitespace in item names</LABEL>
                      </DIV>
                      <DIV CLASS="form-check">
                        <INPUT CLASS="form-check-input" NAME="dontOverwriteNames" ID="create-dontOverwriteNames" TYPE="checkbox" $dontOverwriteNames>
                        <LABEL CLASS="form-check-label" FOR="create-dontOverwriteNames">Don't update product names to match this data set</LABEL>
                      </DIV>

                      <P>&nbsp;</P>
                      Match items using these data fields:
                      <P>
                      <TABLE>
                        <TR>
                          <TD STYLE="text-align:right;">
                            <LABEL FOR="pmatch">Product:</LABEL>
                          </TD>
                          <TD STYLE="text-align:left;">
                            <SELECT CLASS="form-select mx-1" NAME="pmatch" ID="pmatch">
                              <OPTION VALUE="auto">Automatic</OPTION>
                              <OPTION VALUE="upc">UPC</OPTION>
                              <OPTION VALUE="name">Name</OPTION>
                            </SELECT>
                            <SCRIPT>
                              \$('select#pmatch').val('$pmatch');
                            </SCRIPT>
                          </TD>
                        </TR>
                          <TD STYLE="text-align:right;">
                            <LABEL FOR="gmatch">Geography:</LABEL>
                          </TD>
                          <TD STYLE="text-align:left;">
                            <SELECT CLASS="form-select mx-1" NAME="gmatch" ID="gmatch">
                              <OPTION VALUE="auto">Automatic</OPTION>
                              <OPTION VALUE="name">Name</OPTION>
                            </SELECT>
                            <SCRIPT>
                              \$('select#gmatch').val('$gmatch');
                            </SCRIPT>
                          </TD>
                        </TR>
                          <TD STYLE="text-align:right;">
                            <LABEL FOR="tmatch">Time Period:</LABEL>
                          </TD>
                          <TD STYLE="text-align:left;">
                            <SELECT CLASS="form-select mx-1" NAME="tmatch" ID="tmatch">
                              <OPTION VALUE="auto">Automatic</OPTION>
                              <OPTION VALUE="name">Name</OPTION>
                            </SELECT>
                            <SCRIPT>
                              \$('select#tmatch').val('$tmatch');
                            </SCRIPT>
                          </TD>
                        </TR>
                      </TABLE>

                    </DIV>
                  </DIV>
                </DIV>
              </DIV>

              <P>
              <DIV CLASS="text-center">
                <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-create" NAME="dest"><I CLASS="bi bi-cloud-upload"></I> Export</BUTTON>
              </DIV>
              </FORM>

            </DIV>
          </DIV>
        </DIV>

        <DIV CLASS="accordion-item border-primary">
          <H2 CLASS="accordion-header">
            <BUTTON CLASS="accordion-button collapsed bg-primary text-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse3">
              Export to Excel
            </BUTTON>
          </H2>
          <DIV ID="collapse3" CLASS="accordion-collapse collapse" data-bs-parent="#accordion">
            <DIV CLASS="accordion-body">

              <FORM METHOD="post" ACTION="exportExcel.cld" onsubmit="return checkForm(this);">
              <INPUT TYPE="hidden" NAME="f" VALUE="$flowID">
              <INPUT TYPE="hidden" NAME="j" VALUE="$jobID">

              <P>
              Export your prepared data to a downloadable Excel workbook.

              <P>
              <DIV CLASS="text-center">
                <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-excel" NAME="dest"><I CLASS="bi bi-cloud-download"></I> Export</BUTTON>
              </DIV>
              </FORM>


            </DIV>
          </DIV>
        </DIV>

        <DIV CLASS="accordion-item border-primary">
          <H2 CLASS="accordion-header">
            <BUTTON CLASS="accordion-button collapsed bg-primary text-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse4">
              Export to Compressed CSV
            </BUTTON>
          </H2>
          <DIV ID="collapse4" CLASS="accordion-collapse collapse" data-bs-parent="#accordion">
            <DIV CLASS="card-body">
              <FORM METHOD="post" ACTION="exportCSV.cld" onsubmit="return checkForm(this);">
              <INPUT TYPE="hidden" NAME="f" VALUE="$flowID">
              <INPUT TYPE="hidden" NAME="j" VALUE="$jobID">

              <P>
              Export your prepared data to a downloadable compressed CSV file.

              <P>
              <DIV CLASS="text-center">
                <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-csv" NAME="dest"><I CLASS="bi bi-cloud-download"></I> Export</BUTTON>
              </DIV>
              </FORM>

            </DIV>
          </DIV>
        </DIV>

      </DIV>


      <P>
      <DIV CLASS="text-center">
        <A CLASS="btn btn-secondary" HREF="flowViewData.cld?f=$flowID&j=$jobID"><I CLASS="bi bi-x-lg"></I> Cancel</A>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
