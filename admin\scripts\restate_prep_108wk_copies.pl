#!/usr/bin/perl

#
# Creates a copy of all users' update (8 week) data flows that are pulling
# from the AOD IDW and don't already have a 108 week data flow. Only makes
# copies of daily/weekly scheduled update data flows that are updating a
# Koala data source.
#

use lib "/opt/apache/app/";

use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::PrepUtils;


  #connect to the database
  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #get the ID, data source, and category of every update data flow on the system
  $query = "SELECT ID, userID, dsID, sourceInfo FROM prep.flows WHERE sourceInfo LIKE 'FTP=nielsen|%-update.zip' AND dsID > 0";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  while (($flowID, $userID, $dsID, $sourceInfo) = $dbOutput->fetchrow_array)
  {

    #extract the category tag from the flow's sourceInfo string
    if ($sourceInfo =~ m/.*\|PATH=(.*)\-update.zip/)
    {
      $category = $1;
    }
    else
    {
      print("!!! Couldn't parse category from $sourceInfo for flow $flowID, skipping\n");
      next;
    }

    $key = "$userID-$dsID-$category";
    $copyFlowsHash{$key} = $flowID;
  }

  #remove all possibilities that already have a matching 108 week data flow
  $query = "SELECT ID, userID, dsID, sourceInfo FROM prep.flows \
      WHERE sourceInfo LIKE 'FTP=nielsen|%' AND sourceInfo NOT LIKE '%-update.zip' AND dsID > 0";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  while (($flowID, $userID, $dsID, $sourceInfo) = $dbOutput->fetchrow_array)
  {

    #extract the category tag from the flow's sourceInfo string
    if ($sourceInfo =~ m/.*\|PATH=(.*)\.zip/)
    {
      $category = $1;
    }
    else
    {
      print("!!! Couldn't parse category from $sourceInfo for flow $flowID, skipping\n");
      next;
    }

    $key = "$userID-$dsID-$category";
    delete($copyFlowsHash{$key});
    print "Deleting (108 exists) copyFlowsHash{$flowID} = $key\n";
  }

  #reverse hash so flowID becomes key
  %copyFlowsHash = reverse(%copyFlowsHash);

  #create the copies
  foreach $flowID (keys %copyFlowsHash)
  {

    #start by grabbing all of the source flow's primary info
    $query = "SELECT name, userID, source, sourceInfo, parseOptions, dsID FROM prep.flows WHERE ID=$flowID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($name, $userID, $source, $sourceInfo, $parseOptions, $dsID) = $dbOutput->fetchrow_array;

    print "Creating 108 week copy of $name\n";

    #extract the category tag from the flow's sourceInfo string
    if ($sourceInfo =~ m/(.*\|PATH=.*)\-update.zip/)
    {
      $preamble = $1;
      $sourceInfo = $1 . ".zip";
    }
    else
    {
      print("!!! Couldn't parse category from $sourceInfo for flow $flowID, skipping\n");
      next;
    }

    #insert the entry for the copy of the flow
    $q_name = $prepDB->quote($name);
    $q_sourceInfo = $prepDB->quote($sourceInfo);
    $q_parseOptions = $prepDB->quote($parseOptions);
    $query = "INSERT INTO prep.flows (userID, name, description, source, sourceInfo, parseOptions, dsID) \
        VALUES ($userID, $q_name, '*** Auto-Created for 2020 Nielsen restatement ***', '$source', $q_sourceInfo, $q_parseOptions, $dsID)";
    $prepDB->do($query);
    $newFlowID = $prepDB->{q{mysql_insertid}};

    #re-create the file type hints entries for the new flow
    $query = "SELECT name, tabName, type FROM prep.file_types WHERE flowID=$flowID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;

    while (($fileName, $tabName, $type) = $dbOutput->fetchrow_array)
    {
      $q_fileName = $prepDB->quote($fileName);
      $q_tabName = $prepDB->quote($tabName);
      $query = "INSERT INTO prep.file_types \
          (flowID, name, tabName, type) \
          VALUES ($newFlowID, $q_fileName, $q_tabName, '$type')";
      $prepDB->do($query);
    }

    #copy the recipe steps
    $query = "SELECT step, action FROM prep.recipes WHERE flowID=$flowID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;

    while (($step, $action) = $dbOutput->fetchrow_array)
    {
      $q_action = $prepDB->quote($action);
      $query = "INSERT INTO prep.recipes (flowID, step, action) \
          VALUES ($newFlowID, $step, $q_action)";
      $prepDB->do($query);
    }
  }


#EOF
