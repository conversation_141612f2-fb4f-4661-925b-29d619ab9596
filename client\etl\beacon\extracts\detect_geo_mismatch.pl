#!/usr/bin/perl


use Text::CSV_XS;
use DateTime::Duration;



#---------------------------------------------------------------------------
#
# Output log info to console and log file
#

sub DBG
{
  my ($date);

  my ($str) = @_;

  $date = localtime();
  print("$date: $str\n");
}



#---------------------------------------------------------------------------

  DBG("Starting geography mismatch detection process");

=pod
  #grab 25 random historical categories
  undef(@catFiles);
  opendir(DIRHANDLE, "/data2/beacon");
  while ((defined($filename = readdir(DIRHANDLE))) && (scalar(@catFiles) < 25))
  {
    if (($filename =~ m/^(.*)\.zip$/i) && (!($filename =~ m/update\.zip$/i)))
    {
      push(@catFiles, $1);
    }
  }
=cut

  #specify a set of big categories that we know cover all possible geographies
  @catFiles = ('confection', 'cat_food', 'cookies', 'coffee', 'supplements');
#@catFiles = ('custard_mix');

  chdir("/data2/beacon/");

  $csv = Text::CSV_XS->new( {binary => 1, eol => $/} );

  #analyze the geography data from each of the historical cat files
  undef(%historicalGeoHash);
  foreach $fileStub (@catFiles)
  {

    $zipFilename = $fileStub . ".zip";
    $factsFilename = $fileStub . ".csv";

    #uncompress the existing facts data
    DBG("Uncompressing $zipFilename");
    `/usr/bin/unzip -o $zipFilename $factsFilename`;

    open($INPUT, "/data2/beacon/$factsFilename") or die("Unable to open $factsFilename, $!");

    DBG("Analyzing contents of $factsFilename");
    while ($colref = $csv->getline($INPUT))
    {

      if (@$colref[2] =~ m/.*19$/)
      {
        next;
      }

      $historicalGeoHash{@$colref[3]} = 1;
    }
    close($INPUT);
    unlink("/data2/beacon/$factsFilename");
  }

  #analyze the geography data from each of the update cat files
  undef(%updateGeoHash);
  foreach $fileStub (@catFiles)
  {
    $zipFilename = $fileStub . "-update.zip";
    $factsFilename = $fileStub . ".csv";

    #uncompress the existing facts data
    DBG("Uncompressing $zipFilename");
    `/usr/bin/unzip -o $zipFilename $factsFilename`;

    open($INPUT, "/data2/beacon/$factsFilename") or die("Unable to open $factsFilename, $!");

    DBG("Analyzing contents of $factsFilename");
    while ($colref = $csv->getline($INPUT))
    {
      $updateGeoHash{@$colref[3]} = 1;
    }

    close($INPUT);
    unlink("/data2/beacon/$factsFilename");
  }

  DBG("Done");

  print("\n\n");
  print("Deprecated Historical Geographies\n");
  print("---------------------------------\n");

  foreach $geo (sort keys %historicalGeoHash)
  {
    if ($updateGeoHash{$geo} != 1)
    {
      push(@deprecatedGeos, $geo);
    }
  }

  foreach $geo (@deprecatedGeos)
  {
    print("$geo\n");
  }

  print("\n\n");

  print("\n\n");
  print("New Geographies\n");
  print("---------------\n");

  foreach $geo (sort keys %updateGeoHash)
  {
    if ($historicalGeoHash{$geo} != 1)
    {
      push(@newGeos, $geo);
    }
  }

  foreach $geo (@newGeos)
  {
    print("$geo\n");
  }


#EOF
