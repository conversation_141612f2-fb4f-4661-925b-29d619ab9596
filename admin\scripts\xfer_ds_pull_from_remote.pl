#!/usr/bin/perl

use lib "/opt/apache/app/";

use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;

#hard-coded array of data source snowflake tables
@tableNames = ('facts', 'geographies', 'geography_aggregate',
  'geography_attribute_values', 'geography_attributes', 'geography_list',
  'geography_seghierarchy', 'geography_segment', 'geography_segment_item',
  'geography_segmentation', 'geography_seg_rules', 'measure_attribute_values',
  'measure_attributes', 'measure_list', 'measures', 'product_aggregate',
  'product_attribute_values', 'product_attributes', 'product_list',
  'product_seghierarchy', 'product_segment', 'product_segment_item',
  'product_segmentation', 'product_seg_rules', 'products',
  'products_merged', 'time_aggregate', 'time_attribute_values',
  'time_attributes', 'time_list', 'time_seghierarchy', 'time_segment',
  'time_segment_item', 'time_segmentation', 'time_seg_rules', 'timeperiods',
  'update_history');


  #specify the remote cloud and the DS ID on that cloud to be transferred
  $remoteHost = $ARGV[0];
  $remoteDSID = $ARGV[1];

  #connect to both remote and local clouds
  $db = KAPutil_connect_to_database();
  $remoteDB = DBI->connect("DBI:mysql:prep;host=$remoteHost;mysql_compression=1", 'lamont', 'enigma1');

  $remoteSchema = "datasource_$remoteDSID";

  #grab the basic info about the data source from the remote cloud
  $query = "SELECT name, type, lastUpdate, lastModified, timeAlias, timePruning FROM app.dataSources WHERE ID=$remoteDSID";
  $dbOutput = $remoteDB->prepare($query);
  $dbOutput->execute;
  ($name, $type, $lastUpdate, $lastModified, $timeAlias, $timePruning) = $dbOutput->fetchrow_array;

  #insert entry for DS into local cloud, and get back new dsID
  $query = "INSERT INTO app.dataSources (userID, name, type, lastUpdate, lastModified, timeAlias, timePruning) \
      VALUES (0, '$name', '$type', '$lastUpdate', '$lastModified', '$timeAlias', '$timePruning')";
  $db->do($query);
  $dsID = $db->{q{mysql_insertid}};

  $dsSchema = "datasource_$dsID";

  $db->do("CREATE SCHEMA $dsSchema");
  $db->do("USE $dsSchema");

  #run through each of the source DS'd tables
  foreach $tableName (@tableNames)
  {
    print("Creating table $tableName\n");
    $query = "SHOW CREATE TABLE $remoteSchema.$tableName";
    $dbOutput = $remoteDB->prepare($query);
    $dbOutput->execute;
    ($dummy, $createStatement) = $dbOutput->fetchrow_array;

    #strip off any cloud-specific data directory directives
    if ($createStatement =~ m/^(.*) DATA DIRECTORY.*/s)
    {
      $createStatement = $1;
    }

    $db->do($createStatement);

    print("Populating table $tableName\n");
    $query = "SELECT * FROM $remoteSchema.$tableName";
    $dbOutput = $remoteDB->prepare($query);
    $dbOutput->execute;
    while (@rowVals = $dbOutput->fetchrow_array)
    {

      #we've gotta SQL quote every value manually (annoying)
      $q_valStr = "";
      foreach $val (@rowVals)
      {
        $q_val = $db->quote($val);
        $q_valStr = $q_valStr . "$q_val,";
      }
      chop($q_valStr);

      $query = "INSERT INTO $dsSchema.$tableName VALUES ($q_valStr)";
      $db->do($query);
    }
  }

  #run through each report in the remote DS and create a copy in the local cloud
  $query = "SELECT ID, name, lastUpdate, status, scriptProducts, scriptGeographies, scriptTimePeriods, scriptMeasures, slicers, lockVisuals, background FROM app.cubes \
      WHERE dsID=$remoteDSID";
  $dbOutput = $remoteDB->prepare($query);
  $dbOutput->execute;
  while (($srcRptID, $name, $lastUpdate, $status, $scriptProducts, $scriptGeographies, $scriptTimePeriods, $scriptMeasures, $slicers, $lockVisuals, $background) = $dbOutput->fetchrow_array)
  {
    print("Transfering in report $name\n");
    $q_name = $db->quote($name);
    $query = "INSERT INTO app.cubes (userID, orgID, dsID, name, lastUpdate, status, scriptProducts, scriptGeographies, scriptTimePeriods, scriptMeasures, slicers, lockVisuals, background) \
        VALUES (0, 0, $dsID, $q_name, '2010-01-01 00:00:00', '$status', '$scriptProducts', '$scriptGeographies', '$scriptTimePeriods', '$scriptMeasures', '$slicers', '$lockVisuals', '$background')";
    $db->do($query);
    $rptID = $db->{q{mysql_insertid}};

    #import each visual in the report
    $query = "SELECT type, design, tableRowDims, tableFilterDims, tableColDims, graph_x, graph_y, graph_z, selProducts, selGeographies, selTimeperiods, selMeasures \
        FROM app.visuals WHERE cubeID=$srcRptID";
    $dbOutput2 = $remoteDB->prepare($query);
    $dbOutput2->execute;
    while (($type, $design, $tableRowDims, $tableFilterDims, $tableColDims, $graph_x, $graph_y, $graph_z, $selProducts, $selGeographies, $selTimeperiods, $selMeasures) = $dbOutput2->fetchrow_array)
    {
      $q_design = $db->quote($design);
      $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, tableRowDims, tableFilterDims, tableColDims, graph_x, graph_y, graph_z, selProducts, selGeographies, selTimeperiods, selMeasures)
          VALUES ($rptID, $dsID, '$type', $q_design, '$tableRowDims', '$tableFilterDims', '$tableColDims', '$graph_x', '$graph_y', '$graph_z', '$selProducts', '$selGeographies', '$selTimeperiods', '$selMeasures')";
      $db->do($query);
    }
  }

#EOF
