#!/usr/bin/perl

use lib "/opt/apache/app/";

use DBI;
use Text::CSV;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;



$USERID = 29;


  #connect to the database
  $db = KAPutil_connect_to_database();

  #grab the ID and name of every data flow on the system using AOD extract
  $query = "SELECT ID, name FROM prep.flows \
      WHERE sourceInfo LIKE 'FTP=nielsen%' and userID=$USERID AND description LIKE 'veg pro'";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  while (($flowID, $flowName) = $dbOutput->fetchrow_array)
  {
    $flowNameHash{$flowID} = $flowName;
  }

  $csv = Text::CSV->new( {binary => 1} );

  #cycle through every data flow's recipe, looking for a market trim step
  foreach $flowID (keys %flowNameHash)
  {
    $query = "SELECT step, action FROM prep.recipes \
    WHERE flowID=$flowID AND action LIKE 'TRANS-COL-TRIM-DATA|COL=Market Display Name|OP=keep|%'";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;

    while (($stepID, $action) = $dbOutput->fetchrow_array)
    {
      if ($action =~ m/^TRANS-COL-TRIM-DATA\|COL=(.*)\|OP=keep\|DATA=(.*)$/)
      {
        $colName = $1;
        $matchOp = "keep";
        $dataValues = $2;
      }
      else
      {
        print "INVALID RECIPE STEP, NEXT!\n";
      }

      $csv->parse($dataValues);
      @newValArray = $csv->fields();

      #add the additional items desired by the user
      push(@newValArray, "ALBSCO Shaws Mass/Rhode Island Rem");
      push(@newValArray, "ALBSCO Shaws Mass/Rhode Island TA");
      #push(@newValArray, "");
      #...

      #turn the array of new values into the recipe step and save
      $csv->combine(@newValArray);
      $dataFields = $csv->string;
      $step = "TRANS-COL-TRIM-DATA|COL=$colName|OP=$matchOp|DATA=$dataFields";

      print("$flowID $flowNameHash{$flowID}\n$step\n\n");

      $q_action = $prepDB->quote($step);
      $query = "UPDATE prep.recipes SET action=$q_action \
          WHERE flowID=$flowID AND step=$stepID";
      $prepDB->do($query);
    }
  }


#EOF
