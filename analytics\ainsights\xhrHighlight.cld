#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::AInsights::AInsights;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $priceModelID = $q->param('pm');
  $dsID = $q->param('ds');
  $segID = $q->param('seg');
  $segmentIDstr = $q->param('segments');
  $action = $q->param('action');

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsSchema = "datasource_" . $dsID;

  #make sure we have read privs for this elasticity model
  $privs = AInsights_rights($db, $userID, $priceModelID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to view this model.");
  }



########################################################################
#
# This code block is called on submit to save the changes
#
  #if we're being called to save updated filtering settings
  if ($action eq "save")
  {
    if (($segID > 0) && (length($segmentIDstr) > 0))
    {
      $highlight = "$segID:$segmentIDstr";
    }
    else
    {
      $highlight = "";
    }

    $query = "UPDATE analytics.pricing SET highlight='$highlight' \
        WHERE ID=$priceModelID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    $elasticName = AInsights_ID_to_name($db, $priceModelID);
    $dsName = ds_id_to_name($db, $dsID);

    AInsights_audit($db, $userID, $priceModelID, "Changed display highlight");
    $activity = "AInsights: $first $last changed display highlight in elasticity $elasticName in $dsName";
    utils_slack($activity);

    exit;
  }

  #########################################################################
  #
  # Everything after this point is called to display the highlight dialog
  #

  #get the highlight details from the database
  $query = "SELECT highlight FROM analytics.pricing WHERE ID=$priceModelID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($highlight) = $dbOutput->fetchrow_array;

  if ($highlight =~ m/(.*?)\:(.*)/)
  {
    $matchSegVal = $1;
    $matchSegmentVal = $2;
  }

  print <<END_HTML;
<SCRIPT>
function submitForm()
{
  let segmentMatch = \$('#seg-segmentMatch').val();
  let segMatch = document.getElementById('seg-segMatch').value;
  let url = 'xhrHighlight.cld?action=save&pm=$priceModelID&seg=' + segMatch + '&segments=' + segmentMatch;

  \$.get(url, function(data, status)
  {
    location.href = 'elasticity.cld?pm=$priceModelID';
  });
}


function segUpdate(segSel, segmentSel, firstRun, matchSegmentVal)
{
  let urlStr = '';
  let segID = document.getElementById(segSel).value;

  urlStr = '/app/dsr/ajaxAPI.cld?svc=segments&ds=$dsID&dim=p&seg=' + segID;

  \$(segmentSel).empty();
  \$.ajax(
  {
    url: urlStr,
    dataType: 'json',
    type: 'GET',
    success: function(response)
    {
      if (response != '')
      {
        for (i in response)
        {
          \$(segmentSel).append('<OPTION VALUE=' + response[i].id + '>'+response[i].name+'</OPTION>');
        }

        if ((firstRun == 1) && (matchSegmentVal.length > 0))
        {
          let valStr = matchSegmentVal;
          let vals = valStr.split(',');
          \$(segmentSel).val(vals);
        }
      }

      \$(segmentSel).selectpicker('refresh');
    },
    error: function(x, e) {console.log(e)}
  });
}
</SCRIPT>

<DIV CLASS="modal-dialog modal-lg">
  <DIV CLASS="modal-content">
    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Highlighting</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">

      <DIV CLASS="row">
        <DIV CLASS="col-auto mt-1">
          Highlight products that in the
        </DIV>

        <DIV CLASS="col-auto">
          <SELECT CLASS="form-select" ID="seg-segMatch" onChange="segUpdate('seg-segMatch', '#seg-segmentMatch', 0);">
END_HTML

  %segHash = DSRsegmentation_get_segmentations_hash($db, $dsSchema, "p");
  foreach $segID (sort {$segHash{$a} cmp $segHash{$b}} keys %segHash)
  {
    if ($matchSegVal < 1)
    {
      $matchSegVal = $segID;
    }
    print(" <OPTION VALUE='$segID'>$segHash{$segID}</OPTION>\n");
  }

  print <<END_HTML;
          </SELECT>
        </DIV>
      </DIV>

      <DIV CLASS="row my-1">
        <DIV CLASS="col-auto mt-1">
          segmentation are members of the
        </DIV>

        <DIV CLASS="col-auto">
          <SELECT CLASS="selectpicker mx-1" ID="seg-segmentMatch" multiple>
          </SELECT>
        </DIV>

        <DIV CLASS="col-auto mt-1">
          segment(s).
        </DIV>

        <SCRIPT>
        \$('.selectpicker').selectpicker('render');

        \$(document).ready(function()
        {
          \$('#seg-segMatch').val('$matchSegVal');
          segUpdate('seg-segMatch', '#seg-segmentMatch', 1, '$matchSegmentVal');
        });
        </SCRIPT>

      </DIV>
    </DIV>

    <DIV CLASS="modal-footer">
      <DIV CLASS="text-center">
        <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
        <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="submitForm()"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
      </DIV>
   </DIV>

  </DIV>
</DIV>
END_HTML

#EOF
