#!/usr/bin/perl

use lib "/opt/apache/app/";

use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;


#
# Output detailed statistics for every data source on the system, as well as
# overall storage usage stats for each organization.
#
#


  #connect to the database
  $db = KAPutil_connect_to_database();

  #build a hash of all orgs on the cloud
  $query = "SELECT ID, name FROM app.orgs";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  while (($id, $name) = $dbOutput->fetchrow_array)
  {
    $orgNameHash{$id} = $name;
  }

  #create a hash of all users and orgIDs that have dedicated storage
  $query = "SELECT ID, dataStorage FROM app.orgs WHERE !isnull(dataStorage)";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  while (($orgID, $dir) = $dbOutput->fetchrow_array)
  {
    $orgHash{$orgID} = $dir;
  }

  #build hash of per-org storage volumes (if there are any)
  foreach $orgID (keys %orgNameHash)
  {
    $query = "SELECT ID FROM app.users WHERE orgID=$orgID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    while (($userID) = $dbOutput->fetchrow_array)
    {
      $userOrgHash{$userID} = $orgID;
      $userDirHash{$userID} = $orgHash{$orgID};
    }
  }

  #output total storage used by each org on the cloud
  $query = "SELECT orgID, SUM(storage) FROM app.users GROUP BY orgID";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  while (($orgID, $storage) = $dbOutput->fetchrow_array)
  {

    #skip empty orgs
    if (($storage == 0) || ($orgNameHash{$orgID} eq 'Koala Software'))
    {
      next;
    }

    #convert size to GB
    $storage = $storage + ($storage * 0.15);
    $storage = $storage / 1_000_000_000;
    $storage = sprintf("%.2f", $storage);

    print("$orgNameHash{$orgID},$storage\n");
  }

  print("\n\n----------------------------------------------\n\n");

  #output the total storage used by each user on the system
  $query = "SELECT ID, first, last, orgID, storage FROM app.users";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  while (($userID, $first, $last, $orgID, $storage) = $dbOutput->fetchrow_array)
  {

    #convert size to GB
    $storage = $storage + ($storage * 0.15);
    $storage = $storage / 1_000_000_000;
    $storage = sprintf("%.2f", $storage);

    if ($storage > 0)
    {
      print("$first $last,$orgNameHash{$orgID},$storage\n");
    }
  }

  print("\n\n----------------------------------------------\n\n");

  #output detailed storage usage info for each data source on the system
  print("Data Source,Organization,Analyst,Total Size (MB),Last Updated,Products,Geographies,Time Periods,Base Measures,Calculated Measures,ODBC Export\n");
  $query = "SELECT ID, name, type, lastUpdate, userID, description, ODBCexport, ODBCmanual, ODBCstatus, UNIX_TIMESTAMP(ODBCexported), UNIX_TIMESTAMP(lastModified) \
      FROM dataSources ORDER BY userID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;

  $count = 1;
  while (($dsID, $name, $type, $lastUpdate, $dsUserID, $description, $ODBCexport, $ODBCmanual, $ODBCstatus, $ODBCexported, $lastModified) = $dbOutput->fetchrow_array)
  {
    $orgID = $userOrgHash{$dsUserID};
    $orgName = $orgNameHash{$orgID};
    $dsUserName = utils_userID_to_name($db, $dsUserID);
    $dsSchema = "datasource_" . $dsID;

    #get the various sizes for the data sources
    $query = "SELECT SUM(data_length), SUM(index_length), SUM(data_free) \
        FROM information_schema.TABLES \
        WHERE information_schema.TABLES.table_schema = '$dsSchema'";
    $dbOutput1 = $db->prepare($query);
    $dbOutput1->execute;
    ($sizeData, $sizeIndex, $sizeDataFree) = $dbOutput1->fetchrow_array;
    $totalSize = $sizeData + $sizeIndex, $sizeDataFree;

    $query = "SELECT data_length, index_length, data_free \
        FROM information_schema.TABLES \
        WHERE information_schema.TABLES.table_schema = '$dsSchema' AND information_schema.TABLES.table_name = 'facts'";
    $dbOutput1 = $db->prepare($query);
    $dbOutput1->execute;
    ($sizeData, $sizeIndex, $sizeDataFree) = $dbOutput1->fetchrow_array;
    $dataSize = $sizeData + $sizeIndex + $sizeDataFree;

    $query = "SELECT SUM(data_length), SUM(index_length), SUM(data_free) \
        FROM information_schema.TABLES \
        WHERE information_schema.TABLES.table_schema = '$dsSchema' AND information_schema.TABLES.table_name LIKE '_rptcube_%'";
    $dbOutput1 = $db->prepare($query);
    $dbOutput1->execute;
    ($sizeData, $sizeIndex, $sizeDataFree) = $dbOutput1->fetchrow_array;
    $rptSize = $sizeData + $sizeIndex, $sizeDataFree;

    $query = "SELECT SUM(data_length), SUM(index_length), SUM(data_free) \
        FROM information_schema.TABLES \
        WHERE information_schema.TABLES.table_schema = '$dsSchema' AND information_schema.TABLES.table_name LIKE 'export%'";
    $dbOutput1 = $db->prepare($query);
    $dbOutput1->execute;
    ($sizeData, $sizeIndex, $sizeDataFree) = $dbOutput1->fetchrow_array;
    $ODBCsize = $sizeData + $sizeIndex + $sizeDataFree;

    #get size of snapshots
    $userDir = $userDirHash{$dsUserID};
    if (length($userDir) < 1)
    {
      $userDir = "/opt/apache/app";
    }
    $userDir = "$userDir/logs";
    chdir($userDir);
    opendir(DIRHANDLE, $userDir);
    while (defined($filename = readdir(DIRHANDLE)))
    {
      $fileStub = "datasource_$dsID" . "_";
      if ($filename =~ m/^$fileStub.*.zip/i)
      {
        $size = -s $filename;
        $totalSize = $totalSize + $size;
      }
    }

    #get size of backup
    $userDir = $userDirHash{$dsUserID};
    if (length($userDir) < 1)
    {
      $userDir = "/opt/";
    }
    $filename = "$userDir/koala_backup/datasource_$dsID.zip";
    $size = -s $filename;
    $totalSize = $totalSize + $size;

    #convert size to MB
    $totalSize = $totalSize + ($totalSize * 0.15);
    $totalSize = $totalSize / 1_000_000;
    $totalSize = sprintf("%.2f", $totalSize);
    $dataSize = $dataSize + ($dataSize * 0.15);
    $dataSize = $dataSize / 1_000_000;
    $dataSize = sprintf("%.2f", $dataSize);
    $rptSize = $rptSize + ($rptSize * 0.15);
    $rptSize = $rptSize / 1_000_000;
    $rptSize = sprintf("%.2f", $rptSize);
    $ODBCsize = $ODBCsize + ($ODBCsize * 0.15);
    $ODBCsize = $ODBCsize / 1_000_000;
    $ODBCsize = sprintf("%.2f", $ODBCsize);

    #get the number of base items in each dimension
    $query = "SELECT COUNT(ID) FROM $dsSchema.products";
    $dbOutput2 = $db->prepare($query);
    $dbOutput2->execute;
    ($prodCount) = $dbOutput2->fetchrow_array;
    $query = "SELECT COUNT(ID) FROM $dsSchema.geographies";
    $dbOutput2 = $db->prepare($query);
    $dbOutput2->execute;
    ($geoCount) = $dbOutput2->fetchrow_array;
    $query = "SELECT COUNT(ID) FROM $dsSchema.timeperiods";
    $dbOutput2 = $db->prepare($query);
    $dbOutput2->execute;
    ($timeCount) = $dbOutput2->fetchrow_array;
    $query = "SELECT COUNT(ID) FROM $dsSchema.measures";
    $dbOutput2 = $db->prepare($query);
    $dbOutput2->execute;
    ($measCount) = $dbOutput2->fetchrow_array;
    $query = "SELECT COUNT(ID) FROM $dsSchema.measures WHERE !ISNULL(calculation)";
    $dbOutput2 = $db->prepare($query);
    $dbOutput2->execute;
    ($calcMeasCount) = $dbOutput2->fetchrow_array;
    $baseMeasCount = $measCount - $calcMeasCount;

    if ($ODBCexport == 1)
    {
      $ODBCexport = "Tabular";
    }
    elsif ($ODBCexport == 2)
    {
      $ODBCexport = "Star";
    }
    else
    {
      $ODBCexport = "None";
    }

    print("\"$name\",$dsUserName,$orgName,$totalSize,$lastUpdate,$prodCount,$geoCount,$timeCount,$baseMeasCount,$calcMeasCount,$ODBCexport\n");
  }

#EOF
