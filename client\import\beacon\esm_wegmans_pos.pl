#!/usr/bin/perl

use Text::CSV;

#Import Wegmans POS data for ESM-Ferolie

  #instantiate our CSV object
  $csv = Text::CSV->new( {binary => 1} );

  open(INPUT, "$ARGV[0]");
  open(OUTPUT, ">$ARGV[1]");


  #write header row to output file
  $headerCols[0] = "Product";
  $headerCols[1] = "Geography";
  $headerCols[2] = "Time";
  $headerCols[3] = "Units Sold";
  $headerCols[4] = "Net Sales";
  $headerCols[5] = "Units Sold YAGO";
  $headerCols[6] = "Net Sales YAGO";
  $headerCols[7] = "pseg:Brand";
  $headerCols[8] = "pseg:Segment";
  $headerCols[9] = "pseg:Category";
  $csv->combine(@headerCols);
  $line = $csv->string();
  print OUTPUT "$line\n";

  $pass = 1;
  $yago = 1;

  while ($pass <= 8)
  {

    #rewind to the top of the file
    seek(INPUT, 0, 0);

    #burn the first unneeded line
    $line = <INPUT>;

    #parse the primary header line
    $line = <INPUT>;
    $csv->parse($line);
    @columns = $csv->fields();

    #pull date information for this pass
    $rawDate = $columns[$pass];
    $rawDate =~ m/^.*? \- (.*?) \((\d+ Weeks)\)/;
    $dateString = "$2 Weeks Ending $1";

    #run through each line of data in the file
    while ($line = <INPUT>)
    {
      undef($outputCols);

      $csv->parse($line);
      @columns = $csv->fields();

      #products
      $outputCols[0] = $columns[0];

      #geography
      $outputCols[1] = "TOTAL ALL STORES";

      #time period
      $outputCols[2] = $dateString;

      #units sold
      $outputCols[3] = $columns[$pass];

      #net sales
      $outputCols[4] = $columns[$pass+8];

      #units sold YAGO
      $outputCols[5] = $columns[$pass+1];

      #net sales YAGO
      $outputCols[6] = $columns[$pass+9];


      #brand
      $outputCols[7] = $columns[17];

      #segment
      $outputCols[8] = $columns[18];

      #category
      $outputCols[9] = $columns[19];

      $csv->combine(@outputCols);
      $line = $csv->string();

      print OUTPUT "$line\n";
    }

    $pass += 2;
  }

  #increment pass count
