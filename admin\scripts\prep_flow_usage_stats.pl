#!/usr/bin/perl

use lib "/opt/apache/app/";

use DBI;

use Lib::KoalaConfig;
use Lib::PrepFlows;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::PrepUtils;
use Lib::WebUtils;



#
# Output detailed usage statistics for every data flow on the system based on
# the telemetry logs,
#


  #connect to the database
  $db = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  %prepFlowNameHash = prep_flow_get_name_hash($prepDB);
  %userNameHash = utils_get_user_hash($db);

  #build a hash of all orgs on the cloud
  $query = "SELECT ID, name FROM app.orgs";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  while (($id, $name) = $dbOutput->fetchrow_array)
  {
    $orgNameHash{$id} = $name;
  }

  #build hash of user org membership
  foreach $orgID (keys %orgNameHash)
  {
    $query = "SELECT ID FROM app.users WHERE orgID=$orgID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    while (($userID) = $dbOutput->fetchrow_array)
    {
      $userOrgHash{$userID} = $orgID;
    }
  }

  foreach $userID (keys %userOrgHash)
  {
    $orgID = $userOrgHash{$userID};

    #get the user's data flows
    $query = "SELECT ID, name FROM prep.flows WHERE userID=$userID";
    $dbOutput = $prepDB->prepare($query);
    $status = $dbOutput->execute;

    #if the user doesn't have any data flows, move on
    if ($status < 1)
    {
      next;
    }

    #build a hash of the user's data flows
    undef(%userFlowHash);
    while (($flowID, $flowName) = $dbOutput->fetchrow_array)
    {
      $userFlowHash{$flowID} = $flowName;
    }

    #run through each of the user's flows' telemetry logs, calculating elapsed
    #time
    foreach $flowID (keys %userFlowHash)
    {
      $query = "SELECT startTime, telemetry FROM prep.telemetry \
          WHERE flowID=$flowID AND startTime LIKE '2020-10%'";
      $dbOutput = $prepDB->prepare($query);
      $dbOutput->execute;
      while (($startTime, $telemetry) = $dbOutput->fetchrow_array)
      {

        #split the text block into individual lines
        @lines = split('\n', $telemetry);

        #grab the timestamp off the last line
        $line = $lines[-1];
        if ($line =~ m/cleared job/)
        {
          $line = $lines[-2];
        }
        if ($line =~ m/^(.*?)\:\s/)
        {
          $endTime = $1;

          $query = "SELECT UNIX_TIMESTAMP('$endTime') - UNIX_TIMESTAMP('$startTime')";
          $db_time = $prepDB->prepare($query);
          $db_time->execute;
          $wallTime = $db_time->fetchrow_array;
          $userTime{$userID} += $wallTime;
          $orgTime{$orgID} += $wallTime;
        }
      }
    }
  }


  print("\n\n----------------------------------------------\n\n");

  foreach $orgID (keys %orgTime)
  {
    $time = $orgTime{$orgID};
    $time = $time / 3600;
    $time = $time * 1.25;
    print("$orgNameHash{$orgID},$time\n");
  }

  print("\n\n----------------------------------------------\n\n");

  foreach $userID (keys %userTime)
  {
    $time = $userTime{$userID};
    $time = $time / 3600;
    $time = $time * 1.25;

    $orgID = $userOrgHash{$userID};
    $orgName = $orgNameHash{$orgID};
    print("$userNameHash{$userID},$orgName,$time\n");
  }


#EOF
