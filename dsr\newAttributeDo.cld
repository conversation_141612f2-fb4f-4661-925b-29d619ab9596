#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: New Attribute Created</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">New Attribute</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');
  $dim = $q->param('dim');
  $segmentationID = $q->param('segmentation');
  $attName = $q->param('attName');

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  #make sure we have write privs for the data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to modify the data source.");
  }

  $dsSchema = "datasource_" . $dsID;

  $dbStub = KAPutil_get_dim_stub_name($dim);

  $dimTable = $dbStub . "attributes";

  utils_audit($db, $userID, "Created new attribute $attName", $dsID, 0, 0);
  $dsName = ds_id_to_name($db, $dsID);
  $activity = "$first $last created new attribute $attName in $dsName";

  #if we're creating an attribute based on a segmentation
  if ($segmentationID > 0)
  {

    #get the name of our attribute (same name as the segmentation)
    $dbName = $dbStub . "segmentation";
    $query = "SELECT name FROM $dsSchema.$dbName WHERE ID=$segmentationID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($name) = $dbOutput->fetchrow_array;

    #add the new attribute to the DSR and get its ID
    $q_name = $db->quote($name);
    $query = "INSERT INTO $dsSchema.$dimTable (name) VALUES ($q_name)";
    $db->do($query);
    $attrID = $db->{q{mysql_insertid}};

    #get a hash of all the segments contained in the segmentation
    %segmentHash = DSRseg_get_segments_hash($db, $dsSchema, $dim, $segmentationID);

    #for each segment, set its members' attribute value to the segment name
    foreach $segmentID (keys %segmentHash)
    {

      #get the segment's members
      $segMemberStr = DSRsegment_expand_items($db, $dsSchema, $dim, $segmentID);
      @segMembers =split(',', $segMemberStr);

      #make segment name SQL-friendly
      $q_name = $db->quote($segmentHash{$segmentID});

      #foreach base item in the segment, insert its new attribute value into
      #the DSR
      foreach $itemID (@segMembers)
      {
        $dbName = $dbStub . "attribute_values";
        $query = "INSERT INTO $dsSchema.$dbName (attributeID, itemID, value) \
            VALUES ($attrID, $itemID, $q_name)";
        $db->do($query);
      }
    }
  }

  #else we're creating a new blank attribute
  else
  {
    $q_name = $db->quote($attName);

    $query = "INSERT INTO $dsSchema.$dimTable (name) VALUES ($q_name)";
    $db->do($query);
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-11 col-md-8 col-lg-6 col-xl-5"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">New Attribute Created</DIV>
        <DIV CLASS="card-body">

          The attribute $attName was created successfully.

          <P>&nbsp;<P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" onClick="location.href='display.cld?ds=$dsID&dim=$dim'"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();
  utils_slack($activity);

#EOF
