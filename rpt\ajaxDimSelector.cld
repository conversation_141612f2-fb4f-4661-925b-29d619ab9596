#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI::Session;
use CGI qw(:standard);
use DBI;

use Lib::KoalaConfig;
use Lib::DataSel;
use Lib::DSRMeasures;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::Reports;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get the report/visualization ID to supply datasel tree data for
  $dsID = $q->param('ds');
  $rptID = $q->param('r');
  $visID = $q->param('v');
  $dim = $q->param('d');
  $multiMode = $q->param('m');

  #if the user is a viewer, pull selections from the viewer visual selection table
  if ($acctType == 0)
  {
    $visualSelTable = "app.visuals_viewers";
  }
  else
  {
    $visualSelTable = "app.visuals";
  }

  $db = KAPutil_connect_to_database();

  $dsSchema = "datasource_" . $dsID;

  #determine our selected data column based on dimension
  if ($dim eq "p")
  {
    $selColName = "selProducts";
    $dimName = "Products";
    $lockStyle = "lockProducts";
  }
  elsif ($dim eq "g")
  {
    $selColName = "selGeographies";
    $dimName = "Geographies";
    $lockStyle = "lockGeographies";
  }
  elsif ($dim eq "t")
  {
    $selColName = "selTimeperiods";
    $dimName = "Time Periods";
    $lockStyle = "lockTimes";
  }
  elsif ($dim eq "m")
  {
    $selColName = "selMeasures";
    $dimName = "Measures";
    $lockStyle = "lockMeasures";
  }

  %itemNameHash = dsr_get_item_name_hash($db, $dsSchema, $dim);

  $iconJSON = "";

  #if we're getting the data selections for all visualizations in the report
  if ($visID < 1)
  {
    $query = "SELECT $selColName FROM $visualSelTable WHERE cubeID=$rptID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);

    while (($selItemsString) = $dbOutput->fetchrow_array)
    {
      @tmp = split(',', $selItemsString);
      push(@selItems, @tmp);
    }

    #unique-ify the list of selected items
    @selItems = grep { !$seen{$_}++ } @selItems;

    $selItemsString = join(',', @selItems);
  }

  #else we're getting data selections for a specific visualization
  else
  {
    $query = "SELECT design FROM visuals WHERE ID=$visID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($design) = $dbOutput->fetchrow_array;

    $query = "SELECT $selColName FROM $visualSelTable WHERE ID=$visID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($selItemsString) = $dbOutput->fetchrow_array;

    $lockOn = reports_get_style($design, $lockStyle);
    if ($lockOn == 1)
    {
      $iconJSON = "\"icon\":\"/icons/tree_lock.png\",";
    }
  }

  #turn the available item IDs into an array, selected items into hash
  @dimItems = datasel_get_dimension_items($db, $rptID, $dim);
  undef(@temp);
  @temp = split(/,/, $selItemsString);
  undef(%selItems);
  foreach $item (@temp)
  {
    $selItems{$item} = 1;
  }

  #if this dimension allows multiple selections in the chosen visual...
  if ($multiMode > 1)
  {
    $multiMode = "true";
  }
  else
  {
    $multiMode = "false";
  }

  print("Expires: -1\n");
  print("Cache-Control: private, max-age=0");
  print("Content-type: application/json\n\n");

  print("[\n");

  $json = "{\"key\": \"Dimension\", \"title\":\"$dimName\", \"type\":\"dimension\", \"folder\":true, \"checkbox\":$multiMode, $iconJSON \"children\":[";

  #build up the text for the tree memory store
  $count = 0;
  undef($temp);
  foreach $item (@dimItems)
  {
    if (defined($selItems{$item}))
    {
      $checked = "true";
    }
    else
    {
      $checked = "false";
    }
    $name = $itemNameHash{$item};
    $name =~ s/\"/\\"/g;

    if ($count < 1000)
    {
      $json .= "{\"key\": \"$item\", \"title\": \"$name\", \"tooltip\":\"$name\", \"type\": \"item\", \"selected\": $checked},\n";
    }

    $count++;
  }

  #knock off the unneeded comma from the last item
  chop($json); chop($json);

  $json .= "]}\n]\n";

  print("$json");

#EOF
