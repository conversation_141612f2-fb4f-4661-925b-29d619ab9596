#!/usr/bin/perl

use Text::CSV;

#Import Paradies Spin data for BBI

  #instantiate our CSV object
  $csv = Text::CSV->new( {binary => 1} );

  open(INPUT, "$ARGV[0]");
  open(OUTPUT, ">$ARGV[1]");

  my %geoMappings = (
	"Paradies Airport Loc 9704" => "Paradies Airport Location 9704",
	"Paradies Colo Spgs 9880" => "Paradies Colorado Springs 9880",
	"Paradies Fletcher Delta 9828" => "Paradies Fletcher 9828",
	"Paradies Ft Lauderdale 9954" => "Paradies Fort Lauderdale 9954",
  "Paradies Ind A-9053" => "Paradies Indianapolis 9053",
	"Paradies JFK Jamaica 9212" => "Paradies JFK 9212",
	"Paradies KCI Lagardere 9352" => "Paradies Kansas City 9352",
	"Paradies Legar Austin 9512" => "Paradies Legardere Austin 9512",
	"Paradies Syracuse 09315" => "Paradies Syracuse 9315");


  #grab the first line, which contains our date info
  $line = <INPUT>;
  $csv->parse($line);
  @columns = $csv->fields();
  $timeperiod = $columns[0];

  #if we're a quarter-ending date ("1Q 2017 ABCDEF") into a CPG date
  if ($timeperiod =~ m/^(\d)Q (\d+) .*$/)
  {
    if ($1 eq "1")
    {
      $timeperiod = "3 months ending 03/31/$2";
    }
    elsif ($1 eq "2")
    {
      $timeperiod = "3 months ending 06/30/$2";
    }
    elsif ($1 eq "3")
    {
      $timeperiod = "3 months ending 09/30/$2";
    }
    elsif ($1 eq "4")
    {
      $timeperiod = "3 months ending 12/31/$2";
    }
  }

  #else if we're a month-ending date ("FEBRUARY 2018 ABCDEF")
  elsif ($timeperiod =~ m/^(.*?) (\d+) .*$/)
  {
    $year = $2;
    if ($1 =~ m/^jan/i)
    {
      $timeperiod = "1 month ending 01/31/$year";
    }
    elsif ($1 =~ m/^feb/i)
    {
      $timeperiod = "1 month ending 02/28/$year";
    }
    elsif ($1 =~ m/^mar/i)
    {
      $timeperiod = "1 month ending 03/31/$year";
    }
    elsif ($1 =~ m/^apr/i)
    {
      $timeperiod = "1 month ending 04/30/$year";
    }
    elsif ($1 =~ m/^may/i)
    {
      $timeperiod = "1 month ending 05/31/$year";
    }
    elsif ($1 =~ m/^jun/i)
    {
      $timeperiod = "1 month ending 06/30/$year";
    }
    elsif ($1 =~ m/^jul/i)
    {
      $timeperiod = "1 month ending 07/31/$year";
    }
    elsif ($1 =~ m/^aug/i)
    {
      $timeperiod = "1 month ending 08/31/$year";
    }
    elsif ($1 =~ m/^sep/i)
    {
      $timeperiod = "1 month ending 09/30/$year";
    }
    elsif ($1 =~ m/^oct/i)
    {
      $timeperiod = "1 month ending 10/31/$year";
    }
    elsif ($1 =~ m/^nov/i)
    {
      $timeperiod = "1 month ending 11/30/$year";
    }
    elsif ($1 =~ m/^dec/i)
    {
      $timeperiod = "1 month ending 12/31/$year";
    }
  }

  #burn line(s) containing rebate %
  $line = <INPUT>;
  $csv->parse($line);
  @columns = $csv->fields();
  while (length($columns[0]) < 1)
  {
    $line = <INPUT>;
    $csv->parse($line);
    @columns = $csv->fields();
  }

  #parse the primary header line
  $csv->parse($line);
  @columns = $csv->fields();

  $idx = 0;
  foreach $header (@columns)
  {
    if ($header eq "Opco")
    {
      $header = "gattr:Opco";
    }
    elsif ($header eq "Opco Name")
    {
      $header = "gattr:Opco Name";
    }
    elsif ($header eq "Customer #")
    {
      $header = "gattr:Customer #";
    }
    elsif ($header eq "Customer Name")
    {
      $header = "Geography";
      $geoIdx = $idx;
    }
    elsif ($header eq "City")
    {
      $header = "gattr:City";
    }
    elsif ($header eq "State")
    {
      $header = "gattr:State";
    }
    elsif ($header eq "Supplier #")
    {
      $header = "pattr:Supplier #";
    }
    elsif ($header eq "VSN")
    {
      $header = "pattr:VSN";
    }
    elsif ($header eq "Item Description")
    {
      $productIdx = $idx;
      $header = "Product";
    }
    elsif ($header eq "Qty Shp")
    {
      $header = "Qty Ship";
    }

    $idx++;
  }

  @tmp = ('Time Period');
  push(@tmp, @columns);

  #output the headers
  $csv->combine(@tmp);
  $line = $csv->string();
  print OUTPUT "$line\n";


  while ($line = <INPUT>)
  {

    $csv->parse($line);
    @columns = $csv->fields();

    #if there isn't a valid product in the line, ignore it
    if (length($columns[0]) < 1)
    {
      next;
    }

    #normalize the geography name (yank "Rtl", "Retl", etc.)
    $columns[$geoIdx] =~ s/Rtl//;
    $columns[$geoIdx] =~ s/Retl//;

    $columns[$geoIdx] =~ s/\s+/ /g;

    #if the geography ends with " Rt", rip it off
    if ($columns[$geoIdx] =~ m/^(.*) Rt$/)
    {
      $columns[$geoIdx] = $1;
    }
    if ($columns[$geoIdx] =~ m/^(.*) RTL$/)
    {
      $columns[$geoIdx] = $1;
    }

    #turn "Cty" into "City"
    $columns[$geoIdx] =~ s/ Cty / City /;

    #handle one-off mappings, if we need to
    if (length($geoMappings{$columns[$geoIdx]}) > 0)
    {
      $columns[$geoIdx] = $geoMappings{$columns[$geoIdx]};
    }

    @tmp = ("$timeperiod");
    push(@tmp, @columns);

    $csv->combine(@tmp);
    $line = $csv->string();

    print OUTPUT "$line\n";
  }

  close(OUTPUT);
