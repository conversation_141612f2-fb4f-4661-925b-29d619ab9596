#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;
use Text::CSV;
use Text::Fuzzy;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::WebUtils;


#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($kapDB);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Match Products</TITLE>
<LINK REL="SHORTCUT ICON" HREF="$Lib::KoalaConfig::kapHostURL/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>


<SCRIPT>
\$(function()
{
  let listHeight = window.innerHeight - 325;
  if (listHeight < 300)
  {
    listHeight = 300;
  }
  \$('#div-prod-list').css('height', listHeight);
});



function changeDatasource()
{
  let dsID = document.getElementById('ds').value;

  location.href='?f=$flowID&j=$jobID&col=$colID&ds=' + dsID + '&a=ds';
}



function showMatchDlg(flowItem, dsItem)
{
  let url = 'xhrTransColMatchProductMatch.cld?f=$flowID&j=$jobID&col=$colID&ds=$dsID&fi=' + encodeURIComponent(flowItem) + '&dsi=' + dsItem;
  \$('#wait-spinner').css('display', 'block');
  \$('#modal-match').load(url, function (response, status, xhr)
  {
    if (status == 'success')
    {
      let myModal = new bootstrap.Modal(document.getElementById('modal-match'));
      myModal.show();

      \$('#wait-spinner').css('display', 'none');
    }
  });
}

</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($kapDB, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="$Lib::KoalaConfig::kapHostURL/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">Data Prep</A></LI>
    <LI CLASS="breadcrumb-item">$flowName</LI>
    <LI CLASS="breadcrumb-item active">Match Products</LI>
  </OL>
</NAV>

<DIV ID="wait-spinner" STYLE="display:none; z-index:1001; width:100%; height:100%; position:absolute; top:0; left:0; background-color: rgba(0,0,0,0.15);">
 <DIV STYLE="position:absolute; top:50%; left:50%;">
  <I CLASS="fas fa-circle-notch fa-spin" STYLE="font-size:100px; color:blue;"></I>
 </DIV>
</DIV>

<DIV ID="modal-match" CLASS="modal">
</DIV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser and get the forecast to be deleted
  $q = new CGI;

  #get the CGI input variables
  $flowID = $q->param('f');
  $jobID = $q->param('j');
  $colID = $q->param('col');
  $dsID = $q->param('ds');

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #connect to the database
  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #get the name of the data source
  $flowName = prep_flow_id_to_name($prepDB, $flowID);

  if ($jobID < 1)
  {
    $cancelAction = "recipeEdit.cld?f=$flowID";
  }
  else
  {
    $cancelAction = "flowViewData.cld?f=$flowID&j=$jobID";
  }

  #if the flow points to a Koala data source, use it as the default selection
  if ($dsID < 1)
  {
    $query = "SELECT dsID FROM prep.flows WHERE ID=$flowID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($dsID) = $dbOutput->fetchrow_array;
  }

  #if we still don't have a valid data source, see if we've got one from a
  #previous match operation in this flow
  if ($dsID < 1)
  {
    $query = "SELECT action FROM prep.recipes \
        WHERE flowID=$flowID AND action LIKE 'TRANS-COL-PRODUCT-MATCH|%'";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($recipeStep) = $dbOutput->fetchrow_array;
    if ($recipeStep =~ m/^TRANS-COL-PRODUCT-MATCH\|COL=(.*)\|DS=(.*?)\|UNMATCHED=(.*?)\|$/)
    {
      $dsID = $2;
    }
  }

  #if we're given a DS but there isn't one in the data flow yet, add it both
  #to make the match UI a little smoother and to keep the analyst from making
  #a potential mistake when choosing which DS to export to
  $query = "SELECT dsID FROM prep.flows WHERE ID=$flowID";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($flowDSID) = $dbOutput->fetchrow_array;
  if (($dsID > 0) && ($flowDSID < 1))
  {
    $query = "UPDATE prep.flows SET dsID=$dsID WHERE ID=$flowID";
    $status = $prepDB->do($query);
  }

  if ($dsID < 1)
  {
    $dsID = 0;
  }

  print_html_header();

  #make sure we have write privs for this data flow
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this data flow.");
  }

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #build name of column containing the text
  $column = "column_" . $colID;

  #get a hash of the unique dimension items
  $query = "SELECT DISTINCT $column FROM $masterTable";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;

  while (($item) = $dbOutput->fetchrow_array)
  {
    $dimItemsHash{$item} = 1;
    $unmatchedFlowItemHash{$item} = 1;
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-12 col-lg-11 col-xl-9"> <!-- content -->

      <FORM METHOD="post" ACTION="transformProgress.cld" onsubmit="return checkForm(this);">
      <INPUT TYPE="hidden" NAME="f" VALUE="$flowID">
      <INPUT TYPE="hidden" NAME="j" VALUE="$jobID">
      <INPUT TYPE="hidden" NAME="col" VALUE="$colID">
      <INPUT TYPE="hidden" NAME="ds" VALUE="$dsID">
      <INPUT TYPE="hidden" NAME="unmatched" VALUE="new">
      <INPUT TYPE="hidden" NAME="a" VALUE="TRANS-COL-PRODUCT-MATCH">

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Match Dimension Fields</DIV>
        <DIV CLASS="card-body">

          <DIV CLASS="row mx-auto mb-3">
            <DIV CLASS="col"></DIV>
            <DIV CLASS="col-auto mt-1">Match with products from:</DIV>
            <DIV CLASS="col-auto">
              <SELECT CLASS="form-select mx-1 w-100" NAME="ds" ID="ds" onChange="changeDatasource()">
END_HTML

  #get the list of data sources the current user has access to
  @userSources = ds_list($kapDB, $userID, $acctType);
  %dsNames = ds_get_name_hash($kapDB);

  foreach $id (@userSources)
  {
    print("<OPTION VALUE=\"$id\">$dsNames{$id}</OPTION>\n");
  }

  print <<END_HTML;
              </SELECT>
              <SCRIPT>
                \$('select#ds').val('$dsID');
              </SCRIPT>
            </DIV>
            <DIV CLASS="col"></DIV>
          </DIV>

          <DIV ID="div-prod-list" STYLE="height:500px; overflow:auto;">
            <DIV CLASS="list-group">
END_HTML


  #------------------------------------------------------------------------
  # AI for matching up dimension items

  $dsSchema = "datasource_" . $dsID;

  #get a hash of all product names in the data source
  %dsItemNameHash = dsr_get_base_item_name_hash($kapDB, $dsSchema, "p", 1);

  #reverse and lowercase item name hash so we can find the ID by name
  %dsNameToIDHash = dsr_get_base_item_name_hash($kapDB, $dsSchema, "p", 1);
  %dsNameToIDHash = reverse(%dsNameToIDHash);
  %dsNameToIDHash = map { lc $_ => $dsNameToIDHash{$_} } keys %dsNameToIDHash;

  #update the IDs of previous "new item" verified matches (ID will be 0)
  $query = "SELECT dsItem FROM prep.dim_matches \
      WHERE flowID=$flowID AND dsID=$dsID AND dsItemID=0";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  while (($dsItemName) = $dbOutput->fetchrow_array)
  {
    $updatedItemID = $dsNameToIDHash{lc($dsItemName)};
    if ($updatedItemID > 0)
    {
      $q_dsItemName = $prepDB->quote($dsItemName);
      $query = "UPDATE prep.dim_matches SET dsItemID=$updatedItemID \
          WHERE flowID=$flowID AND dsID=$dsID AND dsItem=$q_dsItemName";
      $status = $prepDB->do($query);
    }
  }

  #remove any stored matches that involve no-longer-present (due to rollbacks or
  #deletions) items
  @dsItemIDsArray = dsr_get_itemIDs_array($kapDB, $dsSchema, "p");
  $dsItemIDsStr = join(',', @dsItemIDsArray);
  $query = "DELETE FROM prep.dim_matches \
      WHERE flowID=$flowID AND dsID=$dsID AND dsItemID > 0 AND dsItemID NOT IN ($dsItemIDsStr)";
  $status = $prepDB->do($query);

  #start by grabbing "known" matches for this flow/DS/dim combo from the DB
  $query = "SELECT flowItem, dsItemID FROM prep.dim_matches \
      WHERE flowID=$flowID AND dsID=$dsID AND dim='p' AND confidence IN ('verified', 'high')";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  while (($flowItemName, $dsItemID) = $dbOutput->fetchrow_array)
  {
    $matchedItemHash{$flowItemName} = $dsItemID;
    delete($unmatchedFlowItemHash{$flowItemName});
    $highConfidenceMatchHash{$flowItemName} = 1;
  }

  $query = "SELECT flowItem, dsItemID FROM prep.dim_matches \
      WHERE flowID=$flowID AND dsID=$dsID AND dim='p' AND confidence = 'good'";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  while (($flowItemName, $dsItemID) = $dbOutput->fetchrow_array)
  {
    $matchedItemHash{$flowItemName} = $dsItemID;
    delete($unmatchedFlowItemHash{$flowItemName});
    $goodConfidenceMatchHash{$flowItemName} = 1;
  }



  #TODO try to match items based on complete UPC

  #try matching items based on case-insensitive exact string matches
  foreach $flowItemName (keys %unmatchedFlowItemHash)
  {
    if ($dsNameToIDHash{lc($flowItemName)} > 0)
    {
      $matchedItemHash{$flowItemName} = $dsNameToIDHash{lc($flowItemName)};
      delete($unmatchedFlowItemHash{$flowItemName});
      $highConfidenceMatchHash{$flowItemName} = 1;

      $dsItemID = $matchedItemHash{$flowItemName};
      $q_flowItemName = $prepDB->quote($flowItemName);
      $q_dsItemName = $prepDB->quote($dsItemNameHash{$dsItemID});
      $query = "INSERT INTO prep.dim_matches \
          (flowID, dsID, dim, flowItem, dsItem, dsItemID, confidence) \
          VALUES ($flowID, $dsID, 'p', $q_flowItemName, $q_dsItemName, $dsItemID, 'high') \
          ON DUPLICATE KEY UPDATE dsItem=$q_dsItemName, dsItemID=$dsItemID, confidence='high'";
      $prepDB->do($query);
    }
  }

  #grab almost certain matches from any flow/DS combo for this dim from the DB
  $itemStr = "";
  foreach $flowItemName (keys %unmatchedFlowItemHash)
  {
    $itemStr .= $prepDB->quote($flowItemName) . ",";
  }
  chop($itemStr);
  if (length($itemStr) > 0)
  {
    $query = "SELECT flowItem, dsItemID FROM prep.dim_matches \
        WHERE dim='p' AND flowItem IN ($itemStr)";
    $dbOutput = $prepDB->prepare($query);
    $status = $dbOutput->execute;
    while (($flowItemName, $dsItemID) = $dbOutput->fetchrow_array)
    {
      $matchedItemHash{lc($flowItemName)} = $dsItemID;
      delete($unmatchedFlowItemHash{$flowItemName});
      $goodConfidenceMatchHash{$flowItemName} = 1;

      $q_flowItemName = $prepDB->quote($flowItemName);
      $q_dsItemName = $prepDB->quote($dsItemNameHash{$dsItemID});
      $query = "INSERT INTO prep.dim_matches \
          (flowID, dsID, dim, flowItem, dsItem, dsItemID, confidence) \
          VALUES ($flowID, $dsID, 'p', $q_flowItemName, $q_dsItemName, $dsItemID, 'good') \
          ON DUPLICATE KEY UPDATE dsItem=$q_dsItemName, dsItemID=$dsItemID, confidence='good'";
      $prepDB->do($query);
    }
  }


  #
  #attempt a fuzzy text match for anything remaining
  #

  #build an array of unmatched DS item names
  #NB: we're assuming that any unmatched flow items don't match an already
  #   matched data source item
  %matchedIDtoItemHash = reverse(%matchedItemHash);
  foreach $dsItemID (keys %dsItemNameHash)
  {
    if (length($matchedIDtoItemHash{$dsItemID}) < 1)
    {
      push(@fuzzyMatchWords, $dsItemNameHash{$dsItemID})
    }
  }

  foreach $flowItemName (keys %unmatchedFlowItemHash)
  {
    if (length($flowItemName) < 1)
    {
      next;
    }

    $tf = Text::Fuzzy->new($flowItemName);
    $nearest = $tf->nearestv(\@fuzzyMatchWords);
    $distance = $tf->last_distance;

    #if the "distance" is less than 25% of the source data, take it as OK
    $distanceRatio = $distance / length($flowItemName);
    if ($distanceRatio < 0.25)
    {
      $dsItemID = $dsNameToIDHash{lc($nearest)};
      $matchedItemHash{$flowItemName} = $dsItemID;
      delete($unmatchedFlowItemHash{$flowItemName});
      $goodConfidenceMatchHash{$flowItemName} = 1;

      $q_flowItemName = $prepDB->quote($flowItemName);
      $q_dsItemName = $prepDB->quote($dsItemNameHash{$dsItemID});
      $query = "INSERT INTO prep.dim_matches \
          (flowID, dsID, dim, flowItem, dsItem, dsItemID, confidence) \
          VALUES ($flowID, $dsID, 'p', $q_flowItemName, $q_dsItemName, $dsItemID, 'good') \
          ON DUPLICATE KEY UPDATE dsItem=$q_dsItemName, dsItemID=$dsItemID, confidence='good'";
      $prepDB->do($query);
    }

    #if the "distance" is less than 35% of the source data, take it as
    #maybe OK
    elsif ($distanceRatio < 0.35)
    {
      $dsItemID = $dsNameToIDHash{lc($nearest)};
      $matchedItemHash{$flowItemName} = $dsItemID;
      delete($unmatchedFlowItemHash{$flowItemName});
      $lowConfidenceMatchHash{$flowItemName} = 1;
    }
  }

  #END FUZZY (Levenschtein/Darauma) AI


  #------------------------------------------------------------------------


  #
  #arrange the items into display order, with items needing most human
  #attention at top of display
  #

  #items we couldn't match go at top and are marked in red
  foreach $item (sort keys %unmatchedFlowItemHash)
  {
    push(@dispFlowItems, $item);
    $dispConfidenceLevel{$item} = "none";
  }

  #items we got matching signals for, but aren't sure about, marked in yellow
  foreach $item (sort keys %lowConfidenceMatchHash)
  {
    push(@dispFlowItems, $item);
    $dispConfidenceLevel{$item} = "low";
  }

  #items we're pretty sure about get displayed as normal list group items
  foreach $item (sort keys %goodConfidenceMatchHash)
  {
    push(@dispFlowItems, $item);
    $dispConfidenceLevel{$item} = "good";
  }


  #items we're 100% sure about go at the bottom in green
  foreach $item (sort keys %highConfidenceMatchHash)
  {
    push(@dispFlowItems, $item);
    $dispConfidenceLevel{$item} = "high";
  }


  foreach $item (@dispFlowItems)
  {
    #get the name of the matched item (or None if appropriate)
    $matchedItemID = $matchedItemHash{$item};
    if ($matchedItemID < 1)
    {
      $matchedItemID = 0;
      $matchedItemName = "$item <SMALL CLASS='text-muted'>New Item</SMALL>";
    }
    else
    {
      $matchedItemName = $dsItemNameHash{$matchedItemID};
    }

    if ($dispConfidenceLevel{$item} eq "none")
    {
      $htmlListItemColor = "list-group-item-danger";
    }
    elsif ($dispConfidenceLevel{$item} eq "low")
    {
      $htmlListItemColor = "list-group-item-warning";
    }
    elsif ($dispConfidenceLevel{$item} eq "high")
    {
      $htmlListItemColor = "list-group-item-success";
    }
    else
    {
      $htmlListItemColor = "";
    }

    $jsItemName = $item;
    $jsItemName =~ s/\'/\\'/g;

    print <<END_HTML;
              <DIV CLASS="list-group-item $htmlListItemColor">
                <DIV CLASS="row">
                  <DIV CLASS="col-10">
                    <DIV CLASS="row">
                      <DIV CLASS="col">
                        <I CLASS="align-baseline bi bi-arrow-90deg-down"></I>
                        <SPAN CLASS="fs-5">$item</SPAN><BR>
                        <I CLASS="bi bi-arrow-return-right"></I>
                        $matchedItemName
                      </DIV>
                    </DIV>
                  </DIV>
                  <DIV CLASS="col-2">
                    <BUTTON CLASS="btn btn-sm btn-primary w-100 mb-1" TYPE="button" onclick="showMatchDlg('$jsItemName', $matchedItemID)"><I CLASS="bi bi-pencil"></I> Edit Match</BUTTON>
                  </DIV>
                </DIV>
              </DIV>
END_HTML
  }

  print <<END_HTML;
            </DIV>
          </DIV>

          <P>&nbsp;</P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onclick="location.href='$cancelAction'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="submit" ID="btn-submit"><I CLASS="bi bi-pencil-square"></I> Apply</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
<P>
END_HTML

  print_html_footer();

#EOF
