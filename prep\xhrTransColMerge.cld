#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepRecipes;
use Lib::PrepUtils;
use Lib::WebUtils;


#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $flowID = $q->param('f');
  $jobID = $q->param('j');
  $colID = $q->param('col');

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #make sure we have write privs for this data flow
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this data flow.");
  }

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #build name of column containing the text
  $column = "column_" . $colID;

  #get the current column name for the basis of the split col names
  $query = "SELECT name FROM $masterColTable WHERE ID=$colID";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($colName) = $dbOutput->fetchrow_array;

  print <<END_HTML;
<SCRIPT>
function checkForm(form)
{
  \$("#btn-submit").prop('disabled', true);
  \$("#btn-submit").text("Please Wait...");
  return(true);
}
</SCRIPT>

<FORM METHOD="post" ACTION="transformProgress.cld" onsubmit="return checkForm(this);">
<INPUT TYPE="hidden" NAME="f" VALUE="$flowID">
<INPUT TYPE="hidden" NAME="j" VALUE="$jobID">
<INPUT TYPE="hidden" NAME="col" VALUE="$colID">
<INPUT TYPE="hidden" NAME="a" VALUE="TRANS-COL-MERGE">

  <DIV CLASS="modal-dialog modal-lg">
    <DIV CLASS="modal-content">

      <DIV CLASS="modal-header">
        <H5 CLASS="modal-title">Merge Columns</H5>
        <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
      </DIV>

      <DIV CLASS="modal-body">
        <TABLE STYLE="width:100%;">
          <TR>
            <TD>&nbsp;
          </TD>
          <TD>
            <LABEL FOR="innerText">Inner Text:</LABEL>
          </TD>
          <TD>
            <LABEL FOR="mergeCol">Column to Merge:</LABEL>
          </TD>
        </TR>
        <TR>
          <TD>
            <STRONG>$colName</STRONG>
          </TD>
          <TD>
            <INPUT TYPE="text" ID="innerText" NAME="innerText" CLASS="form-control" STYLE="width:5em;">
          </TD>
          <TD>
            <SELECT ID="mergeCol" NAME="mergeCol" CLASS="form-select">
END_HTML

  #get IDs of columns in display order
  @orderedCols = prep_flow_order_columns($prepDB, $flowID, $jobID);
  $colOrder = join(',', @orderedCols);

  $query = "SELECT ID, name FROM $masterColTable ORDER BY FIELD(ID, $colOrder)";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;

  while (($colID, $colName) = $dbOutput->fetchrow_array)
  {
    print(" <OPTION VALUE='$colName'>$colName</OPTION>\n");
  }

  print <<END_HTML;
            </SELECT>
          </TD>
        </TR>
      </TABLE>
    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="submit" ID="btn-submit"><I CLASS="bi bi-pencil-square"></I> Apply</BUTTON>
    </DIV>

  </DIV>
</DIV>

</FORM>
END_HTML

#EOF
