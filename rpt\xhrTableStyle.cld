#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::Reports;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $rptID = $q->param('rptID');
  $visID = $q->param('v');
  $tableStyle = $q->param('tableStyle');

  $db = KAPutil_connect_to_database();

  #make sure we have edit privs for this report
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this report.");
  }

  #extract the current table design (default is M2_2)
  $query = "SELECT design FROM visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute();
  KAPutil_handle_db_err($db, $status, $query);
  ($design) = $dbOutput->fetchrow_array;


  #########################################################################
  #
  #if we're being called to save the user's selected table design
  #

  if (length($tableStyle) > 1)
  {

    if ($tableStyle eq "none")
    {
      $design = reports_set_style($design, "tableStyle", "none");
      $design = reports_set_style($design, "headerFontColor", "#333333");
      $design = reports_set_style($design, "headerBgColor", "#ffffff");
      $design = reports_set_style($design, "headerWrap", "1");
      $design = reports_set_style($design, "headerFontSize", "11");
      $design = reports_set_style($design, "headerFont", "Helvetica");
      $design = reports_set_style($design, "headerOutline", "bottom");
      $design = reports_set_style($design, "valueFontColor", "#333333");
      $design = reports_set_style($design, "valueBgColor", "#ffffff");
      $design = reports_set_style($design, "valueAlternateFontColor", "#333333");
      $design = reports_set_style($design, "valueAlternateBgColor", "#ffffff");
      $design = reports_set_style($design, "valueWrapDimension", "0");
      $design = reports_set_style($design, "valueWrap", "0");
      $design = reports_set_style($design, "valueFontSize", "11");
      $design = reports_set_style($design, "valueFont", "Helvetica");
      $design = reports_set_style($design, "verticalGrid", "0");
      $design = reports_set_style($design, "horizontalGrid", "1");
      $design = reports_set_style($design, "horizontalGridColor", "#eaeaea");
      $design = reports_set_style($design, "horizontalGridWidth", "1");
      $design = reports_set_style($design, "gridPadding", "0");
    }
    elsif ($tableStyle eq "minimal")
    {
      $design = reports_set_style($design, "tableStyle", "minimal");
      $design = reports_set_style($design, "headerFontColor", "#333333");
      $design = reports_set_style($design, "headerBgColor", "#ffffff");
      $design = reports_set_style($design, "headerWrap", "1");
      $design = reports_set_style($design, "headerFontSize", "11");
      $design = reports_set_style($design, "headerFont", "Helvetica");
      $design = reports_set_style($design, "headerOutline", "bottom");
      $design = reports_set_style($design, "valueFontColor", "#333333");
      $design = reports_set_style($design, "valueBgColor", "#ffffff");
      $design = reports_set_style($design, "valueAlternateFontColor", "#333333");
      $design = reports_set_style($design, "valueAlternateBgColor", "#ffffff");
      $design = reports_set_style($design, "valueWrapDimension", "0");
      $design = reports_set_style($design, "valueWrap", "0");
      $design = reports_set_style($design, "valueFontSize", "11");
      $design = reports_set_style($design, "valueFont", "Helvetica");
      $design = reports_set_style($design, "verticalGrid", "0");
      $design = reports_set_style($design, "horizontalGrid", "1");
      $design = reports_set_style($design, "horizontalGridColor", "#eaeaea");
      $design = reports_set_style($design, "horizontalGridWidth", "1");
      $design = reports_set_style($design, "gridPadding", "3");
    }
    elsif ($tableStyle eq "boldheader")
    {
      $design = reports_set_style($design, "tableStyle", "boldheader");
      $design = reports_set_style($design, "headerFontColor", "#ffffff");
      $design = reports_set_style($design, "headerBgColor", "#333333");
      $design = reports_set_style($design, "headerWrap", "1");
      $design = reports_set_style($design, "headerFontSize", "11");
      $design = reports_set_style($design, "headerFont", "Helvetica");
      $design = reports_set_style($design, "headerOutline", "bottom");
      $design = reports_set_style($design, "valueFontColor", "#333333");
      $design = reports_set_style($design, "valueBgColor", "#ffffff");
      $design = reports_set_style($design, "valueAlternateFontColor", "#333333");
      $design = reports_set_style($design, "valueAlternateBgColor", "#ffffff");
      $design = reports_set_style($design, "valueWrapDimension", "0");
      $design = reports_set_style($design, "valueWrap", "0");
      $design = reports_set_style($design, "valueFontSize", "11");
      $design = reports_set_style($design, "valueFont", "Helvetica");
      $design = reports_set_style($design, "verticalGrid", "0");
      $design = reports_set_style($design, "horizontalGrid", "1");
      $design = reports_set_style($design, "horizontalGridColor", "#eaeaea");
      $design = reports_set_style($design, "horizontalGridWidth", "1");
      $design = reports_set_style($design, "gridPadding", "3");
    }
    elsif ($tableStyle eq "contrastaltrows")
    {
      $design = reports_set_style($design, "tableStyle", "contrastaltrows");
      $design = reports_set_style($design, "headerFontColor", "#ffffff");
      $design = reports_set_style($design, "headerBgColor", "#333333");
      $design = reports_set_style($design, "headerWrap", "1");
      $design = reports_set_style($design, "headerFontSize", "11");
      $design = reports_set_style($design, "headerFont", "Helvetica");
      $design = reports_set_style($design, "headerOutline", "bottom");
      $design = reports_set_style($design, "valueFontColor", "#ffffff");
      $design = reports_set_style($design, "valueBgColor", "#666666");
      $design = reports_set_style($design, "valueAlternateFontColor", "#333333");
      $design = reports_set_style($design, "valueAlternateBgColor", "#CCCCCC");
      $design = reports_set_style($design, "valueWrapDimension", "0");
      $design = reports_set_style($design, "valueWrap", "0");
      $design = reports_set_style($design, "valueFontSize", "11");
      $design = reports_set_style($design, "valueFont", "Helvetica");
      $design = reports_set_style($design, "verticalGrid", "0");
      $design = reports_set_style($design, "horizontalGrid", "1");
      $design = reports_set_style($design, "horizontalGridColor", "#eaeaea");
      $design = reports_set_style($design, "horizontalGridWidth", "1");
      $design = reports_set_style($design, "gridPadding", "3");
    }
    elsif ($tableStyle eq "flashyrows")
    {
      $design = reports_set_style($design, "tableStyle", "flashyrows");
      $design = reports_set_style($design, "headerFontColor", "#333333");
      $design = reports_set_style($design, "headerBgColor", "#ffffff");
      $design = reports_set_style($design, "headerWrap", "1");
      $design = reports_set_style($design, "headerFontSize", "11");
      $design = reports_set_style($design, "headerFont", "Helvetica");
      $design = reports_set_style($design, "headerOutline", "bottom");
      $design = reports_set_style($design, "valueFontColor", "#333333");
      $design = reports_set_style($design, "valueBgColor", "#99E3DD");
      $design = reports_set_style($design, "valueAlternateFontColor", "#333333");
      $design = reports_set_style($design, "valueAlternateBgColor", "#34C6BB");
      $design = reports_set_style($design, "valueWrapDimension", "0");
      $design = reports_set_style($design, "valueWrap", "0");
      $design = reports_set_style($design, "valueFontSize", "11");
      $design = reports_set_style($design, "valueFont", "Helvetica");
      $design = reports_set_style($design, "verticalGrid", "0");
      $design = reports_set_style($design, "horizontalGrid", "0");
      $design = reports_set_style($design, "gridPadding", "3");
    }
    elsif ($tableStyle eq "boldheaderflashrows")
    {
      $design = reports_set_style($design, "tableStyle", "boldheaderflashrows");
      $design = reports_set_style($design, "headerFontColor", "#ffffff");
      $design = reports_set_style($design, "headerBgColor", "#333333");
      $design = reports_set_style($design, "headerWrap", "1");
      $design = reports_set_style($design, "headerFontSize", "11");
      $design = reports_set_style($design, "headerFont", "Helvetica");
      $design = reports_set_style($design, "headerOutline", "bottom");
      $design = reports_set_style($design, "valueFontColor", "#333333");
      $design = reports_set_style($design, "valueBgColor", "#99E3DD");
      $design = reports_set_style($design, "valueAlternateFontColor", "#333333");
      $design = reports_set_style($design, "valueAlternateBgColor", "#34C6BB");
      $design = reports_set_style($design, "valueWrapDimension", "0");
      $design = reports_set_style($design, "valueWrap", "0");
      $design = reports_set_style($design, "valueFontSize", "11");
      $design = reports_set_style($design, "valueFont", "Helvetica");
      $design = reports_set_style($design, "verticalGrid", "0");
      $design = reports_set_style($design, "horizontalGrid", "0");
      $design = reports_set_style($design, "gridPadding", "3");
    }
    elsif ($tableStyle eq "sparse")
    {
      $design = reports_set_style($design, "tableStyle", "sparse");
      $design = reports_set_style($design, "headerFontColor", "#ffffff");
      $design = reports_set_style($design, "headerBgColor", "#333333");
      $design = reports_set_style($design, "headerWrap", "1");
      $design = reports_set_style($design, "headerFontSize", "11");
      $design = reports_set_style($design, "headerFont", "Helvetica");
      $design = reports_set_style($design, "headerOutline", "bottom");
      $design = reports_set_style($design, "valueFontColor", "#333333");
      $design = reports_set_style($design, "valueBgColor", "#ffffff");
      $design = reports_set_style($design, "valueAlternateFontColor", "#333333");
      $design = reports_set_style($design, "valueAlternateBgColor", "#ffffff");
      $design = reports_set_style($design, "valueWrapDimension", "0");
      $design = reports_set_style($design, "valueWrap", "0");
      $design = reports_set_style($design, "valueFontSize", "11");
      $design = reports_set_style($design, "valueFont", "Helvetica");
      $design = reports_set_style($design, "verticalGrid", "0");
      $design = reports_set_style($design, "horizontalGrid", "0");
      $design = reports_set_style($design, "gridPadding", "6");
    }
    elsif ($tableStyle eq "condensed")
    {
      $design = reports_set_style($design, "tableStyle", "condensed");
      $design = reports_set_style($design, "headerFontColor", "#ffffff");
      $design = reports_set_style($design, "headerBgColor", "#333333");
      $design = reports_set_style($design, "headerWrap", "1");
      $design = reports_set_style($design, "headerFontSize", "11");
      $design = reports_set_style($design, "headerFont", "Helvetica");
      $design = reports_set_style($design, "headerOutline", "bottom");
      $design = reports_set_style($design, "valueFontColor", "#333333");
      $design = reports_set_style($design, "valueBgColor", "#ffffff");
      $design = reports_set_style($design, "valueAlternateFontColor", "#333333");
      $design = reports_set_style($design, "valueAlternateBgColor", "#ffffff");
      $design = reports_set_style($design, "valueWrapDimension", "0");
      $design = reports_set_style($design, "valueWrap", "0");
      $design = reports_set_style($design, "valueFontSize", "11");
      $design = reports_set_style($design, "valueFont", "Helvetica");
      $design = reports_set_style($design, "verticalGrid", "1");
      $design = reports_set_style($design, "verticalGridColor", "#eaeaea");
      $design = reports_set_style($design, "verticalGridWidth", "1");
      $design = reports_set_style($design, "horizontalGrid", "1");
      $design = reports_set_style($design, "horizontalGridColor", "#eaeaea");
      $design = reports_set_style($design, "horizontalGridWidth", "1");
      $design = reports_set_style($design, "gridPadding", "0");
    }
    else #default
    {
      $design = reports_set_style($design, "tableStyle", "default");
      $design = reports_set_style($design, "headerFontColor", "#ffffff");
      $design = reports_set_style($design, "headerBgColor", "#333333");
      $design = reports_set_style($design, "headerWrap", "1");
      $design = reports_set_style($design, "headerFontSize", "11");
      $design = reports_set_style($design, "headerFont", "Helvetica");
      $design = reports_set_style($design, "headerOutline", "bottom");
      $design = reports_set_style($design, "valueFontColor", "#333333");
      $design = reports_set_style($design, "valueBgColor", "#ffffff");
      $design = reports_set_style($design, "valueAlternateFontColor", "#333333");
      $design = reports_set_style($design, "valueAlternateBgColor", "#efefef");
      $design = reports_set_style($design, "valueWrapDimension", "0");
      $design = reports_set_style($design, "valueWrap", "0");
      $design = reports_set_style($design, "valueFontSize", "11");
      $design = reports_set_style($design, "valueFont", "Helvetica");
      $design = reports_set_style($design, "verticalGrid", "1");
      $design = reports_set_style($design, "verticalGridColor", "#ffffff");
      $design = reports_set_style($design, "verticalGridWidth", "1");
      $design = reports_set_style($design, "horizontalGrid", "1");
      $design = reports_set_style($design, "horizontalGridColor", "#eaeaea");
      $design = reports_set_style($design, "horizontalGridWidth", "1");
      $design = reports_set_style($design, "gridPadding", "3");
    }

    $q_design = $db->quote($design);
    $query = "UPDATE visuals SET design = $q_design WHERE ID=$visID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    $cubeName = cube_id_to_name($db, $rptID);
    $dsID = cube_get_ds_id($db, $rptID);
    $dsName = ds_id_to_name($db, $dsID);

    utils_audit($db, $userID, "Changed table style", $dsID, $rptID, 0);
    $activity = "$first $last changed table style for $cubeName in $dsName";
    utils_slack($activity);

    exit;
  }


  ########################################################################
  #
  # Everything after this point is called to display the design selection dialog
  #

  #extract the current table design
  $tableStyle = reports_get_style($design, "tableStyle");
  if (length($tableStyle) < 1)
  {
    $tableStyle = "default";
  }

  print <<END_HTML;
<SCRIPT>
function submitForm()
{
  let tableStyle = document.getElementById('tableStyle').value;

  let url = "xhrTableStyle.cld?rptID=$rptID&v=$visID&tableStyle=" + tableStyle;

  \$.get(url, function(data, status)
  {
    location.href = "display.cld?rpt=$rptID&v=$visID";
  });
}
</SCRIPT>

<DIV CLASS="modal-dialog" STYLE="overflow-y: initial !important;">
  <DIV CLASS="modal-content">
    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Table Style</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">

      <FORM>
      <TABLE CLASS="mx-auto">
        <TR>
          <TD STYLE="text-align:right;">
            Style:&nbsp;
          </TD>
          <TD>
            <SELECT CLASS="form-select" NAME="tableStyle" ID="tableStyle">
              <OPTION VALUE="default">Default</OPTION>
              <OPTION VALUE="none">None</OPTION>
              <OPTION VALUE="minimal">Minimal</OPTION>
              <OPTION VALUE="boldheader">Bold Header</OPTION>
              <OPTION VALUE="contrastaltrows">Contrast alternating rows</OPTION>
              <OPTION VALUE="flashyrows">Flashy rows</OPTION>
              <OPTION VALUE="boldheaderflashrows">Bold header flashy rows</OPTION>
              <OPTION VALUE="sparse">Sparse</OPTION>
              <OPTION VALUE="condensed">Condensed</OPTION>
            </SELECT>
            <SCRIPT>
              \$("select#tableStyle").val("$tableStyle");
            </SCRIPT>
          </TD>
        </TR>
      </TABLE>
      </FORM>

    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="submitForm()"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML


#EOF
