#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Calculated Measure</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Please Wait...');
  return(true);
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Calculated Measure $measName</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');
  $measName = $q->param('measureName');
  $measType = $q->param('measType');
  $calcBeforeAgg = $q->param('calcBeforeAgg');
  $formula = $q->param('f');
  $add = $q->param('a');
  $insertID = $q->param('i');
  $measureID = $q->param('measID');

  #sanitize parameters
  $dsID = utils_sanitize_integer($dsID);
  if (!defined($dsID))
  {
    exit_early_error($session, "Invalid data source");
  }
  $measureID = utils_sanitize_integer($measureID);
  $measName = utils_sanitize_string($measName);

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  #make sure we have write privs for the data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to modify the data source.");
  }

  $dsSchema = "datasource_" . $dsID;

  #if we're editing an existing measure, grab the info we need
  if ((length($formula) < 1) && ($measureID > 0))
  {
    $query = "SELECT calculation FROM $dsSchema.measures WHERE ID=$measureID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($calculation) = $dbOutput->fetchrow_array;

    $calculation =~ m/.*?\|(.*)\|/;
    $formula = $1;
  }

  #get a hash of the measure names for display purposes
  %measureNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "m");

  #if we've been requested to add a value to the formula
  if (length($add) > 0)
  {

    #if we're clearing the most recent entry
    if ($add eq "ce")
    {

      #if we're deleting a measure
      if ($formula =~ m/^(.*)measure_\d+$/)
      {
        $formula = $1;
      }

      #else we're deleting a plus sign
      elsif($formula =~ m/pl$/)
      {
        chop($formula); chop($formula);
      }

      #else we're deleting a number
      else
      {
        chop($formula);
      }
    }

    #measures
    elsif ($add eq "m")
    {

      #make sure there's an operator of some kind in front of a measure
      if (length($formula) > 0)	#if we aren't the start of the formula
      {
        if (($formula =~ m/[\d\.\)]$/) || ($formula =~ m/measure_\d+$/))
        {
          $formula .= "*";
        }
      }

      $measureCol = "measure_" . $insertID;
      $formula .= $measureCol;
    }

    #make sure a decimal point has an integer in front of it
    elsif ($add eq ".")
    {
      if ($formula =~ m/[0-9]$/)
      {
        $formula .= $add;
      }
      else
      {
        $formula .= "0" . $add;
      }
    }

    #else if we're an operator
    elsif (($add eq "pl") || ($add eq "/") || ($add eq "*") || ($add eq "-"))
    {

      #don't let the formula start with a bare operator
      if (length($formula) < 1)
      {
        #do nothing - the user will probably figure it out on their own
      }

      #don't let two operators be next to each other
      elsif (($formula =~ m/\.$/) || ($formula =~ m/\+$/) || ($formula =~ m/\-$/)
          || ($formula =~ m/\/$/) || ($formula =~ m/\*$/))
      {
        #do nothing
      }

      #don't let an operator come immediately after an opening paranthesis
      elsif ($formula =~ m/\($/)
      {
        #do nothing
      }

      else	#we're good
      {
        $formula .= $add;
      }

    }

    #else just add the entry to the formula
    else
    {
      $formula .= $add;
    }
  }

  #convert the stored formula to something human-readable
  $dispFormula = $formula;
  $dispFormula =~ s/pl/\+/g;
  while ($dispFormula =~ m/(measure_\d+)/)
  {
    $measColName = $1;

    $measColName =~ m/measure_(\d+)/;
    $measID = $1;

    $formMeasName = $measureNameHash{$measID};

    $dispFormula =~ s/$measColName/$formMeasName/;
  }

  print <<END_HTML;
<SCRIPT>
function insertMeasure()
{
  let measureID = document.getElementById('measure').value;

  location.href='?ds=$dsID&measureName=$measName&measID=$measureID&measType=calc&calcBeforeAgg=$calcBeforeAgg&a=m&f=$formula&i=' + measureID;
}
</SCRIPT>

<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-10 col-lg-7 col-xl-6"> <!-- content -->

      <FORM METHOD="post" ACTION="calcMeasureSave.cld" onsubmit="return checkForm(this);">
      <INPUT TYPE="hidden" NAME="ds" VALUE="$dsID">
      <INPUT TYPE="hidden" NAME="measName" VALUE="$measName">
      <INPUT TYPE="hidden" NAME="measType" VALUE="$measType">
      <INPUT TYPE="hidden" NAME="calcBeforeAgg" VALUE="$calcBeforeAgg">
      <INPUT TYPE="hidden" NAME="formula" VALUE="$formula">
      <INPUT TYPE="hidden" NAME="measureID" VALUE="$measureID">


      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Manual Calculator Measure</DIV>
        <DIV CLASS="card-body">

          <INPUT CLASS="form-control" TYPE="text" NAME="dispFormula" ID="dispFormula"VALUE="$dispFormula" STYLE="width:90%;" readOnly=true>

          <P>
          <TABLE>
            <TR>
              <TD>
                <TABLE STYLE="border:1px solid lightblue;">
                  <TR>
                    <TD> </TD>
                    <TD><BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='?ds=$dsID&measureName=$measName&measID=$measureID&measType=calc&calcBeforeAgg=$calcBeforeAgg&a=(&f=$formula'">(</BUTTON></TD>
                    <TD><BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='?ds=$dsID&measureName=$measName&measID=$measureID&measType=calc&calcBeforeAgg=$calcBeforeAgg&a=)&f=$formula'">)</BUTTON></TD>
                    <TD><BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='?ds=$dsID&measureName=$measName&measID=$measureID&measType=calc&calcBeforeAgg=$calcBeforeAgg&a=ce&f=$formula'"><I CLASS="bi bi-backspace"></I></BUTTON></TD>
                  </TR>
                  <TR>
                    <TD><BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='?ds=$dsID&measureName=$measName&measID=$measureID&measType=calc&calcBeforeAgg=$calcBeforeAgg&a=7&f=$formula'">7</BUTTON></TD>
                    <TD><BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='?ds=$dsID&measureName=$measName&measID=$measureID&measType=calc&calcBeforeAgg=$calcBeforeAgg&a=8&f=$formula'">8</BUTTON></TD>
                    <TD><BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='?ds=$dsID&measureName=$measName&measID=$measureID&measType=calc&calcBeforeAgg=$calcBeforeAgg&a=9&f=$formula'">9</BUTTON></TD>
                    <TD><BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='?ds=$dsID&measureName=$measName&measID=$measureID&measType=calc&calcBeforeAgg=$calcBeforeAgg&a=/&f=$formula'">/</BUTTON></TD>
                  </TR>
                  <TR>
                    <TD><BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='?ds=$dsID&measureName=$measName&measID=$measureID&measType=calc&calcBeforeAgg=$calcBeforeAgg&a=4&f=$formula'">4</BUTTON></TD>
                    <TD><BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='?ds=$dsID&measureName=$measName&measID=$measureID&measType=calc&calcBeforeAgg=$calcBeforeAgg&a=5&f=$formula'">5</BUTTON></TD>
                    <TD><BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='?ds=$dsID&measureName=$measName&measID=$measureID&measType=calc&calcBeforeAgg=$calcBeforeAgg&a=6&f=$formula'">6</BUTTON></TD>
                    <TD><BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='?ds=$dsID&measureName=$measName&measID=$measureID&measType=calc&calcBeforeAgg=$calcBeforeAgg&a=*&f=$formula'">*</BUTTON></TD>
                  </TR>
                  <TR>
                    <TD><BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='?ds=$dsID&measureName=$measName&measType=calc&calcBeforeAgg=$calcBeforeAgg&a=1&f=$formula'">1</BUTTON></TD>
                    <TD><BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='?ds=$dsID&measureName=$measName&measType=calc&calcBeforeAgg=$calcBeforeAgg&a=2&f=$formula'">2</BUTTON></TD>
                    <TD><BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='?ds=$dsID&measureName=$measName&measType=calc&calcBeforeAgg=$calcBeforeAgg&a=3&f=$formula'">3</BUTTON></TD>
                    <TD><BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='?ds=$dsID&measureName=$measName&measType=calc&calcBeforeAgg=$calcBeforeAgg&a=-&f=$formula'">-</BUTTON></TD>
                  </TR>
                  <TR>
                    <TD> </TD>
                    <TD><BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='?ds=$dsID&measureName=$measName&measID=$measureID&measType=calc&calcBeforeAgg=$calcBeforeAgg&a=0&f=$formula'">0</BUTTON></TD>
                    <TD><BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='?ds=$dsID&measureName=$measName&measID=$measureID&measType=calc&calcBeforeAgg=$calcBeforeAgg&a=.&f=$formula'">.</BUTTON></TD>
                    <TD><BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='?ds=$dsID&measureName=$measName&measID=$measureID&measType=calc&calcBeforeAgg=$calcBeforeAgg&a=pl&f=$formula'">+</BUTTON></TD>
                  </TR>
                </TABLE>
              </TD>
              <TD STYLE="width:20px;">
                &nbsp;
              </TD>
              <TD>
                <SELECT CLASS="form-select" NAME="measure" ID="measure">
END_HTML

  foreach $id (sort {$measureNameHash{$a} cmp $measureNameHash{$b}} keys %measureNameHash)
  {
    if ($id != $measureID)
    {
      print(" <OPTION VALUE=$id>$measureNameHash{$id}</OPTION>\n");
    }
  }

  print <<END_HTML;
                </SELECT><BR>
                <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="insertMeasure();">Insert Measure</BUTTON>
              </TD>
            </TR>
          </TABLE>

          <P>&nbsp;<P>

          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="location.href='display.cld?ds=$dsID&dim=m'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-submit">Next <I CLASS="bi bi-arrow-right"></I></BUTTON>
          </DIV>

        </DIV>
      </DIV>

      </FORM>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
