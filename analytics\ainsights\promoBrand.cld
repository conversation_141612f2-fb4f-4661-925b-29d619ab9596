#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::DataSources;
use Lib::AInsights::AInsights;
use Lib::AInsights::Promo;
use Lib::AInsights::Utils;
use Lib::WebUtils;




#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: AInsights Brand Promotion</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/fontawesome-5.10.2/css/all.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<SCRIPT SRC="/fusioncharts-3.18.0/js/fusioncharts.js"></SCRIPT>
<SCRIPT SRC="/fusioncharts-3.18.0/js/themes/fusioncharts.theme.zune.js"></SCRIPT>

<SCRIPT>
let vpHeight = window.innerHeight - 50;
if (vpHeight < 400)
{
  vpHeight = 400;
}

</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">AInsights</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="insights.cld?pm=$priceModelID">$modelName</A></LI>
    <LI CLASS="breadcrumb-item active">Brand Promotion</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $priceModelID = $q->param('pm');
  $geoID = $q->param('g');

  AInsights_Utils_initialize_constants($priceModelID);

  #connect to the database
  $db = KAPutil_connect_to_database();

  $modelName = AInsights_ID_to_name($db, $priceModelID);
  $dsName = ds_id_to_name($db, $dsID);
  $dsID = AInsights_get_dsID($db, $priceModelID);

  $dsSchema = "datasource_" . $dsID;

  %prodNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "p");
  %geoNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "g");
  %timeNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "t");

  print_html_header();

  #make sure we have at least read privs for this pricing model
  $privs = AInsights_rights($db, $userID, $priceModelID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to view this model.");
  }

  #grab some basic info about the pricing model
  $ownBrandID = AInsights_Utils_get_own_brand_id($db, $priceModelID);
  $brandSegID = AInsights_Utils_get_brand_seg_id($db, $priceModelID);
  @geoIDs = AInsights_Utils_get_model_geo_ids($db, $priceModelID);

  #if we weren't passed a geography to display
  if ($geoID < 1)
  {

    #see if we have one to try in the analyst's cookie
    $geoID = $session->param("priceModelGeoSelection.$priceModelID");

    #if still nothing, just use the first geo
    if ($geoID < 1)
    {
      $geoID = $geoIDs[0];
    }
  }
  else
  {
    $session->param("priceModelGeoSelection.$priceModelID", "$geoID");
  }

  #get IDs for our key competitors
  ($compID1, $compID2) = AInsights_Utils_get_brand_comp_ids($db, $dsSchema, $geoID);

  %brandNameHash = DSRseg_get_segments_hash($db, $dsSchema, "p", $brandSegID);
  $ownBrandName = $brandNameHash{$ownBrandID};

  %brandMembershipHash = DSRseg_get_segment_membership_hash($db, $dsSchema, 'p', $brandSegID);

  #get the last 52 weeks' worth of time period IDs from the data source
  @recent52WeekIDs = AInsights_Utils_get_time_period_ids($db, $dsSchema, 52);
  $recent52wksTimeIDStr = join(',', @recent52WeekIDs);

  #output off-canvas data selector
  print <<END_HTML;
  <DIV CLASS="offcanvas offcanvas-start" TABINDEX="-1" ID="offcanvas-data-selector">
  <DIV CLASS="offcanvas-header">
    <H5 CLASS="offcanvas-title" ID="offcanvas-label-data-selector"></H5>
    <BUTTON TYPE="button" CLASS="btn-close text-reset" data-bs-dismiss="offcanvas"></BUTTON>
  </DIV>
  <DIV CLASS="offcanvas-body">
    <DIV>
      <DIV CLASS="card">
        <DIV CLASS="card-header">Focus Geography</DIV>
        <DIV CLASS="card-body">
         <DIV CLASS="list-group">
           <A HREF="overview.cld?pm=$priceModelID&g=$availableGeoID" CLASS="list-group-item list-group-item-action"><B>Overview</B></A>
END_HTML

  #determine which geographies didn't distribute our brand in the past 52 wks
  $query = "SELECT geographyID FROM $dsSchema.$AInsightsBrandTable \
      WHERE brandID=$ownBrandID AND ISNULL(avgDist52)";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  while (($id) = $dbOutput->fetchrow_array)
  {
    $noDistGeosHash{$id} = 1;
  }

  foreach $availableGeoID (@geoIDs)
  {
    $htmlListClass = "";
    if ($availableGeoID == $geoID)
    {
      $htmlListClass = "active";
    }
    elsif ($noDistGeosHash{$availableGeoID} > 0)
    {
      $htmlListClass = "list-group-item-secondary";
    }

    print <<END_HTML;
        <A HREF="?pm=$priceModelID&g=$availableGeoID" CLASS="list-group-item list-group-item-action $htmlListClass">$geoNameHash{$availableGeoID}</A>
END_HTML
  }

  print <<END_HTML;
          </DIV>
        </DIV>
      </DIV>

      <P>
      <FORM METHOD="post" ACTION="modelRefresh.cld">
      <INPUT TYPE="hidden" NAME="pm" VALUE="$priceModelID">
      <INPUT TYPE="hidden" NAME="a" VALUE="c">
      <INPUT TYPE="hidden" NAME="compGeo" VALUE="$geoID">
      <DIV CLASS="card">
        <DIV CLASS="card-header">Key Competitors</DIV>
        <DIV CLASS="card-body">
          <SELECT CLASS="form-select" NAME="comp1" ID="comp1"">
            <OPTION VALUE="auto">Automatically Detect</OPTION>
END_HTML

  ($compID1, $compID2) = AInsights_Utils_get_brand_comp_ids($db, $dsSchema, $geoID);

  foreach $brandID (sort {$brandNameHash{$a} cmp $brandNameHash{$b}} keys %brandNameHash)
  {
    print("  <OPTION VALUE='$brandID'>$brandNameHash{$brandID}\n");
  }

  print <<END_HTML;
          </SELECT>
          <SCRIPT>
          \$(document).ready(function()
          {
            \$('#comp1').val('$compID1');
          });
          </SCRIPT>

          <SELECT CLASS="form-select mt-2" NAME="comp2" ID="comp2">
          <OPTION VALUE="auto">Automatically Detect</OPTION>
END_HTML

  foreach $brandID (sort {$brandNameHash{$a} cmp $brandNameHash{$b}} keys %brandNameHash)
  {
    print("  <OPTION VALUE='$brandID'>$brandNameHash{$brandID}\n");
  }

  print <<END_HTML;
          </SELECT>
          <SCRIPT>
          \$(document).ready(function()
          {
            \$('#comp2').val('$compID2');
          });
          </SCRIPT>

          <DIV CLASS="form-check form-check-inline">
            <INPUT CLASS="form-check-input" NAME="comp-all" ID="comp-all" TYPE="checkbox">
            <LABEL CLASS="form-check-label" FOR="comp-all">Apply change to all geographies</LABEL>
          </DIV>

          <DIV CLASS="text-center mt-2">
            <BUTTON CLASS="btn btn-primary" TYPE="submit" ID="btn-submit-comp">Apply <I CLASS="bi bi-pencil-square""></I></BUTTON>
          </DIV>
        </DIV>
      </DIV>
      </FORM>
    </DIV>
  </DIV>
</DIV>
END_HTML

  print <<END_HTML;
<DIV CLASS="container-fluid">

  <DIV CLASS="row">
    <DIV CLASS="col">

      <H3>Promotion Insights for $ownBrandName in
      <A HREF="#" CLASS="text-decoration-none" data-bs-toggle="offcanvas" data-bs-target="#offcanvas-data-selector">$geoNameHash{$geoID}</A></H3>

      <P>&nbsp;</P>
END_HTML

  #handle the case where the category isn't distributed in this market
  $query = "SELECT avgDist52 FROM $dsSchema.$AInsightsBrandTable \
      WHERE brandID=0 AND geographyID=$geoID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($avgYearlyDistCategory) = $dbOutput->fetchrow_array;

  if ($avgYearlyDistCategory < 0.1)
  {
    print <<END_HTML;
      <DIV CLASS="alert alert-warning">
        This category isn't distributed in this market.
      </DIV>
    </DIV> <!-- row -->
  </DIV>  <!-- col -->

</DIV>

END_HTML
    exit;
  }



#-------------------- Overview of Promotion ------------------------------------

  #grab the overview insight for display
  $insight = AInsights_Utils_get_insight_html($db, $dsSchema,
      "promo", "brand", "overview", $geoID);

  #determine if our brand is carried in this geography
  $query = "SELECT avgDist52 FROM $dsSchema.$AInsightsBrandTable \
      WHERE brandID=$ownBrandID AND geographyID=$geoID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($avgYearlyDistOwnBrand) = $dbOutput->fetchrow_array;

  print <<END_HTML;

      <DIV CLASS="card">
        <DIV CLASS="card-body">

          <H4>Overview</H4>
          <P>
          $insight

          <TABLE CLASS="table table-bordered table-striped-columns">
            <TR>
              <TH>&nbsp</TH>
              <TH COLSPAN="2">Week</TH>
              <TH COLSPAN="2">Month</TH>
              <TH COLSPAN="2">Quarter</TH>
              <TH COLSPAN="2">Year</TH>
            </TR>
            <TR>
              <TH>&nbsp</TH>
              <TH>Promo Units</TH>
              <TH>% Change</TH>
              <TH>Promo Units</TH>
              <TH>% Change</TH>
              <TH>Promo Units</TH>
              <TH>% Change</TH>
              <TH>Promo Units</TH>
              <TH>% Change</TH>
            </TR>
END_HTML

  $brandSQLStr = "0,$ownBrandID";
  if ($compID1 > 0)
  {
    $brandSQLStr .= ",$compID1";
  }
  if ($compID2 > 0)
  {
    $brandSQLStr .= ",$compID2";
  }
  $query = "SELECT brandID, promoUnits52, promoUnits13, promoUnits4 FROM $dsSchema.$AInsightsBrandTable \
      WHERE geographyID=$geoID AND brandID IN ($brandSQLStr)";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  undef(%promoValueHash);
  while (($brandID, $promoUnits52, $promoUnits13, $promoUnits4) = $dbOutput->fetchrow_array)
  {
    $promoUnits52 = AInsights_Utils_html_format_number($promoUnits52, 0, 0);
    $promoUnits13 = AInsights_Utils_html_format_number($promoUnits13, 0, 0);
    $promoUnits4 = AInsights_Utils_html_format_number($promoUnits4, 0, 0);
    $promoValueHash{"$brandID.52"} = $promoUnits52;
    $promoValueHash{"$brandID.13"} = $promoUnits13;
    $promoValueHash{"$brandID.4"} = $promoUnits4;
  }

  ($mostRecentWeekID) = AInsights_Utils_get_time_period_ids($db, $dsSchema, 1);
  $query = "SELECT brandID, promoUnits FROM $dsSchema.$AInsightsBrandCube \
      WHERE geographyID=$geoID AND brandID IN ($brandSQLStr) AND timeID=$mostRecentWeekID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  while (($brandID, $promoUnits) = $dbOutput->fetchrow_array)
  {
    $promoUnits = AInsights_Utils_html_format_number($promoUnits, 0, 0);
    $promoValueHash{"$brandID.1"} = $promoUnits;
  }

  $query = "SELECT brandID, name, value FROM $dsSchema.$AInsightsBrandCalcTable \
      WHERE geographyID=$geoID AND name IN ('promo_units_pct_chg_week', 'promo_units_pct_chg_month', 'promo_units_pct_chg_quarter', 'promo_units_pct_chg_year')";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  undef(%nameIconHTMLHash);
  while (($brandID, $name, $value) = $dbOutput->fetchrow_array)
  {
    $hashKey = "$brandID.$name";
    $HRvalue = AInsights_Utils_html_format_number($value, 1, 0);
    if ($value eq "-0.0")
    {
      $HRvalue = "0.0";
    }
    if ($value > 0)
    {
      $nameIconHTMLHash{$hashKey} = "<I CLASS='bi bi-arrow-up-square-fill text-success'></I> $HRvalue%";
    }
    elsif ($value < 0)
    {
      $nameIconHTMLHash{$hashKey} = "<I CLASS='bi bi-arrow-down-square-fill text-warning'></I> $HRvalue%";
    }
    elsif ($value == 0)
    {
      $nameIconHTMLHash{$hashKey} = "<I CLASS='bi bi-square-fill text-muted'></I> $HRvalue%";
    }
    else
    {
      $nameIconHTMLHash{$hashKey} = "$HRvalue%";
    }
  }

  print <<END_HTML;
            <TR>
              <TH>Category</TH>
              <TD CLASS="text-right">$promoValueHash{"0.1"}</TD>
              <TD CLASS="text-right">$nameIconHTMLHash{"0.promo_units_pct_chg_week"}</TD>
              <TD CLASS="text-right">$promoValueHash{"0.4"}</TD>
              <TD CLASS="text-right">$nameIconHTMLHash{"0.promo_units_pct_chg_month"}</TD>
              <TD CLASS="text-right">$promoValueHash{"0.13"}</TD>
              <TD CLASS="text-right">$nameIconHTMLHash{"0.promo_units_pct_chg_quarter"}</TD>
              <TD CLASS="text-right">$promoValueHash{"0.52"}</TD>
              <TD CLASS="text-right">$nameIconHTMLHash{"0.promo_units_pct_chg_year"}</TD>
            </TR>
END_HTML

  if ($avgYearlyDistOwnBrand > 0)
  {
    print <<END_HTML;
            <TR>
              <TH>$ownBrandName</TH>
              <TD CLASS="text-right">$promoValueHash{"$ownBrandID.1"}</TD>
              <TD CLASS="text-right">$nameIconHTMLHash{"$ownBrandID.promo_units_pct_chg_week"}</TD>
              <TD CLASS="text-right">$promoValueHash{"$ownBrandID.4"}</TD>
              <TD CLASS="text-right">$nameIconHTMLHash{"$ownBrandID.promo_units_pct_chg_month"}</TD>
              <TD CLASS="text-right">$promoValueHash{"$ownBrandID.13"}</TD>
              <TD CLASS="text-right">$nameIconHTMLHash{"$ownBrandID.promo_units_pct_chg_quarter"}</TD>
              <TD CLASS="text-right">$promoValueHash{"$ownBrandID.52"}</TD>
              <TD CLASS="text-right">$nameIconHTMLHash{"$ownBrandID.promo_units_pct_chg_year"}</TD>
            </TR>
END_HTML
  }

  if ($compID1 > 0)
  {
    print <<END_HTML;
            <TR>
              <TH>$brandNameHash{$compID1}</TH>
              <TD CLASS="text-right">$promoValueHash{"$compID1.1"}</TD>
              <TD CLASS="text-right">$nameIconHTMLHash{"$compID1.promo_units_pct_chg_week"}</TD>
              <TD CLASS="text-right">$promoValueHash{"$compID1.4"}</TD>
              <TD CLASS="text-right">$nameIconHTMLHash{"$compID1.promo_units_pct_chg_month"}</TD>
              <TD CLASS="text-right">$promoValueHash{"$compID1.13"}</TD>
              <TD CLASS="text-right">$nameIconHTMLHash{"$compID1.promo_units_pct_chg_quarter"}</TD>
              <TD CLASS="text-right">$promoValueHash{"$compID1.52"}</TD>
              <TD CLASS="text-right">$nameIconHTMLHash{"$compID1.promo_units_pct_chg_year"}</TD>
            </TR>
END_HTML
  }

  if ($compID2 > 0)
  {
    print <<END_HTML;
            <TR>
              <TH>$brandNameHash{$compID2}</TH>
              <TD CLASS="text-right">$promoValueHash{"$compID2.1"}</TD>
              <TD CLASS="text-right">$nameIconHTMLHash{"$compID2.promo_units_pct_chg_week"}</TD>
              <TD CLASS="text-right">$promoValueHash{"$compID2.4"}</TD>
              <TD CLASS="text-right">$nameIconHTMLHash{"$compID2.promo_units_pct_chg_month"}</TD>
              <TD CLASS="text-right">$promoValueHash{"$compID2.13"}</TD>
              <TD CLASS="text-right">$nameIconHTMLHash{"$compID2.promo_units_pct_chg_quarter"}</TD>
              <TD CLASS="text-right">$promoValueHash{"$compID2.52"}</TD>
              <TD CLASS="text-right">$nameIconHTMLHash{"$compID2.promo_units_pct_chg_year"}</TD>
            </TR>
END_HTML
  }

  print <<END_HTML;
          </TABLE>

        </DIV>
      </DIV>

END_HTML


#------------------- Unit Promotion Trends -------------------------

  $insight = AInsights_Utils_get_insight_html($db, $dsSchema,
      "promo", "brand", "trends", $geoID);

  print <<END_HTML;

      <P>&nbsp;</P>

      <DIV CLASS="card">
        <DIV CLASS="card-body">

          <H4>Unit Promotion Trends</H4>
          <P>
          $insight

          <DIV ID="chart-line-trends"></DIV>
          <SCRIPT>
            let trendsChart = new FusionCharts({'type': 'LogMSLine', 'width': '99%', 'height': '450', 'dataFormat': 'json'});
            trendsChart.setJSONUrl('ajaxBrandPromoCharts.cld?pm=$priceModelID&c=trnd&g=$geoID');
            trendsChart.render('chart-line-trends');
          </SCRIPT>


        </DIV>
      </DIV>
END_HTML



#------------------- Promotion Vehicle Breakdown for Own Brand -----------------

print <<END_HTML;
    <P>&nbsp;</P>
    <DIV CLASS="card">
      <DIV CLASS="card-body">

        <H4>Promotion Vehicle Breakdown for $ownBrandName</H4>
        <P>
END_HTML

print <<END_HTML;

        <DIV ID="chart-area-vehicle-own"></DIV>
        <SCRIPT>
          let ownBrandVehicleBreakdownChart = new FusionCharts({'type': 'stackedarea2d', 'width': '100%', 'height': '550', 'dataFormat': 'json'});
          ownBrandVehicleBreakdownChart.setJSONUrl('ajaxBrandPromoCharts.cld?pm=$priceModelID&c=own_vehicle&g=$geoID');
          ownBrandVehicleBreakdownChart.render('chart-area-vehicle-own');
        </SCRIPT>
      </DIV>
    </DIV>
END_HTML



#------------------- Promotion for all brands in geo -----------------

  #get number of bars we're going to graph, and size chart appropriately
  $query = "SELECT COUNT(promoUnits52) FROM $dsSchema.$AInsightsBrandTable \
      WHERE promoUnits52 > 0 AND geographyID=$geoID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($barsToGraph) = $dbOutput->fetchrow_array;
  $chartHeight = $barsToGraph * 20 + 75;

  $insight = AInsights_Utils_get_insight_html($db, $dsSchema,
      "promo", "brand", "brands", $geoID);

  print <<END_HTML;
    <P>&nbsp;</P>
    <DIV CLASS="card">
      <DIV CLASS="card-body">

        <H4>Brand Promotion in $geoNameHash{$geoID}</H4>
        <P>
END_HTML

  print <<END_HTML;

        <DIV ID="chart-bar-brands"></DIV>
        <SCRIPT>
          let brandPromoChart = new FusionCharts({'type': 'stackedbar2d', 'width': '100%', 'height': '$chartHeight', 'dataFormat': 'json'});
          brandPromoChart.setJSONUrl('ajaxBrandPromoCharts.cld?pm=$priceModelID&c=brands_promo&g=$geoID');
          brandPromoChart.render('chart-bar-brands');
        </SCRIPT>
      </DIV>
    </DIV>
END_HTML



#------------------- Own Brand Geographical Promotion -----------------------

print <<END_HTML;
    <P>&nbsp;</P>
    <DIV CLASS="card">
      <DIV CLASS="card-body">

        <H4>$ownBrandName Promotion Across Geographies</H4>
        <P>
END_HTML

  #get number of bars we're going to graph, and size chart appropriately
  $query = "SELECT COUNT(DISTINCT geographyID) FROM $dsSchema.$AInsightsBrandTable \
      WHERE promoUnits52 > 0 AND brandID = $ownBrandID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($barsToGraph) = $dbOutput->fetchrow_array;
  $chartHeight = $barsToGraph * 20 + 105;

  $insight = AInsights_Utils_get_insight_html($db, $dsSchema,
      "promo", "brand", "geos", 0);

print <<END_HTML;
        $insight
        <DIV ID="chart-bar-geographies"></DIV>
        <SCRIPT>
          let ownBrandGeoChart = new FusionCharts({'type': 'stackedbar2d', 'width': '100%', 'height': '$chartHeight', 'dataFormat': 'json'});
          ownBrandGeoChart.setJSONUrl('ajaxBrandPromoCharts.cld?pm=$priceModelID&c=brand_geos&g=$geoID');
          ownBrandGeoChart.render('chart-bar-geographies');
        </SCRIPT>
      </DIV>
    </DIV>
END_HTML



#------------------- Own Brand Item Promotion -----------------------

print <<END_HTML;
    <P>&nbsp;</P>
    <DIV CLASS="card">
      <DIV CLASS="card-body">

        <H4>$ownBrandName Item Promotion</H4>
        <P>
END_HTML

  #get number of bars we're going to graph, and size chart appropriately
  $query = "SELECT COUNT(productID) FROM $dsSchema.$AInsightsItemTable \
      WHERE promoUnits52 > 0 AND brandID = $ownBrandID AND geographyID=$geoID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($barsToGraph) = $dbOutput->fetchrow_array;
  $chartHeight = $barsToGraph * 20 + 105;

  $insight = AInsights_Utils_get_insight_html($db, $dsSchema,
      "promo", "brand", "own_items", $geoID);

print <<END_HTML;
        $insight
        <DIV ID="chart-bar-items"></DIV>
        <SCRIPT>
          let ownBrandItemChart = new FusionCharts({'type': 'stackedbar2d', 'width': '100%', 'height': '$chartHeight', 'dataFormat': 'json'});
          ownBrandItemChart.setJSONUrl('ajaxBrandPromoCharts.cld?pm=$priceModelID&c=brand_items&g=$geoID');
          ownBrandItemChart.render('chart-bar-items');
        </SCRIPT>
      </DIV>
    </DIV>
END_HTML



  #------------------------ Brand Insights -----------------------------

  $brandPromoInsight = AInsights_Utils_get_insight_html($db, $dsSchema,
    "promo", "brand", "own_items", $geoID);

  print <<END_HTML;
      <P>&nbsp;</P>

      <DIV CLASS="card">
        <DIV CLASS="card-body">

          <H4>Market Competitors</H4>
          <P>
          $brandPromoInsight
END_HTML

  print <<END_HTML;
          <P>
          <DIV ID="data-collapse-brand">
            <DIV CLASS="card border-primary mx-auto">
              <DIV CLASS="card-header bg-primary text-white">
                <A CLASS="card-link text-white text-decoration-none" data-bs-toggle="collapse" HREF="#collapse-brand-data-table">Brand Promotion Data Table</A>
                <I CLASS="fas fa-caret-down"></I>
              </DIV>
              <DIV ID="collapse-brand-data-table" CLASS="collapse" data-bs-parent="#data-collapse-brand">
                <DIV CLASS="card-body">
                  <TABLE CLASS="table table-bordered table-striped">
                    <TR>
                      <TH>Brand</TH>
                      <TH>Display Units</TH>
                      <TH>Feature Units</TH>
                      <TH>Feature & Display Units</TH>
                      <TH>Price Reduction Units</TH>
                      <TH>Total Units</TH>
                    </TR>
END_HTML

  $query = "SELECT brandID, promoDispUnits52, promoFeatUnits52, promoFeatDispUnits52, promoPriceDecrUnits52, units52 FROM $dsSchema.$AInsightsBrandTable \
      WHERE geographyID=$geoID AND brandID != 0 \
      ORDER BY units52 DESC";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  while (($brandID, $promoDispUnits, $promoFeatUnits, $promoFeatDispUnits, $promoPriceDecrUnits, $units) = $dbOutput->fetchrow_array)
  {

    #skip anything that wasn't actually sold
    if ($units < 5)
    {
      next;
    }

    #format numerical values
    $dispPromoDispUnits = AInsights_Utils_html_format_number($promoDispUnits, 0);
    $dispPromoFeatUnits = AInsights_Utils_html_format_number($promoFeatUnits, 0);
    $dispPromoFeatDispUnits = AInsights_Utils_html_format_number($promoFeatDispUnits, 0);
    $dispPromoPriceDecrUnits = AInsights_Utils_html_format_number($promoPriceDecrUnits, 0);
    $dispUnits = AInsights_Utils_html_format_number($units, 0);

    if ($brandID == $ownBrandID)
    {
      $htmlTRColor = "table-success";
    }
    else
    {
      $htmlTRColor = "";
    }

    print <<END_HTML;
                    <TR CLASS="$htmlTRColor">
                      <TD>$brandNameHash{$brandID}</TD>
                      <TD CLASS="text-end">$dispPromoDispUnits</TD>
                      <TD CLASS="text-end">$dispPromoFeatUnits</TD>
                      <TD CLASS="text-end">$dispPromoFeatDispUnits</TD>
                      <TD CLASS="text-end">$dispPromoPriceDecrUnits</TD>
                      <TD CLASS="text-end">$dispUnits</TD>
                    </TR>
END_HTML
  }

  print <<END_HTML;
                  </TABLE>
                </DIV>
              </DIV>
            </DIV>
          </DIV>
        </DIV>
      </DIV>
END_HTML



  #--------------------- Item Promotion Insights -------------------------

  $insight = AInsights_Utils_get_insight_html($db, $dsSchema,
    "promo", "brand", "items", $geoID);

  print <<END_HTML;
      <P>&nbsp;</P>

      <DIV CLASS="card">
        <DIV CLASS="card-body">

          <H4>Item Promotion</H4>
          <P>
          $insight

          <P>
          <DIV ID="data-collapse-item">
            <DIV CLASS="card border-primary mx-auto">
              <DIV CLASS="card-header bg-primary text-white">
                <A CLASS="card-link text-white text-decoration-none" data-bs-toggle="collapse" HREF="#collapse-item-data-table">Item Promotion Data Table</A>
                <I CLASS="fas fa-caret-down"></I>
              </DIV>
              <DIV ID="collapse-item-data-table" CLASS="collapse" data-bs-parent="#data-collapse-item">
                <DIV CLASS="card-body">

                  <TABLE CLASS="table table-bordered table-striped">
                    <TR>
                      <TH>Item</TH>
                      <TH>Brand</TH>
                      <TH>Display Units</TH>
                      <TH>Feature Units</TH>
                      <TH>Feature & Display Units</TH>
                      <TH>Price Reduction Units</TH>
                      <TH>Total Units</TH>
                    </TR>
END_HTML

  $query = "SELECT productID, promoDispUnits52, promoFeatUnits52, promoFeatDispUnits52, promoPriceDecrUnits52, units52 FROM $dsSchema.$AInsightsItemTable \
      WHERE geographyID=$geoID\
      ORDER BY units52 DESC";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  while (($prodID, $promoDispUnits, $promoFeatUnits, $promoFeatDispUnits, $promoPriceDecrUnits, $units) = $dbOutput->fetchrow_array)
  {

    #get product's brand membership and name
    $brandID = $brandMembershipHash{$prodID};

    #format numerical values
    $dispPromoDispUnits = AInsights_Utils_html_format_number($promoDispUnits, 0);
    $dispPromoFeatUnits = AInsights_Utils_html_format_number($promoFeatUnits, 0);
    $dispPromoFeatDispUnits = AInsights_Utils_html_format_number($promoFeatDispUnits, 0);
    $dispPromoPriceDecrUnits = AInsights_Utils_html_format_number($promoPriceDecrUnits, 0);
    $dispUnits = AInsights_Utils_html_format_number($units, 0);

    if ($brandID == $ownBrandID)
    {
      $htmlTRColor = "table-success";
    }
    else
    {
      $htmlTRColor = "";
    }

    print <<END_HTML;
                    <TR CLASS="$htmlTRColor">
                      <TD>$prodNameHash{$prodID}</TD>
                      <TD>$brandNameHash{$brandID}</TD>
                      <TD CLASS="text-end">$dispPromoDispUnits</TD>
                      <TD CLASS="text-end">$dispPromoFeatUnits</TD>
                      <TD CLASS="text-end">$dispPromoFeatDispUnits</TD>
                      <TD CLASS="text-end">$dispPromoPriceDecrUnits</TD>
                      <TD CLASS="text-end">$dispUnits</TD>
                    </TR>
END_HTML
  }

  print <<END_HTML;
                  </TABLE>
                </DIV>
              </DIV>
            </DIV>
          </DIV>
        </DIV>
      </DIV>
END_HTML

#--------------------------


  print <<END_HTML;
    </DIV> <!-- row -->
  </DIV>  <!-- col -->

</DIV>
END_HTML

  print_html_footer();

  #flush the CGI session info out to storage
  $session->flush();

#  AInsights_audit($db, $userID, $priceModelID, "Viewed details for $prodNameHash{$prodID} in $geoNameHash{$geoID}");
#  $activity = "ELASTICITY: $first $last viewed model details for $prodNameHash{$prodID} / $geoNameHash{$geoID} in elasticity $modelName in $dsName";
#  utils_slack($activity);

#EOF
