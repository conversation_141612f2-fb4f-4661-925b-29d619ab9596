#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($kapDB);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Delete Data Flow</TITLE>
<LINK REL="SHORTCUT ICON" HREF="$Lib::KoalaConfig::kapHostURL/favicon.png">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Please Wait...');
  return(true);
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($kapDB, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="$Lib::KoalaConfig::kapHostURL/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">Data Prep</A></LI>
    <LI CLASS="breadcrumb-item">$flowName</LI>
    <LI CLASS="breadcrumb-item active">Delete Data Flow</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser and get the forecast to be deleted
  $q = new CGI;
  $flowID = $q->param('f');

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #get the name of the data source
  $flowName = prep_flow_id_to_name($prepDB, $flowID);

  print_html_header();

  #make sure we're the owner of this data flow
  $query = "SELECT userID FROM prep.flows WHERE ID=$flowID";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($dsOwnerID) = $dbOutput->fetchrow_array;

  if (($dsOwnerID != $userID) && ($acctType < 5))
  {
    exit_error("You don't have privileges to delete this data flow - you're not the data flow owner.");
  }

  #make sure the flow doesn't have any active jobs in it
  $query = "SELECT ID FROM prep.jobs \
      WHERE flowID=$flowID AND state NOT IN ('LOADED', 'ERROR', 'PARSE-WAIT', 'DATATYPE-WAIT')";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  if ($status > 0)
  {
    exit_warning("There are jobs running in this flow - please wait for them to complete before deleting it.");
  }

  #generate the random number we're going to make the user type to delete
  $random = 999 + int(rand(8999));

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-11 col-lg-8 col-xl-7"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Delete Data Flow</DIV>
        <DIV CLASS="card-body">

          <FORM METHOD="post" ACTION="deleteFlow.cld" onsubmit="return checkForm(this);">
          <INPUT TYPE="hidden" NAME="f" VALUE="$flowID">
          <INPUT TYPE="hidden" NAME="random" VALUE="$random">
          Are you sure you want to delete the data flow <B>$flowName</B>?

          <P>&nbsp;</P>
          To continue, please enter the confirmation number displayed below:

          <H3 CLASS="text-primary text-center">$random</H3>

          <P>&nbsp;</P>
          <DIV CLASS="row mt-3">
            <DIV CLASS="col-auto mt-2">
              <LABEL FOR="userRandom">Confirmation number:</LABEL>
            </DIV>
            <DIV CLASS="col-auto">
              <INPUT CLASS="form-control" NAME="userRandom" ID="userRandom" STYLE="width:5em;" pattern="[0-9]{4}" required autocomplete="false">
            </DIV>
          </DIV>
          <P>&nbsp;</P>

          <DIV CLASS="text-center">
            <BUTTON TYPE="button" CLASS="btn btn-secondary" onclick="location.href='main.cld'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-danger" TYPE="SUBMIT" ID="btn-submit"><I CLASS="bi bi-trash"></I> Delete Flow</BUTTON>
          </DIV>

          </FORM>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
