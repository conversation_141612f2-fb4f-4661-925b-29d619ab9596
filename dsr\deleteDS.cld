#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Delete Data Source</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item">$dsName</LI>
    <LI CLASS="breadcrumb-item active">Delete Data Source</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser and get the forecast to be deleted
  $q = new CGI;
  $dsID = $q->param('dsID');
  $random = $q->param('random');
  $userRandom = $q->param('userRandom');

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #sanitize parameters
  $dsID = utils_sanitize_integer($dsID);
  $random = utils_sanitize_integer($random);
  $userRandom = utils_sanitize_integer($userRandom);

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  #make sure we're the owner of this data source
  $dsOwnerID = ds_get_owner($db, $dsID);

  if (($dsOwnerID != $userID) && ($acctType < 5))
  {
    exit_error("You don't have privileges to delete this data source - you're not the data source owner.");
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-11 col-md-8 col-lg-6 col-xl-5"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Delete Data Source</DIV>
        <DIV CLASS="card-body">
END_HTML

  #make sure the user correctly typed the confirmation code
  if ($random != $userRandom)
  {
    print <<END_HTML;
          You entered an incorrect confirmation code - the selected data source has not been deleted.
END_HTML
  }

  else
  {
    $query = "UPDATE dataSources SET deleted = 1 WHERE ID=$dsID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    #remove any user feed items related to the data source
    $query = "DELETE FROM app.feed WHERE dsID=$dsID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    print <<END_HTML;
          The data source $dsName has been deleted.
END_HTML
  }

  print <<END_HTML;
          <P>

          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" onClick="location.href='main.cld'"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

  $activity = "$first $last deleted data source $dsName";
  utils_audit($db, $userID, "Deleted data source $dsName", $dsID, 0, 0);

  utils_slack($activity);

#EOF
