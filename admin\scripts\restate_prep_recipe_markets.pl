#!/usr/bin/perl

use lib "/opt/apache/app/";

use DBI;
use Text::CSV;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::PrepUtils;
use Lib::WebUtils;



%restateHash = (
"ALBSCO Acme Div Rem" => "ALBSCO Acme Rem",
"ALBSCO Acme Div TA" => "ALBSCO Acme TA",
"ALBSCO Acme Div xAOC Rem" => "ALBSCO Acme xAOC Rem",
"ALBSCO Eastern Div Rem" => "ALBSCO Eastern Rem",
"ALBSCO Eastern Div TA" => "ALBSCO Eastern TA",
"ALBSCO Eastern Div xAOC Rem" => "ALBSCO Eastern xAOC Rem",
);


  #connect to the database
  $prepDB = PrepUtils_connect_to_database();

  #grab the ID and name of every data flow on the system using AOD extract
  $query = "SELECT ID, name FROM prep.flows WHERE sourceInfo LIKE 'FTP=nielsen%'";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  while (($flowID, $flowName) = $dbOutput->fetchrow_array)
  {
    $flowNameHash{$flowID} = $flowName;
  }

  $csv = Text::CSV->new( {binary => 1} );

  #cycle through every data flow's recipe, looking for a market trim step
  foreach $flowID (keys %flowNameHash)
  {
    $query = "SELECT step, action FROM prep.recipes \
        WHERE flowID=$flowID AND action LIKE 'TRANS-COL-TRIM-DATA|COL=Market Display Name|%'";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;

    while (($stepID, $action) = $dbOutput->fetchrow_array)
    {
      if ($action =~ m/^TRANS-COL-TRIM-DATA\|COL=(.*)\|OP=(.*)\|DATA=(.*)$/)
      {
        $colName = $1;
        $matchOp = $2;
        $dataValues = $3;
      }
      else
      {
        print "INVALID RECIPE STEP, NEXT!\n";
      }

      $csv->parse($dataValues);
      @items = $csv->fields();
      undef(@newValArray);
      foreach $item (@items)
      {

        #normally we'd apply a hash-based map here, but this particular
        #restatement is easier to deal with by using heuristics
=pod
        if ($item =~ m/^ALB\/SFY (.*)$/)
        {
          $item = "ALBSCO $1";
        }
        if ($item =~ m/^SUPERVALU (.*)$/)
        {
          $item = "LEGACY SV $1";
        }
=cut

=pod
        #add the additional items required when a geography has "split" into
        #multiple new geographies
        #NB: the original item will be pushed onto the array below, so we only
        #    need to push any additional geographies created by the split
        if ($item eq "LEGACY SV East Mid Atlantic Division TA")
        {
          push(@newValArray, "UNFI CONVL Atlantic Region TA");
          push(@newValArray, "UNFI CONVL Atlantic Region REM");
          push(@newValArray, "UNFI CONVL Atlantic Region xAOC Rem");
        }
=cut


        #replace the old value with the new one from the hash (if present)
        if (length($restateHash{$item}) > 1)
        {
          $item = $restateHash{$item};
        }

=pod
        #remove the no-longer-included markets from the hash
        if ($item eq "Ingles Total Rem")
        {
          next;
        }
        if ($item eq "Ingles Total TA")
        {
          next;
        }
        if ($item eq "Ingles Total xAOC Rem")
        {
          next;
        }
=cut

        #put the (possibly updated) item in the array to be written back out
        push(@newValArray, $item);
      }

      #turn the array of new values into the recipe step and save
      $csv->combine(@newValArray);
      $dataFields = $csv->string;
      $step = "TRANS-COL-TRIM-DATA|COL=$colName|OP=$matchOp|DATA=$dataFields";

      print("$flowID $flowNameHash{$flowID}\n$step\n\n");

      $q_action = $prepDB->quote($step);
      $query = "UPDATE prep.recipes SET action=$q_action \
          WHERE flowID=$flowID AND step=$stepID";
      $prepDB->do($query);
    }
  }

#EOF
