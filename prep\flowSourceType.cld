#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($kapDB);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: $action Data Flow Source</TITLE>
<LINK REL="SHORTCUT ICON" HREF="$Lib::KoalaConfig::kapHostURL/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($kapDB, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="$Lib::KoalaConfig::kapHostURL/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">Data Prep</A></LI>
    <LI CLASS="breadcrumb-item">$flowName</LI>
    <LI CLASS="breadcrumb-item active">Data Source Type</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $flowID = $q->param('f');

  #determine if we're creating or editing a data flow
  if ($flowID > 0)
  {
    $action = "Edit";
  }
  else
  {
    $action = "New";
  }

  #connect to the database
  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #if we're editing a data flow, get its current values from the database
  $source = "Manual";
  if ($flowID > 0)
  {
    $query = "SELECT name, description, source FROM prep.flows WHERE ID=$flowID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($name, $description, $source) = $dbOutput->fetchrow_array;
  }

  if (length($name) > 1)
  {
    $flowName = $name;
  }
  else
  {
    $flowName = "New Data Flow";
  }

  print_html_header();

  #if the system is overloaded, have the user try again later
  $activeJobs = PrepUtils_active_job_count($prepDB);
  if (($activeJobs >= ($Lib::KoalaConfig::prepCores * 1.25)) && ($acctType < 10))
  {
    exit_warning("Whoa! It looks like your Data Prep cloud is being overused. Wait a little bit, and then try again.")
  }

  #don't let a single user help themselves to all resources
  if (($activeJobs >= ($Lib::KoalaConfig::prepCores * 0.75)) && ($acctType < 10))
  {
    $query = "SELECT COUNT(*) FROM prep.jobs \
        WHERE state NOT IN ('LOADED', 'ERROR') AND userID=$userID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($runningJobs) = $dbOutput->fetchrow_array;
    if ($runningJobs > 0)
    {
      exit_warning("Whoa! It looks like your Data Prep cloud is heavily loaded, and you already have at least one active job. Wait for that job to finish, and then try again.")
    }
  }

  #make sure the prep cloud isn't out of storage
  $usagePct = prep_flow_storage($acctType);
  if ($usagePct > 99)
  {
    exit_error("Your Koala Data Prep cloud is out of storage - please contact your Koala account manager to order more.")
  }

  #make sure we have write privs for this data flow
  if ($flowID > 0)
  {
    $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
    if ($privs ne "W")
    {
      exit_error("You don't have privileges to edit this data flow.");
    }
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-10 col-lg-7 col-xl-6"> <!-- content -->

      <FORM METHOD="post" ACTION="flowCreateRedir.cld">
      <INPUT TYPE="hidden" NAME="f" VALUE="$flowID">

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">$action Data Flow</DIV>
        <DIV CLASS="card-body">
          <P>
          Name:
          <INPUT CLASS="form-control" TYPE="text" NAME="name" ID="name" VALUE="$name" required>

          <P>
          Description:
          <INPUT CLASS="form-control" TYPE="text" NAME="desc" ID="desc" VALUE="$description">

          <P>&nbsp;</P>
          Choose the type of data extraction used by this data flow:

          <P>
          <DIV CLASS="form-check">
            <INPUT CLASS="form-check-input" TYPE="radio" NAME="type" ID="Manual" CHECKED VALUE="Manual">
            <LABEL CLASS="form-check-label" FOR="Manual">Manually Uploaded Files</LABEL>
          </DIV>

          <DIV CLASS="form-check">
            <INPUT CLASS="form-check-input" TYPE="radio" NAME="type" ID="Web" VALUE="Web">
            <LABEL CLASS="form-check-label" FOR="Web">Web</LABEL>
          </DIV>

          <DIV CLASS="form-check">
            <INPUT CLASS="form-check-input" TYPE="radio" NAME="type" ID="FTP" VALUE="FTP">
            <LABEL CLASS="form-check-label" FOR="FTP">FTP</LABEL>
          </DIV>

          <DIV CLASS="form-check">
            <INPUT CLASS="form-check-input" TYPE="radio" NAME="type" ID="Paste" VALUE="Paste">
            <LABEL CLASS="form-check-label" FOR="Paste">Copy and Paste</LABEL>
          </DIV>

          <DIV CLASS="form-check">
            <INPUT CLASS="form-check-input" TYPE="radio" NAME="type" ID="Database" VALUE="Database">
            <LABEL CLASS="form-check-label" FOR="Database">Database</LABEL>
          </DIV>

          <DIV CLASS="form-check">
            <INPUT CLASS="form-check-input" TYPE="radio" NAME="type" ID="AmazonS3" VALUE="AmazonS3">
            <LABEL CLASS="form-check-label" FOR="AmazonS3">Amazon S3</LABEL>
          </DIV>

          <DIV CLASS="form-check">
            <INPUT CLASS="form-check-input" TYPE="radio" NAME="type" ID="Koala" VALUE="Koala">
            <LABEL CLASS="form-check-label" FOR="Koala">Koala Data Source</LABEL>
          </DIV>

          <SCRIPT>
            \$('#$source').attr('checked', true);
          </SCRIPT>

          <P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onclick="location.href='main.cld'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT">Next <I CLASS="bi bi-arrow-right"></I></BUTTON>
          </DIV>

        </DIV>
      </DIV>

      </FORM>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
