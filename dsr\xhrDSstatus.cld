#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  $dsID = $q->param('ds');

  print("Content-type: text/plain\n\n");

  #get the data source's current status
  $db = KAPutil_connect_to_database();

  $query = "SELECT status FROM app.jobs WHERE dsID=$dsID AND operation='DS-UPDATE'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;

  ($DSstatus) = $dbOutput->fetchrow_array;
  print("$DSstatus\n");


#EOF
