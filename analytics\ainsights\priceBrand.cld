#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::DataSources;
use Lib::AInsights::AInsights;
use Lib::AInsights::Price;
use Lib::AInsights::Utils;
use Lib::WebUtils;




#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Pricing Insights</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/fontawesome-5.10.2/css/all.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<SCRIPT SRC="/fusioncharts-3.18.0/js/fusioncharts.js"></SCRIPT>
<SCRIPT SRC="/fusioncharts-3.18.0/js/themes/fusioncharts.theme.zune.js"></SCRIPT>

<SCRIPT>
let vpHeight = window.innerHeight - 50;
if (vpHeight < 400)
{
  vpHeight = 400;
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">AInsights</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="insights.cld?pm=$priceModelID">$modelName</A></LI>
    <LI CLASS="breadcrumb-item active">Brand Pricing</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $priceModelID = $q->param('pm');
  $geoID = $q->param('g');

  AInsights_Utils_initialize_constants($priceModelID);

  #connect to the database
  $db = KAPutil_connect_to_database();

  $modelName = AInsights_ID_to_name($db, $priceModelID);
  $dsName = ds_id_to_name($db, $dsID);
  $dsID = AInsights_get_dsID($db, $priceModelID);

  $dsSchema = "datasource_" . $dsID;

  %prodNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "p");
  %geoNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "g");
  %timeNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "t");

  print_html_header();

  #make sure we have at least read privs for this pricing model
  $privs = AInsights_rights($db, $userID, $priceModelID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to view this pricing model.");
  }

  #grab some basic info about the pricing model
  $ownBrandID = AInsights_Utils_get_own_brand_id($db, $priceModelID);
  $brandSegID = AInsights_Utils_get_brand_seg_id($db, $priceModelID);
  @geoIDs = AInsights_Utils_get_model_geo_ids($db, $priceModelID);

  #if we weren't passed a geography to display
  if ($geoID < 1)
  {

    #see if we have one to try in the analyst's cookie
    $geoID = $session->param("priceModelGeoSelection.$priceModelID");

    #if still nothing, just use the first geo
    if ($geoID < 1)
    {
      $geoID = $geoIDs[0];
    }
  }
  else
  {
    $session->param("priceModelGeoSelection.$priceModelID", "$geoID");
  }

  #get IDs for our key competitors
  ($compID1, $compID2) = AInsights_Utils_get_brand_comp_ids($db, $dsSchema, $geoID);

  %brandNameHash = DSRseg_get_segments_hash($db, $dsSchema, "p", $brandSegID);
  $ownBrandName = $brandNameHash{$ownBrandID};

  %brandMembershipHash = DSRseg_get_segment_membership_hash($db, $dsSchema, 'p', $brandSegID);

  #get the last 52 weeks' worth of time period IDs from the data source
  @recent52WeekIDs = AInsights_Utils_get_time_period_ids($db, $dsSchema, 52);
  $recent52wksTimeIDStr = join(',', @recent52WeekIDs);

  #output off-canvas data selector
  print <<END_HTML;
  <DIV CLASS="offcanvas offcanvas-start" TABINDEX="-1" ID="offcanvas-data-selector">
  <DIV CLASS="offcanvas-header">
    <H5 CLASS="offcanvas-title" ID="offcanvas-label-data-selector"></H5>
    <BUTTON TYPE="button" CLASS="btn-close text-reset" data-bs-dismiss="offcanvas"></BUTTON>
  </DIV>
  <DIV CLASS="offcanvas-body">
    <DIV>
      <DIV CLASS="card">
        <DIV CLASS="card-header">Focus Geography</DIV>
        <DIV CLASS="card-body">
         <DIV CLASS="list-group">
           <A HREF="overview.cld?pm=$priceModelID&g=$availableGeoID" CLASS="list-group-item list-group-item-action"><B>Overview</B></A>
END_HTML

  #determine which geographies didn't distribute our brand in the past 52 wks
  $query = "SELECT geographyID FROM $dsSchema.$AInsightsBrandTable \
      WHERE brandID=$ownBrandID AND ISNULL(avgDist52)";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  while (($id) = $dbOutput->fetchrow_array)
  {
    $noDistGeosHash{$id} = 1;
  }

  foreach $availableGeoID (@geoIDs)
  {
    $htmlListClass = "";
    if ($availableGeoID == $geoID)
    {
      $htmlListClass = "active";
    }
    elsif ($noDistGeosHash{$availableGeoID} > 0)
    {
      $htmlListClass = "list-group-item-secondary";
    }

    print <<END_HTML;
        <A HREF="?pm=$priceModelID&g=$availableGeoID" CLASS="list-group-item list-group-item-action $htmlListClass">$geoNameHash{$availableGeoID}</A>
END_HTML
  }

  print <<END_HTML;
          </DIV>
        </DIV>
      </DIV>

      <P>
      <FORM METHOD="post" ACTION="modelRefresh.cld">
      <INPUT TYPE="hidden" NAME="pm" VALUE="$priceModelID">
      <INPUT TYPE="hidden" NAME="a" VALUE="c">
      <INPUT TYPE="hidden" NAME="compGeo" VALUE="$geoID">
      <DIV CLASS="card">
        <DIV CLASS="card-header">Key Competitors</DIV>
        <DIV CLASS="card-body">
          <SELECT CLASS="form-select" NAME="comp1" ID="comp1"">
            <OPTION VALUE="auto">Automatically Detect</OPTION>
END_HTML

  ($compID1, $compID2) = AInsights_Utils_get_brand_comp_ids($db, $dsSchema, $geoID);

  foreach $brandID (sort {$brandNameHash{$a} cmp $brandNameHash{$b}} keys %brandNameHash)
  {
    print("  <OPTION VALUE='$brandID'>$brandNameHash{$brandID}\n");
  }

  print <<END_HTML;
          </SELECT>
          <SCRIPT>
          \$(document).ready(function()
          {
            \$('#comp1').val('$compID1');
          });
          </SCRIPT>

          <SELECT CLASS="form-select mt-2" NAME="comp2" ID="comp2">
          <OPTION VALUE="auto">Automatically Detect</OPTION>
END_HTML

  foreach $brandID (sort {$brandNameHash{$a} cmp $brandNameHash{$b}} keys %brandNameHash)
  {
    print("  <OPTION VALUE='$brandID'>$brandNameHash{$brandID}\n");
  }

  print <<END_HTML;
          </SELECT>
          <SCRIPT>
          \$(document).ready(function()
          {
            \$('#comp2').val('$compID2');
          });
          </SCRIPT>

          <DIV CLASS="form-check form-check-inline">
            <INPUT CLASS="form-check-input" NAME="comp-all" ID="comp-all" TYPE="checkbox">
            <LABEL CLASS="form-check-label" FOR="comp-all">Apply change to all geographies</LABEL>
          </DIV>

          <DIV CLASS="text-center mt-2">
            <BUTTON CLASS="btn btn-primary" TYPE="submit" ID="btn-submit-comp">Apply <I CLASS="bi bi-pencil-square""></I></BUTTON>
          </DIV>
        </DIV>
      </DIV>
      </FORM>
    </DIV>
  </DIV>
</DIV>
END_HTML

  print <<END_HTML;
<DIV CLASS="container-fluid">

  <DIV CLASS="row">
    <DIV CLASS="col">

      <H3>Pricing Insights for $ownBrandName in
      <A HREF="#" CLASS="text-decoration-none" data-bs-toggle="offcanvas" data-bs-target="#offcanvas-data-selector">$geoNameHash{$geoID}</A></H3>

      <P>&nbsp;</P>
END_HTML

  #handle the case where the category isn't distributed in this market
  $query = "SELECT avgDist52 FROM $dsSchema.$AInsightsBrandTable \
      WHERE brandID=0 AND geographyID=$geoID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($avgYearlyDistCategory) = $dbOutput->fetchrow_array;

  if ($avgYearlyDistCategory < 0.1)
  {
    print <<END_HTML;
      <DIV CLASS="alert alert-warning">
        This category isn't distributed in this market.
      </DIV>
    </DIV> <!-- row -->
  </DIV>  <!-- col -->

</DIV>

END_HTML
    exit;
  }

  #grab the overview insight for display
  $insight = AInsights_Utils_get_insight_html($db, $dsSchema,
      "price", "brand", "overview", $geoID);

  #determine if our brand is carried in this geography
  $query = "SELECT avgDist52 FROM $dsSchema.$AInsightsBrandTable \
      WHERE brandID=$ownBrandID AND geographyID=$geoID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($avgYearlyDistOwnBrand) = $dbOutput->fetchrow_array;

  print <<END_HTML;

      <DIV CLASS="card">
        <DIV CLASS="card-body">

          <H4>Overview</H4>
          <P>
          $insight

          <TABLE CLASS="table table-bordered table-striped-columns">
            <TR>
              <TH>&nbsp</TH>
              <TH COLSPAN="2">Week</TH>
              <TH COLSPAN="2">Month</TH>
              <TH COLSPAN="2">Quarter</TH>
              <TH COLSPAN="2">Year</TH>
            </TR>
            <TR>
              <TH>&nbsp</TH>
              <TH>Avg Price</TH>
              <TH>% Change</TH>
              <TH>Avg Price</TH>
              <TH>% Change</TH>
              <TH>Avg Price</TH>
              <TH>% Change</TH>
              <TH>Avg Price</TH>
              <TH>% Change</TH>
            </TR>
END_HTML

  $brandSQLStr = "0,$ownBrandID";
  if ($compID1 > 0)
  {
    $brandSQLStr .= ",$compID1";
  }
  if ($compID2 > 0)
  {
    $brandSQLStr .= ",$compID2";
  }
  $query = "SELECT brandID, avgPrice52, avgPrice13, avgPrice4 FROM $dsSchema.$AInsightsBrandTable \
      WHERE geographyID=$geoID AND brandID IN ($brandSQLStr)";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  undef(%priceValueHash);
  while (($brandID, $avgPrice52, $avgPrice13, $avgPrice4) = $dbOutput->fetchrow_array)
  {
    $avgPrice52 = AInsights_Utils_html_format_currency($avgPrice52);
    $avgPrice13 = AInsights_Utils_html_format_currency($avgPrice13);
    $avgPrice4 = AInsights_Utils_html_format_currency($avgPrice4);
    $priceValueHash{"$brandID.52"} = $avgPrice52;
    $priceValueHash{"$brandID.13"} = $avgPrice13;
    $priceValueHash{"$brandID.4"} = $avgPrice4;
  }

  ($mostRecentWeekID) = AInsights_Utils_get_time_period_ids($db, $dsSchema, 1);
  $query = "SELECT brandID, avgPrice FROM $dsSchema.$AInsightsBrandCube \
      WHERE geographyID=$geoID AND brandID IN ($brandSQLStr) AND timeID=$mostRecentWeekID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  while (($brandID, $avgPrice) = $dbOutput->fetchrow_array)
  {
    $avgPrice = AInsights_Utils_html_format_currency($avgPrice);
    $priceValueHash{"$brandID.1"} = $avgPrice;
  }

  $query = "SELECT brandID, name, value FROM $dsSchema.$AInsightsBrandCalcTable \
      WHERE geographyID=$geoID AND name IN ('avg_price_pct_chg_week', 'avg_price_pct_chg_month', 'avg_price_pct_chg_quarter', 'avg_price_pct_chg_year')";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  undef(%nameIconHTMLHash);
  while (($brandID, $name, $value) = $dbOutput->fetchrow_array)
  {
    $hashKey = "$brandID.$name";
    $HRvalue = AInsights_Utils_html_format_number($value, 1, 0);
    if ($value eq "-0.0")
    {
      $HRvalue = "0.0";
    }
    $HRvalue = "$HRvalue%";
    if ($value > 0)
    {
      $nameIconHTMLHash{$hashKey} = "<I CLASS='bi bi-arrow-up-square-fill text-success'></I> $HRvalue";
    }
    elsif ($value < 0)
    {
      $nameIconHTMLHash{$hashKey} = "<I CLASS='bi bi-arrow-down-square-fill text-warning'></I> $HRvalue";
    }
    elsif ($value == 0)
    {
      $nameIconHTMLHash{$hashKey} = "<I CLASS='bi bi-square-fill text-muted'></I> $HRvalue";
    }
    else
    {
      $nameIconHTMLHash{$hashKey} = "$HRvalue";
    }
  }

  print <<END_HTML;
            <TR>
              <TH>Category Average</TH>
              <TD CLASS="text-right">$priceValueHash{"0.1"}</TD>
              <TD CLASS="text-right">$nameIconHTMLHash{"0.avg_price_pct_chg_week"}</TD>
              <TD CLASS="text-right">$priceValueHash{"0.4"}</TD>
              <TD CLASS="text-right">$nameIconHTMLHash{"0.avg_price_pct_chg_month"}</TD>
              <TD CLASS="text-right">$priceValueHash{"0.13"}</TD>
              <TD CLASS="text-right">$nameIconHTMLHash{"0.avg_price_pct_chg_quarter"}</TD>
              <TD CLASS="text-right">$priceValueHash{"0.52"}</TD>
              <TD CLASS="text-right">$nameIconHTMLHash{"0.avg_price_pct_chg_year"}</TD>
            </TR>
END_HTML

  if ($avgYearlyDistOwnBrand > 0)
  {
    print <<END_HTML;
            <TR>
              <TH>$ownBrandName</TH>
              <TD CLASS="text-right">$priceValueHash{"$ownBrandID.1"}</TD>
              <TD CLASS="text-right">$nameIconHTMLHash{"$ownBrandID.avg_price_pct_chg_week"}</TD>
              <TD CLASS="text-right">$priceValueHash{"$ownBrandID.4"}</TD>
              <TD CLASS="text-right">$nameIconHTMLHash{"$ownBrandID.avg_price_pct_chg_month"}</TD>
              <TD CLASS="text-right">$priceValueHash{"$ownBrandID.13"}</TD>
              <TD CLASS="text-right">$nameIconHTMLHash{"$ownBrandID.avg_price_pct_chg_quarter"}</TD>
              <TD CLASS="text-right">$priceValueHash{"$ownBrandID.52"}</TD>
              <TD CLASS="text-right">$nameIconHTMLHash{"$ownBrandID.avg_price_pct_chg_year"}</TD>
            </TR>
END_HTML
  }

  if ($compID1 > 0)
  {
    print <<END_HTML;
            <TR>
              <TH>$brandNameHash{$compID1}</TH>
              <TD CLASS="text-right">$priceValueHash{"$compID1.1"}</TD>
              <TD CLASS="text-right">$nameIconHTMLHash{"$compID1.avg_price_pct_chg_week"}</TD>
              <TD CLASS="text-right">$priceValueHash{"$compID1.4"}</TD>
              <TD CLASS="text-right">$nameIconHTMLHash{"$compID1.avg_price_pct_chg_month"}</TD>
              <TD CLASS="text-right">$priceValueHash{"$compID1.13"}</TD>
              <TD CLASS="text-right">$nameIconHTMLHash{"$compID1.avg_price_pct_chg_quarter"}</TD>
              <TD CLASS="text-right">$priceValueHash{"$compID1.52"}</TD>
              <TD CLASS="text-right">$nameIconHTMLHash{"$compID1.avg_price_pct_chg_year"}</TD>
            </TR>
END_HTML
  }

  if ($compID2 > 0)
  {
    print <<END_HTML;
            <TR>
              <TH>$brandNameHash{$compID2}</TH>
              <TD CLASS="text-right">$priceValueHash{"$compID2.1"}</TD>
              <TD CLASS="text-right">$nameIconHTMLHash{"$compID2.avg_price_pct_chg_week"}</TD>
              <TD CLASS="text-right">$priceValueHash{"$compID2.4"}</TD>
              <TD CLASS="text-right">$nameIconHTMLHash{"$compID2.avg_price_pct_chg_month"}</TD>
              <TD CLASS="text-right">$priceValueHash{"$compID2.13"}</TD>
              <TD CLASS="text-right">$nameIconHTMLHash{"$compID2.avg_price_pct_chg_quarter"}</TD>
              <TD CLASS="text-right">$priceValueHash{"$compID2.52"}</TD>
              <TD CLASS="text-right">$nameIconHTMLHash{"$compID2.avg_price_pct_chg_year"}</TD>
            </TR>
END_HTML
  }

  print <<END_HTML;
          </TABLE>

        </DIV>
      </DIV>

END_HTML




#------------------- Average Pricing Trends for Own Brand & Comps---------------

  $insight = AInsights_Utils_get_insight_html($db, $dsSchema,
      "price", "brand", "trends", $geoID);

  print <<END_HTML;
    <P>&nbsp;</P>
    <DIV CLASS="card">
      <DIV CLASS="card-body">

        <H4>Pricing Trends</H4>
        <P>
        $insight

        <DIV ID="chart-line-trends"></DIV>
        <SCRIPT>
          let trendsChart = new FusionCharts({'type': 'MSLine', 'width': '99%', 'height': '450', 'dataFormat': 'json'});
          trendsChart.setJSONUrl('ajaxBrandPriceCharts.cld?pm=$priceModelID&c=trnd&g=$geoID');
          trendsChart.render('chart-line-trends');
        </SCRIPT>
      </DIV>
    </DIV>
END_HTML


#------------------- Elasticity Overview for Own Brand -----------------

  $insight = AInsights_Utils_get_insight_html($db, $dsSchema,
      "price", "brand", "elasticity_overview", $geoID);

  $query = "SELECT elasticity FROM $dsSchema.$AInsightsBrandTable \
      WHERE brandID=$ownBrandID AND geographyID=$geoID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($avgElasticity) = $dbOutput->fetchrow_array;

  print <<END_HTML;
    <P>&nbsp;</P>
    <DIV CLASS="card">
      <DIV CLASS="card-body">

        <H4>Elasticity Overview</H4>
        <P>
        $insight
        <DIV ID="graph-elasticity-gauge"></DIV>
        <SCRIPT>
        FusionCharts.ready(function()
        {
          let elasticityGauge = new FusionCharts(
          {
            type: 'hlineargauge',
            renderAt: 'graph-elasticity-gauge',
            width: '100%', height: '130',
            dataFormat: 'json',
            dataSource:
            {
              "chart":
              {
                "animation": "0",
                "lowerLimit": "-3",
                "upperLimit": "0",
                "valueAbovePointer": "1",
                "showShadow": "0",
                "gaugeFillMix": "{light}",
                'baseFontColor': '#ffffff',
                'labelFontColor': '#333333',
                'valueFontColor': '#333333',
                'baseFontSize': '12',
                "theme": "zune"
              },
              "colorRange":
              {
                "color": [
                {
                  "minValue": "-0.75",
                  "maxValue": "0",
                  "label": "Inelastic",
                  "code": "#00AEEF"
                },
                {
                  "minValue": "-1.25",
                  "maxValue": "-0.75",
                  "label": "Moderately Inelastic",
                  "code": "#8DC63F"
                },
                {
                  "minValue": "-1.75",
                  "maxValue": "-1.25",
                  "label": "Moderately Elastic",
                  "code": "#FFB100"
                },
                {
                  "minValue": "-2.25",
                  "maxValue": "-1.75",
                  "label": "Elastic",
                  "code": "#B21DAC"
                },
                {
                  "minValue": "-3",
                  "maxValue": "-2.255",
                  "label": "Very Elastic",
                  "code": "#DC0015"
                }]
              },
              "pointers":
              {
                "pointer": [
                {
                  "value": "$avgElasticity"
                } ]
              }
            }
          });
          elasticityGauge.render();
        });
        </SCRIPT>

      </DIV>
    </DIV>
END_HTML



#----------------- Elasticity for all brands in geo ---------------------

print <<END_HTML;
    <P>&nbsp;</P>
    <DIV CLASS="card">
      <DIV CLASS="card-body">

        <H4>Brand Elasticity in $geoNameHash{$geoID}</H4>
        <P>
END_HTML

  #get number of bars we're going to graph, and size chart appropriately
  $query = "SELECT COUNT(elasticity) FROM $dsSchema.$AInsightsBrandTable \
      WHERE NOT ISNULL(elasticity) AND geographyID=$geoID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($barsToGraph) = $dbOutput->fetchrow_array;
  $chartHeight = $barsToGraph * 20 + 75;

  $insight = AInsights_Utils_get_insight_html($db, $dsSchema,
      "price", "brand", "elasticity_brands", $geoID);

print <<END_HTML;
        $insight
        <DIV ID="chart-bar-elasticities"></DIV>
        <SCRIPT>
          let brandElasticityChart = new FusionCharts({'type': 'bar2d', 'width': '100%', 'height': '$chartHeight', 'dataFormat': 'json'});
          brandElasticityChart.setJSONUrl('ajaxBrandPriceCharts.cld?pm=$priceModelID&c=elasticities&g=$geoID');
          brandElasticityChart.render('chart-bar-elasticities');
        </SCRIPT>
      </DIV>
    </DIV>
END_HTML



#------------------- Own Brand Elasticity Across Geos -----------------------

print <<END_HTML;
    <P>&nbsp;</P>
    <DIV CLASS="card">
      <DIV CLASS="card-body">

        <H4>$ownBrandName Elasticity Across Geographies</H4>
        <P>
END_HTML

  #get number of bars we're going to graph, and size chart appropriately
  $query = "SELECT COUNT(DISTINCT geographyID) FROM $dsSchema.$AInsightsBrandTable \
      WHERE NOT ISNULL(elasticity) AND brandID = $ownBrandID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($barsToGraph) = $dbOutput->fetchrow_array;
  $chartHeight = $barsToGraph * 20 + 75;

  $insight = AInsights_Utils_get_insight_html($db, $dsSchema,
      "price", "brand", "elasticity_geos", 0);

print <<END_HTML;
        $insight
        <DIV ID="chart-bar-elast-geographies"></DIV>
        <SCRIPT>
          let ownBrandElastGeoChart = new FusionCharts({'type': 'bar2d', 'width': '100%', 'height': '$chartHeight', 'dataFormat': 'json'});
          ownBrandElastGeoChart.setJSONUrl('ajaxBrandPriceCharts.cld?pm=$priceModelID&c=brand_elast_geos&g=$geoID');
          ownBrandElastGeoChart.render('chart-bar-elast-geographies');
        </SCRIPT>
      </DIV>
    </DIV>
END_HTML



#----------------- Average pricing for all brands in geo ---------------------

print <<END_HTML;
    <P>&nbsp;</P>
    <DIV CLASS="card">
      <DIV CLASS="card-body">

        <H4>Average Brand Pricing in $geoNameHash{$geoID}</H4>
        <P>
END_HTML

  #get number of bars we're going to graph, and size chart appropriately
  $query = "SELECT COUNT(avgPrice52) FROM $dsSchema.$AInsightsBrandTable \
      WHERE avgPrice52 > 0 AND geographyID=$geoID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($barsToGraph) = $dbOutput->fetchrow_array;
  $chartHeight = $barsToGraph * 20 + 75;

  $insight = AInsights_Utils_get_insight_html($db, $dsSchema,
      "price", "brand", "brands", $geoID);

print <<END_HTML;
        $insight
        <DIV ID="chart-bar-brands"></DIV>
        <SCRIPT>
          let brandPriceChart = new FusionCharts({'type': 'bar2d', 'width': '100%', 'height': '$chartHeight', 'dataFormat': 'json'});
          brandPriceChart.setJSONUrl('ajaxBrandPriceCharts.cld?pm=$priceModelID&c=brands_price&g=$geoID');
          brandPriceChart.render('chart-bar-brands');
        </SCRIPT>
      </DIV>
    </DIV>
END_HTML



#------------------- Own Brand Pricing Across Geos -----------------------

print <<END_HTML;
    <P>&nbsp;</P>
    <DIV CLASS="card">
      <DIV CLASS="card-body">

        <H4>$ownBrandName Pricing Across Geographies</H4>
        <P>
END_HTML

  #get number of bars we're going to graph, and size chart appropriately
  $query = "SELECT COUNT(DISTINCT geographyID) FROM $dsSchema.$AInsightsBrandTable \
      WHERE avgPrice52 > 0 AND brandID = $ownBrandID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($barsToGraph) = $dbOutput->fetchrow_array;
  $chartHeight = $barsToGraph * 20 + 75;

  $insight = AInsights_Utils_get_insight_html($db, $dsSchema,
      "price", "brand", "geos", 0);

print <<END_HTML;
        $insight
        <DIV ID="chart-bar-geographies"></DIV>
        <SCRIPT>
          let ownBrandGeoChart = new FusionCharts({'type': 'bar2d', 'width': '100%', 'height': '$chartHeight', 'dataFormat': 'json'});
          ownBrandGeoChart.setJSONUrl('ajaxBrandPriceCharts.cld?pm=$priceModelID&c=brand_geos&g=$geoID');
          ownBrandGeoChart.render('chart-bar-geographies');
        </SCRIPT>
      </DIV>
    </DIV>
END_HTML


#------------------- Own Brand Item Pricing -----------------------

print <<END_HTML;
    <P>&nbsp;</P>
    <DIV CLASS="card">
      <DIV CLASS="card-body">

        <H4>$ownBrandName Item Pricing</H4>
        <P>
END_HTML

  #get number of bars we're going to graph, and size chart appropriately
  $query = "SELECT COUNT(productID) FROM $dsSchema.$AInsightsItemTable \
      WHERE avgPrice52 > 0 AND brandID = $ownBrandID AND geographyID=$geoID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($barsToGraph) = $dbOutput->fetchrow_array;
  $chartHeight = $barsToGraph * 20 + 75;

  $insight = AInsights_Utils_get_insight_html($db, $dsSchema,
      "price", "brand", "own_items", $geoID);

print <<END_HTML;
        $insight
        <DIV ID="chart-bar-items"></DIV>
        <SCRIPT>
          let ownBrandItemChart = new FusionCharts({'type': 'bar2d', 'width': '100%', 'height': '$chartHeight', 'dataFormat': 'json'});
          ownBrandItemChart.setJSONUrl('ajaxBrandPriceCharts.cld?pm=$priceModelID&c=brand_items&g=$geoID');
          ownBrandItemChart.render('chart-bar-items');
        </SCRIPT>
      </DIV>
    </DIV>
END_HTML



  #--------------------- Elasticity Brand/Geo Data Grid ----------------------

  $elastGridInsight = AInsights_Utils_get_insight_html($db, $dsSchema,
      "price", "brand", "elast_brand_geo_grid", $geoID);

  print <<END_HTML;
      <P>&nbsp;</P>

      <DIV CLASS="card">
        <DIV CLASS="card-body">

          <H4>Brand Elasticity Data Grid</H4>
          <P>
          $elastGridInsight
END_HTML

  print <<END_HTML;
          <P>
          <DIV ID="data-collapse-elast-brand-grid">
            <DIV CLASS="card border-primary mx-auto">
              <DIV CLASS="card-header bg-primary text-white">
                <A CLASS="card-link text-white text-decoration-none" data-bs-toggle="collapse" HREF="#collapse-brand-elast-table">Brand Elasticity Grid</A>
                <I CLASS="fas fa-caret-down"></I>
              </DIV>
              <DIV ID="collapse-brand-elast-table" CLASS="collapse" data-bs-parent="#data-collapse-elast-brand-grid">
                <DIV CLASS="card-body" STYLE="overflow:auto;">
                  <TABLE CLASS="table table-bordered table-striped">
                    <TR>
                      <TH>Brand</TH>
END_HTML

  #grab list of geos in this model, alpha sorted by name
  @geoIDs = AInsights_Utils_get_model_geo_ids($db, $priceModelID);
  $geoIDStr = join(',', @geoIDs);
  $query = "SELECT ID, name FROM $dsSchema.geographies \
      WHERE ID IN ($geoIDStr) ORDER BY name";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  while (($id, $name) = $dbOutput->fetchrow_array)
  {
    $orderedGeoIDStr .= "$id,";
    print("            <TH>$name</TH>\n");
  }
  chop($orderedGeoIDStr);

  print <<END_HTML;
                    </TR>
END_HTML


  #get alphasorted list of brandIDs with elasticities available in this model
  $query = "SELECT DISTINCT brandID, name FROM $dsSchema.$AInsightsBrandTable \
      WHERE elasticity IS NOT NULL AND avgDist52 > 0 ORDER BY name";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  while (($brandID, $name) = $dbOutput->fetchrow_array)
  {
    push(@brandIDs, $brandID);
  }

  foreach $brandID (@brandIDs)
  {
    print <<END_HTML;
                    <TR>
                      <TD NOWRAP>$brandNameHash{$brandID}</TD>
END_HTML

    $query = "SELECT elasticity FROM $dsSchema.$AInsightsBrandTable \
        WHERE brandID=$brandID \
        ORDER BY FIELD(geographyID, $orderedGeoIDStr)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($elasticity) = $dbOutput->fetchrow_array)
    {
      $bgColor = AInsights_Utils_get_elasticity_html_bgcolor($elasticity);
      $dispElasticity = AInsights_Utils_html_format_elasticity($elasticity);
      print("<TD CLASS='text-white text-end' STYLE='background:$bgColor;'>$dispElasticity</TD>\n");
    }

  print <<END_HTML;
                    </TR>
END_HTML

  }


  print <<END_HTML;
                  </TABLE>
                </DIV>
              </DIV>
            </DIV>
          </DIV>
        </DIV>
      </DIV>
END_HTML


  #------------------------ Brand Insights -----------------------------

  $countInsight = AInsights_Utils_get_insight_html($db, $dsSchema,
      "price", "brand", "market_brand_count", $geoID);

  print <<END_HTML;
      <P>&nbsp;</P>

      <DIV CLASS="card">
        <DIV CLASS="card-body">

          <H4>Market Competitors</H4>
          <P>
          $countInsight
END_HTML

  print <<END_HTML;
          <P>
          <DIV ID="data-collapse-brand">
            <DIV CLASS="card border-primary mx-auto">
              <DIV CLASS="card-header bg-primary text-white">
                <A CLASS="card-link text-white text-decoration-none" data-bs-toggle="collapse" HREF="#collapse-brand-data-table">Brand Pricing Data Table</A>
                <I CLASS="fas fa-caret-down"></I>
              </DIV>
              <DIV ID="collapse-brand-data-table" CLASS="collapse" data-bs-parent="#data-collapse-brand">
                <DIV CLASS="card-body">
                  <TABLE CLASS="table table-bordered table-striped">
                    <TR>
                      <TH>Brand</TH>
                      <TH>Average Price</TH>
                      <TH>Elasticity</TH>
                      <TH>Dollar Sales</TH>
                      <TH>Unit Sales</TH>
                    </TR>
END_HTML

  $query = "SELECT brandID, avgPrice52, elasticity, dollars52, units52 FROM $dsSchema.$AInsightsBrandTable \
      WHERE geographyID=$geoID AND brandID != 0 \
      ORDER BY units52 DESC";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  while (($brandID, $avgPrice, $elasticity, $dollars, $units) = $dbOutput->fetchrow_array)
  {

    #skip anything that wasn't actually sold
    if ($units < 5)
    {
      next;
    }

    #format numerical values
    $dispAvgPrice = AInsights_Utils_html_format_currency($avgPrice);
    $dispElasticity = AInsights_Utils_html_format_elasticity($elasticity);
    $dispDollars = AInsights_Utils_html_format_currency($dollars);
    $dispUnits = AInsights_Utils_html_format_number($units, 0);

    if ($brandID == $ownBrandID)
    {
      $htmlTRColor = "table-success";
    }
    else
    {
      $htmlTRColor = "";
    }

    print <<END_HTML;
                    <TR CLASS="$htmlTRColor">
                      <TD>$brandNameHash{$brandID}</TD>
                      <TD CLASS="text-end">$dispAvgPrice</TD>
                      <TD CLASS="text-end">$dispElasticity</TD>
                      <TD CLASS="text-end">$dispDollars</TD>
                      <TD CLASS="text-end">$dispUnits</TD>
                    </TR>
END_HTML
  }

  print <<END_HTML;
                  </TABLE>
                </DIV>
              </DIV>
            </DIV>
          </DIV>
        </DIV>
      </DIV>
END_HTML


  #--------------------- Item Pricing Insights -------------------------

  $insight = AInsights_Utils_get_insight_html($db, $dsSchema,
      "price", "brand", "items", $geoID);

  print <<END_HTML;
      <P>&nbsp;</P>

      <DIV CLASS="card">
        <DIV CLASS="card-body">

          <H4>Item Pricing</H4>
          <P>
          $insight

          <P>
          <DIV ID="data-collapse-item">
            <DIV CLASS="card border-primary mx-auto">
              <DIV CLASS="card-header bg-primary text-white">
                <A CLASS="card-link text-white text-decoration-none" data-bs-toggle="collapse" HREF="#collapse-item-data-table">Item Pricing Data Table</A>
                <I CLASS="fas fa-caret-down"></I>
              </DIV>
              <DIV ID="collapse-item-data-table" CLASS="collapse" data-bs-parent="#data-collapse-item">
                <DIV CLASS="card-body">

                  <TABLE CLASS="table table-bordered table-striped">
                    <TR>
                      <TH>Item</TH>
                      <TH>Brand</TH>
                      <TH>Average Price</TH>
                      <TH>Elasticity</TH>
                      <TH>Dollar Sales</TH>
                      <TH>Unit Sales</TH>
                    </TR>
END_HTML

  #build up hash of item-level elasticity values
  $query = "SELECT productID, elasticity FROM $dsSchema.$AInsightsItemTable \
      WHERE geographyID=$geoID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  while (($prodID, $elasticity) = $dbOutput->fetchrow_array)
  {
    $itemElasticityHash{$prodID} = $elasticity;
  }

  $query = "SELECT productID, SUM(dollars)/SUM(units), SUM(dollars), SUM(units) AS totalUnits \
      FROM $dsSchema.$AInsightsItemCube \
      WHERE geographyID=$geoID AND timeID IN ($recent52wksTimeIDStr) \
      GROUP BY productID ORDER BY totalUnits DESC";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  while (($prodID, $avgPrice, $dollars, $units) = $dbOutput->fetchrow_array)
  {

    #get product's brand membership and name
    $brandID = $brandMembershipHash{$prodID};

    #format numerical values
    $dispAvgPrice = AInsights_Utils_html_format_currency($avgPrice);
    $dispElasticity = AInsights_Utils_html_format_elasticity($itemElasticityHash{$prodID});
    $dispDollars = AInsights_Utils_html_format_currency($dollars);
    $dispUnits = AInsights_Utils_html_format_number($units, 0);

    if ($brandID == $ownBrandID)
    {
      $htmlTRColor = "table-success";
    }
    else
    {
      $htmlTRColor = "";
    }

    print <<END_HTML;
                    <TR CLASS="$htmlTRColor">
                      <TD>$prodNameHash{$prodID}</TD>
                      <TD>$brandNameHash{$brandID}</TD>
                      <TD CLASS="text-end">$dispAvgPrice</TD>
                      <TD CLASS="text-end">$dispElasticity</TD>
                      <TD CLASS="text-end">$dispDollars</TD>
                      <TD CLASS="text-end">$dispUnits</TD>
                    </TR>
END_HTML
  }

  print <<END_HTML;
                  </TABLE>
                </DIV>
              </DIV>
            </DIV>
          </DIV>
        </DIV>
      </DIV>
END_HTML

#--------------------------


  print <<END_HTML;
    </DIV> <!-- row -->
  </DIV>  <!-- col -->

</DIV>
END_HTML


  print_html_footer();

  #flush the CGI session info out to storage
  $session->flush();

#  AInsights_audit($db, $userID, $priceModelID, "Viewed details for $prodNameHash{$prodID} in $geoNameHash{$geoID}");
#  $activity = "ELASTICITY: $first $last viewed model details for $prodNameHash{$prodID} / $geoNameHash{$geoID} in elasticity $modelName in $dsName";
#  utils_slack($activity);

#EOF
