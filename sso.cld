#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;
use MIME::Base64;
use Digest::SHA qw(sha256_hex);

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::WebUtils;


#-------------------------------------------------------------------------


#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);
}



#-------------------------------------------------------------------------


  #create the CGI session
  $session = CGI::Session->new();

  #get form data
  $form = new CGI;
  $b64Email = $form->param('e');
  $b64Redirect = $form->param('r');
  $credentialCode = $form->param('c');

  #connect to user login database
  $db = KAPutil_connect_to_database();

  #decode the email strings
  $email = decode_base64($b64Email);
  $redirect = decode_base64($b64Redirect);
  #scrub the CGI form data before we let it near the database
  $q_email = $db->quote($email);

  #get the user's info from the database, if it exists
  $query = "SELECT ID, password, orgID, first, last, acctType, disabled \
      FROM users WHERE email=$q_email";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  #if the username doesn't exist, toss an error
  if ($status < 1)
  {
    print_html_header();
    exit_error("Invalid credential.");
  }

  #extract the user's info
  ($userID, $password, $orgID, $first, $last, $acctType, $disabled, $clickThrough) = $dbOutput->fetchrow_array;

  #check the credentials (password/IP)
  $trueCredentials = sha256_hex("$password");
  if ($trueCredentials ne $credentialCode)
  {
    print_html_header();
    exit_error("Invalid credentials.");
  }

  #if the account is disabled, toss an error
  if ($disabled == 1)
  {
    print_html_header();
    exit_error("This account has been disabled.");
  }

  #get the user's organization info from the database
  $query = "SELECT name FROM orgs WHERE ID=$orgID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($orgName) = $dbOutput->fetchrow_array;

  #store the user's info in the CGI session object
  $session->param(userID, $userID);
  $session->param(email, $email);
  $session->param(first, $first);
  $session->param('last', $last);
  $session->param(acctType, $acctType);
  $session->param(orgID, $orgID);
  $session->param(orgName, $orgName);

  #login was successful, so re-direct to requested page
  $session->flush();
  print("Status: 302 Moved temporarily\n");
  print("Location: $redirect\n");
  print($session->header());


#EOF
