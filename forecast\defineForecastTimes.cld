#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: KCast Time Periods</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Please Wait...');
  return(true);
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">KCast</A></LI>
    <LI CLASS="breadcrumb-item">$dsName</LI>
    <LI CLASS="breadcrumb-item active">Forecast Time Periods</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get the CGI input variables
  $fcID = $q->param('f');
  $action = $q->param('a');
  $dsID = $q->param('ds');
  $measure = $q->param('measure');
  if (!($dsID =~ m/[0-9]+/))
  {
    exit_error("Bad data source ID $dsID");
  }

  $db = KAPutil_connect_to_database();

  #if we're editing an existing forecast
  if ($fcID > 0)
  {
    $query = "SELECT periodType, futurePeriods, timeperiods FROM analytics.forecasts WHERE ID=$fcID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($periodType, $futurePeriods, $timeperiods) = $dbOutput->fetchrow_array;

    if ($periodType == 52)
    {
      $periodType = "Weekly";
    }
    elsif ($periodType == 12)
    {
      $periodType = "Monthly";
    }
    elsif ($periodType == 4)
    {
      $periodType = "Quarterly";
    }
    elsif ($periodType == 1)
    {
      $periodType = "Yearly";
    }
    $timeperiods =~ m/^(\d+),/;
    $timeperiods = $1;
  }

  $dsSchema = "datasource_" . $dsID;

  #get our data source name
  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  print <<END_HTML;
  <DIV CLASS="container">

    <DIV CLASS="row">

      <DIV CLASS="col"></DIV>  <!-- spacing -->

      <DIV CLASS="col-xs-12 col-sm-12 col-md-10 col-lg-7 col-xl-6"> <!-- content -->

        <FORM METHOD="post" ACTION="/app/forecast/defineForecastProducts.cld" onsubmit="return checkForm(this);">
        <INPUT TYPE="hidden" NAME="f" VALUE="$fcID">
        <INPUT TYPE="hidden" NAME="a" VALUE="$action">
        <INPUT TYPE="hidden" NAME="ds" VALUE="$dsID">
        <INPUT TYPE="hidden" NAME="measure" VALUE="$measure">

        <DIV CLASS="card border-primary mx-auto">
          <DIV CLASS="card-header bg-primary text-white">Forecast Time Periods</DIV>
          <DIV CLASS="card-body">

          Time Period Type:
          <SELECT CLASS="form-select" NAME="periodType" ID="periodType">
END_HTML

  #retrieve a list of available timeperiods from the data source so we can
  #see which time period types it makes sense to offer as options (and default)
  $weekly = 0;
  $monthly = 0;
  $quarterly = 0;
  $yearly = 0;
  $query = "SELECT type, duration FROM $dsSchema.timeperiods GROUP BY type, duration";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;

  while (($type, $duration) = $dbOutput->fetchrow_array)
  {
    #see if it's a weekly type
    if ((($type == 40) && ($duration == 7)) ||    #7 days ending
        (($type == 30) && ($duration == 1)))      #1 week ending
    {
      $weekly = 1;
      if (!defined($periodType))
      {
        $periodType = "Weekly";
      }
    }

    #see if it's a monthly type
    if ((($type == 30) && ($duration == 4)) ||    #4 weeks ending
        (($type == 20) && ($duration == 1)))      #1 month ending
    {
      $monthly = 1;
      if (!defined($periodType))
      {
        $periodType = "Monthly";
      }
    }

    #see if it's a quarterly type
    if ((($type == 30) && ($duration == 13)) ||   #13 weeks ending
        (($type == 20) && ($duration == 3)))      #3 months ending
    {
      $quarterly = 1;
      if (!defined($periodType))
      {
        $periodType = "Quarterly";
      }
    }

    #see if it's a yearly type
    if ((($type == 30) && ($duration == 52)) ||   #52 weeks ending
        (($type == 20) && ($duration == 12)) ||   #12 months ending
        (($type == 10) && ($duration == 1)))      #1 year ending
    {
      $yearly = 1;
      if (!defined($periodType))
      {
        $periodType = "Yearly";
      }
    }
  }

  #if we need to provide a good default for future periods to forecast
  if ($futurePeriods < 1)
  {
    if ($periodType eq "Weekly")
    {
      $futurePeriods = 13;
    }
    elsif ($periodType eq "Monthly")
    {
      $futurePeriods = 6;
    }
    elsif ($periodType eq "Quarterly")
    {
      $futurePeriods = 4;
    }
    elsif ($periodType eq "Yearly")
    {
      $futurePeriods = 1;
    }
  }

  if ($weekly)
  {
    print("<OPTION>Weekly</OPTION>\n");
  }
  if ($monthly)
  {
    print("<OPTION>Monthly</OPTION>\n");
  }
  if ($quarterly)
  {
    print("<OPTION>Quarterly</OPTION>\n");
  }
  if ($yearly)
  {
    print("<OPTION>Yearly</OPTION>\n");
  }

  print <<END_HTML;
          </SELECT>
          <SCRIPT>
            \$('select#periodType').val('$periodType');
          </SCRIPT>

          <P>&nbsp;</P>

          Number of future time periods to predict:
          <INPUT TYPE="number" CLASS="form-control" NAME="predict" ID="predict" required min=1 value=$futurePeriods>

          <P>&nbsp;</P>
          Choose the starting time period for generating the forecast model:<BR>
          <SELECT CLASS="form-select" NAME="modelStartTime" ID="modelStartTime">
END_HTML

  #retrieve a list of available timeperiods from the data source
  $query = "SELECT ID, name FROM $dsSchema.timeperiods \
      ORDER BY type, duration, endDate";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;

  while (($ID, $name) = $dbOutput->fetchrow_array)
  {
    print("<OPTION VALUE=\"$ID\">$name</OPTION>\n");
    if (!defined($timeperiods))
    {
      $timeperiods = $ID;
    }
  }

  print <<END_HTML;
          </SELECT>
          <SCRIPT>
            \$('select#modelStartTime').val('$timeperiods');
          </SCRIPT>


          <P>&nbsp;</P>

          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onclick="location.href='main.cld'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="submit" ID="btn-submit">Next <I CLASS="bi bi-arrow-right"></I></BUTTON>
          </DIV>

        </DIV>
      </DIV>

      </FORM>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
