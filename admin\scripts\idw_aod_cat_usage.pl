#!/usr/bin/perl

use lib "/opt/apache/app/";

use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::PrepUtils;
use Lib::WebUtils;



  #connect to the database
  $prepDB = PrepUtils_connect_to_database();

  #grab & hash a list of every IDW AOD data flow on the system
  $query = "SELECT sourceInfo FROM prep.flows \
      WHERE sourceInfo LIKE 'FTP=nielsen%' AND userID NOT IN (51,52,53,54,92,95)";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  while (($sourceInfo) = $dbOutput->fetchrow_array)
  {
    $sourceInfo =~ m/^.*PATH=(.*)\.zip/;
    $category = $1;

    #we don't care if the flow is update or historical
    if ($category =~ m/(.*)\-update/)
    {
      $category = $1;
    }

    #turn underscores into spaces
    $category =~ s/_/ /g;

    #uppercase value to match Nielsen formatting
    $category = uc($category);

    $usedCats{$category} += 1;
  }

  #grab & hash a list of every category in the AOD IDW
  opendir(DIRHANDLE, "/data2/beacon");
#  opendir(DIRHANDLE, "/opt/apache/htdocs/prep");
  while (defined($filename = readdir(DIRHANDLE)))
  {
    if ($filename =~ m/^(.*)\-update\.zip$/i)
    {
      $category = $categoryTag = $1;

      #turn underscores into spaces
      $category =~ s/_/ /g;

      #uppercase value to match Nielsen formatting
      $category = uc($category);

      $availableCats{$category} = $categoryTag;

      #get category size
      $zipOutput = `/usr/bin/unzip -l /data2/beacon/$categoryTag.zip`;
      @zipLines = split('\n', $zipOutput);
      $zipLines[6] =~ m/(\d+)\s+2 files/;
      $dataSize = $1;
      $dataSize = $dataSize / 1_000_000;
      $categorySize{$category} = $dataSize;
    }
  }

  foreach $category (sort keys %availableCats)
  {
    $useCount = $usedCats{$category};
    if ($useCount < 1)
    {
      $useCount = "Unused";
    }
    else
    {
      $useCount = "Used in $useCount flow(s)";
    }

    $categoryTag = $availableCats{$category};

    $dataSize = $categorySize{$category};

    print("$category,$useCount,$categoryTag,$dataSize\n");
  }


#EOF
