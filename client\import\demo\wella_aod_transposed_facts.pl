#!/usr/bin/perl

use Text::CSV;


  #redirect STDERR to the Koala error log
  close(STDERR);
  open(STDERR, ">>/opt/apache/htdocs/tmp/koala_error.log");
  select(STDERR);
  $| = 1;

  #instantiate our CSV object
  $csv = Text::CSV->new( {binary => 1} );

  open(INPUT, "$ARGV[0]");
  open(OUTPUT, ">$ARGV[1]");

  #burn the first 2 rows
  $line = <INPUT>;
  $line = <INPUT>;

  #scan through header row, getting date columns & their positions
  $line = <INPUT>;
  $csv->parse($line);
  @columns = $csv->fields();

  $idx = 4;
  $dateCount = 0;
  while (length($columns[$idx]) > 5)
  {
    $columns[$idx] =~ m/^.* \- (.*)$/;
    push(@dateStrings, $1);
    $idx++;
    $dateCount++;
  }

  #headers
  print OUTPUT "Product,PSEG:Brand,Geography,Time Period,\$,Units,Avg Unit Price,\%ACV,TDP,\$ / TDP,Avg # of Items,\$ Shr - Coty Segment,% \$ Disp w/o Feat,% \$ Feat w/o Disp,% \$ Feat & Disp,% \$ Price Decr,PSEG:BC CATEGORY\n";

  #read 12 lines of data
  $line1 = <INPUT>;
  $line2 = <INPUT>;
  $line3 = <INPUT>;
  $line4 = <INPUT>;
  $line5 = <INPUT>;
  $line6 = <INPUT>;
  $line7 = <INPUT>;
  $line8 = <INPUT>;
  $line9 = <INPUT>;
  $line10 = <INPUT>;
  $line11 = <INPUT>;
  $line12 = <INPUT>;

  while (length($line1) > 5)
  {
    $csv->parse($line1);
    @columns1 = $csv->fields();
    $csv->parse($line2);
    @columns2 = $csv->fields();
    $csv->parse($line3);
    @columns3 = $csv->fields();
    $csv->parse($line4);
    @columns4 = $csv->fields();
    $csv->parse($line5);
    @columns5 = $csv->fields();
    $csv->parse($line6);
    @columns6 = $csv->fields();
    $csv->parse($line7);
    @columns7 = $csv->fields();
    $csv->parse($line8);
    @columns8 = $csv->fields();
    $csv->parse($line9);
    @columns9 = $csv->fields();
    $csv->parse($line10);
    @columns10 = $csv->fields();
    $csv->parse($line11);
    @columns11 = $csv->fields();
    $csv->parse($line12);
    @columns12 = $csv->fields();

    $market = $columns1[0];
    $category = $columns1[1];
    $brand = $columns1[2];

    $idx = 4;
    while ($idx < ($dateCount + 4))
    {
      undef(@outputArray);
      $outputArray[0] = $brand;
      $outputArray[1] = $brand;
      $outputArray[2] = $market;
      $outputArray[3] = $dateStrings[$idx-4];

      $outputArray[4] = $columns1[$idx];
      $outputArray[5] = $columns2[$idx];
      $outputArray[6] = $columns3[$idx];
      $outputArray[7] = $columns4[$idx];
      $outputArray[8] = $columns5[$idx];
      $outputArray[9] = $columns6[$idx];
      $outputArray[10] = $columns7[$idx];
      $outputArray[11] = $columns8[$idx];
      $outputArray[12] = $columns9[$idx];
      $outputArray[13] = $columns10[$idx];
      $outputArray[14] = $columns11[$idx];
      $outputArray[15] = $columns12[$idx];

      $outputArray[16] = $category;

      $csv->combine(@outputArray);
      $line = $csv->string();
      print OUTPUT "$line\n";

      $idx++;
    }

    #read next 12 lines of data
    $line1 = <INPUT>;
    $line2 = <INPUT>;
    $line3 = <INPUT>;
    $line4 = <INPUT>;
    $line5 = <INPUT>;
    $line6 = <INPUT>;
    $line7 = <INPUT>;
    $line8 = <INPUT>;
    $line9 = <INPUT>;
    $line10 = <INPUT>;
    $line11 = <INPUT>;
    $line12 = <INPUT>;
  }


#EOF
