#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName Data Selection</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Please Wait...');
  return(true);
}
</SCRIPT>

<STYLE>
.accordion-button.collapsed {
  background: blue
}

.accordion-button.collapsed::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}
</STYLE>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  #output appropriate navigation header for the structure type we're editing
  if ($structType eq "a")
  {
    $dsName = ds_id_to_name($db, $dsID);
    $aggName = $structName;
    if (length($structName) < 1)
    {
      $aggName = DSRagg_id_to_name($db, $dsSchema, $dim, $structID);
    }
    print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/dsr/main.cld">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/dsr/display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Edit Aggregate $aggName</LI>
  </OL>
</NAV>
<P>
END_HTML
  }

  elsif ($structType eq "l")
  {
    $dsName = ds_id_to_name($db, $dsID);
    $listName = $structName;
    if (length($structName) < 1)
    {
      $listName = DSRlist_id_to_name($db, $dsSchema, $dim, $structID);
    }
    print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/dsr/main.cld">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/dsr/display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Edit List $listName</LI>
  </OL>
</NAV>
<P>
END_HTML
  }

  elsif ($structType eq "c")
  {
    $rptName = $structName;
    if (length($structName) < 1)
    {
      $rptName = cube_id_to_name($db, $rptID);
    }
    print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/rpt/main">Reports</A></LI>
    <LI CLASS="breadcrumb-item">$rptName</LI>
    <LI CLASS="breadcrumb-item active">Hierarchy Data Selection</LI>
  </OL>
</NAV>
<P>
END_HTML
  }
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser and get the forecast to be deleted
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  #get CGI parameters
  $dsID = $q->param('ds');
  $dim = $q->param('dim');
  $rptID = $q->param('rptID');
  $segmentationID = $q->param('segmentation');
  $structType = $q->param('st');
  $structID = $q->param('sid');
  $structName = $q->param('name');
  $modifyItem = $q->param('modItem');

  get_cgi_session_info();

  #set human-readable dimension name
  if ($dim eq "p")
  {
    $dimName = "Products";
    $dbName = "product_segment";
    $dbHierName = "product_seghierarchy";
  }
  elsif ($dim eq "g")
  {
    $dimName = "Geographies";
    $dbName = "geography_segment";
    $dbHierName = "geography_seghierarchy";
  }
  elsif ($dim eq "t")
  {
    $dimName = "Time Periods";
    $dbName = "time_segment";
    $dbHierName = "time_seghierarchy";
  }

  #build data source schema name
  $dsSchema = "datasource_" . $dsID;

  #connect to the database
  $db = KAPutil_connect_to_database();

  #figure out which script we're submitting our selections to
  if ($structType eq "l")
  {
    $postScript = "/app/dsr/listEdit.cld";
    $cancelScript = "/app/dsr/display.cld?ds=$dsID";
  }
  elsif ($structType eq "a")
  {
    $postScript = "/app/dsr/aggEdit.cld";
    $cancelScript = "/app/dsr/display.cld?ds=$dsID";
  }
  else
  {
    $postScript = "datasel.cld";
    $cancelScript = "datasel.cld?ds=$dsID&rptID=$rptID&dim=$dim";
  }

  print_html_header();

  #check our permissions
  if ($structType eq "c")
  {
    $privs = cube_rights($db, $userID, $rptID, $acctType);
    if ($privs ne "W")
    {
      exit_error("You don't have privileges to edit this report.");
    }
  }
  else
  {
    $privs = ds_rights($db, $userID, $dsID, $acctType);
    if ($privs ne "W")
    {
      exit_error("You don't have privileges to modify this data source.");
    }
  }

  #get the list of the dimension's item names
  %itemNames = dsr_get_item_name_hash($db, $dsSchema, $dim);

  #if we're dealing with a segmentation hierarchy, set up our names
  if ($segmentationID =~ m/SHS_(\d+)/)
  {
    $fqSegmentationID = $segmentationID;
    $segmentationID = $1;
    $segmentationName = $itemNames{$fqSegmentationID};
  }

  #else we're a segmentation
  else
  {
    $fqSegmentationID = "SEG_$segmentationID";
    $segmentationName = $itemNames{$fqSegmentationID};
  }

  #output selection form for a segmentation
  if ($fqSegmentationID =~ m/SEG_/)
  {

    #if we're modifying an existing entry, extract our segments
    $segSelArray = "";
    if ($modifyItem =~ m/^H\:.*? (.*)$/)
    {
      $segSelArray = $1;
      $segSelArray =~ tr/ /\,/;
    }

    print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-10 col-lg-7 col-xl-6"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Hierarchy Selection</DIV>
        <DIV CLASS="card-body">

          <FORM METHOD="post" ACTION="$postScript" onsubmit="return checkForm(this);">
          <INPUT TYPE="hidden" NAME="ds" VALUE="$dsID">
          <INPUT TYPE="hidden" NAME="rptID" VALUE="$rptID">
          <INPUT TYPE="hidden" NAME="dim" VALUE="$dim">
          <INPUT TYPE="hidden" NAME="st" VALUE="$structType">
          <INPUT TYPE="hidden" NAME="sid" VALUE="$structID">
          <INPUT TYPE="hidden" NAME="name" VALUE="$structName">
          <INPUT TYPE="hidden" NAME="action" VALUE="s">
          <INPUT TYPE="hidden" NAME="selection" VALUE="H:$fqSegmentationID">
          <INPUT TYPE="hidden" NAME="method" VALUE="H">
          <INPUT TYPE="hidden" NAME="modItem" VALUE="$modifyItem">

          Add items from the $segmentationName segmentation that are members of these segments:

          <P>
          <SELECT CLASS="form-select" NAME="segment" ID="segment" SIZE="7" MULTIPLE="multiple" required>
END_HTML

    #output an OPTION for each segment in the selected segmentation
    $query = "SELECT ID, name FROM $dsSchema.$dbName \
        WHERE segmentationID=$segmentationID ORDER BY name";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;

    while (($segmentID, $name) = $dbOutput->fetchrow_array)
    {
      print("   <OPTION VALUE=$segmentID>$name</OPTION>\n");
    }

    print <<END_HTML;
          </SELECT>
          <SCRIPT>
            let selData = '$segSelArray';
            let selArray = selData.split(',');
            \$('#segment').val(selArray);
          </SCRIPT>

          <P>&nbsp;</P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="location.href='$cancelScript'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="submit" ID="btn-submit"><I CLASS="bi bi-save"></I> Save</BUTTON>
          </DIV>
          </FORM>

        </DIV>
      </DIV>
    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML
  }


  #
  #           ------------------------------------------
  #

  #else we're dealing with a segmentation hierarchy
  else
  {

    #now that we know we're a seg hierarchy, change a couple variable names
    #strictly for code readability purposes
    $segHierName = $segmentationName;
    $fqSegHierID = $fqSegmentationID;
    $segHierID = $segmentationID;

    #grab an in-order array of the segmentations contained in the hierarchy
    $query = "SELECT segmentations FROM $dsSchema.$dbHierName WHERE ID=$segHierID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($segmentations) = $dbOutput->fetchrow_array;
    @segmentationIDs = split(',', $segmentations);

    #output scriptable arrays of data for each segmentation in the hierarchy
    print("<SCRIPT>\n");
    foreach $segmentationID (@segmentationIDs)
    {
      %segHash = DSRseg_get_segments_hash($db, $dsSchema, $dim, $segmentationID);
      #output in-order array of segment IDs, sorted by their name
      $arrayStr = "seg_$segmentationID = [";
      foreach $id (sort {$segHash{$a} cmp $segHash{$b}} keys %segHash)
      {
        $arrayStr .= "\"$id\",";
      }
      chop($arrayStr);
      $arrayStr .= "];\n";
      print($arrayStr);

      #output in-order array of segment names in alpha order
      $arrayStr = "segname_$segmentationID = [";
      foreach $id (sort {$segHash{$a} cmp $segHash{$b}} keys %segHash)
      {
        $name = $segHash{$id};
        $name =~ s/\"/\\"/g;
        $arrayStr .= "\"$name\",";
      }
      chop($arrayStr);
      $arrayStr .= "];\n";
      print($arrayStr);
    }

    #assemble an array to handle level changes - a segmentation ID's index in
    #the array is equal to its position in the hierarchy
    $jsSegPosArray = "";
    $count = 1;
    foreach $segmentationID (@segmentationIDs)
    {
      $jsSegPosArray = $jsSegPosArray . "segPosArray[$segmentationID] = $count;\n";
      $count++;
    }

    #if we're modifying an existing selection
    $segHierLevelVal = $segmentationIDs[0];
    $segmentationVal = $segmentationIDs[0];
    $segSelArray = "";
    $intersectCheck = "";
    if (length($modifyItem) > 0)
    {
      if ($modifyItem =~ m/^SH\:.*? (.*?) (.*?) ([0-9,\s]+)/)
      {
        $segHierLevelVal = $1;
        $segmentationVal = $2;
        $segSelArray = $3;
        $segSelArray =~ tr/ /,/;
      }

      if ($modifyItem =~ m/INTERSECT/)
      {
        $intersectCheck = "CHECKED";
      }
    }

    print <<END_HTML;
function flipSegHier()
{
  let itemID, itemName;
  let segSel = document.getElementById('segment');
  let segmentationID = document.getElementById('segmentation').value;
  let idArrName = 'seg_' + segmentationID;
  let nameArrName = 'segname_' + segmentationID;
  let shLevel = document.getElementById('segHierLevel').value;
  let shFilter = document.getElementById('segmentation').value;

  let segPosArray = [0];
  $jsSegPosArray

  if (segPosArray[shLevel] < segPosArray[shFilter])
  {
    document.getElementById('segHierLevel').value = shFilter;
  }

  let nameArray = window[nameArrName];
  let idArray = window[idArrName];

  //empty out current values
  let i = segment.options.length;
  while (i--)
  {
    segment.remove(i);
  }

  for (i = 0; i < idArray.length; i++)
  {
    let opt = document.createElement('option');
    opt.text = nameArray[i];
    opt.value = idArray[i];
    segment.add(opt);
  }
}



function handleLevelChange()
{
  let shLevel = document.getElementById('segHierLevel').value;
  let shFilter = document.getElementById('segmentation').value;

  let segPosArray = [0];
  $jsSegPosArray

  if (segPosArray[shLevel] < segPosArray[shFilter])
  {
    document.getElementById('segmentation').value = shLevel;
    flipSegHier();
  }
}
</SCRIPT>

<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-12 col-lg-10 col-xl-8"> <!-- content -->

      <FORM METHOD="post" ACTION="$postScript" onsubmit="return checkForm(this);">

      <DIV CLASS="accordion mx-auto" ID="accordion">
        <DIV CLASS="accordion-item border-primary">
          <H2 CLASS="accordion-header">
            <BUTTON CLASS="accordion-button bg-primary text-white icon-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
              Segmentation Hierarchy Selection
            </BUTTON>
          </H2>
          <DIV ID="collapse1" CLASS="accordion-collapse collapse show" data-bs-parent="#accordion">
            <DIV CLASS="accordion-body">
              <INPUT TYPE="hidden" NAME="ds" VALUE="$dsID">
              <INPUT TYPE="hidden" NAME="rptID" VALUE="$rptID">
              <INPUT TYPE="hidden" NAME="dim" VALUE="$dim">
              <INPUT TYPE="hidden" NAME="st" VALUE="$structType">
              <INPUT TYPE="hidden" NAME="sid" VALUE="$structID">
              <INPUT TYPE="hidden" NAME="name" VALUE="$structName">
              <INPUT TYPE="hidden" NAME="action" VALUE="s">
              <INPUT TYPE="hidden" NAME="selection" VALUE="SH:$fqSegHierID">
              <INPUT TYPE="hidden" NAME="method" VALUE="SH">
              <INPUT TYPE="hidden" NAME="modItem" VALUE="$modifyItem">

              <DIV CLASS="row">
                <DIV CLASS="col-auto gx-1 mt-1 ms-2">
                  Add $dimName from the $segHierName segmentation hierarchy at the
                </DIV>
                <DIV CLASS="col-auto gx-1">
                  <SELECT CLASS="form-select" NAME="segHierLevel" id="segHierLevel" onChange="handleLevelChange();">
END_HTML

    #output an OPTION for each segmentation in the selected seghierarchy
    foreach $segmentationID (@segmentationIDs)
    {
      $key = "SEG_" . $segmentationID;
      print("   <OPTION VALUE=$segmentationID>$itemNames{$key} level</OPTION>\n");
    }

    print <<END_HTML;
                    <OPTION VALUE="ITEM">Item level</OPTION>
                  </SELECT>
                  <SCRIPT>
                    \$('#segHierLevel').val('$segHierLevelVal');
                  </SCRIPT>
                </DIV>
              </DIV>
              <DIV CLASS="row mt-3">
                <DIV CLASS="col-auto gx-1 mt-1 ms-2">
                  where
                </DIV>
                <DIV CLASS="col-auto gx-1">
                  <SELECT CLASS="form-select" NAME="segmentation" id="segmentation" onChange="flipSegHier();">
END_HTML

    #output an OPTION for each segmentation in the selected seghierarchy
    $query = "SELECT segmentations FROM $dsSchema.$dbHierName WHERE ID=$segHierID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($segmentations) = $dbOutput->fetchrow_array;

    @segmentationIDs = split(',', $segmentations);
    $initialSegmentation = $segmentationVal;

    foreach $segmentationID (@segmentationIDs)
    {
      $key = "SEG_" . $segmentationID;
      print("   <OPTION VALUE=$segmentationID>$itemNames{$key}</OPTION>\n");
    }

    print <<END_HTML;
                  </SELECT>
                  <SCRIPT>
                    \$('#segmentation').val('$segmentationVal');
                  </SCRIPT>
                </DIV>
                <DIV CLASS="col-auto gx-1 mt-1">
                  is
                </DIV>
                <DIV CLASS="col-auto gx-1">
                  <SELECT CLASS="form-select" NAME="segment" ID="segment" STYLE="width:250px; vertical-align:text-top;" SIZE="8" MULTIPLE required>
END_HTML

    #output an OPTION for each segment in the selected segmentation
    $query = "SELECT ID, name FROM $dsSchema.$dbName \
        WHERE segmentationID=$initialSegmentation ORDER BY name";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;

    while (($segmentID, $name) = $dbOutput->fetchrow_array)
    {
      print("   <OPTION VALUE=$segmentID>$name</OPTION>\n");
    }

    print <<END_HTML;
                  </SELECT>
                  <SCRIPT>
                    let selData = '$segSelArray';
                    let selArray = selData.split(',');
                    \$('#segment').val(selArray);
                  </SCRIPT>
                </DIV>
              </DIV>

              <P>

            </DIV>
          </DIV>
        </DIV>

        <DIV CLASS="accordion-item border-primary">
          <H2 CLASS="accordion-header">
            <BUTTON CLASS="accordion-button collapsed bg-primary text-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
              Advanced
            </BUTTON>
          </H2>
          <DIV ID="collapse2" CLASS="collapse collapsed" data-bs-parent="#accordion">
            <DIV CLASS="accordion-body">

              <B>Selection Type:</B><BR>
              <DIV CLASS="form-check">
                <INPUT CLASS="form-check-input" TYPE="radio" NAME="selOp" ID="add" CHECKED VALUE="add">
                <LABEL CLASS="form-check-label" FOR="add">Add the items from this selection to previously selected items.</LABEL>
              </DIV>
              <DIV CLASS="form-check">
                <INPUT CLASS="form-check-input" TYPE="radio" NAME="selOp" VALUE="intersect" ID="intersect" $intersectCheck>
                <LABEL CLASS="form-check-label" FOR="intersect">Intersect the items from this selection to previously selected items (i.e., only keep previously selected items that are also in this selection, and vice versa).</LABEL>
              </DIV>

            </DIV>
          </DIV>
        </DIV>
      </DIV>

      <P>
      <DIV CLASS="text-center">
        <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="location.href='$cancelScript'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
        <BUTTON CLASS="btn btn-primary" TYPE="submit" ID="btn-submit"><I CLASS="bi bi-save"></I> Save</BUTTON>
      </DIV>

      </FORM>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML
  }

  print_html_footer();

#EOF
