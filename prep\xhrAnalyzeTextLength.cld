#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::WebUtils;


#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $flowID = $q->param('f');
  $jobID = $q->param('j');
  $colID = $q->param('col');

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #make sure we have read privs for this data flow
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to analyze this data flow.");
  }

  $flowName = prep_flow_id_to_name($prepDB, $flowID);

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #build name of column containing the numerical data
  $column = "column_" . $colID;

  #get the name of the column we're analyzing
  $query = "SELECT name FROM $masterColTable WHERE ID=$colID";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($colName) = $dbOutput->fetchrow_array;

  #grab the field length data from the raw table
  $query = "SELECT CHAR_LENGTH($column) AS length, COUNT($column) AS count \
      FROM $masterTable GROUP BY length ORDER BY length";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;

  #build up the JSON data string with the bucket data
  $jsonData = "";
  while (($val, $count) = $dbOutput->fetchrow_array)
  {
    $jsonData = $jsonData . "{";
    $jsonData = $jsonData . " \"label\": \"$val\",";
    $jsonData = $jsonData . " \"value\": \"$count\"";
    $jsonData = $jsonData . "},\n";
  }
  chop($jsonData);  chop($jsonData);

  print <<END_HTML;
<SCRIPT>
FusionCharts.ready(function()
{
  let fcTextLength = new FusionCharts(
  {
    type: 'column2d',
    renderAt: 'chart-container-text-length',
    width: '400',
    height: '300',
    dataFormat: 'json',
    dataSource:
    {
      "chart":
      {
        "paletteColors": "#0075c2",
        "xAxisName": "Length",
        "yAxisNAme": "Count",
        "bgColor": "#ffffff",
        "showBorder": "0",
        "showCanvasBorder": "0",
        "showValues": "0",
        "usePlotGradientColor": "0",
        "plotBorderAlpha": "10",
        "placeValuesInside": "0",
        "valueFontColor": "#ffffff",
        "showAxisLines": "1",
        "axisLineAlpha": "25",
        "divLineAlpha": "10",
        "toolTipColor": "#ffffff",
        "toolTipBorderThickness": "0",
        "toolTipBgColor": "#000000",
        "toolTipBgAlpha": "80",
        "toolTipBorderRadius": "2",
        "toolTipPadding": "5"
      },
      "data": [ $jsonData ]
    }
  });
  fcTextLength.render();
});
</SCRIPT>

<DIV CLASS="modal-dialog" STYLE="overflow-y: initial !important;">
  <DIV CLASS="modal-content">

    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Text Length Analysis</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">
      <DIV id="chart-container-text-length"></DIV>
    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-primary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-check-lg"></I> Done</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML

  $query = "UPDATE prep.jobs SET lastAction=NOW() WHERE ID=$jobID";
  $prepDB->do($query);

  prep_audit($prepDB, $userID, "Performed text length analysis on $colName", $flowID);
  utils_slack("PREP: $first $last performed text length analysis on $colName in $flowName");


#EOF
