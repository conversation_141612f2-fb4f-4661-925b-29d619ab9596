
package Lib::ExcelReports;

use lib "/opt/apache/app/";

use Exporter;
use Excel::Writer::XLSX;

use Lib::DataSel;
use Lib::DSRMeasures;
use Lib::DSRUtils;
use Lib::Reports;

our @ISA = ('Exporter');

our @EXPORT = qw(&excel_insert_table
   &excel_insert_graph
   &excel_insert_visuals);



#-------------------------------------------------------------------------------
#
# Handle a database error of some kind during measure calculation
#

sub excelrpt_db_err
{
  my ($date);

  my ($db, $status, $text) = @_;

  if (!defined($status))
  {
    $date = localtime();
    print STDERR "$date: $text\n";
    if ($db->errstr =~ m/^MySQL server has gone away/)
    {
      die("Lost connection to database, terminating");
    }
  }
}



#-------------------------------------------------------------------------------
#
# Insert the specified table into the supplied workbook at the specified
# location. Returns the index of the last row in the table.
#

sub excel_insert_table
{
  my ($query, $dbOutput, $status, $dsSchema, $rptCube, $rowIdx, $col);
  my ($name, $dsID, $selProductsString, $selGeographiesString, $selTimesString);
  my ($prodDisplayIDs, $geoDisplayIDs, $timeDisplayIDs, $offset, $excludeNA);
  my ($numProducts, $numGeographies, $numTimeperiods, $measureCol, $colName);
  my ($filterMeas1, $filterMeas2, $filterMeas3, $whereClause, $cutoff);
  my ($filterNum1, $filterNum2, $filterNum3, $item, $cubeMeasure, $measureID);
  my ($filterOp1, $filterOp2, $filterOp3, $dimSelStr, $dim, $hardLimit);
  my ($sortMeas1, $sortMeas2, $sortMeas3, $dimCount, $limitClause);
  my ($sortOrder1, $sortOrder2, $sortOrder3, $orderByClause, $loadAll);
  my ($selMeasuresString, $measuresString, $first, $excludeZeroClause);
  my ($chart, $rowDim, $tableDesign, $id, $first, $excludeNAClause);
  my ($colDim, $topBottomProdIDs, $topBottomGeoIDs, $topBottomTimeIDs, $d);
  my ($tableProdItemIDs, $tableGeoItemIDs, $tableTimeItemIDs, $dimCols);
  my ($dimCol, $str, $str1, $str2, $str3, $idx, $concatIDstr, $colStripeDim);
  my ($formatCol, $colIdx, $hashRef, $formatMeasure, $excelNumFormat);
  my ($lastCol, $startRow, $banding, $cellStyle, $dataCellStyle);
  my ($valArrayIdx, $rowLength, $itemID, $lastRow, $tablePreset, $style);
  my ($numRowDims, $numStripeCols, $rangeEnd, $filterWhere, $filterDimStr);
  my ($dimConcat, $startRowIdx, $startColIdx, $colStripe);
  my ($formatHeader, $headerFontColor, $headerBgColor, $headerWrap);
  my ($headerOutline, $headerFont, $headerFontSize);
  my ($formatValues, $formatAltValues, $valueFontColor, $valueBgColor);
  my ($valueFont, $valueFontSize, $verticalGrid, $verticalGridColor);
  my ($verticalGridWidth, $horizontalGrid, $horizontalGridColor);
  my ($horizontalGridWidth, $rowAlt, $formatRow);
  my ($valueAlternateFontColor, $valueAlternateBgColor);
  my (@dimProducts, @dimGeographies, @dimTimes, @dimMeasures, @cubeMeasures);
  my (@temp, @dimProducts, @dispMeasures, @rowDims, @tmp, @tableCols);
  my (@selProdIDs, @selGeoIDs, @selTimeIDs, @rowVals, @filterDims, @colStripes);
  my (@concatIDstrings, @tableColsNonref, @measureExcelAltFormats);
  my (@measureExcelFormats);
  my (%selProducts, %selGeographies, %selTimes, %selMeasures, %formatHash);
  my (%productNameHash, %geographyNameHash, %timeNameHash, %measureNameHash);
  my (%colNameHash);

  my ($db, $rptID, $userID, $acctType, $visID, $workbook, $worksheet, $graphCount, $insertRow, $insertCol, $fixedDim, $fixedItem) = @_;


  $rowIdx = $insertRow;

  #get info about the table from the cubes database
  $query = "SELECT name, dsID, measures, slicers FROM cubes WHERE ID=$rptID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  excelrpt_db_err($db, $status, $query);
  ($name, $dsID, $measuresString, $slicersStr) = $dbOutput->fetchrow_array;

  $query = "SELECT tableRowDims, tableColDims, design FROM visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  excelrpt_db_err($db, $status, $query);
  ($rowDim, $colDim, $tableDesign) = $dbOutput->fetchrow_array;

  ($selProductsString, $selGeographiesString, $selTimesString, $selMeasuresString) = reports_get_selected_items($db, $visID, $userID, $acctType);

  #our filter dimensions are everything not in a row
  undef(@filterDims);
  if (!($rowDim =~ m/p/))
  {
    push(@filterDims, "p");
  }
  if (!($rowDim =~ m/g/))
  {
    push(@filterDims, "g");
  }
  if (!($rowDim =~ m/t/))
  {
    push(@filterDims, "t");
  }

  #assemble datasource and cube names
  $dsSchema = "datasource_" . $dsID;
  $rptCube = "_rptcube_" . $rptID;

  #handle a "fixed" dimension selection (used for expanded reports)
  if ($fixedDim eq "p")
  {
    $selProductsString = $fixedItem;
  }
  elsif ($fixedDim eq "g")
  {
    $selGeographiesString = $fixedItem;
  }
  elsif ($fixedDim eq "t")
  {
    $selTimesString = $fixedItem;
  }

  #turn the available item IDs into an array, selected items into hash
  @dimProducts = datasel_get_dimension_items($db, $rptID, "p");
  undef(@temp);
  @temp = split(/,/, $selProductsString);
  undef(%selProducts);
  foreach $item (@temp)
  {
    $selProducts{$item} = 1;
  }

  @dimGeographies = datasel_get_dimension_items($db, $rptID, "g");
  undef(@temp);
  @temp = split(/,/, $selGeographiesString);
  undef(%selGeographies);
  foreach $item (@temp)
  {
    $selGeographies{$item} = 1;
  }

  @dimTimes = datasel_get_dimension_items($db, $rptID, "t");
  undef(@temp);
  @temp = split(/,/, $selTimesString);
  undef(%selTimes);
  foreach $item (@temp)
  {
    $selTimes{$item} = 1;
  }

  @dimMeasures = datasel_get_dimension_items($db, $rptID, "m");

  #figure out which measures we need to display, being careful to keep them
  #in the order the user selected them in the data selector
  undef(@dispMeasures);
  @temp = split(/,/, $selMeasuresString);
  @cubeMeasures = split(/,/, $measuresString);
  undef(%selMeasures);
  foreach $item (@temp)
  {
    $selMeasures{$item} = 1;
  }
  foreach $cubeMeasure (@cubeMeasures)
  {
    if ($selMeasures{$cubeMeasure} eq 1)
    {
      push(@dispMeasures, $cubeMeasure);
    }
  }

  #get the names matching the IDs for dimension items
  %productNameHash = dsr_get_item_name_hash($db, $dsSchema, "p");
  %geographyNameHash = dsr_get_item_name_hash($db, $dsSchema, "g");
  %timeNameHash = dsr_get_item_name_hash($db, $dsSchema, "t");
  %measureNameHash = dsr_get_item_name_hash($db, $dsSchema, "m");

  @selProdIDs = split(',', $selProductsString);
  @selGeoIDs = split(',', $selGeographiesString);
  @selTimeIDs = split(',', $selTimesString);
  $numProducts = scalar @selProdIDs;
  $numGeographies = scalar @selGeoIDs;
  $numTimeperiods = scalar @selTimeIDs;

  #if we're outputting a table by itself, output selection info in top left
  if ($graphCount == 0)
  {
    if ($numProducts == 1)
    {
      $name = $productNameHash{$selProdIDs[0]};
      $worksheet->write($rowIdx, 0, $name);
      $rowIdx++;
    }
    if ($numGeographies == 1)
    {
      $name = $geographyNameHash{$selGeoIDs[0]};
      $worksheet->write($rowIdx, 0, $name);
      $rowIdx++;
    }
    if ($numTimeperiods == 1)
    {
      $name = $timeNameHash{$selTimeIDs[0]};
      $worksheet->write($rowIdx, 0, $name);
      $rowIdx++;
    }
  }

  #for simpler regexes, make sure there's a comma on the end of selection
  #strings
  $selProductsString .= ",";
  $selGeographiesString .= ",";
  $selTimesString .= ",";

  #build up the measure portion of the SQL query string
  $measureCol = "";
  foreach $measureID (@dispMeasures)
  {
    if (($measureID =~ m/ATT_/) || ($measureID =~ m/SEG_/))
    {
      $measureCol .= $measureID . ",";
    }
    else
    {
      $measureCol .= "measure_" . $measureID . ",";
    }
  }
  chop($measureCol);

  #add the dimensions we're displaying as rows to the list of data fields to
  #be selected
  @rowDims = split(',', $rowDim);
  $dimSelStr = "";
  foreach $dim (@rowDims)
  {
    if ($dim eq "p")
    {
     $dimSelStr .= "product,";
    }
    if ($dim eq "g")
    {
     $dimSelStr .= "geography,";
    }
    if ($dim eq "t")
    {
     $dimSelStr .= "time,";
    }
  }
  $measureCol = $dimSelStr . $measureCol;

  #XXX Hackety hack, don't fall back
  $rowDim = $rowDims[0];

  #start by grabbing the first user-selected item in each dimension
  #(we're going to expand out the strings for each of our row dimensions later)
  @tmp = split(',', $selProductsString);
  $prodDisplayIDs = "\'" . $tmp[0] . "\'";
  @tmp = split(',', $selGeographiesString);
  $geoDisplayIDs = "\'" . $tmp[0] . "\'";
  @tmp = split(',', $selTimesString);
  $timeDisplayIDs = "\'" . $tmp[0] . "\'";

  #foreach row dimension, expand its selection string
  undef(@tmp);
  push(@tmp, @rowDims);
  push(@tmp, $colDim);
  foreach $dim (@tmp)
  {
    if ($dim eq "p")
    {
      undef($prodDisplayIDs);
      foreach $item (@dimProducts)
      {
        if ($selProducts{$item} == 1)
        {
          $prodDisplayIDs .= "\'$item\',";
        }
      }
      chop($prodDisplayIDs);
    }
    elsif ($dim eq "g")
    {
      undef($geoDisplayIDs);
      foreach $item (@dimGeographies)
      {
        if ($selGeographies{$item} == 1)
        {
          $geoDisplayIDs .= "\'$item\',";
        }
      }
      chop($geoDisplayIDs);
    }
    elsif ($dim eq "t")
    {
      undef($timeDisplayIDs);
      foreach $item (@dimTimes)
      {
        if ($selTimes{$item} == 1)
        {
          $timeDisplayIDs .= "\'$item\',";
        }
      }
      chop($timeDisplayIDs);
    }
  }

  #NB: For top/bottom filtering, we need to use only one item for non-displayed
  #    dimensions
  $topBottomProdIDs = $prodDisplayIDs;
  $topBottomGeoIDs = $geoDisplayIDs;
  $topBottomTimeIDs = $timeDisplayIDs;

  foreach $d (@filterDims)
  {
    if ($d eq "p")
    {
      if ($topBottomProdIDs =~ m/^(.*?),/)
      {
        $topBottomProdIDs = $1;
      }
    }
    elsif ($d eq "g")
    {
      if ($topBottomGeoIDs =~ m/^(.*?),/)
      {
        $topBottomGeoIDs = $1;
      }
    }
    elsif ($d eq "t")
    {
      if ($topBottomTimeIDs =~ m/^(.*?),/)
      {
        $topBottomTimeIDs = $1;
      }
    }
  }

  #NB: we're going to cheat and use the same IDs for the table item selections
  #    in a bit
  $tableProdItemIDs = $topBottomProdIDs;
  $tableGeoItemIDs = $topBottomGeoIDs;
  $tableTimeItemIDs = $topBottomTimeIDs;

  #assemble the WHERE portion of our SQL query to retrieve table data
  $whereClause = "product IN ($tableProdItemIDs) AND geography IN ($tableGeoItemIDs) AND time IN ($tableTimeItemIDs)";

  #implement table filtering by adding additional clauses to the WHERE portion
  $filterMeas1 = reports_get_style($tableDesign, "filterMeas1");
  $filterMeas2 = reports_get_style($tableDesign, "filterMeas2");
  $filterMeas3 = reports_get_style($tableDesign, "filterMeas3");
  $filterOp1 = reports_get_style($tableDesign, "filterOp1");
  $filterOp2 = reports_get_style($tableDesign, "filterOp2");
  $filterOp3 = reports_get_style($tableDesign, "filterOp3");
  $filterNum1 = reports_get_style($tableDesign, "filterNum1");
  $filterNum2 = reports_get_style($tableDesign, "filterNum2");
  $filterNum3 = reports_get_style($tableDesign, "filterNum3");
  $excludeNA = reports_get_style($tableDesign, "excludeNA");

  undef($hardLimit);

  #make sure the measure the rule depends on exists, or inactivate it
  if (length($measureNameHash{$filterMeas1}) < 1)
  {
    $filterMeas1 = 0;
  }
  if (length($measureNameHash{$filterMeas2}) < 1)
  {
    $filterMeas2 = 0;
  }
  if (length($measureNameHash{$filterMeas3}) < 1)
  {
    $filterMeas3 = 0;
  }

  #gate the number of top/bottom items we're going to show - refactor this
  if (($filterOp1 eq "bottom") || ($filterOp1 eq "top"))
  {
    if ($filterNum1 > 5000)
    {
      $filterNum1 = 5000;
    }
    elsif ($filterNum1 < 0)
    {
      $filterNum1 = 0;
    }
    $hardLimit = $filterNum1;
  }
  if (($filterOp2 eq "bottom") || ($filterOp2 eq "top"))
  {
    if ($filterNum2 > 5000)
    {
      $filterNum2 = 5000;
    }
    elsif ($filterNum2 < 0)
    {
      $filterNum1 = 0;
    }
    $hardLimit = $filterNum2;
  }
  if (($filterOp3 eq "bottom") || ($filterOp3 eq "top"))
  {
    if ($filterNum3 > 5000)
    {
      $filterNum3 = 5000;
    }
    elsif ($filterNum3 < 0)
    {
      $filterNum3 = 0;
    }
    $hardLimit = $filterNum3;
  }

  #append filtering clauses to master WHERE clause, if appropriate
  $whereClause = reports_table_filter_SQL($db, $dsSchema, $rptCube, $whereClause,
      $filterMeas1, $filterOp1, $filterNum1,
      $topBottomProdIDs, $topBottomGeoIDs, $topBottomTimeIDs);
  $whereClause = reports_table_filter_SQL($db, $dsSchema, $rptCube, $whereClause,
      $filterMeas2, $filterOp2, $filterNum2,
      $topBottomProdIDs, $topBottomGeoIDs, $topBottomTimeIDs);
  $whereClause = reports_table_filter_SQL($db, $dsSchema, $rptCube, $whereClause,
      $filterMeas3, $filterOp3, $filterNum3,
      $topBottomProdIDs, $topBottomGeoIDs, $topBottomTimeIDs);

  #handle an "Exclude NA and 0" filtering directive
  if ($excludeNA == 1)
  {
    $primaryDim = $rowDims[0];
    $whereClause = reports_table_excludeNA_SQL($db, $dsSchema, $rptCube, $whereClause,
        $primaryDim, $prodDisplayIDs, $geoDisplayIDs, $timeDisplayIDs, @dispMeasures);
  }

  #if the user has specified a slicer, let's add it to the where clause
  @slicers = split(',', $slicersStr);
  foreach $slicer (@slicers)
  {
    $slicer =~ m/(PSEG_\d+):(\d+)/;
    $segID = $1;
    $segmentID = $2;

    if ($segmentID > 0)
    {
      $tmp = "SMT_$segmentID";
      $segmentName = $db->quote($productNameHash{$tmp});
      $whereClause .= " AND $segID = $segmentName";
    }
  }

  #handle the ORDER BY portion of our SQL query, start by looking for an
  #explicit user-defined sort order we should be using
  $sortMeas1 = reports_get_style($tableDesign, "sortMeas1");
  $sortMeas2 = reports_get_style($tableDesign, "sortMeas2");
  $sortMeas3 = reports_get_style($tableDesign, "sortMeas3");
  $sortOrder1 = reports_get_style($tableDesign, "sortOrder1");
  $sortOrder2 = reports_get_style($tableDesign, "sortOrder2");
  $sortOrder3 = reports_get_style($tableDesign, "sortOrder3");

  $dimCount = @rowDims;

  $orderByClause = "";

  #if the first sorting criteria is specified
  if (($sortMeas1 > 0) || (length($sortMeas1) > 1))
  {
    $orderByClause = "ORDER BY ";

    #figure out our fully-qualified measure ID if a financial measure
    if ($sortMeas1 =~ /^\d+$/)
    {
      $sortMeas1 = "measure_" . $sortMeas1;
    }
    elsif ($sortMeas1 eq "time")
    {
      $sortMeas1 = "sortTime";
    }

    $orderByClause .= "$sortMeas1 $sortOrder1 ";

    #if the second sorting criteria is specified
    if (($sortMeas2 > 0) || (length($sortMeas2) > 1))
    {

      #figure out our fully-qualified measure ID if a financial measure
      if ($sortMeas2 =~ /^\d+$/)
      {
        $sortMeas2 = "measure_" . $sortMeas2;
      }
      elsif ($sortMeas2 eq "time")
      {
        $sortMeas2 = "sortTime";
      }

      $orderByClause .= ", $sortMeas2 $sortOrder2 ";

      #if the third sorting criteria is specified
      if (($sortMeas3 > 0) || (length($sortMeas3) > 1))
      {

        #figure out our fully-qualified measure ID if a financial measure
        if ($sortMeas3 =~ /^\d+$/)
        {
          $sortMeas3 = "measure_" . $sortMeas3;
        }
        elsif ($sortMeas3 eq "time")
        {
          $sortMeas3 = "sortTime";
        }

        $orderByClause .= ", $sortMeas3 $sortOrder3 ";
      }
    }
  }

  #elsif we're displaying a multi-dimension pivot table
  elsif ($dimCount > 1)
  {
    $orderByClause = "ORDER BY ";
    foreach $dim (@rowDims)
    {
      if ($dim eq "p")
      {
        $orderByClause .= "FIELD(product, $prodDisplayIDs),";
      }
      elsif ($dim eq "g")
      {
        $orderByClause .= "FIELD(geography, $geoDisplayIDs),";
      }
      if ($dim eq "t")
      {
        $orderByClause .= "sortTime DESC,";
      }
    }
    chop($orderByClause);
  }

  #elsif the time dimension is the only row dimension
  elsif ($rowDims[0] eq "t")
  {
    $orderByClause = "ORDER BY sortTime DESC";
  }

  #else order by the primary dimension in the order the user selected
  else
  {
    if ($rowDim eq "p")
    {
      $orderByClause = "ORDER BY FIELD(product, $prodDisplayIDs)";
    }
    elsif ($rowDim eq "g")
    {
      $orderByClause = "ORDER BY FIELD(geography, $geoDisplayIDs)";
    }
    elsif ($rowDim eq "t")
    {
      $orderByClause = "ORDER BY FIELD(time, $timeDisplayIDs)";
    }
  }

  #if we're doing nested columns, we have a hard limit of 5000 rows
  #NB: The code that calls us is responsible for erroring out and warning
  #    the user if they're trying to export more than 1M rows to Excel
  $limitClause = "";
  if (length($dimCols) > 0)
  {
    $limitClause = "LIMIT 5000";
  }

  #grab the rows we're going to display, in order (we need this to do nested
  #columns)
  $dimCol = "";
  foreach $dim (@rowDims)
  {
    if ($dim eq "p")
    {
      $dimCol .= "product,";
    }
    if ($dim eq "g")
    {
      $dimCol .= "geography,";
    }
    if ($dim eq "t")
    {
      $dimCol .= "time,";
    }
  }
  chop($dimCol);

  #build up the in-order selection strings for our table
  $str1 = "";
  $str2 = "";
  $str3 = "";

  $query = "SELECT $dimCol FROM $dsSchema.$rptCube \
      WHERE $whereClause $orderByClause $limitClause";

  #force a flush and reconnect to the database. No idea why, but if we don't do
  #this then MySQL force-disconnects us here when we run large queries
  $db = KAPutil_connect_to_database();

  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  excelrpt_db_err($db, $status, $query);
  while ((@rowVals) = $dbOutput->fetchrow_array)
  {
    $str1 .= $rowVals[0] . ",";
    $str2 .= $rowVals[1] . ",";
    $str3 .= $rowVals[2] . ",";
  }
  chop($str1);
  chop($str2);
  chop($str3);

  #build up dimension ID strings that we're going to use for comparison with
  #the SQL CONCAT operator. Ex: if a row is products and geos, and one such
  #row has the IDs AGG_1 for prod and 7 for geo, the string will be AGG_1-7
  #NB: we can be blind about which dimension is which - we just care about
  #    IDs at this point. We'll sort it out later when we build the CONCAT
  #    portion of the SQL query
  undef(@concatIDstrings);
  if (defined($rowDims[0]))
  {
    @tmp = split(',', $str1);
    $idx = 0;
    foreach $id (@tmp)
    {
      $concatIDstrings[$idx] = $tmp[$idx];
      $idx++;
    }
  }
  if (defined($rowDims[1]))
  {
    @tmp = split(',', $str2);
    $idx = 0;
    foreach $id (@tmp)
    {
      $concatIDstrings[$idx] .= "-" . $tmp[$idx];
      $idx++;
    }
  }
  if (defined($rowDims[2]))
  {
    @tmp = split(',', $str3);
    $idx = 0;
    foreach $id (@tmp)
    {
      $concatIDstrings[$idx] .= "-" . $tmp[$idx];
      $idx++;
    }
  }

  #build the concat test string
  $concatIDstr = "";
  foreach $str (@concatIDstrings)
  {
    $concatIDstr .= "'$str',";
  }
  chop($concatIDstr);

  #tell the worksheet to keep leading zeroes in output (for UPCs and such)
  $worksheet->keep_leading_zeros();

  #define basic column format
  $formatCol = $workbook->add_format();

  #if we have nested columns, create the array of each column "stripe" we need
  if ($colDim eq "p")
  {
    $colStripeDim = "product";
    @colStripes = split(',', $selProductsString);
  }
  elsif ($colDim eq "g")
  {
    $colStripeDim = "geography";
    @colStripes = split(',', $selGeographiesString);
  }
  elsif ($colDim eq "t")
  {
    $colStripeDim = "time";
    @colStripes = split(',', $selTimesString);
  }
  else
  {
    $colStripes[0] = "PASSTHROUGH";
  }

  #build up the Excel format for the header cells
  $formatHeader = $workbook->add_format();
  $formatHeader->set_border_color('white');
  $formatHeader->set_border(1);
  $formatHeader->set_bold();

  $headerFontColor = reports_get_style($tableDesign, "headerFontColor");
  if (length($headerFontColor) < 7)
  {
    $headerFontColor = reports_get_style($tableDesign, "headercolor");
  }
  if (length($headerFontColor) < 1)
  {
    $formatHeader->set_color("#ffffff");
  }
  else
  {
    $formatHeader->set_color($headerFontColor);
  }

  $headerBgColor = reports_get_style($tableDesign, "headerBgColor");
  if (length($headerBgColor) < 7)
  {
    $headerBgColor = reports_get_style($tableDesign, "headerbg");
  }
  if (length($headerBgColor) < 1)
  {
    $formatHeader->set_bg_color("#333333");
  }
  else
  {
    $formatHeader->set_bg_color($headerBgColor);
  }

  $headerWrap = reports_get_style($tableDesign, "headerWrap");
  if ($headerWrap != 0)
  {
    $formatHeader->set_text_wrap();
  }

  $headerFontSize = reports_get_style($tableDesign, "headerFontSize");
  if ($headerFontSize < 1)
  {
    $formatHeader->set_size(11);
  }
  else
  {
    $formatHeader->set_size($headerFontSize);
  }

  $headerFont = reports_get_style($tableDesign, "headerFont");
  if ((length($headerFont) < 3) || ($headerFont =~ m/^Helvetica/))
  {
    #NO-OP - keeping default font
  }
  else
  {
    $formatHeader->set_font($headerFont);
  }

  $headerOutline = reports_get_style($tableDesign, "headerOutline");
  if ($headerOutline eq "none")
  {
    #NOOP
  }
  if ($headerOutline eq "bottom")
  {
    $formatHeader->set_bottom_color("#01B8AA");
  }
  elsif ($headerOutline eq "top")
  {
    $formatHeader->set_top_color("#01B8AA");
  }
  elsif ($headerOutline eq "left")
  {
    $formatHeader->set_left_color("#01B8AA");
  }
  elsif ($headerOutline eq "right")
  {
    $formatHeader->set_right_color("#01B8AA");
  }
  elsif ($headerOutline eq "topbottom")
  {
    $formatHeader->set_top_color("#01B8AA");
    $formatHeader->set_bottom_color("#01B8AA");
  }
  elsif ($headerOutline eq "leftright")
  {
    $formatHeader->set_left_color("#01B8AA");
    $formatHeader->set_right_color("#01B8AA");
  }
  elsif ($headerOutline eq "frame")
  {
    $formatHeader->set_border_color("#01B8AA");
  }
  else    #default to bottom-only border
  {
    $formatHeader->set_border_color("#01B8AA");
  }

  #build up the Excel format for the value cells
  $formatValues = $workbook->add_format();
  $formatAltValues = $workbook->add_format();

  $valueFontColor = reports_get_style($tableDesign, "valueFontColor");
  $valueAlternateFontColor = reports_get_style($tableDesign, "valueAlternateFontColor");
  if (length($valueFontColor) < 7)
  {
    $valueFontColor = reports_get_style($tableDesign, "cellcolor");
    $valueAlternateFontColor = reports_get_style($tableDesign, "cellcolor");
  }
  if (length($valueFontColor) < 1)
  {
    $formatValues->set_color("#333333");
  }
  else
  {
    $formatValues->set_color($valueFontColor);
  }
  if (length($valueAlternateFontColor) < 1)
  {
    $formatAltValues->set_color("#333333");
  }
  else
  {
    $formatAltValues->set_color($valueAlternateFontColor);
  }

  $valueBgColor = reports_get_style($tableDesign, "valueBgColor");
  $valueAlternateBgColor = reports_get_style($tableDesign, "valueAlternateBgColor");
  if (length($valueBgColor) < 7)
  {
    $valueBgColor = reports_get_style($tableDesign, "cellcolor");
  }
  if (length($valueAlternateBgColor) < 7)
  {
    $valueAlternateBgColor = reports_get_style($tableDesign, "cellband");
  }
  if (length($valueBgColor) < 1)
  {
    $formatValues->set_bg_color("#FFFFFF");
  }
  else
  {
    $formatValues->set_bg_color($valueBgColor);
  }
  if (length($valueAlternateBgColor) < 1)
  {
    $formatAltValues->set_bg_color("#efefef");
  }
  else
  {
    $formatAltValues->set_bg_color($valueAlternateBgColor);
  }

  $valueFont = reports_get_style($tableDesign, "valueFont");
  if ((length($valueFont) < 3) || ($valueFont =~ m/^Helvetica/))
  {
    #NO-OP - keeping default font
  }
  else
  {
    $formatValues->set_font($valueFont);
    $formatAltValues->set_font($valueFont);
  }

  $valueFontSize = reports_get_style($tableDesign, "valueFontSize");
  if ($valueFontSize < 1)
  {
    $formatValues->set_size(11);
    $formatAltValues->set_size(11);
  }
  else
  {
    $formatValues->set_size($valueFontSize);
    $formatAltValues->set_size($valueFontSize);
  }

  $verticalGrid = reports_get_style($tableDesign, "verticalGrid");
  if ($verticalGrid != 0)
  {
    $verticalGridColor = reports_get_style($tableDesign, "verticalGridColor");
    if (length($verticalGridColor) < 7)
    {
      $verticalGridColor = "#ffffff";
    }
    $formatHeader->set_left_color($verticalGridColor);
    $formatHeader->set_right_color($verticalGridColor);
    $formatValues->set_left_color($verticalGridColor);
    $formatValues->set_right_color($verticalGridColor);
    $formatAltValues->set_left_color($verticalGridColor);
    $formatAltValues->set_right_color($verticalGridColor);

    $verticalGridWidth = reports_get_style($tableDesign, "verticalGridWidth");
    if ($verticalGridWidth > 2)
    {
      $verticalGridWidth = 5;   #NB: this is actually an Excel style index
    }
    elsif ($verticalGridWidth == 2)
    {
      $verticalGridWidth = 2;
    }
    elsif ($verticalGridWidth == 1)
    {
      $verticalGridWidth = 1;
    }
    else
    {
      $verticalGridWidth = 0;
    }
    $formatHeader->set_left($verticalGridWidth);
    $formatHeader->set_right($verticalGridWidth);
    $formatValues->set_left($verticalGridWidth);
    $formatValues->set_right($verticalGridWidth);
    $formatAltValues->set_left($verticalGridWidth);
    $formatAltValues->set_right($verticalGridWidth);
  }

  $horizontalGrid = reports_get_style($tableDesign, "horizontalGrid");
  if ($horizontalGrid != 0)
  {
    $horizontalGridColor = reports_get_style($tableDesign, "horizontalGridColor");
    if (length($horizontalGridColor) < 7)
    {
      $horizontalGridColor = "#ffffff";
    }
    $formatHeader->set_top_color($horizontalGridColor);
    $formatHeader->set_bottom_color($horizontalGridColor);
    $formatValues->set_top_color($horizontalGridColor);
    $formatValues->set_bottom_color($horizontalGridColor);
    $formatAltValues->set_top_color($horizontalGridColor);
    $formatAltValues->set_bottom_color($horizontalGridColor);

    $horizontalGridWidth = reports_get_style($tableDesign, "horizontalGridWidth");
    if ($horizontalGridWidth > 2)
    {
      $horizontalGridWidth = 5;   #NB: this is actually an Excel style index
    }
    elsif ($horizontalGridWidth == 2)
    {
      $horizontalGridWidth = 2;
    }
    elsif ($horizontalGridWidth == 1)
    {
      $horizontalGridWidth = 1;
    }
    else
    {
      $horizontalGridWidth = 0;
    }
    $formatHeader->set_top($horizontalGridWidth);
    $formatHeader->set_bottom($horizontalGridWidth);
    $formatValues->set_top($horizontalGridWidth);
    $formatValues->set_bottom($horizontalGridWidth);
    $formatAltValues->set_top($horizontalGridWidth);
    $formatAltValues->set_bottom($horizontalGridWidth);
  }

  #if the table has a title/subtitle, write them out
  if ($tableDesign =~ m/title:"(.*?)",/)
  {
    $title = $1;

    #handle the styling of the title and subtitle
    $formatTitle = $workbook->add_format();
    $captionFontSize = reports_get_style($tableDesign, "captionFontSize");
    if ($captionFontSize < 5)
    {
      $captionFontSize = 18;
    }
    $formatTitle->set_size($captionFontSize);
    $formatTitle->set_align('center');

    $title = reports_expand_dim_tags($db, $dsSchema, $title, $selProductsString, $selGeographiesString, $selTimesString, $selMeasuresString);

    #add a blank row, then the title and subtitle
    $rowIdx++;
    $rangeEnd = scalar(@dispMeasures);
    $worksheet->merge_range($rowIdx, 0, $rowIdx, $rangeEnd, $title, $formatTitle);
    $rowIdx++;
  }

  if ($tableDesign =~ m/subcaption:"(.*?)",/)
  {
    $subcaption = $1;

    $formatSubtitle = $workbook->add_format();
    $subcaptionFontSize = int($captionFontSize * 0.75);
    $formatSubtitle->set_size($subcaptionFontSize);
    $formatSubtitle->set_align('center');

    $subcaption = reports_expand_dim_tags($db, $dsSchema, $subcaption, $selProductsString, $selGeographiesString, $selTimesString, $selMeasuresString);

    $rangeEnd = scalar(@dispMeasures);
    $worksheet->merge_range($rowIdx, 0, $rowIdx, $rangeEnd, $subcaption, $formatSubtitle);
    $rowIdx++;
  }

  #if we're displaying nested columns, display a meta-header row that groups
  #the various stripe columns together
  if (length($colDim) > 0)
  {

    $colIdx = $insertCol;
    $rowIdx++;

    #skip over dimension columns
    $numRowDims = scalar @rowDims;
    $colIdx += $numRowDims;

    #output a header row for each stripe that covers all columns in the stripe
    $numStripeCols = scalar @dispMeasures;
    foreach $id (@colStripes)
    {
      if ($colDim eq "p")
      {
        $name = $productNameHash{$id};
      }
      elsif ($colDim eq "g")
      {
        $name = $geographyNameHash{$id};
      }
      elsif ($colDim eq "t")
      {
        $name = $timeNameHash{$id};
      }

      $rangeEnd = $colIdx + $numStripeCols - 1;
      if ($colIdx != $rangeEnd)
      {
        $worksheet->merge_range($rowIdx, $colIdx, $rowIdx, $rangeEnd, $name, $formatHeader);
      }
      else	#handle case where stripe only has one column in it
      {
        $worksheet->write($rowIdx, $colIdx, $name, $formatHeader);
      }

      $colIdx += $numStripeCols;
    }
  }

  #format the columns that're going to hold dimension item names
  undef(%colNameHash);
  $colIdx = $insertCol;
  foreach $dim (@rowDims)
  {
    $worksheet->set_column($colIdx, $colIdx, 28, $formatCol);

    if ($dim eq "p")
    {
      $hashRef = { header => "Product"};
      push(@tableCols, $hashRef);
      push(@tableColsNonref, "Product");
      $colNameHash{'product'} = 1;
    }
    if ($dim eq "g")
    {
      $hashRef = { header => "Geography"};
      push(@tableCols, $hashRef);
      push(@tableColsNonref, "Geography");
      $colNameHash{'geography'} = 1;
    }
    if ($dim eq "t")
    {
      $hashRef = { header => "Time Period"};
      push(@tableCols, $hashRef);
      push(@tableColsNonref, "Time Period");
      $colNameHash{'time period'} = 1;
    }

    $colIdx++;
  }

  #
  # Build up Excel formats for each measure we're outputting
  #

  #get the hash of format strings for every measure in our data source
  %formatHash = DSRmeasures_get_format_hash($db, $dsID);

  #build up arrays of Excel styles to use on measure values
  $idx = scalar(@rowDims);  #skip over dimension columns
  foreach $measureID (@dispMeasures)
  {

    #get the Excel-styled number format string based on user settings
    $excelNumFormat = DSRmeasures_format_excel($db, $dsSchema, $measureID, $formatHash{$measureID});

    #create a copy of the "regular" value formatting, and add numerical setting
    $formatMeasure = $workbook->add_format();
    $formatMeasure->copy($formatValues);
    $formatMeasure->set_num_format($excelNumFormat);
    $measureExcelFormats[$idx] = $formatMeasure;

    #create a copy of the "alternate" value formatting, and add numerical setting
    $formatMeasure = $workbook->add_format();
    $formatMeasure->copy($formatAltValues);
    $formatMeasure->set_num_format($excelNumFormat);
    $measureExcelAltFormats[$idx] = $formatMeasure;

    $idx++;
  }

  #output header row for each set of cols, and apply measure formats to columns
  foreach $col (@colStripes)
  {
    foreach $measureID (@dispMeasures)
    {
      $hashRef = { header => $colName };
      push(@tableCols, $hashRef);
      push(@tableColsNonref, $measureNameHash{$measureID});

      $colIdx++;
    }
  }

  $lastCol = $colIdx - 1;
  $startRow = $rowIdx + 1;
  $rowIdx++; $rowIdx++;

  #output the header row
  $worksheet->write($startRow, $insertCol, \@tableColsNonref, $formatHeader);

  #build our where clause for filter dimensions
  $filterWhere = "";
  $filterDimStr = join(',', @filterDims);
  if ($filterDimStr =~ m/p/)
  {
    $filterWhere = "AND product IN ($prodDisplayIDs) ";
  }
  if ($filterDimStr =~ m/g/)
  {
    $filterWhere .= "AND geography IN ($geoDisplayIDs) ";
  }
  if ($filterDimStr =~ m/t/)
  {
    $filterWhere .= "AND time IN ($timeDisplayIDs) ";
  }

  #build up the concat string format for SQL comparison purposes
  $dimConcat = "";
  $first = 1;
  foreach $dim (@rowDims)
  {
    if ($first == 1)
    {
      $first = 0;
    }
    else
    {
      $dimConcat .= ", '-', ";
    }

    if ($dim eq "p")
    {
      $dimConcat .= "product";
    }
    elsif ($dim eq "g")
    {
      $dimConcat .= "geography";
    }
    elsif ($dim eq "t")
    {
      $dimConcat .= "time";
    }
  }

  #if something has gone really wrong and the report is going to be empty,
  #dump out now
  if (length($concatIDstr) < 1)
  {
    return(0);
  }

  #run through each column "stripe", pulling the data we need
  #NB: we're building up an in-order array of the values for each row
  #    in the display table, which we'll output after we're done grabbing
  #    all the data
  $first = 1;           #determine if we're the first "stripe" - output dim data
  $startRowIdx = $rowIdx;
  $startColIdx = $insertCol;
  foreach $colStripe (@colStripes)
  {
    $rowIdx = $startRowIdx;

    if ($colStripe eq "PASSTHROUGH")
    {

      #if we're doing a non-nested column table
      $query = "SELECT $measureCol, CONCAT($dimConcat) AS selStr \
          FROM $dsSchema.$rptCube \
          WHERE CONCAT($dimConcat) IN ($concatIDstr) $filterWhere \
          ORDER BY FIELD(selStr, $concatIDstr)";
    }

    #else we're doing nested columns
    else
    {
      $query = "SELECT $measureCol, CONCAT($dimConcat) AS selStr \
          FROM $dsSchema.$rptCube \
          WHERE CONCAT($dimConcat) IN ($concatIDstr) $filterWhere AND $colStripeDim = '$colStripe' \
          ORDER BY FIELD(selStr, $concatIDstr)";
    }

    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    excelrpt_db_err($db, $status, $query);

    #output data row for each item in our rowDim
    $rowAlt = 0;
    while ((@rowVals) = $dbOutput->fetchrow_array)
    {
      $colIdx = $startColIdx;
      $valArrayIdx = 0;
      $rowLength = scalar(@rowVals) - 1;

      #flip back and forth between alternate row styling
      if ($rowAlt == 0)
      {
        $formatRow = $formatValues;
        $rowAlt = 1;
      }
      else
      {
        $formatRow = $formatAltValues;
        $rowAlt = 0;
      }

      #if we're the first column stripe, output dimension data
      if ($first == 1)
      {
        foreach $dim (@rowDims)
        {
          $itemID = $rowVals[$valArrayIdx];
          if ($dim eq "p")
          {
            $worksheet->write($rowIdx, $colIdx, "$productNameHash{$itemID}", $formatRow);
          }
          elsif ($dim eq "g")
          {
            $worksheet->write($rowIdx, $colIdx, "$geographyNameHash{$itemID}", $formatRow);
          }
          elsif ($dim eq "t")
          {
            $worksheet->write($rowIdx, $colIdx, "$timeNameHash{$itemID}", $formatRow);
          }
          $colIdx++;
          $valArrayIdx++;
        }
      }

      #if we aren't the first column stripe, skip over dimension data
      else
      {
        foreach $dim (@rowDims)
        {
          $valArrayIdx++;
        }
      }

      while ($valArrayIdx < $rowLength)
      {
        if ($rowAlt == 0)
        {
          $worksheet->write($rowIdx, $colIdx, "$rowVals[$valArrayIdx]", $measureExcelAltFormats[$valArrayIdx]);
        }
        else
        {
          $worksheet->write($rowIdx, $colIdx, "$rowVals[$valArrayIdx]", $measureExcelFormats[$valArrayIdx]);
        }
        $colIdx++;
        $valArrayIdx++;
      }

      $rowIdx++;
    }

    if ($first == 1)
    {
      $startColIdx += scalar(@rowDims);
      $first = 0;
    }

    $startColIdx += $numStripeCols;
  }

  $lastRow = $rowIdx - 1;

  #
  # Conditional formatting
  #

  #NB: the idea is to build an array corresponding to each measure's location
  #    in the table (accounting for "column stripes" aka nested tables), and
  #    then cycle through the list of conditional formatting rules applying
  #    each as setup by the user

  #we start with the left-most column of the table
  $index = $insertCol;

  #move right the number of columns required by the dimension info
  $index += scalar(@rowDims);

  #for each column stripe in the table
  #NB: this works for "straight" (non-striped/non-nested) tables without any
  #    trickery because @colStripes contains a single PASSTHROUGH in that case
  foreach $stripe (@colStripes)
  {

    #foreach measure in the stripe
    foreach $measureID (@dispMeasures)
    {
      $measureColMapArray[$index] = $measureID;
      $index++;
    }
  }

  #cycle through any conditional formatting styles for the table
  $index = 1;
  $condFormatName = "condFormat" . $index;
  $condFormatStr = reports_get_style($tableDesign, $condFormatName);
  while (defined($condFormatStr))
  {

    #if it's an "in between" formatting rule
    #3 ib 50 90 ffff00
    if ($condFormatStr =~ m/^(\d+) ib (.*?) (.*?) (.*)$/)
    {
      $measureID = $1;
      $criteria = "between";
      $min = $2;
      $max = $3;
      $color = $4;
    }

    #3 gt 90 92d050
    elsif ($condFormatStr =~ m/^(\d+) (.*?) (.*?) (.*)$/)
    {
      $measureID = $1;
      $criteria = $2;
      $value = $3;
      $color = $4;
    }
    else
    {
      $index++;
      $condFormatName = "condFormat" . $index;
      $condFormatStr = reports_get_style($tableDesign, $condFormatName);
      next;
    }

    #convert our criteria rule into Excel format
    if ($criteria eq "gt")
    {
      $criteria = "greater than";
    }
    if ($criteria eq "lt")
    {
      $criteria = "less than";
    }
    if ($criteria eq "eq")
    {
      $criteria = "equal to";
    }

    #set up our new cell style
    $condFormat = $workbook->add_format;
    $color = "#" . $color;
    $condFormat->set_bg_color($color);

    #cycle through the array of measures/columns, looking for the measure this
    #rule applies to
    $i = 0;
    while ($i <= scalar(@measureColMapArray))
    {
      if ($measureColMapArray[$i] == $measureID)
      {
        if ($criteria eq "between")
        {
          $worksheet->conditional_formatting($startRowIdx, $i, $lastRow, $i,
          {
            type => 'cell',
            criteria => $criteria,
            maximum => $max,
            minimum => $min,
            format => $condFormat
          });
        }
        else
        {
          $worksheet->conditional_formatting($startRowIdx, $i, $lastRow, $i,
          {
            type => 'cell',
            criteria => $criteria,
            value => $value,
            format => $condFormat
          });
        }
      }

      $i++;
    }

    $index++;
    $condFormatName = "condFormat" . $index;
    $condFormatStr = reports_get_style($tableDesign, $condFormatName);
  }

  return($rowIdx);
}



#-------------------------------------------------------------------------------
#
# Inserts the specified graph into the supplied worksheet
# Horrifically messy function (uses lots of globals) to insert a graph
#

sub excel_insert_graph
{
  my ($query, $dbOutput, $dsSchema, $rptCube, $designColName, $dsID);
  my ($dataSheet, $dataSheetName, $graphDesign, $graph_x, $graph_y);
  my ($lockedMeasure, $graphCaption, $showBorder, $useRoundEdges, $bgColor);
  my ($chartObj, $values, $categories, $graphType, $rowIdx, $colIdx, $value);
  my ($paletteColors, $graphSheetName, $status, $measureID, $measureCol);
  my ($productIDstring, $geographyIDstring, $timeIDstring, $measureIDstring);
  my ($q_geography, $q_time, $q_product, $row, $item, $setID);
  my (@setIDs, @categoryIDs);
  my (%setNames, %categoryNames);

  my ($db, $rptID, $userID, $acctType, $visID, $workbook, $worksheet, $insertRow, $insertCol, $fixedDim, $fixedItem) = @_;


  #get our graph styling info from the database
  $query = "SELECT design,graph_x,graph_y FROM visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  excelrpt_db_err($db, $status, $query);
  ($graphDesign, $graph_x, $graph_y) = $dbOutput->fetchrow_array;

  $graphType = reports_get_style($graphDesign, "type");

  # ------- parse graph design info ----------

  #handle a "locked" measure for the graph
  if ($graphDesign =~ m/,measure:(\d+),/)
  {
    $lockedMeasure = $1;
    if (defined($lockedMeasure))
    {
      @setIDs = ($lockedMeasure);
    }
  }

  #handle a caption (chart title)
  if ($graphDesign =~ m/,caption:\"(.*?)\",/)
  {
    $graphCaption = $1;
  }

  #handle a border
  $showBorder = reports_get_style($graphDesign, "showBorder");

  #handle "round edges" styling
  $useRoundEdges = reports_get_style($graphDesign, "useRoundEdges");

  #handle background color
  $bgColor = reports_get_style($graphDesign, "bgColor");

  #handle palette colors
  $paletteColors = reports_get_style($graphDesign, "paletteColors");


  # ------- end of design parsing ----------


  #get the list of axes & selected dimension items from the database
  $query = "SELECT dsID FROM cubes WHERE ID=$rptID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  excelrpt_db_err($db, $status, $query);
  ($dsID) = $dbOutput->fetchrow_array;

  ($productIDstring, $geographyIDstring, $timeIDstring, $measureIDstring) = reports_get_selected_items($db, $visID, $userID, $acctType);

  #add worksheet to hold graph data
  $graphSheetName = $worksheet->get_name();
  $dataSheetName = "Data" . $visID . $graphSheetName;
  $dataSheetName = substr($dataSheetName, 0, 30);
  $dataSheet = $workbook->add_worksheet("$dataSheetName");

  #if we're exporting a single-series graph type
  if (($graphType eq "2DPie") || ($graphType eq "3DPie") ||
      ($graphType eq "2DDonut") || ($graphType eq "3DDonut"))
  {

    #handle a "fixed" dimension selection (used for expanded reports)
    if ($fixedDim eq "p")
    {
      $productIDstring = $fixedItem;
    }
    elsif ($fixedDim eq "g")
    {
      $geographyIDstring = $fixedItem;
    }
    elsif ($fixedDim eq "t")
    {
      $timeIDstring = $fixedItem;
    }

    #assemble datasource and cube names
    $dsSchema = "datasource_" . $dsID;
    $rptCube = "_rptcube_" . $rptID;

    #get the ID of the measure we're graphing
    $measureIDstring =~ m/(\d+)/;
    $measureID = $1;

    #fetch the item names for our data set
    %setNames = dsr_get_item_name_hash($db, $dsSchema, $graph_x);

    #create an array of the categories we need to display, and go ahead and
    #set up our "single selection" dimension IDs while we're at it
    if ($graph_x eq "p")
    {
      @setIDs = split(/,/, $productIDstring);

      $q_geography = $db->quote($geographyIDstring);
      $q_time = $db->quote($timeIDstring);
    }
    elsif ($graph_x eq "g")
    {
      @setIDs = split(/,/, $geographyIDstring);

      $q_product = $db->quote($productIDstring);
      $q_time = $db->quote($timeIDstring);
    }
    elsif ($graph_x eq "t")
    {
      @setIDs = split(/,/, $timeIDstring);

      $q_product = $db->quote($productIDstring);
      $q_geography = $db->quote($geographyIDstring);
    }

    #output the data set into the "Data" worksheet
    $measureCol = "measure_" . $measureID;
    $row = 0;
    foreach $item (@setIDs)
    {

      if ($graph_x eq "p")
      {
        $q_product = $db->quote($item);
        $query = "SELECT $measureCol FROM $dsSchema.$rptCube \
            WHERE product=$q_product AND geography=$q_geography AND time=$q_time \
            ORDER BY $measureCol DESC";
      }
      elsif ($graph_x eq "g")
      {
        $q_geography = $db->quote($item);
        $query = "SELECT $measureCol FROM $dsSchema.$rptCube \
        WHERE product=$q_product AND geography=$q_geography AND time=$q_time \
        ORDER BY $measureCol DESC";
      }
      elsif ($graph_x eq "t")
      {
        $q_time = $db->quote($item);
        $query = "SELECT $measureCol FROM $dsSchema.$rptCube \
        WHERE product=$q_product AND geography=$q_geography AND time=$q_time \
        ORDER BY $measureCol DESC";
      }

      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      excelrpt_db_err($db, $status, $query);
      ($value) = $dbOutput->fetchrow_array;

      $dataSheet->write($row, 0, $setNames{$item});
      $dataSheet->write($row, 1, $value);
      $row++;
    }

    #set the category and value ranges for our chart
    $categories = "='" . $dataSheetName . "'!\$A\$1:\$A\$$row";
    $values = "='" . $dataSheetName . "'!\$B\$1:\$B\$$row";

    #hide the data sheet
    $dataSheet->hide();

    #create chart
    if (($graphType eq "2DPie") || ($graphType eq "3DPie"))
    {
      $chartObj = $workbook->add_chart(type=> 'pie', embedded => 1);
    }

    if (($graphType eq "2DDonut") || ($graphType eq "3DDonut"))
    {
      $chartObj = $workbook->add_chart(type => 'doughnut', embedded => 1);
    }

    #format series
    $chartObj->add_series(
      categories => $categories,
      values => $values,
    );

    #set the graph's title
#      $chartObj->set_title($graphCaption);

    $chartObj->set_style(26);

    #insert the chart into the first page of the workbook
    $worksheet->insert_chart($insertRow, $insertCol, $chartObj);
  }

  #if we're exporting a multi-series graph type
  if (($graphType eq "Lines") || ($graphType eq "ZoomLine") ||
      ($graphType eq "3DColumns") || ($graphType eq "2DBars") ||
      ($graphType eq "3DBars") || ($graphType eq "Radar") ||
      ($graphType eq "Area") || ($graphType eq "2DColumns"))
  {

    #handle a "fixed" dimension selection (used for expanded reports)
    if ($fixedDim eq "p")
    {
      $productIDstring = $fixedItem;
    }
    elsif ($fixedDim eq "g")
    {
      $geographyIDstring = $fixedItem;
    }
    elsif ($fixedDim eq "t")
    {
      $timeIDstring = $fixedItem;
    }

    #assemble report cube name
    $dsSchema = "datasource_" . $dsID;
    $rptCube = "_rptcube_" . $rptID;

    #fetch the item names for our categories
    %categoryNames = dsr_get_item_name_hash($db, $dsSchema, $graph_x);

    #fetch the item names for our data sets (these are always measures here)
    %setNames = dsr_get_item_name_hash($db, $dsSchema, $graph_y);

    #create an array of the categories we need to display, and go ahead and set
    #up our "single selection" dimension IDs while we're at it
    if ($graph_x eq "p")
    {
      @categoryIDs = split(/,/, $productIDstring);

      $q_geography = $db->quote($geographyIDstring);
      $q_time = $db->quote($timeIDstring);
    }
    elsif ($graph_x eq "g")
    {
      @categoryIDs = split(/,/, $geographyIDstring);

      $q_product = $db->quote($productIDstring);
      $q_time = $db->quote($timeIDstring);
    }
    elsif ($graph_x eq "t")
    {
      @categoryIDs = split(/,/, $timeIDstring);

      $q_product = $db->quote($productIDstring);
      $q_geography = $db->quote($geographyIDstring);
    }

    #create an array of the data sets to display (always measures here)
    @setIDs = split(/,/, $measureIDstring);

    # ------- parse graph design info ----------

    #handle a "locked" measure for the graph
    if ($graphDesign =~ m/,measure:(\d+),/)
    {
      $lockedMeasure = $1;
      if (defined($lockedMeasure))
      {
        @setIDs = ($lockedMeasure);
      }
    }

    #handle a caption (chart title)
    if ($graphDesign =~ m/,caption:\"(.*?)\",/)
    {
      $graphCaption = $1;
    }

    #handle a border
    $showBorder = reports_get_style($graphDesign, "showBorder");

    #handle "round edges" styling
    $useRoundEdges = reports_get_style($graphDesign, "useRoundEdges");

    #handle background color
    $bgColor = reports_get_style($graphDesign, "bgColor");

    #handle palette colors
    $paletteColors = reports_get_style($graphDesign, "paletteColors");

    # ------- end of design parsing ----------

    $rowIdx = 1;
    foreach $item (@categoryIDs)
    {
      $dataSheet->write($rowIdx, 0, $categoryNames{$item});
      $rowIdx++;
    }

    #output the data sets (measures)
    $colIdx = 1;
    foreach $setID (@setIDs)
    {
      $dataSheet->write(0, $colIdx, $setNames{$setID});
      $measureCol = "measure_" . $setID;

      $rowIdx = 1;
      foreach $item (@categoryIDs)
      {

        if ($graph_x eq "p")
        {
          $q_product = $db->quote($item);
          $query = "SELECT $measureCol FROM $dsSchema.$rptCube \
              WHERE product=$q_product AND geography=$q_geography AND time=$q_time";
        }
        elsif ($graph_x eq "g")
        {
          $q_geography = $db->quote($item);
          $query = "SELECT $measureCol FROM $dsSchema.$rptCube \
              WHERE product=$q_product AND geography=$q_geography AND time=$q_time";
        }
        elsif ($graph_x eq "t")
        {
          $q_time = $db->quote($item);
          $query = "SELECT $measureCol FROM $dsSchema.$rptCube \
              WHERE product=$q_product AND geography=$q_geography AND time=$q_time";
        }

        $dbOutput = $db->prepare($query);
        $status = $dbOutput->execute;
        excelrpt_db_err($db, $status, $query);

        ($value) = $dbOutput->fetchrow_array;

        $dataSheet->write($rowIdx, $colIdx, $value);
        $rowIdx++;
      }

      $colIdx++;
    }

    #set the category and value ranges for our chart
    $categories = "='" . $dataSheetName . "'!\$A\$1:\$A\$$rowIdx";
    $values = "='" . $dataSheetName . "'!\$B\$1:\$B\$$rowIdx";

    #hide the data sheet
    $dataSheet->hide();

    #create chart
    if (($graphType eq "2DColumns") || ($graphType eq "3DColumns"))
    {
      $chartObj = $workbook->add_chart(type=> 'column', embedded => 1);
    }
    elsif (($graphType eq "Lines") || ($graphType eq "ZoomLine"))
    {
      $chartObj = $workbook->add_chart(type => 'line', embedded => 1);
    }
    elsif (($graphType eq "2DBars") || ($graphType eq "3DBars"))
    {
      $chartObj = $workbook->add_chart(type => 'bar', embedded => 1);
    }
    elsif ($graphType eq "Area")
    {
      $chartObj = $workbook->add_chart(type => 'area', embedded => 1);
    }
    elsif ($graphType eq "Radar")
    {
      $chartObj = $workbook->add_chart(type => 'radar', embedded => 1);
    }

    #format series
    $chartObj->add_series(
      categories => $categories,
      values => $values,
    );

    #set the graph's title
#    $chartObj->set_title($graphCaption);

    $chartObj->set_style(26);

    #insert the chart into the first page of the workbook
    $worksheet->insert_chart($insertRow, $insertCol, $chartObj);
  }
}



#-------------------------------------------------------------------------------
#
# Insert all of the visual elements (graphs, tables, etc.) contained in a Koala
# report into an Excel worksheet object.
#

sub excel_insert_visuals
{
  my ($query, $dbOutput, $visID, $insertRow, $insertCol, $graphs, $rowIdx);
  my ($status);

  my ($db, $rptID, $userID, $acctType, $workbook, $worksheet, $fixedDim, $fixedItem) = @_;


  #see if we have any graphs that need to be written out to the worksheet
  $query = "SELECT ID FROM visuals WHERE cubeID=$rptID AND type='chart'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  excelrpt_db_err($db, $status, $query);

  $insertRow = 0;
  $insertCol = 0;
  $graphs = 0;
  while (($visID) = $dbOutput->fetchrow_array)
  {
    excel_insert_graph($db, $rptID, $userID, $acctType, $visID, $workbook, $worksheet, $insertRow, $insertCol, $fixedDim, $fixedItem);

    # XXX  For the moment, output all graphs in a row across top of worksheet
    #      In the future, try to approximate x and y positions in visualization
    $insertCol = $insertCol + 8;
    $graphs++;
  }

  #now, let's output any tables we have
  $query = "SELECT ID FROM visuals WHERE cubeID=$rptID AND type='table'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  excelrpt_db_err($db, $status, $query);

  $insertCol = 0;
  if ($graphs > 0)
  {
    $insertRow = 20;
  }

  while (($visID) = $dbOutput->fetchrow_array)
  {
    $rowIdx = excel_insert_table($db, $rptID, $userID, $acctType, $visID, $workbook, $worksheet, $graphs, $insertRow, $insertCol, $fixedDim, $fixedItem);

    # XXX  For the moment, output all graphs in a row across top of worksheet
    #      In the future, try to approximate x and y positions in visualization
    $insertRow = $insertRow + $rowIdx + 5;
    $graphs++;
  }
}


#-------------------------------------------------------------------------------



1;
