#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;


#-------------------------------------------------------------------------

sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $userOnly = $q->param('user');
  $filterName = $q->param('n');
  $filterType = $q->param('t');
  $filterOwner = $q->param('o');
  $filterUpdate = $q->param('u');
  $filterDesc = $q->param('d');

  print("Expires: -1\n");
  print("Cache-Control: private, max-age=0");
  print("Content-type: application/json\n\n");

  #connect to the database
  $db = KAPutil_connect_to_database();

  %userNames = utils_get_user_hash($db);

  #get list of all active jobs for display
  $query = "SELECT userID, dsID, operation FROM app.jobs";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  while (($dsUserID, $dsID, $operation) = $dbOutput->fetchrow_array)
  {
    if ($operation eq "DS-UPDATE")
    {
      $activeJobs{$dsID} = "Update in progress ($userNames{$dsUserID})";
    }
    elsif ($operation eq "ROLLBACK")
    {
      $activeJobs{$dsID} = "Rolling back update ($userNames{$dsUserID})";
    }
    elsif ($operation eq "XFER-MEASURES")
    {
      $activeJobs{$dsID} = "Transferring structures ($userNames{$dsUserID})";
    }
    elsif ($operation eq "FORCE-REFRESH")
    {
      $activeJobs{$dsID} = "Forced calculation refresh ($userNames{$dsUserID})";
    }
    elsif ($operation eq "COPY-DS")
    {
      $activeJobs{$dsID} = "Copying data source ($userNames{$dsUserID})";
    }
    elsif ($operation eq "ADD-MEASURE")
    {
      $activeJobs{$dsID} = "Creating calculated measure ($userNames{$dsUserID})";
    }
  }

  @userSources = ds_list($db, $userID, $acctType);
  $sources = join(',', @userSources);

  if ($userOnly > 0)
  {
    $query = "SELECT ID, name, type, lastUpdate, userID, description, status, UNIX_TIMESTAMP(lastModified) \
        FROM dataSources WHERE userID=$userID AND deleted = 0 ORDER BY name";
  }
  else
  {
    $query = "SELECT ID, name, type, lastUpdate, userID, description, status, UNIX_TIMESTAMP(lastModified) \
        FROM dataSources WHERE ID IN ($sources) AND deleted = 0 ORDER BY name";
  }
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  #print json opening bracket
  print("[\n");

  while (($dsID, $name, $type, $lastUpdate, $dsUserID, $description, $dsStatus, $lastModified) = $dbOutput->fetchrow_array)
  {
    $active = 0;
    if (length($activeJobs{$dsID}) > 0)
    {
      $description = $activeJobs{$dsID};
      $active = 1;
    }

    #implement basic filtering
    if (defined($filterName))
    {
      if (!($name =~ m/$filterName/i))
      {
        next;
      }
    }
    if (defined($filterType))
    {
      if (!($type =~ m/$filterType/i))
      {
        next;
      }
    }
    if (defined($filterOwner))
    {
      if (!($userNames{$dsUserID} =~ m/$filterOwner/i))
      {
        next;
      }
    }
    if (defined($filterUpdate))
    {
      if (!($lastUpdate =~ m/$filterUpdate/i))
      {
        next;
      }
    }
    if (defined($filterDesc))
    {
      if (!($description =~ m/$filterDesc/i))
      {
        next;
      }
    }

    #if an update error occurred, let the user know
    if ($dsStatus =~ m/^ERROR:/)
    {
      $name = "<I STYLE='color:red;' CLASS='bi bi-exclamation-triangle' TITLE='Update error'></I> $name";
    }

    $jsonStr .= <<JSON_LABEL;
    {
      "ID": $dsID,
      "active": "$active",
      "Name": "$name",
      "Type": "$type",
      "Last Update": "$lastUpdate",
      "Owner": "$userNames{$dsUserID}",
      "Description": "$description"
    },
JSON_LABEL
  }

  #close json data block
  chop($jsonStr);  chop($jsonStr);
  print("$jsonStr");
  print("]\n");

#EOF
