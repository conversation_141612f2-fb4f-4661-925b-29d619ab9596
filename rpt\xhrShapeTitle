#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DSRUtils;
use Lib::DataSources;
use Lib::Reports;
use Lib::WebUtils;


#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $rptID = $q->param('rptID');
  $visID = $q->param('v');

  $t = $q->param('t');
  $fontColor = $q->param('fontColor');
  $titleFontSize = $q->param('titleFontSize');
  $bgColor = $q->param('bgColor');
  $titleTextAlign = $q->param('titleTextAlign');
  $titleTextFont = $q->param('titleTextFont');

  $fontColor = "#" . $fontColor;
  $bgColor = "#" . $bgColor;

  $db = KAPutil_connect_to_database();

  #make sure we have edit privs for this report
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this report.");
  }

  #get the shape title details from the database
  $query = "SELECT design FROM visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($shapeDesign) = $dbOutput->fetchrow_array;


  ########################################################################
  #
  # This code block is called on submit to save the changes
  #

  if (length($t) > 0)
  {
    #$shapeDesign = reports_set_style($shapeDesign, "title", "\"$t\"");
    $shapeDesign = reports_set_style($shapeDesign, "title", "$t");
    $shapeDesign = reports_set_style($shapeDesign, "fontColor", $fontColor);
    $shapeDesign = reports_set_style($shapeDesign, "titleFontSize", $titleFontSize);
    $shapeDesign = reports_set_style($shapeDesign, "titleBgColor", $bgColor);
    $shapeDesign = reports_set_style($shapeDesign, "titleTextAlign", $titleTextAlign);

    if ($titleTextFont eq "Helvetica")
    {
      $shapeDesign = reports_remove_style($shapeDesign, "titleTextFont");
    }
    else
    {
      $shapeDesign = reports_set_style($shapeDesign, "titleTextFont", $titleTextFont);
    }

    $q_shapeDesign = $db->quote($shapeDesign);

    $query = "UPDATE visuals SET design = $q_shapeDesign WHERE ID=$visID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    $cubeName = cube_id_to_name($db, $rptID);
    $dsID = cube_get_ds_id($db, $rptID);
    $dsName = ds_id_to_name($db, $dsID);

    utils_audit($db, $userID, "Changed rectangle title to $t", $dsID, $rptID, 0);

    $activity = "$first $last changed rectangle title for $cubeName in $dsName";
    utils_slack($activity);

    exit;
  }


  #########################################################################
  #
  # Everything after this point is called to display the shape title dialog
  #

  #extract table title and styling from design string
  if ($shapeDesign =~ m/,title:"(.*?)",/)
  {
    $shapeTitle = $1;
  }
  $shapeTitle = reports_get_style($shapeDesign, "title");
  $fontColor = reports_get_style($shapeDesign, "fontColor");
  $titleFontSize =  reports_get_style($shapeDesign, "titleFontSize");
  $bgColor = reports_get_style($shapeDesign, "titleBgColor");
  $titleTextAlign = reports_get_style($shapeDesign, "titleTextAlign");
  $titleTextFont = reports_get_style($shapeDesign, "titleTextFont");

  #set sensible defaults
  if (length($fontColor) < 2)
  {
    $fontColor = "#000000";
  }
  if ($titleFontSize < 3)
  {
    $titleFontSize = 18;
  }
  if (length($bgColor) < 2)
  {
    $bgColor = "#ffffff";
  }
  if (length($titleTextAlign) < 2)
  {
    $titleTextAlign = "left";
  }
  if (length($titleTextFont) < 3)
  {
    $titleTextFont = "Helvetica";
  }

  print <<END_HTML;
<SCRIPT>
titleTextAlign = "$titleTextAlign";

function submitForm()
{
  let title = document.getElementById('shapeTitle').value;
  let fontColor = document.getElementById('fontColor').value;
  let titleFontSize = document.getElementById('titleFontSize').value;
  let bgColor = document.getElementById('bgColor').value;
  let titleTextFont = document.getElementById('titleTextFont').value;

  fontColor = fontColor.substr(1);
  bgColor = bgColor.substr(1);

  let url = "xhrShapeTitle?rptID=$rptID&v=$visID&t=" + title +
      "&fontColor=" + fontColor + "&titleFontSize=" + titleFontSize +
      "&bgColor=" + bgColor + "&titleTextAlign=" + titleTextAlign +
      "&titleTextFont=" + titleTextFont;
  url = encodeURI(url);

  \$.get(url, function(data, status)
  {
    location.href = "display.cld?rpt=$rptID&v=$visID";
  });
}


function alignUI(alignment)
{
  let titleAlignLeft = document.getElementById('titleAlignLeft');
  let titleAlignCenter = document.getElementById('titleAlignCenter');
  let titleAlignRight = document.getElementById('titleAlignRight');

  if (alignment == "left")
  {
    titleAlignLeft.style.color = "blue";
    titleAlignCenter.style.color = "darkgray";
    titleAlignRight.style.color = "darkgray";
  }
  else if (alignment == "center")
  {
    titleAlignLeft.style.color = "darkgray";
    titleAlignCenter.style.color = "blue";
    titleAlignRight.style.color = "darkgray";
  }
  else if (alignment == "right")
  {
    titleAlignLeft.style.color = "darkgray";
    titleAlignCenter.style.color = "darkgray";
    titleAlignRight.style.color = "blue";
  }

  titleTextAlign = alignment;
}



function revertDefaults()
{
  document.getElementById('fontColor').value = "#333333";
  document.getElementById('bgColor').value = "#ffffff";
  alignUI('left');
  document.getElementById('titleFontSize').value = 18;
  document.getElementById('titleTextFont').value = "Helvetica";
}
</SCRIPT>

<DIV CLASS="modal-dialog" STYLE="overflow-y: initial !important;">
  <DIV CLASS="modal-content">
    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Shape Title</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">

      <FORM>
      <TABLE STYLE="width:95%;">
        <TR>
          <TD STYLE="text-align:right;">Title Text:&nbsp;</TD>
          <TD STYLE="text-align:left;">
            <INPUT CLASS="form-control" TYPE="text" ID="shapeTitle" MAXLENGTH="128" VALUE="$shapeTitle">
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">Font Color:&nbsp;</TD>
          <TD STYLE="text-align:left;">
            <INPUT CLASS="form-control" STYLE="width:3em;" TYPE="color" ID="fontColor" VALUE="$fontColor">
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">Background Color:&nbsp;</TD>
          <TD STYLE="text-align:left;">
            <INPUT CLASS="form-control" STYLE="width:3em;" TYPE="color" ID="bgColor" VALUE="$bgColor">
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">Alignment:&nbsp;</TD>
          <TD STYLE="text-align:left;">
            <SPAN ID="titleAlignLeft" CLASS="bi bi-text-left" STYLE="font-size:24px; color:darkgray;" onClick="alignUI('left');"></SPAN>
            &nbsp;
            <SPAN ID="titleAlignCenter" CLASS="bi bi-text-center" STYLE="font-size:24px; color:darkgray;" onClick="alignUI('center');"></SPAN>
            &nbsp;
            <SPAN ID="titleAlignRight" CLASS="bi bi-text-right" STYLE="font-size:24px; color:darkgray;" onClick="alignUI('right');"></SPAN>
            <SCRIPT>
              alignUI("$titleTextAlign");
            </SCRIPT>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Text Size:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="number" NAME="titleFontSize" ID="titleFontSize" STYLE="width:75px;" VALUE="$titleFontSize" min=3>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Font family:&nbsp;
          </TD>
          <TD>
            <SELECT CLASS="form-select" NAME="titleTextFont" ID="titleTextFont">
              <OPTION VALUE="Arial">Arial</OPTION>
              <OPTION VALUE="Arial Black">Arial Black</OPTION>
              <OPTION VALUE="Comic Sans MS">Comic Sans MS</OPTION>
              <OPTION VALUE="Courier New">Courier New</OPTION>
              <OPTION VALUE="Helvetica">Helvetica</OPTION>
              <OPTION VALUE="Impact">Impact</OPTION>
              <OPTION VALUE="Tahoma">Tahoma</OPTION>
              <OPTION VALUE="Times New Roman">Times New Roman</OPTION>
              <OPTION VALUE="Verdana">Verdana</OPTION>
            </SELECT>
            <SCRIPT>
              \$("select#titleTextFont").val("$titleTextFont");
            </SCRIPT>
          </TD>
        </TR>

        <TR>
          <TD COLSPAN="2" STYLE="text-align:center;">
            <P></P>
            <A HREF="#" CLASS="text-decoration-none" onClick="revertDefaults()">Revert to default</A>
          </TD>
        </TR>
      </TABLE>

      </FORM>

    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="submitForm()"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML


#EOF
