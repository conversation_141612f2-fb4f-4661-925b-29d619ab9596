#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Restore Deleted Data Source</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Please Wait...');
  return(true);
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item active">Restore Deleted Data Sources</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser and get the forecast to be deleted
  $q = new CGI;
  $dsID = $q->param('ds');

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #connect to the database
  $db = KAPutil_connect_to_database();

  print_html_header();

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-11 col-lg-8 col-xl-7"> <!-- content -->

      <FORM METHOD="post" ACTION="/app/dsr/restoreDSDo.cld" onsubmit="return checkForm(this);">

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Restore Deleted Data Sources</DIV>
          <DIV CLASS="card-body">

END_HTML

  #admins can restore any data source
  if ($acctType > 4)
  {
    $query = "SELECT ID, name, description FROM dataSources WHERE deleted > 0";
  }

  #else everybody else can only restore their own deleted data sources
  else
  {
    $query = "SELECT ID, name, description FROM dataSources \
        WHERE userID = $userID AND deleted > 0";
  }

  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute();

  #if the user doesn't have any deleted data sources
  $okDisabled = "";
  if ($status < 1)
  {
    print("You have no data sources that have been deleted in the past two days.<P>\n");
    $okDisabled = "DISABLED=1";
  }

  #else print out a list of the deleted data sources that can be restored
  else
  {
    print <<END_HTML;
            <DIV CLASS="table-responsive">
              <TABLE CLASS="table table-sm table-striped">
                <THEAD><TR>
                  <TH>&nbsp</TH>
                  <TH>Name</TH>
                  <TH>Description</TH>
                </TR></THEAD>
END_HTML

    while (($dsID, $dsName, $dsDescrip) = $dbOutput->fetchrow_array)
    {
      print <<END_HTML;
                <TR>
                  <TD>
                    <DIV CLASS="form-check">
                      <INPUT CLASS="form-check-input" NAME="R $dsID" ID="R_$dsID" TYPE="checkbox">
                      <LABEL CLASS="form-check-label" FOR="R_$dsID">&nbsp;</LABEL>
                    </DIV>
                  </TD>
                  <TD>$dsName</TD>
                  <TD>$dsDescrip</TD>
                </TR>
END_HTML
    }

    print("</TABLE> </DIV>\n");
  }

  print <<END_HTML;
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onclick="location.href='main.cld'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-submit" $okDisabled><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
          </DIV>

        </DIV>
      </DIV>

      </FORM>

      <P>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
