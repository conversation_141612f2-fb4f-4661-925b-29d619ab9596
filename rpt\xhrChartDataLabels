#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::Reports;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $rptID = $q->param('rptID');
  $visID = $q->param('v');
  $showLabels = $q->param('showLabels');
  $labelFontColor = $q->param('labelFontColor');
  $labelDisplay = $q->param('labelDisplay');
  $labelStep = $q->param('labelStep');
  $labelFontSize = $q->param('labelFontSize');
  $labelFont = $q->param('labelFont');

  #fix up the CGI parameters from the submitted form
  if (defined($showLabels))
  {
    $showLabels = ($showLabels eq "false") ? "0" : "1";
  }

  $labelFontColor = "#" . $labelFontColor;

  $db = KAPutil_connect_to_database();

  #make sure we have edit privs for this report
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this report.");
  }

  #get the chart title details from the database
  $query = "SELECT design FROM visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($graphDesign) = $dbOutput->fetchrow_array;


  ########################################################################
  #
  # This code block is called on submit to save the changes
  #

  if (defined($showLabels))
  {
    if ($showLabels ne reports_chart_design_default("showLabels"))
    {
      $graphDesign = reports_set_style($graphDesign, "showLabels", $showLabels);
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "showLabels");
    }

    if ($labelFontColor ne reports_chart_design_default("labelFontColor"))
    {
      $graphDesign = reports_set_style($graphDesign, "labelFontColor", $labelFontColor);
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "labelFontColor");
    }

    if ($labelDisplay ne reports_chart_design_default("labelDisplay"))
    {
      $graphDesign = reports_set_style($graphDesign, "labelDisplay", $labelDisplay);
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "labelDisplay");
    }

    if ($labelStep ne reports_chart_design_default("labelStep"))
    {
      $graphDesign = reports_set_style($graphDesign, "labelStep", $labelStep);
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "labelStep");
    }

    if ($labelFontSize ne reports_chart_design_default("labelFontSize"))
    {
      $graphDesign = reports_set_style($graphDesign, "labelFontSize", $labelFontSize);
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "labelFontSize");
    }

    if ($labelFont eq "Helvetica")
    {
      $graphDesign = reports_remove_style($graphDesign, "labelFont");
    }
    else
    {
      $graphDesign = reports_set_style($graphDesign, "labelFont", $labelFont);
    }

    $q_graphDesign = $db->quote($graphDesign);
    $query = "UPDATE visuals SET design = $q_graphDesign WHERE ID=$visID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    $cubeName = cube_id_to_name($db, $rptID);
    $dsID = cube_get_ds_id($db, $rptID);
    $dsName = ds_id_to_name($db, $dsID);

    utils_audit($db, $userID, "Changed chart data labels", $dsID, $rptID, 0);
    $activity = "$first $last changed chart data labels for $cubeName in $dsName";
    utils_slack($activity);

    exit;
  }


  #########################################################################
  #
  # Everything after this point is called to display the chart layout dialog
  #

  #extract graph captions from design string
  $showLabels = reports_get_style($graphDesign, "showLabels");
  $labelFontColor = reports_get_style($graphDesign, "labelFontColor");
  $labelDisplay = reports_get_style($graphDesign, "labelDisplay");
  $labelStep = reports_get_style($graphDesign, "labelStep");
  $labelFontSize = reports_get_style($graphDesign, "labelFontSize");
  $labelFont = reports_get_style($graphDesign, "labelFont");

  #set appropriate defaults
  if (length($showLabels) < 1)
  {
    $showLabels = "CHECKED";
  }
  if (length($labelFontColor) < 7)
  {
    $labelFontColor = "#333333";
  }
  if (!(defined($labelDisplay)))
  {
    $labelDisplay = "auto";
  }
  if (!(defined($labelStep)))
  {
    $labelStep = "1";
  }
  if ($labelFontSize < 3)
  {
    $labelFontSize = "10";
  }
  if (length($labelFont) < 3)
  {
    $labelFont = "Helvetica";
  }

  #set up things for HTML form display
  if ($showLabels eq "1")
  {
    $showLabels = "CHECKED";
  }

  print <<END_HTML;
<SCRIPT>
function submitForm()
{
  let showLabels = \$("#showLabels").prop("checked");
  let labelFontColor = document.getElementById('labelFontColor').value;
  let labelDisplay = document.getElementById('labelDisplay').value;
  let labelStep = document.getElementById('labelStep').value;
  let labelFontSize = document.getElementById('labelFontSize').value;
  let labelFont = document.getElementById('labelFont').value;

  //knock # off of color strings
  labelFontColor = labelFontColor.substr(1);

  let url = "xhrChartDataLabels?rptID=$rptID&v=$visID&showLabels=" + showLabels +
      "&labelFontColor=" + labelFontColor + "&labelDisplay=" + labelDisplay +
      "&labelStep=" + labelStep + "&labelFontSize=" + labelFontSize +
      "&labelFont=" + labelFont;

  \$.get(url, function(data, status)
  {
    location.href = "display.cld?rpt=$rptID&v=$visID";
  });
}



function revertDefaults()
{
  document.getElementById('showLabels').checked = true;
  document.getElementById('labelFontColor').value = "#333333";
  document.getElementById('labelDisplay').value = "auto";
  document.getElementById('labelStep').value = 1;
  document.getElementById('labelFontSize').value = 10;
  document.getElementById('labelFont').value = "Helvetica";
}
</SCRIPT>

<DIV CLASS="modal-dialog" STYLE="overflow-y: initial !important;">
  <DIV CLASS="modal-content">
    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Data Labels</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">
      <FORM>
      <TABLE>
        <TR>
          <TD STYLE="text-align:right;">
            Display data labels&nbsp;
          </TD>
          <TD>
            <DIV CLASS="form-check form-switch">
              <INPUT TYPE="checkbox" CLASS="form-check-input" NAME="showLabels" ID="showLabels" data-offstyle="secondary" $showLabels>
              <LABEL CLASS="form-check-label" FOR="showLabels">&nbsp;</LABEL>
            </DIV>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Color:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="color" NAME="labelFontColor" ID="labelFontColor" VALUE="$labelFontColor" STYLE="width:50px;"></DIV>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Positioning:&nbsp;
          </TD>
          <TD>
            <SELECT CLASS="form-select" NAME="labelDisplay" ID="labelDisplay">
              <OPTION VALUE="auto">Automatic</OPTION>
              <OPTION VALUE="wrap">Wrap long labels onto multiple lines</OPTION>
              <OPTION VALUE="stagger">Stagger labels across multiple lines</OPTION>
              <OPTION VALUE="rotate">Rotate the data labels</OPTION>
            </SELECT>
            <SCRIPT>
              \$("select#labelDisplay").val("$labelDisplay");
            </SCRIPT>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Label stepping:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="number" NAME="labelStep" ID="labelStep" STYLE="width:50px;" VALUE="$labelStep">
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Text size:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="number" NAME="labelFontSize" ID="labelFontSize" STYLE="width:75px;" VALUE="$labelFontSize" min=3>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Font family:&nbsp;
          </TD>
          <TD>
            <SELECT CLASS="form-select" NAME="labelFont" ID="labelFont">
              <OPTION VALUE="Arial">Arial</OPTION>
              <OPTION VALUE="Arial Black">Arial Black</OPTION>
              <OPTION VALUE="Comic Sans MS">Comic Sans MS</OPTION>
              <OPTION VALUE="Courier New">Courier New</OPTION>
              <OPTION VALUE="Helvetica">Helvetica</OPTION>
              <OPTION VALUE="Impact">Impact</OPTION>
              <OPTION VALUE="Tahoma">Tahoma</OPTION>
              <OPTION VALUE="Times New Roman">Times New Roman</OPTION>
              <OPTION VALUE="Verdana">Verdana</OPTION>
            </SELECT>
            <SCRIPT>
              \$("select#labelFont").val("$labelFont");
            </SCRIPT>
          </TD>
        </TR>

        <TR>
          <TD COLSPAN="2" STYLE="text-align:center;">
            <P></P>
            <A CLASS="text-decoration-none" HREF="#" onClick="revertDefaults()">Revert to default</A>
          </TD>
        </TR>
      </TABLE>
      </FORM>

    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="submitForm()"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML

#EOF
