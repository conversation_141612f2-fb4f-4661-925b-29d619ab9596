#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::Users;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName User Management</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jsgrid/jsgrid.min.js"></SCRIPT>
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<LINK HREF="/jsgrid/jsgrid.min.css" REL="stylesheet">
<LINK HREF="/jsgrid/jsgrid-theme.min.css" REL="stylesheet">

<STYLE>
div.grid th
{
  background: #e2e3e5 !important;
}

.selected-row > td
{
  background: #c4e2ff !important;
  border-color: #c4e2ff;
}
</STYLE>
END_HTML

  $whereClause = "";
  if ($acctType < 4)
  {
    $whereClause = "WHERE acctType=0";
  }

  $jsonData = "[";
  $query = "SELECT ID, email, first, last, orgID, acctType, lastLogin \
      FROM users $whereClause ORDER BY orgID, last";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  while (($id, $userEmail, $userFirst, $userLast, $orgID, $userAcctType, $lastLogin) = $dbOutput->fetchrow_array)
  {
    $userOrgName = Users_orgID_to_name($db, $orgID);
    $acctStr = utils_acctType_to_text($userAcctType);

    $jsonData .= "{id: '$id', email: '$userEmail', first: '$userFirst', last: '$userLast', org: '$userOrgName', type: '$acctStr', lastlog: '$lastLogin'},\n";
  }

  chop($jsonData); chop($jsonData);

  $jsonData .= "]\n";

  print <<END_HTML;
<SCRIPT>
const gridData = $jsonData;
const gridHeight = window.innerHeight - 240;
let selectedUser = 0;

\$(document).ready(function()
{

  \$('#userGrid').jsGrid(
  {
    width: '95%',
    height: gridHeight,
    sorting: true,
    autoload: true,
    loadIndication: true,

    data: gridData,

    rowClick: function(args)
    {
      selectedUser = args.item.id;

      \$('#userGrid tr').removeClass('selected-row');

      \$selectedRow = \$(args.event.target).closest('tr');
      \$selectedRow.addClass('selected-row');
    },

    fields: [
      {name: 'id', type: 'number', visible: false},
      {name: 'email', title: 'Email', type: 'text', width: 150},
      {name: 'first', title: 'First', type: 'text', width: 75},
      {name: 'last', title: 'Last', type: 'text', width: 75},
      {name: 'org', title: 'Organization', type: 'text', width: 150},
      {name: 'type', title: 'Type', type: 'text', width: 100},
      {name: 'lastlog', title: 'Last Login', type: 'text', width: 125},
    ]

  });
});


function modify_user()
{
  if (selectedUser < 1)
  {
    return;
  }
  location.href='/app/admin/userInfo.cld?u=' + selectedUser;
}

function delete_user()
{
  if (selectedUser < 1)
  {
    return;
  }
  location.href='/app/admin/userDeleteConfirm.cld?u=' + selectedUser;
}

</SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-0">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item">
END_HTML

   #if we came in via user settings rather than administration
   if ($acctType < 4)
   {
     $returnLink = "/app/admin/userSettings.cld";
     print("<A CLASS='text-decoration-none' HREF='/app/admin/userSettings.cld'>User Settings</A>");
   }
   else
   {
     $returnLink = "/app/admin/home.cld";
     print("<A CLASS='text-decoration-none' HREF='/app/admin/home.cld'>Administration</A>");
   }

   print <<END_HTML;
    </LI>
    <LI CLASS="breadcrumb-item active">User Management</LI>
  </OL>
</NAV>

END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if ((length($email) < 1) || ($acctType < 1))
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  $db = KAPutil_connect_to_database();

  print_html_header();

  print <<END_HTML;
<NAV CLASS="navbar navbar-expand-md navbar-light bg-light border mb-4">
  <BUTTON CLASS="navbar-toggler" TYPE="button" DATA-TOGGLE="collapse" DATA-TARGET="#navbarNavDropdown">
    <SPAN CLASS="navbar-toggler-icon"></SPAN>
  </BUTTON>
  <DIV CLASS="collapse navbar-collapse justify-content-center" ID="navbarNavDropdown">
    <UL CLASS="navbar-nav">
      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="location.href='userInfo.cld'"><I CLASS="bi bi-person-plus"></I> New User</A></LI>
      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="modify_user()"><I CLASS="bi bi-pencil"></I> Modify</A></LI>
END_HTML

  #if we're an admin (as opposed to an analyst)
  if ($acctType > 1)
  {
    print <<END_HTML;
      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="delete_user()"><I CLASS="bi bi-person-x"></I> Remove User</A></LI>
END_HTML
  }

  print <<END_HTML;
    </UL>
  </DIV>
</NAV>

<P>
<DIV ID="userGrid" CLASS="grid mx-auto" STYLE="font-size:13px; width:98%;"></DIV>

END_HTML

print_html_footer();

#EOF
