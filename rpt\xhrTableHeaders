#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::Reports;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $rptID = $q->param('rptID');
  $visID = $q->param('v');
  $headerFontColor = $q->param('headerFontColor');
  $headerBgColor = $q->param('headerBgColor');
  $headerWrap = $q->param('headerWrap');
  $headerSticky = $q->param('headerSticky');
  $headerFontSize = $q->param('headerFontSize');
  $headerFont = $q->param('headerFont');
  $headerOutline = $q->param('headerOutline');

  #fix up the CGI parameters from the submitted form
  if (defined($headerWrap))
  {
    $headerWrap = ($headerWrap eq "false") ? "0" : "1";
  }

  if (defined($headerSticky))
  {
    $headerSticky = ($headerSticky eq "false") ? "0" : "1";
  }

  $headerFontColor = "#" . $headerFontColor;
  $headerBgColor = "#" . $headerBgColor;

  $db = KAPutil_connect_to_database();

  #make sure we have edit privs for this report
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this report.");
  }

  #get the chart title details from the database
  $query = "SELECT design FROM visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($design) = $dbOutput->fetchrow_array;


  ########################################################################
  #
  # This code block is called on submit to save the changes
  #

  if (defined($headerFont))
  {
    $design = reports_remove_style($design, "headerbg");
    $design = reports_remove_style($design, "headercolor");
    $design = reports_set_style($design, "headerFontColor", $headerFontColor);
    $design = reports_set_style($design, "headerBgColor", $headerBgColor);
    $design = reports_set_style($design, "headerWrap", $headerWrap);
    $design = reports_set_style($design, "headerSticky", $headerSticky);
    $design = reports_set_style($design, "headerFontSize", $headerFontSize);

    if ($headerFont eq "Helvetica")
    {
      $design = reports_remove_style($design, "headerFont");
    }
    else
    {
      $design = reports_set_style($design, "headerFont", $headerFont);
    }

    $design = reports_set_style($design, "headerOutline", $headerOutline);

    $q_design = $db->quote($design);
    $query = "UPDATE visuals SET design = $q_design WHERE ID=$visID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    $cubeName = cube_id_to_name($db, $rptID);
    $dsID = cube_get_ds_id($db, $rptID);
    $dsName = ds_id_to_name($db, $dsID);

    utils_audit($db, $userID, "Changed table header formatting", $dsID, $rptID, 0);
    $activity = "$first $last changed table header formatting for $cubeName in $dsName";
    utils_slack($activity);

    exit;
  }


  #########################################################################

  #extract settings from design string

  #handle legacy headercolor setting
  $headercolor = reports_get_style($design, "headercolor");
  if ($headercolor eq "white")
  {
    $headerFontColor = "#ffffff";
  }
  elsif ($headercolor eq "black")
  {
    $headerFontColor = "#000000";
  }
  elsif ($headercolor =~ m/^#/)
  {
    $headerFontColor = $headercolor;
  }
  else
  {
    $headerFontColor = reports_get_style($design, "headerFontColor");
  }

  #handle legacy headerbg setting
  $headerbg = reports_get_style($design, "headerbg");
  if ($headerbg eq "white")
  {
    $headerBgColor = "#ffffff";
  }
  elsif ($headerbg =~ m/^#/)
  {
    $headerBgColor = $headerbg;
  }
  else
  {
    $headerBgColor = reports_get_style($design, "headerBgColor");
  }

  $headerFontSize = reports_get_style($design, "headerFontSize");
  $headerFont = reports_get_style($design, "headerFont");
  $headerOutline = reports_get_style($design, "headerOutline");
  $headerWrap = reports_get_style($design, "headerWrap");
  $headerSticky = reports_get_style($design, "headerSticky");

  #set appropriate defaults
  if (length($headerFontColor) < 7)
  {
    $headerFontColor = "#ffffff";
  }
  if (length($headerBgColor) < 7)
  {
    $headerBgColor = "#333333";
  }
  if (length($headerWrap) < 1)
  {
    $headerWrap = 1;
  }
  if (length($headerSticky) < 1)
  {
    $headerSticky = 1;
  }
  if ($headerFontSize < 3)
  {
    $headerFontSize = "11";
  }
  if (length($headerFont) < 3)
  {
    $headerFont = "Helvetica";
  }
  if (length($headerOutline) < 1)
  {
    $headerOutline = "bottom";
  }

  #set up things for HTML form display
  if ($headerWrap eq "1")
  {
    $headerWrap = "CHECKED";
  }
  if ($headerSticky eq "1")
  {
    $headerSticky = "CHECKED";
  }


  #########################################################
  #
  # Everything after this point is called to display the table layout dialog
  #

  print <<END_HTML;
<SCRIPT>
function submitForm()
{
  let headerFontColor = document.getElementById('headerFontColor').value;
  let headerBgColor = document.getElementById('headerBgColor').value;
  let headerWrap = \$("#headerWrap").prop("checked");
  let headerSticky = \$("#headerSticky").prop("checked");
  let headerFontSize = document.getElementById('headerFontSize').value;
  let headerFont = document.getElementById('headerFont').value;
  let headerOutline = document.getElementById('headerOutline').value;

  //knock # off of color strings
  headerFontColor = headerFontColor.substr(1);
  headerBgColor = headerBgColor.substr(1);

  let url = "xhrTableHeaders?rptID=$rptID&v=$visID&headerFontSize=" + headerFontSize +
      "&headerFont=" + headerFont + "&headerWrap=" + headerWrap +
      "&headerSticky=" + headerSticky + "&headerFontColor=" + headerFontColor +
      "&headerBgColor=" + headerBgColor + "&headerOutline=" + headerOutline;

  \$.get(url, function(data, status)
  {
    location.href = "display.cld?rpt=$rptID&v=$visID";
  });
}



function revertDefaults()
{
  document.getElementById('headerFontColor').value = "#ffffff";
  document.getElementById('headerBgColor').value = "#333333";
  document.getElementById('headerWrap').checked = true;
  document.getElementById('headerSticky').checked = true;
  document.getElementById('headerFontSize').value = 11;
  document.getElementById('headerFont').value = "Helvetica";
  document.getElementById('headerOutline').value = "bottom";
}
</SCRIPT>

<DIV CLASS="modal-dialog" STYLE="overflow-y: initial !important;">
  <DIV CLASS="modal-content">
    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Column Headers</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">

      <FORM>
      <TABLE CLASS="mx-auto">
        <TR>
          <TD STYLE="text-align:right;">
            Font color:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="color" NAME="headerFontColor" ID="headerFontColor" STYLE="width:3em;"VALUE="$headerFontColor">
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Background color:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="color" NAME="headerBgColor" ID="headerBgColor" STYLE="width:3em;"VALUE="$headerBgColor">
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Word wrap headers:&nbsp;
          </TD>
          <TD>
            <DIV CLASS="form-check form-switch">
              <INPUT TYPE="checkbox" CLASS="form-check-input" NAME="headerWrap" ID="headerWrap" data-offstyle="secondary" $headerWrap>
              <LABEL CLASS="form-check-label" FOR="headerWrap">&nbsp;</LABEL>
            </DIV>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Sticky headers:&nbsp;
          </TD>
          <TD>
            <DIV CLASS="form-check form-switch">
              <INPUT TYPE="checkbox" CLASS="form-check-input" NAME="headerSticky" ID="headerSticky" data-offstyle="secondary" $headerSticky>
              <LABEL CLASS="form-check-label" FOR="headerSticky">&nbsp;</LABEL>
            </DIV>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Text size:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="number" NAME="headerFontSize" ID="headerFontSize" STYLE="width:5em;" VALUE="$headerFontSize" min=3>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Font family:&nbsp;
          </TD>
          <TD>
            <SELECT CLASS="form-select" NAME="headerFont" ID="headerFont">
              <OPTION VALUE="Arial">Arial</OPTION>
              <OPTION VALUE="Arial Black">Arial Black</OPTION>
              <OPTION VALUE="Comic Sans MS">Comic Sans MS</OPTION>
              <OPTION VALUE="Courier New">Courier New</OPTION>
              <OPTION VALUE="Helvetica">Helvetica</OPTION>
              <OPTION VALUE="Impact">Impact</OPTION>
              <OPTION VALUE="Tahoma">Tahoma</OPTION>
              <OPTION VALUE="Times New Roman">Times New Roman</OPTION>
              <OPTION VALUE="Verdana">Verdana</OPTION>
            </SELECT>
            <SCRIPT>
              \$("select#headerFont").val("$headerFont");
            </SCRIPT>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Outline:&nbsp;
          </TD>
          <TD>
            <SELECT CLASS="form-select" NAME="headerOutline" ID="headerOutline">
              <OPTION VALUE="none">None</OPTION>
              <OPTION VALUE="bottom">Bottom only</OPTION>
              <OPTION VALUE="top">Top only</OPTION>
              <OPTION VALUE="left">Left only</OPTION>
              <OPTION VALUE="right">Right only</OPTION>
              <OPTION VALUE="topbottom">Top + bottom</OPTION>
              <OPTION VALUE="leftright">Left + right</OPTION>
              <OPTION VALUE="frame">Frame</OPTION>
            </SELECT>
            <SCRIPT>
              \$("select#headerOutline").val("$headerOutline");
            </SCRIPT>
          </TD>
        </TR>

        <TR>
          <TD COLSPAN="2" STYLE="text-align:center;">
            <P></P>
            <A HREF="#" CLASS="text-decoration-none" onClick="revertDefaults()">Revert to default</A>
          </TD>
        </TR>
      </TABLE>

      </FORM>

    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="submitForm()"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML


#EOF
