#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::Reports;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $rptID = $q->param('rptID');
  $visID = $q->param('v');
  $showBorder = $q->param('showBorder');
  $borderColor = $q->param('borderColor');
  $borderThickness = $q->param('borderThickness');

  #fix up the CGI parameters from the submitted form
  if (defined($showBorder))
  {
    $showBorder = ($showBorder eq "false") ? "0" : "1";
  }
  $borderColor = "#" . $borderColor;

  $db = KAPutil_connect_to_database();

  #make sure we have edit privs for this report
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this report.");
  }

  #get the table border details from the database
  $query = "SELECT design FROM visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($design) = $dbOutput->fetchrow_array;


  ########################################################################
  #
  # This code block is called on submit to save the changes
  #

  if (defined($showBorder))
  {
    $design = reports_set_style($design, "showBorder", $showBorder);
    $design = reports_set_style($design, "borderColor", $borderColor);
    $design = reports_set_style($design, "borderThickness", $borderThickness);

    $q_design = $db->quote($design);
    $query = "UPDATE visuals SET design = $q_design WHERE ID=$visID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    $cubeName = cube_id_to_name($db, $rptID);
    $dsID = cube_get_ds_id($db, $rptID);
    $dsName = ds_id_to_name($db, $dsID);

    utils_audit($db, $userID, "Changed table border", $dsID, $rptID, 0);
    $activity = "$first $last changed table border for $cubeName in $dsName";
    utils_slack($activity);

    exit;
  }


  #########################################################################
  #
  # Everything after this point is called to display the chart layout dialog
  #

  #extract table border styling from design string
  $showBorder = reports_get_style($design, "showBorder");
  $borderColor = reports_get_style($design, "borderColor");
  $borderThickness = reports_get_style($design, "borderThickness");

  #extract table border styling from design string
  if (!(defined($borderColor)))
  {
    $borderColor = "#333333";
  }
  if (!(defined($borderThickness)))
  {
    $borderThickness = "1";
  }
  $showBorder = ($showBorder eq "1") ? "CHECKED" : "";

  print <<END_HTML;
<SCRIPT>
function submitForm()
{
  let showBorder = \$("#showBorder").prop("checked");
  let borderColor = document.getElementById('borderColor').value;
  let borderThickness = document.getElementById('borderThickness').value;

  //knock # off of color strings
  borderColor = borderColor.substr(1);

  let url = "xhrTableBorder?rptID=$rptID&v=$visID&showBorder=" + showBorder +
      "&borderColor=" + borderColor + "&borderThickness=" + borderThickness;

  \$.get(url, function(data, status)
  {
    location.href = "display.cld?rpt=$rptID&v=$visID";
  });
}
</SCRIPT>

<DIV CLASS="modal-dialog" STYLE="overflow-y: initial !important;">
  <DIV CLASS="modal-content">
    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Table Border</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">

      <FORM>
      <TABLE>
        <TR>
          <TD STYLE="text-align:right;">
            Show a border around the table:&nbsp;
          </TD>
          <TD>
            <DIV CLASS="form-check form-switch">
              <INPUT TYPE="checkbox" CLASS="form-check-input" NAME="showBorder" ID="showBorder" $showBorder>
              <LABEL CLASS="form-check-label" FOR="showBorder">&nbsp;</LABEL>
            </DIV>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Border color:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="color" NAME="borderColor" ID="borderColor" STYLE="width:3em;" VALUE="$borderColor"></DIV>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Border thickness:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="number" NAME="borderThickness" ID="borderThickness" STYLE="width:4em;" VALUE="$borderThickness" min=0>
          </TD>
        </TR>
      </TABLE>
      </FORM>

    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="submitForm()"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML


#EOF
