#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::AInsights::AInsights;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: AInsights Model History</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function show_bs_modal(resource)
{
  \$('#modal-telemetry .modal-content').html('');
  \$('#modal-telemetry').modal('show');
  \$('#modal-telemetry .modal-content').load(resource);
}
</SCRIPT>

</HEAD>

<BODY>

END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">AInsights</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="insights.cld?pm=$priceModelID">$pricingName</A></LI>
    <LI CLASS="breadcrumb-item active">History</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  $q = new CGI;
  $priceModelID = $q->param('pm');

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #connect to the database
  $db = KAPutil_connect_to_database();

  #get the name of the pricing model
  $pricingName = AInsights_ID_to_name($db, $priceModelID);

  print_html_header();

  #make sure we have at least read privs for this pricing model
  $privs = AInsights_rights($db, $userID, $priceModelID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to view this model's history.");
  }

  #get basic model
  $query = "SELECT lastRun FROM analytics.pricing WHERE ID=$priceModelID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($lastRun) = $dbOutput->fetchrow_array;

  if (length($lastRun) < 1)
  {
    $lastRun = "(Never)";
  }

  %userNameHash = utils_get_user_hash($db);
  $userNameHash{0} = "Koala";

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-12 col-lg-11 col-xl-9"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">AInsights Model History</DIV>
        <DIV CLASS="card-body">

          <TABLE CLASS="table table-striped table-bordered table-sm w-50">
            <TR>
              <TD CLASS="text-end">
                <B>Last Model Refresh:</B>
              </TD>
              <TD CLASS="text-start">
                $lastRun
              </TD>
            </TR>
          </TABLE>

          <P>
          <DIV CLASS = "table-responsive" STYLE="height:400px; overflow:auto;">
            <TABLE CLASS="table table-striped table-sm">
END_HTML

  #grab all of the activity info related to this data flow
  $query = "SELECT timestamp, userID, action FROM analytics.AInsights_audit \
      WHERE elasticID=$priceModelID ORDER BY timestamp DESC";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  #output HTML row for every line in activity audit log
  while (($timestamp, $userID, $action) = $dbOutput->fetchrow_array)
  {
    if ($action =~ m/^(.*)\|(\d+)$/)
    {
      $action = "$1 <A HREF='#' CLASS='text-decoration-none' onClick=\"show_bs_modal('xhrTelemetry.cld?j=$2')\">View Telemetry</A>";
    }

    print <<END_HTML;
              <TR>
                <TD NOWRAP>$timestamp</TD>
                <TD NOWRAP>$userNameHash{$userID}</TD>
                <TD>$action</TD>
              </TR>
END_HTML
  }

  print <<END_HTML;
            </TABLE>
          </DIV>

          <P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" onClick="location.href='main.cld'"><I CLASS="bi bi-check-lg"></I> Done</BUTTON>
          </DIV>

        </DIV>
      </DIV>

      <DIV ID="modal-telemetry" CLASS="modal" ROLE="dialog">
        <DIV CLASS="modal-dialog modal-lg" STYLE="overflow-y: initial !important;">
          <DIV CLASS="modal-content">
          </DIV>
        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

  $activity = "AInsights: $first $last viewed history for $pricingName";
  utils_slack($activity);


#EOF
