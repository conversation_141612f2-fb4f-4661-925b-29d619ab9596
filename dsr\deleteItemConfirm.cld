#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  #output Content-type header
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  #output HTML
  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Delete Item</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Deleting...');
  return(true);
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">$readableCapAction $readableType</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');
  $dim = $q->param('dim');
  $itemID = $q->param('item');
  $type = $q->param('type');
  $merged = $q->param('merged');

  #sanitize parameters
  $dsID = utils_sanitize_integer($dsID);
  $dim = utils_sanitize_dim($dim);
  $itemID = utils_sanitize_string($itemID);
  $type = utils_sanitize_string($type);
  $merged = utils_sanitize_integer($merged);

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsSchema = "datasource_" . $dsID;

  $dsName = ds_id_to_name($db, $dsID);

  #by default, we want to tell the user we're "deleting" the item unless it's
  #merged
  if ($merged == 1)
  {
    $readableAction = "unmerge";
    $readableCapAction = "Unmerge";
  }
  else
  {
    $readableAction = "delete";
    $readableCapAction = "Delete";
  }

  $structDB = KAPutil_get_dim_stub_name($dim);
  $dimDB = KAPutil_get_dim_db_name($dim);
  $dimName = KAPutil_get_dim_name_singular($dim, 1);

  #make sure we have write privs for the data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    print_html_header();
    exit_error("You don't have privileges to modify the data source.");
  }

  #handle a product "segment delete", with option to delete items
  if (($type ne "base") && ($itemID =~ m/^SMT_(\d+)/))
  {

    #NB: we handle this type of selection a little differently below for UI
    #   purposes
    $type = "segment";
    $readableType = "Segments";

    @segmentIDs = split(',', $itemID);
    $idx = 0;
    foreach $segmentID (@segmentIDs)
    {
      $segmentID =~ m/^SMT_(\d+)/;
      $segmentIDs[$idx] = $1;
      $idx++;
    }

    foreach $segmentID (@segmentIDs)
    {
      $dbName = $structDB . "segment";
      $query = "SELECT name FROM $dsSchema.$dbName where ID=$segmentID";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      KAPutil_handle_db_err($db, $status, $query);
      ($segmentName) = $dbOutput->fetchrow_array;
      $name .= "$segmentName, ";
    }
    chop($name);  chop($name);
  }

  elsif ($itemID =~ m/^\d/)
  {
    $type = "base";
    $readableType = "Base Item";

    @itemIDs = split(',', $itemID);

    $str = join(',', @itemIDs);
    $query = "SELECT name FROM $dsSchema.$dimDB WHERE ID IN ($str)";
  }
  elsif ($itemID =~ m/^SMT_\d+\.\d+/)
  {
    $type = "base";
    $readableType = "Base Item";

    #turn the segment items into regular old base items
    @itemIDs = split(',', $itemID);
    $str = "";
    foreach $id (@itemIDs)
    {
      $id =~ m/^SMT_\d+\.(\d+)$/;
      $str .= "$1,";
    }
    chop($str);
    $itemID = $str;

    $query = "SELECT name FROM $dsSchema.$dimDB WHERE ID IN ($str)";
  }
  elsif ($itemID =~ m/^ATT_(\d+)/)
  {
    $type = "attr";
    $readableType = "Attribute";
    $itemID = $1;
    $dbName = $structDB . "attributes";
    $query = "SELECT name FROM $dsSchema.$dbName WHERE ID=$itemID";
  }
  elsif ($itemID =~ m/^LIS_(\d+)/)
  {
    $type = "list";
    $readableType = "List";
    $itemID = $1;
    $dbName = $structDB . "list";
    $query = "SELECT name FROM $dsSchema.$dbName WHERE ID=$itemID";
  }
  elsif ($itemID =~ m/^AGG_(\d+)/)
  {
    $type = "aggr";
    $readableType = "Aggregate";
    $itemID = $1;
    $dbName = $structDB . "aggregate";
    $query = "SELECT name FROM $dsSchema.$dbName WHERE ID=$itemID";
  }
  elsif ($itemID =~ m/^SEG_(\d+)/)
  {
    $type = "seg";
    $readableType = "Segmentation";
    $itemID = $1;
    $dbName = $structDB . "segmentation";
    $query = "SELECT name FROM $dsSchema.$dbName WHERE ID=$itemID";
  }
  elsif ($itemID =~ m/^SHS_(\d+)/)
  {
    $type = "segmenthier";
    $readableType = "Segmentation Hierarchy";
    $itemID = $1;
    $dbName = $structDB . "seghierarchy";
    $query = "SELECT name FROM $dsSchema.$dbName WHERE ID=$itemID";
  }
  else
  {
    print_html_header();
    exit_error("The ability to delete this type of item is not yet available.");
  }

  if ($type ne "segment")
  {
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);

    $name = "";
    while (($str, $alias) = $dbOutput->fetchrow_array)
    {
      $name .= $str . ", ";
    }
    chop($name); chop($name);
  }

  print_html_header();

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-10 col-lg-7 col-xl-6"> <!-- content -->

      <FORM METHOD="post" ACTION="/app/dsr/deleteItem.cld" onsubmit="return checkForm(this);">
      <INPUT TYPE="hidden" NAME="dsID" VALUE="$dsID">
      <INPUT TYPE="hidden" NAME="dim" VALUE="$dim">
      <INPUT TYPE="hidden" NAME="item" VALUE="$itemID">
      <INPUT TYPE="hidden" NAME="type" VALUE="$type">
      <INPUT TYPE="hidden" NAME="merged" VALUE="$merged">
      <INPUT TYPE="hidden" NAME="readableType" VALUE="$readableType">
      <INPUT TYPE="hidden" NAME="readableAction" VALUE="$readableAction">
      <INPUT TYPE="hidden" NAME="readableCapAction" VALUE="$readableCapAction">

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Delete $readableType</DIV>
        <DIV CLASS="card-body">

          Are you sure you want to $readableAction $name?
END_HTML

  if ($type eq "segment")
  {

    #get the names of all products in the selected segments
    foreach $segmentID (@segmentIDs)
    {
      $segmentIDStr .= "$segmentID,";
    }
    chop($segmentIDStr);
    $dbName = $structDB . "segment_item";
    $query = "SELECT itemID FROM $dsSchema.$dbName where segmentID IN ($segmentIDStr)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    $itemIDStr = "";
    while (($id) = $dbOutput->fetchrow_array)
    {
      $itemIDStr .= "$id,"
    }
    chop($itemIDStr);

    #if there are products inside the segments, output option to delete them
    if (length($itemIDStr) > 0)
    {
      print <<END_HTML;
          <P>&nbsp;</P>
          <DIV CLASS="form-check">
            <INPUT CLASS="form-check-input" NAME="delsegitems" ID="delsegitems" TYPE="checkbox">
            <LABEL CLASS="form-check-label" FOR="delsegitems">Also delete all of the products contained in the above segments:</LABEL>
          </DIV>
          <INPUT TYPE="hidden" NAME="baseitems" VALUE="$itemIDStr">

          <TABLE CLASS="table table-striped table-sm">
END_HTML

      $query = "SELECT name FROM $dsSchema.$dimDB WHERE ID IN ($itemIDStr) ORDER BY name";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      KAPutil_handle_db_err($db, $status, $query);
      while (($itemName) = $dbOutput->fetchrow_array)
      {
        print("<TR><TD>$itemName</TD></TR>\n");
      }

      print("</TABLE>\n");
    }
  }

  print <<END_HTML;

          <P>&nbsp;</P>

          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onclick="location.href='display.cld?ds=$dsID&dim=$dim'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-danger" TYPE="SUBMIT" ID="btn-submit"><I CLASS="bi bi-trash"></I> Delete $readableType</BUTTON>
          </DIV>

        </DIV>
      </DIV>
      </FORM>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
