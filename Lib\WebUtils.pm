
package Lib::WebUtils;

use lib "/opt/apache/app/";

use Exporter;
use Lib::KoalaConfig;

our @ISA = ('Exporter');

our @EXPORT = qw(
    &utils_userID_to_name
    &utils_userID_to_email
    &utils_get_user_hash
    &utils_get_org_hash
    &utils_acctType_to_text
    &print_html_navbar
    &print_html_footer
    &WebUtils_get_app_name
    &exit_error
    &exit_early_error
    &exit_warning
    &utils_sanitize_dim
    &utils_sanitize_integer
    &utils_sanitize_string
    &utils_slack
    &utils_slack_devops
    &utils_audit
  );




#-------------------------------------------------------------------------------
#
# Handle a database error of some kind during a web utility function call
#

sub utils_db_err
{
 my ($date);

 my ($db, $status, $text) = @_;


 if (!defined($status))
 {
   $date = localtime();
   print STDERR "$date: $text\n";
   if ($db->errstr =~ m/^MySQL server has gone away/)
   {
     die("Lost connection to database, terminating");
   }
 }
}



#-------------------------------------------------------------------------------
#
# Convert the specified user ID to the user's actual name
#

sub utils_userID_to_name
{
  my ($query, $dbOutput, $first, $last, $userName, $status);

  my ($db, $userID) = @_;


  $query = "SELECT first, last FROM app.users WHERE ID=$userID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  utils_db_err($db, $status, $query);
  ($first, $last) = $dbOutput->fetchrow_array;

  $userName = "$first $last";
  return($userName);
}



#-------------------------------------------------------------------------------
#
# Build and return a hash of all user names, hashed by userID
#

sub utils_get_user_hash
{
  my ($query, $dbOutput, $id, $first, $last, $name, $status);
  my (%userNames);

  my ($db) = @_;


  undef(%userNames);

  $userNames{0} = "Koala";

  #add user names to hash
  $query = "SELECT ID, first, last FROM app.users";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  utils_db_err($db, $status, $query);

  while (($id, $first, $last) = $dbOutput->fetchrow_array)
  {
    $name = "$first $last";
    $userNames{$id} = $name;
  }

  return(%userNames);
}



#-------------------------------------------------------------------------------
#
# Build and return a hash of all organization names, hashed by orgID
#

sub utils_get_org_hash
{
  my ($query, $dbOutput, $name, $status, $id);
  my (%orgNames);

  my ($db) = @_;


  #add user names to hash
  $query = "SELECT ID, name FROM app.orgs";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  utils_db_err($db, $status, $query);

  while (($id, $name) = $dbOutput->fetchrow_array)
  {
    $orgNames{$id} = $name;
  }

  return(%orgNames);
}



#-------------------------------------------------------------------------------
#
# Convert the specified account type to human-readable text
#

sub utils_acctType_to_text
{
  my ($acctType) = @_;


  if ($acctType == 0)
  {
    return("Viewer");
  }
  elsif ($acctType == 1)
  {
    return("Analyst");
  }
  elsif ($acctType == 5)
  {
    return("Administrator");
  }
  elsif ($acctType == 10)
  {
    return("Universal Administrator");
  }
}



#-------------------------------------------------------------------------------
#
# Get the specified user ID's email address
#

sub utils_userID_to_email
{
  my ($query, $dbOutput, $email, $status);

  my ($db, $userID) = @_;


  if (!($userID =~ m/^\d+$/))
  {
    return("");
  }

  $query = "SELECT email FROM app.users WHERE ID=$userID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  utils_db_err($db, $status, $query);
  ($email) = $dbOutput->fetchrow_array;

  return($email);
}



#-------------------------------------------------------------------------------
#
# Output HTML navigation bar at top of every appropriate page
#

sub print_html_navbar
{
  my ($db, $userID, $first, $last, $orgName) = @_;


  print <<END_HTML;
<DIV CLASS="container-fluid">
  <DIV CLASS="row align-items-center my-1">
    <DIV CLASS="col-auto">
      <A CLASS="navbar-brand" HREF="$Lib::KoalaConfig::kapHostURL/app/home.cld"><IMG SRC="$Lib::KoalaConfig::kapHostURL/images/navbarlogo.png" STYLE="border:0;" ALT="Koala Software"></A>
    </DIV>
    <DIV CLASS="col">
      &nbsp;
    </DIV>
    <DIV CLASS="col-auto">
      <A TARGET="_blank" HREF="$Lib::KoalaConfig::kapHostURL/app/docs.cld"><I CLASS="bi bi-question-circle"></I></A>
    </DIV>
    <DIV CLASS="col-auto">
      <DIV CLASS="dropdown">
        <BUTTON CLASS="btn btn-outline-primary dropdown-toggle" TYPE="button" data-bs-toggle="dropdown">$first <I CLASS="bi bi-person"></I></BUTTON>
        <UL CLASS="dropdown-menu dropdown-menu-right" STYLE="width:250px;">
          <LI>
            <H5>&nbsp;$first $last</H5>
            &nbsp;$orgName
            <P>
          </LI>
          <LI>
            <TABLE WIDTH="90%" ALIGN="center">
              <TR>
                <TD WIDTH="50%" ALIGN="left">
                  <BUTTON CLASS="btn btn-sm btn-primary" TYPE="button" onclick="location.href='$Lib::KoalaConfig::kapHostURL/app/admin/userSettings.cld'"><I CLASS="bi bi-gear"></I> Settings</BUTTON>
                </TD>
                <TD WIDTH="50%" ALIGN="right">
                  <BUTTON CLASS="btn btn-sm btn-primary" TYPE="button" onclick="location.href='$Lib::KoalaConfig::kapHostURL/'">Logout <I CLASS="bi bi-box-arrow-right"></I></BUTTON>
                </TD>
              </TR>
            </TABLE>
          </LI>
        </UL>
      </DIV>
    </DIV>
  </DIV>
</DIV>
END_HTML
}



#-------------------------------------------------------------------------------
#
# Output bottom half of HTML page (everything after actual page content)
#

sub print_html_footer
{
  print <<END_HTML;
</BODY>
</HTML>
END_HTML
}



#-------------------------------------------------------------------------------
#
# Return the application name ("Koala" unless it's a white-label cloud)
#

sub WebUtils_get_app_name
{
  my ($query, $status, $dbOutput, $appName);

  my ($db) = @_;


  $appName = "Koala";
  if ($Lib::KoalaConfig::whiteLabel)
  {
    $query = "SELECT value FROM app.config WHERE name='app_name'";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    utils_db_err($db, $status, $query);
    ($appName) = $dbOutput->fetchrow_array;
  }

  return($appName);
}



#-------------------------------------------------------------------------------
#
# Print an error message out to the browser and exit
#

sub exit_error
{
  my ($error) = @_;


  #send error to client
  print <<END_HTML;
<DIV CLASS="card border-danger" STYLE="width:550px; margin:auto;">
  <DIV CLASS="card-header bg-danger text-white">ERROR</DIV>
  <DIV CLASS="card-body">

    <P>
    $error

  </DIV>
</DIV>
END_HTML

  print_html_footer();

  exit;
}



#-------------------------------------------------------------------------------
#
# Print an error message out to the browser and exit, before we might have a
# DB connection and before any sort of header info has gone out.
#

sub exit_early_error
{
  my ($session, $error) = @_;


  print($session->header);

  #send error to client
  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>Koala</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
</HEAD>

<BODY>
<P>&nbsp;</P>
<DIV CLASS="card border-danger w-50 mx-auto">
  <DIV CLASS="card-header bg-danger text-white">ERROR</DIV>
  <DIV CLASS="card-body">

    <P>
    $error

  </DIV>
</DIV>
END_HTML

  print_html_footer();

  exit;
}



#-------------------------------------------------------------------------------
#
# Print an error message out to the browser and exit
#

sub exit_warning
{
  my ($warning) = @_;


  #send error to client
  print <<END_HTML;
<DIV CLASS="card border-warning" STYLE="width:550px; margin:auto;">
  <DIV CLASS="card-header bg-warning">Warning</DIV>
  <DIV CLASS="card-body">

    <P>
    $warning

  </DIV>
</DIV>
END_HTML

  print_html_footer();

  exit;
}



#-------------------------------------------------------------------------------
#
# "Sanitize" a dimension value passed as a CGI argument
#

sub utils_sanitize_dim
{
  my ($val) = @_;


  if (($val ne "p") && ($val ne "g") && ($val ne "t") && ($val ne "m"))
  {
    undef($val);
  }

  return($val);
}



#-------------------------------------------------------------------------------
#
# "Sanitize" an integer value passed as a CGI argument
#

sub utils_sanitize_integer
{
  my ($val) = @_;


  if (!($val =~ m/^\d+$/))
  {
    undef($val);
  }

  return($val);
}



#-------------------------------------------------------------------------------
#
# "Sanitize" a string value passed as a CGI argument. At the moment, just
# pulling HTML tags to avoid obvious potential security issues.
#

sub utils_sanitize_string
{
  my ($val) = @_;


  $val =~ s/<.+?>//g;

  return($val);
}



#-------------------------------------------------------------------------------
#
# Add an entry to the user actions audit log.
#

sub utils_audit
{
  my ($query, $q_action, $status);

  my ($db, $userID, $action, $dsID, $rptID, $presID) = @_;


  #correctly handle cases where data fields don't make sense to store
  if (length($userID) < 1)
  {
    $userID = "NULL";
  }
  if ($dsID == 0)
  {
    $dsID = "NULL";
  }
  if ($rptID == 0)
  {
    $rptID = "NULL";
  }
  if ($presID == 0)
  {
    $presID = "NULL";
  }

  if (length($action) < 1)
  {
    $action = "Whoa! Got an empty action!";
  }

  #if the action string is too long to fit in its column...
  if (length($action) > 127)
  {
    $action = substr($action, 0, 124);
    $action .= "...";
  }

  $q_action = $db->quote($action);

  $query = "INSERT INTO audit.userActions \
      (userID, timestamp, action, dsID, rptID, presID) \
      VALUES ($userID, NOW(), $q_action, $dsID, $rptID, $presID)";
  $status = $db->do($query);
  utils_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------------
#
# Posts an activity notification to Slack, in a separate process so any
# slowness between our cloud and Slack doesn't affect our UI speed
#

sub utils_slack
{
  my ($payload, $pid);

  my ($activity) = @_;


  #if we're a dev server, don't log activity
  if ($Lib::KoalaConfig::cloudtype eq "dev")
  {
    return;
  }

  $payload = "{\"channel\": \"#activity\", \"username\": \"$Lib::KoalaConfig::hostname\", \"text\": \"$activity\"}";

  #fork a new process to do the actual Slack post in the background
  $SIG{CHLD} = "IGNORE";
  if ($pid = fork())
  {
    #parent process
    return;
  }
  else
  {
    #child process

    #let Apache know not to wait on the child process
    close(STDIN);
    close(STDOUT);
    close(STDERR);

    #execute the standalone import program
    `/usr/bin/curl -s -X POST --data-urlencode 'payload=$payload' *****************************************************************************`;

    exit;
  }
}



#-------------------------------------------------------------------------------
#
# Posts a #devops notification to Slack, in a separate process so any
# slowness between our cloud and Slack doesn't affect our UI speed
#

sub utils_slack_devops
{
  my ($payload, $pid);

  my ($activity) = @_;


  #if we're a dev server, don't log activity
  if ($Lib::KoalaConfig::cloudtype eq "dev")
  {
    return;
  }

  $payload = "{\"channel\": \"#devops\", \"username\": \"$Lib::KoalaConfig::hostname\", \"text\": \"$activity\"}";

  #fork a new process to do the actual Slack post in the background
  $SIG{CHLD} = "IGNORE";
  if ($pid = fork())
  {
    #parent process
    return;
  }
  else
  {
    #child process

    #let Apache know not to wait on the child process
    close(STDIN);
    close(STDOUT);
    close(STDERR);

    #execute the standalone import program
    `/usr/bin/curl -s -X POST --data-urlencode 'payload=$payload' *****************************************************************************`;

    exit;
  }
}



#-------------------------------------------------------------------------------



1;
