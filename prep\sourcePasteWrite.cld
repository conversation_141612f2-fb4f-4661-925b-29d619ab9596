#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $flowID = $q->param('f');
  $key = $q->param('key');
  $action = $q->param('a');
  $jobID = $q->param('j');
  $rawdata = $q->param('rawdata');

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  $flowName = prep_flow_id_to_name($prepDB, $flowID);

  #write the data out to the file
  $localfile = "/opt/apache/app/tmp/prep.$userID.$key.pasted.txt";
  open(OUTPUT, ">$localfile");
  print OUTPUT "$rawdata";
  close(OUTPUT);

  #by default, we want to move on to the next step of the flow create/edit proc
  $redirect = "flowExtractData.cld?f=$flowID&key=$key&a=$action";

  #but if we're running in batch mode, go back to the statuspage
  if ($action eq "r")
  {
    $redirect = "runFlow.cld?f=$flowID&j=$jobID&c=1";
  }

  prep_audit($prepDB, $userID, "Manually pasted data to start a new job", $flowID);
  utils_slack("PREP: $first $last manually pasted data to start a new job in $flowName");

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META HTTP-EQUIV="refresh" CONTENT="0; URL=$redirect">
</HEAD>
<BODY>
</BODY>
</HTML>
END_HTML


#EOF
