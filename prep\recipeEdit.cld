#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;
use Text::CSV;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::DataSources;
use Lib::PrepFlows;
use Lib::PrepRecipes;
use Lib::PrepUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($kapDB);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Edit Recipe</TITLE>
<LINK REL="SHORTCUT ICON" HREF="$Lib::KoalaConfig::kapHostURL/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function changeDatasource()
{
  let dsID = document.getElementById('ds').value;

  location.href='?f=$flowID&j=$jobID&ds=' + dsID + '&a=ds';
}
</SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($kapDB, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="$Lib::KoalaConfig::kapHostURL/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?f=$flowID">Data Prep</A></LI>
    <LI CLASS="breadcrumb-item">$flowName</LI>
    <LI CLASS="breadcrumb-item active">Edit Recipe</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $flowID = $q->param('f');
  $jobID = $q->param('j');
  $step = $q->param('s');
  $action = $q->param('a');
  $ds = $q->param('ds');

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  $flowName = prep_flow_id_to_name($prepDB, $flowID);

  #make sure we have write privs for this data source
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs ne "W")
  {
    print_html_header();
    exit_error("You don't have privileges to edit this data flow.");
  }

  #determine where the "OK" button goes (if the user is editing a recipe from
  #the list of data flows, take them back there)
  if ($jobID < 1)
  {
    $okAction = "main.cld?f=$flowID";
  }
  else
  {
    $okAction = "flowViewData.cld?f=$flowID&j=$jobID";
  }

  print_html_header();

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #if we're being asked to move a step up in the recipe
  if ($action eq "u")
  {
    prep_recipe_step_up($prepDB, $flowID, $step);
  }
  elsif ($action eq "d")
  {
    prep_recipe_step_down($prepDB, $flowID, $step);
  }
  elsif ($action eq "r")
  {
    prep_recipe_step_remove($prepDB, $flowID, $step);
  }
  elsif ($action eq "ds")
  {
    if ($ds < 1)
    {
      $ds = "NULL";
    }
    $query = "UPDATE prep.flows SET dsID=$ds WHERE ID=$flowID";
    $prepDB->do($query);
  }

  #output the panel header HTML
  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-12 col-lg-10 col-xl-8"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Edit Data Transform Recipe</DIV>
        <DIV CLASS="card-body">

          <P>
          <DIV CLASS="table-responsive">
            <TABLE CLASS="table table-sm table-striped table-hover">
END_HTML

  #grab every recipe step for this flow
  $query = "SELECT step, action FROM prep.recipes WHERE flowID=$flowID ORDER BY step";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;

  while (($step, $stepAction) = $dbOutput->fetchrow_array)
  {

    #get a human-readable version of the recipe step action
    $readable = prep_recipe_step_text($stepAction, 1);

    print <<END_HTML;
              <TR>
                <TD>
                  <I CLASS="bi bi-arrow-up" STYLE="color:#007bff;" onClick="location.href='?f=$flowID&j=$jobID&s=$step&a=u'" TITLE="Move recipe step up"></I>
                </TD>
                <TD>
                  <I CLASS="bi bi-arrow-down" STYLE="color:#007bff;" onClick="location.href='?f=$flowID&j=$jobID&s=$step&a=d'" TITLE="Move recipe step down"></I>
                </TD>
                <TD>
                  <I CLASS="bi bi-trash" STYLE="color:#007bff;" onClick="location.href='?f=$flowID&j=$jobID&s=$step&a=r'" TITLE="Remove recipe step"></I>
                </TD>
END_HTML

  #allow editing of trim data recipe steps
  if (($stepAction =~ m/^TRANS-COL-TRIM-DATA/) && ($jobID < 1))
  {
    print <<END_HTML;
                <TD>
                  <I CLASS="bi bi-pencil" STYLE="color:#007bff;" onClick="location.href='transColTrimData.cld?f=$flowID&j=$jobID&s=$step&a=e'" TITLE="Edit recipe step"></I>
                </TD>
END_HTML
  }
  else
  {
    print("<TD></TD>\n");
  }

  print <<END_HTML;
                <TD>
                  $readable
                </TD>
              </TR>
END_HTML
  }

  print <<END_HTML;
            </TABLE>
          </DIV>

          <P>
            <LABEL FOR="ds">Export data to:</LABEL>
            <SELECT CLASS="form-select mx-1 w-100" NAME="ds" ID="ds" onChange="changeDatasource()">
              <OPTION VALUE="0" STYLE="color:gray; font-style:italic;">(Don't Export)</OPTION>
END_HTML

  #determine if the flow outputs to a Koala DS or not
  $query = "SELECT dsID FROM prep.flows WHERE ID=$flowID";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($dsID) = $dbOutput->fetchrow_array;
  if ($dsID < 1)
  {
    $dsID = 0;
  }

  #get the list of data sources the current user has access to
  @userSources = ds_list($kapDB, $userID, $acctType);
  %dsNames = ds_get_name_hash($kapDB);

  foreach $id (@userSources)
  {
    print("<OPTION VALUE=\"$id\">$dsNames{$id}</OPTION>\n");
  }

  print <<END_HTML;
            </SELECT>
            <SCRIPT>
              \$('select#ds').val('$dsID');
            </SCRIPT>

          <P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='$okAction'"><I CLASS="bi bi-check-lg"></I> Done</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
<P>
END_HTML

  print_html_footer();

  if (($action eq "u") || ($action eq "d"))
  {
    prep_audit($prepDB, $userID, "Rearranged order of recipe steps", $flowID);
    flow_telemetry($prepDB, $jobID, "$first $last rearranged order of recipe steps");
    utils_slack("PREP: $first $last rearranged order of recipe steps in $flowName");
  }
  elsif ($action eq "r")
  {
    prep_audit($prepDB, $userID, "Removed step from recipe", $flowID);
    flow_telemetry($prepDB, $jobID, "$first $last removed step from transform recipe");
    utils_slack("PREP: $first $last removed step from transform recipe in $flowName");
  }
  elsif (($action eq "ds") && ($ds < 1))
  {
    prep_audit($prepDB, $userID, "Turned off automatic data source updating", $flowID);
    flow_telemetry($prepDB, $jobID, "$first $last disabled automatic data source update");
    utils_slack("PREP: $first $last set $flowName to not automatically update a data source");
  }
  elsif ($action eq "ds")
  {
    prep_audit($prepDB, $userID, "Set data flow to automatically update data source $dsNames{$ds}", $flowID);
    flow_telemetry($prepDB, $jobID, "$first $last set data flow to automatically update data source");
    utils_slack("PREP: $first $last set $flowName to automatically update data source $dsNames{$ds}");
  }


#EOF
