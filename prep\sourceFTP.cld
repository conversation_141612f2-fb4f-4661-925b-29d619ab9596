#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($kapDB);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: FTP Upload</TITLE>
<LINK REL="SHORTCUT ICON" HREF="$Lib::KoalaConfig::kapHostURL/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
\$(document).ready(function()
  {
    \$('#modal-files').on('hide.bs.modal', function ()
    {
      \$('#modal-files').removeData('bs.modal');
      \$('#modal-files .modal-content').html('');
    });
  }
);



function showFiles()
{
  let ftpserver = document.getElementById('ftpserver').value;
  let ftpuser = document.getElementById('ftpuser').value;
  let ftppass = document.getElementById('ftppass').value;
  let ftppath = document.getElementById('ftppath').value;

  let url = "sourceFTPFiles.cld?f=$flowID&j=$jobID&ftpserver=" + ftpserver + "&ftpuser=" + ftpuser + "&ftppass=" + ftppass + "&ftppath=" + ftppath;

  \$('#modal-files').load(url, function (response, status, xhr)
  {
    if (status == "success")
    {
      \$('#modal-files').modal('show');
    }
  });
}



function updateFile(newFile)
{
  document.getElementById('ftppath').value = newFile;
}



function checkForm(form)
{
  \$("#btn-submit").prop('disabled', true);
  \$("#btn-submit").text("Please Wait...");
  return(true);
}
</SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($kapDB, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="$Lib::KoalaConfig::kapHostURL/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">Data Prep</A></LI>
    <LI CLASS="breadcrumb-item">$flowName</LI>
    <LI CLASS="breadcrumb-item active">Import FTP Data</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  get_cgi_session_info();

  #get CGI parameters
  $flowID = $q->param('f');
  $action = $q->param('a');

  $ftpServer = "";
  $ftpUser = "";
  $ftpPass = "";
  $ftpPath = "";
  if ($action eq "e")
  {
    $query = "SELECT sourceInfo FROM prep.flows WHERE ID=$flowID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($sourceInfo) = $dbOutput->fetchrow_array;
    if ($sourceInfo =~ m/^FTP=(.*?)\|USER=(.*?)\|PASS=(.*?)\|PATH=(.*)$/)
    {
      $ftpServer = $1;
      $ftpUser = $2;
      $ftpPass = $3;
      $ftpPath = $4;
    }
    $actionVal = "Edit";
  }
  else
  {
    $actionVal = "New";
  }

  $flowName = prep_flow_id_to_name($prepDB, $flowID);

  print_html_header();

  #make sure we have write privs for this data flow
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to work with jobs in this data flow.");
  }

  #come up with a random 5 digit integer, used to uniquely identify source
  #files for this run
  $key = int(rand(99999));

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-11 col-lg-8 col-xl-7"> <!-- content -->

      <FORM METHOD="post" ACTION="sourceFTPLoad.cld" onsubmit="return checkForm(this);">
      <INPUT TYPE="hidden" NAME="f" VALUE="$flowID">
      <INPUT TYPE="hidden" NAME="key" VALUE="$key">
      <INPUT TYPE="hidden" NAME="a" VALUE="$action">

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Upload Data Files Via FTP</DIV>
        <DIV CLASS="card-body">

          <P>
          <TABLE STYLE="width:95%;">
            <TR>
              <TD STYLE="text-align:right;"><STRONG>FTP Server:</STRONG></TD>
              <TD COLSPAN=2><INPUT TYPE="text" NAME="ftpserver" ID="ftpserver" CLASS="form-control" VALUE="$ftpServer" required></TD>
            </TR>
            <TR>
              <TD STYLE="text-align:right;"><STRONG>FTP User Name:</STRONG></TD>
              <TD COLSPAN=2><INPUT TYPE="text" NAME="ftpuser" ID="ftpuser" CLASS="form-control" VALUE="$ftpUser" required></TD>
            </TR>
            <TR>
              <TD STYLE="text-align:right;"><STRONG>FTP Password:</STRONG></TD>
              <TD COLSPAN=2><INPUT TYPE="password" NAME="ftppass" ID="ftppass" CLASS="form-control" VALUE="$ftpPass" autocomplete="new-password" required></TD>
            </TR>
            <TR>
              <TD STYLE="text-align:right;"><STRONG>File Path:</STRONG></TD>
              <TD><INPUT TYPE="text" NAME="ftppath" ID="ftppath" CLASS="form-control" VALUE="$ftpPath" required></TD>
              <TD>&nbsp;<BUTTON CLASS="btn btn-primary" TYPE="button" onClick="showFiles()"><I CLASS="bi bi-search"></I></BUTTON></TD>
            </TR>
          </TABLE>

          <P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="location.href='main.cld'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-submit">Next <I CLASS="bi bi-arrow-right"></I></BUTTON>
          </DIV>

        </DIV>
      </DIV>

      </FORM>


    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->

<DIV ID="modal-files" CLASS="modal" ROLE="dialog">
</DIV>
END_HTML

  print_html_footer();

#EOF
