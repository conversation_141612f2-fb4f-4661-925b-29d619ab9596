package Lib::KAPSegmentation;

use lib "/opt/apache/app/";

use Exporter;
use Lib::DSRUtils;


our @ISA = ('Exporter');

our @EXPORT = qw(
    &KAPsegmentation_seg_ID_to_name
    &KAPsegmentation_segment_id_to_name
    &KAPsegmentation_hierarchy_ID_to_name
  );





#-------------------------------------------------------------------------------
#
# Return the name associated with the specified segmentation ID
#

sub KAPsegmentation_seg_ID_to_name
{
  my ($query, $dbOutput, $dbStub, $dbName, $name, $status);

  my ($db, $dsSchema, $dim, $segmentationID) = @_;


  #handle ITEM (used by item-level seghierarchy selections)
  if ($segmentationID eq "ITEM")
  {
    return("item");
  }

  $dbStub = KAPutil_get_dim_stub_name($dim);
  $dbName = $dbStub . "segmentation";

  #if we got handed a fully-qualified segmentation ID, strip it to the number
  if ($segmentationID =~ m/SEG_(\d+)/)
  {
    $segmentationID = $1;
  }

  $query = "SELECT name FROM $dsSchema.$dbName WHERE ID=$segmentationID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  ($name) = $dbOutput->fetchrow_array;

  return($name);
}



#-------------------------------------------------------------------------
#
# Return the name associated with the specified segment ID
#

sub KAPsegmentation_segment_id_to_name
{
  my ($query, $dbOutput, $dbStub, $dbName, $name, $status);

  my ($db, $dsSchema, $dim, $segmentID) = @_;


  $dbStub = KAPutil_get_dim_stub_name($dim);
  $dbName = $dbStub . "segment";

  #if we got handed a fully-qualified segment ID, strip it to the number
  if ($segmentID =~ m/SMT_(\d+)/)
  {
    $segmentID = $1;
  }

  $query = "SELECT name FROM $dsSchema.$dbName WHERE ID=$segmentID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  ($name) = $dbOutput->fetchrow_array;

  return($name);
}



#-------------------------------------------------------------------------------
#
# Return the name associated with the specified segmentation hierarchy ID
#

sub KAPsegmentation_hierarchy_ID_to_name
{
  my ($query, $dbOutput, $dbName, $name, $status);

 # my ($segHierID, $schema, $dim, $db) = @_;
  my ($db, $dsSchema, $dim, $segHierID) = @_;


  $dbStub = KAPutil_get_dim_stub_name($dim);
  $dbName = $dbStub . "seghierarchy";

  #if we got handed a fully-qualified seg hier ID, strip it to the number
  if (($segHierID =~ m/SHS_(\d+)/) || ($segHierID =~ m/SHR_(\d+)/))
  {
    $segHierID = $1;
  }

  $query = "SELECT name FROM $dsSchema.$dbName WHERE ID=$segHierID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  ($name) = $dbOutput->fetchrow_array;

  return($name);
}



#-------------------------------------------------------------------------------


1;
