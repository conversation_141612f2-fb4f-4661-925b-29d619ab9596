
package Lib::PrepClientTrim;

use lib "/opt/apache/app/";


use Exporter;


our @ISA = ('Exporter');

our @EXPORT = qw(
    &prep_client_trim_hash
    &prep_client_org_name_hash
);

our %archpointMktKeep = (
'ALBSCO Dal & Ft Wth Rem' => 1,
'ALBSCO Dal & Ft Wth TA' => 1,
'ALBSCO Houston Louisiana Rem' => 1,
'ALBSCO Houston Louisiana TA' => 1,
'ALBSCO Houston Louisiana xAOC Rem' => 1,
'ALBSCO Southern Div Rem' => 1,
'ALBSCO Southern Div TA' => 1,
'ALBSCO Southern Div xAOC Rem' => 1,
'ALBSCO Southwest Div TA' => 1,
'ALBSCO Southwest Div xAOC Rem' => 1,
'ALBSCO United Div Rem' => 1,
'ALBSCO United Div TA' => 1,
'ALBSCO United Div xAOC Rem' => 1,
'Austin SMM xAOC' => 1,
'AWG OK City/Dallas/Ft.Worth TA' => 1,
'AWG OK City/Dallas/Ft.Worth Rem' => 1,
'AWG OK City/Dallas/Ft.Worth xAOC Rem' => 1,
'Brookshire Brothers Total Rem' => 1,
'Brookshire Brothers Total TA' => 1,
'Brookshire Brothers Total xAOC Rem' => 1,
'Brookshires Total Rem' => 1,
'Brookshires Total TA' => 1,
'Brookshires Total xAOC Rem' => 1,
'Dallas/Ft. Worth SMM Food' => 1,
'Dallas/Ft. Worth SMM xAOC' => 1,
'Fiesta Total Rem' => 1,
'Fiesta Total TA' => 1,
'Fiesta Total xAOC Rem' => 1,
'Houston SMM Food' => 1,
'Houston SMM xAOC' => 1,
'Midwest Region Food' => 1,
'Midwest Region xAOC' => 1,
'Northeast Region Food' => 1,
'Northeast Region xAOC' => 1,
'Rem US South Central SMM Food' => 1,
'Rem US South Central SMM xAOC' => 1,
'San Antonio SMM xAOC' => 1,
'South Region Food' => 1,
'South Region xAOC' => 1,
'Target Total Dallas BM/FF TA' => 1,
'Target Total Dallas BM/FF xAOC Rem' => 1,
'Target Total Houston BM/FF TA' => 1,
'Target Total Houston BM/FF xAOC Rem' => 1,
'Target Total San Ant/Austin/W Tx BM/FF xAOC Rem' => 1,
'Total US Food' => 1,
'Total US xAOC' => 1,
'Walmart Dallas-Ft Worth TA' => 1,
'Walmart Dallas-Ft Worth xAOC Rem' => 1,
'Walmart Houston TA' => 1,
'Walmart Houston xAOC Rem' => 1,
'Walmart South-West Texas TA' => 1,
'Walmart South-West Texas xAOC Rem' => 1,
'Walmart Total US TA' => 1,
'Walmart Total US xAOC Rem' => 1,
'West Region Food' => 1,
'West Region xAOC' => 1,
'West South Central Division Drug' => 1,

#Connect
'ALBSCO Southern Div RM Food' => 1,
'ALBSCO Southern Div RM xAOC' => 1,
'ALBSCO Southern Div TA' => 1,
'ALBSCO Southwest Div RM xAOC' => 1,
'ALBSCO Southwest Div TA' => 1,
'ALBSCO United Div RM Food' => 1,
'ALBSCO United Div RM xAOC' => 1,
'ALBSCO United Div TA' => 1,
'Austin SMM xAOC' => 1,
'Brookshire Brothers Total RM Food' => 1,
'Brookshire Brothers Total RM xAOC' => 1,
'Brookshire Brothers Total TA' => 1,
'Brookshire Total RM Food' => 1,
'Brookshire Total RM xAOC' => 1,
'Brookshire Total TA' => 1,
'Dallas/Ft. Worth SMM Conv' => 1,
'Dallas/Ft. Worth SMM Food' => 1,
'Dallas/Ft. Worth SMM xAOC' => 1,
'El Rancho Supermercado Total RM Food' => 1,
'El Rancho Supermercado Total RM xAOC' => 1,
'El Rancho Supermercado Total TA' => 1,
'Fiesta Total RM Food' => 1,
'Fiesta Total RM xAOC' => 1,
'Fiesta Total TA' => 1,
'Houston SMM Conv' => 1,
'Houston SMM Drug' => 1,
'Houston SMM Food' => 1,
'Houston SMM xAOC' => 1,
'Mass Texas TA' => 1,
'Reasors Total TA' => 1,
'Reasors Total RM xAOC' => 1,
'Rem US South Central SMM Conv' => 1,
'Rem US South Central SMM Food' => 1,
'Rem US South Central SMM xAOC' => 1,
'Rem US South SMM Drug' => 1,
'San Antonio SMM xAOC' => 1,
'Target Supercenter BM/FF TA' => 1,
'Target Texas TA' => 1,
'Target Texas xAOC' => 1,
'Target Total BM/FF RM xAOC' => 1,
'Target Total BM/FF TA' => 1,
'Target Total Dallas BM/FF RM xAOC' => 1,
'Target Total Dallas BM/FF TA' => 1,
'Target Total Houston BM/FF RM xAOC' => 1,
'Target Total Houston BM/FF TA' => 1,
'Target Total San Ant/Austn/W TX BM/FF RM xAOC' => 1,
'Target Total San Ant/Austn/W TX BM/FF TA' => 1,
'Texas SMM Food' => 1,
'Texas SMM xAOC' => 1,
'Total US Conv' => 1,
'Total US Drug' => 1,
'Total US Food' => 1,
'Total US xAOC' => 1,
'Walmart Dallas-Ft Worth RM xAOC' => 1,
'Walmart Dallas-Ft Worth TA' => 1,
'Walmart Houston RM xAOC' => 1,
'Walmart Houston TA' => 1,
'Walmart South-West Texas RM xAOC' => 1,
'Walmart South-West Texas TA' => 1,
'Walmart Texas TA' => 1,
'Walmart Texas xAOC' => 1,
'Walmart Total US RM xAOC' => 1,
'Walmart Total US TA' => 1,
'Walmart Total US 2022 RM xAOC' => 1,
'Walmart Total US 2022 TA' => 1,
);


our %archpointAcadianMktKeep = (
'Ahold Delhaize Total Corp Rem' => 1,
'Ahold Delhaize Total Corp TA' => 1,
'Ahold Delhaize Total Corp xAOC Rem' => 1,
'Ahold USA Corp Total Rem' => 1,
'Ahold USA Corp Total TA' => 1,
'Ahold USA Corp Total xAOC Rem' => 1,
'ALBSCO Acme Rem' => 1,
'ALBSCO Acme TA' => 1,
'ALBSCO Acme xAOC Rem' => 1,
'ALBSCO Dal & Ft Wth Rem' => 1,
'ALBSCO Dal & Ft Wth TA' => 1,
'ALBSCO Houston Louisiana TA' => 1,
'ALBSCO Houston Louisiana xAOC Rem' => 1,
'ALBSCO Southern Div Rem' => 1,
'ALBSCO Southern Div TA' => 1,
'ALBSCO Southern Div xAOC Rem' => 1,
'ALBSCO Southwest Div TA' => 1,
'ALBSCO Southwest Div xAOC Rem' => 1,
'ALBSCO Total Company Rem' => 1,
'ALBSCO Total Company TA' => 1,
'ALBSCO United Div Rem' => 1,
'ALBSCO United Div TA' => 1,
'ALBSCO United Div xAOC Rem' => 1,
'AWG Gulf Coast Rem' => 1,
'AWG Gulf Coast TA' => 1,
'AWG Gulf Coast xAOC Rem' => 1,
'AWG Nashville Rem' => 1,
'AWG Nashville TA' => 1,
'AWG Nashville xAOC Rem' => 1,
'AWG OK City/Dallas/Ft.Worth TA' => 1,
'AWG OK City/Dallas/Ft.Worth xAOC Rem' => 1,
'AWG Springfield Rem' => 1,
'AWG Springfield TA' => 1,
'AWG Springfield xAOC Rem' => 1,
'Austin SMM xAOC' => 1,
'BJs Total Rem' => 1,
'BJs Total TA' => 1,
'BJs Total xAOC Rem' => 1,
'Bashas Rem' => 1,
'Bashas TA' => 1,
'Bashas Total Rem' => 1,
'Bashas Total TA' => 1,
'Bashas Total xAOC Rem' => 1,
'Big Y Total Rem' => 1,
'Big Y Total TA' => 1,
'Big Y Total xAOC Rem' => 1,
'Brookshire Brothers Banner TA' => 1,
'Brookshire Brothers Total Rem' => 1,
'Brookshire Brothers Total TA' => 1,
'Brookshire Brothers Total xAOC Rem' => 1,
'Brookshires Total Rem' => 1,
'Brookshires Total TA' => 1,
'Brookshires Total xAOC Rem' => 1,
'Coborns Cashwise Total Corp Rem' => 1,
'Coborns Cashwise Total Corp TA' => 1,
'Coborns Cashwise Total Corp xAOC Rem' => 1,
'Dallas/Ft. Worth SMM Food' => 1,
'Dallas/Ft. Worth SMM xAOC' => 1,
'Fiesta Total Rem' => 1,
'Fiesta Total TA' => 1,
'Fiesta Total xAOC Rem' => 1,
'Food Lion Total Rem' => 1,
'Food Lion Total TA' => 1,
'Food Lion Total xAOC Rem' => 1,
'Giant Eagle Cleveland Rem' => 1,
'Giant Eagle Cleveland TA' => 1,
'Giant Eagle Total Rem' => 1,
'Giant Eagle Total TA' => 1,
'Giant Eagle Total xAOC Rem' => 1,
'Giant Food Total Rem' => 1,
'Giant Food Total TA' => 1,
'Giant Food Total xAOC Rem' => 1,
'Hannaford Total Rem' => 1,
'Hannaford Total TA' => 1,
'Hannaford Total xAOC Rem' => 1,
'Houston SMM Food' => 1,
'Houston SMM xAOC' => 1,
'Hy-Vee Total Rem' => 1,
'Hy-Vee Total TA' => 1,
'Hy-Vee Total xAOC Rem' => 1,
'Publix Jacksonville Rem' => 1,
'Publix Jacksonville TA' => 1,
'Publix Jacksonville xAOC Rem' => 1,
'KVAT Food City Rem' => 1,
'KVAT Food City TA' => 1,
'KVAT Food City xAOC Rem' => 1,
'King Kullen Total Rem' => 1,
'King Kullen Total TA' => 1,
'King Kullen Total xAOC Rem' => 1,
'Lowes Food Banner Total Rem' => 1,
'Lowes Food Banner Total TA' => 1,
'Lowes Food Banner Total xAOC Rem' => 1,
'Meijer Total TA' => 1,
'Meijer Total xAOC Rem' => 1,
'Piggly Wiggly Midwest Rem' => 1,
'Piggly Wiggly Midwest TA' => 1,
'Piggly Wiggly Midwest xAOC Rem' => 1,
'Price Chopper Total Rem' => 1,
'Price Chopper Total TA' => 1,
'Price Chopper Total xAOC Rem' => 1,
'Publix Total Rem' => 1,
'Publix Total TA' => 1,
'Publix Total xAOC Rem' => 1,
'Raley\'s Corp Rem' => 1,
'Raley\'s Corp TA' => 1,
'Raley\'s Corp xAOC Rem' => 1,
'San Antonio SMM xAOC' => 1,
'Save Mart Banner Total Rem' => 1,
'Save Mart Banner Total TA' => 1,
'Save Mart Banner Total xAOC Rem' => 1,
'Schnucks Total Rem' => 1,
'Schnucks Total TA' => 1,
'Schnucks Total xAOC Rem' => 1,
'ShopRite Total Rem' => 1,
'ShopRite Total TA' => 1,
'ShopRite Total xAOC Rem' => 1,
'SpartanNash Total Retail Rem' => 1,
'SpartanNash Total Retail TA' => 1,
'SpartanNash Total Retail xAOC Rem' => 1,
'Stater Bros Total Rem' => 1,
'Stater Bros Total TA' => 1,
'Stater Bros Total xAOC Rem' => 1,
'Stop & Shop Total Rem' => 1,
'Stop & Shop Total TA' => 1,
'Stop & Shop Total xAOC Rem' => 1,
'Target Supercenter BM/FF TA' => 1,
'Target Total BM/FF TA' => 1,
'Target Total BM/FF xAOC Rem' => 1,
'Target Total Dallas BM/FF TA' => 1,
'Target Total Dallas BM/FF xAOC Rem' => 1,
'Target Total Houston BM/FF TA' => 1,
'Target Total Houston BM/FF xAOC Rem' => 1,
'Target Total San Ant/Austin/W TX BM/FF TA' => 1,
'Target Total San Ant/Austin/W TX BM/FF xAOC Rem' => 1,
'Texas SMM Food' => 1,
'Texas SMM xAOC' => 1,
'The Giant Company Total Rem' => 1,
'The Giant Company Total TA' => 1,
'The Giant Company Total xAOC Rem' => 1,
'Tops Total Rem' => 1,
'Tops Total TA' => 1,
'Tops Total xAOC Rem' => 1,
'Total US Food' => 1,
'Total US xAOC' => 1,
'WFM Southwest TA' => 1,
'WFM Southwest xAOC Rem' => 1,
'WFM Total Rem' => 1,
'WFM Total TA' => 1,
'Walmart Dallas-Ft Worth TA' => 1,
'Walmart Dallas-Ft Worth xAOC Rem' => 1,
'Walmart Houston TA' => 1,
'Walmart Houston xAOC Rem' => 1,
'Walmart South-West Texas TA' => 1,
'Walmart South-West Texas xAOC Rem' => 1,
'Walmart Total US TA' => 1,
'Walmart Total US xAOC Rem' => 1,
'Wegmans Total Rem' => 1,
'Wegmans Total TA' => 1,
'Wegmans Total xAOC Rem' => 1,
'Weis Total Rem' => 1,
'Weis Total TA' => 1,
'Weis Total xAOC Rem' => 1,

#Connect
'Ahold Delhaize Corp Total RM Food' => 1,
'Ahold Delhaize Corp Total RM xAOC' => 1,
'Ahold Delhaize Corp Total TA' => 1,
'Ahold USA Corp Total RM Food' => 1,
'Ahold USA Corp Total RM xAOC' => 1,
'Ahold USA Corp Total TA' => 1,
'ALBSCO Acme RM Food' => 1,
'ALBSCO Acme RM xAOC' => 1,
'ALBSCO Acme TA' => 1,
'ALBSCO Southern Div RM Food' => 1,
'ALBSCO Southern Div RM xAOC' => 1,
'ALBSCO Southern Div TA' => 1,
'ALBSCO Southwest Div RM xAOC' => 1,
'ALBSCO Southwest Div TA' => 1,
'ALBSCO Total Company RM Food' => 1,
'ALBSCO Total Company TA' => 1,
'ALBSCO United Div RM Food' => 1,
'ALBSCO United Div RM xAOC' => 1,
'ALBSCO United Div TA' => 1,
'Austin SMM xAOC' => 1,
'AWG Gulf Coast RM Food' => 1,
'AWG Gulf Coast RM xAOC' => 1,
'AWG Gulf Coast TA' => 1,
'AWG Nashville RM Food' => 1,
'AWG Nashville RM xAOC' => 1,
'AWG Nashville TA' => 1,
'AWG Ok City/Dallas/Ft.Worth RM Food' => 1,
'AWG Ok City/Dallas/Ft.Worth RM xAOC' => 1,
'AWG Ok City/Dallas/Ft.Worth TA' => 1,
'AWG Springfield RM Food' => 1,
'AWG Springfield RM xAOC' => 1,
'AWG Springfield TA' => 1,
'Bashas Total RM Food' => 1,
'Bashas Total RM xAOC' => 1,
'Bashas Total TA' => 1,
'Big Y Total RM Food' => 1,
'Big Y Total RM xAOC' => 1,
'Big Y Total TA' => 1,
'BJs Total RM Food' => 1,
'BJs Total RM xAOC' => 1,
'BJs Total TA' => 1,
'Brookshire Brothers Banner TA' => 1,
'Brookshire Brothers Total RM Food' => 1,
'Brookshire Brothers Total RM xAOC' => 1,
'Brookshire Brothers Total TA' => 1,
'Brookshire Total RM Food' => 1,
'Brookshire Total RM xAOC' => 1,
'Brookshire Total TA' => 1,
'Coborns Cashwise Total Corp RM Food' => 1,
'Coborns Cashwise Total Corp RM xAOC' => 1,
'Coborns Cashwise Total Corp TA' => 1,
'Dallas/Ft. Worth SMM Conv' => 1,
'Dallas/Ft. Worth SMM Food' => 1,
'Dallas/Ft. Worth SMM xAOC' => 1,
'Fiesta Total RM Food' => 1,
'Fiesta Total RM xAOC' => 1,
'Fiesta Total TA' => 1,
'Food Lion Total RM Food' => 1,
'Food Lion Total RM xAOC' => 1,
'Food Lion Total TA' => 1,
'Giant Eagle Total RM Food' => 1,
'Giant Eagle Total RM xAOC' => 1,
'Giant Eagle Total TA' => 1,
'Giant Food Total RM Food' => 1,
'Giant Food Total RM xAOC' => 1,
'Giant Food Total TA' => 1,
'Hannaford Total RM Food' => 1,
'Hannaford Total RM xAOC' => 1,
'Hannaford Total TA' => 1,
'Houston SMM Conv' => 1,
'Houston SMM Drug' => 1,
'Houston SMM Food' => 1,
'Houston SMM xAOC' => 1,
'Hy-Vee Total RM Food' => 1,
'Hy-Vee Total RM xAOC' => 1,
'Hy-Vee Total TA' => 1,
'King Kullen Total RM Food' => 1,
'King Kullen Total RM xAOC' => 1,
'King Kullen Total TA' => 1,
'KVAT Food City RM Food' => 1,
'KVAT Food City RM xAOC' => 1,
'KVAT Food City TA' => 1,
'Lowes Food Banner Total RM Food' => 1,
'Lowes Food Banner Total RM xAOC' => 1,
'Lowes Food Banner Total TA' => 1,
'Mass Texas TA' => 1,
'Meijer Total RM xAOC' => 1,
'Meijer Total TA' => 1,
'Piggly Wiggly Midwest RM Food' => 1,
'Piggly Wiggly Midwest RM xAOC' => 1,
'Piggly Wiggly Midwest TA' => 1,
'Price Chopper Total RM Food' => 1,
'Price Chopper Total RM xAOC' => 1,
'Price Chopper Total TA' => 1,
'Publix Total RM Food' => 1,
'Publix Total RM xAOC' => 1,
'Publix Total TA' => 1,
'Raley\'s Banner TA' => 1,
'Raley\'s Corp RM Food' => 1,
'Raley\'s Corp RM xAOC' => 1,
'Raley\'s Corp TA' => 1,
'Rem US South Central SMM Conv' => 1,
'Rem US South Central SMM Food' => 1,
'Rem US South Central SMM xAOC' => 1,
'Rem US South SMM Drug' => 1,
'San Antonio SMM xAOC' => 1,
'Save Mart Banner Total RM Food' => 1,
'Save Mart Banner Total RM xAOC' => 1,
'Save Mart Banner Total TA' => 1,
'Schnucks Total RM Food' => 1,
'Schnucks Total RM xAOC' => 1,
'Schnucks Total TA' => 1,
'ShopRite Total RM Food' => 1,
'ShopRite Total RM xAOC' => 1,
'ShopRite Total TA' => 1,
'SpartanNash Total Retail RM Food' => 1,
'SpartanNash Total Retail RM xAOC' => 1,
'SpartanNash Total Retail TA' => 1,
'Stater Bros Total RM Food' => 1,
'Stater Bros Total RM xAOC' => 1,
'Stater Bros Total TA' => 1,
'Stop & Shop Total RM Food' => 1,
'Stop & Shop Total RM xAOC' => 1,
'Stop & Shop Total TA' => 1,
'Target Supercenter BM/FF TA' => 1,
'Target Texas TA' => 1,
'Target Texas xAOC' => 1,
'Target Total BM/FF RM xAOC' => 1,
'Target Total BM/FF TA' => 1,
'Target Total Dallas BM/FF RM xAOC' => 1,
'Target Total Dallas BM/FF TA' => 1,
'Target Total Houston BM/FF RM xAOC' => 1,
'Target Total Houston BM/FF TA' => 1,
'Target Total San Ant/Austn/W TX BM/FF RM xAOC' => 1,
'Target Total San Ant/Austn/W TX BM/FF TA' => 1,
'Texas SMM Food' => 1,
'Texas SMM xAOC' => 1,
'The Giant Company Total RM Food' => 1,
'The Giant Company Total RM xAOC' => 1,
'The Giant Company Total TA' => 1,
'Tops Total RM Food' => 1,
'Tops Total RM xAOC' => 1,
'Tops Total TA' => 1,
'Total US Conv' => 1,
'Total US Drug' => 1,
'Total US Food' => 1,
'Total US xAOC' => 1,
'Walmart Dallas-Ft Worth RM xAOC' => 1,
'Walmart Dallas-Ft Worth TA' => 1,
'Walmart Houston RM xAOC' => 1,
'Walmart Houston TA' => 1,
'Walmart South-West Texas RM xAOC' => 1,
'Walmart South-West Texas TA' => 1,
'Walmart Texas TA' => 1,
'Walmart Texas xAOC' => 1,
'Walmart Total US 2022 RM xAOC' => 1,
'Walmart Total US 2022 TA' => 1,
'Walmart Total US RM xAOC' => 1,
'Walmart Total US TA' => 1,
'Wegmans Total RM Food' => 1,
'Wegmans Total RM xAOC' => 1,
'Wegmans Total TA' => 1,
'Weis Total RM Food' => 1,
'Weis Total RM xAOC' => 1,
'Weis Total TA' => 1,
'WFM South RM Food' => 1,
'WFM South RM xAOC' => 1,
'WFM South TA' => 1,
'WFM Southwest RM Food' => 1,
'WFM Southwest RM xAOC' => 1,
'WFM Southwest TA' => 1,
'WFM Total RM Food' => 1,
'WFM Total RM xAOC' => 1,
'WFM Total TA' => 1,
);


our %archpointRitewayMktKeep = (
'Ahold Delhaize Corp Total Rem' => 1,
'Ahold Delhaize Corp Total TA' => 1,
'Ahold Delhaize Corp Total xAOC Rem' => 1,
'Ahold Delhaize Total Corp TA' => 1,
'Ahold Delhaize Total Corp xAOC Rem' => 1,
'Ahold USA Corp Total Rem' => 1,
'Ahold USA Corp Total TA' => 1,
'Ahold USA Corp Total xAOC Rem' => 1,
'ALBSCO Total Company TA' => 1,
'Atlanta SMM Food' => 1,
'Atlanta SMM xAOC' => 1,
'Austin SMM xAOC' => 1,
'AWG Nashville TA' => 1,
'AWG Nashville xAOC Rem' => 1,
'Bashas TA' => 1,
'Bashas Total xAOC Rem' => 1,
'Big Y Total TA' => 1,
'Big Y Total xAOC Rem' => 1,
'Birmingham/Anniston/Tuscaloosa SMM Food' => 1,
'Birmingham/Anniston/Tuscaloosa SMM xAOC' => 1,
'Brookshire Brothers Banner TA' => 1,
'Brookshire Brothers Total xAOC Rem' => 1,
'Brookshires Total TA' => 1,
'Brookshires Total xAOC Rem' => 1,
'Charlotte SMM Food' => 1,
'Charlotte SMM xAOC' => 1,
'Coborns Cashwise Total Corp TA' => 1,
'Coborns Cashwise Total Corp xAOC Rem' => 1,
'CVS Total Corp ex HI TA' => 1,
'Dallas/Ft. Worth SMM xAOC' => 1,
'Eastern Carolinas Food' => 1,
'Florida Panhandle Plus Food' => 1,
'Food Lion Total Rem' => 1,
'Food Lion Total TA' => 1,
'Food Lion Total xAOC Rem' => 1,
'Giant Eagle Total TA' => 1,
'Giant Eagle Total xAOC Rem' => 1,
'Giant Food Total Rem' => 1,
'Giant Food Total TA' => 1,
'Giant Food Total xAOC Rem' => 1,
'Giant Martins Total Rem' => 1,
'Giant Martins Total TA' => 1,
'Giant Martins Total xAOC Rem' => 1,
'Greensboro SMM Food' => 1,
'Greensboro SMM xAOC' => 1,
'Greenville/Spartanburg SMM xAOC' => 1,
'Hannaford Total Rem' => 1,
'Hannaford Total TA' => 1,
'Hannaford Total xAOC Rem' => 1,
'Houston SMM xAOC' => 1,
'Hy-Vee Total Rem' => 1,
'Hy-Vee Total TA' => 1,
'Hy-Vee Total xAOC Rem' => 1,
'IGA Supermarket Total TA' => 1,
'IGA Supermarket Total xAOC Rem' => 1,
'Jacksonville SMM Food' => 1,
'Jacksonville SMM xAOC' => 1,
'King Kullen Total TA' => 1,
'King Kullen Total xAOC Rem' => 1,
'Knoxville SMM Food' => 1,
'Knoxville SMM xAOC' => 1,
'KVAT Food City Rem' => 1,
'KVAT Food City TA' => 1,
'KVAT Food City xAOC Rem' => 1,
'Lowes Food Banner Total Rem' => 1,
'Lowes Food Banner Total TA' => 1,
'Lowes Food Banner Total xAOC Rem' => 1,
'Meijer Total TA' => 1,
'Meijer Total xAOC Rem' => 1,
'Miami/West Palm Beach SMM Food' => 1,
'Miami/West Palm Beach SMM xAOC' => 1,
'Middle Atlantic Division Food' => 1,
'Middle Atlantic Division xAOC' => 1,
'Mobile/Pensacola SMM Food' => 1,
'Mobile/Pensacola SMM xAOC' => 1,
'Nashville SMM Food' => 1,
'Nashville SMM xAOC' => 1,
'Norfolk/Portsmouth/Newport News SMM Food' => 1,
'Norfolk/Portsmouth/Newport News SMM xAOC' => 1,
'Orlando/Daytona Beach/Melbourne SMM Food' => 1,
'Orlando/Daytona Beach/Melbourne SMM xAOC' => 1,
'Piggly Wiggly Carolina Total Rem' => 1,
'Piggly Wiggly Carolina Total TA' => 1,
'Piggly Wiggly Midwest TA' => 1,
'Piggly Wiggly Midwest xAOC Rem' => 1,
'Price Chopper Total TA' => 1,
'Price Chopper Total xAOC Rem' => 1,
'Publix Atlanta Rem' => 1,
'Publix Atlanta TA' => 1,
'Publix Atlanta xAOC Rem' => 1,
'Publix Charlotte Rem' => 1,
'Publix Charlotte TA' => 1,
'Publix Charlotte xAOC Rem' => 1,
'Publix Jacksonville Rem' => 1,
'Publix Jacksonville TA' => 1,
'Publix Jacksonville xAOC Rem' => 1,
'Publix Lakeland Rem' => 1,
'Publix Lakeland TA' => 1,
'Publix Lakeland xAOC Rem' => 1,
'Publix Miami Rem' => 1,
'Publix Miami TA' => 1,
'Publix Miami xAOC Rem' => 1,
'Publix Total Rem' => 1,
'Publix Total TA' => 1,
'Publix Total xAOC Rem' => 1,
'Raleigh/Durham/Fayetteville SMM Food' => 1,
'Raleigh/Durham/Fayetteville SMM xAOC' => 1,
'Raley\'s Banner TA' => 1,
'Raley\'s Corp xAOC Rem' => 1,
'Rem US South Atlantic SMM Food' => 1,
'Rem US South Atlantic SMM xAOC' => 1,
'Richmond Norfolk xAOC' => 1,
'Richmond/Petersburg SMM Food' => 1,
'San Antonio SMM xAOC' => 1,
'Save Mart Banner Total TA' => 1,
'Save Mart Banner Total xAOC Rem' => 1,
'Schnucks Total TA' => 1,
'Schnucks Total xAOC Rem' => 1,
'ShopRite Total Rem' => 1,
'ShopRite Total TA' => 1,
'ShopRite Total xAOC Rem' => 1,
'South Atlantic Division Food' => 1,
'South Atlantic Division xAOC' => 1,
'South Region Food' => 1,
'South Region xAOC' => 1,
'Stater Bros Total Rem' => 1,
'Stater Bros Total TA' => 1,
'Stater Bros Total xAOC Rem' => 1,
'Stop & Shop Total Rem' => 1,
'Stop & Shop Total TA' => 1,
'Stop & Shop Total xAOC Rem' => 1,
'Tampa/Ft. Myers SMM Food' => 1,
'Tampa/Ft. Myers SMM xAOC' => 1,
'Target Supercenter BM/FF TA' => 1,
'Target Total BM/FF TA' => 1,
'Target Total BM/FF xAOC Rem' => 1,
'Target Total Houston BM/FF TA' => 1,
'Target Total Houston BM/FF xAOC Rem' => 1,
'Target Total San Ant/Austin/W TX BM/FF TA' => 1,
'Target Total San Ant/Austin/W TX BM/FF xAOC Rem' => 1,
'The Giant Company Total Rem' => 1,
'The Giant Company Total TA' => 1,
'The Giant Company Total xAOC Rem' => 1,
'Tops Total TA' => 1,
'Tops Total xAOC Rem' => 1,
'Total US Food' => 1,
'Total US xAOC' => 1,
'Walgreens Corp Total TA' => 1,
'Walmart Dallas-Ft Worth TA' => 1,
'Walmart Dallas-Ft Worth xAOC Rem' => 1,
'Walmart Houston TA' => 1,
'Walmart Houston xAOC Rem' => 1,
'Walmart South-West Texas TA' => 1,
'Walmart South-West Texas xAOC Rem' => 1,
'Walmart Total US TA' => 1,
'Walmart Total US xAOC Rem' => 1,
'Wegmans Total Rem' => 1,
'Wegmans Total TA' => 1,
'Wegmans Total xAOC Rem' => 1,
'Weis Total Rem' => 1,
'Weis Total TA' => 1,
'Weis Total xAOC Rem' => 1,
'WFM Florida Rem' => 1,
'WFM Florida TA' => 1,
'WFM Florida xAOC Rem' => 1,
'WFM South Rem' => 1,
'WFM South TA' => 1,
'WFM South xAOC Rem' => 1,
'WFM Southwest Rem' => 1,
'WFM Southwest TA' => 1,
'WFM Southwest xAOC Rem' => 1,
'WFM Total Rem' => 1,
'WFM Total TA' => 1,
'WFM Total xAOC Rem' => 1,
);


our %bairMktKeep = (
'Ahold Delhaize Corp Total RM Food' => 1,
'Ahold Delhaize Corp Total RM xAOC' => 1,
'Ahold Delhaize Corp Total TA' => 1,
'Ahold USA Corp Total RM Food' => 1,
'Ahold USA Corp Total RM xAOC' => 1,
'Ahold USA Corp Total TA' => 1,
'ALBSCO Acme Philadelphia RM Food' => 1,
'ALBSCO Acme Philadelphia TA' => 1,
'ALBSCO Acme RM Food' => 1,
'ALBSCO Acme RM xAOC' => 1,
'ALBSCO Acme TA' => 1,
'ALBSCO Eastern RM Food' => 1,
'ALBSCO Eastern RM xAOC' => 1,
'ALBSCO Eastern TA' => 1,
'ALBSCO Mid-Atlantic Div RM Food' => 1,
'ALBSCO Mid-Atlantic Div TA' => 1,
'ALBSCO Southern Div RM Food' => 1,
'ALBSCO Southern Div RM xAOC' => 1,
'ALBSCO Southern Div TA' => 1,
'ALBSCO Southwest Div RM xAOC' => 1,
'ALBSCO Southwest Div TA' => 1,
'ALBSCO Total Company RM Food' => 1,
'ALBSCO Total Company TA' => 1,
'ALBSCO United Div RM Food' => 1,
'ALBSCO United Div RM xAOC' => 1,
'ALBSCO United Div TA' => 1,
'Austin SMM xAOC' => 1,
'AWG Gulf Coast RM Food' => 1,
'AWG Gulf Coast RM xAOC' => 1,
'AWG Gulf Coast TA' => 1,
'AWG Nashville RM Food' => 1,
'AWG Nashville RM xAOC' => 1,
'AWG Nashville TA' => 1,
'AWG Ok City/Dallas/Ft.Worth RM Food' => 1,
'AWG Ok City/Dallas/Ft.Worth RM xAOC' => 1,
'AWG Ok City/Dallas/Ft.Worth TA' => 1,
'AWG Springfield RM Food' => 1,
'AWG Springfield RM xAOC' => 1,
'AWG Springfield TA' => 1,
'Bashas Total RM Food' => 1,
'Bashas Total RM xAOC' => 1,
'Bashas Total TA' => 1,
'Big Y Total RM Food' => 1,
'Big Y Total RM xAOC' => 1,
'Big Y Total TA' => 1,
'BJs Total RM Food' => 1,
'BJs Total RM xAOC' => 1,
'BJs Total TA' => 1,
'Brookshire Brothers Banner TA' => 1,
'Brookshire Brothers Total RM Food' => 1,
'Brookshire Brothers Total RM xAOC' => 1,
'Brookshire Brothers Total TA' => 1,
'Brookshire Total RM Food' => 1,
'Brookshire Total RM xAOC' => 1,
'Brookshire Total TA' => 1,
'Coborns Cashwise Total Corp RM Food' => 1,
'Coborns Cashwise Total Corp RM xAOC' => 1,
'Coborns Cashwise Total Corp TA' => 1,
'Dallas/Ft. Worth SMM Conv' => 1,
'Dallas/Ft. Worth SMM Food' => 1,
'Dallas/Ft. Worth SMM xAOC' => 1,
'East North Central Division Food' => 1,
'East North Central Division xAOC' => 1,
'East South Central Division Food' => 1,
'East South Central Division xAOC' => 1,
'Fiesta Total RM Food' => 1,
'Fiesta Total RM xAOC' => 1,
'Fiesta Total TA' => 1,
'Food Lion Total RM Food' => 1,
'Food Lion Total RM xAOC' => 1,
'Food Lion Total TA' => 1,
'Giant Eagle Total RM Food' => 1,
'Giant Eagle Total RM xAOC' => 1,
'Giant Eagle Total TA' => 1,
'Giant Food Total RM Food' => 1,
'Giant Food Total RM xAOC' => 1,
'Giant Food Total TA' => 1,
'Hannaford Total RM Food' => 1,
'Hannaford Total RM xAOC' => 1,
'Hannaford Total TA' => 1,
'Harrisburg/Lancaster SMM Food' => 1,
'Houston SMM Conv' => 1,
'Houston SMM Drug' => 1,
'Houston SMM Food' => 1,
'Hy-Vee Total RM Food' => 1,
'Hy-Vee Total RM xAOC' => 1,
'Hy-Vee Total TA' => 1,
'King Kullen Total RM Food' => 1,
'King Kullen Total RM xAOC' => 1,
'King Kullen Total TA' => 1,
'KVAT Food City RM Food' => 1,
'KVAT Food City RM xAOC' => 1,
'KVAT Food City TA' => 1,
'Lowes Food Banner Total RM Food' => 1,
'Lowes Food Banner Total RM xAOC' => 1,
'Lowes Food Banner Total TA' => 1,
'Meijer Total RM xAOC' => 1,
'Meijer Total TA' => 1,
'Middle Atlantic Division Food' => 1,
'Middle Atlantic Division xAOC' => 1,
'Midwest Region Food' => 1,
'Midwest Region xAOC' => 1,
'Mitchell Grocery Total RM Food' => 1,
'Mitchell Grocery Total RM xAOC' => 1,
'Mitchell Grocery Total TA' => 1,
'New England Division Food' => 1,
'New England Division xAOC' => 1,
'Northeast Region Food' => 1,
'Northeast Region xAOC' => 1,
'Pacific Division Food' => 1,
'Pacific Division xAOC' => 1,
'Philadelphia SMM Conv' => 1,
'Piggly Wiggly Midwest RM Food' => 1,
'Piggly Wiggly Midwest RM xAOC' => 1,
'Piggly Wiggly Midwest TA' => 1,
'Price Chopper Total RM Food' => 1,
'Price Chopper Total RM xAOC' => 1,
'Price Chopper Total TA' => 1,
'Publix Atlanta RM Food' => 1,
'Publix Atlanta RM xAOC' => 1,
'Publix Atlanta TA' => 1,
'Publix Charlotte RM Food' => 1,
'Publix Charlotte RM xAOC' => 1,
'Publix Charlotte TA' => 1,
'Publix Jacksonville RM Food' => 1,
'Publix Jacksonville RM xAOC' => 1,
'Publix Jacksonville TA' => 1,
'Publix Lakeland RM Food' => 1,
'Publix Lakeland RM xAOC' => 1,
'Publix Lakeland TA' => 1,
'Publix Miami RM Food' => 1,
'Publix Miami RM xAOC' => 1,
'Publix Miami TA' => 1,
'Publix Total RM Food' => 1,
'Publix Total RM xAOC' => 1,
'Publix Total TA' => 1,
'Raley\'s Banner TA' => 1,
'Raley\'s Corp RM Food' => 1,
'Raley\'s Corp RM xAOC' => 1,
'Raley\'s Corp TA' => 1,
'Rem US Middle Atlantic SMM Conv' => 1,
'Rem US South Central SMM Conv' => 1,
'Rem US South Central SMM Food' => 1,
'Rem US South Central SMM xAOC' => 1,
'Rem US South SMM Drug' => 1,
'Rouses Total RM Food' => 1,
'Rouses Total RM xAOC' => 1,
'Rouses Total TA' => 1,
'San Antonio SMM xAOC' => 1,
'Save Mart Banner Total RM Food' => 1,
'Save Mart Banner Total RM xAOC' => 1,
'Save Mart Banner Total TA' => 1,
'Schnucks Total RM Food' => 1,
'Schnucks Total RM xAOC' => 1,
'Schnucks Total TA' => 1,
'Shoppers Food Warehouse TA' => 1,
'Shoppers Food Warehouse RM Food' => 1,
'Shoppes Food Warehouse RM xAOC' => 1,
'ShopRite Total RM Food' => 1,
'ShopRite Total RM xAOC' => 1,
'ShopRite Total TA' => 1,
'South Atlantic Division Food' => 1,
'South Atlantic Division xAOC' => 1,
'South Region Food' => 1,
'South Region xAOC' => 1,
'SpartanNash Total Retail RM Food' => 1,
'SpartanNash Total Retail RM xAOC' => 1,
'SpartanNash Total Retail TA' => 1,
'Stater Bros Total RM Food' => 1,
'Stater Bros Total RM xAOC' => 1,
'Stater Bros Total TA' => 1,
'Stop & Shop Total RM Food' => 1,
'Stop & Shop Total RM xAOC' => 1,
'Stop & Shop Total TA' => 1,
'Target Supercenter BM/FF TA' => 1,
'Target Total BM/FF RM xAOC' => 1,
'Target Total BM/FF TA' => 1,
'Target Total Dallas BM/FF RM xAOC' => 1,
'Target Total Dallas BM/FF TA' => 1,
'Target Total Houston BM/FF RM xAOC' => 1,
'Target Total Houston BM/FF TA' => 1,
'Target Total San Ant/Austn/W TX BM/FF RM xAOC' => 1,
'Target Total San Ant/Austn/W TX BM/FF TA' => 1,
'The Giant Company Total RM Food' => 1,
'The Giant Company Total RM xAOC' => 1,
'The Giant Company Total TA' => 1,
'Tops Total RM Food' => 1,
'Tops Total RM xAOC' => 1,
'Tops Total TA' => 1,
'Total US Conv' => 1,
'Total US Drug' => 1,
'Total US Food' => 1,
'Total US xAOC' => 1,
'UNFI CONVL Atlantic Region RM Food' => 1,
'UNFI CONVL Atlantic Region RM xAOC' => 1,
'UNFI CONVL Atlantic Region TA' => 1,
'UNFI CONVL Pittsburgh RM Food' => 1,
'UNFI CONVL Pittsburgh RM xAOC' => 1,
'UNFI CONVL Pittsburgh TA' => 1,
'UNFI CONVL Southeast Division RM Food' => 1,
'UNFI CONVL Southeast Division RM xAOC' => 1,
'UNFI CONVL Southeast Division TA' => 1,
'Walmart Dallas-Ft Worth RM xAOC' => 1,
'Walmart Dallas-Ft Worth TA' => 1,
'Walmart Houston RM xAOC' => 1,
'Walmart Houston TA' => 1,
'Walmart South-West Texas RM xAOC' => 1,
'Walmart South-West Texas TA' => 1,
'Walmart Total US 2022 RM xAOC' => 1,
'Walmart Total US 2022 TA' => 1,
'Walmart Total US RM xAOC' => 1,
'Walmart Total US TA' => 1,
'Wegmans Total RM Food' => 1,
'Wegmans Total RM xAOC' => 1,
'Wegmans Total TA' => 1,
'Weis Total RM Food' => 1,
'Weis Total RM xAOC' => 1,
'Weis Total TA' => 1,
'West Region Food' => 1,
'West Region xAOC' => 1,
'West South Central Division Food' => 1,
'West South Central Division xAOC' => 1,
'WFM South RM Food' => 1,
'WFM South RM xAOC' => 1,
'WFM South TA' => 1,
'WFM Southwest RM Food' => 1,
'WFM Southwest RM xAOC' => 1,
'WFM Southwest TA' => 1,
'WFM Total RM Food' => 1,
'WFM Total RM xAOC' => 1,
'WFM Total TA' => 1,
);


our %bbiMktKeep = (
'Ahold USA Corp Total RM Food' => 1,
'Ahold USA Corp Total TA' => 1,
'BBI CONV' => 1,
'BBI FOOD TA' => 1,
'Chicago SMM Conv' => 1,
'Dol Gen Total RM Food' => 1,
'Dol Gen Total TA' => 1,
'Dollar' => 1,
'Family Dollar Total RM Food' => 1,
'Family Dollar Total RM xAOC' => 1,
'Family Dollar Total TA' => 1,
'Food Lion Total RM Food' => 1,
'Food Lion Total RM xAOC' => 1,
'Food Lion Total TA' => 1,
'Hannaford Total RM Food' => 1,
'Hannaford Total RM xAOC' => 1,
'Hannaford Total TA' => 1,
'KVAT Food City RM Food' => 1,
'KVAT Food City RM xAOC' => 1,
'KVAT Food City TA' => 1,
'Lowes Food Banner Total RM Food' => 1,
'Lowes Food Banner Total RM xAOC' => 1,
'Lowes Food Banner Total TA' => 1,
'Mass' => 1,
'Nashville SMM Conv' => 1,
'Publix Total RM Food' => 1,
'Publix Total RM xAOC' => 1,
'Publix Total TA' => 1,
'Rem US Pacific SMM Conv' => 1,
'Rem US Pacific SMM Food' => 1,
'SE CONV' => 1,
'SE FOOD' => 1,
'Total US Conv' => 1,
'Total US Food' => 1,
'Total US xAOC' => 1,
'Total US xAOC + C' => 1,
);


our %esmMktKeep = (
'Ahold Delhaize Corp Total Rem' => 1,
'Ahold Delhaize Corp Total TA' => 1,
'Ahold Delhaize Corp Total xAOC Rem' => 1,
'Ahold USA Corp Total Rem' => 1,
'Ahold USA Corp Total TA' => 1,
'Ahold USA Corp Total xAOC Rem' => 1,
'ALBSCO Acme Rem' => 1,
'ALBSCO Acme TA' => 1,
'ALBSCO Acme xAOC Rem' => 1,
'ALBSCO Eastern Rem' => 1,
'ALBSCO Eastern TA' => 1,
'ALBSCO Eastern xAOC Rem' => 1,
'ALBSCO Shaws Div Rem' => 1,
'ALBSCO Shaws Div TA' => 1,
'ALBSCO Shaws Div xAOC Rem' => 1,
'ALBSCO Shaws Mass/Rhode Island Rem' => 1,
'ALBSCO Shaws Mass/Rhode Island TA' => 1,
'ALBSCO Total Company Rem' => 1,
'ALBSCO Total Company TA' => 1,
'ALBSCO Total Company xAOC Rem' => 1,
'Albany/Schenectady/Troy SMM Food' => 1,
'Allegiance Retail Services Rem' => 1,
'Allegiance Retail Services TA' => 1,
'Baltimore SMM Food' => 1,
'Big Y Total Rem' => 1,
'Big Y Total TA' => 1,
'Big Y Total xAOC Rem' => 1,
'BJs Central Region TA' => 1,
'BJs North Region TA' => 1,
'BJs South Region TA' => 1,
'BJs Total Rem' => 1,
'BJs Total TA' => 1,
'Boston/Manchester SMM Conv' => 1,
'Boston/Manchester SMM Drug' => 1,
'Boston/Manchester SMM Food' => 1,
'Providence RI/New Bedford MA SMM Food' => 1,
'Boston/Manchester SMM xAOC' => 1,
'Providence RI/New Bedford MA SMM xAOC' => 1,
'Buffalo SMM Food' => 1,
'Buffalo SMM xAOC' => 1,
'Charlotte SMM Food' => 1,
'Charlotte SMM xAOC' => 1,
'Cleveland/Akron/Canton SMM Conv' => 1,
'Cleveland/Akron/Canton SMM Food' => 1,
'Cleveland/Akron/Canton SMM xAOC' => 1,
'Columbus OH SMM Food' => 1,
'CVS Northeast Corp Comp Mkt' => 1,
'CVS Northeast Corp TA' => 1,
'CVS Northeast Corp xAOC Comp Mkt' => 1,
'CVS Total Corp ex HI Comp Mkt' => 1,
'CVS Total Corp ex HI Rem' => 1,
'CVS Total Corp ex HI TA' => 1,
'CVS Total Corp ex HI xAOC Rem' => 1,
'Delhaize America Total Corp Rem' => 1,
'Delhaize America Total Corp TA' => 1,
'Delhaize America Total Corp xAOC Rem' => 1,
'DeMoulas Total Rem' => 1,
'DeMoulas Total TA' => 1,
'DeMoulas Total xAOC Rem' => 1,
'Duane Reade Total TA' => 1,
'Eastern Carolinas Food' => 1,
'Fairway Markets Total Rem' => 1,
'Fairway Markets Total TA' => 1,
'Fairway Markets Total xAOC Rem' => 1,
'Family Dollar TA' => 1,
'Farm Fresh Total Rem' => 1,
'Farm Fresh Total TA' => 1,
'Farm Fresh Total xAOC Rem' => 1,
'Food Lion Total Rem' => 1,
'Food Lion Total TA' => 1,
'Food Lion Total xAOC Rem' => 1,
'Giant Eagle Cleveland Rem' => 1,
'Giant Eagle Cleveland TA' => 1,
'Giant Eagle Columbus TA' => 1,
'Giant Eagle Pittsburgh Rem' => 1,
'Giant Eagle Pittsburgh TA' => 1,
'Giant Eagle Total Rem' => 1,
'Giant Eagle Total TA' => 1,
'Giant Eagle Total xAOC Rem' => 1,
'Giant Food Total Rem' => 1,
'Giant Food Total TA' => 1,
'Giant Food Total xAOC Rem' => 1,
'The Giant Company Total Rem' => 1,
'The Giant Company Total TA' => 1,
'The Giant Company Total xAOC Rem' => 1,
'Hannaford Total Rem' => 1,
'Hannaford Total TA' => 1,
'Hannaford Total xAOC Rem' => 1,
'Hartford/New Haven SMM Food' => 1,
'Hartford/New Haven SMM xAOC' => 1,
'Heinen\'s Total Rem' => 1,
'Heinen\'s Total TA' => 1,
'King Kullen Total Rem' => 1,
'King Kullen Total TA' => 1,
'King Kullen Total xAOC Rem' => 1,
'Kings Super Market Total TA' => 1,
'Kinney Drugs Total Comp Mkt' => 1,
'Kinney Drugs Total Rem' => 1,
'Kinney Drugs Total TA' => 1,
'Kinney Drugs Total xAOC Rem' => 1,
'Kmart Total TA' => 1,
'KVAT Food City Rem' => 1,
'KVAT Food City TA' => 1,
'KVAT Food City xAOC Rem' => 1,
'Lowes Food Banner Total Rem' => 1,
'Lowes Food Banner Total TA' => 1,
'Lowes Food Banner Total xAOC Rem' => 1,
'Middle Atlantic Division Drug' => 1,
'Middle Atlantic Division Food' => 1,
'Middle Atlantic Division xAOC' => 1,
'New England Division Drug' => 1,
'New England Division Food' => 1,
'New England Division xAOC' => 1,
'New York SMM Conv' => 1,
'New York SMM Drug' => 1,
'New York SMM Food' => 1,
'New York SMM xAOC' => 1,
'Northeast Region Drug' => 1,
'Northeast Region Food' => 1,
'Northeast Region xAOC' => 1,
'Philadelphia SMM Conv' => 1,
'Philadelphia SMM Drug' => 1,
'Philadelphia SMM Food' => 1,
'Philadelphia SMM xAOC' => 1,
'Piggly Wiggly Carolina Total Rem' => 1,
'Piggly Wiggly Carolina Total TA' => 1,
'Pittsburgh SMM Drug' => 1,
'Pittsburgh SMM Food' => 1,
'Pittsburgh SMM xAOC' => 1,
'Price Chopper Enterprises Total Rem' => 1,
'Price Chopper Enterprises Total TA' => 1,
'Price Chopper Total Rem' => 1,
'Price Chopper Total TA' => 1,
'Price Chopper Total xAOC Rem' => 1,
'Raleigh/Durham/Fayetteville SMM Food' => 1,
'Greensboro SMM Food' => 1,
'Raleigh/Durham/Fayetteville SMM xAOC' => 1,
'Greensboro SMM xAOC' => 1,
'Richmond/Petersburg SMM Food' => 1,
'Norfolk/Portsmouth/Newport News SMM Food' => 1,
'Richmond Norfolk xAOC' => 1,
'Richmond/Norfolk Conv' => 1,
'Rite Aid - Corp Total Rem' => 1,
'Rite Aid - Corp Total TA' => 1,
'Rite Aid - Corp Total xAOC Rem' => 1,
'Roanoke/Lynchburg Food' => 1,
'ShopKo Total TA' => 1,
'Shoppers Food Warehouse Rem' => 1,
'Shoppers Food Warehouse TA' => 1,
'Shoppers Food Warehouse xAOC Rem' => 1,
'ShopRite Total Rem' => 1,
'ShopRite Total TA' => 1,
'ShopRite Total xAOC Rem' => 1,
'South Atlantic Division Drug' => 1,
'South Atlantic Division Food' => 1,
'South Atlantic Division xAOC' => 1,
'South Region Drug' => 1,
'South Region Food' => 1,
'South Region xAOC' => 1,
'Stop & Shop Central Rem' => 1,
'Stop & Shop Central TA' => 1,
'Stop & Shop Central xAOC Rem' => 1,
'Stop & Shop North Rem' => 1,
'Stop & Shop North TA' => 1,
'Stop & Shop North xAOC Rem' => 1,
'Stop & Shop South Rem' => 1,
'Stop & Shop South TA' => 1,
'Stop & Shop South xAOC Rem' => 1,
'Stop & Shop Total Rem' => 1,
'Stop & Shop Total TA' => 1,
'Stop & Shop Total xAOC Rem' => 1,
'LEGACY SV East Mid Atlantic Division Rem' => 1,
'LEGACY SV East Mid Atlantic Division TA' => 1,
'LEGACY SV East Mid Atlantic Division xAOC Rem' => 1,
'LEGACY SV East Region Independent Rem' => 1,
'LEGACY SV East Region Independent TA' => 1,
'LEGACY SV East Region Independent xAOC Rem' => 1,
'UNFI CONVL Pittsburgh Rem' => 1,
'UNFI CONVL Pittsburgh TA' => 1,
'UNFI CONVL Pittsburgh xAOC Rem' => 1,
'UNFI CONVL Southeast Region TA' => 1,
'LEGACY SV Total Enterprise Rem' => 1,
'LEGACY SV Total Enterprise TA' => 1,
'Syracuse Food' => 1,
'Target Total TA' => 1,
'Tops Total Rem' => 1,
'Tops Total TA' => 1,
'Tops Total xAOC Rem' => 1,
'Total US Convenience' => 1,
'Total US Drug' => 1,
'Total US Food' => 1,
'Total US xAOC' => 1,
'UNFI CONVL Atlantic Region TA' => 1,
'UNFI CONVL Atlantic Region REM' => 1,
'UNFI CONVL Atlantic Region xAOC Rem' => 1,
'Walgreens Corp Total TA' => 1,
'Walgreens Total Rem' => 1,
'Walgreens Total TA' => 1,
'Walmart Total US TA' => 1,
'Washington DC/Hagerstown SMM Drug' => 1,
'Washington DC/Hagerstown SMM Food' => 1,
'Washington DC/Hagerstown SMM xAOC' => 1,
'Wegmans Total Rem' => 1,
'Wegmans Total TA' => 1,
'Wegmans Total xAOC Rem' => 1,
'Weis Total Rem' => 1,
'Weis Total TA' => 1,
'Weis Total xAOC Rem' => 1,
'WFM Total TA' => 1,
'Wilkes Barre/Scranton Food' => 1
);


our %integrityMktKeep = (
'Ahold Delhaize Total Corp Rem' => 1,
'Ahold Delhaize Total Corp TA' => 1,
'Ahold Delhaize Total Corp xAOC Rem' => 1,
'Ahold USA Corp Total Rem' => 1,
'Ahold USA Corp Total TA' => 1,
'Ahold USA Corp Total xAOC Rem' => 1,
'ALBSCO Acme Rem' => 1,
'ALBSCO Acme TA' => 1,
'ALBSCO Acme xAOC Rem' => 1,
'ALBSCO Eastern Rem' => 1,
'ALBSCO Eastern TA' => 1,
'ALBSCO Eastern xAOC Rem' => 1,
'Giant Food Total Rem' => 1,
'Giant Food Total TA' => 1,
'Giant Food Total xAOC Rem' => 1,
'The Giant Company Total Rem' => 1,
'The Giant Company Total TA' => 1,
'The Giant Company Total xAOC Rem' => 1,
'Middle Atlantic Division Drug' => 1,
'Middle Atlantic Division Food' => 1,
'Middle Atlantic Division xAOC' => 1,
'Philadelphia SMM Conv' => 1,
'Stop & Shop Total Rem' => 1,
'Stop & Shop Total TA' => 1,
'Stop & Shop Total xAOC Rem' => 1,
'UNFI CONVL Pittsburgh Rem' => 1,
'UNFI CONVL Pittsburgh TA' => 1,
'UNFI CONVL Pittsburgh xAOC Rem' => 1,
'UNFI CONVL Southeast Region TA' => 1,
'Total US Convenience' => 1,
'Total US Food' => 1,
'Total US xAOC' => 1,
'UNFI CONVL Atlantic Region TA' => 1,
'UNFI CONVL Atlantic Region REM' => 1,
'UNFI CONVL Atlantic Region xAOC Rem' => 1,
'Weis Total Rem' => 1,
'Weis Total TA' => 1,
'Weis Total xAOC Rem' => 1,
'Wilkes Barre/Scranton Food' => 1,

#Connect
'Ahold Delhaize Corp Total RM Food' => 1,
'Ahold Delhaize Corp Total RM xAOC' => 1,
'Ahold Delhaize Corp Total TA' => 1,
'Ahold USA Corp Total RM Food' => 1,
'Ahold USA Corp Total RM xAOC' => 1,
'Ahold USA Corp Total TA' => 1,
'ALBSCO Acme Philadelphia RM Food' => 1,
'ALBSCO Acme Philadelphia TA' => 1,
'ALBSCO Acme RM Food' => 1,
'ALBSCO Acme RM xAOC' => 1,
'ALBSCO Eastern RM Food' => 1,
'ALBSCO Eastern RM xAOC' => 1,
'ALBSCO Eastern TA' => 1,
'ALBSCO Mid-Atlantic Div RM Food' => 1,
'ALBSCO Mid-Atlantic Div TA' => 1,
'ALBSCO Mid-Atlantic Div RM xAOC' => 1,
'ALBSCO Total Company RM Food' => 1,
'ALBSCO Total Company TA' => 1,
'Americas Food Basket Total RM Food' => 1,
'Americas Food Basket Total RM xAOC' => 1,
'Americas Food Basket Total TA' => 1,
'Food Lion Total RM Food' => 1,
'Food Lion Total RM xAOC' => 1,
'Food Lion Total TA' => 1,
'Giant Food Total RM Food' => 1,
'Giant Food Total RM xAOC' => 1,
'Giant Food Total TA' => 1,
'Hannaford Total RM Food' => 1,
'Hannaford Total RM xAOC' => 1,
'Hannaford Total TA' => 1,
'Harrisburg/Lancaster SMM Food' => 1,
'Middle Atlantic Division Food' => 1,
'Philadelphia SMM Conv' => 1,
'Rem US Middle Atlantic SMM Conv' => 1,
'Shoppers Food Warehouse TA' => 1,
'Shoppers Food Warehouse RM Food' => 1,
'Shoppes Food Warehouse RM xAOC' => 1,
'Stop & Shop Total RM Food' => 1,
'Stop & Shop Total RM xAOC' => 1,
'Stop & Shop Total TA' => 1,
'The Giant Company Total RM Food' => 1,
'The Giant Company Total RM xAOC' => 1,
'The Giant Company Total TA' => 1,
'Total US Conv' => 1,
'Total US Food' => 1,
'UNFI CONVL Atlantic Region RM Food' => 1,
'UNFI CONVL Atlantic Region RM xAOC' => 1,
'UNFI CONVL Atlantic Region TA' => 1,
'UNFI CONVL East Region TA' => 1,
'UNFI CONVL East Region RM Food' => 1,
'UNFI CONVL East Region RM xAOC' => 1,
'UNFI CONVL Pittsburgh RM Food' => 1,
'UNFI CONVL Pittsburgh RM xAOC' => 1,
'UNFI CONVL Pittsburgh TA' => 1,
'UNFI CONVL Southeast Division RM Food' => 1,
'UNFI CONVL Southeast Division RM xAOC' => 1,
'UNFI CONVL Southeast Division TA' => 1,
'Wawa Total RM Conv' => 1,
'Wawa Total TA' => 1,
'Weis Total RM Food' => 1,
'Weis Total RM xAOC' => 1,
'Weis Total TA' => 1,
);

our %ritewayMktKeep = (
'Eastern Carolinas Food' => 1,
'Florida Panhandle Plus Food' => 1,
'Food Lion Total Rem' => 1,
'Food Lion Total TA' => 1,
'Food Lion Total xAOC Rem' => 1,
'Hannaford Total Rem' => 1,
'Hannaford Total TA' => 1,
'Hannaford Total xAOC Rem' => 1,
'IGA Supermarket Total TA' => 1,
'IGA Supermarket Total xAOC Rem' => 1,
'KVAT Food City Rem' => 1,
'KVAT Food City TA' => 1,
'KVAT Food City xAOC Rem' => 1,
'Lowes Food Banner Total Rem' => 1,
'Lowes Food Banner Total TA' => 1,
'Lowes Food Banner Total xAOC Rem' => 1,
'Middle Atlantic Division Food' => 1,
'Middle Atlantic Division xAOC' => 1,
'Publix Atlanta Rem' => 1,
'Publix Atlanta TA' => 1,
'Publix Atlanta xAOC Rem' => 1,
'Publix Charlotte Rem' => 1,
'Publix Charlotte TA' => 1,
'Publix Charlotte xAOC Rem' => 1,
'Publix Jacksonville Rem' => 1,
'Publix Jacksonville TA' => 1,
'Publix Jacksonville xAOC Rem' => 1,
'Publix Lakeland Rem' => 1,
'Publix Lakeland TA' => 1,
'Publix Lakeland xAOC Rem' => 1,
'Publix Miami Rem' => 1,
'Publix Miami TA' => 1,
'Publix Miami xAOC Rem' => 1,
'Publix Total Rem' => 1,
'Publix Total TA' => 1,
'Publix Total xAOC Rem' => 1,
'Rem US South Atlantic SMM Food' => 1,
'Rem US South Atlantic SMM xAOC' => 1,
'South Atlantic Division Food' => 1,
'South Atlantic Division xAOC' => 1,
'South Region Food' => 1,
'South Region xAOC' => 1,
'Target Total BM/FF TA' => 1,
'Target Total BM/FF xAOC Rem' => 1,
'Total US Food' => 1,
'Total US xAOC' => 1,
'Walmart Total US TA' => 1,
'Walmart Total US xAOC Rem' => 1,
'WFM Florida Rem' => 1,
'WFM Florida TA' => 1,
'WFM Florida xAOC Rem' => 1,
'WFM South Rem' => 1,
'WFM South TA' => 1,
'WFM South xAOC Rem' => 1,
'WFM Total Rem' => 1,
'WFM Total TA' => 1,
'WFM Total xAOC Rem' => 1,


#Connect
'CUSTOM MID ATLANTIC FOOD' => 1,
'CUSTOM MID ATLANTIC XAOC' => 1,
'DEEP SOUTH FOOD' => 1,
'DEEP SOUTH XAOC' => 1,
'East South Central Division Food' => 1,
'East South Central Division xAOC' => 1,
'Food Lion Total RM Food' => 1,
'Food Lion Total RM xAOC' => 1,
'Food Lion Total TA' => 1,
'KVAT Food City RM Food' => 1,
'KVAT Food City RM xAOC' => 1,
'KVAT Food City TA' => 1,
'Lowes Food Banner Total RM Food' => 1,
'Lowes Food Banner Total RM xAOC' => 1,
'Lowes Food Banner Total TA' => 1,
'Mitchell Grocery Total RM Food' => 1,
'Mitchell Grocery Total RM xAOC' => 1,
'Mitchell Grocery Total TA' => 1,
'Publix Atlanta RM Food' => 1,
'Publix Atlanta RM xAOC' => 1,
'Publix Atlanta TA' => 1,
'Publix Charlotte RM Food' => 1,
'Publix Charlotte RM xAOC' => 1,
'Publix Charlotte TA' => 1,
'Publix Jacksonville RM Food' => 1,
'Publix Jacksonville RM xAOC' => 1,
'Publix Jacksonville TA' => 1,
'Publix Lakeland RM Food' => 1,
'Publix Lakeland RM xAOC' => 1,
'Publix Lakeland TA' => 1,
'Publix Miami RM Food' => 1,
'Publix Miami RM xAOC' => 1,
'Publix Miami TA' => 1,
'Publix Total RM Food' => 1,
'Publix Total RM xAOC' => 1,
'Publix Total TA' => 1,
'Rouses Total RM Food' => 1,
'Rouses Total RM xAOC' => 1,
'Rouses Total TA' => 1,
'South Atlantic Division Food' => 1,
'South Atlantic Division xAOC' => 1,
'South Region Food' => 1,
'South Region xAOC' => 1,
'SOUTHEAST XAOC' => 1,
'SOUTHEAST FOOD' => 1,
'Target Total BM/FF RM xAOC' => 1,
'Target Total BM/FF TA' => 1,
'Total US Food' => 1,
'Total US xAOC' => 1,
'Walmart Total US 2022 RM xAOC' => 1,
'Walmart Total US 2022 TA' => 1,
'Walmart Total US RM xAOC' => 1,
'Walmart Total US TA' => 1,
'WFM South RM Food' => 1,
'WFM South RM xAOC' => 1,
'WFM South TA' => 1,
'WFM Southeast RM Food' => 1,
'WFM Southeast RM xAOC' => 1,
'WFM Southeast TA' => 1,
'WFM Total RM Food' => 1,
'WFM Total RM xAOC' => 1,
'WFM Total TA' => 1,
);



our %koalaMixMktKeep = (
'Ahold Delhaize Total Corp Rem' => 1,
);


our %koalaFoodMktKeep = (
'Ahold Delhaize Total Corp TA' => 1,
'Ahold USA Corp Total TA' => 1,
'ALBSCO Acme TA' => 1,
'ALBSCO Jewel Div TA' => 1,
'ALBSCO Shaws Div TA' => 1,
'ALBSCO Total Company TA' => 1,
'ALBSCO United Div TA' => 1,
'Big Y Total TA' => 1,
'BJs Total TA' => 1,
'DeMoulas Total TA' => 1,
'Food Lion Total TA' => 1,
'Giant Eagle Total TA' => 1,
'Giant Food Total TA' => 1,
'Hannaford Total TA' => 1,
'IGA Supermarket Total TA' => 1,
'Meijer Total TA' => 1,
'Piggly Wiggly Midwest TA' => 1,
'Price Chopper Total TA' => 1,
'Publix Total TA' => 1,
'ShopRite Total TA' => 1,
'Stop & Shop Total TA' => 1,
'Target Total BM/FF TA' => 1,
'Tops Total TA' => 1,
'Total US Food' => 1,
'Wegmans Total TA' => 1,
'WFM Total TA' => 1,
);


our %koalaDrugMktKeep = (
'CVS Total Corp ex HI TA' => 1,
'Duane Reade Total TA' => 1,
'Kinney Drugs Total TA' => 1,
'Publix Total TA' => 1,
'Stop & Shop Total TA' => 1,
'Target Total BM/FF TA' => 1,
'Total US Drug' => 1,
'Walgreens Total TA' => 1,
);


our %koalaMassMktKeep = (
'BJs Total TA' => 1,
'Family Dollar TA' => 1,
'Meijer Total TA' => 1,
'Publix Total TA' => 1,
'Stop & Shop Total TA' => 1,
'Target Total BM/FF TA' => 1,
'Total US xAOC' => 1,
);


our %koalaSMMMktKeep = (
'Ahold Delhaize Total Corp Rem' => 1,
);


#-------------------------------------------------------------------------
#
# Return the customer-specific hash that contains pre-trim markets based on
# the tag included in the customer's MKTTRIM field of the parsing options
# string.
#

sub prep_client_trim_hash
{
  my ($clientTag) = @_;


  if ($clientTag eq "archpoint")
  {
    return(%archpointMktKeep);
  }
  elsif ($clientTag eq "apac")
  {
    return(%archpointAcadianMktKeep);
  }
  elsif ($clientTag eq "aprw")
  {
    return(%archpointRitewayMktKeep);
  }
  elsif ($clientTag eq "bair")
  {
    return(%bairMktKeep);
  }
  elsif ($clientTag eq "bbi")
  {
    return(%bbiMktKeep);
  }
  elsif ($clientTag eq "esm")
  {
    return(%esmMktKeep);
  }
  elsif ($clientTag eq "integrity")
  {
    return(%integrityMktKeep);
  }
  elsif ($clientTag eq "riteway")
  {
    return(%ritewayMktKeep);
  }
  elsif ($clientTag eq "koalamix")
  {
    return(%koalaMixMktKeep);
  }
  elsif ($clientTag eq "koalaf")
  {
    return(%koalaFoodMktKeep);
  }
  elsif ($clientTag eq "koalad")
  {
    return(%koalaDrugMktKeep);
  }
  elsif ($clientTag eq "koalam")
  {
    return(%koalaMassMktKeep);
  }
  elsif ($clientTag eq "koalasmm")
  {
    return(%koalaSMMMktKeep);
  }
  else
  {
    return;
  }
}



#-------------------------------------------------------------------------
#
# For UI purposes, return a human-readable version of org names that have a
# pre-trim.
#

sub prep_client_org_name_hash
{
  my ($clientTag) = @_;


  if ($clientTag eq "archpoint")
  {
    return("ArchPoint");
  }
  elsif ($clientTag eq "apac")
  {
    return("ArchPoint/Acadian");
  }
  elsif ($clientTag eq "aprw")
  {
    return("ArchPoint/RiteWay");
  }
  elsif ($clientTag eq "bair")
  {
    return("BAIR");
  }
  elsif ($clientTag eq "bbi")
  {
    return("BBI");
  }
  elsif ($clientTag eq "esm")
  {
    return("ESM Ferolie");
  }
  elsif ($clientTag eq "integrity")
  {
    return("Integrity");
  }
  elsif ($clientTag eq "riteway")
  {
    return("RiteWay");
  }
  elsif ($clientTag eq "koalamix")
  {
    return("Koala Test Mixed Markets");
  }
  elsif ($clientTag eq "koalaf")
  {
    return("Koala Test National Food");
  }
  elsif ($clientTag eq "koalad")
  {
    return("Koala Test National Drug");
  }
  elsif ($clientTag eq "koalam")
  {
    return("Koala Test National Mass");
  }
  elsif ($clientTag eq "koalasmm")
  {
    return("Koala Test SMMs");
  }

  else
  {
    return;
  }
}



#-------------------------------------------------------------------------


1;
