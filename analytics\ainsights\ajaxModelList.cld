#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::DataSources;
use Lib::AInsights::AInsights;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $userOnly = $q->param('user');

  print("Expires: -1\n");
  print("Cache-Control: private, max-age=0");
  print("Content-type: application/json\n\n");

  #get the list of the user's data flows and output them
  $db = KAPutil_connect_to_database();

  @userModels = AInsights_model_list($db, $userID, $acctType);
  $models = join(',', @userModels);

  #get list of all active model update jobs
  $query = "SELECT userID, analyticsID FROM app.jobs \
      WHERE operation='ANALYTICS-PRICE'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  while (($analyticsUserID, $analyticsID) = $dbOutput->fetchrow_array)
  {
    $activeJobs{$analyticsID} = 1;
  }

  %userNames = utils_get_user_hash($db);
  %dsNameHash = ds_get_name_hash($db);
  %dsUpdateHash = ds_get_update_hash($db);

  if ($userOnly > 0)
  {
    $query = "SELECT ID, userID, name, dsID, lastRun, UNIX_TIMESTAMP(lastRun) \
        FROM analytics.pricing WHERE userID=$userID ORDER BY name";
  }
  else
  {
    $query = "SELECT ID, userID, name, dsID, lastRun, UNIX_TIMESTAMP(lastRun) \
        FROM analytics.pricing WHERE ID IN ($models) ORDER BY name";
  }
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  print <<JSON_LABEL;
[
JSON_LABEL

  $count = 1;
  while (($priceModelID, $modelUserID, $name, $dsID, $lastRun, $lastRunSeconds) = $dbOutput->fetchrow_array)
  {

    #don't show models that belong to deleted data sources
    if (length($dsNameHash{$dsID}) < 1)
    {
      $count++;
      next;
    }

    #if the model is currently being updated
    $active = 0;
    if ($activeJobs{$priceModelID} == 1)
    {
      $active = 1;
      $name = "$name (Refreshing)";
    }

    #if appropriate, add a "refresh needed" status icon
    elsif ($lastRunSeconds < $dsUpdateHash{$dsID})
    {
      $name = "<IMG SRC='/icons/warn_yellow16.png' HEIGHT='12px' TITLE='The data source has been updated since the last model refresh'> $name";
    }

    print <<JSON_LABEL;
    {
      "ID": $priceModelID,
      "active": "$active",
      "AInsights Model": "$name",
      "Data Source": "$dsNameHash{$dsID}",
      "Last Run": "$lastRun",
      "Owner": "$userNames{$modelUserID}"
    }
JSON_LABEL
    if ($count < $status)
    {
      print(",");
    }

    $count++;
  }

  print <<JSON_LABEL;
]
JSON_LABEL

#EOF
