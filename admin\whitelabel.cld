#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Confirm User Removal</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png?t=$uniqueID">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Please Wait...');
  return(true);
}
</SCRIPT>

<STYLE>
.custom-file-button input[type=file]
{
  margin-left: -2px !important;
}

.custom-file-button input[type=file]::-webkit-file-upload-button
{
  display: none;
}

.custom-file-button input[type=file]::file-selector-button
{
  display: none;
}

.custom-file-button:hover label
{
  background-color: #dde0e3;
  cursor: pointer;
}

.input-hidden
{
  position: absolute;
  left: -9999px;
}

input[type=radio]:checked + label
{
  border: 3px solid #337ab7;
}
</STYLE>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="home.cld">Administration</A></LI>
    <LI CLASS="breadcrumb-item active">White Label Settings</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if ((length($email) < 1) || ($acctType < 1))
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $user = $q->param('u');

  $uniqueID = time();

  #connect to the database
  $db = KAPutil_connect_to_database();

  print_html_header();

  #determine if we already have a custom login logo we should show
  if (-e "/opt/apache/htdocs/images/loginlogo.png")
  {
    $loginLogo = "<IMG SRC='$Lib::KoalaConfig::kapHostURL/images/loginlogo.png?t=$uniqueID'>";
  }
  else
  {
    $defaultLoginLogoText = "<SMALL>(Koala default)</SMALL>";
  }

  $companyName = WebUtils_get_app_name($db);



  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-12 col-lg-10 col-xl-8"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Koala Cloud White Label Settings</DIV>
        <DIV CLASS="card-body">

          <P>
          <H6 CLASS="mb-2 text-muted">Company Name:</H6>
          <FORM ACTION="/app/admin/xhrHandleImageUpload.cld?t=corp" METHOD="post">
          <DIV CLASS="row">
            <DIV CLASS="col-4">
              <INPUT CLASS="form-control" NAME="name" onInput="showCorpSaveButton();" VALUE="$companyName">
            </DIV>  <!-- col -->
            <DIV CLASS="col-auto">
              <BUTTON ID="btn-comp-save" CLASS="btn btn-primary" STYLE="visibility:hidden;" TYPE="submit">Save</BUTTON>
              <SCRIPT>
              function showCorpSaveButton()
              {
                document.getElementById('btn-comp-save').style.visibility = 'visible';
              }
              </SCRIPT>
            </DIV>  <!-- col -->
          </DIV> <!--- row --->
          </FORM>

          <P>&nbsp;</P>
          <H6 CLASS="mb-2 text-muted">Page Branding Logo:</H6>
          <FORM ACTION="/app/admin/xhrHandleImageUpload.cld?t=pb" METHOD="post" ENCTYPE="multipart/form-data">
          <DIV CLASS="row">
            <DIV CLASS="col-2">
              <IMG SRC="$Lib::KoalaConfig::kapHostURL/images/navbarlogo.png?t=$uniqueID">
            </DIV>  <!-- col -->
            <DIV CLASS="col-auto">
              <DIV CLASS="input-group custom-file-button">
                <LABEL CLASS="input-group-text" FOR="inputGroupFile">Browse</LABEL>
                <INPUT TYPE="file" CLASS="form-control" ID="inputGroupFile" NAME="imgFile" onchange="showUploadButton();">
              </DIV>
            </DIV>
            <DIV CLASS="col-auto">
              <INPUT ID="btn-upload-file" CLASS="btn btn-primary mx-auto" STYLE="visibility:hidden;" TYPE="submit" VALUE="Upload Image" NAME="submit">
              <SCRIPT>
              function showUploadButton()
              {
                document.getElementById('btn-upload-file').style.visibility = 'visible';
              }
              </SCRIPT>
            </DIV>  <!-- col -->
          </DIV> <!--- row --->
          </FORM>

          <P>&nbsp;</P>
          <H6 CLASS="mb-2 text-muted">Browser Tab Icon (favicon):</H6>
          <FORM ACTION="/app/admin/xhrHandleImageUpload.cld?t=fav" METHOD="post" ENCTYPE="multipart/form-data">
          <DIV CLASS="row">
            <DIV CLASS="col-2">
              <IMG SRC="$Lib::KoalaConfig::kapHostURL/favicon.png?t=$uniqueID">
            </DIV>  <!-- col -->
            <DIV CLASS="col-auto">
              <DIV CLASS="input-group custom-file-button">
                <LABEL CLASS="input-group-text" FOR="inputGroupFavFile">Browse</LABEL>
                <INPUT TYPE="file" CLASS="form-control" ID="inputGroupFavFile" NAME="imgFile" onchange="showFavUploadButton();">
              </DIV>
            </DIV>
            <DIV CLASS="col-auto">
              <INPUT ID="btn-fav-upload-file" CLASS="btn btn-primary mx-auto" STYLE="visibility:hidden;" TYPE="submit" VALUE="Upload Image" NAME="submit">
              <SCRIPT>
              function showFavUploadButton()
              {
                document.getElementById('btn-fav-upload-file').style.visibility = 'visible';
              }
              </SCRIPT>
            </DIV>  <!-- col -->
          </DIV> <!--- row --->
          </FORM>

          <P>&nbsp;</P>
          <H6 CLASS="mb-2 text-muted">Login Page Logo:</H6>
          <FORM ACTION="/app/admin/xhrHandleImageUpload.cld?t=log" METHOD="post" ENCTYPE="multipart/form-data">
          <DIV CLASS="row">
            <DIV CLASS="col-2">
              $defaultLoginLogoText
            </DIV>  <!-- col -->
            <DIV CLASS="col-auto">
              <DIV CLASS="input-group custom-file-button">
                <LABEL CLASS="input-group-text" FOR="inputGroupLoginFile">Browse</LABEL>
                <INPUT TYPE="file" CLASS="form-control" ID="inputGroupLoginFile" NAME="imgFile" onchange="showLoginUploadButton();">
              </DIV>
            </DIV>
            <DIV CLASS="col-auto">
              <INPUT ID="btn-login-upload-file" CLASS="btn btn-primary mx-auto" STYLE="visibility:hidden;" TYPE="submit" VALUE="Upload Image" NAME="submit">
              <SCRIPT>
              function showLoginUploadButton()
              {
                document.getElementById('btn-login-upload-file').style.visibility = 'visible';
              }
              </SCRIPT>
            </DIV>  <!-- col -->
          </DIV> <!--- row --->
          <DIV CLASS="row">
            <DIV CLASS="col-auto">
              $loginLogo
            </DIV>  <!-- col -->
          </DIV> <!--- row-->
          </FORM>


END_HTML


  print <<END_HTML;
        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
