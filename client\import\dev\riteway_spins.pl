#!/usr/bin/perl

use Text::CSV;

#Import Spins data for Riteway

  open(INPUT, "$ARGV[0]");
  open(OUTPUT, ">$ARGV[1]");

  #start by grabbing all of the date columns from the file
  $line = <INPUT>;
  $line = <INPUT>;

  #the 2nd line contains our run date, which we're assuming contains year info
  $line =~ m/RUN\s+\d+\/\d+\/(\d+)/;
  $year = $1;

  $line = <INPUT>;
  $line = <INPUT>;	#burn remaining lines of file (header block)
  $line = <INPUT>;
  $line = <INPUT>;

  #read the actual column header line
  $line = <INPUT>;

  #get our list of dates, and turn into an array
  $line =~ m/SIZE\/UOM\s+(.*?)\s+Total/;
  $dateStr = $1;
  $dateStr =~ s/\s+/ /g;
  @timePeriods = split(' ', $dateStr);
  $idx = 0;
  foreach $timePeriod (@timePeriods)
  {
    $timePeriods[$idx] = "1 W/E $timePeriod/$year";
    $idx++;
  }

  #now that we have our time period array, rewind the input file
  close(INPUT);
  open(INPUT, "$ARGV[0]");

  #output our header line
  print OUTPUT "Geography,Time Period,UPC,pattr:CASE UPC,pattr:C&S #,pattr:ITEM #,Product,pseg:VENDOR,pseg:PACK,pseg:SIZE/UOM,CASES SOLD\n";

  #run through every line of the file
  while ($line = <INPUT>)
  {

    #if the line is blank, we're either starting a new data block or EOF
    if (length($line) < 5)
    {

      #burn the next line
      $line = <INPUT>;

      #next line contains our geography info
      $line = <INPUT>;
      $line =~ m/^DC: ([A-z,\-, ]+)/;
      $geography = $1;

      #next line contains vendor info
      $line = <INPUT>;
      $line =~ m/^.*?\s\s\s+(.*)\n$/;
      $vendor = $1;
      $vendor =~ s/,//g;
      chop($vendor);

      #burn remaining header lines
      $line = <INPUT>;
      $line = <INPUT>;
      $line = <INPUT>;
      $line = <INPUT>;
      $line = <INPUT>;
    }

    #clean special characters out of the data
    $line =~ s/,//g;
    $line =~ s/\"//g;

    #compress whitespace in "DESCRIPTION"
    $line =~ m/^(.*?\s+.*?\s+.*?\s+.*?\s+)(.*?)(\s\s+\d.*)$/;
    $tmp1 = $1;
    $tmp2 = $2;
    $tmp3 = $3;
    $tmp2 =~ s/\s+/ /g;
    $line = $tmp1 . $tmp2 . $tmp3;

    #turn the line into CSV
    $line =~ s/\s\s+/,/g;
    @columns = split(',', $line);

    #extract data values
    $caseUPC = $columns[0];
    $upc = $columns[1];
    $csNum = $columns[2];
    $itemNum = $columns[3];
    $product = $columns[4];
    $pack = $columns[5];
    $size = $columns[6];

    #output one line for each of the 13 time periods in the source line
    for ($idx=0; $idx < 13; $idx++)
    {
      $valueIdx = $idx + 7;
      print OUTPUT "$geography,$timePeriods[$idx],$upc,$caseUPC,$csNum,$itemNum,$product,$vendor,$pack,$size,$columns[$valueIdx]\n";
    }
  }

  close(OUTPUT);
  close(INPUT);
