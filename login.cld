#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;
use HTML::Packer;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::WebUtils;


#-------------------------------------------------------------------------


#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print("Content-type: text/html\n\n");

  $appName = WebUtils_get_app_name($db);

  $outputHTML = <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Logging In...');
  return(true);
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML
}



#-------------------------------------------------------------------------

  #make sure we use HTTPS, if available
  if ($Lib::KoalaConfig::kapHostURL =~ m/^https/)
  {
    if ($ENV{HTTPS} ne "on")
    {
      print("Status: 302 Moved temporarily\n");
      print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
      exit;
    }
  }

  $q = new CGI;
  #get CGI parameters
  $dev = $q->param('dev');

  $db = KAPutil_connect_to_database();

  if (!(defined($db)))
  {
    exit_early_error("Your cloud instance is currently overloaded - please try again in 15 minutes.");
  }


  print_html_header();

  #if the customer is white labeling, generate a generic login with their logo
  if (-e "/opt/apache/htdocs/images/loginlogo.png")
  {
    $outputHTML .= <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-8 col-md-6 col-lg-5 col-xl-4"> <!-- content -->

      <DIV CLASS="card mt-5">

      <IMG SRC="/images/loginlogo.png" CLASS="mx-auto img-fluid">

        <DIV CLASS="card-body">

          <FORM NAME="login" METHOD="post" ACTION="$Lib::KoalaConfig::kapHostURL/app/auth.cld" onsubmit="return checkForm(this);">
          <INPUT TYPE="hidden" NAME="dev" VALUE="$dev">
          <P>
          <LABEL FOR="email">Email:</LABEL>
          <INPUT TYPE="email" ID="email" NAME="email" CLASS="form-control mb-3" required>

          <LABEL FOR="password">Password:</LABEL>
          <INPUT TYPE="password" NAME="password" ID="password" CLASS="form-control mb-3" required>

          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-submit"><I CLASS="bi bi-box-arrow-in-right"></I> Login</BUTTON>
          </DIV>
          </FORM>
        </DIV>
      </DIV>
    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->

</BODY>
</HTML>
END_HTML
  }

  else
  {
   $outputHTML .= <<END_HTML;
<P>&nbsp;<P>
<DIV STYLE="margin:0 auto; background-image:url('/images/loginbg.png'); background-repeat:no-repeat; width:800px; height:488px;">
  <FORM NAME="login" METHOD="post" ACTION="$Lib::KoalaConfig::kapHostURL/app/auth.cld" onsubmit="return checkForm(this);">
  <INPUT TYPE="hidden" NAME="dev" VALUE="$dev">
  <DIV STYLE="height:175px;"></DIV>
  <TABLE STYLE="margin-left: 376px;">
    <TR>
      <TD>
        <LABEL STYLE="color:white;" FOR="email">Email:</LABEL>
        <INPUT TYPE="email" ID="email" NAME="email" CLASS="form-control" STYLE="width:250px;" required>
      </TD>
    </TR>
    <TR>
      <TD>
        <LABEL STYLE="color:white;" FOR="password">Password:</LABEL>
        <INPUT TYPE="password" NAME="password" ID="password" CLASS="form-control" STYLE="width:250px;" required>
      </TD>
    </TR>
    <TR>
      <TD STYLE="text-align:center;">
        <P>&nbsp;</P>
        <BUTTON CLASS="btn btn-success" TYPE="SUBMIT" ID="btn-submit">Login</BUTTON>
      </TD>
    </TR>
  </TABLE>
  </FORM>
</DIV>

<P>&nbsp;</P>
<DIV STYLE="margin:0 auto; width:800px; text-align:center; padding-left:200px;">
  <A HREF="http://www.koala-corp.com/">Business Intelligence in the Cloud</A> by Koala
</DIV>
</BODY>
</HTML>
END_HTML

  }

  if ($dev < 1)
  {
    $packer = HTML::Packer->init();
    %packerOpts = ('html5' => 1, 'remove_comments' => 1, 'remove_newlines' => 1,
                  'do_javascript' => 'shrink');
    $packer->minify(\$outputHTML, \%packerOpts);
  }

  print($outputHTML);

#EOF
