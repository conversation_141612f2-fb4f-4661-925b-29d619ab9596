#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#--------------------------------------------------------- ----------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Transfer Data Source Structures</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Please Wait...');
  return(true);
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Transfer Data Source Structures</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');

  #connect to the database
  $db = KAPutil_connect_to_database();

  #make sure we have write privs for this data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to modify this data source.");
  }

  $dsSchema = "datasource_" . $dsID;
  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  #make sure the data source isn't locked by another background process
  $ok = DSRutil_operation_ok($db, $dsID, "XFER-STRUCTS");

  if ($ok != 1)
  {
    exit_warning("Another job is currently using this data source - please try again later.")
  }

  $loadAvg = KAPutil_get_load_avg($db);
  if (($loadAvg >= 0.75) && ($acctType < 10))
  {
    $jobCount = KAPutil_get_user_jobs($db, $userID);

    #if the user already has more than a couple jobs running
    if ($jobCount > 1)
    {
      exit_error("Your analytics cloud is heavily loaded, and you already have at least one large job running. Wait a bit for that job to finish, and then try again.");
    }
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-11 col-lg-8 col-xl-7"> <!-- content -->

      <FORM METHOD="post" ACTION="transferStructures.cld" onsubmit="return checkForm(this);">
      <INPUT TYPE="hidden" NAME="dsID" VALUE="$dsID">

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Transfer Data Source Structures</DIV>
        <DIV CLASS="card-body">

          <B>Choose the data source to transfer data source structures from:</B><BR>
          <SELECT CLASS="form-select" NAME="sourceDS" >
END_HTML

  #get the list of data sources the user has access to
  @userSources = ds_list($db, $userID, $acctType);
  $sources = join(',', @userSources);
  $query = "SELECT ID, name, type FROM dataSources WHERE ID IN ($sources) \
      ORDER BY NAME";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  while (($id, $name, $type) = $dbOutput->fetchrow_array)
  {
    print("<OPTION VALUE=\"$id\">$name ($type)</OPTION>\n");
  }

  print <<END_HTML;
          </SELECT>

          <P>
          <DIV CLASS="form-check">
            <INPUT CLASS="form-check-input" name="overwrite" ID="overwrite" TYPE="checkbox">
            <LABEL CLASS="form-check-label" FOR="overwrite">Overwrite any existing structures of the same name and type</LABEL>
          </DIV>
          <DIV CLASS="form-check">
            <INPUT CLASS="form-check-input" name="matchUPC" ID="matchUPC" TYPE="checkbox">
            <LABEL CLASS="form-check-label" FOR="matchUPC">Match products using their UPC instead of name</LABEL>
          </DIV>

          <P>&nbsp;</P>

          <B>I want to transfer these structures:</B>
          <DIV CLASS="card bg-light">
            <DIV CLASS="card-body">
              <DIV CLASS="table-responsive">
              <TABLE STYLE="width:100%;">
                <TR>
                  <TD WIDTH="50%">
                    <DIV CLASS="form-check" STYLE="margin-top:0px; margin-bottom:0px;">
                      <INPUT CLASS="form-check-input" NAME="aggs" ID="aggs" TYPE="checkbox">
                      <LABEL CLASS="form-check-label" FOR="aggs">Aggregates</LABEL>
                    </DIV>
                  </TD>
                  <TD WIDTH="50%">
                    <DIV CLASS="form-check" STYLE="margin-top:0px; margin-bottom:0px;">
                      <INPUT CLASS="form-check-input" NAME="lists" ID="lists" TYPE="checkbox">
                      <LABEL CLASS="form-check-label" FOR="lists">Lists</LABEL>
                    </DIV>
                  </TD>
                </TR>
                <TR>
                  <TD WIDTH="50%">
                    <DIV CLASS="form-check" STYLE="margin-top:0px; margin-bottom:0px;">
                      <INPUT CLASS="form-check-input" NAME="segs" ID="segs" TYPE="checkbox">
                      <LABEL CLASS="form-check-label" FOR="segs">Segmentations</LABEL>
                    </DIV>
                  </TD>
                  <TD WIDTH="50%">
                    <DIV CLASS="form-check" STYLE="margin-top:0px; margin-bottom:0px;">
                      <INPUT CLASS="form-check-input" NAME="seghiers" ID="seghiers" TYPE="checkbox">
                      <LABEL CLASS="form-check-label" FOR="seghiers">Segmentation Hierarchies</LABEL>
                    </DIV>
                  </TD>
                </TR>
                <TR>
                  <TD WIDTH="50%">
                    <DIV CLASS="form-check" STYLE="margin-top:0px; margin-bottom:0px;">
                      <INPUT CLASS="form-check-input" NAME="aliases" ID="aliases" TYPE="checkbox">
                      <LABEL CLASS="form-check-label" FOR="aliases">Aliases</LABEL>
                    </DIV>
                  </TD>
                  <TD WIDTH="50%">
                    <DIV CLASS="form-check" STYLE="margin-top:0px; margin-bottom:0px;">
                      <INPUT CLASS="form-check-input" NAME="attrs" ID="attrs" TYPE="checkbox">
                      <LABEL CLASS="form-check-label" FOR="attrs">Attributes</LABEL>
                    </DIV>
                  </TD>
                </TR>
                <TR>
                  <TD WIDTH="50%" STYLE="vertical-align:top;">
                    <DIV CLASS="form-check" STYLE="margin-top:0px; margin-bottom:0px;">
                      <INPUT CLASS="form-check-input" NAME="calcmeas" ID="calcmeas" TYPE="checkbox">
                      <LABEL CLASS="form-check-label" FOR="calcmeas">Calculated Measures</LABEL>
                    </DIV>
                  </TD>
                  <TD WIDTH="50%">
                    <DIV CLASS="form-check" STYLE="margin-top:0px; margin-bottom:0px;">
                      <INPUT CLASS="form-check-input" NAME="aggrules" ID="aggrules" TYPE="checkbox">
                      <LABEL CLASS="form-check-label" FOR="aggrules">Measure Aggregation/Formatting</LABEL>
                    </DIV>
                  </TD>
                </TR>
              </TABLE>
            </DIV>
          </DIV>
        </DIV>

        <P>&nbsp;</P>

        <B>From these dimensions:</B>
        <DIV CLASS="card bg-light">
          <DIV CLASS="card-body">
            <DIV CLASS="table-responsive">
              <TABLE STYLE="width:500px;">
                <TR>
                  <TD STYLE="width:50%;">
                    <DIV CLASS="form-check" STYLE="margin-top:0px; margin-bottom:0px;">
                      <INPUT CLASS="form-check-input" NAME="xferprod" ID="xferprod" TYPE="checkbox">
                      <LABEL CLASS="form-check-label" FOR="xferprod">Products</LABEL>
                    </DIV>
                  </TD>
                  <TD WIDTH="50%">
                    <DIV CLASS="form-check" STYLE="margin-top:0px; margin-bottom:0px;">
                      <INPUT CLASS="form-check-input" NAME="xfergeo" ID="xfergeo" TYPE="checkbox">
                      <LABEL CLASS="form-check-label" FOR="xfergeo">Geographies</LABEL>
                    </DIV>
                  </TD>
                </TR>
                <TR>
                  <TD WIDTH="50%">
                    <DIV CLASS="form-check" STYLE="margin-top:0px; margin-bottom:0px;">
                      <INPUT CLASS="form-check-input" NAME="xfertime" ID="xfertime" TYPE="checkbox">
                      <LABEL CLASS="form-check-label" FOR="xfertime">Time Periods</LABEL>
                    </DIV>
                  </TD>
                  <TD WIDTH="50%">
                    <DIV CLASS="form-check" STYLE="margin-top:0px; margin-bottom:0px;">
                      <INPUT CLASS="form-check-input" NAME="xfermeas" ID="xfermeas" TYPE="checkbox">
                      <LABEL CLASS="form-check-label" FOR="xfermeas">Measures</LABEL>
                    </DIV>
                  </TD>
                </TR>
              </TABLE>
            </DIV>
          </DIV>
        </DIV>

        <P>&nbsp;<P>
        <DIV CLASS="text-center">
          <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="location.href='display.cld?ds=$dsID'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
          <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-submit">Next <I CLASS="bi bi-arrow-right"></I></BUTTON>
        </DIV>

      </DIV>
    </DIV>

    </FORM>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
<P>
END_HTML

  print_html_footer();

#EOF
