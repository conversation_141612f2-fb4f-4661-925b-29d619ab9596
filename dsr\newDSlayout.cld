#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: New Data Source</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Please Wait...');
  return(true);
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item active">New Data Source</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  $key = $q->param("key");
  @tabArray = $q->param("xlsxTabs");

  #if our import contains multi-tabbed Excel files, grab the tabs the user told
  #us to import
  undef($xlsxTabs);
  foreach $tab (@tabArray)
  {
    if ($tab =~ m/^TAB (.*) (\d+)$/)
    {
      $fileIndex = $1;
      $sheetID = $2;
      $xlsxTabs .= "$fileIndex $sheetID,";
    }
  }

  #connect to user login database
  $db = KAPutil_connect_to_database();

  print_html_header();

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-10 col-lg-7 col-xl-6"> <!-- content -->

      <FORM METHOD="post" ACTION="/app/dsr/newDSName.cld" onsubmit="return checkForm(this);">
      <INPUT TYPE="hidden" NAME="key" VALUE="$key">
      <INPUT TYPE="hidden" NAME="tabs" VALUE="$xlsxTabs">

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Data File Layout</DIV>
        <DIV CLASS="card-body">

          How is the data file laid out?

          <P>
          <DIV CLASS="form-check">
            <INPUT CLASS="form-check-input" TYPE="radio" NAME="layout" ID="Tabular" CHECKED VALUE="Tabular">
            <LABEL CLASS="form-check-label" FOR="Tabular">Tabular</LABEL>
          </DIV>
          <IMG STYLE="border:1px solid lightblue; height:79px;" SRC="/icons/format_tabular.png"><P>

          <DIV CLASS="form-check">
            <INPUT CLASS="form-check-input" TYPE="radio" NAME="layout" ID="Nested" VALUE="Nested">
            <LABEL CLASS="form-check-label" FOR="Nested">Nested Tables</LABEL>
          </DIV>
          <IMG STYLE="border:1px solid lightblue; height:79px;" SRC="/icons/format_nested.png"><P>
END_HTML

  #if the user has any import scripts defined for their cloud, display them
  $importScriptDir = "/opt/apache/app/client/import/" . $Lib::KoalaConfig::cloudname;
  if (-e $importScriptDir)
  {
    print <<END_HTML;
          <P>&nbsp;</P>
            <LABEL FOR="scriptID">Apply a normalization script:</LABEL>
            <SELECT CLASS="form-select" NAME="scriptID" ID="scriptID" STYLE="width:auto;">
              <OPTION VALUE="none">(None)</OPTION>
END_HTML

    #read the manifest file and build up a list of the client's custom imports
    open(MANIFEST, "$importScriptDir/manifest.dat");
    while ($line = <MANIFEST>)
    {
      $line =~ m/^(.*?) (.*?)$/;
      $script = $1;
      $scriptName = $2;
      print(" <OPTION VALUE='$script'>$scriptName</OPTION>\n");
    }
    close(MANIFEST);

    print("</SELECT></P>\n");
  }

  print <<END_HTML;

          <P>&nbsp;</P>
          <DIV CLASS="form-check">
            <INPUT CLASS="form-check-input" NAME="appendUPC" ID="appendUPC" TYPE="checkbox">
            <LABEL CLASS="form-check-label" FOR="appendUPC">Append UPC/EAN to product names</LABEL>
          </DIV>
          <DIV CLASS="form-check">
            <INPUT CLASS="form-check-input" NAME="compressWS" ID="compressWS" TYPE="checkbox" CHECKED>
            <LABEL CLASS="form-check-label" FOR="compressWS">Compress extra whitespace in item names</LABEL>
          </DIV>
          <DIV CLASS="form-check">
            <INPUT CLASS="form-check-input" NAME="dontOverwriteNames" ID="dontOverwriteNames" TYPE="checkbox">
            <LABEL CLASS="form-check-label" FOR="dontOverwriteNames">Don't update product names to match this data set</LABEL>
          </DIV>


          <P>&nbsp;<P>

          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="location.href='/app/dsr/main.cld'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-submit">Next <I CLASS="bi bi-arrow-right"></I></BUTTON>
          </DIV>

        </DIV>
      </DIV>

      </FORM>

      <P>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
