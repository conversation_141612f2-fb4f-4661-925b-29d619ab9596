#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  #output Content-type header
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  #output HTML
  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Data Source Sharing</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Sharing</LI>
  </OL>
</NAV>

<P>
END_HTML
}


#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');
  $refLinkCode = $q->param('r');

  if ($refLinkCode eq "m")
  {
    $refLink = "main.cld?ds=$dsID";
  }
  else
  {
    $refLink = "display.cld?ds=$dsID";
  }

  #sanitize parameters
  $dsID = utils_sanitize_integer($dsID);

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  #if we got an invalid parameter
  if ($dsID < 1)
  {
    exit_error("Invalid data source");
  }

  #make sure we're the owner of this data source or an admin
  $query = "SELECT userID FROM dataSources WHERE ID=$dsID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($dsOwnerID) = $dbOutput->fetchrow_array;

  if (($dsOwnerID != $userID) && ($acctType < 5))
  {
    exit_error("You don't have privileges to change sharing settings for this data source - you're not the data source owner.");
  }

  #run through the list of CGI parameters, extracting sharing information
  undef($Rusers);
  undef($RWusers);
  @names = $q->param;
  foreach $name (@names)
  {
    if ($name =~ m/^R (\d+)/)
    {
      $val = $q->param($name);
      if ($val eq "on")
      {
        $Rusers .= "$1,";
      }
    }
    if ($name =~ m/^W (\d+)/)
    {
      $val = $q->param($name);
      if ($val eq "on")
      {
        $RWusers .= "$1,";
      }
    }
  }
  chop($Rusers); chop($RWusers);

  #update the list of users with privs on the data source
  $q_rusers = $db->quote($Rusers);
  $q_rwusers = $db->quote($RWusers);
  $query = "UPDATE dataSources SET Rusers=$q_rusers, RWusers=$q_rwusers \
      WHERE ID=$dsID";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-7 col-lg-6 col-xl-5"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Sharing Settings Saved</DIV>
        <DIV CLASS="card-body">

          <P>
          Your changes to the sharing settings for data source <B>$dsName</B> have been saved.

          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary my-1" onClick="location.href='$refLink'"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

  $activity = "$first $last changed user permissions for $dsName";
  utils_audit($db, $userID, "Changed sharing settings", $dsID, 0, 0);
  utils_slack($activity);


#EOF
