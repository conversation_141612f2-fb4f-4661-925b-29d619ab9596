package Lib::Forecasts;

use lib "/opt/apache/app/";

use Exporter;
use Statistics::R;

use Lib::DSRUtils;
use Lib::DataSources;


@ISA = ('Exporter');

@EXPORT = qw(
    &forecast_id_to_name
    &forecast_get_dsID
    &forecast_list
    &forecast_rights
    &forecast_measure_by_name
    &forecast_audit
    &exponential_smoothing_simple
    &exponential_smoothing_double
    &holt_winters
    &arima
    &seasonal_classic
    &seasonal_loess
    &trend
    &calculate_errors
    &forecast_build);


our $TELEMETRY = 1;



#-------------------------------------------------------------------------------
#
# Output telemetry data, if enabled
#

sub DBG
{
  my ($str) = @_;


  if ($TELEMETRY == 1)
  {
    print STDERR "$str\n";
  }
}



#-------------------------------------------------------------------------------
#
# Handle a database error of some kind during forecast calculations
#

sub forecast_db_err
{
  my ($date);

  my ($db, $status, $text) = @_;


  if (!defined($status))
  {
    $date = localtime();
    print STDERR "$date: $text\n";
    if ($db->errstr =~ m/^MySQL server has gone away/)
    {
      die("Lost connection to database, terminating");
    }
  }
}



#-------------------------------------------------------------------------------
#
# Log telemetry information about the forecast process
#

sub forecast_telemetry
{
  my ($query, $q_text);

  my ($db, $fcUpdateID, $text) = @_;


  $text = ": $text\n";
  $q_text = $db->quote($text);
  $query = "UPDATE analytics.forecast_telemetry \
      SET telemetry = CONCAT(telemetry, NOW(), $q_text) \
      WHERE ID=$fcUpdateID";
  $db->do($query);
}



#-------------------------------------------------------------------------------
#
# Set the forecast creation/update status that'll be displayed to users.
#

sub forecast_set_status
{
  my ($query, $q_text);

  my ($db, $text) = @_;


  if (length($text) > 1)
  {
    $q_text = $db->quote($text);
  }
  else
  {
    $q_text = "NULL";
  }

  $query = "UPDATE app.jobs SET lastAction=NOW(), status=$q_text WHERE PID=$$";
  $db->do($query);
}



#-------------------------------------------------------------------------------
#
# Return the text name of the forecast with the supplied ID
#

sub forecast_id_to_name
{
  my ($query, $dbOutput, $status, $name);

  my ($db, $fcastID) = @_;


  #handle invalid arguments
  if ($fcastID < 1)
  {
    return("");
  }

  $query = "SELECT name FROM analytics.forecasts WHERE ID=$fcastID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  forecast_db_err($db, $status, $query);
  ($name) = $dbOutput->fetchrow_array;

  return($name);
}



#-------------------------------------------------------------------------------
#
# Return the data source containing the specified forecast
#

sub forecast_get_dsID
{
  my ($query, $dbOutput, $status, $dsID);

  my ($db, $fcID) = @_;


  #handle invalid arguments
  if ($fcID < 1)
  {
    return(0);
  }

  $query = "SELECT dsID FROM analytics.forecasts WHERE ID=$fcID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  forecast_db_err($db, $status, $query);
  ($dsID) = $dbOutput->fetchrow_array;

  return($dsID);
}



#-------------------------------------------------------------------------------
#
# Return an array of forecast IDs that the specified user has some
# level of privileges on (used mostly to display list of forecasts in UI).
#

sub forecast_list
{
  my ($query, $dbOutput, $status, $userEmail, $fcID);
  my ($ownerID, $Rusers, $RWusers, $ruser);
  my (@rusers, @rwusers, @userForecasts);
  my (%seen);

  my ($db, $userID, $acctType) = @_;


  #get the list of forecasts stored on the system, with priv info
  $query = "SELECT ID, userID, Rusers, RWusers FROM analytics.forecasts \
      ORDER BY name";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  forecast_db_err($db, $status, $query);

  #run through the list of models
  while (($fcID, $ownerID, $Rusers, $RWusers) = $dbOutput->fetchrow_array)
  {

    #split the user lists into arrays, and combine
    @rusers = split(',', $Rusers);
    @rwusers = split(',', $RWusers);
    push(@rusers, @rwusers);

    #push the model owner's ID onto the combined array
    push(@rusers, $ownerID);

    #see if the user has privs, and add the model ID to the returned
    #array if so
    foreach $ruser (@rusers)
    {
      if (($ruser == $userID) || ($acctType > 4))
      {
        push(@userForecasts, $fcID);
      }
    }
  }

  #unique-ify the list of data flows
  @userForecasts = grep { !$seen{$_}++ } @userForecasts;

  return(@userForecasts);
}



#-------------------------------------------------------------------------------
#
# Determine what rights the specified user has to the specified forecast.
# Returns a single character: "N" for no rights, "R" for run-only, "W" for
# write (edit).
#

sub forecast_rights
{
  my ($query, $dbOutput, $status, $userEmail, $ownerID, $Rusers, $RWusers);
  my ($rwuser, $ruser);
  my (@rusers, @rwusers);

  my ($db, $userID, $fcID, $acctType) = @_;


  #if no forecast ID is supplied, it implies it's a new one that isn't yet created
  if ($fcID < 1)
  {
    return("W");
  }

  #admins can access everything
  if ($acctType > 4)
  {
    return("W");
  }

  #get the list of read and read/write users for the specified model
  $query = "SELECT userID, Rusers, RWusers FROM analytics.forecasts WHERE ID=$fcID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  forecast_db_err($db, $status, $query);
  ($ownerID, $Rusers, $RWusers) = $dbOutput->fetchrow_array;

  #if the user owns the data source, they have full rights
  if ($userID == $ownerID)
  {
    return("W");
  }

  #split the user lists into arrays
  @rusers = split(',', $Rusers);
  @rwusers = split(',', $RWusers);

  #see if the user has read/write privs
  foreach $rwuser (@rwusers)
  {
    if ($rwuser == $userID)
    {
      return("W");
    }
  }

  #see if the user has run privs
  foreach $ruser (@rusers)
  {
    if ($ruser == $userID)
    {
      return("R");
    }
  }

  #if we made it this far, the user has no privs on the forecast
  return("N");
}



#-------------------------------------------------------------------------------
#
# Return the ID of the first measure we find matching a list of potential
# names.
#

sub forecast_measure_by_name
{
  my ($measureID, $dbOutput, $query, $status, $name);

  my ($db, $dsSchema, @measureNames) = @_;


  #if this is the first time we're called, populate the measure name hash
  if (!%measureNameHash)
  {
    $query = "SELECT ID, name FROM $dsSchema.measures";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    forecast_db_err($db, $status, $query);
    while (($measureID, $name) = $dbOutput->fetchrow_array)
    {
      $name = lc($name);
      $measureNameHash{$name} = $measureID;
    }
  }

  #hunt through the hash of measure names to see if we have any matches
  foreach $name (@measureNames)
  {
    $name = lc($name);
    if ($measureNameHash{$name} > 0)
    {
      return($measureNameHash{$name});
    }
  }

  #nothing found, so return undef
  return;
}



#-------------------------------------------------------------------------------
#
# Add an entry to the user actions audit log for forecasting
#

sub forecast_audit
{
  my ($query, $q_action, $status);

  my ($db, $userID, $fcID, $action) = @_;


  #correctly handle cases where data fields don't make sense to store
  if (length($userID) < 1)
  {
    $userID = "NULL";
  }

  if (length($action) < 1)
  {
    $action = "Whoa! Got an empty action!";
  }

  #if the action string is too long to fit in its column...
  if (length($action) > 127)
  {
    $action = substr($action, 0, 124);
    $action .= "...";
  }

  $q_action = $db->quote($action);

  $query = "INSERT INTO analytics.forecast_audit (userID, timestamp, fcID, action) \
    VALUES ($userID, NOW(), $fcID, $q_action)";
  $status = $db->do($query);
  forecast_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------------
#
# Perform simple exponential smoothing on the supplied data, automatically
# calculating the alpha unless one is specified.
# NB: Simple ES is really only good for predicting one time period into the
#     future - requesting more than 1 time period will just get you a bunch
#     of the same number
#

sub exponential_smoothing_simple
{
  my ($fcID, $dsID, $predict, $db) =  @_;


  DBG("\n\n================= SIMPLE EXPONENTIAL SMOOTHING ===================");

  #construct schema and cube names
  $dsSchema = "datasource_" . $dsID;
  $fcCube = "__fcastcube_" . $fcID;
  $fcMeta = "__fcast_" . $fcID;

  #connect to the R engine
  $R = Statistics::R->new();
  $R->startR or die("$$\n");

  #get the products, geographies, and times for the forecast
  $query = "SELECT timeperiods, products, geographies FROM analytics.forecasts \
      WHERE ID=$fcID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  forecast_db_err($db, $status, $query);

  ($timeIDstring, $productIDstring, $geoIDstring) = $dbOutput->fetchrow_array;

  #convert the ID strings into arrays
  @times = split(/,/, $timeIDstring);
  @products = split(/,/, $productIDstring);
  @geographies = split(/,/, $geoIDstring);

  #foreach combination of product and geography
  foreach $product (@products)
  {
    foreach $geography (@geographies)
    {
      DBG("\n\n----------- Product: $product Geography: $geography-----------");

      #add the forecast type to the forecast details
      $details = "type=ES-Simple";

      #get chrono-sorted observed values to feed into the forecasting engine
      $query = "SELECT time, measure FROM $dsSchema.$fcCube \
          WHERE product = $product AND geography = $geography AND source='base' \
          ORDER BY FIELD(time, $timeIDstring)";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      forecast_db_err($db, $status, $query);
      undef(@values);
      while (($time, $value) = $dbOutput->fetchrow_array)
      {
        push(@values, $value);
      }

      #build an R-style value vector
      $vectorValues = "x = c(";
      foreach $value (@values)
      {
        #a NULL value in the database is a 0 for simple smoothing purposes
        if (!defined($value))
        {
          $vectorValues .= "0,";
          next;
        }

        $vectorValues .= "$value,";
      }
      chop($vectorValues);
      $vectorValues .= ")";

      #send the vector to the forecasting engine
      DBG("-> $vectorValues");
      $R->send($vectorValues);

      #convert the vector into a time series object
      $R->send("x = ts(x)");

      #run simple Holt-Winters smoothing against the data and save fit
      $R->send("x.hw = HoltWinters(x, alpha=NULL, beta=FALSE, gamma=FALSE)");

      if ($TELEMETRY == 1)
      {
        $R->send("print(x.hw)");
        $output = $R->read();
        DBG("<- $output");
      }

      #get the smoothed data
      $R->send("print(x.hw\$fitted)");
      $output = $R->read();
      DBG("\n\n<- $output");

      #convert the smoothed data (all in 1 giant string) into an array of
      #strings, each looking like "12 11825.73 11825.73"
      @lines = split(/\n/, $output);

      #the first 5 lines are header info
      $index = 5;

      #extract the xhat value (2nd column) and update the forecast cube
      #appropriately.
      $first = 1;
      foreach $time (@times)
      {

        #there's no forecast for the very first timeperiod with ES
        if ($first)
        {
          $first = 0;
          $query = "UPDATE $dsSchema.$fcCube SET forecast=NULL \
              WHERE product=$product AND geography=$geography AND time = $time";
          $status = $db->do($query);
          forecast_db_err($db, $status, $query);
          next;
        }

        #extract the forecast value from the output and update database
        if ($lines[$index] =~ m/^\s*\d+\s+\S+\s+(\S+)$/)
        {
          $query = "UPDATE $dsSchema.$fcCube SET forecast=$1 \
              WHERE product=$product AND geography=$geography AND time = $time";
          $status = $db->do($query);
          forecast_db_err($db, $status, $query);
        }

        $index++;
      }

      #predict the measure value for the specified number of time periods
      #in the future
      $R->send("fc <- predict(x.hw, n.ahead=$predict)");
      $R->send("print(formatC(fc, digits=2, format=\"f\"), quote=FALSE)");
      $output = $R->read();
      DBG("<- $output");

      #convert the predictions (all in 1 giant string) into an array of
      #strings, each looking like " [1,] 125516.88"
      @lines = split(/\n/, $output);

      #peel off the first "fit" line that we don't use
      splice(@lines, 0, 1);

      #extract the predicted value (2nd column)
      undef($forecast);
      foreach $line (@lines)
      {

        #extract the forecast value from the output and build up the forecast
        #value string
        if ($line =~ m/^\s*\[\d+\,\]\s+(\S+)/)
        {
          $forecast .= "$1,";
        }
        else
        {
          $forecast .= "0,";
        }
      }

      #chop the trailing comma off the forecast value string
      chop($forecast);

      #get the alpha value for the fit and add to forecast details string
      $R->send("print(x.hw\$alpha)");
      $output = $R->read();
      $output =~ m/\[1\]\s+([\d\.]+)/;
      $details .= ",alpha=$1";

      #update the forecast for the future periods
      $q_forecast = $db->quote($forecast);
      $q_details = $db->quote($details);
      $query = "UPDATE $dsSchema.$fcMeta SET forecasts=$q_forecast, details=$q_details \
          WHERE product=$product AND geography=$geography";
      $status = $db->do($query);
      forecast_db_err($db, $status, $query);

      #store the forecasted values in the forecast data cube
      $query = "SELECT endDate FROM $dsSchema.$fcCube \
          WHERE product=$product AND geography=$geography AND source='forecast' \
          ORDER BY endDate";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      forecast_db_err($db, $status, $query);
      @forecastVals = split(',', $forecast);
      $previousVal = 0;
      foreach $val (@forecastVals)
      {

        #see if the forecast has stopped being useful (the same value twice in
        #sequence, if it isn't zero)
        if (($previousVal == $val) && ($previousVal > 0))
        {
          $query = "UPDATE $dsSchema.$fcMeta SET commentary='SES-Truncated' \
              WHERE product=$product AND geography=$geography";
          $status = $db->do($query);
          forecast_db_err($db, $status, $query);
          last;
        }
        $previousVal = $val;

        ($endDate) = $dbOutput->fetchrow_array;
        $query = "UPDATE $dsSchema.$fcCube SET forecast=$val \
            WHERE product=$product AND geography=$geography AND endDate='$endDate'";
        $status = $db->do($query);
        forecast_db_err($db, $status, $query);
      }
    }
  }

  $R->send("q('no')");
}



#-------------------------------------------------------------------------------
#
# Perform double exponential smoothing on the supplied data, automatically
# calculating alpha and beta unless they are specified.
#

sub exponential_smoothing_double
{

  my ($fcID, $dsID, $predict, $db) =  @_;


  DBG("\n\n================= DOUBLE EXPONENTIAL SMOOTHING ===================");

  #construct schema and cube names
  $dsSchema = "datasource_" . $dsID;
  $fcCube = "__fcastcube_" . $fcID;
  $fcMeta = "__fcast_" . $fcID;

  #connect to the R engine
  $R = Statistics::R->new();
  $R->startR or die("$$\n");

  #get the products, geographies, and times for the forecast
  $query = "SELECT timeperiods, products, geographies FROM analytics.forecasts \
      WHERE ID=$fcID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  forecast_db_err($db, $status, $query);

  ($timeIDstring, $productIDstring, $geoIDstring) = $dbOutput->fetchrow_array;

  #convert the ID strings into arrays
  @times = split(/,/, $timeIDstring);
  @products = split(/,/, $productIDstring);
  @geographies = split(/,/, $geoIDstring);

  #foreach combination of product and geography
  foreach $product (@products)
  {
    foreach $geography (@geographies)
    {
      DBG("\n\n----------- Product: $product Geography: $geography-----------");

      #add the forecast type to the forecast details
      $details = "type=ES-Double,period=$periodType";

      #select all time periods (with measures) for the product/geo combination
      #and store them in a hash
      $query = "SELECT time, measure FROM $dsSchema.$fcCube \
          WHERE product = $product AND geography = $geography AND source='base' \
          ORDER BY FIELD(time, $timeIDstring)";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      forecast_db_err($db, $status, $query);
      undef(@values);
      while (($time, $value) = $dbOutput->fetchrow_array)
      {
        push(@values, $value);
      }

      #build an R-style value vector
      $vectorValues = "x = c(";
      foreach $value (@values)
      {
        #a NULL value in the database is a 0 for simple smoothing purposes
        if (!defined($value))
        {
          $vectorValues .= "0,";
          next;
        }

        $vectorValues .= "$value,";
      }
      chop($vectorValues);
      $vectorValues .= ")";

      #send the vector to the forecasting engine
      DBG("-> $vectorValues");
      $R->send($vectorValues);

      #convert the vector into a time series object
      $R->send("x = ts(x)");

      #run double Holt-Winters smoothing against the data and save fit
      $R->send("tryCatch({ \
          x.hw = HoltWinters(x, alpha=NULL, beta=NULL, gamma=FALSE)}, \
          error = function(cond) {message(\"Optimization Error\")} )");

      if ($TELEMETRY == 1)
      {
        $R->send("print(x.hw)");
        $output = $R->read();
        DBG("<- $output");
      }

      #get the smoothed data
      $R->send("print(x.hw\$fitted)");
      $output = $R->read();
      DBG("\n\n<- $output");

      #convert the smoothed data (all in 1 giant string) into an array of
      #strings, each looking like "12 11825.73 11825.73 -304.80"
      @lines = split(/\n/, $output);

      #the first 5 lines are header info
      $index = 5;

      #extract the xhat value (2nd column) and update the forecast cube
      #appropriately.
      $leadin = 2;
      foreach $time (@times)
      {

        #there's no forecast for the first two timeperiods with ES-Double
        if ($leadin)
        {
          $leadin--;
          $query = "UPDATE $dsSchema.$fcCube SET forecast=NULL \
              WHERE product=$product AND geography=$geography AND time = $time";
          $status = $db->do($query);
          forecast_db_err($db, $status, $query);
          next;
        }

        #extract the forecast value from the output and update database
        if ($lines[$index] =~ m/^\s*\d+\s+(\S+)\s+\S+/)
        {
          $query = "UPDATE $dsSchema.$fcCube SET forecast=$1 \
              WHERE product=$product AND geography=$geography AND time = $time";
          $status = $db->do($query);
          forecast_db_err($db, $status, $query);
        }

        $index++;
      }

      #predict the measure value for the specified number of time periods
      #in the future
      $R->send("fc <- predict(x.hw, n.ahead=$predict)");
      $R->send("print(fc)");
      $output = $R->read();
      DBG("<- $output");

      #convert the predictions (all in 1 giant string) into an array of
      #strings, each looking like " [1,] 125516.88"
      @lines = split(/\n/, $output);

      #the first 5 lines are header info
      splice(@lines, 0, 5);

      #extract the predicted value (2nd column)
      undef($forecast);
      foreach $line (@lines)
      {

        #extract the forecast value from the output and build up the forecast
        #value string
        if ($line =~ m/^\s*\[\d+\,\]\s+(\S+)/)
        {
          $forecast .= "$1,";
        }
        else
        {
          $forecast .= "0,";
        }
      }

      #chop the trailing comma off the forecast value string
      chop($forecast);

      #get the alpha value for the fit and add to forecast details string
      $R->send("print(x.hw\$alpha)");
      $output = $R->read();
      $output =~ m/\s+alpha\s+([\d\.]+)/s;
      $details .= ",alpha=$1";

      #get the beta value for the fit and add to forecast details string
      $R->send("print(x.hw\$beta)");
      $output = $R->read();
      $output =~ m/\s+beta\s+([\d\.]+)/s;
      $details .= ",beta=$1";

      #update the forecast for the future periods
      $q_forecast = $db->quote($forecast);
      $q_details = $db->quote($details);
      $query = "UPDATE $dsSchema.$fcMeta SET forecasts=$q_forecast, details=$q_details \
          WHERE product=$product AND geography=$geography";
      $status = $db->do($query);
      forecast_db_err($db, $status, $query);

      #store the forecasted values in the forecast data cube
      $query = "SELECT endDate FROM $dsSchema.$fcCube \
          WHERE product=$product AND geography=$geography AND source='forecast' \
          ORDER BY endDate";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      forecast_db_err($db, $status, $query);
      @forecastVals = split(',', $forecast);
      $previousVal = 0;
      foreach $val (@forecastVals)
      {

        #see if the forecast has stopped being useful (the same value twice in
        #sequence, if it isn't zero)
        if (($previousVal == $val) && ($previousVal > 0))
        {
          $query = "UPDATE $dsSchema.$fcMeta SET commentary='SES-Truncated' \
              WHERE product=$product AND geography=$geography";
          $status = $db->do($query);
          forecast_db_err($db, $status, $query);
          last;
        }
        $previousVal = $val;

        ($endDate) = $dbOutput->fetchrow_array;
        $query = "UPDATE $dsSchema.$fcCube SET forecast=$val \
            WHERE product=$product AND geography=$geography AND endDate='$endDate'";
        $status = $db->do($query);
        forecast_db_err($db, $status, $query);
      }
    }
  }
  $R->send("q('no')");
}



#-------------------------------------------------------------------------------
#
# Holt-Winters seasonality - automatically determined alpha, beta, and gamma
# NB: We need at least 2 * $frequency time periods for the calculation
#

sub holt_winters
{

  my ($fcID, $dsID, $predict, $db, $type, $frequency) =  @_;


  DBG("\n\n================= HOLT-WINTERS ===================");

  #construct schema and cube names
  $dsSchema = "datasource_" . $dsID;
  $fcCube = "__fcastcube_" . $fcID;
  $fcMeta = "__fcast_" . $fcID;

  #determine if we're doing additive or multiplicative seasonality
  if ($type =~ m/Additive/)
  {
    $subtype = "add";
  }
  else
  {
    $subtype = "mult";
  }

  #connect to the R engine
  $R = Statistics::R->new();
  $R->startR or die("$$\n");

  #get the products, geographies, and times for the forecast
  $query = "SELECT timeperiods, products, geographies FROM analytics.forecasts \
      WHERE ID=$fcID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  forecast_db_err($db, $status, $query);

  ($timeIDstring, $productIDstring, $geoIDstring) = $dbOutput->fetchrow_array;

  #convert the ID strings into arrays
  @times = split(/,/, $timeIDstring);
  @products = split(/,/, $productIDstring);
  @geographies = split(/,/, $geoIDstring);

  #foreach combination of product and geography
  foreach $product (@products)
  {
    foreach $geography (@geographies)
    {
      DBG("\n\n----------- Product: $product Geography: $geography-----------");

      #add the forecast type to the forecast details
      $details = "type=HoltWinters-Seasonal-$subtype,period=$periodType";
      $zeroValuesPresent = 0;

      #select all time periods (with measures) for the product/geo combination
      #and store them in a hash
      $query = "SELECT time, measure FROM $dsSchema.$fcCube \
          WHERE product = $product AND geography = $geography AND source='base' \
          ORDER BY FIELD(time, $timeIDstring)";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      forecast_db_err($db, $status, $query);
      undef(@values);
      while (($time, $value) = $dbOutput->fetchrow_array)
      {
        push(@values, $value);

        #detect zero/NA values for H-W mult
        if (($value == 0) || (!defined($value)))
        {
          $zeroValuesPresent = 1;
        }
      }

      #if we're doing H-W mult, zeroes/NA aren't allowed
      if ($zeroValuesPresent == 1)
      {
        $query = "UPDATE $dsSchema.$fcMeta SET commentary='HWmult-zeroes' \
            WHERE product=$product AND geography=$geography";
        $status = $db->do($query);
        forecast_db_err($db, $status, $query);
        next;
      }

      #build an R-style value vector
      $vectorValues = "x = c(";
      foreach $value (@values)
      {
        #a NULL value in the database is a 0 for H-W
        if (!defined($value))
        {
          $vectorValues .= "0,";
          next;
        }

        $vectorValues .= "$value,";
      }
      chop($vectorValues);
      $vectorValues .= ")";

      #send the vector to the forecasting engine
      DBG("-> $vectorValues");
      $R->send($vectorValues);

      #convert the vector into a time series object
      $cmd = "x = ts(x, frequency=$frequency)";
      $R->send($cmd);

      #run seasonal Holt-Winters smoothing against the data and save fit
      $cmd = "tryCatch({ \
          x.hw = HoltWinters(x, seasonal = \"$subtype\")}, \
          error = function(cond) {message(\"Optimization Error\")} )";
      $R->send($cmd);

      if ($TELEMETRY == 1)
      {
        $R->send("print(x.hw)");
        $output = $R->read();
        DBG("<- $output");
      }

      #get the smoothed data
      $cmd = "print(x.hw\$fitted)";
      $R->send($cmd);

      $output = $R->read();
      DBG("\n\n<- $output");

      @lines = split(/\n/, $output);

      #the first 5 lines are header info
      splice(@lines, 0, 5);

      #extract the xhat value (2nd column) and update the forecast cube
      #appropriately.
      $leadin = $frequency;
      $index = 0;
      foreach $time (@times)
      {

        #there's no forecast for the first $frequency timeperiods with
        #Holt-Winters seasonal calculations
        if ($leadin)
        {
          $leadin--;
          $query = "UPDATE $dsSchema.$fcCube SET forecast=NULL \
              WHERE product=$product AND geography=$geography AND time = $time";
          $status = $db->do($query);
          forecast_db_err($db, $status, $query);
          next;
        }

        #extract the forecast value from the output and update database
        if ($lines[$index] =~ m/^[\d\.]+\s+(\S+)\s+.*/)
        {
          $query = "UPDATE $dsSchema.$fcCube SET forecast=$1 \
              WHERE product=$product AND geography=$geography AND time = $time";
          $status = $db->do($query);
          forecast_db_err($db, $status, $query);
        }
        $index++;
      }

      #predict the measure value for the specified number of time periods
      #in the future
      $R->send("fc <- predict(x.hw, n.ahead=$predict)");
      $R->send("cat(formatC(fc, digits=2, format=\"f\"))");
      $output = $R->read();
      DBG("<- $output");

      #convert the predictions string from space-delimited to csv
      $output =~ tr/ /\,/;
      $forecast = $output;

      #get the alpha value for the fit and add to forecast details string
      $R->send("cat(x.hw\$alpha)");
      $output = $R->read();
      $details .= ",alpha=$output";

      #get the beta value for the fit and add to forecast details string
      $R->send("cat(x.hw\$beta)");
      $output = $R->read();
      $details .= ",beta=$output";

      #get the gamma value for the fit and add to forecast details string
      $R->send("cat(x.hw\$gamma)");
      $output = $R->read();
      $details .= ",gamma=$output";

      #update the forecast for the future periods
      $q_forecast = $db->quote($forecast);
      $q_details = $db->quote($details);
      $query = "UPDATE $dsSchema.$fcMeta SET forecasts=$q_forecast, details=$q_details \
          WHERE product=$product AND geography=$geography";
      $status = $db->do($query);
      forecast_db_err($db, $status, $query);

      #store the forecasted values in the forecast data cube
      $query = "SELECT endDate FROM $dsSchema.$fcCube \
          WHERE product=$product AND geography=$geography AND source='forecast' \
          ORDER BY endDate";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      forecast_db_err($db, $status, $query);
      @forecastVals = split(',', $forecast);
      foreach $val (@forecastVals)
      {
        ($endDate) = $dbOutput->fetchrow_array;
        $query = "UPDATE $dsSchema.$fcCube SET forecast=$val \
            WHERE product=$product AND geography=$geography AND endDate='$endDate'";
        $status = $db->do($query);
        forecast_db_err($db, $status, $query);
      }
    }
  }
  $R->send("q('no')");
}



#-------------------------------------------------------------------------------
#
# ARIMA (AutoRegression with Integral Moving Averages)
# NB: This is the latest-gen "auto-ARIMA" that, for better or for worse,
#     makes a best-effort to determine the appropriate model using from
#     0 to 5 autoregressors and from 0-5 moving averages.
#

sub arima
{

  my ($fcID, $dsID, $predict, $db, $frequency) =  @_;


  DBG("\n\n================= ARIMA ===================");

  #construct schema and cube names
  $dsSchema = "datasource_" . $dsID;
  $fcCube = "__fcastcube_" . $fcID;
  $fcMeta = "__fcast_" . $fcID;

  #connect to the R engine
  $R = Statistics::R->new();
  $R->startR or die("$$\n");

  #load up the forecast library
  $R->send("library(forecast)");

  #get the products, geographies, and times for the forecast
  $query = "SELECT timeperiods, products, geographies FROM analytics.forecasts \
      WHERE ID=$fcID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  forecast_db_err($db, $status, $query);

  ($timeIDstring, $productIDstring, $geoIDstring) = $dbOutput->fetchrow_array;

  #convert the ID strings into arrays
  @times = split(/,/, $timeIDstring);
  @products = split(/,/, $productIDstring);
  @geographies = split(/,/, $geoIDstring);

  #foreach combination of product and geography
  foreach $product (@products)
  {
    foreach $geography (@geographies)
    {
      DBG("\n\n----------- Product: $product Geography: $geography-----------");

      #add the forecast type to the forecast details
      $details = "type=ARIMA-Auto,period=$periodType";

      #select all time periods (with measures) for the product/geo combination
      #and store them in a hash
      $query = "SELECT time, measure FROM $dsSchema.$fcCube \
          WHERE product = $product AND geography = $geography AND source='base' \
          ORDER BY FIELD(time, $timeIDstring)";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      forecast_db_err($db, $status, $query);
      undef(@values);
      while (($time, $value) = $dbOutput->fetchrow_array)
      {
        push(@values, $value);
      }

      #build an R-style value vector
      $vectorValues = "x = c(";
      foreach $value (@values)
      {
        #a NULL value in the database is a 0
        if (!defined($value))
        {
          $vectorValues .= "0,";
          next;
        }

        $vectorValues .= "$value,";
      }
      chop($vectorValues);
      $vectorValues .= ")";

      #send the vector to the forecasting engine
      DBG("-> $vectorValues");
      $R->send($vectorValues);

      #convert the vector into a time series object
      $R->send("x = ts(x, frequency=$frequency)");

      #run the auto-ARIMA procedure against the data and save the fit
      $R->send("x.ar = auto.arima(x)");
      $output = $R->read();
      DBG("<- $output");

      #extract the Kalman Filter model
      $order = "(";
      $seasonal = "(";

      $R->send("cat(x.ar\$arma[1])"); #autoregressors
      $output = $R->read();
      DBG("<- $output");
      $order .= "$output,";

      $R->send("cat(x.ar\$arma[6])"); #differences
      $output = $R->read();
      DBG("<- $output");
      $order .= "$output,";

      $R->send("cat(x.ar\$arma[2])"); #moving averages
      $output = $R->read();
      DBG("<- $output");
      $order .= "$output";

      $R->send("cat(x.ar\$arma[3])"); #seasonal autoregressors
      $output = $R->read();
      DBG("<- $output");
      $seasonal .= "$output,";

      $R->send("cat(x.ar\$arma[7])"); #seasonal differences
      $output = $R->read();
      DBG("<- $output");
      $seasonal .= "$output,";

      $R->send("cat(x.ar\$arma[4])"); #seasonal moving avergaes
      $output = $R->read();
      DBG("<- $output");
      $seasonal .= "$output";

      $order .= ")";
      $seasonal .= ")";

      $details .= ",order=$order,seasonal=$seasonal";

      #get the fitted data for the observed values
      $R->send("cat(formatC(fitted(x.ar), digits=2, format=\"f\"))");
      $output = $R->read();
      DBG("<- $output");

      #convert the fitted data into an array
      @lines = split(/ /, $output);

      #update the forecast cube with the fitted data
      $index = 0;
      foreach $time (@times)
      {

        #extract the forecast value from the output and update database
        $forecast = $lines[$index];
        $query = "UPDATE $dsSchema.$fcCube SET forecast=$forecast \
            WHERE product=$product AND geography=$geography AND time = $time";
        $db->do($query);

        $index++;
      }

      #predict the measure value for the specified number of time periods
      #in the future
      $cmd = "tryCatch({ \
          fc <- predict(x.ar, n.ahead=$predict)}, \
          error = function(cond) {message(\"Unable to determine seasonality\")} )";
      $R->send($cmd);
      $R->send("cat(formatC(fc\$pred, digits=2, format=\"f\"))");
      $output = $R->read();
      DBG("<- $output");

      #convert predictions into CSV string
      $forecast = $output;
      $forecast =~ tr/ /,/;

      #update the forecast for the future periods
      $q_forecast = $db->quote($forecast);
      $q_details = $db->quote($details);
      $query = "UPDATE $dsSchema.$fcMeta SET forecasts=$q_forecast, details=$q_details \
          WHERE product=$product AND geography=$geography";
      $db->do($query);

      #store the forecasted values in the forecast data cube
      $query = "SELECT endDate FROM $dsSchema.$fcCube \
          WHERE product=$product AND geography=$geography AND source='forecast' \
          ORDER BY endDate";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      forecast_db_err($db, $status, $query);
      @forecastVals = split(',', $forecast);
      foreach $val (@forecastVals)
      {
        ($endDate) = $dbOutput->fetchrow_array;
        $query = "UPDATE $dsSchema.$fcCube \
            SET forecast=$val \
            WHERE product=$product AND geography=$geography AND endDate='$endDate'";
        $status = $db->do($query);
        forecast_db_err($db, $status, $query);
      }
    }
  }

  $R->send("q('no')");
}



#-------------------------------------------------------------------------------
#
# "Classic" seasonal decomposition
# NB: We need at least 2 * $frequency time periods for the calculation
#

sub seasonal_classic
{

  my ($fcID, $dsID, $predict, $db, $type, $frequency) =  @_;


  DBG("\n\n================= SEASONAL - CLASSIC ===================");

  #construct schema and cube names
  $dsSchema = "datasource_" . $dsID;
  $fcCube = "__fcastcube_" . $fcID;
  $fcMeta = "__fcast_" . $fcID;

  #determine if we're doing additive or multiplicative seasonality
  if ($type =~ m/Additive/)
  {
    $subtype = "add"
  }
  else
  {
    $subtype = "mult";
  }

  #connect to the R engine
  $R = Statistics::R->new();
  $R->startR or die("$$\n");

  #get the products, geographies, and times for the forecast
  $query = "SELECT timeperiods, products, geographies FROM analytics.forecasts \
      WHERE ID=$fcID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  forecast_db_err($db, $status, $query);

  ($timeIDstring, $productIDstring, $geoIDstring) = $dbOutput->fetchrow_array;

  #convert the ID strings into arrays
  @times = split(/,/, $timeIDstring);
  @products = split(/,/, $productIDstring);
  @geographies = split(/,/, $geoIDstring);

  #foreach combination of product and geography
  foreach $product (@products)
  {
    foreach $geography (@geographies)
    {
      DBG("\n\n----------- Product: $product Geography: $geography-----------");

      #add the forecast type to the forecast details
      $details = "type=Seasonal-$subtype,period=$periodType";

      #select all time periods (with measures) for the product/geo combination
      #and store them in a hash
      $query = "SELECT time, measure FROM $dsSchema.$fcCube \
          WHERE product = $product AND geography = $geography AND source='base' \
          ORDER BY FIELD(time, $timeIDstring)";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      forecast_db_err($db, $status, $query);
      undef(@values);
      while (($time, $value) = $dbOutput->fetchrow_array)
      {
        push(@values, $value);
      }

      #build an R-style value vector
      $vectorValues = "y = c(";
      foreach $value (@values)
      {
        #a NULL value in the database is a 0 for simple smoothing purposes
        if (!defined($value))
        {
          $vectorValues .= "0,";
          next;
        }

        $vectorValues .= "$value,";
      }
      chop($vectorValues);
      $vectorValues .= ")";

      #send the vector to the forecasting engine
      DBG("-> $vectorValues");
      $R->send($vectorValues);

      #convert the vector into a time series object
      $R->send("y = ts(y, frequency=$frequency)");

      #run classic seasonal decomposition against the data and save fit
      $R->send("y.l = decompose(y, type=\"$subtype\")");

      #get the seasonal data
      $R->send("cat(formatC(y.l\$seasonal, digits=2, format=\"f\"))");
      $output = $R->read();
      DBG("<- $output");

      #convert the seasonal data into an array of strings
      @seasonallines = split(/ /, $output);

      #get the trend coefficients from the raw trend data
      $cmd = "x = c(";
      $index = 1 + ($frequency / 2);
      foreach $time (@times)
      {
        $cmd .= "$index,";
        $index++;
      }
      chop($cmd);
      $cmd .= ")";
      DBG("-> $cmd");
      $R->send($cmd);

      #run appropriate regression model against data, and save fit
      $R->send("y.fit = lm(y.l\$trend ~ x)");

      #get the coefficients from the linear fit (format is "intercept slope")
      $R->send("cat(formatC(y.fit\$coefficients, digits=2, format=\"f\"))");
      $output = $R->read();
      DBG("<- $output");
      $output =~ m/^([\d\.\-]+) ([\d\.\-]+)/;
      $intercept = $1;
      $slope = $2;

      #calculate the predicted values for the observed data (trend + season)
      #and update the forecast cube appropriately
      $index = 0;
      foreach $time (@times)
      {

        #extract the forecast value from the output and update database
        $trendvalue = $intercept + (($index - 1) * $slope);
        if ($subtype eq "add")
        {
          $forecast = $seasonallines[$index] + $trendvalue;
        }
        else
        {
          $forecast = $seasonallines[$index] * $trendvalue;
        }

        if ($forecast eq "nan")
        {
          $forecast = "NULL";
        }

        $query = "UPDATE $dsSchema.$fcCube SET forecast=$forecast \
            WHERE product=$product AND geography=$geography AND time = $time";
        $status = $db->do($query);
        forecast_db_err($db, $status, $query);

        $index++;
      }

      #predict the measure value for the specified number of time periods
      #in the future

      #figure out where in the seasonal index array we should be, based on
      #how many time periods were processed for observed values
      $index = scalar(@times) % $frequency;

      $count = scalar(@times);

      #predict the requested number of time periods
      undef($forecast);
      for($i = 0; $i < $predict; $i++)
      {
        if ($subtype eq "add")
        {
          $value = $intercept + ($slope * $count) + $seasonallines[$index];
        }
        else
        {
          $value = $intercept + ($slope * $count) * $seasonallines[$index];
        }

        $forecast .= "$value,";

        $count++;
        $index++;
        $index = $index % $frequency;
      }

      chop($forecast);

      #put the slope, intercept, and seasonality data into the details string
      $details = "slope=$slope,intercept=$intercept,";

      #update the forecast for the future periods
      $q_forecast = $db->quote($forecast);
      $q_details = $db->quote($details);
      $query = "UPDATE $dsSchema.$fcMeta SET forecasts=$q_forecast, details=$q_details \
          WHERE product=$product AND geography=$geography";
      $status = $db->do($query);
      forecast_db_err($db, $status, $query);

      #store the forecasted values in the forecast data cube
      $query = "SELECT endDate FROM $dsSchema.$fcCube \
          WHERE product=$product AND geography=$geography AND source='forecast' \
          ORDER BY endDate";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      forecast_db_err($db, $status, $query);
      @forecastVals = split(',', $forecast);
      foreach $val (@forecastVals)
      {
        if ($val eq "nan")
        {
          $val = "NULL";
        }

        ($endDate) = $dbOutput->fetchrow_array;
        $query = "UPDATE $dsSchema.$fcCube SET forecast=$val \
            WHERE product=$product AND geography=$geography AND endDate='$endDate'";
        $status = $db->do($query);
        forecast_db_err($db, $status, $query);
      }
    }
  }

  $R->send("q('no')");
}



#-------------------------------------------------------------------------------
#
# Seasonal decomposition by the "loess" method (Cleveland et al)
#

sub seasonal_loess
{
  my ($fcID, $dsID, $predict, $db, $type, $frequency) =  @_;


  DBG("\n\n================= SEASONAL - LOESS ===================");

  #construct schema and cube names
  $dsSchema = "datasource_" . $dsID;
  $fcCube = "__fcastcube_" . $fcID;
  $fcMeta = "__fcast_" . $fcID;

  #connect to the R engine
  $R = Statistics::R->new();
  $R->startR or die("$$\n");

  #get the products, geographies, and times for the forecast
  $query = "SELECT timeperiods, products, geographies FROM analytics.forecasts \
      WHERE ID=$fcID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  forecast_db_err($db, $status, $query);

  ($timeIDstring, $productIDstring, $geoIDstring) = $dbOutput->fetchrow_array;

  #convert the ID strings into arrays
  @times = split(/,/, $timeIDstring);
  @products = split(/,/, $productIDstring);
  @geographies = split(/,/, $geoIDstring);

  #foreach combination of product and geography
  foreach $product (@products)
  {
    foreach $geography (@geographies)
    {
      DBG("\n\n----------- Product: $product Geography: $geography-----------");

      #add the forecast type to the forecast details
      $details = "type=Seasonal-loess,period=$periodType";

      #select all time periods (with measures) for the product/geo combination
      #and store them in a hash
      $query = "SELECT time, measure FROM $dsSchema.$fcCube \
          WHERE product = $product AND geography = $geography AND source='base' \
          ORDER BY FIELD(time, $timeIDstring)";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      undef(@values);
      while (($time, $value) = $dbOutput->fetchrow_array)
      {
        push(@values, $value);
      }

      #XXX make sure there's more than one frequency cycle worth of data

      #build an R-style value vector
      $vectorValues = "y = c(";
      foreach $value (@values)
      {
        #a NULL value in the database is a 0 for simple smoothing purposes
        if (!defined($value))
        {
          $vectorValues .= "0,";
          next;
        }

        $vectorValues .= "$value,";
      }
      chop($vectorValues);
      $vectorValues .= ")";

      #send the vector to the forecasting engine
      DBG("-> $vectorValues");
      $R->send($vectorValues);

      #convert the vector into a time series object
      $R->send("y = ts(y, frequency=$frequency)");

      #run classic seasonal decomposition against the data and save fit
      $R->send("y.l = stl(y, s.window=\"per\")");

      #get the seasonal data
      $R->send("cat(formatC(y.l\$time.series[,1], digits=2, format=\"f\"))");
      $output = $R->read();
      DBG("<- $output");

      #convert the seasonal data into an array of strings
      @seasonallines = split(/ /, $output);

      #get the trend data
      $R->send("cat(formatC(y.l\$time.series[,2], digits=2, format=\"f\"))");
      $output = $R->read();
      DBG("<- $output");

      #convert the trend data into an array of strings
      @trendlines = split(/ /, $output);

      #calculate the predicted values for the observed data (trend + season)
      #and update the forecast cube appropriately
      #BIG ASSUMPTION: the @times array is in chrono order
      $index = 0;
      foreach $time (@times)
      {

        #extract the forecast value from the output and update database
        $forecast = $seasonallines[$index] + $trendlines[$index];
        $query = "UPDATE $dsSchema.$fcCube SET forecast=$forecast \
            WHERE product=$product AND geography=$geography AND time = $time";
        $db->do($query);

        $index++;
      }

      #predict the measure value for the specified number of time periods
      #in the future

      #first, get the slope and intercept of the trend line (we need to
      #build up a simple x vector of sequential values)
      $cmd = "x = c(";
      $index = 1;
      foreach $time (@times)
      {
        $cmd .= "$index,";
        $index++;
      }
      chop($cmd);
      $cmd .= ")";
      $R->send($cmd);

      #run appropriate regression model against data, and save fit
      $R->send("y.fit = lm(y ~ x)");

      #get the coefficients from the linear fit (format is "intercept slope")
      $R->send("cat(formatC(y.fit\$coefficients, digits=2, format=\"f\"))");
      $output = $R->read();
      DBG("<- $output");
      $output =~ m/^([\d\.\-]+) ([\d\.\-]+)/;
      $intercept = $1;
      $slope = $2;

      #we already have the loess seasonal values in the seasonallines array,
      #so let's do a bunch of pre-forecast work

      #figure out where in the seasonal index array we should be, based on
      #how many time periods were processed for observed values
      $index = scalar(@times) % $frequency;

      $count = scalar(@times);

      #predict the requested number of time periods
      undef($forecast);
      for($i = 0; $i < $predict; $i++)
      {
        $value = $intercept + ($slope * $count) + $seasonallines[$index];
        $forecast .= "$value,";

        $count++;
        $index++;
        $index = $index % $frequency;
      }

      chop($forecast);

      #put the slope, intercept, and seasonality data into the details string
      $details = "slope=$slope,intercept=$intercept,";

      #update the forecast for the future periods
      $q_forecast = $db->quote($forecast);
      $q_details = $db->quote($details);
      $query = "UPDATE $dsSchema.$fcMeta SET forecasts=$q_forecast, details=$q_details \
          WHERE product=$product AND geography=$geography";
      $status = $db->do($query);
      forecast_db_err($db, $status, $query);

      #store the forecasted values in the forecast data cube
      $query = "SELECT endDate FROM $dsSchema.$fcCube \
          WHERE product=$product AND geography=$geography AND source='forecast' \
          ORDER BY endDate";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      forecast_db_err($db, $status, $query);
      @forecastVals = split(',', $forecast);
      foreach $val (@forecastVals)
      {
        ($endDate) = $dbOutput->fetchrow_array;
        $query = "UPDATE $dsSchema.$fcCube SET forecast=$val \
            WHERE product=$product AND geography=$geography AND endDate='$endDate'";
        $status = $db->do($query);
        forecast_db_err($db, $status, $query);
      }
    }
  }

  $R->send("q('no')");
}



#-------------------------------------------------------------------------------
#
# Trend line fitting models: linear, quadratic, and cubic
#

sub trend
{
  my ($fcID, $dsID, $predict, $db, $type) =  @_;


  DBG("\n\n================= TREND - $type ===================");

  #construct schema and cube names
  $dsSchema = "datasource_" . $dsID;
  $fcCube = "__fcastcube_" . $fcID;
  $fcMeta = "__fcast_" . $fcID;

  #determine what order of trend we're fitting
  if ($type =~ m/Linear/)
  {
    $subtype = "linear";
  }
  elsif ($type =~ m/Quadratic/)
  {
    $subtype = "quadratic";
  }
  else
  {
    $subtype = "cubic";
  }

  #connect to the R engine
  $R = Statistics::R->new();
  $R->startR or die("$$\n");

  #get the products, geographies, and times for the forecast
  $query = "SELECT timeperiods, products, geographies \
      FROM analytics.forecasts WHERE ID=$fcID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  forecast_db_err($db, $status, $query);

  ($timeIDstring, $productIDstring, $geoIDstring) = $dbOutput->fetchrow_array;

  #convert the ID strings into arrays
  @times = split(/,/, $timeIDstring);
  @products = split(/,/, $productIDstring);
  @geographies = split(/,/, $geoIDstring);

  #foreach combination of product and geography
  foreach $product (@products)
  {
    foreach $geography (@geographies)
    {
      DBG("\n\n----------- Product: $product Geography: $geography-----------");

      #add the forecast type to the forecast details
      $details = "type=Trend-$subtype,period=$periodType";

      #select all time periods (with measures) for the product/geo combination
      #and store them in a hash
      $query = "SELECT time, measure FROM $dsSchema.$fcCube \
          WHERE product = $product AND geography = $geography AND source='base' \
          ORDER BY FIELD(time, $timeIDstring)";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      undef(@values);
      while (($time, $value) = $dbOutput->fetchrow_array)
      {
        push(@values, $value);
      }

      #build an R-style value vector and index vector
      $vectorValues = "y = c(";
      $vectorIndex = "x = c(";
      $index = 0;
      foreach $value (@values)
      {
        $index++;
        $vectorIndex .= "$index,";

        #a NULL value in the database is a 0 for simple smoothing purposes
        if (!defined($value))
        {
          $vectorValues .= "0,";
          next;
        }

        $vectorValues .= "$value,";
      }
      chop($vectorValues);
      $vectorValues .= ")";
      chop($vectorIndex);
      $vectorIndex .= ")";

      #send the vector to the forecasting engine
      $R->send($vectorValues);
      DBG("-> $vectorValues");
      $R->send($vectorIndex);
      DBG("-> $vectorIndex");

      #run appropriate regression model against data, and save fit
      if ($subtype eq "linear")
      {
        $R->send("y.fit = lm(y ~ x)");
      }
      elsif ($subtype eq "quadratic")
      {
        $R->send("y.fit = lm(y ~ poly(x, 2, raw=TRUE))");
      }
      elsif ($subtype eq "cubic")
      {
        $R->send("y.fit = lm(y ~ poly(x, 3, raw=TRUE))");
      }
      elsif ($subtype eq "logistic")
      {
        $R->send("y.fit = lm(y ~ log(x)");
      }

      #get the fitted values for the observed values
      $R->send("cat(formatC(y.fit\$fitted.values, digits=2, format=\"f\"))");
      $output = $R->read();
      DBG("<- $output");

      #convert the fitted values into an array
      @lines = split(/ /, $output);

      #extract the fitted value and update the forecast cube appropriately
      #BIG ASSUMPTION: the @times array is in chrono order
      $index = 0;
      foreach $time (@times)
      {

        #extract the forecast value from the output and update database
        $query = "UPDATE $dsSchema.$fcCube SET forecast=$lines[$index] \
            WHERE product=$product AND geography=$geography AND time = $time";
        $db->do($query);

        $index++;
      }

      #get the coefficients for the fit and add to forecast details string
      $R->send("cat(formatC(y.fit\$coefficients, digits=2, format=\"f\"))");
      $output = $R->read();
      DBG("<- $output");
      @lines = split(/ /, $output);
      $intercept = $lines[0];
      if ($subtype eq "linear")
      {
        $slope = $lines[1];
        $details .= ",intercept=$lines[0],slope=$lines[1]";
      }
      elsif ($subtype eq "quadratic")
      {
        $slope = $lines[1];
        $beta = $lines[2];
        $details .= ",intercept=$lines[0],slope=$lines[1],beta=$lines[2]";
      }
      elsif ($subtype eq "cubic")
      {
        $slope = $lines[1];
        $beta = $lines[2];
        $gamma = $lines[3];
        $details .= ",intercept=$lines[0],slope=$lines[1],beta=$lines[2],gamma=$lines[3]";
      }

      #predict the measure value for the specified number of time periods
      #in the future
      undef($forecast);
      $index = scalar(@times) + 1;
      while ($index <=  scalar(@times) + $predict)
      {
        if ($subtype eq "linear")
        {
          $value = $intercept + ($index * $slope);
        }
        elsif ($subtype eq "quadratic")
        {
          $value = $intercept + ($index * $slope);
          $value = $value + (($index * $index) * $beta);
        }
        elsif ($subtype eq "cubic")
        {
          $value = $intercept + ($index * $slope);
          $value = $value + (($index * $index) * $beta);
          $value = $value + (($index * $index * $index) * $gamma);
        }

        $forecast .= "$value,";
        $index++;
      }
      chop($forecast);

      #update the forecast for the future periods
      $q_forecast = $db->quote($forecast);
      $q_details = $db->quote($details);
      $query = "UPDATE $dsSchema.$fcMeta \
          SET forecasts=$q_forecast, details=$q_details \
          WHERE product=$product AND geography=$geography";
      $status = $db->do($query);
      forecast_db_err($db, $status, $query);

      #store the forecasted values in the forecast data cube
      $query = "SELECT endDate FROM $dsSchema.$fcCube \
          WHERE product=$product AND geography=$geography AND source='forecast' \
          ORDER BY endDate";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      forecast_db_err($db, $status, $query);
      @forecastVals = split(',', $forecast);
      foreach $val (@forecastVals)
      {
        ($endDate) = $dbOutput->fetchrow_array;
        $query = "UPDATE $dsSchema.$fcCube SET forecast=$val \
            WHERE product=$product AND geography=$geography AND endDate='$endDate'";
        $status = $db->do($query);
        forecast_db_err($db, $status, $query);
      }
    }
  }

  $R->send("q('no')");
}



#-------------------------------------------------------------------------------
#
# Calculate standard statistical forecast error measures. Return the average
# MAPE for every geo/prod combination (used to automatically select best
# forecast measure if Automatic method is chosen).
#

sub calculate_errors
{
  my ($dsID, $fcID, $db) =  @_;


  #construct schema and cube names
  $dsSchema = "datasource_" . $dsID;
  $fcCube = "__fcastcube_" . $fcID;
  $fcMeta = "__fcast_" . $fcID;

  #get the products, geographies, and times for the forecast
  $query = "SELECT timeperiods, products, geographies FROM analytics.forecasts \
      WHERE ID=$fcID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;

  ($timeIDstring, $productIDstring, $geoIDstring) = $dbOutput->fetchrow_array;

  #convert the ID strings into arrays
  @times = split(/,/, $timeIDstring);
  @products = split(/,/, $productIDstring);
  @geographies = split(/,/, $geoIDstring);

  $avgMAPE = 0;
  $numProdGeoCombinations = 0;

  #run bulk calculations for goodness-of-fit statistics
  $query = "UPDATE $dsSchema.$fcCube SET error = forecast - measure, \
      mad = ABS(forecast - measure)";
  $status = $db->do($query);
  forecast_db_err($db, $status, $query);

  #foreach combination of product and geography
  foreach $product (@products)
  {
    foreach $geography (@geographies)
    {

      $numProdGeoCombinations++;

      #select all time periods (with values) for the product/geo combination
      #and store them in a hash
      $query = "SELECT time, measure, forecast, error, mad FROM $dsSchema.$fcCube \
          WHERE product = $product AND geography = $geography AND source='base'";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      forecast_db_err($db, $status, $query);

      $totalMAD = 0;
      $totalMSE = 0;
      $totalMAPE = 0;
      $RSFE = 0;
      $cumError = 0;
      $currentMAD = 0;
      $count = 0;
      while (($time, $actual, $forecast, $error, $mad) = $dbOutput->fetchrow_array)
      {

        #if the value for forecast is undefined (NULL), skip this row
        if (!defined($forecast))
        {
          next;
        }
        $count++;

        #update the Running Sum of Forecast Error (for tracking signals)
        $RSFE += $error;

        #calculate the Mean Absolute Deviation for the forecast value
        $totalMAD += $mad;

        #calculate the current running MAD (for tracking signals)
        $currentMAD = $totalMAD / $count;

        #calculate the Mean Squared Error for the forecast value
        $mse = $error * $error;
        $totalMSE += $mse;

        #NB: because we're dealing with demand data, there tends to be a lot of
        #   zero values that cause all kinds of issues with calculating MAPE.
        #   These are well-known issues:
        #       https://en.wikipedia.org/wiki/Mean_absolute_percentage_error
        #
        #XXX limit upper MAPE to 100%? Deal with zeroes in denominator by setting scaled error?

        #calculate the Mean Absolute Percentage Error for the forecast value
        if ($actual != 0)
        {
          $mape = abs($error / $actual);
        }
        else
        {
          $mape = 0;
        }
        $totalMAPE += $mape;

        #calculate the tracking signal for the current forecast
        if ($currentMAD != 0)
        {
          $trackingSignal = $RSFE / $currentMAD;
        }
        else
        {
          $trackingSignal = 0;
        }

        #write the error measures & tracking signals out to the forecast cube
        $query = "UPDATE $dsSchema.$fcCube SET mse=$mse, mape=$mape, rsfe=$RSFE, cummad=$currentMAD, trackingsignal=$trackingSignal \
            WHERE product=$product AND geography=$geography AND time = $time";
        $status = $db->do($query);
        forecast_db_err($db, $status, $query);
      }

      #calculate cumulative error measures
      $bias = $mad = $mse = $mape = "NULL";
      if ($count != 0)
      {
        $mse = $totalMSE / $count;
      }

      #calculate overall stats for the entire forecast
      if ($count > 0)
      {
        $query = "SELECT SUM(mape), SUM(error), SUM(mad) FROM $dsSchema.$fcCube \
            WHERE product=$product AND geography=$geography";
        $dbOutput = $db->prepare($query);
        $status = $dbOutput->execute;
        forecast_db_err($db, $status, $query);
        ($totalMAPE, $totalError, $totalMAD) = $dbOutput->fetchrow_array;

        $bias = $totalError / $count;
        $mape = $totalMAPE / $count;
        $mad = $totalMAD / $count;
      }

      #store MAPE for use in automated fitting - we compare all methods using it
      $avgMAPE = $avgMAPE + $mape;

      #write the overall error measures out to the forecast meta cube
      $query = "UPDATE $dsSchema.$fcMeta SET bias=$bias, mad=$mad, mse=$mse, mape=$mape \
          WHERE product=$product AND geography=$geography";
      $status = $db->do($query);
      forecast_db_err($db, $status, $query);
    }
  }

  if ($numProdGeoCombinations != 0)
  {
    $avgMAPE = $avgMAPE / $numProdGeoCombinations;
  }
  else
  {
    undef($numProdGeoCombinations);
  }
  return($avgMAPE);
}



#-------------------------------------------------------------------------------
#
# Create the table that'll hold the cube inside the data source schema. This
# should be called as the first step of creating a new cube.
#

sub forecast_create_tables
{
  my ($db, $dsSchema, $fcID) = @_;


  #determine if we're using an external table space for capacity/IO reasons
  $dsSchema =~ m/datasource_(\d+)/;
  $dsID = $1;
  $query = "SELECT userID FROM app.dataSources WHERE ID=$dsID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  forecast_db_err($db, $status, $query);
  ($ownerID) = $dbOutput->fetchrow_array;

  $orgID = KAPutil_get_user_org_id($db, $ownerID);

  $query = "SELECT dataStorage FROM app.orgs WHERE ID=$orgID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  forecast_db_err($db, $status, $query);
  ($dataDir) = $dbOutput->fetchrow_array;

  if (length($dataDir) > 2)
  {
    $dataDir .= "/mysql/";
    $dataDir = "DATA DIRECTORY='$dataDir'";
  }
  else
  {
    $dataDir = "";
  }

  $fcastWorkTable = "__fcast_" . $fcID;
  $fcastWorkCube = "__fcastcube_" . $fcID;

  #create the table for the forecast's data cube
  $query = "CREATE TABLE $dsSchema.$fcastWorkCube \
      ( \
        time INT UNSIGNED, \
        geography INT UNSIGNED, \
        product INT UNSIGNED, \
        endDate DATETIME, \
        source VARCHAR(24), \
        measure FLOAT DEFAULT NULL, \
        forecast FLOAT DEFAULT NULL, \
        error FLOAT DEFAULT NULL, \
        mad FLOAT DEFAULT NULL, \
        mse FLOAT DEFAULT NULL, \
        mape FLOAT DEFAULT NULL, \
        rsfe FLOAT DEFAULT NULL, \
        cummad FLOAT DEFAULT NULL, \
        trackingsignal FLOAT DEFAULT NULL, \
        promoDisp FLOAT DEFAULT 0, \
        promoFeat FLOAT DEFAULT 0, \
        promoDispFeat FLOAT DEFAULT 0, \
        promoPrice FLOAT DEFAULT 0, \
        PRIMARY KEY (geography, product, endDate) \
      ) $dataDir";
  $status = $db->do($query);
  forecast_db_err($db, $status, $query);

  #create the table for the forecast's meta info
  $query = "CREATE TABLE $dsSchema.$fcastWorkTable \
      ( \
        geography INT UNSIGNED, \
        product INT UNSIGNED, \
        forecasts VARCHAR(512) DEFAULT NULL, \
        bias FLOAT DEFAULT NULL, \
        mad FLOAT DEFAULT NULL, \
        mse FLOAT DEFAULT NULL, \
        mape FLOAT DEFAULT NULL, \
        details VARCHAR(512) DEFAULT NULL, \
        status VARCHAR(128) DEFAULT NULL,
        commentary VARCHAR(128) DEFAULT NULL,
        PRIMARY KEY (geography, product) \
      ) $dataDir";
  $status = $db->do($query);
  forecast_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------------
#
# Build the specified forecast
#

sub forecast_build
{
  my ($db, $dsID, $fcID, $userID) = @_;


  $dsSchema = "datasource_" . $dsID;

  $fcastWorkTable = "__fcast_" . $fcID;
  $fcastWorkCube = "__fcastcube_" . $fcID;
  $fcastTable = "_fcast_" . $fcID;
  $fcastCube = "_fcastcube_" . $fcID;

  #set our initial state in the jobs table
  KAPutil_job_store_status($db, $userID, $dsID, 0, "FORECAST", "Creating forecasts");

  $dsName = ds_id_to_name($db, $dsID);
  $q_dsName = $db->quote($dsName);
  $query = "UPDATE app.jobs SET dsName=$q_dsName, analyticsID=$fcID WHERE PID=$$";
  $status = $db->do($query);
  forecast_db_err($db, $status, $query);

  #if our DS is being used for another operation, set the model build to wait
  $ok = DSRutil_operation_ok($db, $dsID, 0, "FORECAST");
  while ($ok != 1)
  {
    forecast_set_status($db, "Waiting for another job to complete");
    sleep(60);
    $ok = DSRutil_operation_ok($db, $dsID, 0, "FORECAST");
  }

  #get our update ID that we're going to use for telemetry logging
  $query = "INSERT INTO analytics.forecast_telemetry (fcID, startTime) \
      VALUES ($fcID, NOW())";
  $status = $db->do($query);
  forecast_db_err($db, $status, $query);
  $fcUpdateID = $db->{q{mysql_insertid}};

  #add our startup telemetry
  $query = "UPDATE analytics.forecast_telemetry \
      SET telemetry = CONCAT(NOW(), ': Starting forecast\n') WHERE ID=$fcUpdateID";
  $status = $db->do($query);
  forecast_db_err($db, $status, $query);

  forecast_audit($db, $userID, $fcID, "Refreshed forecast|$fcUpdateID");

  #get the "last model refresh" timestamp
  #NB: We do this at the top of the refresh process so any changes made
  #    by users during the refresh will trigger yet another refresh
  #NB: We put the timestamp in place at the end of the refresh process, just
  #    in case something keeps the update process from completing
  $query = "SELECT NOW() FROM dataSources LIMIT 1";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  forecast_db_err($db, $status, $query);
  ($refreshTime) = $dbOutput->fetchrow_array;

  #determine measures containing promotional data
  forecast_telemetry($db, $fcUpdateID, "Scanning data source for promotional data");
  @measNames = ('Disp w/o Feat Units');
  $promoDispMeasID = forecast_measure_by_name($db, $dsSchema, @measNames);
  $promoDispColName = "measure_" . $promoDispMeasID;

  @measNames = ('Feat & Disp Units');
  $promoFeatDispMeasID = forecast_measure_by_name($db, $dsSchema, @measNames);
  $promoFeatDispColName = "measure_" . $promoFeatDispMeasID;

  @measNames = ('Feat w/o Disp Units');
  $promoFeatMeasID = forecast_measure_by_name($db, $dsSchema, @measNames);
  $promoFeatColName = "measure_" . $promoFeatMeasID;

  @measNames = ('Price Decr Units');
  $promoPriceMeasID = forecast_measure_by_name($db, $dsSchema, @measNames);
  $promoPriceColName = "measure_" . $promoPriceMeasID;

  #if there are any old working forecast tables, drop them
  forecast_telemetry($db, $fcUpdateID, "Clearing old working forecast data");
  forecast_set_status($db, "Clearing old working model data");
  KAPutil_db_delete_table($db, $dsSchema, $fcastWorkTable);
  KAPutil_db_delete_table($db, $dsSchema, $fcastWorkCube);

  #create the working tables where we're going to create/store the new forecast
  forecast_telemetry($db, $fcUpdateID, "Creating working area for new forecast");
  forecast_set_status($db, "Creating working tables");
  forecast_create_tables($db, $dsSchema, $fcID);

  #grab products, geographies, and other data we need to create the forecasts
  $query = "SELECT products, geographies, timeperiods, measureID, forecastType, periodType, futureperiods \
      FROM analytics.forecasts WHERE ID=$fcID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  forecast_db_err($db, $status, $query);
  ($prodIDStr, $geoIDStr, $timeIDStr, $measureID, $fcastType, $frequency, $predict) = $dbOutput->fetchrow_array;
  @productIDs = split(',', $prodIDStr);
  @geographyIDs = split(',', $geoIDStr);
  @timeIDs = split(',', $timeIDStr);

  #build a hash of all timeIDs and their end dates
  $query = "SELECT ID, endDate FROM $dsSchema.timeperiods";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  forecast_db_err($db, $status, $query);
  while (($timeID, $endDate) = $dbOutput->fetchrow_array)
  {
    $endDateHash{$timeID} = $endDate;
  }

  #build an in-order array of the end dates of the time periods to be forecast
  #NB: we use this repeatedly for each forecast combination, so it saves a lot
  #   of time to do it once up-front
  $mostRecentDate = $endDateHash{$timeIDs[-1]};
  $fcastPeriodWeeks = 52 / $frequency;
  for ($i = 0; $i < $predict; $i++)
  {

    #use the database to calculate the next time period's endDate
    $query = "SELECT DATE_ADD('$mostRecentDate', INTERVAL $fcastPeriodWeeks WEEK)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    forecast_db_err($db, $status, $query);
    ($mostRecentDate) = $dbOutput->fetchrow_array;
    push(@futureEndDates, $mostRecentDate);
  }

  #populate the forecast cube's fact data
  $measure = "measure_" . $measureID;
  foreach $product (@productIDs)
  {
    foreach $geography (@geographyIDs)
    {

      #insert entry in forecast metacube
      $query = "INSERT INTO $dsSchema.$fcastWorkTable (product, geography) \
          VALUES ($product, $geography)";
      $status = $db->do($query);
      forecast_db_err($db, $status, $query);

      foreach $time (@timeIDs)
      {

        #get the measure & promo values from the facts table
        $query = "SELECT $measure, $promoDispColName, $promoFeatDispColName, $promoFeatColName, $promoPriceColName \
            FROM $dsSchema.facts \
            WHERE productID = $product AND geographyID = $geography AND timeID = $time";
        $dbOutput = $db->prepare($query);
        $status = $dbOutput->execute;
        forecast_db_err($db, $status, $query);
        ($value, $promoDisp, $promoDispFeat, $promoFeat, $promoPrice) = $dbOutput->fetchrow_array;

        if ($promoDisp < 1)
        {
          $promoDisp = "NULL";
        }

        if ($promoDispFeat < 1)
        {
          $promoDispFeat = "NULL";
        }

        if ($promoFeat < 1)
        {
          $promoFeat = "NULL";
        }

        if ($promoPrice < 1)
        {
          $promoPrice = "NULL";
        }

        #insert the measure value and TGP IDs into the forecast cube
        if (!defined($value))
        {
          $value = "NULL";
        }
        $query = "INSERT INTO $dsSchema.$fcastWorkCube \
            (time, geography, product, endDate, source, measure, promoDisp, promoDispFeat, promoFeat, promoPrice) \
            VALUES ($time, $geography, $product, '$endDateHash{$time}', 'base', $value, $promoDisp, $promoDispFeat, $promoFeat, $promoPrice)";
        $status = $db->do($query);
        forecast_db_err($db, $status, $query);
      }

      #
      #create cube entries for the values we're going to forecast
      #

      foreach $endDate (@futureEndDates)
      {

        $query = "INSERT INTO $dsSchema.$fcastWorkCube \
            (geography, product, endDate, source) \
            VALUES ($geography, $product, '$endDate', 'forecast')";
        $status = $db->do($query);
        forecast_db_err($db, $status, $query);
      }
    }
  }

  if ($fcastType eq "Exponential Smoothing - Simple")
  {
    exponential_smoothing_simple($fcID, $dsID, $predict, $db);
  }

  if ($fcastType eq "Exponential Smoothing - Double")
  {
    exponential_smoothing_double($fcID, $dsID, $predict, $db);
  }

  if ($fcastType =~ m/^Holt-Winters Smoothing - Seasonal/)
  {
    holt_winters($fcID, $dsID, $predict, $db, $fcastType, $frequency);
  }

  if ($fcastType eq "Auto ARIMA")
  {
    arima($fcID, $dsID, $predict, $db, $frequency);
  }

  if (($fcastType eq "Seasonal Decomposition - Additive") ||
      ($fcastType eq "Seasonal Decomposition - Multiplicative"))
  {
    seasonal_classic($fcID, $dsID, $predict, $db, $fcastType, $frequency);
  }

  if ($fcastType eq "Seasonal Decomposition - Loess")
  {
    seasonal_loess($fcID, $dsID, $predict, $db, $fcastType, $frequency);
  }

  if ($fcastType =~ m/^Trend - /)
  {
    trend($fcID, $dsID, $predict, $db, $fcastType);
  }

  #XXX Make this more efficient later
  #run through each of the most effective algorithms, calculate the average
  #MAPE after each, and compare the MAPEs to choose the best.
  if ($fcastType =~ m/^Automatically Choose/)
  {
    holt_winters($fcID, $dsID, $predict, $db, $fcastType, $frequency);
    $holtMAPE = calculate_errors($dsID, $fcID, $db);
    $bestAlgorithm = "holt";
    $bestMAPE = $holtMAPE;

#    arima($fcID, $dsID, $predict, $db, $frequency);
#    $arimaMAPE = calculate_errors($dsID, $fcID, $db);
#    if ($arimaMAPE < $bestMAPE)
#    {
#      $bestAlgorithm = "arima";
#      $bestMAPE = $arimaMAPE;
#    }

    seasonal_classic($fcID, $dsID, $predict, $db, $fcastType, $frequency);
    $seasonalClassicMAPE = calculate_errors($dsID, $fcID, $db);
    if ($seasonalClassicMAPE < $bestMAPE)
    {
      $bestAlgorithm = "seasonalclassic";
      $bestMAPE = $seasonalClassicMAPE;
    }

    seasonal_loess($fcID, $dsID, $predict, $db, $fcastType, $frequency);
    $seasonalLoessMAPE = calculate_errors($dsID, $fcID, $db);
    if ($seasonalLoessMAPE < $bestMAPE)
    {
      $bestAlgorithm = "seasonalloess";
      $bestMAPE = $seasonalLoessMAPE;
    }

    trend($fcID, $dsID, $predict, $db, $fcastType);
    $trendMAPE = calculate_errors($dsID, $fcID, $db);
    if ($trendMAPE < $bestMAPE)
    {
      $bestAlgorithm = "trend";
      $bestMAPE = $trendMAPE;
    }

    #do a final run of the best-fit algorithm to store its results in the
    #fcast cube
    if ($bestAlgorithm eq "holt")
    {
      $fcastType = "Holt-Winters Smoothing - Seasonal (Auto)";
      holt_winters($fcID, $dsID, $predict, $db, $fcastType, $frequency);
    }
    elsif ($bestAlgorithm eq "arima")
    {
      $fcastType = "Auto ARIMA (Auto)";
      arima($fcID, $dsID, $predict, $db, $frequency);
    }
    elsif ($bestAlgorithm eq "seasonalclassic")
    {
      $fcastType = "Seasonal Decomposition - Multiplicative (Auto)";
      seasonal_classic($fcID, $dsID, $predict, $db, $fcastType, $frequency);
    }
    elsif ($bestAlgorithm eq "seasonalloess")
    {
      $fcastType = "Seasonal Decomposition - Loess (Auto)";
      seasonal_loess($fcID, $dsID, $predict, $db, $fcastType, $frequency);
    }
    elsif ($bestAlgorithm eq "trend")
    {
      $fcastType = "Trend - Cubic (Auto)";
      trend($fcID, $dsID, $predict, $db, $fcastType);
    }

    $q_type = $db->quote($fcastType);
    $query = "UPDATE analytics.forecasts SET forecastType=$q_type WHERE ID=$fcID";
    $db->do($query);
  }

  #run error calculations
  calculate_errors($dsID, $fcID, $db);

  #remove old forecast data tables, if they exist
  KAPutil_db_delete_table($db, $dsSchema, $fcastTable);
  KAPutil_db_delete_table($db, $dsSchema, $fcastCube);

  #put new forecast tables in place
  forecast_set_status($db, "Putting updated forecast data in place");
  forecast_telemetry($db, $fcUpdateID, "Putting updated forecast data in place");
  $query = "RENAME TABLE $dsSchema.$fcastWorkTable TO $dsSchema.$fcastTable";
  $status = $db->do($query);
  forecast_db_err($db, $status, $query);

  $query = "RENAME TABLE $dsSchema.$fcastWorkCube TO $dsSchema.$fcastCube";
  $status = $db->do($query);
  forecast_db_err($db, $status, $query);

  #remove this task from the jobs table
  DSRutil_clear_status($db);
}



#-------------------------------------------------------------------------------



1;
