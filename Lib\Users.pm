package Lib::Users;

use lib "/opt/apache/app/";

use Exporter;
use Lib::KoalaConfig;
use Lib::DSRUtils;


our @ISA = ('Exporter');

our @EXPORT = qw(
    &Users_orgID_to_name
  );




#-------------------------------------------------------------------------
#
# Convert the specified org ID to the org's actual name
#

sub Users_orgID_to_name
{
  my ($query, $dbOutput, $orgName, $status);

  my ($db, $orgID) = @_;


  $query = "SELECT name FROM app.orgs WHERE ID=$orgID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($orgName) = $dbOutput->fetchrow_array;

  return($orgName);
}





#-------------------------------------------------------------------------


1;
