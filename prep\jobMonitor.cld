#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($kapDB);

  $cloudDashHTML = prep_web_cloud_dashboard($kapDB, $prepDB);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Data Prep Job & Cloud Resource Monitor</TITLE>
<LINK REL="SHORTCUT ICON" HREF="$Lib::KoalaConfig::kapHostURL/favicon.png">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<SCRIPT SRC="/fusioncharts-3.18.0/js/fusioncharts.js"></SCRIPT>
<SCRIPT SRC="/fusioncharts-3.18.0/js/themes/fusioncharts.theme.fusion.js"></SCRIPT>

$cloudDashHTML

</HEAD>

<BODY>
END_HTML

  print_html_navbar($kapDB, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="$Lib::KoalaConfig::kapHostURL/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">Data Prep</A></LI>
    <LI CLASS="breadcrumb-item active">Active Job & Cloud Resource Monitor</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #connect to the database
  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  $storagePct = prep_flow_storage(1);

  print_html_header();

  #see if there's any current jobs
  $query = "SELECT ID, flowID, state, rowCount, opInfo, mode, userID, lastAction, exportedKoala \
      FROM prep.jobs WHERE state != 'LOADED' AND state != 'ERROR' \
      ORDER BY lastAction DESC";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;

  #output the panel header HTML
  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-auto"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Data Prep Active Job & Cloud Resource Monitor</DIV>
        <DIV CLASS="card-body">

        <div class="container">
          <div class="row no-gutters">

            <div class="col">
              <div id="div-gauge-jobs-bulb"></div>
            </div>
            <div class="col">
              <div id="div-gauge-cpu-angular"></div>
            </div>
            <div class="col">
              <div id="div-gauge-memory-angular"></div>
            </div>
            <div class="col">
              <div id="div-gauge-throughput-angular"></div>
            </div>
            <div class="col">
              <div id="div-gauge-storage-cylinder"></div>
            </div>

          </div>
        </div>
END_HTML

  #if there aren't any jobs, let the user know and finish
  if ($status < 1)
  {
    print <<END_HTML;
          There aren't any active jobs right now.
END_HTML
  }

  #else there's at least one job
  else
  {
    print <<END_HTML;
          <DIV CLASS="table-responsive">
          <TABLE CLASS="table table-condensed">
            <THEAD><TR>
              <TH>Data Flow</TH>
              <TH>&nbsp;</TH>
              <TH>Status</TH>
              <TH>User</TH>
            </TR></THEAD>
END_HTML

  while (($jobID, $flowID, $jobState, $rowCount, $jobInfo, $jobMode, $jobUserID, $lastAction, $exportedKoala) = $dbOutput->fetchrow_array)
  {
    $query = "SELECT userID FROM prep.flows WHERE ID=$flowID";
    $dbOutput1 = $prepDB->prepare($query);
    $dbOutput1->execute;
    ($flowOwner) = $dbOutput1->fetchrow_array;
    $flowOwner = utils_userID_to_name($kapDB, $flowOwner);

    $flowName = prep_flow_id_to_name($prepDB, $flowID);
    if ($jobUserID == 0)
    {
      $jobUserID = "Koala Scheduler";
    }
    else
    {
      $jobUserID = utils_userID_to_name($kapDB, $jobUserID);
    }

    #determine if the job is in some kind of wait state for display purposes
    $waitState = 0;
    if (($jobState ne "PARSE-WAIT") && ($jobState ne "DATATYPE-WAIT"))
    {
      if ($jobInfo eq "DONE")
      {
        $waitState = 1;
      }
      if ($jobState =~ m/WAIT/)
      {
        $waitState = 1;
      }
    }

    if ($jobState eq "LOADED")
    {

      if ($exportedKoala > 0)
      {
        $jobState = "Exported to Koala";
        $rowColor = "table-light";
      }
      else
      {
        $jobState = "Loaded";
        $rowColor = "table-success";
      }
    }
    elsif ($jobState eq "LOAD-MANUAL")
    {
      $jobState = "Loading manual data";
      $rowColor = "table-secondary";
      $waitState = 1;
    }
    elsif ($jobState eq "LOAD-WEB")
    {
      $jobState = "Loading from web";
      $rowColor = "table-primary";
    }
    elsif ($jobState eq "LOAD-FTP")
    {
      $jobState = "Loading from FTP";
      $rowColor = "table-primary";
    }
    elsif ($jobState eq "LOAD-KOALA")
    {
      $jobState = "Loading from Koala";
      $rowColor = "table-primary";
    }
    elsif ($jobState eq "LOAD-DATABASE")
    {
      $jobState = "Loading from database";
      $rowColor = "table-primary";
    }
    elsif ($jobState eq "LOAD-AMAZON")
    {
      $jobState = "Loading from Amazon S3";
      $rowColor = "table-primary";
    }
    elsif (($jobState eq "EXTRACT-DATA") || ($jobState eq "EXTRACT-DATA-WAIT"))
    {
      $jobState = "Extracting Data";
      $rowColor = "table-primary";
    }
    elsif (($jobState eq "NESTED-WAIT") || ($jobState eq "NESTED-CONVERT"))
    {
      $jobState = "Detecting Nested Data";
      $rowColor = "table-primary";
    }
    elsif ($jobState eq "PARSE-WAIT")
    {
      $jobState = "Waiting for parsing options";
      $rowColor = "table-warning";
    }
    elsif ($jobState =~ m/^LOAD-DATA/)
    {
      $jobState = "Loading raw data";
      $rowColor = "table-primary";
    }
    elsif ($jobState eq "DATATYPE-WAIT")
    {
      $jobState = "Waiting for data types";
      $rowColor = "table-warning";
    }
    elsif ($jobState =~ m/^MERGE-DATA/)
    {
      $jobState = "Merging data";
      $rowColor = "table-primary";
    }
    elsif (($jobState eq "COLUMN-TYPES") || ($jobState eq "COL-TYPES-WAIT"))
    {
      $jobState = "Detecting column types";
      $rowColor = "table-primary";
    }
    elsif ($jobState =~ m/^RECIPE-/)
    {
      $jobState = "Applying transform recipe";
      $rowColor = "table-primary";
    }
    elsif ($jobState =~ m/^VALIDATE/)
    {
      $jobState = "Validating data";
      $rowColor = "table-primary";
    }
    elsif (($jobState eq "TRANSFORM-DATA") || ($jobState eq "TRANSFORM-DATA-WAIT"))
    {
      $jobState = "Transforming data";
      $rowColor = "table-primary";
    }
    elsif ($jobState =~ m/EXP-KOALA/)
    {
      $jobState = "Exporting to Koala Analytics";
      $rowColor = "table-primary";
    }
    elsif ($jobState =~ m/UPDATE-KOALA/)
    {
      $jobState = "Updating Koala data source";
      $rowColor = "table-primary";
    }
    elsif ($jobState eq "ERROR")
    {
      $jobState = "Fatal error";
      if ($jobInfo =~ m/^ERR\|(.*)$/)
      {
        $jobState = $jobState . ": $1";
      }
      $rowColor = "table-danger";
    }
    elsif ($jobState eq "ERROR-EXPORT")
    {
      $jobState = "Error exporting data";
      $rowColor = "table-danger";
    }
    else
    {
      $jobState = "Unknown";
      $rowColor = "table-danger";
    }

    if ($waitState == 1)
    {
      $rowColor = "table-secondary";
    }

    $popoverJobState = $jobState;

    if ($jobMode eq "run")
    {
      $jobState = "Running";

      if ($waitState != 1)
      {
        $rowColor = "table-primary";
      }
    }

    $jobPct = "";
    if ($jobInfo =~ m/^(.*?)\|(.*?)\|(.*)$/)
    {
      $jobPct = $1;
    }
    elsif ($jobInfo =~ m/^(.*?)\|(.*)$/)
    {
      $jobPct = $1;
    }
    if ($jobPct > 0)
    {
      $jobInfo = "<BR><B>Progress: </B>$1%"
    }
    else
    {
      $jobInfo = "";
    }

    $recordCount = "";
    if ($rowCount > 0)
    {
      $recordCount = prep_autoscale_number($rowCount);
      $recordCount = "<BR><B>Records: </B>$recordCount";
    }

    print <<END_HTML;
            <TR CLASS="$rowColor">
              <TD><A CLASS="text-decoration-none" HREF='flowOpen.cld?f=$flowID'>$flowName</A></TD>
              <TD>
                <A HREF="#" data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                  data-bs-content="<B>Flow Owner: </B>$flowOwner<BR><B>Status: </B>$popoverJobState $jobInfo $recordCount">
                  <I CLASS="bi bi-info-circle"></I>
                </A>
              </TD>
              <TD>$jobState</TD>
              <TD>$jobUserID</TD>
            </TR>
END_HTML
    }

    print <<END_HTML;
          </TABLE>
          </DIV>
END_HTML
  }

  print <<END_HTML;
<SCRIPT>
\$(function()
{
  \$('[data-bs-toggle="popover"]').popover()
})
</SCRIPT>

          <P>
          <CENTER>
            <BUTTON CLASS="btn btn-primary" TYPE="button" onclick="location.href='main.cld'"><I CLASS="bi bi-check-lg"></I> Done</BUTTON>
          </CENTER>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
