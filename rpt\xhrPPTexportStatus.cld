#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  $dsID = $q->param('d');

  print("Content-type: text/plain\n\n");

  $db = KAPutil_connect_to_database();

  #get the data source's current status
  $query = "SELECT status FROM jobs \
      WHERE userID=$userID AND dsID=$dsID AND operation='REPORT-EXPORT' \
      ORDER BY PID DESC LIMIT 1";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($opStatus) = $dbOutput->fetchrow_array;

  if (length($opStatus) < 1)
  {
    $opStatus = "DONE";
  }

  print("$opStatus\n");


#EOF
