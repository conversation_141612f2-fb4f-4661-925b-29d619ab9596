package Lib::ODBC;

use lib "/opt/apache/app/";

use Exporter;
use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::Social;
use Lib::WebUtils;


our @ISA = ('Exporter');

our @EXPORT = qw(
    &ODBC_telemetry
    &ODBC_export_tabular
    &ODBC_export_star
  );




#-------------------------------------------------------------------------------
#
# Log telemetry information about the ODBC export process
#

sub ODBC_telemetry
{
  my ($query);

  my ($db, $odbcUpdateID, $text) = @_;


  $text = ": $text\n";
  $q_text = $db->quote($text);
  $query = "UPDATE audit.telemetry_odbc \
      SET telemetry = CONCAT(telemetry, NOW(), $q_text) WHERE ID=$odbcUpdateID";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------------
#
# Create a tabular export table for the specified data source
#

sub ODBC_export_tabular
{
  my ($db, $dsName, $ODBCexport, $ODBCmanual, $ODBCbaseItems, $ODBCstatus);

  my ($dsID, $dsOwnerID) = @_;


  #reconnect to the database
  $db = KAPutil_connect_to_database();

  #turn off SQL binary logging (if the system crashes during creation, anybody
  #in their right mind is going to assume the partially-created data cube
  #is garbage anyways). Should result in noticeable speed improvement due to
  #reduced disk I/O
  $query = "SET sql_log_bin = 0";
  $db->do($query);

  #turn off the transaction isolation guarantee for the same reasons as above
  $query = "SET SESSION transaction_isolation='READ-UNCOMMITTED'";
  $db->do($query);

  #grab info about the ODBC export for this data source
  $query = "SELECT name, ODBCexport, ODBCmanual, ODBCbaseItems, ODBCstatus, UNIX_TIMESTAMP(lastUpdate), ODBCuser, ODBCpassword \
      FROM dataSources WHERE ID=$dsID";
  $ds_output = $db->prepare($query);
  $status = $ds_output->execute;
  KAPutil_handle_db_err($db, $status, $query);

  ($dsName, $ODBCexport, $ODBCmanual, $ODBCbaseItems, $ODBCstatus, $lastUpdate, $ODBCuser, $ODBCpassword) = $ds_output->fetchrow_array;

  KAPutil_job_store_status($db, 0, $dsID, 0, "ODBC", "Refreshing ODBC export");
  $q_name = $db->quote($dsName);
  $query = "UPDATE app.jobs SET dsName=$q_name WHERE PID=$$ AND dsID=$dsID";
  $db->do($query);

  KAPutil_job_update_state($db, "STARTING");

  #get our update ID that we're going to use for telemetry logging
  $query = "INSERT INTO audit.telemetry_odbc (dsID, startTime) \
      VALUES ($dsID, NOW())";
  $status = $db->do($query);
  $odbcUpdateID = $db->{q{mysql_insertid}};

  #add our startup telemetry
  $query = "UPDATE audit.telemetry_odbc \
      SET telemetry = CONCAT(NOW(), ': Starting ODBC export\n') \
      WHERE ID=$odbcUpdateID";
  $db->do($query);
  utils_audit($db, 0, "Exported data source to ODBC table|$odbcUpdateID", $dsID, 0, 0);

  #set the data source's ODBC export op status
  $query = "UPDATE dataSources SET ODBCstatus='WORKING' WHERE ID=$dsID";
  $db->do($query);

  $dsSchema = "datasource_" . $dsID;

  #if the schema for the ODBC export table doesn't already exist, create it
  #NB: don't be tempted to use a CREATE SCHEMA IF NOT EXISTS here - causes
  #   locking problems.
  ODBC_telemetry($db, $odbcUpdateID, "Checking schema");
  KAPutil_job_update_status($db, "Checking schema");
  $query = "SELECT COUNT(1) FROM information_schema.tables
      WHERE table_schema='$dsSchema'";
  $dbOutput = $odbcDB->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($odbcDB, $status, $query);
  ($schemaExists) = $dbOutput->fetchrow_array;
  if ($schemaExists < 1)
  {
    $query = "CREATE SCHEMA $dsSchema";
    $status = $odbcDB->do($query);
    KAPutil_handle_db_err($odbcDB, $status, $query);
  }

  #if there's an old working export table, drop it
  ODBC_telemetry($db, $odbcUpdateID, "Cleaning up old working export tables");
  KAPutil_job_update_status($db, "Cleaning up old working export tables");
  KAPutil_db_delete_table($db, $dsSchema, "_export");

  #get the "last ODBC exported" timestamp
  #NB: We do this at the top of the refresh process so any changes made
  #    by users during the refresh will trigger yet another refresh
  #NB: We put the timestamp in place at the end of the export process, just
  #    in case something keeps the update process from completing
  $query = "SELECT NOW() FROM dataSources LIMIT 1";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($ODBCexported) = $dbOutput->fetchrow_array;

  #create a copy of the facts table structure
  #NB: by using the original facts table creation statement, we're automatically
  #    picking up things like the DATA DIRECTORY directive that controls the
  #    tablespace where the ODBC table is created
  ODBC_telemetry($db, $odbcUpdateID, "Creating the export table base structure");
  KAPutil_job_update_status($db, "Creating export table base structure");
  KAPutil_job_update_state($db, "CREATE-TABLE");
  $query = "SHOW CREATE TABLE $dsSchema.facts";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($tblName, $createSQL) = $dbOutput->fetchrow_array;
  $createSQL =~ s/CREATE TABLE `facts` \(/CREATE TABLE $dsSchema._export \(/;
  $status = $db->do($createSQL);
  KAPutil_handle_db_err($db, $status, $query);

  #populate the working table with the data from the facts table
  ODBC_telemetry($db, $odbcUpdateID, "Populating export table with measure data");
  KAPutil_job_update_status($db, "Populating export table with measure data");
  KAPutil_job_update_state($db, "POPULATING-FACTS");
  $query = "INSERT $dsSchema._export SELECT * FROM $dsSchema.facts";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);

  #add columns to hold product segmentation info
  ODBC_telemetry($db, $odbcUpdateID, "Altering export table to hold additional product data");
  KAPutil_job_update_status($db, "Adding product data columns");
  KAPutil_job_update_state($db, "ADD-PRODUCT-COLUMNS");
  %prodSegmentationIDs = DSRsegmentation_get_segmentations_hash($db, $dsSchema, "p");
  $query = "ALTER TABLE $dsSchema._export ";
  undef(%usedNames);
  foreach $segID (keys %prodSegmentationIDs)
  {
    $segmentationName = $prodSegmentationIDs{$segID};

    if ($segmentationName =~ m/^product$/i)
    {
      $segmentationName = "Product Attribute";
      $prodSegmentationIDs{$segID} = "Product Attribute";
    }

    #don't create duplicate columns - give it a DUPE tag and a number
    $tmp = lc($segmentationName);
    if ($usedNames{$tmp} > 0 )
    {
      $segmentationName = "DUPE $usedNames{$tmp} $segmentationName";
      $prodSegmentationIDs{$segID} = $segmentationName;
    }
    $usedNames{$tmp}++;

    #add a column to the ODBC table for the segmentation
    $query .= "ADD COLUMN `$segmentationName` VARCHAR(128) AFTER timeID,";
  }

  #add columns containing the human-readable dimension item names
  $query = $query . "ADD COLUMN UPC VARCHAR(24) AFTER timeID, \
            ADD COLUMN TimeAlias VARCHAR(128) AFTER timeID, \
            ADD COLUMN Time VARCHAR(128) AFTER timeID, \
            ADD COLUMN Geography VARCHAR(128) AFTER timeID, \
            ADD COLUMN Product VARCHAR(128) AFTER timeID";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);

  #add columns to hold geography segmentation info
  ODBC_telemetry($db, $odbcUpdateID, "Altering export table to hold additional geography data");
  KAPutil_job_update_status($db, "Adding geography data columns");
  KAPutil_job_update_state($db, "ADD-GEOGRAPHY-COLUMNS");
  %segmentationIDs = DSRsegmentation_get_segmentations_hash($db, $dsSchema, "g");
  $query = "ALTER TABLE $dsSchema._export ";
  $exist = 0;
  foreach $segID (keys %segmentationIDs)
  {
    $exist = 1;
    $segmentationName = $segmentationIDs{$segID};

    #add a column to the ODBC table for the segmentation
    $query .= "ADD COLUMN `$segmentationName` VARCHAR(128) AFTER Time,";
  }
  chop($query);
  if ($exist > 0)
  {
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);
  }

  #NB: For performance reasons, we're going to build up a hash of UPDATE
  #    statements for products, hashed by the product ID. This way instead
  #    of performing potentially millions of UPDATE statements (which get
  #    slower as the table grows), we just do one for each product in the
  #    underlying data source

  undef(%prodUpdates);

  #populate the new human-readable dimension columns
  ODBC_telemetry($db, $odbcUpdateID, "Collecting product name data");
  KAPutil_job_update_status($db, "Collecting product name data");
  KAPutil_job_update_state($db, "COLLECT-PRODUCT-NAMES");
  %prodNameHash = dsr_get_item_name_hash($db, $dsSchema, "p", $ODBCbaseItems);

  foreach $productID (keys %prodNameHash)
  {
    if ($productID =~ m/^[0-9]+$/)
    {
      $q_name = $db->quote($prodNameHash{$productID});
      $prodUpdates{$productID} .= " Product=$q_name,";
    }
  }

  ODBC_telemetry($db, $odbcUpdateID, "Adding geography names to exported data");
  KAPutil_job_update_status($db, "Adding geography names");
  KAPutil_job_update_state($db, "ADD-GEOGRAPHY-NAMES");
  $query = "UPDATE $dsSchema._export exp, $dsSchema.geographies geo \
              SET exp.Geography = COALESCE(geo.alias, geo.name) \
              WHERE exp.geographyID = geo.ID";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);

  #we've been gone a long time, so make sure we're still connected to Analytics
  if (!($db->ping))
  {
    $db = KAPutil_connect_to_database();
  }

  ODBC_telemetry($db, $odbcUpdateID, "Adding time period names and aliases to exported data");
  KAPutil_job_update_status($db, "Adding time period names");
  KAPutil_job_update_state($db, "ADD-TIME-NAMES");
  $query = "UPDATE $dsSchema._export exp, $dsSchema.timeperiods time \
              SET exp.Time = time.name, exp.TimeAlias = time.alias \
              WHERE exp.timeID = time.ID";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);

  #we've been gone a long time, so make sure we're still connected to Analytics
  if (!($db->ping))
  {
    $db = KAPutil_connect_to_database();
  }

  #change the measure column names into human readable strings
  ODBC_telemetry($db, $odbcUpdateID, "Altering measure columns to have natural language names");
  KAPutil_job_update_status($db, "Setting measure column names");
  KAPutil_job_update_state($db, "ALTER-COLUMN-NAMES");
  %measureNameHash = dsr_get_item_name_hash($db, $dsSchema, "m", $ODBCbaseItems);
  $query = "";
  foreach $measureID (keys %measureNameHash)
  {
    $name = $measureNameHash{$measureID};
    if ($name =~ m/^(.*)\s+$/)
    {
      $name = $1;
    }

    #don't create duplicate columns - give it a DUPE tag and a number
    $tmp = lc($name);
    if ($usedNames{$tmp} > 0 )
    {
      $name = "DUPE $usedNames{$tmp} $name";
    }
    $usedNames{$tmp}++;

    $measureCol = "measure_" . $measureID;
    if ($measureID =~ /^\d+/)
    {
      $query = "$query CHANGE COLUMN $measureCol `$name` DOUBLE,";
    }
  }
  chop($query);
  $query = "ALTER TABLE $dsSchema._export $query";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);


  #################    UPC        #######################

  #get the attribute ID for our primary UPC (always named UPC)
  ODBC_telemetry($db, $odbcUpdateID, "Preparing to add UPC information to export table");
  KAPutil_job_update_status($db, "Collecting UPC information");
  KAPutil_job_update_state($db, "COLLECT-UPC-INFO");
  $query = "SELECT ID from $dsSchema.product_attributes WHERE name='UPC'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($upcID) = $dbOutput->fetchrow_array;

  if ($upcID > 0)
  {

    #get hash of UPC values
    %upcHash = DSRattr_get_values_hash($db, $dsSchema, "p", $upcID);

    #update the UPC value for each product in the table
    foreach $productID (keys %upcHash)
    {
      $q_upc = $db->quote($upcHash{$productID});
      $prodUpdates{$productID} = $prodUpdates{$productID} . " UPC=$q_upc,";
    }
  }


  ################# SEGMENTATIONS #######################

  #get hash of all product segmentations
  ODBC_telemetry($db, $odbcUpdateID, "Preparing to add product segment information");
  KAPutil_job_update_status($db, "Collecting segmentation info");
  KAPutil_job_update_state($db, "COLLECT-PRODUCT-INFO");

  #run through every segmentation
  $row = 1;
  foreach $segID (keys %prodSegmentationIDs)
  {
    $segmentationName = $prodSegmentationIDs{$segID};

    #if this is the annoying Nielsen "PRODUCT" segmentation, give it a
    #non-conflicting name
    if ($segmentationName =~ m/^product$/i)
    {
      $segmentationName = "Product Attribute";
    }

    if ($segmentationName =~ m/^product $/i)
    {
      $segmentationName = "Product SPACE";
    }

    #get the segment each product is a member of for the current segmentation
    %segMembershipHash = DSRseg_get_segment_membership_hash($db, $dsSchema, "p", $segID);

    #get the name of the segments in this segmentation, hashed by ID
    %segmentsHash = DSRseg_get_segments_hash($db, $dsSchema, "p", $segID);

    #run through each product in the segmentation, and add the membership
    #to the product's UPDATE statement
    foreach $productID (keys %segMembershipHash)
    {
      $segmentID = $segMembershipHash{$productID};
      $q_segmentName = $db->quote($segmentsHash{$segmentID});
      $prodUpdates{$productID} .= " `$segmentationName`=$q_segmentName,";
    }
  }

  #get hash of all geography segmentations
  ODBC_telemetry($db, $odbcUpdateID, "Adding geography segment information");
  KAPutil_job_update_status($db, "Adding geography segment information");
  KAPutil_job_update_state($db, "UPDATE-GEOGRAPHY-INFO");
  %segmentationIDs = DSRsegmentation_get_segmentations_hash($db, $dsSchema, "g");

  #run through every segmentation
  $row = 1;
  foreach $segID (keys %segmentationIDs)
  {
    $segmentationName = $segmentationIDs{$segID};

    #run through each segment in the segmentation
    %segmentsHash = DSRseg_get_segments_hash($db, $dsSchema, "g", $segID);
    foreach $segment (keys %segmentsHash)
    {
      $q_segmentName = $db->quote($segmentsHash{$segment});

      #get an array of the segment's member items, and write to export table
      @itemIDs = DSRseg_get_segitems_array($db, $dsSchema, "g", $segID, $segment);
      foreach $item (@itemIDs)
      {
        $query = "UPDATE $dsSchema._export \
            SET `$segmentationName`=$q_segmentName WHERE geographyID=$item ";
        $status = $db->do($query);
        KAPutil_handle_db_err($db, $status, $query);
      }
    }
  }

  #run through each product UPDATE statement, and apply it to the export table
  ODBC_telemetry($db, $odbcUpdateID, "Adding product name, UPC, and segment information");
  KAPutil_job_update_status($db, "Adding product segments/attributes");
  KAPutil_job_update_state($db, "UPDATE-PRODUCT-INFO");
  foreach $productID (keys %prodUpdates)
  {
    chop($prodUpdates{$productID});
    $query = "UPDATE $dsSchema._export SET $prodUpdates{$productID} \
        WHERE productID=$productID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);
  }

  #create indexes on geography and market dimensions
  ODBC_telemetry($db, $odbcUpdateID, "Indexing dimension information");
  KAPutil_job_update_status($db, "Indexing dimension information");
  KAPutil_job_update_state($db, "INDEX-DIMENSIONS");
  $db->do("CREATE INDEX idx_geo ON $dsSchema._export (Geography) \
      ALGORITHM=INPLACE LOCK=EXCLUSIVE");
  $db->do("CREATE INDEX idx_time ON $dsSchema._export (TimeAlias) \
      ALGORITHM=INPLACE LOCK=EXCLUSIVE");

  #set the facts table to be compressed
  ODBC_telemetry($db, $odbcUpdateID, "Optimizing table space");
  KAPutil_job_update_status($db, "Optimizing table space");
  KAPutil_job_update_state($db, "COMPRESS-TABLE");
  $query = "ALTER TABLE $dsSchema._export ROW_FORMAT=COMPRESSED";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);


  ########################################################

  #replace the old export table with the working table
  KAPutil_job_update_state($db, "REPLACE-EXPORT");
  KAPutil_job_update_status($db, "Replacing old export table");
  KAPutil_db_delete_table($db, $dsSchema, "export");

  $query = "RENAME TABLE $dsSchema._export TO $dsSchema.export";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);

  #shouldn't need to do this, but just in case the user has done something
  #crazy, re-set the GRANT table
  $q_ODBCuser = $db->quote($ODBCuser);
  $q_ODBCpassword = $db->quote($ODBCpassword);
  $query = "CREATE USER IF NOT EXISTS $q_ODBCuser IDENTIFIED BY $q_ODBCpassword";
  $db->do($query);
  $query = "GRANT SELECT ON $dsSchema.export TO $q_ODBCuser";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);

  #set the data source's ODBC export op status
  $query = "UPDATE dataSources SET ODBCstatus=NULL, ODBCexported='$ODBCexported' \
      WHERE ID=$dsID";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);

  $query = "UPDATE audit.telemetry_odbc \
      SET endTime=NOW(), telemetry = CONCAT(telemetry, NOW(), ': Finished export to ODBC\n') \
      WHERE ID=$odbcUpdateID";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);

  DSRutil_clear_status($db);

  $dsName = ds_id_to_name($db, $dsID);
  Social_feed_add_item($db, $dsOwnerID, $dsID, 0, 0, "success", "odbc_refresh");
  utils_slack("Refreshed tabular ODBC export for $dsName");
}



#-------------------------------------------------------------------------
#
# Create basic star schema export tables for the specified data source
#

sub ODBC_export_star
{
  my ($db, $dsName, $ODBCexport, $ODBCmanual, $ODBCbaseItems, $ODBCstatus);
  my ($odbcDB);

  my ($dsID, $dsOwnerID) = @_;


  #reconnect to the database
  $db = KAPutil_connect_to_database();
  $odbcDB = DBI->connect($Lib::KoalaConfig::ODBCDBServer, 'app', $Lib::KoalaConfig::password);

  #turn off SQL binary logging (if the system crashes during creation, anybody
  #in their right mind is going to assume the partially-created data cube
  #is garbage anyways). Should result in noticeable speed improvement due to
  #reduced disk I/O
  $query = "SET sql_log_bin = 0";
  $odbcDB->do($query);

  #turn off the transaction isolation guarantee for the same reasons as above
  $query = "SET SESSION transaction_isolation='READ-UNCOMMITTED'";
  $odbcDB->do($query);

  #grab info about the ODBC export for this data source
  $query = "SELECT name, ODBCexport, ODBCmanual, ODBCbaseItems, ODBCstatus, UNIX_TIMESTAMP(lastUpdate), ODBCuser, ODBCpassword \
      FROM dataSources WHERE ID=$dsID";
  $ds_output = $db->prepare($query);
  $status = $ds_output->execute;
  KAPutil_handle_db_err($db, $status, $query);

  ($dsName, $ODBCexport, $ODBCmanual, $ODBCbaseItems, $ODBCstatus, $lastUpdate, $ODBCuser, $ODBCpassword) = $ds_output->fetchrow_array;

  KAPutil_job_store_status($db, 0, $dsID, 0, "ODBC", "Refreshing ODBC export");
  $q_name = $db->quote($dsName);
  $query = "UPDATE app.jobs SET dsName=$q_name WHERE PID=$$ AND dsID=$dsID";
  $db->do($query);

  KAPutil_job_update_state($db, "STARTING");

  #get our update ID that we're going to use for telemetry logging
  $query = "INSERT INTO audit.telemetry_odbc (dsID, startTime) \
      VALUES ($dsID, NOW())";
  $status = $db->do($query);
  $odbcUpdateID = $db->{q{mysql_insertid}};

  #add our startup telemetry
  $query = "UPDATE audit.telemetry_odbc \
      SET telemetry = CONCAT(NOW(), ': Starting ODBC export\n') \
      WHERE ID=$odbcUpdateID";
  $db->do($query);
  utils_audit($db, 0, "Exported data source to ODBC table|$odbcUpdateID", $dsID, 0, 0);

  #set the data source's ODBC export op status
  $query = "UPDATE dataSources SET ODBCstatus='WORKING' WHERE ID=$dsID";
  $db->do($query);

  $dsSchema = "datasource_" . $dsID;

  #if the schema for the ODBC export table doesn't already exist, create it
  #NB: don't be tempted to use a CREATE SCHEMA IF NOT EXISTS here - causes
  #   locking problems.
  ODBC_telemetry($db, $odbcUpdateID, "Checking schema");
  KAPutil_job_update_status($db, "Checking schema");
  $query = "SELECT COUNT(1) FROM information_schema.tables
      WHERE table_schema='$dsSchema'";
  $dbOutput = $odbcDB->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($odbcDB, $status, $query);
  ($schemaExists) = $dbOutput->fetchrow_array;
  if ($schemaExists < 1)
  {
    $query = "CREATE SCHEMA $dsSchema";
    $status = $odbcDB->do($query);
    KAPutil_handle_db_err($odbcDB, $status, $query);
  }

  #if there are old working export tables, drop them
  ODBC_telemetry($db, $odbcUpdateID, "Cleaning up old working export tables");
  KAPutil_job_update_status($db, "Cleaning up old working export tables");
  KAPutil_db_delete_table($db, $dsSchema, "_export");
  KAPutil_db_delete_table($db, $dsSchema, "_export_product");
  KAPutil_db_delete_table($db, $dsSchema, "_export_geography");
  KAPutil_db_delete_table($db, $dsSchema, "_export_time");

  #get the "last ODBC exported" timestamp
  #NB: We do this at the top of the refresh process so any changes made
  #    by users during the refresh will trigger yet another refresh
  #NB: We put the timestamp in place at the end of the export process, just
  #    in case something keeps the update process from completing
  $query = "SELECT NOW() FROM dataSources LIMIT 1";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($ODBCexported) = $dbOutput->fetchrow_array;

  #create a copy of the facts table structure
  ODBC_telemetry($db, $odbcUpdateID, "Creating the export table base structure");
  KAPutil_job_update_status($db, "Creating export table base structure");
  KAPutil_job_update_state($db, "CREATE-TABLE");
  $query = "SHOW CREATE TABLE $dsSchema.facts";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($tblName, $createSQL) = $dbOutput->fetchrow_array;
  $createSQL =~ s/CREATE TABLE `facts` \(/CREATE TABLE $dsSchema._export \(/;
  $status = $db->do($createSQL);
  KAPutil_handle_db_err($odbcDB, $status, $query);

  #populate the working table with the data from the facts table
  ODBC_telemetry($db, $odbcUpdateID, "Populating export table with measure data");
  KAPutil_job_update_status($db, "Populating export table with measure data");
  KAPutil_job_update_state($db, "POPULATING-FACTS");
  $query = "INSERT $dsSchema._export SELECT * FROM $dsSchema.facts";
  $status = $odbcDB->do($query);
  KAPutil_handle_db_err($odbcDB, $status, $query);

  #change the measure column names into human readable strings
  ODBC_telemetry($db, $odbcUpdateID, "Altering measure columns to have natural language names");
  KAPutil_job_update_status($db, "Setting measure column names");
  KAPutil_job_update_state($db, "ALTER-COLUMN-NAMES");
  %measureNameHash = dsr_get_item_name_hash($db, $dsSchema, "m", $ODBCbaseItems);
  $query = "";
  foreach $measureID (keys %measureNameHash)
  {
    $name = $measureNameHash{$measureID};
    if ($name =~ m/^(.*)\s+$/)
    {
      $name = $1;
    }

    #don't create duplicate columns - give it a DUPE tag and a number
    $tmp = lc($name);
    if ($usedNames{$tmp} > 0 )
    {
      $name = "DUPE $usedNames{$tmp} $name";
    }
    $usedNames{$tmp}++;

    $measureCol = "measure_" . $measureID;
    if ($measureID =~ /^\d+/)
    {
      $query = "$query CHANGE COLUMN $measureCol `$name` DOUBLE,";
    }
  }
  chop($query);
  $query = "ALTER TABLE $dsSchema._export $query";
  $status = $odbcDB->do($query);
  KAPutil_handle_db_err($odbcDB, $status, $query);

  #set the facts table to be compressed
  ODBC_telemetry($db, $odbcUpdateID, "Optimizing table space");
  KAPutil_job_update_status($db, "Optimizing table space");
  KAPutil_job_update_state($db, "COMPRESS-TABLE");
  $query = "ALTER TABLE $dsSchema._export ROW_FORMAT=COMPRESSED";
  $status = $odbcDB->do($query);
  KAPutil_handle_db_err($odbcDB, $status, $query);


  ################### Product Table ######################

  #create a copy of the products table structure
  ODBC_telemetry($db, $odbcUpdateID, "Creating the export_product table base structure");
  KAPutil_job_update_status($db, "Creating export_product table base structure");
  KAPutil_job_update_state($db, "CREATE-TABLE");
  $query = "SHOW CREATE TABLE $dsSchema.products";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($tblName, $createSQL) = $dbOutput->fetchrow_array;
  $createSQL =~ s/CREATE TABLE `products` \(/CREATE TABLE $dsSchema._export_product \(/;
  $status = $db->do($createSQL);
  KAPutil_handle_db_err($odbcDB, $status, $query);

  #populate the working table with the data from the products table
  ODBC_telemetry($db, $odbcUpdateID, "Populating product table with data");
  KAPutil_job_update_status($db, "Populating product table with data");
  KAPutil_job_update_state($db, "POPULATING-FACTS");
  $query = "INSERT $dsSchema._export_product SELECT * FROM $dsSchema.products";
  $status = $odbcDB->do($query);
  KAPutil_handle_db_err($odbcDB, $status, $query);

  #coalesce aliases and product names
  $query = "UPDATE $dsSchema._export_product SET name=COALESCE(alias, name)";
  $status = $odbcDB->do($query);
  KAPutil_handle_db_err($odbcDB, $status, $query);

  #delete child merged items from both product and facts table
  $query = "SELECT ID FROM $dsSchema._export_product WHERE merged=2";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($odbcDB, $status, $query);
  $childProdIDs = "";
  while (($id) = $dbOutput->fetchrow_array)
  {
    $childProdIDs .= "$id,";
  }
  chop($childProdIDs);

  if (length($childProdIDs) > 0)
  {
    $query = "DELETE FROM $dsSchema._export_product WHERE ID IN ($childProdIDs)";
    $status = $odbcDB->do($query);
    KAPutil_handle_db_err($odbcDB, $status, $query);

    $query = "DELETE FROM $dsSchema._export WHERE productID IN ($childProdIDs)";
    $status = $odbcDB->do($query);
    KAPutil_handle_db_err($odbcDB, $status, $query);
  }

  #drop unneeded columns
  $query = "ALTER TABLE $dsSchema._export_product \
      DROP COLUMN alias, DROP COLUMN merged, \
      CHANGE COLUMN `name` `Product` VARCHAR(128)";
  $status = $odbcDB->do($query);
  KAPutil_handle_db_err($odbcDB, $status, $query);

  #add columns to hold product segmentation/attribute info
  ODBC_telemetry($db, $odbcUpdateID, "Altering export table to hold additional product data");
  KAPutil_job_update_status($db, "Adding product data columns");
  KAPutil_job_update_state($db, "ADD-PRODUCT-COLUMNS");
  %prodSegmentationIDs = DSRsegmentation_get_segmentations_hash($db, $dsSchema, "p");
  $query = "ALTER TABLE $dsSchema._export_product ";
  undef(%usedNames);
  foreach $segID (keys %prodSegmentationIDs)
  {
    $segmentationName = $prodSegmentationIDs{$segID};

    if ($segmentationName =~ m/^product$/i)
    {
      $segmentationName = "Product Segmentation";
      $prodSegmentationIDs{$segID} = "Product Segmentation";
    }

    #don't create duplicate columns - give it a DUPE tag and a number
    $tmp = lc($segmentationName);
    if ($usedNames{$tmp} > 0)
    {
      $segmentationName = "DUPE $usedNames{$tmp} $segmentationName";
      $prodSegmentationIDs{$segID} = $segmentationName;
    }
    $usedNames{$tmp}++;

    #add a column to the ODBC table for the segmentation
    $query = $query . "ADD COLUMN `$segmentationName` VARCHAR(128),";
  }

  #add columns for any non-UPC product attributes
  %prodAttributeIDs = DSRattr_get_attributes_hash($db, $dsSchema, "p");
  foreach $attributeID (keys %prodAttributeIDs)
  {
    $attributeName = $prodAttributeIDs{$attributeID};
    if ($attributeName eq "UPC")
    {
      next;
    }

    #don't create duplicate columns - give it a DUPE tag and a number
    $tmp = lc($attributeName);
    if ($usedNames{$tmp} > 0)
    {
      $attributeName = "DUPE $usedNames{$tmp} $attributeName";
      $prodAttributeIDs{$attributeID} = $attributeName;
    }
    $usedNames{$tmp}++;

    #add a column to the ODBC table for the segmentation
    $query = $query . "ADD COLUMN `$attributeName` VARCHAR(128),";
  }

  #add columns containing the human-readable dimension item names
  $query = $query . "ADD COLUMN UPC VARCHAR(24) AFTER Product";
  $status = $odbcDB->do($query);
  KAPutil_handle_db_err($odbcDB, $status, $query);


  ################### Geography Table #####################

  #create a copy of the geographies table structure
  ODBC_telemetry($db, $odbcUpdateID, "Creating the export_geography table base structure");
  KAPutil_job_update_status($db, "Creating export_geography table base structure");
  KAPutil_job_update_state($db, "CREATE-TABLE");
  $query = "SHOW CREATE TABLE $dsSchema.geographies";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($tblName, $createSQL) = $dbOutput->fetchrow_array;
  $createSQL =~ s/CREATE TABLE `geographies` \(/CREATE TABLE $dsSchema._export_geography \(/;
  $status = $db->do($createSQL);
  KAPutil_handle_db_err($odbcDB, $status, $query);

  #populate the working table with the data from the products table
  ODBC_telemetry($db, $odbcUpdateID, "Populating geography table with data");
  KAPutil_job_update_status($db, "Populating geography table with data");
  KAPutil_job_update_state($db, "POPULATING-FACTS");
  $query = "INSERT $dsSchema._export_geography \
      SELECT * FROM $dsSchema.geographies";
  $status = $odbcDB->do($query);
  KAPutil_handle_db_err($odbcDB, $status, $query);

  #coalesce aliases and product names
  $query = "UPDATE $dsSchema._export_geography SET name=COALESCE(alias, name)";
  $status = $odbcDB->do($query);
  KAPutil_handle_db_err($odbcDB, $status, $query);

  #drop unneeded columns
  $query = "ALTER TABLE $dsSchema._export_geography DROP COLUMN alias, \
      CHANGE COLUMN `name` `Geography` VARCHAR(128)";
  $status = $odbcDB->do($query);
  KAPutil_handle_db_err($odbcDB, $status, $query);

  #add columns to hold geography segmentation info
  ODBC_telemetry($db, $odbcUpdateID, "Altering export table to hold additional geography data");
  KAPutil_job_update_status($db, "Adding geography data columns");
  KAPutil_job_update_state($db, "ADD-GEOGRAPHY-COLUMNS");
  %geoSegmentationIDs = DSRsegmentation_get_segmentations_hash($db, $dsSchema, "g");
  $query = "ALTER TABLE $dsSchema._export_geography ";
  $exist = 0;
  foreach $segID (keys %geoSegmentationIDs)
  {
    $exist = 1;
    $segmentationName = $geoSegmentationIDs{$segID};

    #add a column to the ODBC table for the segmentation
    $query .= "ADD COLUMN `$segmentationName` VARCHAR(128),";
  }
  chop($query);
  if ($exist > 0)
  {
    $status = $odbcDB->do($query);
    KAPutil_handle_db_err($odbcDB, $status, $query);
  }


  ################### Time Periods Table ######################

  #create a copy of the time periods table structure
  ODBC_telemetry($db, $odbcUpdateID, "Creating the export_time table base structure");
  KAPutil_job_update_status($db, "Creating export_time table base structure");
  KAPutil_job_update_state($db, "CREATE-TABLE");
  $query = "SHOW CREATE TABLE $dsSchema.timeperiods";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($tblName, $createSQL) = $dbOutput->fetchrow_array;
  $createSQL =~ s/CREATE TABLE `timeperiods` \(/CREATE TABLE $dsSchema._export_time \(/;
  $status = $db->do($createSQL);
  KAPutil_handle_db_err($odbcDB, $status, $query);

  #drop unneeded columns
  $query = "ALTER TABLE $dsSchema._export_time \
      DROP duration, DROP type, DROP forecast";
  $status = $odbcDB->do($query);
  KAPutil_handle_db_err($odbcDB, $status, $query);

  #populate the working table with the data from the products table
  ODBC_telemetry($db, $odbcUpdateID, "Populating time table with data");
  KAPutil_job_update_status($db, "Populating time table with data");
  KAPutil_job_update_state($db, "POPULATING-FACTS");
  $query = "SET sql_mode='ALLOW_INVALID_DATES';";
  $status = $odbcDB->do($query);
  KAPutil_handle_db_err($odbcDB, $status, $query);
  $query = "INSERT $dsSchema._export_time SELECT ID, name, alias, endDate \
      FROM $dsSchema.timeperiods";
  $status = $odbcDB->do($query);
  KAPutil_handle_db_err($odbcDB, $status, $query);

  #rename columns
  $query = "ALTER TABLE $dsSchema._export_time \
      CHANGE COLUMN alias `timeAlias` VARCHAR(128), \
      CHANGE COLUMN `name` `Time` VARCHAR(128)";
  $status = $odbcDB->do($query);
  KAPutil_handle_db_err($odbcDB, $status, $query);

  #add columns to hold time segmentation info
  ODBC_telemetry($db, $odbcUpdateID, "Altering export table to hold additional time data");
  KAPutil_job_update_status($db, "Adding time data columns");
  KAPutil_job_update_state($db, "ADD-TIME-COLUMNS");
  %timeSegmentationIDs = DSRsegmentation_get_segmentations_hash($db, $dsSchema, "t");
  $query = "ALTER TABLE $dsSchema._export_time ";
  $exist = 0;
  foreach $segID (keys %timeSegmentationIDs)
  {
    $exist = 1;
    $segmentationName = $timeSegmentationIDs{$segID};

    #add a column to the ODBC table for the segmentation
    $query .= "ADD COLUMN `$segmentationName` VARCHAR(128),";
  }
  chop($query);
  if ($exist > 0)
  {
    $status = $odbcDB->do($query);
    KAPutil_handle_db_err($odbcDB, $status, $query);
  }



  ################# SEGMENTATIONS #######################

  #get hash of all product segmentations
  ODBC_telemetry($db, $odbcUpdateID, "Preparing to add product segment information");
  KAPutil_job_update_status($db, "Collecting segmentation info");
  KAPutil_job_update_state($db, "COLLECT-PRODUCT-INFO");

  #run through every segmentation
  $row = 1;
  foreach $segID (keys %prodSegmentationIDs)
  {
    $segmentationName = $prodSegmentationIDs{$segID};

    #if this is the annoying Nielsen "PRODUCT" segmentation, give it a
    #non-conflicting name
    if ($segmentationName =~ m/^product$/i)
    {
      $segmentationName = "Product Segmentation";
    }

    if ($segmentationName =~ m/^product $/i)
    {
      $segmentationName = "Product SPACE";
    }

    #get the segment each product is a member of for the current segmentation
    %segMembershipHash = DSRseg_get_segment_membership_hash($db, $dsSchema, "p", $segID);

    #get the name of the segments in this segmentation, hashed by ID
    %segmentsHash = DSRseg_get_segments_hash($db, $dsSchema, "p", $segID);

    #run through each product in the segmentation, and add the membership
    #to the product's UPDATE statement
    foreach $productID (keys %segMembershipHash)
    {
      $segmentID = $segMembershipHash{$productID};
      $q_segmentName = $db->quote($segmentsHash{$segmentID});
      $prodUpdates{$productID} .= " `$segmentationName`=$q_segmentName,";
    }
  }

  #run through every attribute (including UPC)
  foreach $attributeID (keys %prodAttributeIDs)
  {
    $attributeName = $prodAttributeIDs{$attributeID};

    #get the product values for the current attribute
    %attributeValueHash = DSRattr_get_values_hash($db, $dsSchema, "p", $attributeID);

    #run through each product in the segmentation, and add the membership
    #to the product's UPDATE statement
    foreach $productID (keys %attributeValueHash)
    {
      $q_attributeVal = $db->quote($attributeValueHash{$productID});
      $prodUpdates{$productID} .= " `$attributeName`=$q_attributeVal,";
    }
  }


  #run through each product UPDATE statement, and apply it to the export table
  ODBC_telemetry($db, $odbcUpdateID, "Adding product name, UPC, attribute, and segment information");
  KAPutil_job_update_status($db, "Adding product segments/attributes");
  KAPutil_job_update_state($db, "UPDATE-PRODUCT-INFO");
  foreach $productID (keys %prodUpdates)
  {
    chop($prodUpdates{$productID});
    $query = "UPDATE $dsSchema._export_product SET $prodUpdates{$productID} \
        WHERE ID=$productID";
    $status = $odbcDB->do($query);
    KAPutil_handle_db_err($odbcDB, $status, $query);
  }

  #get hash of all geography segmentations
  ODBC_telemetry($db, $odbcUpdateID, "Preparing to add geography segment information");
  KAPutil_job_update_status($db, "Collecting segmentation info");
  KAPutil_job_update_state($db, "COLLECT-GEOGRAPHY-INFO");

  #run through every segmentation
  foreach $segID (keys %geoSegmentationIDs)
  {
    $segmentationName = $geoSegmentationIDs{$segID};

    #get the segment each product is a member of for the current segmentation
    %segMembershipHash = DSRseg_get_segment_membership_hash($db, $dsSchema, "g", $segID);

    #get the name of the segments in this segmentation, hashed by ID
    %segmentsHash = DSRseg_get_segments_hash($db, $dsSchema, "g", $segID);

    #run through each product in the segmentation, and add the membership
    #to the product's UPDATE statement
    foreach $geoID (keys %segMembershipHash)
    {
      $segmentID = $segMembershipHash{$geoID};
      $q_segmentName = $db->quote($segmentsHash{$segmentID});
      $geoUpdates{$geoID} .= " `$segmentationName`=$q_segmentName,";
    }
  }

  #run through each geography UPDATE statement, and apply it to the export table
  ODBC_telemetry($db, $odbcUpdateID, "Adding geography segment information");
  KAPutil_job_update_status($db, "Adding geography segments/attributes");
  KAPutil_job_update_state($db, "UPDATE-GEOGRAPHY-INFO");
  foreach $geoID (keys %geoUpdates)
  {
    chop($geoUpdates{$geoID});
    $query = "UPDATE $dsSchema._export_geography SET $geoUpdates{$geoID} \
        WHERE ID=$geoID";
    $status = $odbcDB->do($query);
    KAPutil_handle_db_err($odbcDB, $status, $query);
  }


  #get hash of all time period segmentations
  ODBC_telemetry($db, $odbcUpdateID, "Preparing to add time period segment information");
  KAPutil_job_update_status($db, "Collecting segmentation info");
  KAPutil_job_update_state($db, "COLLECT-TIME-INFO");

  #run through every segmentation
  foreach $segID (keys %timeSegmentationIDs)
  {
    $segmentationName = $timeSegmentationIDs{$segID};

    #get the segment each time is a member of for the current segmentation
    %segMembershipHash = DSRseg_get_segment_membership_hash($db, $dsSchema, "t", $segID);

    #get the name of the segments in this segmentation, hashed by ID
    %segmentsHash = DSRseg_get_segments_hash($db, $dsSchema, "t", $segID);

    #run through each product in the segmentation, and add the membership
    #to the time period's UPDATE statement
    foreach $timeID (keys %segMembershipHash)
    {
      $segmentID = $segMembershipHash{$timeID};
      $q_segmentName = $db->quote($segmentsHash{$segmentID});
      $timeUpdates{$timeID} .= " `$segmentationName`=$q_segmentName,";
    }
  }

  #run through each time period UPDATE statement, and apply it to the export table
  ODBC_telemetry($db, $odbcUpdateID, "Adding time segment information");
  KAPutil_job_update_status($db, "Adding time segments/attributes");
  KAPutil_job_update_state($db, "UPDATE-TIME-INFO");
  foreach $timeID (keys %timeUpdates)
  {
    chop($timeUpdates{$timeID});
    $query = "UPDATE $dsSchema._export_time SET $timeUpdates{$timeID} \
        WHERE ID=$timeID";
    $status = $odbcDB->do($query);
    KAPutil_handle_db_err($odbcDB, $status, $query);
  }


  ##########################################################

  #replace the old export tables with the working tables
  KAPutil_job_update_state($db, "REPLACE-EXPORT");
  KAPutil_job_update_status($db, "Replacing old export tables");

  KAPutil_db_delete_table($db, $dsSchema, "export");
  KAPutil_db_delete_table($db, $dsSchema, "export_product");
  KAPutil_db_delete_table($db, $dsSchema, "export_geography");
  KAPutil_db_delete_table($db, $dsSchema, "export_time");

  $query = "RENAME TABLE $dsSchema._export TO $dsSchema.export";
  $status = $odbcDB->do($query);
  KAPutil_handle_db_err($odbcDB, $status, $query);
  $query = "RENAME TABLE $dsSchema._export_product TO $dsSchema.export_product";
  $status = $odbcDB->do($query);
  KAPutil_handle_db_err($odbcDB, $status, $query);
  $query = "RENAME TABLE $dsSchema._export_geography TO $dsSchema.export_geography";
  $status = $odbcDB->do($query);
  KAPutil_handle_db_err($odbcDB, $status, $query);
  $query = "RENAME TABLE $dsSchema._export_time TO $dsSchema.export_time";
  $status = $odbcDB->do($query);
  KAPutil_handle_db_err($odbcDB, $status, $query);

  #reset permissions to the export tables
  $q_ODBCuser = $db->quote($ODBCuser);
  $q_ODBCpassword = $db->quote($ODBCpassword);
  $query = "CREATE USER IF NOT EXISTS $q_ODBCuser IDENTIFIED BY $q_ODBCpassword";
  $db->do($query);
  $query = "GRANT SELECT ON $dsSchema.export TO $q_ODBCuser";
  $status = $odbcDB->do($query);
  KAPutil_handle_db_err($odbcDB, $status, $query);
  $query = "GRANT SELECT ON $dsSchema.export_product TO $q_ODBCuser";
  $status = $odbcDB->do($query);
  KAPutil_handle_db_err($odbcDB, $status, $query);
  $query = "GRANT SELECT ON $dsSchema.export_geography TO $q_ODBCuser";
  $status = $odbcDB->do($query);
  KAPutil_handle_db_err($odbcDB, $status, $query);
  $query = "GRANT SELECT ON $dsSchema.export_time TO $q_ODBCuser";
  $status = $odbcDB->do($query);
  KAPutil_handle_db_err($odbcDB, $status, $query);

  #set the data source's ODBC export op status
  $query = "UPDATE dataSources SET ODBCstatus=NULL, ODBCexported='$ODBCexported' \
      WHERE ID=$dsID";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);

  $query = "UPDATE audit.telemetry_odbc \
      SET endTime=NOW(), telemetry = CONCAT(telemetry, NOW(), ': Finished export to ODBC\n') \
      WHERE ID=$odbcUpdateID";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);

  DSRutil_clear_status($db);

  $dsName = ds_id_to_name($db, $dsID);
  Social_feed_add_item($db, $dsOwnerID, $dsID, 0, 0, "success", "odbc_refresh");
  utils_slack("Refreshing star schema ODBC export for $dsName");
}





#-------------------------------------------------------------------------------


1;
