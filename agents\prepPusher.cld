#!/usr/bin/perl

use lib "/opt/apache/app/";

use DBI;
use Email::Valid;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepRecipes;
use Lib::PrepUtils;
use Lib::WebUtils;

my $debug;



#-------------------------------------------------------------------------
#
# Output debug data, if enabled

sub DBG
{
  my ($str) = @_;


  if ($debug == 1)
  {
    print STDERR "$str\n";
  }
}



#-------------------------------------------------------------------------

  #determine if we're being run manually
  $debug = 0;
  if ($ARGV[0] eq "debug")
  {
    $debug = 1;
  }

  #redirect STDERR/STDIN to the Koala error log
  if ($debug == 0)
  {
    close(STDERR);
    open(STDERR, ">>/opt/apache/htdocs/tmp/koala_error.log");
    select(STDERR);
    $| = 1;
  }

  #see if a lockfile exists.  If it does and the PID it contains is still
  #running, then exit.
  if ( -f "/opt/apache/app/agents/.prepPusher.lock")
  {
    $old_pid = `cat /opt/apache/app/agents/.prepPusher.lock`;
    chomp($old_pid);

    #cheat and check to see if there's a corresponding directory in /proc,
    #rather than go through the crap of getting and parsing ps output
    if ( -e "/proc/$old_pid")
    {
      exit;
    }
    else
    {
      unlink("/opt/apache/app/agents/.prepPusher.lock");
    }
  }

  #connect to the master database
  my $prepDB = PrepUtils_connect_to_database();

  system("echo $$ > /opt/apache/app/agents/.prepPusher.lock");

  #if the system is more than 75% loaded, back off
  $runningJobs = prep_running_jobs($prepDB);
  $loadRatio = $runningJobs / $Lib::KoalaConfig::prepCores;
  if ($loadRatio > 0.75)
  {
    DBG("Prep cloud at over 75% of capacity, waiting until later to push");
    exit;
  }

  #make sure the prep cloud isn't out of storage
  $usagePct = prep_flow_storage($acctType);
  if ($usagePct > 99)
  {
    DBG("Prep cloud out of storage, not pushing anything");
    exit;
  }

  #grab a list of every interactive job on the system that isn't running/done
  $query = "SELECT ID, flowID, userID, opInfo, state, UNIX_TIMESTAMP(NOW()) - UNIX_TIMESTAMP(lastAction) \
      FROM prep.jobs \
      WHERE ISNULL(PID) AND state != 'LOADED' AND mode='interactive' ORDER BY ID";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;

  #build up a multi-dimensional array containing the data fields we need
  #NB: For a variety of reasons, we want to do all of our DB work before we
  #    start forking off a bunch of processes
  $idx = 0;
  while (($jobID, $flowID, $userID, $opInfo, $state, $lastAction) = $dbOutput->fetchrow_array)
  {
    $jobs[$idx][0] = $jobID;
    $jobs[$idx][1] = $flowID;
    $jobs[$idx][2] = $userID;
    $jobs[$idx][3] = $opInfo;
    $jobs[$idx][4] = $state;
    $jobs[$idx][5] = $lastAction;

    $idx++;
  }

  #have the system auto-reap any child processes we fork off
  $SIG{CHLD} = 'IGNORE';

  #run through each job, and push it forward to the next step in the process
  foreach $job (@jobs)
  {
    $jobID = $job->[0];
    $flowID = $job->[1];
    $userID = $job->[2];
    $opInfo = $job->[3];
    $state = $job->[4];
    $lastAction = $job->[5];

    DBG("$jobID, $flowID, $userID, $opInfo, $state, $lastAction");

    #if the current job hasn't been idle for at least 5 minutes, move on
    if ($lastAction < 300)
    {
      DBG("$jobID: Job hasn't been idle for 5 minutes, skipping");
      next;
    }

    #if the opInfo field doesn't indicate that processing is done
    if (!($opInfo =~ m/^DONE.*/))
    {
      DBG("$jobID: opInfo says we aren't done ($opInfo)");
      next;
    }

    #if the job is in a state we know how to push
    if (($state eq "LOAD-FTP") || ($state eq 'EXTRACT-DATA') ||
       ($state eq "NESTED-CONVERT") || ($state eq 'LOAD-DATA') ||
       ($state eq "MERGE-DATA") || ($state eq "COLUMN-TYPES") ||
       ($state eq "RECIPE-APPLY"))
    {

      #split off a child process to run the next step in the flow
      if ($pid = fork())
      {
        #parent process - do nothing and continue processing jobs
      }

      #else we're the child process
      else
      {
        my $childDB = PrepUtils_connect_to_database();

        #make sure cron knows not to hang around waiting for children to exit
        close(STDIN);
        close(STDOUT);

        #if the job's current state is LOAD-FTP, next step is to extract
        if ($state eq "LOAD-FTP")
        {
          #expand any zip archives included in the data upload
          DBG("$jobID: Expanding any uploaded zips");
          prep_flow_expand_zips($childDB, $jobID, $userID, $jobID);

          #run through every file in the upload director, looking for files
          #uploaded by our user that need to have data extracted, then add all
          #the files to the prep.files table
          DBG("$jobID: Extracting data from uploaded files");
          my $kapDB = KAPutil_connect_to_database();
          prep_flow_extract_file_data($childDB, $kapDB, $flowID, $jobID, $userID, $jobID);
          exit;
        }

        #if the job's current state is EXTRACT-DATA, next step is to handle
        #nested layouts
        if ($state eq "EXTRACT-DATA")
        {
          #normalize nested layouts
          DBG("$jobID: Detecting and normalizing nested layouts");
          my $kapDB = KAPutil_connect_to_database();
          prep_flow_nested_to_tabular($childDB, $kapDB, $flowID, $jobID, $userID);
          exit;
        }

        #if the job's current state is NESTED-CONVERT, next step is to get
        #parsing settings from the user
        if ($state eq "NESTED-CONVERT")
        {
          DBG("$jobID: Getting parse settings from user");
          $query = "UPDATE prep.jobs SET state='PARSE-WAIT' WHERE ID=$jobID";
          $childDB->do($query);
          exit;
        }

        #if the job's current state is LOAD-DATA, next step is to get file
        #types from user
        if ($state eq "LOAD-DATA")
        {
          DBG("$jobID: Getting data types from user");
          $query = "UPDATE prep.jobs SET state='DATATYPE-WAIT' WHERE ID=$jobID";
          $childDB->do($query);
          exit;
        }

        #if the job's current state is MERGE-DATA, next step is to detect
        #and adjust column types
        if ($state eq "MERGE-DATA")
        {
          #normalize nested layouts
          DBG("$jobID: Detecting and adjusting column types");
          my $kapDB = KAPutil_connect_to_database();
          prep_flow_detect_column_types($childDB, $flowID, $jobID, $userID);
          exit;
        }

        #if the job's current state is COLUMN-TYPES, next step is to apply
        #any applicable recipe steps
        if ($state eq "COLUMN-TYPES")
        {
          #normalize nested layouts
          DBG("$jobID: Applying recipe steps");
          my $kapDB = KAPutil_connect_to_database();
          prep_recipe_apply($childDB, $kapDB, $flowID, $jobID);
          exit;
        }

        #if the job's current state is RECIPE-APPLY, next step is to apply
        #validation rules
        if ($state eq "RECIPE-APPLY")
        {
          #normalize nested layouts
          DBG("$jobID: Applying validation rules");
          my $kapDB = KAPutil_connect_to_database();
          prep_flow_validate($childDB, $kapDB, $flowID, $jobID, 0);
          exit;
        }

        #we shouldn't ever make it this far, but go ahead and exit if we did
        exit;
      }
    }
  }

  close(STDIN);
  close(STDOUT);


#EOF
