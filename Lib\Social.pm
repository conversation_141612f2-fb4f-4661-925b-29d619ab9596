package Lib::Social;

use lib "/opt/apache/app/";

use Exporter;


our @ISA = ('Exporter');

our @EXPORT = qw(
    &Social_feed_add_item
    &Social_clear_ds_items
    &Social_clear_seg_items
    &Social_clear_agg_items
    &Social_clear_list_items
    &Social_clear_report_items
    &Social_clear_prep_parse_wait_items
    &Social_clear_prep_datatype_wait_items
    &Social_clear_prep_export_ready_items
  );




#-------------------------------------------------------------------------
#
# Handle a database error of some kind during a utility function call
#

sub social_db_err
{
  my ($date);

  my ($db, $status, $text) = @_;


  if (!defined($status))
  {
    $date = localtime();
    print STDERR "$date: $text\n";
    if ($db->errstr =~ m/^MySQL server has gone away/)
    {
      die("Lost connection to database, terminating");
    }
  }
}



#-------------------------------------------------------------------------
#
# Add an item to a user's information feed.
#

sub Social_feed_add_item
{
  my ($query, $status, $dbOutput, $q_content);

  my ($db, $userID, $dsID, $cubeID, $flowID, $class, $type, $content) = @_;


  if ($dsID < 1)
  {
    $dsID = "NULL";
  }
  if ($cubeID < 1)
  {
    $cubeID = "NULL";
  }
  if ($flowID < 1)
  {
    $flowID = "NULL";
  }
  $q_content = $db->quote($content);

  #make sure we're not adding duplicate entries for a variety of feed types
  if (($type eq "rpt_missing_dim") || ($type eq "rpt_size") ||
      ($type eq "rpt_manual_refresh"))
  {
    $query = "SELECT ID FROM app.feed \
        WHERE userID=$userID AND cubeID=$cubeID AND type='$type'";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    social_db_err($db, $status, $query);
    if ($status > 0)
    {
      return;
    }
  }

  #Data Prep items that are flowID-dependent for duplicates
  if (($type eq "prep_flow_idw_schedule") || ($type eq "prep_flow_idw_trim_bccat") ||
      ($type eq "prep_flow_parse_wait") || ($type eq "prep_flow_datatype_wait") ||
      ($type eq "prep_flow_export_ready"))
  {
    $query = "SELECT ID FROM app.feed \
        WHERE userID=$userID AND flowID=$flowID AND type='$type'";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    social_db_err($db, $status, $query);
    if ($status > 0)
    {
      return;
    }
  }

  #data source items that are dsID-dependent for duplicates
  if (($type eq "ds_no_updates3") || ($type eq "odbc_needs_update") ||
      ($type eq "odbc_large_tabular"))
  {
    $query = "SELECT ID FROM app.feed \
        WHERE userID=$userID AND dsID=$dsID AND type='$type'";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    social_db_err($db, $status, $query);
    if ($status > 0)
    {
      return;
    }
  }

  #data source items that are content-dependent for duplicates
  if (($type eq "ds_empty_list") || ($type eq "ds_empty_agg") ||
      ($type eq "ds_unsegmented") || ($type eq "ds_undef_meas_agg"))
  {
    $query = "SELECT ID FROM app.feed \
        WHERE userID=$userID AND dsID=$dsID AND type='$type' AND content=$q_content";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    social_db_err($db, $status, $query);
    if ($status > 0)
    {
      return;
    }
  }

  #clear any previous report refresh notifications that would just be dupes
  if ($type eq "rpt_update")
  {
    $query = "DELETE FROM app.feed WHERE type='rpt_update' AND cubeID=$cubeID";
    $db->do($query);
  }
  if ($type eq "rpt_update_agent")
  {
    $query = "DELETE FROM app.feed \
        WHERE (type='rpt_update' OR type = 'rpt_update_agent') AND dsID=$dsID";
    $db->do($query);
  }

  $query = "INSERT INTO app.feed \
      (userID, insertDate, dsID, cubeID, flowID, class, type, content) \
      VALUES ($userID, NOW(), $dsID, $cubeID, $flowID, '$class', '$type', $q_content)";
  $status = $db->do($query);
  social_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------
#
# Clear all error/warn/info messages for a specific data source from a user's
# feed. Should be called when a DS is updated to clear any items that might not
# be relevant after the DS update (if they are, they'll be auto-created again).
#
# Does not clear "success" items, like previous updates.
#

sub Social_clear_ds_items
{
  my ($query, $status);

  my ($db, $dsID) = @_;


  $query = "DELETE FROM app.feed \
      WHERE dsID=$dsID AND type IN ('ds_unsegmented', 'ds_empty_list', 'ds_empty_agg', 'ds_no_updates3')";
  $status = $db->do($query);
  social_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------
#
# Clear all error/warn/info messages for a specific segmentation from a user's
# feed. Should be called when a segmentation is edited to clear any items that
# might not be relevant after the edit.
#
#

sub Social_clear_seg_items
{
  my ($query, $status, $dbOutput);

  my ($db, $dsID, $segName) = @_;


  $query = "DELETE FROM app.feed WHERE dsID=$dsID AND \
              type IN ('ds_unsegmented') AND content LIKE '%$segName%'";
  $status = $db->do($query);
  social_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------
#
# Clear all error/warn/info messages for a specific aggregate from a user's
# feed. Should be called when an aggregate is edited to clear any items that
# might not be relevant after the edit.
#
#

sub Social_clear_agg_items
{
  my ($query, $status, $dbOutput);

  my ($db, $dsID, $aggName) = @_;


  $query = "DELETE FROM app.feed WHERE dsID=$dsID AND \
              type IN ('ds_empty_agg') AND content LIKE '%$aggName%'";
  $status = $db->do($query);
  social_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------
#
# Clear all error/warn/info messages for a specific list from a user's
# feed. Should be called when a list is edited to clear any items that
# might not be relevant after the edit.
#
#

sub Social_clear_list_items
{
  my ($query, $status, $dbOutput);

  my ($db, $dsID, $listName) = @_;


  $query = "DELETE FROM app.feed WHERE dsID=$dsID AND \
              type IN ('ds_empty_list') AND content LIKE '%$listName%'";
  $status = $db->do($query);
  social_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------
#
# Clear all items associated with a specific report from the user's feed.
# Should be called just before a report is refreshed to clear any items that
# might not be relevant after the report refreshes. (If they are, the refresh
# will put them back in place.)
#
# Does not clear rpt_update/rpt_update_agent items.
#

sub Social_clear_report_items
{
  my ($query, $status);

  my ($db, $cubeID) = @_;


  $query = "DELETE FROM app.feed WHERE cubeID=$cubeID AND \
              type IN ('rpt_missing_dim', 'rpt_size', 'rpt_manual_refresh')";
  $status = $db->do($query);
  social_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------
#
# Clear items notifying the user that they have a Data Prep job waiting for
# data parsing input.
#

sub Social_clear_prep_parse_wait_items
{
  my ($query, $status);

  my ($db, $flowID) = @_;


  $query = "DELETE FROM app.feed WHERE flowID=$flowID AND \
              type IN ('prep_flow_parse_wait')";
  $status = $db->do($query);
  social_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------
#
# Clear items notifying the user that they have a Data Prep job waiting for
# data type [data|lookup|ignore] input.
#

sub Social_clear_prep_datatype_wait_items
{
  my ($query, $status);

  my ($db, $flowID) = @_;


  $query = "DELETE FROM app.feed WHERE flowID=$flowID AND \
              type IN ('prep_flow_datatype_wait')";
  $status = $db->do($query);
  social_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------
#
# Clear items notifying the user that they have a Data Prep job waiting for
# export to Koala Analytics.
#

sub Social_clear_prep_export_ready_items
{
  my ($query, $status);

  my ($db, $flowID) = @_;


  $query = "DELETE FROM app.feed WHERE flowID=$flowID AND \
              type IN ('prep_flow_export_ready')";
  $status = $db->do($query);
  social_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------


1;
