#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DataSel;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::Reports;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $rptID = $q->param('rptID');
  $action = $q->param('action');
  $lockVisuals = $q->param('lockVisuals');

  $db = KAPutil_connect_to_database();

  #make sure we have edit privs for this report
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this report.");
  }


  ########################################################################
  #
  # This code block is called on submit to save the changes
  #

  if ($action eq "s")
  {
    $lockVisuals = ($lockVisuals eq "true") ? 1 : 0;

    $query = "UPDATE cubes SET lockVisuals=$lockVisuals WHERE ID=$rptID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    $cubeName = cube_id_to_name($db, $rptID);
    $dsID = cube_get_ds_id($db, $rptID);
    $dsName = ds_id_to_name($db, $dsID);

    utils_audit($db, $userID, "Locked/unlocked visuals", $dsID, $rptID, 0);
    $activity = "$first $last locked/unlocked visuals for $cubeName in $dsName";
    utils_slack($activity);

    exit;
  }


  #########################################################################
  #
  # Everything after this point is called to display the locked measure dialog
  #

  $query = "SELECT lockVisuals FROM cubes WHERE ID=$rptID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($lockVisuals) = $dbOutput->fetchrow_array;

  if ($lockVisuals == 1)
  {
    $checked = "CHECKED";
  }

  print <<END_HTML;
<SCRIPT>
function submitForm()
{
  let lockVisuals = \$("#lockVisuals").prop("checked");

  let url = "xhrLockVisuals?rptID=$rptID&action=s&lockVisuals=" + lockVisuals;

  \$.get(url, function(data, status)
  {
    location.href = "display.cld?rpt=$rptID";
  });
}
</SCRIPT>

<DIV CLASS="modal-dialog" STYLE="overflow-y: initial !important;">
  <DIV CLASS="modal-content">
    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Lock Visuals</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">

      <FORM>
      <DIV CLASS="form-check">
        <INPUT CLASS="form-check-input" NAME="lockVisuals" ID="lockVisuals" TYPE="checkbox" $checked>
        <LABEL CLASS="form-check-label" FOR="lockVisuals">Lock position and size of visuals in this report</LABEL>
      </DIV>
      </FORM>

    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="submitForm()"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML

#EOF
