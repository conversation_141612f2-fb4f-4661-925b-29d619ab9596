#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::BuildCube;
use Lib::Cubes;
use Lib::DataSel;
use Lib::DSRMeasures;
use Lib::Reports;
use Lib::WebUtils;
use Lib::DSRUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if ((length($email) < 1) && ($local != 1))
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------
#
#

sub generateSortSQLElement
{

  my ($db, $sortMeas, $sortOrder) = @_;

  if ($sortMeas =~ /^\d+$/)
  {
    $sortMeas = "measure_" . $sortMeas;
  }
  elsif ($sortMeas eq "time")
  {
    $sortMeas = "sortTime";
  }

  $sortElement =  "$sortMeas $sortOrder";

  return($sortElement);
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #get the CGI input variables
  $rptID = $q->param('rpt');
  $visID = $q->param('v');
  $loadAll = $q->param('loadAll');

  #get any "fixed" dimensions (used for expanded reports)
  $fProd = $q->param('p');
  $fGeo = $q->param('g');
  $fTime = $q->param('t');
  $local = $q->param('l');

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #output Content-type header
  print($session->header());

  $db = KAPutil_connect_to_database();

  #see what sort of rights we have to the cube underlying this report
  #NB: This'll only get called if somebody is being cute and tries mucking
  #    with the CGI parameters
  if ($local != 1)
  {
    $privs = cube_rights($db, $userID, $rptID, $acctType);
    if ($privs eq "N")
    {
      exit_error("You don't have privileges on this report.");
    }
  }

  #if we're being called as part of a PPT export
  if ($userID < 1)
  {
    $userID = $q->param('u');
    $query = "SELECT acctType FROM app.users WHERE ID=$userID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($acctType) = $dbOutput->fetchrow_array;
  }

  #get info about the table from the cubes database
  $query = "SELECT name, dsID, measures, slicers FROM cubes WHERE ID=$rptID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($name, $dsID, $measuresString, $slicersStr) = $dbOutput->fetchrow_array;

  #get info about this particular table visual
  $query = "SELECT tableRowDims, tableColDims, design FROM visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($rowDim, $colDim, $tableDesign) = $dbOutput->fetchrow_array;

  ($selProductsString, $selGeographiesString, $selTimesString, $selMeasuresString) = reports_get_selected_items($db, $visID, $userID, $acctType);

  #our filter dimensions are everything not in a row
  undef(@filterDims);
  if (!($rowDim =~ m/p/))
  {
    push(@filterDims, "p");
  }
  if (!($rowDim =~ m/g/))
  {
    push(@filterDims, "g");
  }
  if (!($rowDim =~ m/t/))
  {
    push(@filterDims, "t");
  }

  #assemble datasource and cube names
  $dsSchema = "datasource_" . $dsID;
  $rptCube = "_rptcube_" . $rptID;

  #if we're dealing with a fixed dimension (used for expanded reports)
  if (length($fProd) > 0)
  {
    $selProductsString = $fProd;
  }
  if (length($fGeo) > 0)
  {
    $selGeographiesString = $fGeo;
  }
  if (length($fTime) > 0)
  {
    $selTimesString = $fTime;
  }

  #turn the available item IDs into an array, selected items into hash
  @dimProducts = datasel_get_dimension_items($db, $rptID, "p");
  undef(@temp);
  @temp = split(/,/, $selProductsString);
  undef(%selProducts);
  foreach $item (@temp)
  {
    $selProducts{$item} = 1;
  }

  @dimGeographies = datasel_get_dimension_items($db, $rptID, "g");
  undef(@temp);
  @temp = split(/,/, $selGeographiesString);
  undef(%selGeographies);
  foreach $item (@temp)
  {
    $selGeographies{$item} = 1;
  }

  @dimTimes = datasel_get_dimension_items($db, $rptID, "t");
  undef(@temp);
  @temp = split(/,/, $selTimesString);
  undef(%selTimes);
  foreach $item (@temp)
  {
    $selTimes{$item} = 1;
  }

  @dimMeasures = datasel_get_dimension_items($db, $rptID, "m");

  #figure out which measures we need to display, being careful to keep them
  #in the order the user selected them in the data selector
  undef(@dispMeasures);
  @temp = split(/,/, $selMeasuresString);
  @cubeMeasures = split(/,/, $measuresString);
  undef(%selMeasures);
  foreach $item (@temp)
  {
    $selMeasures{$item} = 1;
  }
  foreach $cubeMeasure (@cubeMeasures)
  {
    if ($selMeasures{$cubeMeasure} == 1)
    {
      push(@dispMeasures, $cubeMeasure);
    }
  }

  #get the names matching the IDs for dimension items
  %productNameHash = dsr_get_item_name_hash($db, $dsSchema, "p");
  %geographyNameHash = dsr_get_item_name_hash($db, $dsSchema, "g");
  %timeNameHash = dsr_get_item_name_hash($db, $dsSchema, "t");
  %measureNameHash = dsr_get_item_name_hash($db, $dsSchema, "m");


#-------------------------------------------------------------------------------
#
# Output table
#

  print <<END_HTML;
<TABLE STYLE="width:100%;">
 <TR>
  <TD VALIGN="top">
END_HTML

  #for simpler regexes, make sure there's a comma on the end of selection
  #strings
  $selProductsStr .= ",";
  $selGeographiesString .= ",";
  $selTimesString .= ",";

  #build up the measure portion of the SQL query string
  $measureCol = "";
  foreach $measureID (@dispMeasures)
  {
    if (($measureID =~ m/ATT_/) || ($measureID =~ m/SEG_/))
    {
      $measureCol .= $measureID . ",";
    }
    else
    {
      $measureCol .= "measure_" . $measureID . ",";
    }
  }
  chop($measureCol);
  $measDisplayIDs = join(',', @dispMeasures);

  #if the user hasn't selected any measures
  if (length($measureCol) < 1)
  {
    print("<TR><TD NOWRAP STYLE='color:red; font-weight:bold;'>No measures selected</TD></TR></TABLE>\n");
    exit;
  }

  #add the dimensions we're displaying as rows to the list of data fields to
  #be selected
  @rowDims = split(',', $rowDim);
  $dimSelStr = "";
  foreach $dim (@rowDims)
  {
    if ($dim eq "p")
    {
     $dimSelStr .= "product,";
    }
    if ($dim eq "g")
    {
     $dimSelStr .= "geography,";
    }
    if ($dim eq "t")
    {
     $dimSelStr .= "time,";
    }
  }
  $measureCol = $dimSelStr . $measureCol;

#XXX Hackety hack
$rowDim = $rowDims[0];
#XXX

  #start by grabbing the first user-selected item in each dimension
  #(we're going to expand out the strings for each of our row dimensions later)
  @tmp = split(',', $selProductsString);
  $prodDisplayIDs = "\'$tmp[0]\'";
  @tmp = split(',', $selGeographiesString);
  $geoDisplayIDs = "\'$tmp[0]\'";
  @tmp = split(',', $selTimesString);
  $timeDisplayIDs = "\'$tmp[0]\'";

  #if we have an empty dimension from the jump
  if (($prodDisplayIDs eq "''") || ($geoDisplayIDs eq "''") ||
      ($timeDisplayIDs eq "''"))
  {
    print("<TR><TD NOWRAP STYLE='color:red; font-weight:bold;'>No data to display</TD></TR></TABLE>\n");
    exit;
  }

  #foreach row & column dimension, expand its selection string
  undef(@tmp);
  push(@tmp, @rowDims);
  push(@tmp, $colDim);
  foreach $dim (@tmp)
  {
    if ($dim eq "p")
    {
      undef($prodDisplayIDs);
      foreach $item (@dimProducts)
      {
        if ($selProducts{$item} == 1)
        {
          $prodDisplayIDs .= "\'$item\',";
        }
      }
      chop($prodDisplayIDs);
    }
    elsif ($dim eq "g")
    {
      undef($geoDisplayIDs);
      foreach $item (@dimGeographies)
      {
        if ($selGeographies{$item} == 1)
        {
          $geoDisplayIDs .= "\'$item\',";
        }
      }
      chop($geoDisplayIDs);
    }
    elsif ($dim eq "t")
    {
      undef($timeDisplayIDs);
      foreach $item (@dimTimes)
      {
        if ($selTimes{$item} == 1)
        {
          $timeDisplayIDs .= "\'$item\',";
        }
      }
      chop($timeDisplayIDs);
    }
  }

  #handle a case where the user has selected a combination of dimensions that
  #has no data to display for a row or column
  if ((length($prodDisplayIDs) < 1) || (length($geoDisplayIDs) < 1) ||
      (length($timeDisplayIDs) < 1))
  {
    print("<TR><TD NOWRAP STYLE='color:red; font-weight:bold;'>No data to display</TD></TR></TABLE>\n");
    exit;
  }

  #NB: For top/bottom filtering, we need to use only one item for non-displayed
  #    dimensions
  $topBottomProdIDs = $prodDisplayIDs;
  $topBottomGeoIDs = $geoDisplayIDs;
  $topBottomTimeIDs = $timeDisplayIDs;

  foreach $d (@filterDims)
  {
    if ($d eq "p")
    {
      if ($topBottomProdIDs =~ m/^(.*?),/)
      {
        $topBottomProdIDs = $1;
      }
    }
    elsif ($d eq "g")
    {
      if ($topBottomGeoIDs =~ m/^(.*?),/)
      {
        $topBottomGeoIDs = $1;
      }
    }
    elsif ($d eq "t")
    {
      if ($topBottomTimeIDs =~ m/^(.*?),/)
      {
        $topBottomTimeIDs = $1;
      }
    }
  }

  #NB: we're going to cheat and use the same IDs for the table item selections
  #    in a bit
  $tableProdItemIDs = $topBottomProdIDs;
  $tableGeoItemIDs = $topBottomGeoIDs;
  $tableTimeItemIDs = $topBottomTimeIDs;

  #assemble the WHERE portion of our SQL query to retrieve table data
  $whereClause = "product IN ($tableProdItemIDs) AND geography IN ($tableGeoItemIDs) AND time IN ($tableTimeItemIDs)";

  #implement table filtering by adding additional clauses to the WHERE portion
  $filterMeas1 = reports_get_style($tableDesign, "filterMeas1");
  $filterMeas2 = reports_get_style($tableDesign, "filterMeas2");
  $filterMeas3 = reports_get_style($tableDesign, "filterMeas3");
  $filterOp1 = reports_get_style($tableDesign, "filterOp1");
  $filterOp2 = reports_get_style($tableDesign, "filterOp2");
  $filterOp3 = reports_get_style($tableDesign, "filterOp3");
  $filterNum1 = reports_get_style($tableDesign, "filterNum1");
  $filterNum2 = reports_get_style($tableDesign, "filterNum2");
  $filterNum3 = reports_get_style($tableDesign, "filterNum3");
  $excludeNA = reports_get_style($tableDesign, "excludeNA");

  undef($hardLimit);

  #make sure the measure the rule depends on exists, or inactivate it
  if (length($measureNameHash{$filterMeas1}) < 1)
  {
    $filterMeas1 = 0;
  }
  if (length($measureNameHash{$filterMeas2}) < 1)
  {
    $filterMeas2 = 0;
  }
  if (length($measureNameHash{$filterMeas3}) < 1)
  {
    $filterMeas3 = 0;
  }

  #gate the number of top/bottom items we're going to show - refactor this
  if (($filterOp1 eq "bottom") || ($filterOp1 eq "top"))
  {
    if ($filterNum1 > 5000)
    {
      $filterNum1 = 5000;
    }
    elsif ($filterNum1 < 0)
    {
      $filterNum1 = 0;
    }
    $hardLimit = $filterNum1;
  }
  if (($filterOp2 eq "bottom") || ($filterOp2 eq "top"))
  {
    if ($filterNum2 > 5000)
    {
      $filterNum2 = 5000;
    }
    elsif ($filterNum2 < 0)
    {
      $filterNum1 = 0;
    }
    $hardLimit = $filterNum2;
  }
  if (($filterOp3 eq "bottom") || ($filterOp3 eq "top"))
  {
    if ($filterNum3 > 5000)
    {
      $filterNum3 = 5000;
    }
    elsif ($filterNum3 < 0)
    {
      $filterNum3 = 0;
    }
    $hardLimit = $filterNum3;
  }

  #append filtering clauses to master WHERE clause, if appropriate
  $whereClause = reports_table_filter_SQL($db, $dsSchema, $rptCube, $whereClause,
      $filterMeas1, $filterOp1, $filterNum1,
      $topBottomProdIDs, $topBottomGeoIDs, $topBottomTimeIDs);
  $whereClause = reports_table_filter_SQL($db, $dsSchema, $rptCube, $whereClause,
      $filterMeas2, $filterOp2, $filterNum2,
      $topBottomProdIDs, $topBottomGeoIDs, $topBottomTimeIDs);
  $whereClause = reports_table_filter_SQL($db, $dsSchema, $rptCube, $whereClause,
      $filterMeas3, $filterOp3, $filterNum3,
      $topBottomProdIDs, $topBottomGeoIDs, $topBottomTimeIDs);

  #handle an "Exclude NA and 0" filtering directive
  if ($excludeNA == 1)
  {
    $primaryDim = $rowDims[0];
    $whereClause = reports_table_excludeNA_SQL($db, $dsSchema, $rptCube, $whereClause,
        $primaryDim, $prodDisplayIDs, $geoDisplayIDs, $timeDisplayIDs, @dispMeasures);
  }

  #if the user has specified a slicer, let's add it to the where clause
  @slicers = split(',', $slicersStr);
  foreach $slicer (@slicers)
  {
    $slicer =~ m/(PSEG_\d+):(\d+)/;
    $segID = $1;
    $segmentID = $2;

    if ($segmentID > 0)
    {
      $tmp = "SMT_$segmentID";
      $segmentName = $db->quote($productNameHash{$tmp});
      $whereClause .= " AND $segID = $segmentName";
    }
  }

  #handle the ORDER BY portion of our SQL query, start by looking for an
  #explicit user-defined sort order we should be using
  $sortMeas1 = reports_get_style($tableDesign, "sortMeas1");
  $sortMeas2 = reports_get_style($tableDesign, "sortMeas2");
  $sortMeas3 = reports_get_style($tableDesign, "sortMeas3");
  $sortOrder1 = reports_get_style($tableDesign, "sortOrder1");
  $sortOrder2 = reports_get_style($tableDesign, "sortOrder2");
  $sortOrder3 = reports_get_style($tableDesign, "sortOrder3");

  $dimCount = @rowDims;

  $orderByClause = "";

  #if the first sorting criteria is specified
  if (($sortMeas1 > 0) || (length($sortMeas1) > 1))
  {
    $orderByClause = "ORDER BY ";

    $sortElement = generateSortSQLElement($db, $sortMeas1, $sortOrder1);
    if (length($sortElement) > 0)
    {
      $orderByClause .= "$sortElement ";
    }

    #if the second sorting criteria is specified
    if (($sortMeas2 > 0) || (length($sortMeas2) > 1))
    {

      #figure out if our fully-qualified measure ID is a financial measure
      if ($sortMeas2 =~ /^\d+$/)
      {
        $sortMeas2 = "measure_" . $sortMeas2;
      }
      elsif ($sortMeas2 eq "time")
      {
        $sortMeas2 = "sortTime";
      }

      $orderByClause .= ", $sortMeas2 $sortOrder2 ";

      #if the third sorting criteria is specified
      if (($sortMeas3 > 0) || (length($sortMeas3) > 1))
      {

        #figure out our fully-qualified measure ID if a financial measure
        if ($sortMeas3 =~ /^\d+$/)
        {
          $sortMeas3 = "measure_" . $sortMeas3;
        }
        elsif ($sortMeas3 eq "time")
        {
          $sortMeas3 = "sortTime";
        }

        $orderByClause .= ", $sortMeas3 $sortOrder3 ";
      }
    }
  }

  #elsif we're displaying a multi-dimension pivot table
  elsif ($dimCount > 1)
  {
    $orderByClause = "ORDER BY ";
    foreach $dim (@rowDims)
    {
      if ($dim eq "p")
      {
        $orderByClause .= "FIELD(product, $prodDisplayIDs),";
      }
      elsif ($dim eq "g")
      {
        $orderByClause .= "FIELD(geography, $geoDisplayIDs),";
      }
      if ($dim eq "t")
      {
        $orderByClause .= "sortTime DESC,";
      }
    }
    chop($orderByClause);
  }

  #elsif the time dimension is the only row dimension
  elsif ($rowDims[0] eq "t")
  {
    $orderByClause = "ORDER BY sortTime DESC";
  }

  #else order by the primary dimension in the order the user selected
  else
  {
    if ($rowDim eq "p")
    {
      $orderByClause = "ORDER BY FIELD(product, $prodDisplayIDs)";
    }
    elsif ($rowDim eq "g")
    {
      $orderByClause = "ORDER BY FIELD(geography, $geoDisplayIDs)";
    }
    elsif ($rowDim eq "t")
    {
      $orderByClause = "ORDER BY FIELD(time, $timeDisplayIDs)";
    }
  }

  #determine what our LIMIT clause should be - by default, we only load the
  #first 5000 rows of the table to keep the browser from crashing. If the user
  #says so, though, we'll load it all. And if there's a "hard limit" set by
  #a user-defined filter, we'll take that uber alles
  if (defined($hardLimit))
  {
    $limitClause = "LIMIT $hardLimit";
  }
  elsif ($loadAll == 1)
  {
    $limitClause ="";
  }
  else
  {
    $limitClause = "LIMIT 5000";
  }

  #grab the rows we're going to display, in order (we need this to do nested
  #columns)
  $dimCol = "";
  foreach $dim (@rowDims)
  {
    if ($dim eq "p")
    {
      $dimCol .= "product,";
    }
    if ($dim eq "g")
    {
      $dimCol .= "geography,";
    }
    if ($dim eq "t")
    {
      $dimCol .= "time,";
    }
  }
  chop($dimCol);

  $query = "SELECT $dimCol FROM $dsSchema.$rptCube \
      WHERE $whereClause $orderByClause $limitClause";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  (@rowVals) = $dbOutput->fetchrow_array;

  #determine if we have more than 5000 rows in the report
  if (($status > 4999) && ($loadAll != 1))
  {
    $tablePartialDisplay = 1;
  }
  else
  {
    $tablePartialDisplay = 0;
  }

  #build up the in-order selection strings for our table
  $str1 = "";
  $str2 = "";
  $str3 = "";
  while (defined($rowVals[0]))
  {
    $str1 .= $rowVals[0] . ",";
    $str2 .= $rowVals[1] . ",";
    $str3 .= $rowVals[2] . ",";
    (@rowVals) = $dbOutput->fetchrow_array;
  }
  chop($str1);
  chop($str2);
  chop($str3);

  #build up dimension ID strings that we're going to use for comparison with
  #the SQL CONCAT operator. Ex: if a row is products and geos, and one such
  #row has the IDs AGG_1 for prod and 7 for geo, the string will be AGG_1-7
  #NB: we can be blind about which dimension is which - we just care about
  #    IDs at this point. We'll sort it out later when we build the CONCAT
  #    portion of the SQL query
  undef(@concatIDstrings);
  if (defined($rowDims[0]))
  {
    @tmp = split(',', $str1);
    $idx = 0;
    foreach $id (@tmp)
    {
      $concatIDstrings[$idx] = $tmp[$idx];
      $idx++;
    }
  }
  if (defined($rowDims[1]))
  {
    @tmp = split(',', $str2);
    $idx = 0;
    foreach $id (@tmp)
    {
      $concatIDstrings[$idx] .= "-" . $tmp[$idx];
      $idx++;
    }
  }
  if (defined($rowDims[2]))
  {
    @tmp = split(',', $str3);
    $idx = 0;
    foreach $id (@tmp)
    {
      $concatIDstrings[$idx] .= "-" . $tmp[$idx];
      $idx++;
    }
  }

  #build the concat test string
  $concatIDstr = "";
  foreach $str (@concatIDstrings)
  {
    $concatIDstr .= "'$str',";
  }
  chop($concatIDstr);

  $tableBorderStyle = "";
  $tableShowBorder = reports_get_style($tableDesign, "showBorder");
  if ($tableShowBorder == 1)
  {
    $borderColor = reports_get_style($tableDesign, "borderColor");
    if (length($borderColor) < 7)
    {
      $borderColor = "#333333";
    }
    $borderThickness = reports_get_style($tableDesign, "borderThickness");
    if ($borderThickness < 1)
    {
      $borderThickness = 1;
    }
    $tableBorderStyle = $borderThickness . "px solid $borderColor";
  }

  $verticalGridColor = reports_get_style($tableDesign, "verticalGridColor");
  if (length($verticalGridColor) < 7)
  {
    $verticalGridColor = "#ffffff";
  }

  $verticalGridWidth = reports_get_style($tableDesign, "verticalGridWidth");
  if (length($verticalGridWidth) < 1)
  {
    $verticalGridWidth = "1";
  }
  $verticalGridWidth .= "px";

  $horizontalGridColor = reports_get_style($tableDesign, "horizontalGridColor");
  if (length($horizontalGridColor) < 7)
  {
    $horizontalGridColor = "#ffffff";
  }

  $horizontalGridWidth = reports_get_style($tableDesign, "horizontalGridWidth");
  if (length($horizontalGridWidth) < 1)
  {
    $horizontalGridWidth = "1";
  }
  $horizontalGridWidth .= "px";

  $gridVerticalStyle = "$verticalGridWidth solid $verticalGridColor";
  $gridHorizontalStyle = "$horizontalGridWidth solid $horizontalGridColor";

  #turn off grids if that's what the analyst wants
  $verticalGrid = reports_get_style($tableDesign, "verticalGrid");
  if ($verticalGrid == 0)
  {
    $gridVerticalStyle = "";
  }
  $horizontalGrid = reports_get_style($tableDesign, "horizontalGrid");
  if ($horizontalGrid == 0)
  {
    $gridHorizontalStyle = "";
  }

  $gridPadding = reports_get_style($tableDesign, "gridPadding");
  if (length($gridPadding) < 1)
  {
    $gridPadding = 3;
  }
  $gridPadding .= "px";

  #extract our table design info (color. borders, etc) if specified
  if ($tableDesign =~ m/title:"(.*?)",/)
  {
    $tableTitle = $1;
  }

  if ($tableDesign =~ m/subcaption:"(.*?)",/)
  {
    $subcaption = $1;
  }

  $captionFontSize = reports_get_style($tableDesign, "captionFontSize");
  if ($captionFontSize < 5)
  {
    $captionFontSize = 18;
  }
  $subcaptionFontSize = int($captionFontSize * 0.75);
  $captionFontSize .= "px";
  $subcaptionFontSize .= "px";

  $captionFontColor = reports_get_style($tableDesign, "captionFontColor");
  if (length($captionFontColor) < 7)
  {
    $captionFontColor = "#333333";
  }

  $captionBgColor = reports_get_style($tableDesign, "captionBgColor");
  if (length($captionBgColor) < 7)
  {
    $captionBgColor = "#ffffff";
  }

  $captionAlignment = reports_get_style($tableDesign, "captionAlignment");
  if (length($captionAlignment) < 3)
  {
    $captionAlignment = "center";
  }

  $captionFont = reports_get_style($tableDesign, "captionFont");
  if (length($captionFont) < 3)
  {
    $headerFont = "-apple-system,BlinkMacSystemFont,\"Segoe UI\",Roboto,\"Helvetica Neue\",Arial,sans-serif";
  }

  $headerFontColor = reports_get_style($tableDesign, "headerFontColor");
  if (length($headerFontColor) < 7)
  {
    $headerFontColor = reports_get_style($tableDesign, "headercolor");
  }
  if (length($headerFontColor) < 1)
  {
    $headerFontColor = "#ffffff";
  }

  $headerBgColor = reports_get_style($tableDesign, "headerBgColor");
  if (length($headerBgColor) < 7)
  {
    $headerBgColor = reports_get_style($tableDesign, "headerbg");
  }
  if (length($headerBgColor) < 1)
  {
    $headerBgColor = "#333333";
  }

  $headerWrap = reports_get_style($tableDesign, "headerWrap");
  if ($headerWrap != 0)
  {
    $headerWrap = "normal";
  }
  else
  {
    $headerWrap = "nowrap";
  }

  $headerSticky = reports_get_style($tableDesign, "headerSticky");
  if ($headerSticky == 0)
  {
    $headerSticky = "";
  }
  else
  {
    $headerSticky = "position: sticky;";
  }

  $headerFontSize = reports_get_style($tableDesign, "headerFontSize");
  if ($headerFontSize < 1)
  {
    $headerFontSize = "11px";
  }
  else
  {
    $headerFontSize .= "px";
  }

  $headerFont = reports_get_style($tableDesign, "headerFont");
  if (length($headerFont) < 3)
  {
    $headerFont = "Verdana,-apple-system,BlinkMacSystemFont,\"Segoe UI\",Roboto,\"Helvetica Neue\",Arial,sans-serif";
  }

  $headerOutline = reports_get_style($tableDesign, "headerOutline");
  if ($headerOutline eq "none")
  {
    $headerOutline = "";
  }
  if ($headerOutline eq "bottom")
  {
    $headerOutline = "border-bottom: 1px solid #01B8AA;";
  }
  elsif ($headerOutline eq "top")
  {
    $headerOutline = "border-top: 1px solid #01B8AA;";
  }
  elsif ($headerOutline eq "left")
  {
    $headerOutline = "border-left: 1px solid #01B8AA;";
  }
  elsif ($headerOutline eq "right")
  {
    $headerOutline = "border-right: 1px solid #01B8AA;";
  }
  elsif ($headerOutline eq "topbottom")
  {
    $headerOutline = "border-top: 1px solid #01B8AA; border-bottom: 1px solid #01B8AA;";
  }
  elsif ($headerOutline eq "leftright")
  {
    $headerOutline = "border-left: 1px solid #01B8AA; border-right: 1px solid #01B8AA;";
  }
  elsif ($headerOutline eq "frame")
  {
    $headerOutline = "border: 1px solid #01B8AA;";
  }
  else    #default to frame border
  {
    $headerOutline = "border: 1px solid #01B8AA;";
  }

  #handle background colors for values in the table
  $valueBgColor = reports_get_style($tableDesign, "valueBgColor");
  if (length($valueBgColor) < 7)
  {
    $valueBgColor = reports_get_style($tableDesign, "cellbg");
  }
  if (length($valueBgColor) < 1)
  {
    $valueBgColor = "#ffffff";
  }
  if ($valueBgColor eq "black")
  {
    $valueBgColor = "#ffffff";
  }

  $valueAlternateBgColor = reports_get_style($tableDesign, "valueAlternateBgColor");
  if (length($valueAlternateBgColor) < 7)
  {
    $valueAlternateBgColor = reports_get_style($tableDesign, "cellband");
  }
  if (length($valueAlternateBgColor) < 1)
  {
    $valueAlternateBgColor = "#efefef";
  }
  if ($valueAlternateBgColor eq "black")
  {
    $valueAlternateBgColor = "#efefef";
  }

  if ($tableDesign =~ m/,cellborder:(.*?),/)
  {
    $styleCellBorder = $1;
  }

  #handle font colors for values (and alternating values) in the table
  $valueFontColor = reports_get_style($tableDesign, "valueFontColor");
  $valueAlternateFontColor = reports_get_style($tableDesign, "valueAlternateFontColor");
  if (length($valueFontColor) < 7)
  {
    $valueFontColor = reports_get_style($tableDesign, "cellcolor");
    $valueAlternateFontColor = reports_get_style($tableDesign, "cellcolor");
  }
  if (length($valueFontColor) < 1)
  {
    $valueFontColor = "#333333";
  }
  if (length($valueAlternateFontColor) < 1)
  {
    $valueAlternateFontColor = "#333333";
  }

  #handle value/dimension value word wrapping
  $valueWrapDimension = reports_get_style($tableDesign, "valueWrapDimension");
  if ($valueWrapDimension != 1)
  {
    $valueWrapDimension = "nowrap";
  }
  else
  {
    $valueWrapDimension = "normal";
  }

  $valueWrap = reports_get_style($tableDesign, "valueWrap");
  if ($valueWrap != 1)
  {
    $valueWrap = "nowrap";
  }
  else
  {
    $valueWrap = "normal";
  }

  $valueFont = reports_get_style($tableDesign, "valueFont");
  if (length($valueFont) < 3)
  {
    $valueFont = "Verdana,-apple-system,BlinkMacSystemFont,\"Segoe UI\",Roboto,\"Helvetica Neue\",Arial,sans-serif";
  }

  $valueFontSize = reports_get_style($tableDesign, "valueFontSize");
  if ($valueFontSize < 1)
  {
    $valueFontSize = "11px";
  }
  else
  {
    $valueFontSize .= "px";
  }

  #build up table style string
  $tableStyle = "border-collapse:collapse; padding:2px;";

  #build up header cell style string
  $headerCellStyle = "padding: $gridPadding;";

  #build up data cell style string
  $dataCellStyle = "";

  #output table styling CSS
  print <<END_HTML;
<STYLE>
table#tbl_data-$visID
{
  border-collapse: collapse;
  padding: 2px;
  border: $tableBorderStyle;
}

table#tbl_data-$visID tr:nth-child(even)
{
  background-color: $valueBgColor;
  color: $valueFontColor;
}

table#tbl_data-$visID tr:nth-child(odd)
{
  background-color: $valueAlternateBgColor;
  color: $valueAlternateFontColor;
}

table#tbl_data-$visID th
{
  $headerSticky;
  background-color: $headerBgColor;
  color: $headerFontColor;
  border-left: $gridVerticalStyle;
  border-right: $gridVerticalStyle;
  border-top: $gridHorizontalStyle;
  border-bottom: $gridHorizontalStyle;
  font-family: $headerFont;
  font-size: $headerFontSize;
  font-weight: bold;
  white-space: $headerWrap;
  $headerOutline
}

table#tbl_data-$visID td
{
  padding: $gridPadding;
  border-left: $gridVerticalStyle;
  border-right: $gridVerticalStyle;
  border-top: $gridHorizontalStyle;
  border-bottom: $gridHorizontalStyle;
  font-size: $valueFontSize;
  font-family: "$valueFont";
  white-space: $valueWrap;
}

</STYLE>

<SCRIPT>
\$(document).ready
{
  let stripeHeaderHeight = \$('#tr-nested-header').outerHeight(true);
  if (stripeHeaderHeight == undefined)
  {
    stripeHeaderHeight = 0;
  }
  stripeHeaderHeight += "px";
  \$('.th-header-$visID').css({top: stripeHeaderHeight});
}
</SCRIPT>
END_HTML

  print("<TABLE ID='tbl_data-$visID'>\n");

  if (length($tableTitle) > 0)
  {
    $tableTitle = reports_expand_dim_tags($db, $dsSchema, $tableTitle, $prodDisplayIDs, $geoDisplayIDs, $timeDisplayIDs, $measDisplayIDs);
    print("<CAPTION STYLE='caption-side:top; text-align:$captionAlignment; background-color:$captionBgColor; font-family:$captionFont;'>\n");
    print("<SPAN STYLE='font-weight:bold; font-size:$captionFontSize; color:$captionFontColor;'>$tableTitle</SPAN><BR>\n");

    if (length($subcaption) > 0)
    {
      $subcaption = reports_expand_dim_tags($db, $dsSchema, $subcaption, $prodDisplayIDs, $geoDisplayIDs, $timeDisplayIDs, $measDisplayIDs);
      print("<SPAN STYLE='font-weight:bold; font-size:$subcaptionFontSize; color:$captionFontColor;'>$subcaption</SPAN>\n");
    }
    print("</CAPTION>\n");
  }

  #if we have nested columns, create the array of each column "stripe" we need
  if ($colDim eq "p")
  {
    $colStripeDim = "product";
    @colStripes = split(',', $selProductsString);

    #use an intersection to arrange the stripes in selected order
    foreach $i (@colStripes, @dimProducts) { $union{$i}++ && $isect{$i}++ };
    undef(@colStripes);
    foreach $id (@dimProducts)
    {
      if ($isect{$id} > 0)
      {
        push(@colStripes, $id);
      }
    }
  }

  elsif ($colDim eq "g")
  {
    $colStripeDim = "geography";
    @colStripes = split(',', $selGeographiesString);

    #use an intersection to arrange the stripes in selected order
    foreach $i (@colStripes, @dimGeographies) { $union{$i}++ && $isect{$i}++ };
    undef(@colStripes);
    foreach $id (@dimGeographies)
    {
      if ($isect{$id} > 0)
      {
        push(@colStripes, $id);
      }
    }
  }

  elsif ($colDim eq "t")
  {
    $colStripeDim = "time";
    @colStripes = split(',', $selTimesString);

    #use an intersection to arrange the stripes in selected order
    foreach $i (@colStripes, @dimTimes) { $union{$i}++ && $isect{$i}++ };
    undef(@colStripes);
    foreach $id (@dimTimes)
    {
      if ($isect{$id} > 0)
      {
        push(@colStripes, $id);
      }
    }
  }
  else
  {
    $colStripes[0] = "PASSTHROUGH";
  }

  #if we're displaying nested columns, display a meta-header row that groups
  #the various stripe columns together
  if (length($colDim) > 0)
  {
    print(" <TR ID='tr-nested-header' STYLE='background-color:white;'>\n");

    #skip over dimension columns
    $numRowDims = scalar @rowDims;
    print("  <TH STYLE='position: sticky; top:0; background-color:white;' COLSPAN='$numRowDims'>&nbsp;</TH>\n");

    #output a header row for each stripe that covers all columns in the stripe
    $numStripeCols = scalar @dispMeasures;
    foreach $id (@colStripes)
    {
      if ($colDim eq "p")
      {
        $name = $productNameHash{$id};
      }
      elsif ($colDim eq "g")
      {
        $name = $geographyNameHash{$id};
      }
      elsif ($colDim eq "t")
      {
        $name = $timeNameHash{$id};
      }

      print("  <TH COLSPAN=$numStripeCols STYLE='position: sticky; top:0; text-align:center; text-weight:bold; font-size:$headerFontSize;'>$name</TH>\n");
    }

    print(" </TR>\n");
  }

  #output header row
  print(" <TR>\n");
  foreach $dim (@rowDims)
  {
    if ($dim eq "p")
    {
      print("  <TH CLASS='th-header-$visID' STYLE='$headerCellStyle'>Product</TH>\n");
    }
    if ($dim eq "g")
    {
      print("  <TH CLASS='th-header-$visID' STYLE='$headerCellStyle'>Geography</TH>\n");
    }
    if ($dim eq "t")
    {
      print("  <TH CLASS='th-header-$visID' STYLE='$headerCellStyle'>Time Period</TH>\n");
    }
  }

  #output the measure names once for each column "stripe" we're displaying
  foreach $col (@colStripes)
  {
    foreach $measureID (@dispMeasures)
    {
      print("  <TH CLASS='th-header-$visID' STYLE='$headerCellStyle'>$measureNameHash{$measureID}</TH>\n");
    }
  }

  print(" </TR>\n");

  #build our where clause for filter dimensions
  $filterWhere = "";
  $filterDimStr = join(',', @filterDims);
  if ($filterDimStr =~ m/p/)
  {
    $filterWhere = "AND product IN ($prodDisplayIDs) ";
  }
  if ($filterDimStr =~ m/g/)
  {
    $filterWhere .= "AND geography IN ($geoDisplayIDs) ";
  }
  if ($filterDimStr =~ m/t/)
  {
    $filterWhere .= "AND time IN ($timeDisplayIDs) ";
  }

  #build up the concat string format for SQL comparison purposes
  $dimConcat = "";
  $first = 1;
  foreach $dim (@rowDims)
  {
    if ($first == 1)
    {
      $first = 0;
    }
    else
    {
      $dimConcat .= ", '-', ";
    }

    if ($dim eq "p")
    {
      $dimConcat .= "product";
    }
    elsif ($dim eq "g")
    {
      $dimConcat .= "geography";
    }
    elsif ($dim eq "t")
    {
      $dimConcat .= "time";
    }
  }

  #get hash of conditional formatting rules for this table (if any)
  %condFormattingHash = reports_get_conditional_formatting_hash($db, $visID);

  #run through each column "stripe", pulling the data we need
  #NB: we're building up an in-order array of the inner HTML for each row
  #    in the display table, which we'll output after we're done grabbing
  #    all the data
  $first = 1;		#determine if we're the first "stripe" - output dim data
  foreach $colStripe (@colStripes)
  {

    #pull the table data from the data source
    if ($colStripe eq "PASSTHROUGH")
    {

      #if we're doing a non-nested column table
      $query = "SELECT $measureCol, CONCAT($dimConcat) AS selStr FROM $dsSchema.$rptCube WHERE CONCAT($dimConcat) IN ($concatIDstr) $filterWhere ORDER BY FIELD(selStr, $concatIDstr)";
    }

    #else we're doing nested columns
    else
    {
      $query = "SELECT $measureCol, CONCAT($dimConcat) AS selStr FROM $dsSchema.$rptCube WHERE CONCAT($dimConcat) IN ($concatIDstr) $filterWhere AND $colStripeDim = '$colStripe' ORDER BY FIELD(selStr, $concatIDstr)";
    }

    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    (@rowVals) = $dbOutput->fetchrow_array;

    #output data row for each item in our rowDim
    $banding = 0;
    $tableRowIdx = 0;
    while (defined($rowVals[0]))
    {
      $valArrayIdx = 0;
      $rowLength = @rowVals - 1;

      #choose which style to use for this row (implements row banding)
      if ($banding == 0)
      {
        $cellStyle = $dataCellStyle;
        $banding = 1;
      }
      else
      {
        $cellStyle = $bandedCellStyle;
        $banding = 0;
      }
      $measureCellStyle = $cellStyle . "text-align:right;";

      #if we're the first column stripe, output dimension data
      if ($first == 1)
      {
        foreach $dim (@rowDims)
        {
          $itemID = $rowVals[$valArrayIdx];
          if ($dim eq "p")
          {
            $tableRows[$tableRowIdx] .= "  <TD STYLE='$cellStyle white-space:$valueWrapDimension;'>$productNameHash{$itemID}</TD>\n";
          }
          if ($dim eq "g")
          {
            $tableRows[$tableRowIdx] .= "  <TD STYLE='$cellStyle white-space:$valueWrapDimension;'>$geographyNameHash{$itemID}</TD>\n";
          }
          if ($dim eq "t")
          {
            $tableRows[$tableRowIdx] .= "  <TD STYLE='$cellStyle white-space:$valueWrapDimension;'>$timeNameHash{$itemID}</TD>\n";
          }
          $valArrayIdx++;
        }
      }

      #if we aren't the first column stripe, skip over dimension data
      else
      {
        foreach $dim (@rowDims)
        {
          $valArrayIdx++;
        }
      }

      %formatHash = DSRmeasures_get_format_hash($db, $dsID);

      $idx = 0;
      while ($valArrayIdx < $rowLength)
      {
        $measureID = $dispMeasures[$idx];
        $measureVal = $rowVals[$valArrayIdx];

        #if we're text, left justify; if numerical, right justify
        if (($measureID =~ m/ATT_/) || ($measureID =~ m/SEG_/))
        {
          $tableRows[$tableRowIdx] .= "  <TD STYLE='$cellStyle'>$measureVal</TD>\n";
        }
        else
        {
          $measureHTML = DSRmeasures_format_html($db, $dsSchema, $measureID, $measureVal, $formatHash{$measureID});
          $conditionalBgColor = DSRmeasures_get_conditional_color($condFormattingHash{$measureID}, $measureVal);
          $tableRows[$tableRowIdx] .= "  <TD STYLE='$measureCellStyle $conditionalBgColor'>$measureHTML</TD>\n";
        }

        $valArrayIdx++;
        $idx++;
      }

      (@rowVals) = $dbOutput->fetchrow_array;
      $tableRowIdx++;
    }

    $first = 0;
  }

  foreach $row (@tableRows)
  {
    print(" <TR>\n");
    print("  $row");
    print(" </TR>\n");
  }

  print("</TABLE>\n");

  if ($tablePartialDisplay)
  {
    print <<END_HTML;
      <P>
      <TABLE>
        <TR>
          <TD STYLE="border:1px solid lightgray; background-color:lightyellow;">
            There's still a lot more data in your table. If Koala displays it, there's a good chance that your browser won't be able to handle it and will crash.

            <P>&nbsp;</P>
            If you want to wave your hands in the air like you just don't care and risk it, click the button below to reload <B>ALL</B> the data in this report view.
            But before you do, keep in mind that you don't have to display the entire report in the web interface to export the entire report to Excel or other integrated applications.

            <P>&nbsp;</P>
            <DIV CLASS="text-center">
              <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='?rpt=$rptID&loadAll=1'">Load & Display All Data In This Table</BUTTON>
            </DIV>
          </TD>
        </TR>
      </TABLE>
END_HTML
  }

  print <<END_HTML;
    </TD>
  </TR>
</TABLE>
END_HTML

#EOF
