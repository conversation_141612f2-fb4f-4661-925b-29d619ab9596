#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($kapDB);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Open Data Flow</TITLE>
<LINK REL="SHORTCUT ICON" HREF="$Lib::KoalaConfig::kapHostURL/favicon.png">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($kapDB, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="$Lib::KoalaConfig::kapHostURL/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?f=$flowID">Data Prep</A></LI>
    <LI CLASS="breadcrumb-item active">$flowName</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $flowID = $q->param('f');

  #connect to the database
  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  $flowName = prep_flow_id_to_name($prepDB, $flowID);

  print_html_header();

  #make sure we have read privs for this data source
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to view this data flow.");
  }

  #see if there's any current jobs for this flow
  $query = "SELECT ID, state, opInfo, rowCount, mode, userID, lastAction, validation, exportedKoala FROM prep.jobs WHERE flowID=$flowID ORDER BY lastAction DESC";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;

  #output the panel header HTML
  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-12 col-lg-10 col-xl-8"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Open Data Flow</DIV>
        <DIV CLASS="card-body">
END_HTML

  #if there aren't any jobs, let the user know and finish
  if ($status < 1)
  {
    print <<END_HTML;
          There aren't any active jobs for this data flow.

          <P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" TYPE="button" onclick="location.href='main.cld?f=$flowID'"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
          </DIV>

        </DIV>
      </DIV>
    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML
    exit;
  }

  #if we made it this far there's at least one job
  print <<END_HTML;
          <DIV CLASS="table-responsive">
            <TABLE CLASS="table table-striped table-sm">
              <THEAD>
                <TR>
                  <TH>&nbsp;</TH>
                  <TH>Status</TH>
                  <TH>User</TH>
                  <TH>Last Activity</TH>
                </TR>
              </THEAD>
END_HTML

  while (($jobID, $jobState, $jobInfo, $rowCount, $jobMode, $jobUserID, $lastAction, $validation, $exportedKoala) = $dbOutput->fetchrow_array)
  {

    if ($jobUserID == 0)
    {
      $jobUserID = "Koala Scheduler";
    }
    else
    {
      $jobUserID = utils_userID_to_name($kapDB, $jobUserID);
    }

    if (($jobState eq "LOADED") && ($validation eq "ERROR"))
    {
      $jobState = "Invalid data detected";
      $rowColor = "table-danger";
    }
    elsif ($jobState eq "LOADED")
    {
      if ($exportedKoala > 0)
      {
        $jobState = "Exported to Koala";
        $rowColor = "table-light";
      }
      else
      {
        $jobState = "Loaded";
        $rowColor = "table-success";
      }
    }
    elsif ($jobState =~ m/UPDATE-KOALA/)
    {
      $jobState = "Updating Koala data source";
      $rowColor = "table-info";
    }
    elsif ($jobMode eq "run")
    {
      $jobState = "Running";
      $rowColor = "table-info";
    }
    elsif ($jobState eq "LOAD-WEB")
    {
      $jobState = "Loading from web";
      $rowColor = "table-info";
    }
    elsif ($jobState eq "LOAD-FTP")
    {
      $jobState = "Loading from FTP";
      $rowColor = "table-info";
    }
    elsif ($jobState eq "LOAD-AMAZON")
    {
      $jobState = "Loading from Amazon";
      $rowColor = "table-info";
    }
    elsif ($jobState eq "LOAD-DATABASE")
    {
      $jobState = "Loading from database";
      $rowColor = "table-info";
    }
    elsif ($jobState eq "LOAD-KOALA")
    {
      $jobState = "Loading from Koala";
      $rowColor = "table-info";
    }
    elsif (($jobState eq "EXTRACT-DATA") || ($jobState eq "EXTRACT-DATA-WAIT"))
    {
      $jobState = "Extracting Data";
      $rowColor = "table-info";
    }
    elsif (($jobState eq "NESTED-WAIT") || ($jobState eq "NESTED-CONVERT"))
    {
      $jobState = "Detecting Nested Data";
      $rowColor = "table-info";
    }
    elsif ($jobState eq "PARSE-WAIT")
    {
      $jobState = "Waiting for parsing options";
      $rowColor = "table-warning";
    }
    elsif ($jobState =~ m/^LOAD-DATA/)
    {
      $jobState = "Loading raw data";
      $rowColor = "table-info";
    }
    elsif ($jobState eq "DATATYPE-WAIT")
    {
      $jobState = "Waiting for data types";
      $rowColor = "table-warning";
    }
    elsif ($jobState =~ m/^MERGE-DATA/)
    {
      $jobState = "Merging data";
      $rowColor = "table-info";
    }
    elsif (($jobState eq "COLUMN-TYPES") || ($jobState eq "COL-TYPES-WAIT"))
    {
      $jobState = "Detecting column types";
      $rowColor = "table-info";
    }
    elsif ($jobState =~ m/^RECIPE-/)
    {
      $jobState = "Applying transform recipe";
      $rowColor = "table-info";
    }
    elsif ($jobState =~ m/^VALIDATE/)
    {
      $jobState = "Validating data";
      $rowColor = "table-info";
    }
    elsif (($jobState eq "TRANSFORM-DATA") || ($jobState eq "TRANSFORM-DATA-WAIT"))
    {
      $jobState = "Transforming data";
      $rowColor = "table-info";
    }
    elsif ($jobState =~ m/EXP-KOALA/)
    {
      $jobState = "Exporting to Koala Analytics";
      $rowColor = "table-info";
    }
    elsif ($jobState eq "ERROR")
    {
      $jobState = "Fatal error";
      if ($jobInfo =~ m/^ERR\|(.*)$/)
      {
        $jobState .= ": $1";
      }
      $rowColor = "table-danger";
    }
    elsif ($jobState eq "ERROR-EXPORT")
    {
      $jobState = "Error exporting data";
      if ($jobInfo =~ m/^ERR\|(.*)$/)
      {
        $jobState .= ": $1";
      }
      $rowColor = "table-danger";
    }
    else
    {
      $jobState = "Unknown";
      $rowColor = "table-danger";
    }

    print(" <TR CLASS='$rowColor'>\n");
    print("  <TD>\n");

    if (($jobState eq "Loaded") || ($jobState eq "Exported to Koala"))
    {
      print("<A CLASS='btn btn-primary' HREF='flowViewData.cld?f=$flowID&j=$jobID'><I CLASS='bi bi-folder2-open'></I> Open</A>\n");
      print("<P></P>\n");
      print("<A CLASS='btn btn-secondary' HREF='clearJobConfirm.cld?f=$flowID&j=$jobID'><I CLASS='bi bi-eraser'></I> Clear</A>\n");
    }

    elsif (($jobState eq "Invalid data detected") ||
           ($jobInfo =~ m/Unable to export to Koala, missing dimensions/))
    {
      print("<A CLASS='btn btn-primary' HREF='flowViewData.cld?f=$flowID&j=$jobID'><I CLASS='bi bi-folder2-open'></I> Open</A>\n");
      print("<P></P>\n");
      print("<A CLASS='btn btn-secondary' HREF='clearJobConfirm.cld?f=$flowID&j=$jobID'><I CLASS='bi bi-eraser'></I> Clear</A>\n");
    }

    elsif ($jobState eq "Loading from web")
    {
      print("<A CLASS='btn btn-primary' HREF='sourceWebLoad.cld?f=$flowID&j=$jobID'><I CLASS='bi bi-eye'></I> Check Status</A>\n");
    }

    elsif ($jobState eq "Loading from FTP")
    {
      print("<A CLASS='btn btn-primary' HREF='sourceFTPLoad.cld?f=$flowID&j=$jobID'><I CLASS='bi bi-eye'></I> Check Status</A>\n");
    }

    elsif ($jobState eq "Loading from Amazon")
    {
      print("<A CLASS='btn btn-primary' HREF='sourceAmazonLoad.cld?f=$flowID&j=$jobID'><I CLASS='bi bi-eye'></I> Check Status</A>\n");
    }

    elsif ($jobState eq "Loading from database")
    {
      print("<A CLASS='btn btn-primary' HREF='sourceDatabaseLoad.cld?f=$flowID&j=$jobID'><I CLASS='bi bi-eye'></I> Check Status</A>\n");
    }

    elsif ($jobState eq "Loading from Koala")
    {
      print("<A CLASS='btn btn-primary' HREF='sourceKoalaLoad.cld?f=$flowID&j=$jobID'><I CLASS='bi bi-eye'></I> Check Status</A>\n");
    }

    elsif ($jobState eq "Extracting Data")
    {
      print("<A CLASS='btn btn-primary' HREF='flowExtractData.cld?f=$flowID&j=$jobID'><I CLASS='bi bi-eye'></I> Check Status</A>\n");
    }

    elsif ($jobState eq "Detecting Nested Data")
    {
      print("<A CLASS='btn btn-primary' HREF='flowDetectNested.cld?f=$flowID&j=$jobID'><I CLASS='bi bi-eye'></I> Check Status</A>\n");
    }

    elsif ($jobState eq "Waiting for parsing options")
    {
      print("<A CLASS='btn btn-primary' HREF='flowParseOptions.cld?f=$flowID&j=$jobID'><I CLASS='bi bi-eye'></I> Check Status</A>\n");
      print("<P></P>\n");
      print("<A CLASS='btn btn-secondary' HREF='clearJobConfirm.cld?f=$flowID&j=$jobID'><I CLASS='bi bi-eraser'></I> Clear</A>\n");
    }

    elsif ($jobState eq "Loading raw data")
    {
      print("<A CLASS='btn btn-primary' HREF='flowLoadRawData.cld?f=$flowID&j=$jobID'><I CLASS='bi bi-eye'></I> Check Status</A>\n");
    }

    elsif ($jobState eq "Waiting for data types")
    {
      print("<A CLASS='btn btn-primary' HREF='flowDataType.cld?f=$flowID&j=$jobID'><I CLASS='bi bi-eye'></I> Check Status</A>\n");
      print("<P></P>\n");
      print("<A CLASS='btn btn-secondary' HREF='clearJobConfirm.cld?f=$flowID&j=$jobID'><I CLASS='bi bi-eraser'></I> Clear</A>\n");
    }

    elsif ($jobState eq "Merging data")
    {
      print("<A CLASS='btn btn-primary' HREF='flowMergeData.cld?f=$flowID&j=$jobID'><I CLASS='bi bi-eye'></I> Check Status</A>\n");
    }

    elsif ($jobState eq "Detecting column types")
    {
      print("<A CLASS='btn btn-primary' HREF='flowColumnTypes.cld?f=$flowID&j=$jobID'><I CLASS='bi bi-eye'></I> Check Status</A>\n");
    }

    elsif ($jobState eq "Applying transform recipe")
    {
      print("<A CLASS='btn btn-primary' HREF='flowApplyRecipe.cld?f=$flowID&j=$jobID'><I CLASS='bi bi-eye'></I> Check Status</A>\n");
    }

    elsif ($jobState eq "Validating data")
    {
      print("<A CLASS='btn btn-primary' HREF='flowValidate.cld?f=$flowID&j=$jobID'><I CLASS='bi bi-eye'></I> Check Status</A>\n");
    }

    elsif ($jobState eq "Transforming data")
    {
      print("<A CLASS='btn btn-primary' HREF='transformProgress.cld?f=$flowID&j=$jobID'><I CLASS='bi bi-eye'></I> Check Status</A>\n");
    }

    elsif ($jobState eq "Exporting to Koala Analytics")
    {
      print("<A CLASS='btn btn-primary' HREF='exportKoalaDS.cld?f=$flowID&j=$jobID'><I CLASS='bi bi-eye'></I> Check Status</A>\n");
    }

    elsif ($jobState eq "Updating Koala data source")
    {
      $query = "SELECT dsID FROM prep.flows WHERE ID=$flowID";
      $dbOutput1 = $prepDB->prepare($query);
      $dbOutput1->execute;
      ($dsID) = $dbOutput1->fetchrow_array;
      if ($dsID < 1)
      {
        print("<A CLASS='btn btn-primary' HREF='$Lib::KoalaConfig::kapHostURL/app/dsr/main.cld'><I CLASS='bi bi-eye'></I> Check Status</A>\n");
      }
      else
      {
        print("<A CLASS='btn btn-primary' HREF='/app/dsr/updateDSwork.cld?ds=$dsID'><I CLASS='bi bi-eye'></I> Check Status</A>\n");
      }
    }

    elsif ($jobState eq "Running")
    {
      print("<A CLASS='btn btn-primary' HREF='runFlow.cld?f=$flowID&j=$jobID'><I CLASS='bi bi-eye'></I> Check Status</A>\n");
    }

    elsif ($jobState =~ m/^Fatal error.*/)
    {
      print("<A CLASS='btn btn-primary' HREF='clearJobConfirm.cld?f=$flowID&j=$jobID'><I CLASS='bi bi-eraser'></I></SPAN> Clear</A>\n");
    }

    elsif ($jobState =~ m/^Error exporting data.*/)
    {
      print("<A CLASS='btn btn-primary' HREF='flowViewData.cld?f=$flowID&j=$jobID'><I CLASS='bi bi-folder2-open'></I> Open</A>\n");
      print("<P></P>\n");
      print("<A CLASS='btn btn-secondary' HREF='clearJobConfirm.cld?f=$flowID&j=$jobID'><I CLASS='bi bi-eraser'></I> Clear</A>\n");
    }

    elsif ($jobState eq "Unknown")
    {
      print("<A CLASS='btn btn-primary' HREF='clearJobConfirm.cld?f=$flowID&j=$jobID'><I CLASS='bi bi-eraser'></I> Clear</A>\n");
    }
    print("</TD>\n");

    if ($rowCount > 0)
    {
      $recordCount = prep_autoscale_number($rowCount);
      $recordCount = "<BR>($recordCount records)";
    }
    print("  <TD>$jobState $recordCount</TD>\n");

    print("  <TD>$jobUserID</TD>\n");
    print("  <TD>$lastAction</TD>\n");

    print(" </TR>\n");
  }

    print <<END_HTML;
            </TABLE>
          </DIV>

          <P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" TYPE="button" onclick="location.href='main.cld?f=$flowID'"><I CLASS="bi bi-check-lg"></I> Done</BUTTON>
          </DIV>

        </DIV>  <!-- card body -->
      </DIV> <!-- card -->

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
<P>
END_HTML

  print_html_footer();

#EOF
