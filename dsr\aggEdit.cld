#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSel;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::Social;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Edit Aggregate</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<SCRIPT SRC="/jsgrid/jsgrid.min.js"></SCRIPT>
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/jsgrid/jsgrid.min.css" REL="stylesheet">
<LINK HREF="/jsgrid/jsgrid-theme.min.css" REL="stylesheet">
<LINK HREF="/fontawesome-5.10.2/css/all.min.css" REL="stylesheet">

<STYLE>
div.grid th
{
  background: #e2e3e5 !important;
}

.selected-row > td
{
  background: #c4e2ff !important;
  border-color: #c4e2ff;
}

.accordion-button.collapsed {
  background: blue
}

.accordion-button.collapsed::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}
</STYLE>

END_HTML

  $dataBlock = datasel_get_script_json($db, $aggID, "a", $dim, $dsID);
  $dataSubtractBlock = datasel_get_script_json($db, $aggID, "as", $dim, $dsID);

  #add aggregate add/sub glyphs as appropriate
  @scriptLines = split('\n', $dataBlock);
  $dataBlock = "";
  foreach $line (@scriptLines)
  {
    $line =~ m/id: '(.*?)'/;
    $id = $1;
    $clickGlyph = "<A HREF=\"?ds=$dsID&dim=$dim&a=$aggID&action=sub&item=$id\"><I STYLE=\"color:green;\" CLASS=\"bi bi-plus-lg\"></I></A> ";
    $line =~ m/^(.*? product: ')(.*)/;
    $line = $1 . $clickGlyph . $2;
    $dataBlock .= $line . "\n";
  }

  @scriptLines = split('\n', $dataSubtractBlock);
  $dataSubtractBlock = "";
  foreach $line (@scriptLines)
  {
    $line =~ m/id: '(.*?)'/;
    $id = $1;
    $clickGlyph = "<A HREF=\"?ds=$dsID&dim=$dim&a=$aggID&action=add&item=$id\"><I STYLE=\"color:red;\" CLASS=\"bi bi-dash\"></I></A> ";
    $line =~ m/^(.*? product: ')(.*)/;
    $line = $1 . $clickGlyph . $2;
    $dataSubtractBlock .= $line . "\n";
  }

  if (length($dataSubtractBlock) > 1)
  {
    if (length($dataBlock) > 1)
    {
      $dataBlock .= ",\n" . $dataSubtractBlock;
    }
    else
    {
      $dataBlock = $dataSubtractBlock;
    }
  }

  $dimItems = datasel_expand_script($db, $dsSchema, $aggID, $dim, "a");
  datasel_expand_script($db, $dsSchema, $aggID, $dim, "as");
  $dimItemsCount = $dimItems =~ tr/,//;
  if (length($dimItems) > 0)
  {
    $dimItemsCount++;
  }

  print <<END_HTML;
<SCRIPT>
let gridHeight = window.innerHeight - 400;
if (gridHeight < 200)
{
  gridHeight = 200;
}

let gridData = [ $dataBlock ];

\$(document).ready(function()
{
  \$('#aggGrid').jsGrid(
  {
    width: '95%',
    height: gridHeight,
    sorting: true,
    autoload: true,
    loadIndication: true,
    multiselect: true,

    data: gridData,

    rowClick: function(args)
    {

      //Shift + selection
      if (args.event.shiftKey)
      {
        document.getSelection().removeAllRanges();

        let i = 0;
        let firstSelection = -1;
        while ((i < this.data.length) && (firstSelection < 0))
        {
          if (this.data[i].selected == 1)
          {
            firstSelection = i;
          }
          i++;
        }

        i = 0;
        let curSelection = -1;
        while ((i < this.data.length) && (curSelection < 0))
        {
          if (args.item.id == this.data[i].id)
          {
            curSelection = i;
          }
          i++;
        }

        clearAllSelections();

        let start, stop;
        if (curSelection > firstSelection)
        {
          start = firstSelection;
          end = curSelection;
        }
        else
        {
          end = firstSelection;
          start = curSelection;
        }

        for (i = start; i <= end; i++)
        {
          this.data[i].selected = 1;
          \$selectedRow = \$('#aggGrid').jsGrid('rowByItem',
              this.data[i]).closest('tr');
          \$selectedRow.addClass('selected-row');
        }

      }

      //Ctrl+selection
      else if (event.ctrlKey || event.altKey || event.metaKey)
      {
        args.item.selected = 1;
        \$selectedRow = \$(args.event.target).closest('tr');
        \$selectedRow.addClass('selected-row');
      }

      //single selection
      else
      {
        clearAllSelections();
        args.item.selected = 1;
        \$selectedRow = \$(args.event.target).closest('tr');
        \$selectedRow.addClass('selected-row');
      }
    },

    rowDoubleClick: function(args)
    {
      item = args.item.id;

      location.href = '/app/datasel/selectionMethod.cld?ds=$dsID&dim=$dim&st=a&sid=$aggID&modItem=' + item;
    },

    fields: [
      {name: 'id', type: 'number', visible: false},
      {name: 'product', title: '$dimName ($dimItemsCount)', type: 'text', width: 500},
      {name: 'type', title: 'Type', type: 'text', width: 100}
    ]
  });

  //init selections, if needed
  let idx = $itemIndex;
  if (idx.length > 0)
  {
    let grid = \$('#aggGrid').jsGrid('option', 'data');
    let i = 0;

    \$selectedRow = \$('#aggGrid').jsGrid('rowByItem',
        grid[idx[0]]).closest('tr');
    \$selectedRow[0].scrollIntoView();

    while (i < idx.length)
    {
      grid[idx[i]].selected = 1;
      \$selectedRow = \$('#aggGrid').jsGrid('rowByItem',
          grid[idx[i]]).closest('tr');
      \$selectedRow.addClass('selected-row');
      i++;
    }
  }
});



function clearAllSelections()
{
  let grid = \$('#aggGrid').jsGrid('option', 'data');

  for (let i = 0; i < grid.length; i++)
  {
    grid[i].selected = 0;
  }

  \$('#aggGrid tr').removeClass('selected-row');
}



function getSelectionStr()
{
  let grid = \$('#aggGrid').jsGrid('option', 'data');
  let selStr = '';

  for (let i = 0; i < grid.length; i++)
  {
    if (grid[i].selected == 1)
    {
      selStr = selStr + grid[i].id + ',';
    }
  }

  return(selStr);
}



function getSelectionIdx()
{
  let grid = \$('#aggGrid').jsGrid('option', 'data');
  let selStr = '';

  for (let i = 0; i < grid.length; i++)
  {
    if (grid[i].selected == 1)
    {
      selStr = selStr + i + ',';
    }
  }

  return(selStr);
}



function modifySelection()
{
  let grid = \$('#aggGrid').jsGrid('option', 'data');
  let item = null;

  for (let i = 0; i < grid.length; i++)
  {
    if ((grid[i].selected == 1) && (item == null))
    {
      item = grid[i].id;
    }
  }

  if (item == null)
  {
    return;
  }

  location.href='/app/datasel/selectionMethod.cld?ds=$dsID&dim=$dim&st=a&sid=$aggID&modItem=' + item;
}



function removeSelection()
{
  let val = getSelectionStr();

  if (val.length == 0)
  {
    return;
  }

  location.href='?ds=$dsID&dim=$dim&a=$aggID&action=r&item=' + val;
}



function topItem()
{
  let val = getSelectionStr();

  if (val.length == 0)
  {
    return;
  }

  location.href='?ds=$dsID&dim=$dim&a=$aggID&action=t&item=' + val;
}



function upItem()
{
  let val = getSelectionStr();

  if (val.length == 0)
  {
    return;
  }

  let selIdx = getSelectionIdx();

  location.href='?ds=$dsID&dim=$dim&a=$aggID&action=u&item=' + val + '&i=' + selIdx;
}



function downItem()
{
  let val = getSelectionStr();

  if (val.length == 0)
  {
    return;
  }

  let selIdx = getSelectionIdx();

  location.href='?ds=$dsID&dim=$dim&a=$aggID&action=d&item=' + val + '&i=' + selIdx;
}



function bottomItem()
{
  let val = getSelectionStr();

  if (val.length == 0)
  {
    return;
  }

  location.href='?ds=$dsID&dim=$dim&a=$aggID&action=b&item=' + val;
}



function appendDate(source)
{
  let state = source.checked;

  location.href='?ds=$dsID&dim=$dim&a=$aggID&action=appendDate&state=' + state;
}
</SCRIPT>

<STYLE>
.accordion-button.collapsed {
  background: blue
}

.accordion-button.collapsed::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}
</STYLE>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Edit Aggregate $name</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');
  $dim = $q->param('dim');
  $aggID = $q->param('a');
  $action = $q->param('action');
  $selection = $q->param('selection');
  $itemIndex = $q->param('i');
  $segmentID = $q->param('segment');
  @segmentIDs = $q->param('segment');
  $segmentationID = $q->param('segmentation');
  $segHierLevel = $q->param('segHierLevel');
  $name = $q->param('name');
  $selMethod = $q->param('method');
  $structID = $q->param('sid');
  $item = $q->param('item');
  $modifyItem = $q->param('modItem');

  $selOp = $q->param('selOp');

  $selType = $q->param('type');
  $selDuration = $q->param('duration');
  $selStarting = $q->param('starting');
  $selEnding = $q->param('ending');

  $matchType = $q->param('matchType');

  #if we're being called to create an aggregate from a list, this contains
  #the source list's ID
  $listID = $q->param('listID');

  if (!defined($aggID))
  {
    $aggID = $structID;
  }

  #if we got a fully-qualified aggregate ID, strip it to the numerical ID
  $aggID = DSRstructure_extract_id($aggID);

  #sanitize parameters
  $dsID = utils_sanitize_integer($dsID);
  if (!defined($dsID))
  {
    exit_early_error($session, "Invalid data source");
  }
  $dim = utils_sanitize_dim($dim);
  if (!defined($dim))
  {
    exit_early_error($session, "Invalid dimension");
  }


  #build up our schema name
  $dsSchema = "datasource_$dsID";

  #if we got an item index (used to maintain grid selection state), convert it
  #into a JS array
  if (length($itemIndex) > 0)
  {
    @tmp = split(',', $itemIndex);
    $itemIndex = "[";
    foreach $idx (@tmp)
    {
      if (($idx > 0) && ($action eq "u"))
      {
        $idx--;
      }
      elsif ($action eq "d")
      {
        $idx++;
      }
      $itemIndex .= "$idx,";
    }
    chop($itemIndex);
    $itemIndex .= "];";
  }
  else
  {
    $itemIndex = "[];";
  }

  $dimName = KAPutil_get_dim_name($dim, 1);
  $dbStub = KAPutil_get_dim_stub_name($dim);
  $dbName = $dbStub . "aggregate";

  #connect to the database
  $db = KAPutil_connect_to_database();

  $activity = "";

  $dsName = ds_id_to_name($db, $dsID);

  if (length($name) < 1)
  {
    $query = "SELECT name FROM $dsSchema.$dbName WHERE ID=$aggID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($name) = $dbOutput->fetchrow_array;
  }

  #make sure we have write privs for the data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    print_html_header();
    exit_error("You don't have privileges to modify this data source.");
  }

  #clear any warnings/errors from the user's feed related to this agg
  Social_clear_agg_items($db, $dsID, $name);

  #if we're being called to create a new aggregate based on a list
  if (defined($listID))
  {

    #get the list's definition from the DSR
    $listDBName = $dbStub . "list";
    $query = "SELECT name, script, members FROM $dsSchema.$listDBName \
        WHERE ID=$listID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($listName, $listScript, $listMembers) = $dbOutput->fetchrow_array;

    #insert the new aggregate into the DSR
    $query = "INSERT INTO $dsSchema.$dbName (name, addScript, addMembers) \
        VALUES ('$listName', '$listScript', '$listMembers')";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    #get the ID of our newly created aggregate, then feed it to the rest of this
    #script for display purposes
    $structID = $db->{q{mysql_insertid}};
    $aggID = $structID;
    $name = $listName;

    $activity = "$first $last created an aggregate from list $listName in $dsName";
    utils_audit($db, $userID, "Created an aggregate from list $listName", $dsID, 0, 0);
  }

  if (length($activity) < 1)
  {
    $activity = "$first $last editing aggregate $name in $dsName";
    utils_audit($db, $userID, "Edited aggregate $name", $dsID, 0, 0);
  }

  #if we're being called to edit an agg, update the (possibly changed) name
  if ($action eq "e")
  {
    $q_name = $db->quote($name);
    $query = "UPDATE $dsSchema.$dbName SET name=$q_name WHERE ID=$aggID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);
  }

  #handle basic aggregate edit requests
  if (($action eq "r") || ($action eq "t") || ($action eq "u") ||
      ($action eq "d") || ($action eq "b"))
  {

    #grab the script that generates the aggregate
    $query = "SELECT addScript, subtractScript FROM $dsSchema.$dbName \
        WHERE ID=$aggID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($oldScript, $oldSubScript) = $dbOutput->fetchrow_array;

    #perform the actual action to the script (remove, top, up, down, bottom)
    if ($action eq "r")
    {
      $newLine = datasel_delete_items($oldScript, $item);
      $newSubLine = datasel_delete_items($oldSubScript, $item);
    }
    elsif ($action eq "t")
    {
      $newLine = datasel_move_items_top($oldScript, $item);
      $newSubLine = $oldSubScript;
    }
    elsif ($action eq "u")
    {
      $newLine = datasel_move_items_up($oldScript, $item);
      $newSubLine = $oldSubScript;
    }
    elsif ($action eq "d")
    {
      $newLine = datasel_move_items_down($oldScript, $item);
      $newSubLine = $oldSubScript;
    }
    elsif ($action eq "b")
    {
      $newLine = datasel_move_items_bottom($oldScript, $item);
      $newSubLine = $oldSubScript;
    }

    $q_newLine = $db->quote($newLine);
    $q_newSubLine = $db->quote($newSubLine);

    #store the new script
    $query = "UPDATE $dsSchema.$dbName \
        SET addScript=$q_newLine, subtractScript=$q_newSubLine WHERE ID=$aggID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    #NB: the agg add/sub scripts get re-expanded in a few microseconds when
    #    the HTML header code is created

    #update the data source's and this agg's last modified time stamps
    $db->do("UPDATE dataSources SET lastModified=NOW() WHERE ID=$dsID");
    $db->do("UPDATE $dsSchema.$dbName SET lastUpdated=NOW() WHERE ID=$aggID");
  }


  #################################


  #if we're being called to set an item to be subtracted from the aggregate
  if ($action eq "sub")
  {

    #grab the add script for the aggregate, and remove the item
    $query = "SELECT addScript FROM $dsSchema.$dbName WHERE ID=$aggID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($oldScript) = $dbOutput->fetchrow_array;

    $newLine = datasel_delete_items($oldScript, $item);

    $q_newLine = $db->quote($newLine);

    #store the new add script
    $query = "UPDATE $dsSchema.$dbName SET addScript=$q_newLine WHERE ID=$aggID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    #add the item to the subtract script
    $query = "SELECT subtractScript FROM $dsSchema.$dbName WHERE ID=$aggID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($oldScript) = $dbOutput->fetchrow_array;

    if (!($item =~ m/:/))
    {
      $item = "M:$item";
    }

    if (length($oldScript) > 1)
    {
      $newLine = $oldScript . ",$item";
    }
    else
    {
      $newLine = $item;
    }

    $q_newLine = $db->quote($newLine);

    $query = "UPDATE $dsSchema.$dbName SET subtractScript=$q_newLine \
        WHERE ID=$aggID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    #NB: the agg add/sub scripts get re-expanded in a few microseconds when
    #    the HTML header code is created

    #update the data source's and this agg's last modified time stamps
    $db->do("UPDATE dataSources SET lastModified=NOW() WHERE ID=$dsID");
    $db->do("UPDATE $dsSchema.$dbName SET lastUpdated=NOW() WHERE ID=$aggID");
  }


  #################################


  #if we're being called to set an item to be added to the aggregate
  if ($action eq "add")
  {

    #grab the subtract script for the aggregate, and remove the item
    $query = "SELECT subtractScript FROM $dsSchema.$dbName WHERE ID=$aggID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($oldScript) = $dbOutput->fetchrow_array;

    $newLine = datasel_delete_items($oldScript, $item);

    $q_newLine = $db->quote($newLine);

    #store the new subtract script
    $query = "UPDATE $dsSchema.$dbName SET subtractScript=$q_newLine \
        WHERE ID=$aggID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    #add the item to the add script
    $query = "SELECT addScript FROM $dsSchema.$dbName WHERE ID=$aggID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($oldScript) = $dbOutput->fetchrow_array;

    if (!($item =~ m/:/))
    {
      $item = "M:$item";
    }

    if (length($oldScript) > 1)
    {
      $newLine = $oldScript . ",$item";
    }
    else
    {
      $newLine = $item;
    }

    $q_newLine = $db->quote($newLine);

    $query = "UPDATE $dsSchema.$dbName SET addScript=$q_newLine WHERE ID=$aggID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    #NB: the agg add/sub scripts get re-expanded in a few microseconds when
    #    the HTML header code is created

    #update the data source's and this agg's last modified time stamps
    $db->do("UPDATE dataSources SET lastModified=NOW() WHERE ID=$dsID");
    $db->do("UPDATE $dsSchema.$dbName SET lastUpdated=NOW() WHERE ID=$aggID");
  }


  #################################


  #if we're being called to create a new agg or save selection changes to an
  #existing one
  if ($action eq "s")
  {

    #if there's an extra comma on the end of the selection string, chop it
    if ($selection =~ m/,$/)
    {
      chop($selection);
    }

    #if we're being called to create a new agg, insert it into the appropriate
    #aggregates table
    if (length($structID) < 1)
    {
      $q_name = $db->quote($name);
      $query = "INSERT INTO $dsSchema.$dbName (name) VALUES ($q_name)";
      $status = $db->do($query);
      KAPutil_handle_db_err($db, $status, $query);

      #get the unique ID for this aggregate
      $structID = $db->{q{mysql_insertid}};
      $aggID = $structID;
    }

    $dataSelOpts{'db'} = $db;
    $dataSelOpts{'dim'} = $dim;
    $dataSelOpts{'dbName'} = "$dsSchema.$dbName";
    $dataSelOpts{'structType'} = "a";
    $dataSelOpts{'structID'} = $structID;
    $dataSelOpts{'selMethod'} = $selMethod;
    $dataSelOpts{'selection'} = $selection;
    $dataSelOpts{'modifyItem'} = $modifyItem;
    $dataSelOpts{'segHierLevel'} = $segHierLevel;
    $dataSelOpts{'segmentationID'} = $segmentationID;
    $dataSelOpts{'selOp'} = $selOp;
    $dataSelOpts{'selDuration'} = $selDuration;
    $dataSelOpts{'selType'} = $selType;
    $dataSelOpts{'selEnding'} = $selEnding;
    $dataSelOpts{'selStarting'} = $selStarting;
    $dataSelOpts{'matchType'} = $matchType;

    datasel_add_item(\%dataSelOpts, @segmentIDs);

    #NB: the agg add/sub scripts get re-expanded in a few microseconds when
    #    the HTML header code is created

    #update the data source's and this agg's last modified time stamps
    $db->do("UPDATE dataSources SET lastModified=NOW() WHERE ID=$dsID");
    $db->do("UPDATE $dsSchema.$dbName SET lastUpdated=NOW() WHERE ID=$structID");
  }

  #if we're being called to append (or stop appending) a date to the name
  if ($action eq "appendDate")
  {
    $state = $q->param('state');

    if ($state eq "true")
    {
      $endDate = DSRagg_get_recent_date($db, $dsSchema, $aggID);
      $name = "$name $endDate";
      $appendEndDate = 1;
    }
    else
    {
      $name =~ m/^(.*) .*?$/;
      $name = $1;
      $appendEndDate = 0;
    }
    $q_name = $db->quote($name);
    $query = "UPDATE $dsSchema.$dbName \
        SET name=$q_name, appendEndDate=$appendEndDate WHERE ID=$aggID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);
  }

  #if we're a time aggregate, get the checkbox state for append date option
  if ($dim eq "t")
  {
    $query = "SELECT appendEndDate FROM $dsSchema.$dbName WHERE ID=$aggID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($appendEndDate) = $dbOutput->fetchrow_array;
    if ($appendEndDate == 1)
    {
      $appendEndDate = "CHECKED";
    }
    else
    {
      $appendEndDate = "";
    }
  }

  print_html_header();

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-lg-10 col-xl-10"> <!-- content -->

      <P>
      <DIV CLASS="accordion mx-auto" ID="accordion">
        <DIV CLASS="accordion-item border-primary">
          <H2 CLASS="accordion-header">
            <BUTTON CLASS="accordion-button bg-primary text-white icon-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
              Edit Aggregate
            </BUTTON>
          </H2>
          <DIV ID="collapse1" CLASS="accordion-collapse collapse show" data-bs-parent="#accordion">
            <DIV CLASS="accordion-body">

                <DIV CLASS="row g-0">

                  <DIV CLASS="col-11 overflow-auto">
                    <DIV ID="aggGrid" CLASS="grid" STYLE="margin:auto; font-size:13px;"></DIV>
                  </DIV>

                  <DIV CLASS="col-1">
                    <DIV CLASS="position-relative top-50 start-50 translate-middle">
                      <BUTTON TYPE="button" CLASS="btn btn-primary" onClick="topItem()" TITLE="Move selected items to the top of the aggregate"><I CLASS="bi bi-chevron-bar-up"></I></BUTTON>
                      <P></P>
                      <BUTTON TYPE="button" CLASS="btn btn-primary" onClick="upItem()" TITLE="Move selected items up"><I CLASS="bi bi-chevron-up"></I></BUTTON>
                      <P></P>
                      <BUTTON TYPE="button" CLASS="btn btn-primary" onClick="downItem()" TITLE="Move selected items down"><I CLASS="bi bi-chevron-down"></I></BUTTON>
                      <P></P>
                      <BUTTON TYPE="button" CLASS="btn btn-primary" onClick="bottomItem()" TITLE="Move selected items to the bottom of the aggregate"><I CLASS="bi bi-chevron-bar-down"></I></BUTTON>
                    </DIV>
                  </DIV>

                </DIV>

              <P>
              <DIV CLASS="text-center">
                <BUTTON TYPE="button" CLASS="btn btn-primary" onClick="location.href='/app/datasel/selectionMethod.cld?ds=$dsID&dim=$dim&st=a&sid=$aggID'" TITLE="Add a data selection to this aggregate"><I CLASS="bi bi-plus-lg"></I> Add</BUTTON>
                <BUTTON TYPE="button" CLASS="btn btn-primary mx-1" onClick="modifySelection()" TITLE="Edit the selected aggregate member"><I CLASS="bi bi-pencil"></I> Modify</BUTTON>
                <BUTTON TYPE="button" CLASS="btn btn-primary" onClick="removeSelection()" TITLE="Delete the selected aggregate members"><I CLASS="bi bi-trash"></I> Remove</BUTTON>
              </DIV>

            </DIV>
          </DIV>
        </DIV>

        <DIV CLASS="accordion-item border-primary">
          <H2 CLASS="accordion-header">
            <BUTTON CLASS="accordion-button collapsed bg-primary text-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
              Advanced Options
            </BUTTON>
          </H2>
          <DIV ID="collapse2" CLASS="accordion-collapse collapse" data-bs-parent="#accordion">
            <DIV CLASS="accordion-body">
END_HTML

   if ($dim eq "t")
   {
     print <<END_HTML;
              <DIV CLASS="form-check">
                <INPUT CLASS="form-check-input" TYPE='checkbox' NAME='appendDate' ID="appendDate" onclick="appendDate(this)" $appendEndDate>
                <LABEL CLASS="form-check-label" FOR="appendDate">Append the latest date in this aggregate to its name</LABEL>
              </DIV>

              <P>&nbsp;</P>
END_HTML
    }

   print <<END_HTML;
              Create a list based on this aggregate:
              <BUTTON TYPE="button" CLASS="btn btn-primary" onclick="location.href='listEdit.cld?ds=$dsID&dim=$dim&aggID=$aggID'">Create List</BUTTON>
            </DIV>
          </DIV>
        </DIV>
      </DIV>

      <P>
      <DIV CLASS="text-center">
        <BUTTON TYPE="button" CLASS="btn btn-primary" onclick="location.href='display.cld?ds=$dsID&dim=$dim'"><I CLASS="bi bi-check-lg"></I> Done</BUTTON>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

  utils_slack($activity);

#EOF
