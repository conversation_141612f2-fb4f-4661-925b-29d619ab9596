#!/usr/bin/perl

###############################################################################
#
# Create a copy of the specified data source, strip out everything but the
# total US geography, rename segmentations from AOD to Connect, and update
# any segmentation rules as appropriate.
#
###############################################################################

use lib "/opt/apache/app/";

use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;


  $dsID = $ARGV[0];
  $dsSchema = "datasource_" . $dsID;

  #connect to the database
  $db = KAPutil_connect_to_database();

  $query = "SELECT userID, name FROM app.dataSources WHERE ID=$dsID";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($userID, $name) = $dbOutput->fetchrow_array;
  $name = "SEG XFER $name";
  $description = "Temporary for Connect segmentation xfer";

  #start by creating a new entry in the dataSources table
  $q_name = $db->quote($name);
  $q_desc = $db->quote($description);
  $query = "INSERT INTO app.dataSources (userID, name, description) VALUES ($userID, $q_name, $q_desc)";
  $db->do($query);

  #get our new data source's unique ID
  $newDSID = $db->{q{mysql_insertid}};
  $newSchema = "datasource_" . $newDSID;

  #fill out the rest of new data source's table entry
  $query = "SELECT type, timeAlias FROM dataSources WHERE ID=$dsID";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($type, $timeAlias) = $dbOutput->fetchrow_array;

  $q_type = $db->quote($type);
  $q_description = $db->quote($description);
  $q_timeAlias = $db->quote($timeAlias);
  $query = "UPDATE dataSources \
      SET type=$q_type, description=$q_desc, timeAlias=$q_timeAlias, lastUpdate=NOW(), lastModified=NOW() \
      WHERE ID=$newDSID";
  $db->do($query);

  #next, dump out the data source contents to disk
  KAPutil_job_update_status($db, "Extracting source data");
  `/usr/bin/mysqldump -h$Lib::KoalaConfig::dbServerName -u app -p$Lib::KoalaConfig::password -e $dsSchema --ignore-table=$dbName.export > /opt/apache/app/logs/copy_$userID.$dsSchema.sql`;

  #create the new database schema
  $query = "CREATE DATABASE $newSchema";
  $db->do($query);

  #import the source data into the new data source
  KAPutil_job_update_status($db, "Loading data into new data source");
  `/usr/bin/mysql -h$Lib::KoalaConfig::dbServerName -u app -p$Lib::KoalaConfig::password $newSchema < /opt/apache/app/logs/copy_$userID.$dsSchema.sql`;

  #delete the data source dump file from disk
  unlink("/opt/apache/app/logs/copy_$userID.$dsSchema.sql");

  #delete all but Total US xAOC geography
  print STDERR "Removing extraneous geographies\n";
  %geoHash = dsr_get_base_item_name_hash($db, $newSchema, "g", 1);
  foreach $geoID (keys %geoHash)
  {
    if ($geoHash{$geoID} ne "Total US xAOC")
    {
      $query = "DELETE FROM $newSchema.facts WHERE geographyID=$geoID";
      $db->do($query);
      $query = "DELETE FROM $newSchema.geographies WHERE ID=$geoID";
      $db->do($query);
    }
  }


  #convert segmentation names
  %segMatchHash = (
  'BRAND OWNER HIGH' =>	'MANUFACTURER',
  'BRAND OWNER LOW'	=> 'BRAND OWNER',
  'BRAND HIGH'	=> 'BRAND',
  'BRAND LOW'	=> 'BRAND LOW',
  'MULTI'	=> 'IM MULTI CHAR',
  'PACK SIZE'	=> 'OUTER PACK SIZE',
  'BC DEPARTMENT'	=> 'DEPARTMENT',
  'BC SUPER CATEGORY'	=> 'SUPER CATEGORY',
  'BC CATEGORY'	=> 'CATEGORY',
  'BC SUB CATEGORY'	=> 'SUB CATEGORY',
  'BC SEGMENT'	=> 'SEGMENT',
  'BRANDED VS. PRIVATE LABEL' =>	'BRAND TYPE',
  );

  foreach $aodSeg (keys %segMatchHash)
  {
    $q_aodSeg = $db->quote($aodSeg);
    $q_connectSeg = $db->quote($segMatchHash{$aodSeg});
    $query = "UPDATE $newSchema.product_segmentation SET name=$q_connectSeg WHERE name=$q_aodSeg";
    $db->do($query);
  }


  #update segmentation rules
  #NO-OP - rules are based on IDs, so should auto-update to match


  #optimize new data source
  print STDERR "Optimizing new data source\n";
  $query = "OPTIMIZE TABLE $newSchema.facts";
  $db->do($query);
  $query = "OPTIMIZE TABLE $newSchema.geographies";
  $db->do($query);


#EOF
