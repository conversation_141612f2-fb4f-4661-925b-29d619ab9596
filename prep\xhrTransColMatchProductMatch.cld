#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $flowID = $q->param('f');
  $jobID = $q->param('j');
  $colID = $q->param('col');
  $dsID = $q->param('ds');
  $flowItemName = $q->param('fi');
  $dsItemID = $q->param('dsi');
  $action = $q->param('a');

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  $dsSchema = "datasource_" . $dsID;

  #make sure we have write privs for this data flow
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this data flow.");
  }

  $dsItemName = KAPutil_get_item_ID_name($kapDB, $dsSchema, "p", $dsItemID);


########################################################################
#
# This code block is called on submit to save the changes
#
  #if we're being called to verify the match
  if ($action eq "save")
  {

    #if the analyst accidentally said "Save" without picking a valid match
    if (!defined($dsItemID))
    {
      exit;
    }

    #if the analyst verified that this is a new (not-yet-created) item
    if ($dsItemID == 0)
    {
      $dsItemName = $flowItemName;
    }

    $q_flowItemName = $prepDB->quote($flowItemName);
    $q_dsItemName = $prepDB->quote($dsItemName);
    $query = "INSERT INTO prep.dim_matches \
        (flowID, dsID, dim, flowItem, dsItem, dsItemID, confidence) \
        VALUES ($flowID, $dsID, 'p', $q_flowItemName, $q_dsItemName, $dsItemID, 'verified') \
        ON DUPLICATE KEY UPDATE dsItem=$q_dsItemName, dsItemID=$dsItemID, confidence='verified'";
    $prepDB->do($query);

=pod
    $flowName = prep_flow_id_to_name($prepDB, $flowID);
    $dsName = ds_id_to_name($db, $dsID);

    prep_audit($db, $userID, $flowID, "Matched $flowItemName to $dsItemName in $dsName");
    $activity = "PREP: $first $last matched $flowItemName in $flowName to $dsItemName in $dsName";
    utils_slack($activity);
=cut

    exit;
  }

  #########################################################################
  #
  # Everything after this point is called to display the item match dialog
  #

  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #look for some common and useful attributes/segmentations
  $query = "SELECT ID, name FROM $masterColTable WHERE type='upc'";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($upcColID, $upcColName) = $dbOutput->fetchrow_array;
  if ($upcColID < 1)
  {
    $query = "SELECT ID, name FROM $masterColTable WHERE name LIKE '%upc%'";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($upcColID, $upcColName) = $dbOutput->fetchrow_array;
  }
  if ($upcColID > 0)
  {
    $query = "SELECT column_$upcColID FROM $masterTable WHERE column_$colID = '$flowItemName' LIMIT 1";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($upc) = $dbOutput->fetchrow_array;
  }
  if (length($upc) > 0)
  {
    $upcHTML = "<SPAN CLASS='text-muted small'><B>UPC:</B> $upc</SPAN><BR>";
  }

  %itemNameHash = dsr_get_item_name_hash($kapDB, $dsSchema, "p");

  @segmentationArray = DSRsegmentation_get_segmentations_array($kapDB, $dsSchema, "p");
  foreach $id (@segmentationArray)
  {
    $segIDStr .= "'$id',";
  }
  chop($segIDStr);

  $query = "SELECT segmentationID, segmentID FROM $dsSchema.product_segment_item \
      WHERE itemID=$dsItemID ORDER BY FIELD (segmentationID, $segIDStr)";
  $dbOutput = $kapDB->prepare($query);
  $dbOutput->execute;
  while (($segID, $segmentID) = $dbOutput->fetchrow_array)
  {
    $segName = $itemNameHash{"SEG_$segID"};
    $segmentName = $itemNameHash{"SMT_$segmentID"};

    if (($segmentName eq "NOT APPLICABLE") || ($segmentName eq "NOT STATED"))
    {
      next;
    }

    $segmentHTML .= "<SPAN CLASS='text-muted small'><B>$segName:</B> $segmentName</SPAN><BR>\n";
  }


  #get a list of every item in the data source
  %dsItemNameHash = dsr_get_base_item_name_hash($kapDB, $dsSchema, "p", 1);
  %unusedItemNameHash = dsr_get_base_item_name_hash($kapDB, $dsSchema, "p", 1);


  #an item with an ID of 0 implies a new item
  $dsItemNameHash{0} = "$flowItemName <SMALL CLASS='text-muted'>New Item</SMALL>";

  #
  #build a sorted array of all available match options, with best choices first
  #

  #current selection goes at top of list
  push(@rankedDSItemNames, $dsItemID);
  delete($unusedItemNameHash{$dsItemID});

  #if an item with an exact name match doesn't already exist in the DS, make
  #sure we display the "new item" option near the top
  $tmpID = KAPutil_base_name_to_ID($kapDB, $dsSchema, "p", $flowItemName);
  if (($tmpID < 1) && ($dsItemID > 0))
  {
    push(@rankedDSItemNames, 0);
    delete($unusedItemNameHash{0});
  }

  #list everything that's left in alpha order
  foreach $itemID (sort {$unusedItemNameHash{$a} cmp $unusedItemNameHash{$b} } keys %unusedItemNameHash)
  {
    push(@rankedDSItemNames, $itemID);
  }


  print <<END_HTML;
<SCRIPT>
function submitForm()
{
  let dsi = 0;
  for (i = 0; i < document.options.dsi.length; i++)
  {
    if (document.options.dsi[i].checked)
    {
      dsi = document.options.dsi[i].id;
    }
  }

  let url = 'xhrTransColMatchProductMatch.cld?a=save&f=$flowID&j=$jobID&ds=$dsID&fi=' + encodeURIComponent('$flowItemName') + '&dsi=' + dsi;

  \$.get(url, function(data, status)
  {
    location.href = 'transColMatchProducts.cld?f=$flowID&j=$jobID&ds=$dsID&col=$colID';
  });
}
</SCRIPT>

<DIV CLASS="modal-dialog modal-lg">
  <DIV CLASS="modal-content">
    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Match Items</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">

      <DIV CLASS="row">
        <DIV CLASS="col-6">

          <DIV CLASS="card" STYLE="height:400px; overflow:auto;">
            <DIV CLASS="card-body">
              <H5 CLASS="card-title">$flowItemName</H5>
              $upcHTML
              $segmentHTML
            </DIV>
          </DIV>

        </DIV>
        <DIV CLASS="col-6">
          <FORM NAME="options">

          <DIV CLASS="list-group" STYLE="height:400px; overflow:auto;">
END_HTML

  foreach $itemID (@rankedDSItemNames)
  {
    $checked = "";
    if ($itemID == $dsItemID)
    {
      $checked = "CHECKED";
    }
    print <<END_HTML;
            <DIV CLASS="list-group-item">
              <DIV CLASS="form-check">
                <INPUT CLASS="form-check-input" TYPE="radio" NAME="dsi" ID="$itemID" $checked>
                <LABEL CLASS="form-check-label" FOR="$itemID">$dsItemNameHash{$itemID}</LABEL>
              </DIV>
            </DIV>
END_HTML
  }

  print <<END_HTML;
          </DIV>
          </FORM>

        </DIV>
      </DIV>

    </DIV>

    <DIV CLASS="modal-footer">
      <DIV CLASS="text-center">
        <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
        <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="submitForm()"><I CLASS="bi bi-save"></I> Save</BUTTON>
      </DIV>
   </DIV>

  </DIV>
</DIV>
END_HTML

#EOF
