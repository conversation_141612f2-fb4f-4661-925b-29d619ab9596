#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepRecipes;
use Lib::PrepUtils;
use Lib::WebUtils;


#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $flowID = $q->param('f');
  $jobID = $q->param('j');
  $colID = $q->param('col');

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #make sure we have write privs for this data flow
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this data flow.");
  }

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #build name of column containing the text
  $column = "column_" . $colID;

  #get the current column name for the basis of the split col names
  $query = "SELECT name FROM $masterColTable WHERE ID=$colID";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($colName) = $dbOutput->fetchrow_array;

  print <<END_HTML;
<SCRIPT>
function showOtherCharDiv()
{
  let sepChar = document.getElementById('sepChar').value;

  if (sepChar == "other")
  {
    document.getElementById('other-char-div').style.visibility = 'visible';
  }
  else
  {
    document.getElementById('other-char-div').style.visibility = 'hidden';
  }
}



function checkForm(form)
{
  \$("#btn-submit").prop('disabled', true);
  \$("#btn-submit").text("Please Wait...");
  return(true);
}
</SCRIPT>

<FORM METHOD="post" ACTION="transformProgress.cld" onsubmit="return checkForm(this);">
<INPUT TYPE="hidden" NAME="f" VALUE="$flowID">
<INPUT TYPE="hidden" NAME="j" VALUE="$jobID">
<INPUT TYPE="hidden" NAME="col" VALUE="$colID">
<INPUT TYPE="hidden" NAME="a" VALUE="TRANS-COL-SPLIT">

<DIV CLASS="modal-dialog">
  <DIV CLASS="modal-content">

    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Split Column By Separator</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">
      <LABEL FOR="sepChar">Split the selected column into multiple columns on</LABEL>
      <SELECT ID="sepChar" NAME="sepChar" CLASS="form-select" onchange="showOtherCharDiv()">
        <OPTION VALUE="comma">Comma</OPTION>
        <OPTION VALUE="space">Space</OPTION>
        <OPTION VALUE="tab">Tab</OPTION>
        <OPTION VALUE="pipe">Pipe (|)</OPTION>
        <OPTION VALUE="other">Other</OPTION>
      </SELECT>

      <DIV ID="other-char-div" STYLE="visibility:hidden;">
        <DIV CLASS="row mt-2">
          <DIV CLASS="col-auto mt-2">
            <LABEL FOR="otherChar">Character to split on:</LABEL>
          </DIV>
          <DIV CLASS="col-auto">
            <INPUT TYPE="text" CLASS="form-control" ID="otherChar" NAME="otherChar" STYLE="width:3em;" MAXLENGTH=1>
          </DIV>
        </DIV>
      </DIV>

    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-submit"><I CLASS="bi bi-pencil-square"></I> Apply</BUTTON>
    </DIV>

  </DIV>
</DIV>
</FORM>

END_HTML

#EOF
