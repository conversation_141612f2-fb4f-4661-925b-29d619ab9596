package Lib::Reports;

use lib "/opt/apache/app/";

use Exporter;

use Lib::DSRUtils;

our @ISA = ('Exporter');

our @EXPORT = qw(
    &reports_data_script
    &reports_graph_alias
    &reports_graph_type
    &reports_graph_color_array
    &reports_chart_design_default
    &reports_get_style
    &reports_set_style
    &reports_set_captions
    &reports_remove_style
    &reports_get_conditional_formatting_hash
    &reports_ds_list
    &reports_multiselect
    &reports_get_selected_items
    &reports_table_default_selections
    &reports_graph_default_selections
    &reports_map_default_selections
    &reports_expand_dim_tags
    &report_id_to_name
    &reports_table_filter_SQL
    &reports_table_excludeNA_SQL
  );



#-------------------------------------------------------------------------
#
# Handle a database error of some kind during a report API cal;
#

sub reports_db_err
{
  my ($date);

  my ($db, $status, $text) = @_;


  if (!defined($status))
  {
    $date = localtime();
    print STDERR "$date: $text\n";
    if ($db->errstr =~ m/^MySQL server has gone away/)
    {
      die("Lost connection to database, terminating");
    }
  }
}



#-------------------------------------------------------------------------
#
# Return the correct AJAX data backing script for the supplied graph type
#

sub reports_data_script
{
  my ($scriptName);

  my ($graphType) = @_;


  #manually specify the backing AJAX data script for each chart type
  my %dataScripts = (
      "Lines" => "ajaxReportGraphMulti.cld",
      "ZoomLines" => "ajaxReportGraphMulti.cld",
      "Scatter" => "ajaxReportGraphXYZ.cld",
      "Area" => "ajaxReportGraphMulti.cld",
      "Bubble" => "ajaxReportGraphXYZ.cld",
      "2DColumns" => "ajaxReportGraphMulti.cld",
      "Stacked2DColumns" => "ajaxReportGraphMulti.cld",
      "3DColumns" => "ajaxReportGraphMulti.cld",
      "Stacked3DColumns" => "ajaxReportGraphMulti.cld",
      "2DBars" => "ajaxReportGraphMulti.cld",
      "Stacked2DBars" => "ajaxReportGraphMulti.cld",
      "3DBars" => "ajaxReportGraphMulti.cld",
      "Stacked3DBars" => "ajaxReportGraphMulti.cld",
      "Waterfall" => "ajaxReportGraphSingle.cld",
      "2DPie" =>  "ajaxReportGraphSingle.cld",
      "3DPie" =>  "ajaxReportGraphSingle.cld",
      "2DDonut" =>  "ajaxReportGraphSingle.cld",
      "3DDonut" => "ajaxReportGraphSingle.cld",
      "Radar" => "ajaxReportGraphMulti.cld",
      "TreeMap" => "ajaxReportGraphTree.cld",
      "Funnel" => "ajaxReportGraphSingle.cld",
      "Pareto" => "ajaxReportGraphSingle.cld",
      "ColumnLine" => "ajaxReportGraphMulti.cld",
      "DualY" => "ajaxReportGraphMulti.cld",
    );

  $scriptName = $dataScripts{$graphType};
  return($scriptName);
}



#-------------------------------------------------------------------------
#
# Return the FusionCharts JS library to use, based on our graph name
#

sub reports_graph_alias
{
  my ($graphAlias);

  my ($graphType) = @_;


  #manually specify the FusionCharts JS library for each chart type
  my %FSLibs = (
      "Lines" => "MSLine",
      "ZoomLines" => "ZoomLine",
      "Scatter" => "Scatter",
      "Area" => "MSArea",
      "Bubble" => "Bubble",
      "2DColumns" => "MSColumn2D",
      "Stacked2DColumns" => "stackedcolumn2d",
      "3DColumns" => "MSColumn3D",
      "Stacked3DColumns" => "stackedcolumn3d",
      "2DBars" => "MSBar2D",
      "Stacked2DBars" => "stackedbar2d",
      "3DBars" => "MSBar3D",
      "Stacked3DBars" => "stackedbar3d",
      "Waterfall" => "Waterfall2D",
      "2DPie" =>  "Pie2D",
      "3DPie" =>  "Pie3D",
      "2DDonut" =>  "Doughnut2D",
      "3DDonut" => "Doughnut3D",
      "Radar" => "Radar",
      "TreeMap" => "treeMap",
      "Funnel" => "funnel",
      "Pareto" => "pareto2d",
      "ColumnLine" => "mscombi2d",
      "DualY" => "mscombidy2d",
    );

  $graphAlias = $FSLibs{$graphType};
  return($graphAlias);
}



#-------------------------------------------------------------------------
#
# Return array of hex color codes for default theme
#

sub reports_graph_color_array
{
  my (@colors);


  #PowerBI classic
  $colors[0] = "#01B8AA";  $colors[1] = "#4A8DDC";  $colors[2] = "#FD625E";
  $colors[3] = "#F2C80F";  $colors[4] = "#5F6B6D";  $colors[5] = "#8AD4EB";
  $colors[6] = "#FE9666";  $colors[7] = "#A66999";  $colors[8] = "#99E3DD";
  $colors[9] = "#AFB5B6";  $colors[10] = "#FEC0BF";  $colors[11] = "#FAE99F";
  $colors[12] = "#BFC4C5";  $colors[13] = "#D0EEF7";  $colors[14] = "#FFD5C2";
  $colors[15] = "#DBC3D6";  $colors[16] = "#67D4CC";  $colors[17] = "#879092";
  $colors[18] = "#FEA19E";  $colors[19] = "#F7DE6F";  $colors[20] = "#9FA6A7";
  $colors[21] = "#B9E5F3";  $colors[22] = "#FEC0A3";  $colors[23] = "#CAA5C2";
  $colors[24] = "#34C6BB";  $colors[25] = "#5F6B6D";  $colors[26] = "#FD817E";
  $colors[27] = "#F5D33F";  $colors[28] = "#7F898A";  $colors[29] = "#A1DDEF";
  $colors[30] = "#FEAB85";  $colors[31] = "#B887AD";  $colors[32] = "#018A80";
  $colors[33] = "#293537";  $colors[34] = "#BE4A47";  $colors[35] = "#B6960B";
  $colors[36] = "#475052";  $colors[37] = "#689FB0";  $colors[38] = "#BF714D";
  $colors[39] = "#7D4F73";  $colors[40] = "#015C55";  $colors[41] = "#1C2325";
  $colors[42] = "#7F312F";  $colors[43] = "#796408";  $colors[44] = "#303637";
  $colors[45] = "#456A76";  $colors[46] = "#7F4B33";  $colors[47] = "#53354D";

  return(@colors);
}



#-------------------------------------------------------------------------
#
# Return a human-readable graph type based on internal type string
#

sub reports_graph_type
{
  my ($graphAlias);

  my ($graphType) = @_;


  my %Types = (
      "Lines" => "Line",
      "ZoomLines" => "Zoom Line",
      "Scatter" => "Scatter",
      "Area" => "Area",
      "Bubble" => "Bubble",
      "2DColumns" => "Column",
      "3DColumns" => "3D Column",
      "2DBars" => "Bar",
      "3DBars" => "3D Bar",
      "Waterfall" => "Waterfall",
      "2DPie" =>  "Pie",
      "3DPie" =>  "3D Pie",
      "2DDonut" =>  "Doughnut",
      "3DDonut" => "3D Doughnut",
      "Radar" => "Radar",
      "TreeMap" => "Tree Map",
      "Funnel" => "Funnel",
      "Pareto" => "Pareto",
      "ColumnLine" => "Combined",
      "DualY" => "Dual Y",
    );

  $graphAlias = $Types{$graphType};
  return($graphAlias);
}



#-------------------------------------------------------------------------
#
# Return the default chart styling elements
#

sub reports_chart_design_default
{
  my ($value);
  my (%chartDefaults);

  my ($styleItem) = @_;


  %chartDefaults = (
      "bgColor" => "#ffffff",
      "showBorder"  => 0,
      "borderColor" => "#cccccc",
      "borderThickness" => 1,

      "captionFontColor" => "#333333",
      "captionAlignment" => "center",
      "captionFontSize" => 14,
      "captionFont" => "Helvetica",
      "xAxisNameFontSize" => 10,
      "yAxisNameFontSize" => 10,

      "showLegend" => 1,
      "legendItemFontColor" => "#333333",
      "legendPosition" => "bottom",
      "legendItemFont" => "Helvetica",
      "legendItemFontSize" => 10,

      "showLabels" => 1,
      "labelFontColor" => "#333333",
      "labelDisplay" => "auto",
      "labelStep" => 1,
      "labelFontSize" => 10,
      "labelFont" => "Helvetica",

      "showValues" => 1,
      "valueFontColor" => "#333333",
      "formatNumberScale" => 1,
      "showPercentValues" => 0,
      "decimals" => 2,
      "placeValuesInside" => 0,
      "rotateValues" => 0,
      "valueFontSize" => 10,
      "valueFont" => "Helvetica",
      "showValuesBg" => 0,
      "valueBgColor" => "#cccccc",
      "valueBgAlpha" => 90,

      "showSumAtEnd" => 1,
    );

  $value = $chartDefaults{$styleItem};
  return($value);
}



#-------------------------------------------------------------------------
#
# Extract the value for the specified style item from the supplied graph
# style string (pulled from the reports database). Returns undefined if the
# specified style item doesn't exist.
#

sub reports_get_style
{
  my ($val);

  my ($graphStyle, $styleItem) = @_;


  undef($val);
  if (($graphStyle =~ m/,$styleItem:(.*?),/) ||
      ($graphStyle =~ m/^$styleItem:(.*?),/))
  {
    $val = $1;
  }

  return($val);
}



#-------------------------------------------------------------------------
#
# Set the value for the specified style item in the supplied graph style
# string.
#

sub reports_set_style
{
  my (@designElements, $done, $updatedDesign, $element);

  my ($graphDesign, $styleItem, $styleValue) = @_;


  #split the graphDesign string into style substrings
  @designElements = split(',', $graphDesign);

  #run through the list of style strings, looking for the one we're updating
  $done = 0;
  $updatedDesign = "";
  foreach $element (@designElements)
  {

    #if the current element is the one we're looking for
    if ($element =~ m/^$styleItem:/)
    {

      #if the value is empty, we want to delete the element
      if (length($styleValue) < 1)
      {
        #NO-OP - effectively deletes the element
      }
      else
      {
        $updatedDesign .= "$styleItem:$styleValue,";
      }
      $done = 1;
    }

    #else blind copy the current element
    else
    {
      $updatedDesign .= "$element,";
    }
  }

  #if the style element wasn't already in the design string, add it
  if (($done == 0) && (length($styleValue) > 0))
  {
    $updatedDesign .= "$styleItem:$styleValue,";
  }

  return($updatedDesign);
}



#-------------------------------------------------------------------------
#
# Set the captions for the specified report, correctly handling "specials"
# like commas.
#

sub reports_set_captions
{
  my (@designElements, $done, $updatedDesign, $element);

  my ($design, $styleItem, $styleValue) = @_;


  $design =~ m/^(.*\,)$styleItem:\".*?\"(.*)$/;

  #if the caption/subcaption needs to be replaced
  if (length($1) > 0)
  {
    $design = $1 . "$styleItem:\"$styleValue\"" . $2;
  }

  #if there was no previous caption/subcaption so we need to add one
  else
  {
    $design .= "$styleItem:\"$styleValue\",";
  }

  return($design);
}



#-------------------------------------------------------------------------
#
# Remove the specified style item from the supplied visualization style
# string (mostly used to remove conditional formatting rules)
#

sub reports_remove_style
{
  my (@designElements, $done, $updatedDesign, $element);

  my ($graphDesign, $styleItem) = @_;


  #split the graphDesign string into style substrings
  @designElements = split(',', $graphDesign);

  #run through the list of style strings, looking for the one we're removing
  $done = 0;
  $updatedDesign = "";
  foreach $element (@designElements)
  {

    #if the current element is the one we're looking for
    if ($element =~ m/^$styleItem:/)
    {
      #NO-OP to remove the style item
    }

    #else blind copy the current element
    else
    {
      $updatedDesign .= "$element,";
    }
  }

  return($updatedDesign);
}



#-------------------------------------------------------------------------
#
# Build up a measureID-keyed hash of conditional formatting rules
#

sub reports_get_conditional_formatting_hash
{
  my ($query, $dbOutput, $design, $status, $style);
  my (@styles);
  my (%condFormattingHash);

  my ($db, $visID) = @_;


  undef(%condFormattingHash);

  #get the design string for the specified table
  $query = "SELECT design FROM app.visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  reports_db_err($db, $status, $query);
  ($design) = $dbOutput->fetchrow_array;

  @styles = split(',', $design);
  foreach $style (@styles)
  {
    if ($style =~ m/^condFormat(\d+):(\d+) (.*?) (.*?) (.*)$/)
    {
      $condFormattingHash{$2} = "$condFormattingHash{$2},$3 $4 $5";
    }
  }

  return(%condFormattingHash)
}



#-------------------------------------------------------------------------
#
# Return an array of data source IDs that contain reports shared with the
# specified user.
#

sub reports_ds_list
{
  my ($query, $dbOutput, $privs, $ownerID, $Rusers, $RWusers, $ruser, $dsID);
  my ($acctType, $status);
  my (@userSources, @rusers, @rwusers);

  my ($db, $userID) = @_;


  undef(@userSources);

  #get the data source and priv info of all reports on the system
  $query = "SELECT dsID, userID, Rusers, RWusers FROM cubes";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  reports_db_err($db, $status, $query);

  #run through the list of data sources
  while (($dsID, $ownerID, $Rusers, $RWusers) = $dbOutput->fetchrow_array)
  {

    #split the user lists into arrays, and combine
    @rusers = split(',', $Rusers);
    @rwusers = split(',', $RWusers);
    push(@rusers, @rwusers);

    #push the data source owner's ID onto the combined array
    push(@rusers, $ownerID);

    #see if the user has privs, and add the data source ID to the returned
    #array if so
    foreach $ruser (@rusers)
    {
      if (($ruser == $userID) || ($acctType > 4))
      {
        push(@userSources, $dsID);
      }
    }
  }

  return(@userSources);
}



#-------------------------------------------------------------------------
#
# Return an array containing the correct multi-select status for the
# specified visualization. (If no visualization is specified, do it for
# everything in the report.)
#

sub reports_multiselect
{
  my ($query, $dbOutput, $multiProd, $multiGeo, $multiTime, $multiMeas);
  my ($tmp, $type, $design, $tableRowDims, $tableColDims, $graph_x, $graph_y);
  my ($graph_z, $graphType, $status);
  my (@multiStates);

  my ($db, $rptID, $visID) = @_;


  #start off by not allowing any dimension to have multiple selections
  #NB: A value of 1 corresponds to single-selction mode in our tree control, a
  #    value of 3 corresponds to hierarchical multi-selection
  $multiProd = 1;
  $multiGeo = 1;
  $multiTime = 1;
  $multiMeas = 1;

  #get multiselect states for specified visual
  if ($visID > 0)
  {
    $query = "SELECT type, design, tableRowDims, tableColDims, graph_x, graph_y, graph_z \
        FROM app.visuals WHERE ID=$visID";
  }

  #else for the whole report if no visualization specified
  else
  {
    $query = "SELECT type, design, tableRowDims, tableColDims, graph_x, graph_y, graph_z \
        FROM app.visuals WHERE cubeID=$rptID";
  }

  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute();
  reports_db_err($db, $status, $query);
  while (($type, $design, $tableRowDims, $tableColDims, $graph_x, $graph_y, $graph_z) = $dbOutput->fetchrow_array)
  {

    #if we're a table, the rows and columns and measures are multi-selectable
    if ($type eq "table")
    {
      $multiMeas = 3;

      if (($tableRowDims =~ m/p/) || ($tableColDims =~ m/p/))
      {
        $multiProd = 3;
      }
      if (($tableRowDims =~ m/g/) || ($tableColDims =~ m/g/))
      {
        $multiGeo = 3;
      }
      if (($tableRowDims =~ m/t/) || ($tableColDims =~ m/t/))
      {
        $multiTime = 3;
      }
    }

    #if we're a graph, the axes are multi-selectable
    if ($type eq "chart")
    {

      #get the type of graph to verify we only use valid axis definitions
      $graphType = reports_get_style($design, "type");
      if (($graphType eq "Waterfall") || ($graphType eq "2DPie") ||
          ($graphType eq "3DPie") || ($graphType eq "2DDonut") ||
          ($graphType eq "3DDonut") || ($graphType eq "TreeMap") ||
          ($graphType eq "Scatter") || ($graphType eq "Bubble") ||
          ($graphType eq "Funnel") || ($graphType eq "Pareto"))
      {
        $tmp = "$graph_x";
      }
      else
      {
        $tmp = "$graph_x,$graph_y";
      }

      if ($tmp =~ m/p/)
      {
        $multiProd = 3;
      }
      if ($tmp =~ m/g/)
      {
        $multiGeo = 3;
      }
      if ($tmp =~ m/t/)
      {
        $multiTime = 3;
      }
      if ($tmp =~ m/m/)
      {
        $multiMeas = 3;
      }
    }

    #if we're a map, geos are multi-selectable
    if ($type eq "map")
    {
      $multiGeo = 3;
    }
  }

  undef(@multiStates);
  @multiStates = ($multiProd, $multiGeo, $multiTime, $multiMeas);

  return(@multiStates);
}



#-------------------------------------------------------------------------
#
# Internal function that reorders the specified string containing current
# display selections for a report to match the analyst-specified order of
# items.
#

sub _reports_reorder_selection_string
{
  my ($itemID);
  my (@selIDs, @orderedIDs, @newSelIDs);
  my (%selIDHash);

  my ($selectionStr, $orderStr) = @_;


  @orderedIDs = split(',', $orderStr);
  @selIDs = split(',', $selectionStr);

  foreach $itemID (@selIDs)
  {
    $selIDHash{$itemID} = 1;
  }

  foreach $itemID (@orderedIDs)
  {
    if ($selIDHash{$itemID} > 0)
    {
      push(@newSelIDs, $itemID);
    }
  }

  $orderStr = join(',', @newSelIDs);
  return($orderStr);
}



#-------------------------------------------------------------------------
#
# Return each dimension's item selection strings for the specified visual.
# The trick here is to determine if the user is a "viewer" or an analyst,
# and return the selection strings from the correct table handling a
# default scenario.
#

sub reports_get_selected_items
{
  my ($query, $status, $dbOutput, $rptID);
  my ($productIDstring, $geographyIDstring, $timeIDstring, $measureIDstring);
  my ($prodOrderStr, $geoOrderStr, $timeOrderStr, $measOrderStr);

  my ($db, $visID, $userID, $acctType) = @_;


  #if the user is a viewer, use the viewer selection table
  if ($acctType == 0)
  {
    $query = "SELECT cubeID, selProducts, selGeographies, selTimeperiods, selMeasures \
        FROM app.visuals_viewers WHERE ID=$visID AND userID=$userID";
  }

  #else use analyst-set defaults from master visuals table
  else
  {
    $query = "SELECT cubeID, selProducts, selGeographies, selTimeperiods, selMeasures \
        FROM app.visuals WHERE ID=$visID";
  }

  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  reports_db_err($db, $status, $query);
  ($rptID, $productIDstring, $geographyIDstring, $timeIDstring, $measureIDstring) = $dbOutput->fetchrow_array;

  #if we're a viewer, and we don't have a custom selection for this visual
  if (($acctType == 0) && ($status == 0))
  {

    #grab the defaults from the master visuals table
    $query = "SELECT cubeID, selProducts, selGeographies, selTimeperiods, selMeasures \
        FROM app.visuals WHERE ID=$visID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    reports_db_err($db, $status, $query);
    ($rptID, $productIDstring, $geographyIDstring, $timeIDstring, $measureIDstring) = $dbOutput->fetchrow_array;
  }

  #get the analyst-ordered selection lists, and re-order the selected item
  #strings to match (need to do this to have graphs come out in user-specified
  #order)
  $query = "SELECT products, geographies, timeperiods, measures \
      FROM app.cubes WHERE ID=$rptID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  reports_db_err($db, $status, $query);
  ($prodOrderStr, $geoOrderStr, $timeOrderStr, $measOrderStr) = $dbOutput->fetchrow_array;

  $productIDstring = _reports_reorder_selection_string($productIDstring, $prodOrderStr);
  $geographyIDstring = _reports_reorder_selection_string($geographyIDstring, $geoOrderStr);
  $timeIDstring = _reports_reorder_selection_string($timeIDstring, $timeOrderStr);
  $measureIDstring = _reports_reorder_selection_string($measureIDstring, $measOrderStr);

  return($productIDstring, $geographyIDstring, $timeIDstring, $measureIDstring);
}



#-------------------------------------------------------------------------
#
# Make sensible default data/plotted dimension selections for the supplied
# table.
#

sub reports_table_default_selections
{
  my ($query, $dbOutput, $tableDesign, $tableRowDims, $tableFilterDims);
  my ($tableColDims, $count, $id, $type, $design, $width, $height, $xpct);
  my ($ypct, $tWidth, $tHeight, $q_design, $q_tableDesign, $i);
  my ($selProdStr, $selGeoStr, $selTimeStr, $selMeasStr, $prodStr, $geoStr);
  my ($timeStr, $measStr, $status);
  my (@prodIDs, @geoIDs, @timeIDs, @measIDs, @selProdIDs, @selGeoIDs);
  my (@selTimeIDs, @selMeasIDs);
  my (%union, %isect);

  my ($db, $rptID, $visID) = @_;


  #get the current table layout and data selections
  $query = "SELECT design, tableRowDims, tableFilterDims, tableColDims, selProducts, selGeographies, selTimeperiods, selMeasures \
      FROM visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  reports_db_err($db, $status, $query);
  ($tableDesign, $tableRowDims, $tableFilterDims, $tableColDims, $selProdStr, $selGeoStr, $selTimeStr, $selMeasStr) = $dbOutput->fetchrow_array;

  #get info about the items included in the report
  $query = "SELECT products, geographies, timeperiods, measures FROM cubes \
      WHERE ID=$rptID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  reports_db_err($db, $status, $query);
  ($prodStr, $geoStr, $timeStr, $measStr) = $dbOutput->fetchrow_array;

  #convert cube items into arrays
  @prodIDs = split(',', $prodStr);
  @geoIDs = split(',', $geoStr);
  @timeIDs = split(',', $timeStr);
  @measIDs = split(',', $measStr);

  #convert data selections into arrays
  @selProdIDs = split(',', $selProdStr);
  @selGeoIDs = split(',', $selGeoStr);
  @selTimeIDs = split(',', $selTimeStr);
  @selMeasIDs = split(',', $selMeasStr);

  #calculate the intersection of the selection and item arrays (effectively
  #removes any items from the selection array that are no longer in the cube)
  undef(%union);        undef(%isect);
  foreach $i (@prodIDs, @selProdIDs) { $union{$i}++ && $isect{$i}++ };
  @selProdIDs = keys %isect;
  undef(%union);        undef(%isect);
  foreach $i (@geoIDs, @selGeoIDs) { $union{$i}++ && $isect{$i}++ };
  @selGeoIDs = keys %isect;
  undef(%union);        undef(%isect);
  foreach $i (@timeIDs, @selTimeIDs) { $union{$i}++ && $isect{$i}++ };
  @selTimeIDs = keys %isect;
  undef(%union);        undef(%isect);
  foreach $i (@measIDs, @selMeasIDs) { $union{$i}++ && $isect{$i}++ };
  @selMeasIDs = keys %isect;

  #if no row dimension is specified, set table layout to default
  if (length($tableRowDims) < 1)
  {
    $tableRowDims = "p";
    $tableFilterDims = "";
    $tableColDims = "";
    $query = "UPDATE visuals SET tableRowDims='p', tableFilterDims=NULL, tableColDims=NULL \
        WHERE ID=$visID";
    $status = $db->do($query);
    reports_db_err($db, $status, $query);
  }

  #extract our current sizing/position info (if not defined, we're being added)
  $width = reports_get_style($tableDesign, "width");
  $height = reports_get_style($tableDesign, "height");
  $xpct = reports_get_style($tableDesign, "xpct");
  $ypct = reports_get_style($tableDesign, "ypct");

  #see if we're the first visual being added to the report
  $query = "SELECT ID, type, design FROM visuals \
      WHERE cubeID=$rptID AND ID != $visID";
  $dbOutput = $db->prepare($query);
  $status = $count = $dbOutput->execute;
  reports_db_err($db, $status, $query);
  ($id, $type, $design) = $dbOutput->fetchrow_array;

  #if we're first, set our table to take up the whole canvas
  if ($count < 1)
  {
    $width = "0.98";
    $height = "0.98";
    $xpct = 0;
    $ypct = 0;
  }

  #if we aren't the only visual and need sizing, only take up 25% of the canvas
  elsif (($width < 0.01) || ($height < 0.01))
  {
    $width = "0.48";
    $height = "0.48";
    $xpct = "0.5";
    $ypct = "0.5";

    #if the only other visual is a table that takes the whole screen, shrink
    if (($count == 1) && ($type eq "table"))
    {
      $tWidth = reports_get_style($design, "width");
      $tHeight = reports_get_style($design, "height");

      if (($tWidth > 0.5) && ($tHeight > 0.5))
      {
        $design = reports_set_style($design, "width", "0.48");
        $design = reports_set_style($design, "height", "0.48");
        $q_design = $db->quote($design);
        $query = "UPDATE visuals SET design = $q_design WHERE ID=$id";
        $status = $db->do($query);
        reports_db_err($db, $status, $query);
      }
    }
  }

  $tableDesign = reports_set_style($tableDesign, "width", "$width");
  $tableDesign = reports_set_style($tableDesign, "height", "$height");
  $tableDesign = reports_set_style($tableDesign, "xpct", "$xpct");
  $tableDesign = reports_set_style($tableDesign, "ypct", "$ypct");

  #if products are a row or column dimension, select all
  if (($tableRowDims =~ m/p/) || ($tableColDims =~ m/p/))
  {
    @selProdIDs = @prodIDs;
  }

  #if we don't have any product selections, select the first one
  if (scalar(@selProdIDs) < 1)
  {
    $selProdIDs[0] = $prodIDs[0];
  }

  #if geographies are a row dimension, select all
  if ($tableRowDims =~ m/g/)
  {
    @selGeoIDs = @geoIDs;
  }

  #if geographies are a column dimension and 1 or less are selected, select all
  if (($tableColDims =~ m/g/) && (scalar(@selGeoIDs) < 2))
  {
    @selGeoIDs = @geoIDs;
  }

  #if we don't have any geography selections, select the first one
  if (scalar(@selGeoIDs) < 1)
  {
    $selGeoIDs[0] = $geoIDs[0];
  }

  #if time periods are a row or column dimension, select all
  if (($tableRowDims =~ m/t/) || ($tableColDims =~ m/t/))
  {
    @selTimeIDs = @timeIDs;
  }

  #if we don't have any time period selections, select the first one
  if (scalar(@selTimeIDs) < 1)
  {
    $selTimeIDs[0] = $timeIDs[0];
  }

  #if we don't have any measure selections, select them all
  if (scalar(@selMeasIDs) < 1)
  {
    @selMeasIDs = @measIDs;
  }

  $q_tableDesign = $db->quote($tableDesign);
  $selProdStr = join(',', @selProdIDs);
  $selGeoStr = join(',', @selGeoIDs);
  $selTimeStr = join(',', @selTimeIDs);
  $selMeasStr = join(',', @selMeasIDs);

  $query = "UPDATE visuals \
      SET design=$q_tableDesign, selProducts='$selProdStr', selGeographies='$selGeoStr', selTimeperiods='$selTimeStr', selMeasures='$selMeasStr' \
      WHERE ID=$visID";
  $status = $db->do($query);
  reports_db_err($db, $status, $query);

  #wipe out any now-stale custom selections made by viewers
  $query = "DELETE FROM visuals_viewers WHERE ID=$visID";
  $status = $db->do($query);
  reports_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------
#
# Make sensible default data/plotted dimension selections for the supplied
# graph. We take into account the type of graph and the selections inside
# each of its dimensions
#

sub reports_graph_default_selections
{
  my ($query, $dbOutput, $graph_x, $graph_y, $graph_z, $reportType, $i);
  my ($graphType, $graphDesign, $prodStr, $geoStr, $timeStr, $measureStr);
  my ($selProds, $selGeos, $selTimes, $selMeasures, $count, $id, $type);
  my ($design, $width, $height, $xpct, $ypct, $tWidth, $tHeight, $q_design);
  my ($q_graphDesign, $graphIdx, $selProdStr, $selGeoStr, $selTimeStr);
  my ($selMeasStr, $measStr, $measureX, $measureY, $measureZ, $status);
  my (@products, @geographies, @timeperiods, @measures, @prodIDs, @geoIDs);
  my (@timeIDs, @measIDs, @selProdIDs, @selGeoIDs, @selTimeIDs, @selMeasIDs);
  my (%union, %isect);

  my ($db, $rptID, $visID, $lockSizing) = @_;


  #get the graph type and current style selections
  $query = "SELECT design, graph_x, graph_y, graph_z, selProducts, selGeographies, selTimeperiods, selMeasures \
      FROM visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  reports_db_err($db, $status, $query);
  ($graphDesign, $graph_x, $graph_y, $graph_z, $selProdStr, $selGeoStr, $selTimeStr, $selMeasStr) = $dbOutput->fetchrow_array;

  #get info about the items included in the report
  $query = "SELECT products, geographies, timeperiods, measures \
      FROM cubes WHERE ID=$rptID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  reports_db_err($db, $status, $query);
  ($prodStr, $geoStr, $timeStr, $measStr) = $dbOutput->fetchrow_array;

  #convert cube items into arrays
  @prodIDs = split(',', $prodStr);
  @geoIDs = split(',', $geoStr);
  @timeIDs = split(',', $timeStr);
  @measIDs = split(',', $measStr);

  #convert data selections into arrays
  @selProdIDs = split(',', $selProdStr);
  @selGeoIDs = split(',', $selGeoStr);
  @selTimeIDs = split(',', $selTimeStr);
  @selMeasIDs = split(',', $selMeasStr);

  #calculate the intersection of the selection and item arrays (effectively
  #removes any items from the selection array that are no longer in the cube)
  undef(%union);        undef(%isect);
  foreach $i (@prodIDs, @selProdIDs) { $union{$i}++ && $isect{$i}++ };
  @selProdIDs = keys %isect;
  undef(%union);        undef(%isect);
  foreach $i (@geoIDs, @selGeoIDs) { $union{$i}++ && $isect{$i}++ };
  @selGeoIDs = keys %isect;
  undef(%union);        undef(%isect);
  foreach $i (@timeIDs, @selTimeIDs) { $union{$i}++ && $isect{$i}++ };
  @selTimeIDs = keys %isect;
  undef(%union);        undef(%isect);
  foreach $i (@measIDs, @selMeasIDs) { $union{$i}++ && $isect{$i}++ };
  @selMeasIDs = keys %isect;

  #extract our current sizing/position info (if not defined, we're being added)
  $width = reports_get_style($graphDesign, "width");
  $height = reports_get_style($graphDesign, "height");
  $xpct = reports_get_style($graphDesign, "xpct");
  $ypct = reports_get_style($graphDesign, "ypct");

  #see if we're the first visual being added to the report
  $query = "SELECT ID, type, design FROM visuals \
      WHERE cubeID=$rptID AND ID != $visID";
  $dbOutput = $db->prepare($query);
  $count = $status = $dbOutput->execute;
  reports_db_err($db, $status, $query);
  ($id, $type, $design) = $dbOutput->fetchrow_array;

  #if we're first, we want to take up 25% of the canvas
  if (($count < 1) && ($lockSizing < 1))
  {
    $width = "0.48";
    $height = "0.48";
    $xpct = 0;
    $ypct = 0;
  }

  #if the only other visual is a table that takes the whole screen, shrink it
  if (($count == 1) && ($type eq "table") && ($lockSizing < 1))
  {
    $tWidth = reports_get_style($design, "width");
    $tHeight = reports_get_style($design, "height");
    if (($tWidth > 0.5) && ($tHeight > 0.5))
    {
      $design = reports_set_style($design, "width", "0.48");
      $design = reports_set_style($design, "height", "0.48");
      $q_design = $db->quote($design);
      $query = "UPDATE visuals SET design = $q_design WHERE ID=$id";
      $status = $db->do($query);
      reports_db_err($db, $status, $query);
    }

    #make sure we aren't displayed right on top of it (probably)
    $ypct = 0.48;
  }

  #if we still don't have a defined position/size, set the defaults
  if (($width < 0.01) || ($height < 0.01))
  {
    $width = "0.48";
    $height = "0.48";
    $xpct = "0.5";
    $ypct = "0.5";
  }

  #get the graph type from the style string
  $graphType = reports_get_style($graphDesign, "type");

  #if no graph type is specified... you get a pie! You get a pie! Everyone...
  if (length($graphType) < 2)
  {
    $graphDesign = reports_set_style($graphDesign, "type", "2DPie");
    $graphType = "2DPie";
  }


        ########### PIE/DONUT STYLING ###########

  #if we're a pie-style chart
  if (($graphType eq "2DPie") || ($graphType eq "3DPie") ||
      ($graphType eq "2DDonut") || ($graphType eq "3DDonut"))
  {

    #if we're a new graph, set some default options for cosmetic purposes
    if (length($graph_x) < 1)
    {
      #don't show label text (user can get it by hovering over slice, and
      #it tends to make the graphs look too small for their space
      $graphDesign = reports_set_style($graphDesign, "showLabels", "0");

      #show the legend at the right, since we usually have more hori space
      $graphDesign = reports_set_style($graphDesign, "showLegend", "1");
      $graphDesign = reports_set_style($graphDesign, "legendPosition", "right");
    }

    #our preference is to display products as slices, but if there's a ton of
    #them and a reasonable number of geographies... (and so on for times)
    #if there's more than 25 of everything, then, well... crap ton of products
    #it is. If a plotted dimension is already defined, leave it alone.
    if (length($graph_x) < 1)
    {
      $graph_x = "p";
      if (scalar(@prodIDs) > 25)
      {
        if (scalar(@geoIDs) < 25)
        {
          $graph_x = "g";
        }
        elsif (scalar(@timeIDs) < 25)
        {
          $graph_x = "t";
        }
      }
    }

    #select everything in our plotted dimension unless there's already a
    #selection in place; then if we don't have exactly 1 selection for each
    #of the other dimensions pick a reasonable default (the first item)
    if ($graph_x eq "p")
    {
      if (scalar(@selProdIDs) < 2)
      {
        @selProdIDs = @prodIDs;
      }
      if (scalar(@selGeoIDs) != 1)
      {
        undef(@selGeoIDs);
        $selGeoIDs[0] = $geoIDs[0];
      }
      if (scalar(@selTimeIDs) != 1)
      {
        undef(@selTimeIDs);
        $selTimeIDs[0] = $timeIDs[0];
      }
    }
    elsif ($graph_x eq "g")
    {
      if (scalar(@selGeoIDs) < 2)
      {
        @selGeoIDs = @geoIDs;
      }
      if (scalar(@selProdIDs) != 1)
      {
        undef(@selProdIDs);
        $selProdIDs[0] = $prodIDs[0];
      }
      if (scalar(@selTimeIDs) != 1)
      {
        undef(@selTimeIDs);
        $selTimeIDs[0] = $timeIDs[0];
      }
    }
    elsif ($graph_x eq "t")
    {
      if (scalar(@selTimeIDs) < 2)
      {
        @selTimeIDs = @timeIDs;
      }
      if (scalar(@selGeoIDs) != 1)
      {
        undef(@selGeoIDs);
        $selGeoIDs[0] = $geoIDs[0];
      }
      if (scalar(@selProdIDs) != 1)
      {
        undef(@selProdIDs);
        $selProdIDs[0] = $prodIDs[0];
      }
    }

    #make sure we have one measure selected
    if (scalar(@selMeasIDs) != 1)
    {
      undef(@selMeasIDs);
      $selMeasIDs[0] = $measIDs[0];
    }
  }


       ########### BAR/COLUMN STYLING ###########

  #if we're a bar or column chart
  if (($graphType eq "2DColumns") || ($graphType eq "3DColumns") ||
      ($graphType eq "2DBars") || ($graphType eq "3DBars"))
  {

    #if we're a new graph, set some default cosmetics
    if (length($graph_x) < 1)
    {
      $graphDesign = reports_set_style($graphDesign, "showLabels", "1");
      $graphDesign = reports_set_style($graphDesign, "showLegend", "1");
      $graphDesign = reports_set_style($graphDesign, "legendPosition", "bottom");
      $graphDesign = reports_set_style($graphDesign, "canvasBgColor", "#ffffff");
    }


    #our preference is to display products as bars, but if there's a ton of
    #them and a reasonable number of geographies... (and so on for times)
    #if there's more than 25 of everything, then, well... crap ton of products
    #it is
    if (length($graph_x) < 1)
    {
      $graph_x = "p";
      if (scalar(@prodIDs) > 25)
      {
        if (scalar(@geoIDs) < 25)
        {
          $graph_x = "g";
        }
        elsif (scalar(@timeIDs) < 25)
        {
          $graph_x = "t";
        }
      }
    }

    #we want the length of the bars to be measures by default
    if (length($graph_y) < 1)
    {
      $graph_y = "m";
    }

    #select everything in our plotted dimension unless there's already a
    #selection in place; then if we don't have exactly 1 selection for each
    #of the other dimensions pick a reasonable default (the first item)
    if ($graph_x eq "p")
    {
      if (scalar(@selProdIDs) < 2)
      {
        @selProdIDs = @prodIDs;
      }
      if (scalar(@selGeoIDs) != 1)
      {
        undef(@selGeoIDs);
        $selGeoIDs[0] = $geoIDs[0];
      }
      if (scalar(@selTimeIDs) != 1)
      {
        undef(@selTimeIDs);
        $selTimeIDs[0] = $timeIDs[0];
      }
    }
    elsif ($graph_x eq "g")
    {
      if (scalar(@selGeoIDs) < 2)
      {
        @selGeoIDs = @geoIDs;
      }
      if (scalar(@selProdIDs) != 1)
      {
        undef(@selProdIDs);
        $selProdIDs[0] = $prodIDs[0];
      }
      if (scalar(@selTimeIDs) != 1)
      {
        undef(@selTimeIDs);
        $selTimeIDs[0] = $timeIDs[0];
      }
    }
    elsif ($graph_x eq "t")
    {
      if (scalar(@selTimeIDs) < 2)
      {
        @selTimeIDs = @timeIDs;
      }
      if (scalar(@selGeoIDs) != 1)
      {
        undef(@selGeoIDs);
        $selGeoIDs[0] = $geoIDs[0];
      }
      if (scalar(@selProdIDs) != 1)
      {
        undef(@selProdIDs);
        $selProdIDs[0] = $prodIDs[0];
      }
    }

    #make sure we have one measure selected
    if (scalar(@selMeasIDs) != 1)
    {
      undef(@selMeasIDs);
      $selMeasIDs[0] = $measIDs[0];
    }
  }


       ########### STACKED BAR/COLUMN STYLING ###########

  #if we're a stacked bar or stacked column chart
  if (($graphType eq "Stacked2DColumns") || ($graphType eq "Stacked3DColumns") ||
      ($graphType eq "Stacked2DBars") || ($graphType eq "Stacked3DBars"))
  {

    #if we're a new graph, set some default cosmetics
    if (length($graph_x) < 1)
    {
      $graphDesign = reports_set_style($graphDesign, "showLabels", "1");
      $graphDesign = reports_set_style($graphDesign, "showLegend", "1");
      $graphDesign = reports_set_style($graphDesign, "legendPosition", "bottom");
      $graphDesign = reports_set_style($graphDesign, "canvasBgColor", "#ffffff");
    }

    #our preference is to display products as bars, but if there's a ton of
    #them and a reasonable number of geographies... (and so on for times)
    #if there's more than 25 of everything, then, well... crap ton of products
    #it is
    if (length($graph_x) < 1)
    {
      $graph_x = "p";
      if (scalar(@prodIDs) > 25)
      {
        if (scalar(@geoIDs) < 25)
        {
          $graph_x = "g";
        }
        elsif (scalar(@timeIDs) < 25)
        {
          $graph_x = "t";
        }
      }
    }

    #we want the length of the bars to be measures by default
    if (length($graph_y) < 1)
    {
      $graph_y = "m";
    }

    #select everything in our plotted dimension unless there's already a
    #selection in place; then if we don't have exactly 1 selection for each
    #of the other dimensions pick a reasonable default (the first item)
    if ($graph_x eq "p")
    {
      if (scalar(@selProdIDs) < 2)
      {
        @selProdIDs = @prodIDs;
      }
      if (scalar(@selGeoIDs) != 1)
      {
        undef(@selGeoIDs);
        $selGeoIDs[0] = $geoIDs[0];
      }
      if (scalar(@selTimeIDs) != 1)
      {
        undef(@selTimeIDs);
        $selTimeIDs[0] = $timeIDs[0];
      }
    }
    elsif ($graph_x eq "g")
    {
      if (scalar(@selGeoIDs) < 2)
      {
        @selGeoIDs = @geoIDs;
      }
      if (scalar(@selProdIDs) != 1)
      {
        undef(@selProdIDs);
        $selProdIDs[0] = $prodIDs[0];
      }
      if (scalar(@selTimeIDs) != 1)
      {
        undef(@selTimeIDs);
        $selTimeIDs[0] = $timeIDs[0];
      }
    }
    elsif ($graph_x eq "t")
    {
      if (scalar(@selTimeIDs) < 2)
      {
        @selTimeIDs = @timeIDs;
      }
      if (scalar(@selGeoIDs) != 1)
      {
        undef(@selGeoIDs);
        $selGeoIDs[0] = $geoIDs[0];
      }
      if (scalar(@selProdIDs) != 1)
      {
        undef(@selProdIDs);
        $selProdIDs[0] = $prodIDs[0];
      }
    }

    #if we don't have at least 1 measure already selected, select them all
    if (scalar(@selMeasIDs) < 1)
    {
      @selMeasIDs = @measIDs;
    }
  }


       ########### LINE/AREA CHART STYLING ###########

  #if we're a line or area chart
  if (($graphType eq "Lines") || ($graphType eq "ZoomLines") ||
      ($graphType eq "Area"))
  {

    #our preference is to display measure values (y axis) over time (X axis)
    if (length($graph_x) < 1)
    {
      $graph_x = "t";
    }
    if (length($graph_y) < 1)
    {
      $graph_y = "m";
    }

    #select everything in our plotted dimension unless there's already a
    #selection in place; then if we don't have exactly 1 selection for each
    #of the other dimensions pick a reasonable default (the first item)
    if ($graph_x eq "p")
    {
      if (scalar(@selProdIDs) < 2)
      {
        @selProdIDs = @prodIDs;
      }
      if (scalar(@selGeoIDs) != 1)
      {
        undef(@selGeoIDs);
        $selGeoIDs[0] = $geoIDs[0];
      }
      if (scalar(@selTimeIDs) != 1)
      {
        undef(@selTimeIDs);
        $selTimeIDs[0] = $timeIDs[0];
      }
    }
    elsif ($graph_x eq "g")
    {
      if (scalar(@selGeoIDs) < 2)
      {
        @selGeoIDs = @geoIDs;
      }
      if (scalar(@selProdIDs) < 1)
      {
        undef(@selProdIDs);
        $selProdIDs[0] = $prodIDs[0];
      }
      if (scalar(@selTimeIDs) < 1)
      {
        undef(@selTimeIDs);
        $selTimeIDs[0] = $timeIDs[0];
      }
    }
    elsif ($graph_x eq "t")
    {
      if (scalar(@selTimeIDs) < 2)
      {
        @selTimeIDs = @timeIDs;
      }
      if (scalar(@selGeoIDs) < 1)
      {
        undef(@selGeoIDs);
        $selGeoIDs[0] = $geoIDs[0];
      }
      if (scalar(@selProdIDs) < 1)
      {
        undef(@selProdIDs);
        $selProdIDs[0] = $prodIDs[0];
      }
    }

    #select all measures unless one or more are already selected
    if (scalar(@selMeasIDs) < 1)
    {
      @selMeasIDs = @measIDs;
    }
  }


       ########### SCATTER CHART STYLING ###########

  #if we're a scatter chart
  if ($graphType eq "Scatter")
  {

    #see if we have an currently defined axis values
    $measureX = reports_get_style($graphDesign, "measureX");
    $measureY = reports_get_style($graphDesign, "measureY");

    #if we don't have one or more axis values, set some defaults
    if ($measureX < 1)
    {
      $graphDesign = reports_set_style($graphDesign, "measureX", $measIDs[0]);
    }
    if ($measureY < 1)
    {
      $graphDesign = reports_set_style($graphDesign, "measureY", $measIDs[1]);
    }

    #by default, we're plotting products against the axes values
    if (length($graph_x) < 1)
    {
      $graph_x = "p";
    }

    #select everything in our plotted dimension unless there's already a
    #selection in place; then if we don't have exactly 1 selection for each
    #of the other dimensions pick a reasonable default (the first item)
    if ($graph_x eq "p")
    {
      if (scalar(@selProdIDs) < 2)
      {
        @selProdIDs = @prodIDs;
      }
      if (scalar(@selGeoIDs) != 1)
      {
        undef(@selGeoIDs);
        $selGeoIDs[0] = $geoIDs[0];
      }
      if (scalar(@selTimeIDs) != 1)
      {
        undef(@selTimeIDs);
        $selTimeIDs[0] = $timeIDs[0];
      }
    }
    elsif ($graph_x eq "g")
    {
      if (scalar(@selGeoIDs) < 2)
      {
        @selGeoIDs = @geoIDs;
      }
      if (scalar(@selProdIDs) != 1)
      {
        undef(@selProdIDs);
        $selProdIDs[0] = $prodIDs[0];
      }
      if (scalar(@selTimeIDs) != 1)
      {
        undef(@selTimeIDs);
        $selTimeIDs[0] = $timeIDs[0];
      }
    }
    elsif ($graph_x eq "t")
    {
      if (scalar(@selTimeIDs) < 2)
      {
        @selTimeIDs = @timeIDs;
      }
      if (scalar(@selGeoIDs) != 1)
      {
        undef(@selGeoIDs);
        $selGeoIDs[0] = $geoIDs[0];
      }
      if (scalar(@selProdIDs) != 1)
      {
        undef(@selProdIDs);
        $selProdIDs[0] = $prodIDs[0];
      }
    }

    #make sure we have one measure selected
    if (scalar(@selMeasIDs) != 1)
    {
      undef(@selMeasIDs);
      $selMeasIDs[0] = $measIDs[0];
    }
  }


       ########### RADAR CHART STYLING ###########

  #if we're a radar chart
  if ($graphType eq "Radar")
  {

    #if we're a new graph, set some default cosmetics
    if (length($graph_x) < 1)
    {
      #don't show label text (user can get it by hovering over slice, and
      #it tends to make the graphs look too small for their space
      $graphDesign = reports_set_style($graphDesign, "showLabels", "0");

      #show the legend at the right, since we usually have more hori space
      $graphDesign = reports_set_style($graphDesign, "showLegend", "1");
      $graphDesign = reports_set_style($graphDesign, "legendPosition", "right");
    }

    #our preference is to display products as tensors, but if there's a ton of
    #them and a reasonable number of geographies... (and so on for times)
    #if there's more than 25 of everything, then, well... crap ton of products
    #it is
    if (length($graph_x) < 1)
    {
      $graph_x = "p";
      if (scalar(@prodIDs) > 25)
      {
        if (scalar(@geoIDs) < 25)
        {
          $graph_x = "g";
        }
        elsif (scalar(@timeIDs) < 25)
        {
          $graph_x = "t";
        }
      }
    }

    #select everything in our plotted dimension unless there's already a
    #selection in place; then if we don't have exactly 1 selection for each
    #of the other dimensions pick a reasonable default (the first item)
    if ($graph_x eq "p")
    {
      if (scalar(@selProdIDs) < 2)
      {
        @selProdIDs = @prodIDs;
      }
      if (scalar(@selGeoIDs) != 1)
      {
        undef(@selGeoIDs);
        $selGeoIDs[0] = $geoIDs[0];
      }
      if (scalar(@selTimeIDs) != 1)
      {
        undef(@selTimeIDs);
        $selTimeIDs[0] = $timeIDs[0];
      }
    }
    elsif ($graph_x eq "g")
    {
      if (scalar(@selGeoIDs) < 2)
      {
        @selGeoIDs = @geoIDs;
      }
      if (scalar(@selProdIDs) != 1)
      {
        undef(@selProdIDs);
        $selProdIDs[0] = $prodIDs[0];
      }
      if (scalar(@selTimeIDs) != 1)
      {
        undef(@selTimeIDs);
        $selTimeIDs[0] = $timeIDs[0];
      }
    }
    elsif ($graph_x eq "t")
    {
      if (scalar(@selTimeIDs) < 2)
      {
        @selTimeIDs = @timeIDs;
      }
      if (scalar(@selGeoIDs) != 1)
      {
        undef(@selGeoIDs);
        $selGeoIDs[0] = $geoIDs[0];
      }
      if (scalar(@selProdIDs) != 1)
      {
        undef(@selProdIDs);
        $selProdIDs[0] = $prodIDs[0];
      }
    }

    #make sure we have one measure selected
    if (scalar(@selMeasIDs) != 1)
    {
      undef(@selMeasIDs);
      $selMeasIDs[0] = $measIDs[0];
    }
  }


       ########### TREEMAP CHART STYLING ###########

  #if we're a line or area chart
  if ($graphType eq "TreeMap")
  {

    #usually want to display products as nodes in the map
    if (length($graph_x) < 1)
    {
      $graph_x = "p";
    }

    #select everything in our plotted dimension unless there's already a
    #selection in place; then if we don't have exactly 1 selection for each
    #of the other dimensions pick a reasonable default (the first item)
    if ($graph_x eq "p")
    {
      if (scalar(@selProdIDs) < 2)
      {
        @selProdIDs = @prodIDs;
      }
      if (scalar(@selGeoIDs) != 1)
      {
        undef(@selGeoIDs);
        $selGeoIDs[0] = $geoIDs[0];
      }
      if (scalar(@selTimeIDs) != 1)
      {
        undef(@selTimeIDs);
        $selTimeIDs[0] = $timeIDs[0];
      }
    }
    elsif ($graph_x eq "g")
    {
      if (scalar(@selGeoIDs) < 2)
      {
        @selGeoIDs = @geoIDs;
      }
      if (scalar(@selProdIDs) != 1)
      {
        undef(@selProdIDs);
        $selProdIDs[0] = $prodIDs[0];
      }
      if (scalar(@selTimeIDs) != 1)
      {
        undef(@selTimeIDs);
        $selTimeIDs[0] = $timeIDs[0];
      }
    }
    elsif ($graph_x eq "t")
    {
      if (scalar(@selTimeIDs) < 2)
      {
        @selTimeIDs = @timeIDs;
      }
      if (scalar(@selGeoIDs) != 1)
      {
        undef(@selGeoIDs);
        $selGeoIDs[0] = $geoIDs[0];
      }
      if (scalar(@selProdIDs) != 1)
      {
        undef(@selProdIDs);
        $selProdIDs[0] = $prodIDs[0];
      }
    }

    #make sure we have one measure selected
    if (scalar(@selMeasIDs) != 1)
    {
      undef(@selMeasIDs);
      $selMeasIDs[0] = $measIDs[0];
    }

    #set default colors if nothing already there ("classroom" PBI color scheme)
    $minColor = reports_get_style($graphDesign, "minColor");
    $maxColor = reports_get_style($graphDesign, "maxColor");
    if (length($minColor) < 6)
    {
      $graphDesign = reports_set_style($graphDesign, "minColor", "fd625e");
    }
    if (length($maxColor) < 6)
    {
      $graphDesign = reports_set_style($graphDesign, "maxColor", "01b8aa");
    }
  }


       ########### WATERFALL/PARETO CHART STYLING ###########

  if (($graphType eq "Waterfall") || ($graphType eq "Pareto"))
  {

    #our preference is to display products as segments, but if there's a ton
    #of them and a reasonable number of geographies (or measures)...
    if (length($graph_x) < 1)
    {
      $graph_x = "p";
      if (scalar(@prodIDs) > 25)
      {
        if (scalar(@geoIDs) < 25)
        {
          $graph_x = "g";
        }
        elsif (scalar(@timeIDs) < 25)
        {
          $graph_x = "t";
        }
        else
        {
          $graph_x = "m";
        }
      }
    }

    #select everything in our plotted dimension unless there's already a
    #selection in place; then if we don't have exactly 1 selection for each
    #of the other dimensions pick a reasonable default (the first item)
    if ($graph_x eq "p")
    {
      if (scalar(@selProdIDs) < 2)
      {
        @selProdIDs = @prodIDs;
      }
      if (scalar(@selGeoIDs) != 1)
      {
        undef(@selGeoIDs);
        $selGeoIDs[0] = $geoIDs[0];
      }
      if (scalar(@selTimeIDs) != 1)
      {
        undef(@selTimeIDs);
        $selTimeIDs[0] = $timeIDs[0];
      }
      if (scalar(@selMeasIDs) != 1)
      {
        undef(@selMeasIDs);
        $selMeasIDs[0] = $measIDs[0];
      }
    }
    elsif ($graph_x eq "g")
    {
      if (scalar(@selGeoIDs) < 2)
      {
        @selGeoIDs = @geoIDs;
      }
      if (scalar(@selProdIDs) != 1)
      {
        undef(@selProdIDs);
        $selProdIDs[0] = $prodIDs[0];
      }
      if (scalar(@selTimeIDs) != 1)
      {
        undef(@selTimeIDs);
        $selTimeIDs[0] = $timeIDs[0];
      }
      if (scalar(@selMeasIDs) != 1)
      {
        undef(@selMeasIDs);
        $selMeasIDs[0] = $measIDs[0];
      }
    }
    elsif ($graph_x eq "t")
    {
      if (scalar(@selTimeIDs) < 2)
      {
        @selTimeIDs = @timeIDs;
      }
      if (scalar(@selGeoIDs) != 1)
      {
        undef(@selGeoIDs);
        $selGeoIDs[0] = $geoIDs[0];
      }
      if (scalar(@selProdIDs) != 1)
      {
        undef(@selProdIDs);
        $selProdIDs[0] = $prodIDs[0];
      }
      if (scalar(@selMeasIDs) != 1)
      {
        undef(@selMeasIDs);
        $selMeasIDs[0] = $measIDs[0];
      }
    }
    elsif ($graph_x eq "m")
    {
      if (scalar(@selMeasIDs) < 2)
      {
        @selMeasIDs = @measIDs;
      }
      if (scalar(@selGeoIDs) != 1)
      {
        undef(@selGeoIDs);
        $selGeoIDs[0] = $geoIDs[0];
      }
      if (scalar(@selProdIDs) != 1)
      {
        undef(@selProdIDs);
        $selProdIDs[0] = $prodIDs[0];
      }
      if (scalar(@selTimeIDs) != 1)
      {
        undef(@selTimeIDs);
        $selTimeIDs[0] = $timeIDs[0];
      }
    }
  }


       ########### BUBBLE CHART STYLING ###########

  #if we're a bubble chart
  if ($graphType eq "Bubble")
  {

    #see if we have an currently defined axis values
    $measureX = reports_get_style($graphDesign, "measureX");
    $measureY = reports_get_style($graphDesign, "measureY");
    $measureZ = reports_get_style($graphDesign, "measureZ");

    #if we don't have one or more axis values, set some defaults
    if ($measureX < 1)
    {
      $graphDesign = reports_set_style($graphDesign, "measureX", $measIDs[0]);
    }
    if ($measureY < 1)
    {
      $graphDesign = reports_set_style($graphDesign, "measureY", $measIDs[1]);
    }
    if ($measureZ < 1)
    {
      $graphDesign = reports_set_style($graphDesign, "measureZ", $measIDs[2]);
    }

    #by default, we're plotting products against the axes values
    if (length($graph_x) < 1)
    {
      $graph_x = "p";
    }

    #select everything in our plotted dimension unless there's already a
    #selection in place; then if we don't have exactly 1 selection for each
    #of the other dimensions pick a reasonable default (the first item)
    if ($graph_x eq "p")
    {
      if (scalar(@selProdIDs) < 2)
      {
        @selProdIDs = @prodIDs;
      }
      if (scalar(@selGeoIDs) != 1)
      {
        undef(@selGeoIDs);
        $selGeoIDs[0] = $geoIDs[0];
      }
      if (scalar(@selTimeIDs) != 1)
      {
        undef(@selTimeIDs);
        $selTimeIDs[0] = $timeIDs[0];
      }
    }
    elsif ($graph_x eq "g")
    {
      if (scalar(@selGeoIDs) < 2)
      {
        @selGeoIDs = @geoIDs;
      }
      if (scalar(@selProdIDs) != 1)
      {
        undef(@selProdIDs);
        $selProdIDs[0] = $prodIDs[0];
      }
      if (scalar(@selTimeIDs) != 1)
      {
        undef(@selTimeIDs);
        $selTimeIDs[0] = $timeIDs[0];
      }
    }
    elsif ($graph_x eq "t")
    {
      if (scalar(@selTimeIDs) < 2)
      {
        @selTimeIDs = @timeIDs;
      }
      if (scalar(@selGeoIDs) != 1)
      {
        undef(@selGeoIDs);
        $selGeoIDs[0] = $geoIDs[0];
      }
      if (scalar(@selProdIDs) != 1)
      {
        undef(@selProdIDs);
        $selProdIDs[0] = $prodIDs[0];
      }
    }
  }


       ########### COLUMN/LINE CHART STYLING ###########

  #if we're a combined column/line chart
  if ($graphType eq "ColumnLine")
  {

    #our preference is to display measure values (y axis) over time (X axis)
    if (length($graph_x) < 1)
    {
      $graph_x = "t";
    }
    if (length($graph_y) < 1)
    {
      $graph_y = "m";
    }

    #select everything in our plotted dimension unless there's already a
    #selection in place; then if we don't have exactly 1 selection for each
    #of the other dimensions pick a reasonable default (the first item)
    if ($graph_x eq "p")
    {
      if (scalar(@selProdIDs) < 2)
      {
        @selProdIDs = @prodIDs;
      }
      if (scalar(@selGeoIDs) != 1)
      {
        undef(@selGeoIDs);
        $selGeoIDs[0] = $geoIDs[0];
      }
      if (scalar(@selTimeIDs) != 1)
      {
        undef(@selTimeIDs);
        $selTimeIDs[0] = $timeIDs[0];
      }
    }
    elsif ($graph_x eq "g")
    {
      if (scalar(@selGeoIDs) < 2)
      {
        @selGeoIDs = @geoIDs;
      }
      if (scalar(@selProdIDs) != 1)
      {
        undef(@selProdIDs);
        $selProdIDs[0] = $prodIDs[0];
      }
      if (scalar(@selTimeIDs) != 1)
      {
        undef(@selTimeIDs);
        $selTimeIDs[0] = $timeIDs[0];
      }
    }
    elsif ($graph_x eq "t")
    {
      if (scalar(@selTimeIDs) < 2)
      {
        @selTimeIDs = @timeIDs;
      }
      if (scalar(@selGeoIDs) != 1)
      {
        undef(@selGeoIDs);
        $selGeoIDs[0] = $geoIDs[0];
      }
      if (scalar(@selProdIDs) != 1)
      {
        undef(@selProdIDs);
        $selProdIDs[0] = $prodIDs[0];
      }
    }

    #make sure we have at least 2 measures selected
    if ($selMeasIDs[0] < 1)
    {
      $selMeasIDs[0] = $measIDs[0];
    }
    if ($selMeasIDs[1] < 1)
    {
      $selMeasIDs[1] = $measIDs[1];
    }
  }


        ########### FUNNEL STYLING ###########

  #if we're a funnel chart
  if ($graphType eq "Funnel")
  {

    #if we're a new graph, set some default options for cosmetic purposes
    if (length($graph_x) < 1)
    {
      #don't show label text (user can get it by hovering over slice, and
      #it tends to make the graphs look too small for their space
      $graphDesign = reports_set_style($graphDesign, "showLabels", "0");

      #don't show a legend by default
      $graphDesign = reports_set_style($graphDesign, "showLegend", "0");
      $graphDesign = reports_set_style($graphDesign, "legendPosition", "bottom");
    }

    #our preference is to display products as slices, but if there's a ton of
    #them and a reasonable number of geographies... (and so on for times)
    #if there's more than 25 of everything, then, well... crap ton of products
    #it is. If a plotted dimension is already defined, leave it alone.
    if (length($graph_x) < 1)
    {
      $graph_x = "p";
      if (scalar(@prodIDs) > 25)
      {
        if (scalar(@geoIDs) < 25)
        {
          $graph_x = "g";
        }
        elsif (scalar(@timeIDs) < 25)
        {
          $graph_x = "t";
        }
      }
    }

    #select everything in our plotted dimension unless there's already a
    #selection in place; then if we don't have exactly 1 selection for each
    #of the other dimensions pick a reasonable default (the first item)
    if ($graph_x eq "p")
    {
      if (scalar(@selProdIDs) < 2)
      {
        @selProdIDs = @prodIDs;
      }
      if (scalar(@selGeoIDs) != 1)
      {
        undef(@selGeoIDs);
        $selGeoIDs[0] = $geoIDs[0];
      }
      if (scalar(@selTimeIDs) != 1)
      {
        undef(@selTimeIDs);
        $selTimeIDs[0] = $timeIDs[0];
      }
    }
    elsif ($graph_x eq "g")
    {
      if (scalar(@selGeoIDs) < 2)
      {
        @selGeoIDs = @geoIDs;
      }
      if (scalar(@selProdIDs) != 1)
      {
        undef(@selProdIDs);
        $selProdIDs[0] = $prodIDs[0];
      }
      if (scalar(@selTimeIDs) != 1)
      {
        undef(@selTimeIDs);
        $selTimeIDs[0] = $timeIDs[0];
      }
    }
    elsif ($graph_x eq "t")
    {
      if (scalar(@selTimeIDs) < 2)
      {
        @selTimeIDs = @timeIDs;
      }
      if (scalar(@selGeoIDs) != 1)
      {
        undef(@selGeoIDs);
        $selGeoIDs[0] = $geoIDs[0];
      }
      if (scalar(@selProdIDs) != 1)
      {
        undef(@selProdIDs);
        $selProdIDs[0] = $prodIDs[0];
      }
    }

    #make sure we have one measure selected
    if (scalar(@selMeasIDs) != 1)
    {
      undef(@selMeasIDs);
      $selMeasIDs[0] = $measIDs[0];
    }
  }


       ########### DUAL Y CHART STYLING ###########

  #if we're a dual-Y chart
  if ($graphType eq "DualY")
  {

    #our preference is to display measure values (y axis) over time (X axis)
    if (length($graph_x) < 1)
    {
      $graph_x = "t";
    }
    if (length($graph_y) < 1)
    {
      $graph_y = "m";
    }

    #select everything in our plotted dimension unless there's already a
    #selection in place; then if we don't have exactly 1 selection for each
    #of the other dimensions pick a reasonable default (the first item)
    if ($graph_x eq "p")
    {
      if (scalar(@selProdIDs) < 2)
      {
        @selProdIDs = @prodIDs;
      }
      if (scalar(@selGeoIDs) != 1)
      {
        undef(@selGeoIDs);
        $selGeoIDs[0] = $geoIDs[0];
      }
      if (scalar(@selTimeIDs) != 1)
      {
        undef(@selTimeIDs);
        $selTimeIDs[0] = $timeIDs[0];
      }
    }
    elsif ($graph_x eq "g")
    {
      if (scalar(@selGeoIDs) < 2)
      {
        @selGeoIDs = @geoIDs;
      }
      if (scalar(@selProdIDs) != 1)
      {
        undef(@selProdIDs);
        $selProdIDs[0] = $prodIDs[0];
      }
      if (scalar(@selTimeIDs) != 1)
      {
        undef(@selTimeIDs);
        $selTimeIDs[0] = $timeIDs[0];
      }
    }
    elsif ($graph_x eq "t")
    {
      if (scalar(@selTimeIDs) < 2)
      {
        @selTimeIDs = @timeIDs;
      }
      if (scalar(@selGeoIDs) != 1)
      {
        undef(@selGeoIDs);
        $selGeoIDs[0] = $geoIDs[0];
      }
      if (scalar(@selProdIDs) != 1)
      {
        undef(@selProdIDs);
        $selProdIDs[0] = $prodIDs[0];
      }
    }

    #make sure we have at least 2 measures selected
    if ($selMeasIDs[0] < 1)
    {
      $selMeasIDs[0] = $measIDs[0];
    }
    if ($selMeasIDs[1] < 1)
    {
      $selMeasIDs[1] = $measIDs[1];
    }
  }

  #tell the visualization interface to restore default pos and size
  $graphDesign = reports_set_style($graphDesign, "width", "$width");
  $graphDesign = reports_set_style($graphDesign, "height", "$height");
  $graphDesign = reports_set_style($graphDesign, "xpct", "$xpct");
  $graphDesign = reports_set_style($graphDesign, "ypct", "$ypct");

  #save changes to graph design/layout
  $q_graphDesign = $db->quote($graphDesign);
  $selProdStr = join(',', @selProdIDs);
  $selGeoStr = join(',', @selGeoIDs);
  $selTimeStr = join(',', @selTimeIDs);
  $selMeasStr = join(',', @selMeasIDs);

  $query = "UPDATE visuals \
      SET design=$q_graphDesign, graph_x='$graph_x', graph_y='$graph_y', graph_z='$graph_z', selProducts='$selProdStr', selGeographies='$selGeoStr', selTimeperiods='$selTimeStr', selMeasures='$selMeasStr' \
      WHERE ID=$visID";
  $status = $db->do($query);
  reports_db_err($db, $status, $query);

  #wipe out any now-stale custom selections made by viewers
  $query = "DELETE FROM visuals_viewers WHERE ID=$visID";
  $status = $db->do($query);
  reports_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------
#
# Make sensible default data/plotted dimension selections for the supplied
# map.
#

sub reports_map_default_selections
{
  my ($query, $dbOutput, $mapDesign, $selProdStr, $selGeoStr, $selTimeStr);
  my ($selMeasStr, $prodStr, $geoStr, $timeStr, $measStr, $i, $mapType);
  my ($q_mapDesign, $status);
  my (@prodIDs, @geoIDs, @timeIDs, @measIDs, @selProdIDs, @selGeoIDs);
  my (@selTimeIDs, @selMeasIDs);
  my (%union, %isect);

  my ($db, $rptID, $visID) = @_;


  #get the current map layout and data selections
  $query = "SELECT design, selProducts, selGeographies, selTimeperiods, selMeasures \
      FROM visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  reports_db_err($db, $status, $query);
  ($mapDesign, $selProdStr, $selGeoStr, $selTimeStr, $selMeasStr) = $dbOutput->fetchrow_array;

  #get info about the items included in the report
  $query = "SELECT products, geographies, timeperiods, measures \
      FROM cubes WHERE ID=$rptID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  reports_db_err($db, $status, $query);
  ($prodStr, $geoStr, $timeStr, $measStr) = $dbOutput->fetchrow_array;

  #convert cube items into arrays
  @prodIDs = split(',', $prodStr);
  @geoIDs = split(',', $geoStr);
  @timeIDs = split(',', $timeStr);
  @measIDs = split(',', $measStr);

  #convert data selections into arrays
  @selProdIDs = split(',', $selProdStr);
  @selGeoIDs = split(',', $selGeoStr);
  @selTimeIDs = split(',', $selTimeStr);
  @selMeasIDs = split(',', $selMeasStr);

  #calculate the intersection of the selection and item arrays (effectively
  #removes any items from the selection array that are no longer in the cube)
  undef(%union);  undef(%isect);
  foreach $i (@prodIDs, @selProdIDs) { $union{$i}++ && $isect{$i}++ };
  @selProdIDs = keys %isect;
  undef(%union);        undef(%isect);
  foreach $i (@geoIDs, @selGeoIDs) { $union{$i}++ && $isect{$i}++ };
  @selGeoIDs = keys %isect;
  undef(%union);        undef(%isect);
  foreach $i (@timeIDs, @selTimeIDs) { $union{$i}++ && $isect{$i}++ };
  @selTimeIDs = keys %isect;
  undef(%union);        undef(%isect);
  foreach $i (@measIDs, @selMeasIDs) { $union{$i}++ && $isect{$i}++ };
  @selMeasIDs = keys %isect;

  #if there isn't a map style defined, let's make it a default US 50 map
  $mapType = reports_get_style($mapDesign, "type");
  if (!defined($mapType))
  {
    $mapDesign = reports_set_style($mapDesign, "type", "usa");
  }

  #if there's more than one product selected or no products, set selection
  #back to the first product
  if (scalar(@selProdIDs) != 1)
  {
    undef(@selProdIDs);
    $selProdIDs[0] = $prodIDs[0];
  }

  #if there aren't any geographies selected, select them all
  if (scalar(@selGeoIDs) < 1)
  {
    push(@selGeoIDs, @geoIDs);
  }

  #if there's more than one time selected or no times, set selection
  #back to the first time period
  if (scalar(@selTimeIDs) != 1)
  {
    undef(@selTimeIDs);
    $selTimeIDs[0] = $timeIDs[0];
  }

  #if there's more than one measure selected or no measures, set selection
  #back to the first measure
  if (scalar(@selMeasIDs) != 1)
  {
    undef(@selMeasIDs);
    $selMeasIDs[0] = $measIDs[0];
  }

  $q_mapDesign = $db->quote($mapDesign);
  $selProdStr = join(',', @selProdIDs);
  $selGeoStr = join(',', @selGeoIDs);
  $selTimeStr = join(',', @selTimeIDs);
  $selMeasStr = join(',', @selMeasIDs);

  $query = "UPDATE visuals SET design=$q_mapDesign, selProducts='$selProdStr', selGeographies='$selGeoStr', selTimeperiods='$selTimeStr', selMeasures='$selMeasStr' WHERE ID=$visID";
  $status = $db->do($query);
  reports_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------
#
# Return the text name of the report with the supplied ID
#

sub report_id_to_name
{
  my ($query, $dbOutput, $name, $status);

  my ($db, $rptID) = @_;


  $query = "SELECT name FROM cubes WHERE ID=$rptID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  reports_db_err($db, $status, $query);
  ($name) = $dbOutput->fetchrow_array;

  return($name);
}



#-------------------------------------------------------------------------
#
# Expand any special tags in a report's caption/title to match current
# selections.
#

sub reports_expand_dim_tags
{
  my ($tagName);

  my ($db, $dsSchema, $caption, $productIDstring, $geographyIDstring, $timeIDstring, $measIDstring) = @_;


  #find and expand any dimension tags in the supplied string
  if ($caption =~ m/(.*)\{PROD\}(.*)/)
  {
    $tagName = KAPutil_get_item_ID_name($db, $dsSchema, "p", $productIDstring);
    $caption = $1 . $tagName . $2;
  }
  if ($caption =~ m/(.*)\{GEOG\}(.*)/)
  {
    $tagName = KAPutil_get_item_ID_name($db, $dsSchema, "g", $geographyIDstring);
    $caption = $1 . $tagName . $2;
  }
  if ($caption =~ m/(.*)\{TIME\}(.*)/)
  {
    $tagName = KAPutil_get_item_ID_name($db, $dsSchema, "t", $timeIDstring);
    $caption = $1 . $tagName . $2;
  }
  if ($caption =~ m/(.*)\{MEAS\}(.*)/)
  {
    $tagName = KAPutil_get_item_ID_name($db, $dsSchema, "m", $measIDstring);
    $caption = $1 . $tagName . $2;
  }

  return($caption);
}



#-------------------------------------------------------------------------
#
# Generate a snipped of an SQL WHERE clause that implements the supplied
# filter for a table. Used for both web display and Excel export of tables.
#

sub reports_table_filter_SQL
{
  my ($query, $dbOutput, $cutoff, $offset);

  my ($db, $dsSchema, $rptCube, $whereClause, $filterMeas, $filterOp, $filterNum,
      $topBottomProdIDs, $topBottomGeoIDs, $topBottomTimeIDs) = @_;


  #make sure filter definition is valid
  if (($filterMeas < 1) || (length($filterNum) < 1))
  {
    return($whereClause);
  }

  $filterMeas = "measure_" . $filterMeas;

  if ($filterOp eq "gt")
  {
    $whereClause .= " AND $filterMeas > $filterNum";
    return($whereClause);
  }
  elsif ($filterOp eq "lt")
  {
    $whereClause .= " AND $filterMeas < $filterNum";
    return($whereClause);
  }
  elsif ($filterOp eq "eq")
  {
    $whereClause .= " AND $filterMeas = $filterNum";
    return($whereClause);
  }

  elsif ($filterOp eq "top")
  {

    #based on the number of items (offset), get the cut-off value for WHERE
    $query = "SELECT $filterMeas FROM $dsSchema.$rptCube \
        WHERE product IN ($topBottomProdIDs) AND geography IN ($topBottomGeoIDs) AND time IN ($topBottomTimeIDs) \
        ORDER BY $filterMeas DESC LIMIT 1 OFFSET $filterNum";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($cutoff) = $dbOutput->fetchrow_array;

    #add the compound where clause
    if (defined($cutoff))
    {
      $whereClause .= " AND $filterMeas >= $cutoff";
    }
    return($whereClause);
  }

  elsif ($filterOp eq "bottom")
  {

    #based on the number of items (offset), get the cut-off value for WHERE
    $query = "SELECT $filterMeas FROM $dsSchema.$rptCube \
        WHERE product IN ($topBottomProdIDs) AND geography IN ($topBottomGeoIDs) AND time IN ($topBottomTimeIDs) \
        ORDER BY $filterMeas ASC LIMIT 1 OFFSET $filterNum";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($cutoff) = $dbOutput->fetchrow_array;

    #add the compound where clause
    $whereClause .= " AND $filterMeas <= $cutoff";
    return($whereClause);
  }

  elsif ($filterOp eq "toppct")
  {

    #calculate the numerical percent of items we're going to display, gate it
    if ($filterNum > 100)
    {
      $filterNum = 100;
    }
    elsif ($filterNum < 0)
    {
      $filterNum = 0;
    }
    $filterNum = $filterNum / 100;

    #get the total count of items we're going to display
    $query = "SELECT $filterNum * COUNT(*) FROM $dsSchema.$rptCube \
        WHERE product IN ($topBottomProdIDs) AND geography IN ($topBottomGeoIDs) AND time IN ($topBottomTimeIDs)";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($offset) = $dbOutput->fetchrow_array;
    $offset = sprintf("%.0f", $offset);

    #based on the number of items (offset), get the cut-off value for WHERE
    $query = "SELECT $filterMeas FROM $dsSchema.$rptCube \
        WHERE product IN ($topBottomProdIDs) AND geography IN ($topBottomGeoIDs) AND time IN ($topBottomTimeIDs) \
        ORDER BY $filterMeas DESC LIMIT 1 OFFSET $offset";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($cutoff) = $dbOutput->fetchrow_array;

    #add the compound where clause
    $whereClause .= " AND $filterMeas >= $cutoff";
    return($whereClause);
  }

  elsif ($filterOp eq "bottompct")
  {

    #calculate the numerical percent of items we're going to display, gate it
    if ($filterNum > 100)
    {
      $filterNum = 100;
    }
    elsif ($filterNum < 0)
    {
      $filterNum = 0;
    }
    $filterNum = $filterNum / 100;

    #get the total count of items we're going to display
    $query = "SELECT $filterNum * COUNT(*) FROM $dsSchema.$rptCube \
        WHERE product IN ($topBottomProdIDs) AND geography IN ($topBottomGeoIDs) AND time IN ($topBottomTimeIDs)";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($offset) = $dbOutput->fetchrow_array;
    $offset = sprintf("%.0f", $offset);

    #based on the number of items (offset), get the cut-off value for WHERE
    $query = "SELECT $filterMeas FROM $dsSchema.$rptCube \
        WHERE product IN ($topBottomProdIDs) AND geography IN ($topBottomGeoIDs) AND time IN ($topBottomTimeIDs) \
        ORDER BY $filterMeas ASC LIMIT 1 OFFSET $offset";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($cutoff) = $dbOutput->fetchrow_array;

    #add the compound where clause
    $whereClause = $whereClause . " AND $filterMeas <= $cutoff";
    return($whereClause);
  }
}



#-------------------------------------------------------------------------
#
# Generate a snipped of an SQL WHERE clause that implements the "exclude NAs
# and zeroes" logic for a report table.
# Used for both web display and Excel export of tables.
#

sub reports_table_excludeNA_SQL
{
  my ($query, $dbOutput, $status, $first, $excludeZeroClause, $excludeNAClause);
  my ($id, $dimName, $excludeNAItems);

  my ($db, $dsSchema, $rptCube, $whereClause, $primaryDim,
      $prodDisplayIDs, $geoDisplayIDs, $timeDisplayIDs, @dispMeasures) = @_;


  $first = 1;
  $excludeZeroClause = " AND (";
  $excludeNAClause = " AND (";
  foreach $id (@dispMeasures)
  {
    if ($id =~ m/^\d+$/)
    {
      if ($first == 1)
      {
        $first = 0;
        $excludeZeroClause .= "measure_$id != 0";
        $excludeNAClause .= "measure_$id IS NOT NULL";
      }
      else
      {
        $excludeZeroClause .= " OR measure_$id != 0";
        $excludeNAClause .= " OR measure_$id IS NOT NULL";
      }
    }
  }

  $excludeZeroClause .= ")";
  $excludeNAClause .= ")";

  #NB: we just need the first of the dimensions being displayed in a row
  #   to make this work, since the logic will catch any others
  if ($primaryDim eq "p")
  {
   $dimName = "product";
  }
  if ($primaryDim eq "g")
  {
   $dimName = "geography";
  }
  if ($primaryDim eq "t")
  {
   $dimName = "time";
  }

  $query = "SELECT DISTINCT $dimName FROM $dsSchema.$rptCube \
      WHERE product IN ($prodDisplayIDs) \
          AND geography IN ($geoDisplayIDs) \
          AND time IN ($timeDisplayIDs) \
          $excludeZeroClause $excludeNAClause";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  while (($id) = $dbOutput->fetchrow_array)
  {
    $excludeNAItems .= "'$id',"
  }
  chop($excludeNAItems);

  $whereClause .= " AND $dimName IN ($excludeNAItems) ";
  return($whereClause);
}



#-------------------------------------------------------------------------


1;
