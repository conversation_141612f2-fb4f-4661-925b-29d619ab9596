#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Roll Back Update</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Rollback Update</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('dsID');
  $updateID = $q->param('updateID');

  #connect to the database
  $db = KAPutil_connect_to_database();

  #get the name of the data source
  $dsName = ds_id_to_name($db, $dsID);
  $activity = "$first $last rolling back an update to $dsName";

  print_html_header();

  $dsSchema = "datasource_" . $dsID;

  #make sure we have write privs for this data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to modify this data source.");
  }

  $ok = DSRutil_operation_ok($db, $dsID, 0, "ROLLBACK");
  if ($ok != 1)
  {
    exit_warning("This data source is currently in use by another job - please try again later.");
  }

  #split the rollback process off into the background
  $SIG{CHLD} = "IGNORE";
  if ($pid = fork())
  {
    #parent process - we're just going to finish up our display script
  }
  else
  {
    #child process - do the actual rollback

    #we're in a new process, so we need a new connection to the database
    $db = KAPutil_connect_to_database();

    #let Apache know not to wait on the child process
    close(STDIN);
    close(STDOUT);

    #redirect STDERR to the Koala error log
    close(STDERR);
    open(STDERR, ">>/opt/apache/htdocs/tmp/koala_error.log");

    #set our initial state in the jobs table
    KAPutil_job_store_status($db, $userID, $dsID, 0, "ROLLBACK", "Rolling back data source update");
    $q_name = $db->quote($dsName);
    $query = "UPDATE app.jobs SET dsName=$q_name WHERE PID=$$ AND dsID=$dsID";
    $db->do($query);

    #grab the filename for the user's selected restore point
    $query = "SELECT filename FROM $dsSchema.update_history WHERE ID=$updateID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($filename) = $dbOutput->fetchrow_array;
    if ($filename =~ m/^(\/.*\/)(.*)\.zip/)
    {
      $directory = $1;
      $fileStub = $2;
    }
    else
    {
      $filename =~ m/(.*)\.zip/;
      $fileStub = $1;
      $directory = "/opt/apache/app/logs/";
    }

    #uncompress the saved data source dump
    chdir("$directory");
    `/usr/bin/unzip -n $directory/$fileStub.zip`;

    #reload the saved data source SQL dump from disk
    `/usr/bin/mysql -h$Lib::KoalaConfig::dbServerName -D datasource_$dsID -u app -p$Lib::KoalaConfig::password < $directory/$fileStub.sql`;

    #delete the old SQL data dump
    unlink("$directory/$fileStub.sql");
    unlink("$directory/$fileStub.zip");

    #wipe out all of the restore points that happened before the selected point
    $query = "SELECT ID, filename FROM $dsSchema.update_history \
        WHERE ID > $updateID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;

    while (($id, $filename) = $dbOutput->fetchrow_array)
    {
      if ($filename =~ m/^(\/.*\/)(.*)\.zip/)
      {
        $directory = $1;
        $fileStub = $2;
      }
      else
      {
        $filename =~ m/(.*)\.zip/;
        $fileStub = $1;
        $directory = "/opt/apache/app/logs/";
      }

      unlink("$directory/$fileStub.zip");
      $query = "DELETE FROM $dsSchema.update_history WHERE ID=$id";
      $db->do($query);
    }

    #unlock the data source and update "last updated/last modifed"
    $query = "UPDATE dataSources SET lastUpdate=NOW(), lastModified=NOW()  \
        WHERE ID=$dsID";
    $db->do($query);

    #remove this task from the jobs table
    DSRutil_clear_status($db);

    exit;
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-10 col-lg-7 col-xl-6"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Roll Back Data Source Update</DIV>
        <DIV CLASS="card-body">

          The data source and all of the reports that depend on it are being rolled back to their pre-update status.

          <P>&nbsp;</P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" onClick="location.href='main.cld?ds=$dsID'"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

  utils_audit($db, $userID, "Rolled back data source update", $dsID, 0, 0);
  utils_slack($activity);

#EOF
