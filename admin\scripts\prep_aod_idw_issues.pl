#!/usr/bin/perl

use lib "/opt/apache/app/";

use DBI;
use Text::CSV;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::PrepUtils;
use Lib::WebUtils;



  #connect to the database
  $prepDB = PrepUtils_connect_to_database();

  #grab the ID and name of every data flow on the system using AOD extract
  $query = "SELECT ID, name FROM prep.flows WHERE sourceInfo LIKE 'FTP=nielsen%'";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  while (($flowID, $flowName) = $dbOutput->fetchrow_array)
  {
    $flowNameHash{$flowID} = $flowName;
  }

  $csv = Text::CSV->new( {binary => 1} );

  #cycle through every data flow's recipe, looking for recipe issues
  foreach $flowID (keys %flowNameHash)
  {

    $header = "\n\n-----------------------------------------------------------\n";
    $header .= "Checking $flowNameHash{$flowID} ($flowID)\n";
    $query = "SELECT step, action FROM prep.recipes \
        WHERE flowID=$flowID ORDER BY step";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;

    $catDiscardSeen = 0;
    $upcLookupSeen = 0;
    while (($stepID, $action) = $dbOutput->fetchrow_array)
    {

      #record the fact we've now seen a trim of the extra category file
      if ($action eq "TRANS-COL-DISCARD|COL=BC CATEGORY")
      {
        $catDiscardSeen = 1;
      }

      if ($action =~ m/TRANS-COL-LOOKUP\|COL=UPC\|TABLE=.*prodlookup.csv/)
      {
        $upcLookupSeen = 1;

=pod
        if ($catDiscardSeen == 0)
        {
          print("$header");
          $header = "";
          print("*** Lookup before BC CATEGORY discard\n");
        }
=cut
      }

      if ($action =~ m/TRANS-COL-TRIM-DATA\|COL=Market Display Name\|/)
      {
        if ($upcLookupSeen == 1)
        {
          print("$header");
          $header = "";
          print("*** Lookup before geography trim\n");
          print("$prepHostURL/app/prep/recipeEdit.cld?f=$flowID");
        }
      }
    }

    #if we hit the end and didn't see a BC CATEGORY trim step
    if ($catDiscardSeen == 0)
    {
      print("$header");
      $header = "";
      print("*** Missing BC CATEGORY discard\n");
      print("$Lib::KoalaConfig::prepHostURL/app/prep/recipeEdit.cld?f=$flowID");
    }
  }


#EOF
