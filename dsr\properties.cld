#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::Users;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Data Source Properties</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<SCRIPT SRC="/jquery-editable-select-2.2.5/dist/jquery-editable-select.min.js"></SCRIPT>
<LINK HREF="/jquery-editable-select-2.2.5/dist/jquery-editable-select.min.css" REL="stylesheet">

<STYLE>
.accordion-button.collapsed {
  background: blue
}

.accordion-button.collapsed::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}
</STYLE>


<SCRIPT>
function checkForm(form)
{
  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Please Wait...');
  return(true);
}



function exportDisplay(layout)
{
  document.getElementById('table-export-div').style.display = 'none';
  document.getElementById('star-export-div').style.display = 'none';

  if (layout == 'star')
  {
    document.getElementById('star-export-div').style.display = 'block';
  }
  else
  {
    document.getElementById('table-export-div').style.display = 'block';
  }
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Properties</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');
  $refLinkCode = $q->param('r');

  if ($refLinkCode eq "m")
  {
    $refLink = "main.cld?ds=$dsID";
  }
  else
  {
    $refLink = "display.cld?ds=$dsID";
  }

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsSchema = "datasource_" . $dsID;
  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  #make sure we have read privs for this data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to view the properties for this data source.");
  }

  $query = "SELECT name, userID, type, description, autoUpdateCubes, timePruning, ODBCexport, ODBCmanual, ODBCbaseItems, ODBCuser, ODBCpassword, ODBCstatus \
      FROM dataSources WHERE ID=$dsID";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($name, $ownerID, $dsType, $description, $autoUpdateCubes, $timePruning, $ODBCexport, $ODBCmanual, $ODBCbaseItems, $ODBCuser, $ODBCpassword, $ODBCstatus) = $dbOutput->fetchrow_array;

  $autoUpdateCubes = ($autoUpdateCubes == 1) ? "CHECKED" : "";

  $timePruningDuration = 104;
  $timePruningType = "weeks";
  if (defined($timePruning))
  {
    $enableTimePruning = "CHECKED";
    $timePruning =~ m/^(\d+) (.*?)$/;
    $timePruningDuration = $1;
    $timePruningType = $2;
  }

  $ODBClayout = "table";
  if ($ODBCexport > 0)
  {
    if ($ODBCexport == 2)
    {
      $ODBClayout = "star";
    }
    else
    {
      $ODBClayout = "table";
    }

    $oldODBCexport = $ODBCexport;
    $ODBCexport = "CHECKED";
  }
  else
  {
    $oldODBCexport = 0;
    $ODBCexport = "";
  }

  $ODBCmanual = ($ODBCmanual == 1) ? "CHECKED" : "";
  $ODBCbaseItems = ($ODBCbaseItems == 1) ? "CHECKED" : "";

  #get the number of base items in each dimension
  $query = "SELECT COUNT(ID) FROM $dsSchema.products";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($prodCount) = $dbOutput->fetchrow_array;
  $query = "SELECT COUNT(ID) FROM $dsSchema.geographies";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($geoCount) = $dbOutput->fetchrow_array;
  $query = "SELECT COUNT(ID) FROM $dsSchema.timeperiods";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($timeCount) = $dbOutput->fetchrow_array;
  $query = "SELECT COUNT(ID) FROM $dsSchema.measures";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($measCount) = $dbOutput->fetchrow_array;

  #total number of data points contained in data source
  $dataPoints = $prodCount * $geoCount * $timeCount * $measCount;

  #get the number of rows in the facts table (deal with sparse POS data sources)
  $tableName = $dsSchema . ".facts";
  $query = "SELECT table_rows FROM information_schema.TABLES \
      WHERE information_schema.TABLES.table_schema = '$dsSchema' AND information_schema.TABLES.table_name = 'facts'";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($recordCount) = $dbOutput->fetchrow_array;

  $ODBCmsg = "";
  $ODBCdisabled = "";
  if ($ODBCstatus eq "WORKING")
  {
    $ODBCmsg = "<P CLASS='text-warning'>This data source's ODBC export tables are being updated right now - ODBC options will be available when the update process completes.</P>";
    $ODBCdisabled = "DISABLED";
  }

  #XXX make this a configurable per-cloud variable in the future
  if (($dataPoints > 50_000_000_000) && ($recordCount > 10_000_000))
  {
    $ODBCmsg = "<P CLASS='text-warning'>This data source is too large for your cloud to export - please contact your account manager to increase your cloud sizing.</P>";
    $ODBCdisabled = "DISABLED";
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-11 col-lg-8 col-xl-7"> <!-- content -->

      <FORM METHOD="post" ACTION="/app/dsr/propertiesSave.cld" onsubmit="return checkForm(this);">
      <INPUT TYPE="hidden" NAME="ds" VALUE="$dsID">
      <INPUT TYPE="hidden" NAME="r" VALUE="$refLinkCode">
      <INPUT TYPE="hidden" NAME="oldOwnerID" VALUE="$ownerID">
      <INPUT TYPE="hidden" NAME="oldODBCexport" VALUE="$oldODBCexport">
      <INPUT TYPE="hidden" NAME="oldODBCuser" VALUE="$ODBCuser">
      <INPUT TYPE="hidden" NAME="oldODBCpassword" VALUE="$ODBCpassword">

      <DIV CLASS="accordion mx-auto" ID="accordion">

        <DIV CLASS="accordion-item border-primary">
          <H2 CLASS="accordion-header">
            <BUTTON CLASS="accordion-button bg-primary text-white icon-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
              Data Source Properties
            </BUTTON>
          </H2>
          <DIV ID="collapse1" CLASS="accordion-collapse collapse show" data-bs-parent="#accordion">
            <DIV CLASS="accordion-body">

              <TABLE>
                <TR>
                  <TD STYLE="text-align:right;">
                    <LABEL FOR="name">Name:</LABEL>
                  </TD>
                  <TD>
                    <INPUT CLASS="form-control" TYPE="text" NAME="name" ID="name" VALUE="$name" STYLE="width:300px;" required>
                  </TD>
                </TR>

                <TR>
                  <TD STYLE="text-align:right;">
                    <LABEL FOR="dsTypes">Type:</LABEL>
                  </TD>
                  <TD>
                    <DIV CLASS="row form-group">
                      <DIV CLASS="col">
                        <SELECT CLASS="form-select" NAME="dsType" ID="dsType" required>
END_HTML

  #get alpha-sorted list of all data types in cloud, and display
  @dsTypes = ds_types($db);
  foreach $type (@dsTypes)
  {
    print("    <OPTION>$type</OPTION>\n");
  }

  print <<END_HTML;
                        </SELECT>
                        <SCRIPT>
                          \$('#dsType').editableSelect({filter:false});
                          \$('#dsType').val('$dsType');
                        </SCRIPT>
                      </DIV>
                    </DIV>
                  </TD>
                </TR>

                <TR>
                  <TD>
                    <LABEL FOR="dsDescription">Description:</LABEL>
                    </TD>
                    <TD>
                      <INPUT CLASS="form-control" TYPE="text" NAME="dsDescription" ID="dsDescription" VALUE="$description" MAXLENGTH="1023">
                    </TD>
                  </TR>
                </TABLE>

                <P>
                <DIV CLASS="row">
                  <DIV CLASS="col-auto mt-2">
                    <LABEL FOR="dsOwner">Data Source Owner:</LABEL>
                  </DIV>
                  <DIV CLASS="col-auto">
                    <SELECT CLASS="form-select" NAME="dsOwner" ID="dsOwner">
END_HTML

  #get a list of all users in the private cloud, and display
  $query = "SELECT ID, first, last, orgID FROM users WHERE acctType > 0 \
      ORDER BY orgID, last";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;

  while (($id, $userFirst, $userLast, $userOrg) = $dbOutput->fetchrow_array)
  {
    $userOrg = Users_orgID_to_name($db, $userOrg);
    print(" <OPTION VALUE='$id'>$userFirst $userLast ($userOrg)</OPTION>\n");
  }

  print <<END_HTML;
                    </SELECT>
                    <SCRIPT>
                      \$('select#dsOwner').val('$ownerID');
                    </SCRIPT>
                  </DIV>
                </DIV>

                <P></P>
                (If you change the owner of this data source to another user, you'll still retain read and write privileges.)

              </DIV>
            </DIV>
          </DIV>

          <DIV CLASS="accordion-item border-primary">
            <H2 CLASS="accordion-header">
              <BUTTON CLASS="accordion-button collapsed bg-primary text-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
                Update History / Rollback Updates
              </BUTTON>
            </H2>
            <DIV ID="collapse2" CLASS="accordion-collapse collapse" data-bs-parent="#accordion">
              <DIV CLASS="accordion-body">

                <DIV CLASS="table-responsive">
                  <TABLE CLASS="table table-sm table-bordered mx-auto w-100">
                    <THEAD><TR>
                      <TH>Update User</TH>
                      <TH>Update Time</TH>
                    </TR></THEAD>
END_HTML

  #get a hash of all the usernames on the system for display purposes
  %userNames = utils_get_user_hash($db);

  $query = "SELECT ID, userID, timestamp, filename \
      FROM $dsSchema.update_history ORDER BY timestamp DESC";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;

  while (($updateID, $updateUserID, $updateTime, $updateFile) = $dbOutput->fetchrow_array)
  {
    print(" <TR>\n");
    print("  <TD>$userNames{$updateUserID}</TD>\n");
    print("  <TD>$updateTime\n");

    if (defined($updateFile))
    {
      print("<BUTTON CLASS='btn btn-primary' TYPE='button' onClick=\"location.href='updateRollbackConfirm.cld?ds=$dsID&id=$updateID'\"><I CLASS='bi bi-arrow-counterclockwise'></I> Roll Back</BUTTON>\n");
    }

    print(" </TD></TR>\n");
  }

  print <<END_HTML;
                  </TABLE>
                </DIV>

              </DIV>
            </DIV>
          </DIV>

          <DIV CLASS="accordion-item border-primary">
            <H2 CLASS="accordion-header">
              <BUTTON CLASS="accordion-button collapsed bg-primary text-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse3">
                Automatic Report Updating
              </BUTTON>
            </H2>
            <DIV ID="collapse3" CLASS="collapse collapsed" data-bs-parent="#accordion">
              <DIV CLASS="accordion-body">

                <DIV CLASS="form-check">
                  <INPUT CLASS="form-check-input" NAME="autoUpdateCubes" ID="autoUpdateCubes" TYPE="checkbox" $autoUpdateCubes>
                  <LABEL CLASS="form-check-label" FOR="autoUpdateCubes">Automatically update reports and presentations built on this data source when the data source is updated.</LABEL>
                </DIV>

              </DIV>
            </DIV>
          </DIV>

          <DIV CLASS="accordion-item border-primary">
            <H2 CLASS="accordion-header">
              <BUTTON CLASS="accordion-button collapsed bg-primary text-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse5">
                Time Period Pruning
              </BUTTON>
            </H2>
            <DIV ID="collapse5" CLASS="accordion-collapse collapse" data-bs-parent="#accordion">
              <DIV CLASS="accordion-body">

                <DIV CLASS="row">

                  <DIV CLASS="col-auto mt-1">
                    <DIV CLASS="form-check">
                      <INPUT CLASS="form-check-input" NAME="enableTimePruning" ID="enableTimePruning" TYPE="checkbox" $enableTimePruning>
                      <LABEL CLASS="form-check-label" FOR="enableTimePruning">Only retain</LABEL>
                    </DIV>
                  </DIV>

                  <DIV CLASS="col-auto gx-0">
                    <INPUT TYPE="number" NAME="timePruningDuration" ID="timePruningDuration" CLASS="form-control mx-1" STYLE="width:5em" VALUE="$timePruningDuration">
                  </DIV>

                  <DIV CLASS="col-auto gx-0">
                    <SELECT NAME="timePruningType" ID="timePruningType" CLASS="form-select">
                      <OPTION VALUE="weeks">weeks</OPTION>
                      <OPTION VALUE="months">months</OPTION>
                      <OPTION VALUE="years">years</OPTION>
                    </SELECT>
                    <SCRIPT>
                      \$('select#timePruningType').val('$timePruningType');
                    </SCRIPT>
                  </DIV>

                  <DIV CLASS="col-auto mt-1">
                    worth of data in this data source.
                  </DIV>
                </DIV>

                <P>&nbsp;</P>
                <EM>(Note that any changes will take effect the next time you update this data source, or gradually over time.)</EM>


          <P>&nbsp;</P>
          <DIV CLASS="accordion" ID="accordion-time-pruning">
            <DIV CLASS="accordion-item">
              <H2 CLASS="accordion-header">
                <BUTTON CLASS="accordion-button bg-secondary bg-opacity-10" TYPE="button" data-bs-toggle="collapse" data-bs-target="#time-period-pruning-collapse1">
                  Advanced
                </BUTTON>
              </H2>

              <DIV ID="time-period-pruning-collapse1" CLASS="accordion-collapse collapse" data-bs-parent="#accordion-time-pruning">
                <DIV CLASS="accordion-body">

END_HTML

  #if there's an existing advanced time pruning config, load it up for display
  $query = "SELECT timePruningAdvanced FROM app.dataSources WHERE ID=$dsID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ($timePruningAdvanced) = $dbOutput->fetchrow_array;
  if (length($timePruningAdvanced) > 0)
  {
    @timePruningCmds = split(/\|/, $timePruningAdvanced);
    foreach $timePruningCmd (@timePruningCmds)
    {
      if ($timePruningCmd =~ m/^(\d+) (\d+) (\d+)$/)
      {
        $type = $1;
        $duration = $2;
        $count = $3;
        $key = "$type-$duration";

        $advTimePruningOn{$key} = "CHECKED";
        $advTimePruningVal{$key} = $count;
      }
    }
  }

  #grab every time period type and duration present in the data source, and
  #output pruning row for them
  $query = "SELECT DISTINCT duration, type FROM $dsSchema.timeperiods";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  while (($duration, $type) = $dbOutput->fetchrow_array)
  {
    $key = "$type-$duration";
    if (!defined($advTimePruningVal{$key}))
    {
      $advTimePruningVal{$key} = 10;
    }

    #turn the type number into a human-readable string
    if ($type == 10)
    {
      $typeString = "year";
    }
    elsif ($type == 20)
    {
      $typeString = "month";
    }
    elsif ($type == 30)
    {
      $typeString = "week";
    }
    elsif ($type == 40)
    {
      $typeString = "day";
    }
    elsif ($type == 50)
    {
      $typeString = "hour";
    }
    elsif ($type == 0)
    {
      next;
    }

    print <<END_HTML;
                    <DIV CLASS="hstack">
                      <DIV CLASS="form-check">
                        <INPUT CLASS="form-check-input" NAME="prune-$type-$duration" ID="prune-$type-$duration" TYPE="checkbox" $advTimePruningOn{$key}>
                        <LABEL CLASS="form-check-label" FOR="prune-$type-$duration"></LABEL>
                      </DIV>
                        Only retain
                        <INPUT TYPE="number" NAME="prune-count-$type-$duration" ID="prune-count-$type-$duration" CLASS="form-control mx-1" STYLE="width:4em" VALUE="$advTimePruningVal{$key}">
                        $duration $typeString time period(s)
                    </DIV>
                    <P>
END_HTML
  }

  print <<END_HTML;
                </DIV>
              </DIV>

            </DIV>
          </DIV>



              </DIV>
            </DIV>
          </DIV>

          <DIV CLASS="accordion-item border-primary">
            <H2 CLASS="accordion-header">
              <BUTTON CLASS="accordion-button collapsed bg-primary text-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse4">
                ODBC Export
              </BUTTON>
            </H2>

            <DIV ID="collapse4" CLASS="accordion-collapse collapse" data-bs-parent="#accordion">
              <DIV CLASS="accordion-body">

                $ODBCmsg

                <DIV CLASS="form-check">
                  <INPUT CLASS="form-check-input" ID="ODBCexport" NAME="ODBCexport" TYPE="checkbox" $ODBCexport $ODBCdisabled>
                  <LABEL CLASS="form-check-label" FOR="ODBCexport">Export the contents of this data source to other applications via ODBC</LABEL>
                </DIV>

                <P></P>
                <DIV CLASS="form-check">
                  <INPUT CLASS="form-check-input" TYPE="radio" NAME="ODBClayout" ID="table" VALUE="table" onChange="exportDisplay('table')" $ODBCdisabled>
                  <LABEL CLASS="form-check-label" FOR="table">Export as a single table</LABEL>
                </DIV>
                <DIV CLASS="form-check">
                  <INPUT CLASS="form-check-input" TYPE="radio" NAME="ODBClayout" ID="star" VALUE="star" onChange="exportDisplay('star')" $ODBCdisabled>
                  <LABEL CLASS="form-check-label" FOR="star">Export as a basic star schema</LABEL>
                </DIV>
                <SCRIPT>
                  \$('#$ODBClayout').attr('checked', true);
                  \$(document).ready(function()
                  {
                    exportDisplay('$ODBClayout');
                  });
                </SCRIPT>

                <P></P>
                <DIV CLASS="form-check">
                  <INPUT CLASS="form-check-input" ID="ODBCmanual" NAME="ODBCmanual" TYPE="checkbox" $ODBCmanual $ODBCdisabled>
                  <LABEL CLASS="form-check-label" FOR="ODBCmanual">Don't automatically re-export this data source when changes are made</LABEL><BR>
                </DIV>
                <DIV CLASS="form-check">
                  <INPUT CLASS="form-check-input" ID="ODBCbaseItems" NAME="ODBCbaseItems" TYPE="checkbox" $ODBCbaseItems $ODBCdisabled>
                  <LABEL CLASS="form-check-label" FOR="ODBCbaseItems">Only use base item names (not aliases) in exported data</LABEL>
                </DIV>

                <P>&nbsp;</P>
                Specify the user name and password the remote application must use to access this Koala data source.

                <P>
                <TABLE>
                  <TR>
                    <TD STYLE="text-align:right;">
                      <LABEL FOR="ODBCuser">ODBC User Name:&nbsp;</LABEL>
                    </TD>
                    <TD ALIGN:"left"><INPUT CLASS="form-control" TYPE="text" NAME="ODBCuser" ID="ODBCuser" VALUE="$ODBCuser" $ODBCdisabled MAXLENGTH="16"></TD>
                  </TR>
                  <TR>
                    <TD STYLE="text-align:right;">
                      <LABEL FOR="ODBCpassword">ODBC Password:&nbsp;</LABEL>
                    </TD>
                    <TD><INPUT CLASS="form-control" TYPE="text" NAME="ODBCpassword" ID="ODBCpassword" VALUE="$ODBCpassword" $ODBCdisabled></TD>
                  </TR>
                </TABLE>

                <P>&nbsp;</P>
                <DIV ID="table-export-div">
                  The data source will be exported as <B>datasource_$dsID.export</B>
                </DIV>
                <DIV ID="star-export-div">
                  The data source will be exported as <B>datasource_$dsID.export</B><BR>
                  Product information will be exported as <B>datasource_$dsID.export_product</B><BR>
                  Geography information will be exported as <B>datasource_$dsID.export_geography</B><BR>
                  Time information will be exported as <B>datasource_$dsID.export_time</B>
                </DIV>

              </DIV>
            </DIV>
          </DIV>

        </DIV>

        <P>
        <DIV CLASS="text-center">
          <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="location.href='$refLink'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
          <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-submit"><I CLASS="bi bi-save"></I> Save</BUTTON>
        </DIV>

        </FORM>

        <P>

      </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
