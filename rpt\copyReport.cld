#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Copy Report</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$("#btn-submit").prop('disabled', true);
  \$("#btn-submit").text("Please Wait...");
  return(true);
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  $rptName = cube_id_to_name($db, $rptID);
  $dsName = ds_id_to_name($db, $dsID);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/rpt/main">Reports</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/rpt/main?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Copy Report</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser and get the forecast to be deleted
  $q = new CGI;
  $rptID = $q->param('r');
  $dsID = $q->param('ds');

  if ($rptID =~ m/^(\d+)\,.*/)
  {
    $rptID = $1;
  }

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  $db = KAPutil_connect_to_database();

  #get the report's name from the database
  if ($rptID > 0)
  {
    $query = "SELECT ID, name, dsID FROM app.cubes WHERE ID=$rptID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);

    ($srcRptID, $name, $dsID) = $dbOutput->fetchrow_array;
  }

  print_html_header();

  #make sure we have read privs for this data cube
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to copy this report.");
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-11 col-lg-8 col-xl-7"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Copy Report</DIV>
        <DIV CLASS="card-body">

          <P>
          <FORM METHOD="post" ACTION="/app/rpt/copyReportDo.cld" onsubmit="return checkForm(this);">
          <TABLE>
            <TR>
              <TD STYLE="text-align:right;">Report to copy:</TD>
              <TD ALIGN:"left">
                <SELECT CLASS="form-select mx-1" NAME="srcRpts" ID="srcRpts" required>
END_HTML

  $query = "SELECT ID, name FROM app.cubes WHERE dsID=$dsID ORDER BY name";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  while (($id, $name) = $dbOutput->fetchrow_array)
  {
    print("<OPTION VALUE=$id>$name</OPTION>");
  }

  print <<END_HTML;
                </SELECT>
                <SCRIPT>
                  \$('select#srcRpts').val('$rptID');
                </SCRIPT>

              </TD>
            </TR>
            <TR>
              <TD STYLE="text-align:right;">Create copy in:</TD>
              <TD>
                <SELECT CLASS="form-select mx-1" NAME="destDS">
                  <OPTION VALUE="0">Current Data Source</OPTION>
END_HTML

  #get the list of data sources the user has access to
  @userSources = ds_list($db, $userID, $acctType);
  $sources = join(',', @userSources);

  $query = "SELECT ID, name FROM dataSources WHERE ID IN ($sources) ORDER BY name";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  #output a select row for each data source the user has privs to
  ($id, $name) = $dbOutput->fetchrow_array;
  if ($dsID < 1)
  {
    $dsID = $id;
  }

  while (defined($id))
  {
    print("<OPTION VALUE=\"$id\">$name</OPTION>\n");
    ($id, $name) = $dbOutput->fetchrow_array;
  }

  print <<END_HTML;
                </SELECT>
              </TD>
            </TR>
          </TABLE>

          <P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="location.href='main'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-submit"><I CLASS="bi bi-clipboard-plus"></I> Copy Report</BUTTON>
          </DIV>
          </FORM>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
