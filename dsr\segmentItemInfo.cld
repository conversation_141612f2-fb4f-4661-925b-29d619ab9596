#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $dsID = $q->param('ds');
  $dim = $q->param('d');
  $itemStr = $q->param('i');
  $curSegID = $q->param('s');
  $action = $q->param('a');

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsSchema = "datasource_" . $dsID;

  $dbStub = KAPutil_get_dim_stub_name($dim);
  $dbName = $dbStub . "segment_item";

  #make sure we have read privs for this data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to view this data source.");
  }

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Segmentation Information</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Please Wait...');
  return(true);
}
</SCRIPT>

</HEAD>


<BODY>
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"> <!-- content -->

      <FORM METHOD="post" ACTION="segmentItemInfoSave.cld" onsubmit="return checkForm(this);">
      <INPUT TYPE="hidden" NAME="ds" VALUE="$dsID">
      <INPUT TYPE="hidden" NAME="d" VALUE="$dim">

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Item Segmentation Information</DIV>
        <DIV CLASS="card-body">
END_HTML

  #get the item/structure names for the current dimension
  %itemNameHash = dsr_get_item_name_hash($db, $dsSchema, $dim);

  #if there's a UPC attribute in this data source, find its ID and get val hash
  if ($dim eq "p")
  {
    $query = "SELECT ID FROM $dsSchema.product_attributes WHERE name = 'UPC'";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($upcID) = $dbOutput->fetchrow_array;
    undef(%upcHash);
    if (defined($upcID))
    {
      %upcHash = DSRattr_get_values_hash($db, $dsSchema, "p", $upcID);
    }
  }

  #get the name-sorted array of all segmentations for the current dimension
  @segmentationArray = DSRsegmentation_get_segmentations_array($db, $dsSchema, $dim);

  #build up an array of segment arrays (first level is segmentation, second
  #level is segment
  undef(@segmentsArray);
  $segIdx = 0;
  foreach $segmentationID (@segmentationArray)
  {
    @segments = DSRseg_get_segments_array($db, $dsSchema, $dim, $segmentationID);

    #keep "bad" segmentations that contain a bazillion (well, over 500) segments
    #that are numbers from overloading the user's browser
    if (scalar(@segments) > 500)
    {

      #quasi-random check to see if the segments are numbers
      if (($itemNameHash{$segments[2]} =~ m/^[\d\.\-]+$/) &&
          ($itemNameHash{$segments[100]} =~ m/^[\d\.\-]+$/) &&
          ($itemNameHash{$segments[200]} =~ m/^[\d\.\-]+$/) &&
          ($itemNameHash{$segments[300]} =~ m/^[\d\.\-]+$/))
      {

        #truncate the list of segments, for display purposes, by inserting undef
        undef($segments[200]);
      }
    }

    $smtIdx = 0;
    foreach $smtID (@segments)
    {
      $segmentsArray[$segIdx][$smtIdx] = $smtID;
      $smtIdx++;
    }
    $segIdx++;
  }

  #run through each item requested by the user, and output all seg and attr info
  @baseItems = split(',', $itemStr);
  foreach $itemID (@baseItems)
  {
    print("<H4>$itemNameHash{$itemID}</H4><BR>\n");
    print(" <TABLE>\n");

    #if we're a product dimension, output UPC
    if ($dim eq "p")
    {
      print("  <TR>\n");
      print("<TD STYLE='text-align:right; font-weight:bold;'>UPC:</TD>\n");
      print("<TD>$upcHash{$itemID}</TD>\n");
      print("  </TR>\n");
    }

    #get all segmentation memberships for the current item, and hash them
    undef(%itemSegMembership);
    $query = "SELECT segmentationID, segmentID FROM $dsSchema.$dbName \
        WHERE itemID=$itemID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    while (($segID, $segmentID) = $dbOutput->fetchrow_array)
    {
      $itemSegMembership{$segID} = $segmentID;
    }

    #output a membership line for each segmentation in the data source
    $segIdx=0;
    foreach $segID (@segmentationArray)
    {
      $segmentationID = "SEG_" . $segID;
      $segmentationName = $itemNameHash{$segmentationID};

      $segmentID = $itemSegMembership{$segID};
      $segmentID = "SMT_" . $segmentID;
      $segmentName = $itemNameHash{$segmentID};

      $htmlSelectName = "ITM_" . $itemID . $segmentationID;

      #don't allow changing of entries in the segmentation being edited
      $disabled="";
      if ($segID == $curSegID)
      {
        $disabled = "DISABLED";
      }

      print("  <TR>\n");
      print("<TD STYLE='text-align:right; font-weight:bold;'>$segmentationName:</TD>\n");
      print("<TD><SELECT CLASS='form-select' $disabled NAME=\"$htmlSelectName\" ID=\"$htmlSelectName\">\n");
      print("<OPTION VALUE=0>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</OPTION>\n");
      $smtIdx = 0;
      while (defined($segmentsArray[$segIdx][$smtIdx]))
      {
        $fqSmtID = "SMT_" . $segmentsArray[$segIdx][$smtIdx];
        $smtName = $itemNameHash{$fqSmtID};
        print("<OPTION VALUE=\"$fqSmtID\">$smtName</OPTION>\n");
        $smtIdx++;
      }

      print("</SELECT>\n");
      print("  <SCRIPT>\$('select#$htmlSelectName').val('$segmentID');</SCRIPT>\n");
      print("   </TD>\n");
      print("  </TR>\n");

      $segIdx++;
    }

    print(" </TABLE>\n");
    print("<HR STYLE='height:1px; background:lightblue; color:lightblue; width:100%;'>\n");
  }

  print <<END_HTML;
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-submit"><I CLASS="bi bi-save"></I> Save Changes</BUTTON>
          </DIV>

        </DIV>
      </DIV>

      </FORM>

    </DIV>  <!-- content -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
<P>

</BODY>
</HTML>

END_HTML

#EOF
