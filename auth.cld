#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;
use Email::Valid;
use LWP::Simple qw(!head);

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::WebUtils;


#-------------------------------------------------------------------------


#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV CLASS="bg-secondary bg-opacity-10">
  <OL CLASS="breadcrumb px-2 py-2">
   <LI CLASS="breadcrumb-item">License Agreement</LI>
  </OL>
</NAV>
END_HTML
}



#-------------------------------------------------------------------------


  #create the CGI session
  $session = CGI::Session->new();

  #get form data
  $form = new CGI;
  $email = $form->param('email');
  $password = $form->param('password');

  #connect to user login database
  $db = KAPutil_connect_to_database();

  if (!(defined($db)))
  {
    print_html_header();
    exit_error("Your cloud instance is currently overloaded - please try again in 15 minutes.");
  }

  #validate the email address
  $email = lc($email);
  $email = Email::Valid->address($email);
  if (!(defined($email)))
  {
    print_html_header();
    exit_error("Invalid email address $email; press your browser's \"Back\" button and try again.");
  }

  #scrub the CGI form data before we let it near the database
  $q_email = $db->quote($email);
  $q_password = $db->quote($password);

  #get the user's info from the database, if it exists
  $query = "SELECT ID, orgID, first, last, acctType, disabled, clickThrough \
      FROM users WHERE email=$q_email AND password=$q_password";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  #if the username/password combo doesn't exist, toss an error
  if ($status < 1)
  {
    print_html_header();

    $activity = "Invalid login attempt on account $email ($ENV{'REMOTE_ADDR'}), $ENV{'HTTP_USER_AGENT'}";
    utils_slack($activity);

    exit_error("Invalid username/password combination.");
  }

  #extract the user's info
  ($userID, $orgID, $first, $last, $acctType, $disabled, $clickThrough) = $dbOutput->fetchrow_array;

  #if the account is disabled, toss an error
  if ($disabled == 1)
  {
    print_html_header();
    exit_error("This account has been disabled.");
  }

  #get the user's organization info from the database
  $query = "SELECT name FROM orgs WHERE ID=$orgID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($orgName) = $dbOutput->fetchrow_array;

  #store the user's info in the CGI session object
  $session->param(userID, $userID);
  $session->param(email, $email);
  $session->param(first, $first);
  $session->param('last', $last);
  $session->param(acctType, $acctType);
  $session->param(orgID, $orgID);
  $session->param(orgName, $orgName);

  #record login date/time in database
  $query = "UPDATE users SET lastLogin=NOW() WHERE email=$q_email";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);

  #force user to accept click-through license if this is their first login
  if ($clickThrough == 0)
  {
    print_html_header();
    print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-11 col-md-8 col-lg-6 col-xl-5"> <!-- content -->

      <P>
      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Terms and Conditions</DIV>
        <DIV CLASS="card-body">

          <P>
          I agree to the terms and conditions of the Koala Analytics <A TARGET="_blank" HREF="/agreement.html">Subscription Agreement</A>.

          <P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" onClick="location.href='/index.html'">Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" onClick="location.href='/app/home.cld?ct=1'">Agree</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

    print_html_footer();

    #flush the CGI session info out to storage
    $session->flush();

    exit;
  }

  #login was successful, so re-direct to home page
  $session->flush();
  print("Status: 302 Moved temporarily\n");
  print("Location: /app/home.cld\n");
  print($session->header());

  #send a login notification to Slack - very last thing we do, so any
  #slowness on Slack's part doesn't affect us
  $activity = "$first $last from $orgName logged in ($ENV{'REMOTE_ADDR'}), $ENV{'HTTP_USER_AGENT'}";
  utils_slack($activity);

#EOF
