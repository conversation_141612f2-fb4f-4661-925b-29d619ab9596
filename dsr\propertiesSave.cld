#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Data Source Properties</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Properties</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');
  $refLinkCode = $q->param('r');
  $dsName = $q->param('name');
  $dsType = $q->param('dsType');
  $dsDescription = $q->param('dsDescription');
  $dsOwner = $q->param('dsOwner');
  $oldOwnerID = $q->param('oldOwnerID');
  $autoUpdateCubes = $q->param('autoUpdateCubes');
  $enableTimePruning = $q->param('enableTimePruning');
  $timePruningDuration = $q->param('timePruningDuration');
  $timePruningType = $q->param('timePruningType');
  $ODBCexport = $q->param('ODBCexport');
  $ODBClayout = $q->param('ODBClayout');
  $ODBCmanual = $q->param('ODBCmanual');
  $ODBCbaseItems = $q->param('ODBCbaseItems');
  $oldODBCexport = $q->param('oldODBCexport');
  $ODBCuser = $q->param('ODBCuser');
  $oldODBCuser = $q->param('oldODBCuser');
  $ODBCpassword = $q->param('ODBCpassword');
  $oldODBCpassword = $q->param('oldODBCpassword');

  $autoUpdateCubes = $autoUpdateCubes ? 1 : 0;
  $ODBCexport = ($ODBCexport eq "on") ? 1 : 0;
  $ODBCmanual = ($ODBCmanual eq "on") ? 1 : 0;
  $ODBCbaseItems = ($ODBCbaseItems eq "on") ? 1 : 0;

  if (($ODBCexport == 1) && ($ODBClayout eq "star"))
  {
    $ODBCexport = 2;
  }

  if ($refLinkCode eq "m")
  {
    $refLink = "main.cld?ds=$dsID";
  }
  else
  {
    $refLink = "display.cld?ds=$dsID";
  }

  #connect to the database
  $db = KAPutil_connect_to_database();

  print_html_header();

  #make sure we have write privs for the data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to modify this data source.");
  }

  #preserve reserved ODBC user names
  if ((lc($ODBCuser) eq "root") || (lc($ODBCuser) eq "app") ||
      (lc($ODBCuser) eq "lamont"))
  {
    exit_error("The ODBC user name <B>$ODBCuser</B> is reserved by the system and can't be used for data sources.");
  }

  #enforce some basic naming/password security rules for ODBC
  if ($ODBCexport > 0)
  {
    if (length($ODBCuser) < 1)
    {
      exit_error("An ODBC user name must be specified to export a data source.");
    }
    if (length($ODBCpassword) < 1)
    {
      exit_error("An ODBC password must be specified to export a data source.");
    }
  }

  $dsSchema = "datasource_" . $dsID;

  utils_audit($db, $userID, "Edited data source properties", $dsID, 0, 0);
  $activity = "$first $last edited properties of data source $dsName";

  #set up time pruning info for storage
  if (defined($enableTimePruning))
  {
    $timePruning = "$timePruningDuration $timePruningType";
    $q_timePruning = $db->quote($timePruning);
  }
  else
  {
    $q_timePruning = "NULL";
  }

  #get any advanced time period pruning directives
  @names = $q->param;
  foreach $name (@names)
  {
    if ($name =~ m/^prune\-(\d+)\-(\d+)/)
    {
      $key = "$1 $2";
      $val = $q->param($name);
      if ($val eq "on")
      {
        $count = $q->param("prune-count-$1-$2");
        $advTimePruning .= "$key $count|";
      }
    }
  }



  #update name and alias
  $q_name = $db->quote($dsName);
  $q_type = $db->quote($dsType);
  $q_desc = $db->quote($dsDescription);
  $q_timePruning = $db->quote($timePruning);
  $q_advTimePruning = $db->quote($advTimePruning);
  $q_ODBCuser = $db->quote($ODBCuser);
  $q_oldODBCuser = $db->quote($oldODBCuser);
  $q_ODBCpassword = $db->quote($ODBCpassword);
  $query = "UPDATE dataSources \
      SET name=$q_name, type=$q_type, description=$q_desc, autoUpdateCubes=$autoUpdateCubes, timePruning=$q_timePruning, timePruningAdvanced=$q_advTimePruning, ODBCexport=$ODBCexport, ODBCmanual=$ODBCmanual, ODBCbaseItems=$ODBCbaseItems, ODBCuser=$q_ODBCuser, ODBCpassword=$q_ODBCpassword \
      WHERE ID=$dsID";
  $db->do($query);

  #if the user is disabling a previously enabled ODBC export
  if (($oldODBCexport > 0) && ($ODBCexport == 0))
  {
    $query = "REVOKE ALL ON $dsSchema.export FROM $q_oldODBCuser";
    $db->do($query);
    KAPutil_db_delete_table($db, $dsSchema, $pricingTable);

    KAPutil_db_delete_table($db, $dsSchema, "export");

    if ($oldODBCexport == 2)
    {
      $query = "REVOKE ALL ON $dsSchema.export_product FROM $q_oldODBCuser";
      $db->do($query);
      $query = "REVOKE ALL ON $dsSchema.export_geography FROM $q_oldODBCuser";
      $db->do($query);
      $query = "REVOKE ALL ON $dsSchema.export_time FROM $q_oldODBCuser";
      $db->do($query);

      KAPutil_db_delete_table($db, $dsSchema, "export_product");
      KAPutil_db_delete_table($db, $dsSchema, "export_geography");
      KAPutil_db_delete_table($db, $dsSchema, "export_time");
    }

    utils_audit($db, $userID, "Disabled ODBC export", $dsID, 0, 0);
    $activity = "$activity\n$first $last disabled ODBC export for $dsName";
  }

  #elsif the user is enabling a previously disabled export
  elsif (($oldODBCexport == 0) && ($ODBCexport > 0))
  {
    $query = "CREATE TABLE $dsSchema.export(id int)";
    $db->do($query);
    $query = "CREATE USER IF NOT EXISTS $q_ODBCuser IDENTIFIED BY $q_ODBCpassword";
    $db->do($query);
    $query = "GRANT SELECT ON $dsSchema.export TO $q_ODBCuser";
    $db->do($query);

    utils_audit($db, $userID, "Enabled ODBC export of data source", $dsID, 0, 0);
    $activity = "$activity\n$first $last enabled ODBC export of data source $dsName";
  }

  #elsif we're changing the username or password
  elsif (($ODBCuser ne $oldODBCuser) || ($ODBCpassword ne $oldODBCpassword))
  {
    $query = "REVOKE ALL ON $dsSchema.export FROM $q_oldODBCuser";
    $db->do($query);
    $query = "CREATE USER IF NOT EXISTS $q_ODBCuser IDENTIFIED BY $q_ODBCpassword";
    $db->do($query);
    $query = "GRANT SELECT ON $dsSchema.export TO $q_ODBCuser";
    $db->do($query);

    if ($oldODBCexport == 2)
    {
      $query = "REVOKE ALL ON $dsSchema.export_product FROM $q_oldODBCuser";
      $db->do($query);
      $query = "GRANT SELECT ON $dsSchema.export_product TO $q_ODBCuser";
      $db->do($query);
      $query = "REVOKE ALL ON $dsSchema.export_geography FROM $q_oldODBCuser";
      $db->do($query);
      $query = "GRANT SELECT ON $dsSchema.export_geography TO $q_ODBCuser";
      $db->do($query);
      $query = "REVOKE ALL ON $dsSchema.export_time FROM $q_oldODBCuser";
      $db->do($query);
      $query = "GRANT SELECT ON $dsSchema.export_time TO $q_ODBCuser";
      $db->do($query);
    }

    utils_audit($db, $userID, "Changed ODBC username/password", $dsID, 0, 0);
    $activity = "$activity\n$first $last changed ODBC username/password for data source $dsName";
  }

  #if we're changing the data source's owner
  if (($dsOwner > 0) && ($dsOwner != $oldOwnerID))
  {

    #update the data source owner to the new selection
    $query = "UPDATE dataSources SET userID=$dsOwner WHERE ID=$dsID";
    $db->do($query);

    #grant the original DS owner read/write privs on the data source
    $query = "SELECT RWusers FROM dataSources WHERE ID=$dsID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($rwusers) = $dbOutput->fetchrow_array;

    if (length($rwusers) > 0)
    {
      $rwusers .= ",";
    }
    $rwusers .= $oldOwnerID;

    $query = "UPDATE dataSources SET RWusers='$rwusers' WHERE ID=$dsID";
    $db->do($query);

    utils_audit($db, $userID, "Changed data source ownership", $dsID, 0, 0);
    $activity = "$activity\n$first $last changed ownership of data source $dsName";

    #change the ownership of the original user's reports in the data source
    $query = "SELECT ID FROM cubes WHERE dsID=$dsID AND userID=$oldOwnerID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;

    while (($cubeID) = $dbOutput->fetchrow_array)
    {
      $query = "UPDATE cubes SET userID=$dsOwner WHERE ID=$cubeID";
      $db->do($query);
      utils_audit($db, $userID, "Changed ownership to new data source owner", $dsID, $cubeID, 0);

      #grant the original DS owner read/write privs on the report
      $query = "SELECT RWusers FROM cubes WHERE ID=$cubeID";
      $dbOutput1 = $db->prepare($query);
      $dbOutput1->execute;
      ($rwusers) = $dbOutput1->fetchrow_array;

      if (length($rwusers) > 0)
      {
        $rwusers .= ",";
      }
      $rwusers .= $oldOwnerID;

      $query = "UPDATE cubes SET RWusers='$rwusers' WHERE ID=$cubeID";
      $db->do($query);
    }
  }

  #if the analyst flipped the ODBC layout between star and table
  if (($ODBCexport > 0) && ($oldODBCexport != $ODBCexport))
  {
    $query = "UPDATE app.dataSources SET ODBCexported='2000-01-01 01:00:00' \
        WHERE ID=$dsID";
    $db->do($query);
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-10 col-lg-7 col-xl-6"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Data Source Properties</DIV>
        <DIV CLASS="card-body">

          Your changes to the $dsName data source have been saved.

          <P>&nbsp;<P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='$refLink'"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();
  utils_slack($activity);

#EOF
