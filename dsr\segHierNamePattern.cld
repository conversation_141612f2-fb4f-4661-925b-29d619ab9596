#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: $readableAction Segmentation Hierarchy Naming Pattern</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Please Wait...');
  return(true);
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">$readableAction Segmentation Hierarchy</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('dsID');
  $dim = $q->param('dim');
  $segHierName = $q->param('segHierName');
  $segHierID = $q->param('segHier');
  $hierarchy = $q->param('hierarchy');

  if ($segHierID > 0)
  {
    $readableAction = "Edit";
    $textAction = "edited";
  }
  else
  {
    $readableAction = "New";
    $textAction = "created";
  }

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsSchema = "datasource_" . $dsID;
  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  #make sure we have write privs for the data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to modify this data source.");
  }

  #get our dimension's table name
  $dimDB = KAPutil_get_dim_stub_name($dim);
  $segHierTable = $dimDB . "seghierarchy";

  #get hash of segmentation names for display purposes
  %segmentations = DSRsegmentation_get_segmentations_hash($db, $dsSchema, $dim);

  #grab any existing hierarchy level naming pattern on initial load
  if ($segHierID > 0)
  {
    $query = "SELECT namePattern FROM $dsSchema.$segHierTable WHERE ID=$segHierID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($namePattern) = $dbOutput->fetchrow_array;

    #get a hash, indexed by level, of current pattern selections
    if (length($namePattern) > 1)
    {
      @namePatternLevels = split(',', $namePattern);
      foreach $pattern (@namePatternLevels)
      {
        if ($pattern =~ m/(\d+):(.*)/)
        {
          $curPatternHash{$1} = $2;
        }
      }
    }
  }

  #split the patterns into a hash indexed by level ID
  undef(%namePatternHash);
  @levelPatterns = split('|', $namePatternLevels);
  foreach $levelPattern (@levelPatterns)
  {
    if ($levelPattern =~ m/(\d+):(.*)/)
    {
      $namePatternHash{$1} = $2;
    }
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-11 col-lg-8 col-xl-7"> <!-- content -->

      <FORM METHOD="post" ACTION="segHierSave.cld" onsubmit="return checkForm(this);">
      <INPUT TYPE="hidden" NAME="dsID" VALUE="$dsID">
      <INPUT TYPE="hidden" NAME="dim" VALUE="$dim">
      <INPUT TYPE="hidden" NAME="segHierName" VALUE="$segHierName">
      <INPUT TYPE="hidden" NAME="segHier" VALUE="$segHierID">
      <INPUT TYPE="hidden" NAME="hierarchy" VALUE="$hierarchy">

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">$readableAction Segmentation Hierarchy Naming Pattern</DIV>
        <DIV CLASS="card-body">

          <TABLE>
            <TR>
              <TD STYLE='font-weight:bold;padding-right:25px;'>Hierarchy Level</TD>
              <TD STYLE='font-weight:bold;'>Naming Pattern</TD>
            </TR>
END_HTML

  #output naming pattern row for every level in hierarchy
  @levels = split(',', $hierarchy);
  $levelIdx = 1;
  foreach $levelID (@levels)
  {
    print <<END_HTML;
            <TR>
              <TD STYLE='text-align:right;padding-right:25px;'>$segmentations{$levelID}:</TD>
              <TD>
                <SELECT CLASS='form-select' NAME='pattern_$levelIdx' ID='pattern_$levelIdx'>
END_HTML

    $leftmostSeg = 1;
    while ($leftmostSeg <= $levelIdx)
    {

      $val = "";
      $name = "";
      $i = $leftmostSeg;
      while ((defined($levels[$i-1])) && ($i <= $levelIdx))
      {
        $val .= $i . "_";
        $name .= $segmentations{$levels[$i-1]} . " ";
        $i++;
      }
      chop($val);

      print("   <OPTION VALUE='$val'>$name</OPTION>\n");

      $leftmostSeg++;
    }

    print(" </SELECT>\n");
    if (length($curPatternHash{$levelIdx}) > 0)
    {
      print(" <SCRIPT>\$('select#pattern_$levelIdx').val('$curPatternHash{$levelIdx}');</SCRIPT>\n");
    }
    print(" </TD>\n");
    print("</TR>\n");

    $levelIdx++;
  }

  print <<END_HTML;
          </TABLE>

          <P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="location.href='display.cld?ds=$dsID&dim=$dim'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-submit"><I CLASS="bi bi-save"></I> Save</BUTTON>
          </DIV>

        </DIV>
      </DIV>
      </FORM>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();
  utils_slack($activity);

#EOF
