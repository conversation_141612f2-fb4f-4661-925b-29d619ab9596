#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::Reports;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $rptID = $q->param('rptID');
  $visID = $q->param('v');
  $minColor = $q->param('minColor');
  $midColor = $q->param('midColor');
  $maxColor = $q->param('maxColor');

  #fix up the CGI parameters from the submitted form
  if (defined($minColor))
  {
    $minColor = "#" . $minColor;
    $midColor = "#" . $midColor;
    $maxColor = "#" . $maxColor;
  }

  $db = KAPutil_connect_to_database();

  #make sure we have edit privs for this report
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this report.");
  }

  $cubeName = cube_id_to_name($db, $rptID);
  $dsID = cube_get_ds_id($db, $rptID);
  $dsName = ds_id_to_name($db, $dsID);

  #get the chart border/background details from the database
  $query = "SELECT design FROM visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($design) = $dbOutput->fetchrow_array;

  #extract map gradient colors from design string
  $mapMinColor = reports_get_style($design, "minColor");
  $mapMidColor = reports_get_style($design, "midColor");
  $mapMaxColor = reports_get_style($design, "maxColor");

  if (!(defined($mapMinColor)))
  {
    $mapMinColor = "#fd625e";
  }
  if (!(defined($mapMidColor)))
  {
    $mapMidColor = "#f2c80f";
  }
  if (!(defined($mapMaxColor)))
  {
    $mapMaxColor = "#01b8aa";
  }


  ########################################################################
  #
  # This code block is called on submit to save the changes
  #

  if (defined($minColor))
  {

    $design = reports_set_style($design, "minColor", $minColor);
    $design = reports_set_style($design, "midColor", $midColor);
    $design = reports_set_style($design, "maxColor", $maxColor);

    $q_design = $db->quote($design);
    $query = "UPDATE visuals SET design = $q_design WHERE ID=$visID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    utils_audit($db, $userID, "Changed map color gradients", $dsID, $rptID, 0);
    $activity = "$first $last changed map color gradients for $cubeName in $dsName";
    utils_slack($activity);

    exit;
  }


  #########################################################################
  #
  # Everything after this point is called to display the map colors dialog
  #

  print <<END_HTML;
<SCRIPT>
function submitForm()
{
  let min = document.getElementById('minColor').value;
  let mid = document.getElementById('midColor').value;
  let max = document.getElementById('maxColor').value;

  //knock # off of color strings
  min = min.substr(1);
  mid = mid.substr(1);
  max = max.substr(1);

  let url = "xhrMapColors?rptID=$rptID&v=$visID&minColor=" + min +
      "&midColor=" + mid + "&maxColor=" + max;

  \$.get(url, function(data, status)
  {
    location.href = "display.cld?rpt=$rptID&v=$visID";
  });
}
</SCRIPT>

<DIV CLASS="modal-dialog" STYLE="overflow-y: initial !important;">
  <DIV CLASS="modal-content">
    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Map Gradient Color Points</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">

      <FORM>
      <TABLE CLASS="mx-auto">
        <TR>
          <TD STYLE="text-align:right;">
            Minimum color:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="color" NAME="minColor" ID="minColor" VALUE="$mapMinColor" STYLE="width:3em;">
          </TD>
        </TR>

        <TR>
          <TD>&nbsp;</TD><TD>&nbsp;</TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Midpoint color:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="color" NAME="midColor" ID="midColor" VALUE="$mapMidColor">
          </TD>
        </TR>

        <TR>
          <TD>&nbsp;</TD><TD>&nbsp;</TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Maximum color:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="color" NAME="maxColor" ID="maxColor" VALUE="$mapMaxColor">
          </TD>
        </TR>
      </TABLE>
      </FORM>

    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="submitForm()"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML


#EOF
