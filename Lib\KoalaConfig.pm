package Lib::KoalaConfig;

$dbServer = "DBI:mysql:app;host=beacon.koaladata.com";
$dbServerName = "beacon.koalacorp.com";

$user = "app";
$password = "screwth1s";
$hostname = "demo.koaladata.com";
$cloudname = "dev";
$kapHostURL = "https://$hostname";

$cloudtype = "prod"; #dev, prod, poc, multi

$cores = "2";

$dataLimit = "500";
$dataDisk = "/dev/sda2";

$prepDBServer = "DBI:mysql:prep;host=**************";
$prepHostname = "beacon-prep.koaladata.com";
$prepHostURL = "https://$prepHostname";
$prepCores = 2;

1;