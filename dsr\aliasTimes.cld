#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Alias All Time Periods</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<SCRIPT>

function checkForm(form)
{
  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Please Wait...');
  return(true);
}



function updateSample()
{
  let sample1, sample2, sample3, htmlString;
  let selDuration = document.getElementById('duration').value;
  let selFormat = document.getElementById('tformat').value;

  if (selDuration == 1)
  {
    sample1 = '1 Week Ending ';
    sample2 = '4 Weeks Ending ';
    sample3 = '3 Months Ending ';
  }
  else if (selDuration == 2)
  {
    sample1 = '1 Week ';
    sample2 = '4 Weeks ';
    sample3 = '3 Months ';
  }
  else if (selDuration == 3)
  {
    sample1 = '1 WE ';
    sample2 = '4 WE ';
    sample3 = '3 ME ';
  }
  else if (selDuration == 4)
  {
    sample1 = '';
    sample2 = '';
    sample3 = '';
  }

  if (selFormat == 1)
  {
    sample1 = sample1 + '3/27/2019';
    sample2 = sample2 + '3/27/2019';
    sample3 = sample3 + '3/27/2019';
  }
  else if (selFormat == 2)
  {
    sample1 = sample1 + '3/27/19';
    sample2 = sample2 + '3/27/19';
    sample3 = sample3 + '3/27/19';
  }
  else if (selFormat == 3)
  {
    sample1 = sample1 + '3/27';
    sample2 = sample2 + '3/27';
    sample3 = sample3 + '3/27';
  }
  else if (selFormat == 4)
  {
    sample1 = sample1 + 'Mar 27, 2019';
    sample2 = sample2 + 'Mar 27, 2019';
    sample3 = sample3 + 'Mar 27, 2019';
  }
  else if (selFormat == 5)
  {
    sample1 = sample1 + 'March 27, 2019';
    sample2 = sample2 + 'March 27, 2019';
    sample3 = sample3 + 'March 27, 2019';
  }
  else if (selFormat == 6)
  {
    sample1 = sample1 + '27-MAR-2019';
    sample2 = sample2 + '27-MAR-2019';
    sample3 = sample3 + '27-MAR-2019';
  }
  else if (selFormat == 7)
  {
    sample1 = sample1 + '03/27/2019';
    sample2 = sample2 + '03/27/2019';
    sample3 = sample3 + '03/27/2019';
  }
  else if (selFormat == 8)
  {
    sample1 = sample1 + '03/27/19';
    sample2 = sample2 + '03/27/19';
    sample3 = sample3 + '03/27/19';
  }

  htmlString = sample1 + '<BR>' + sample2 + '<BR>' + sample3;

  document.getElementById('samples').innerHTML = htmlString;
}
</SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Alias All Time Periods</LI>
  </OL>
</NAV>

<P>

END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');

  #sanitize parameters
  $dsID = utils_sanitize_integer($dsID);
  if (!defined($dsID))
  {
    exit_early_error($session, "Invalid data source");
  }

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  #make sure we have read privs for this data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to view this data source.");
  }

  #grab our current settings
  $query = "SELECT timeAlias FROM app.dataSources WHERE ID=$dsID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($timeAlias) = $dbOutput->fetchrow_array;
  if ($timeAlias =~ m/(\d+)\|(\d+)\|(\d+)/)
  {
    $duration = $1;
    $timeFormat = $2;
    $overwrite = $3;
    if ($overwrite > 0)
    {
      $overwrite = "CHECKED";
    }
  }
  else
  {
    $duration = 1;
    $timeFormat = 1;
    $overwrite = "";
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-10 col-lg-7 col-xl-6"> <!-- content -->

      <FORM METHOD="post" ACTION="aliasTimesDo.cld" onsubmit="return checkForm(this);">
      <INPUT TYPE="hidden" NAME="ds" VALUE="$dsID">

      <DIV CLASS="card border-primary">
        <DIV CLASS="card-header bg-primary text-white">Alias All Time Periods</DIV>
        <DIV CLASS="card-body">
          <P>
          Choose the layout you'd like to use to create an alias for every time period in the data source. (The alias will also be automatically created for new time periods that come into the data source when it's updated.)
          <P>
          <TABLE CLASS="w-100">
            <TR>
              <TD>
                <LABEL FOR="duration">Duration Formatting:</LABEL>
                <SELECT NAME="duration" ID="duration" CLASS="form-select me-1" onChange="updateSample()" required>
                  <OPTION VALUE="1">N Weeks Ending</OPTION>
                  <OPTION VALUE="2">N Weeks</OPTION>
                  <OPTION VALUE="3">N WE</OPTION>
                  <OPTION VALUE="4">(None)</OPTION>
                </SELECT>
                <SCRIPT>
                  \$('select#duration').val('$duration');
                </SCRIPT>
              </TD>
              <TD>
                <LABEL FOR="tformat">Time Formatting:</LABEL>
                <SELECT NAME="tformat" ID="tformat" CLASS="form-select ms-1" onChange="updateSample()" required>
                  <OPTION VALUE="1">3/27/2019</OPTION>
                  <OPTION VALUE="7">03/27/2019</OPTION>
                  <OPTION VALUE="8">03/27/19</OPTION>
                  <OPTION VALUE="2">3/27/19</OPTION>
                  <OPTION VALUE="3">3/27</OPTION>
                  <OPTION VALUE="4">Mar 27, 2019</OPTION>
                  <OPTION VALUE="5">March 27, 2019</OPTION>
                  <OPTION VALUE="6">27-MAR-2019</OPTION>
                </SELECT>
                <SCRIPT>
                  \$('select#tformat').val('$timeFormat');
                </SCRIPT>
              </TD>
            </TR>
          </TABLE>

          <DIV CLASS="form-check my-3">
            <INPUT CLASS="form-check-input" NAME="overwrite" ID="overwrite" TYPE="checkbox" $overwrite>
            <LABEL CLASS="form-check-label" FOR="overwrite">Overwrite any existing time period aliases</LABEL>
          </DIV>

          <B>Samples:</B>
          <DIV ID="samples" CLASS="ps-1" STYLE="border:1px solid lightblue;">
            1 Week Ending 3/27/2019<BR>
            4 Weeks Ending 3/27/2019<BR>
            3 Months Ending 3/27/2019
          </DIV>
          <P>&nbsp;<P>

          <P>
          <DIV CLASS="text-center">
            <BUTTON TYPE="button" CLASS="btn btn-secondary" onclick="location.href='display.cld?ds=$dsID&dim=t'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
END_HTML

  #if aliasing is already turned on, give analyst option to turn it off
  if (length($timeAlias) > 0)
  {
    $applyBtnText = "Update Aliases";
    print <<END_HTML;
            <BUTTON TYPE="button" CLASS="btn btn-danger" onclick="location.href='aliasTimesDo.cld?ds=$dsID&undo=1'"><I CLASS="bi bi-trash"></I> Clear Aliases</BUTTON>
END_HTML
  }
  else
  {
    $applyBtnText = "Create Aliases";
  }

  print <<END_HTML;

            <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-submit"><I CLASS="bi bi-pencil-square"></I> $applyBtnText</BUTTON>
          </DIV>

        </DIV>
      </DIV>

      </FORM>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->

END_HTML

  print_html_footer();

#EOF
