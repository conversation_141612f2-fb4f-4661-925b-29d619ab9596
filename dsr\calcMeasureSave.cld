#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRMeasures;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  #output Content-type header
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  #output HTML
  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: $action Calculated Measure</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">$action Calculated Measure $measName</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');
  $dim = $q->param('dim');
  $measName = $q->param('measName');
  $measType = $q->param('measType');
  $calcBeforeAgg = $q->param('calcBeforeAgg');
  $measureID = $q->param('measureID');
  $measure = $q->param('measure');
  $measure1 = $q->param('measure1');
  $measure2 = $q->param('measure2');
  $convertPct = $q->param('convertPct');
  $constant = $q->param('constant');
  $structType = $q->param('struct');
  $itemID = $q->param('itemID');
  $aggID = $q->param('aggID');
  $segID = $q->param('segID');
  $hierID = $q->param('hierID');
  $hierLevel = $q->param('hierLevel');
  $timeDirection = $q->param('timeDirection');
  $formula = $q->param('formula');
  @measures = $q->param('multimeasures');
  $useCond = $q->param('useCond');
  $conditional = $q->param('conditional');

  #sanitize parameters
  $dsID = utils_sanitize_integer($dsID);
  if (!defined($dsID))
  {
    exit_early_error($session, "Invalid data source");
  }
  $measureID = utils_sanitize_integer($measureID);
  $measName = utils_sanitize_string($measName);

  if (defined($convertPct))
  {
    $convertPct = 1;
  }
  else
  {
    $convertPct = 0;
  }
  if (defined($useCond))
  {
    $useCond = 1;
  }
  else
  {
    $useCond = 0;
  }

  if ($structType eq "item")
  {
    $structID = $itemID;
  }
  elsif ($structType eq "agg")
  {
    $structID = $aggID;
  }
  elsif ($structType eq "seg")
  {
    $structID = $segID;
  }
  elsif ($structType eq "hier")
  {
    $structID = $hierID . "_" . $hierLevel;
  }

  if ($measureID > 0)
  {
    $action = "Edit";
  }
  else
  {
    $action = "New";
  }

  if ($calcBeforeAgg < 1)
  {
    $calcBeforeAgg = 0;
  }

  $newMeasure = 0;

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsName = ds_id_to_name($db, $dsID);
  $q_dsName = $db->quote($dsName);

  print_html_header();

  #make sure we have write privs for the data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to modify this data source.");
  }

  $dsSchema = "datasource_" . $dsID;

  #make sure the data source isn't locked by another background process
  $ok = DSRutil_operation_ok($db, $dsID, "ADD-MEASURE");

  if ($ok != 1)
  {
    exit_warning("Another job is currently using this data source - please try again later.")
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-11 col-md-8 col-lg-6 col-xl-5"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Calculated Measures</DIV>
        <DIV CLASS="card-body">

          <P>
END_HTML

  #build up our calculation definition string depending on measure type
  if ($measType eq "sum")
  {
    $measures = join(',', @measures);
    $calculation = "sum|$measures|";
  }
  elsif ($measType eq "difference")
  {
    $calculation = "difference|$measure1|$measure2|";
  }
  elsif ($measType eq "ratio")
  {
    $calculation = "ratio|$measure1|$measure2|$convertPct|";
  }
  elsif ($measType eq "multiplication")
  {
    $measures = join(',', @measures);
    $calculation = "multiplication|$measures|$constant|";
  }
  elsif ($measType eq "pct_change_meas")
  {
    $calculation = "pct_change_meas|$measure1|$measure2|";
  }
  elsif ($measType eq "change")
  {
    $calculation = "change|$measure|1|year|";
  }
  elsif ($measType eq "pct_change")
  {
    $calculation = "pct_change|$measure|1|year|";
  }
  elsif ($measType eq "lag")
  {
    $calculation = "lag|$measure|1|year|";
  }
  elsif ($measType eq "lead")
  {
    $calculation = "lead|$measure|1|year|";
  }
  elsif ($measType eq "mov_avg")
  {
    $calculation = "mov_avg|$measure1|$constant|$timeDirection|";
  }
  elsif ($measType eq "mov_total")
  {
    $calculation = "mov_total|$measure1|$constant|$timeDirection|";
  }
  elsif ($measType eq "ytd")
  {
    $calculation = "ytd|$measure1|";
  }
  elsif ($measType eq "count")
  {
    $calculation = "count|$dim|$useCond|$measure1|$conditional|$constant|";
  }
  elsif ($measType eq "index")
  {
    $calculation = "index|$dim|$measure|$structType|$structID|";
  }
  elsif ($measType eq "share")
  {
    $calculation = "share|$dim|$measure|$structType|$structID|";
  }
  elsif ($measType eq "calc")
  {

    #make sure the formula doesn't end with an operator or .
    if (($formula =~ m/\.$/) || ($formula =~ m/pl$/) ||
        ($formula =~ m/\*$/) || ($formula =~ m/\/$/) ||
        ($formula =~ m/\-$/))
    {
      chop($formula);
    }

    #make sure the formula has matching parantheses
    $openParaCount = () = $formula =~ /\(/g;
    $closedParaCount = () = $formula =~ /\)/g;
    if ($openParaCount > $closedParaCount)
    {
      for ($i = $closedParaCount; $i < $openParaCount; $i++)
      {
        $formula = $formula . ")";
      }
    }
    if ($closedParaCount > $openParaCount)
    {
      for ($i = $openParaCount; $i < $closedParaCount; $i++)
      {
        $formula = "(" . $formula;
      }
    }

    #build up the string describing the calculated measure
    $formula =~ s/pl/\+/g;
    $calculation = "calc|$formula|";
  }

  $q_measName = $db->quote($measName);
  $q_calculation = $db->quote($calculation);

  #if editing existing measure
  if ($measureID > 0)
  {
    $query = "UPDATE $dsSchema.measures \
        SET name=$q_measName, calculation=$q_calculation, calcBeforeAgg=$calcBeforeAgg, lastCalc=NULL \
        WHERE ID=$measureID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);
  }

  #else insert an entry for the new measure in the measures table
  else
  {
    $query = "INSERT INTO $dsSchema.measures (name, calculation, calcBeforeAgg) \
        VALUES ($q_measName, $q_calculation, $calcBeforeAgg)";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    #get the ID of the new measure
    $measureID = $db->{q{mysql_insertid}};
    $newMeasure = 1;
  }

  #split the calculation process off into background process
  $SIG{CHLD} = "IGNORE";
  if ($pid = fork())
  {
    #parent process - we're just going to finish up our display script
  }
  else
  {

    #we're in a new process, so we need a new connection to the database
    $childDB = KAPutil_connect_to_database();

    #let Apache know not to wait on the child process
    close(STDIN);
    close(STDOUT);

    #redirect STDERR to the Koala error log
    close(STDERR);
    open(STDERR, ">>/opt/apache/htdocs/tmp/koala_error.log");

    #set our initial state in the jobs table
    KAPutil_job_store_status($childDB, $userID, $dsID, 0, "ADD-MEASURE",
        "Adding new calculated $measType measure $measName");
    $query = "UPDATE app.jobs SET dsName=$q_dsName WHERE PID=$$ AND dsID=$dsID";
    $status = $childDB->do($query);
    KAPutil_handle_db_err($childDB, $status, $query);

    #add a column for the new measure if needed to the facts table
    if ($newMeasure > 0)
    {
      $colName = "measure_" . $measureID;
      $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
      $status = $childDB->do($query);
      KAPutil_handle_db_err($childDB, $status, $query);
    }

    #do the initial measure calculation for the entire data source
    if ($measType eq "change")
    {
      DSRmeasures_calc_change($childDB, $dsSchema, $measureID, $measure);
    }
    elsif ($measType eq "pct_change")
    {
      DSRmeasures_calc_pct_change($childDB, $dsSchema, $measureID, $measure, 0);
    }
    elsif ($measType eq "lag")
    {
      DSRmeasures_calc_lag($childDB, $dsSchema, $measureID, $measure, 0);
    }
    elsif ($measType eq "lead")
    {
      DSRmeasures_calc_lead($childDB, $dsSchema, $measureID, $measure, 0);
    }
    elsif ($measType eq "mov_total")
    {
      DSRmeasures_calc_mov_total($childDB, $dsSchema, $measureID, $measure1, $constant, $timeDirection, 0);
    }
    elsif ($measType eq "mov_avg")
    {
      DSRmeasures_calc_mov_avg($childDB, $dsSchema, $measureID, $measure1, $constant, $timeDirection, 0);
    }
    elsif ($measType eq "ytd")
    {
      DSRmeasures_calc_ytd($childDB, $dsSchema, $measureID, $measure1);
    }
    elsif ($measType eq "ratio")
    {
      DSRmeasures_calc_ratio($childDB, $dsSchema, $measureID, $measure1, $measure2, $convertPct);
    }
    elsif ($measType eq "multiplication")
    {
      DSRmeasures_calc_multiplication($childDB, $dsSchema, $measureID, $measures, $constant);
    }
    elsif ($measType eq "sum")
    {
      DSRmeasures_calc_sum($childDB, $dsSchema, $measureID, $measures);
    }
    elsif ($measType eq "difference")
    {
      DSRmeasures_calc_difference($childDB, $dsSchema, $measureID, $measure1, $measure2);
    }
    elsif ($measType eq "pct_change_meas")
    {
      DSRmeasures_calc_pct_change_meas($childDB, $dsSchema, $measureID, $measure1, $measure2);
    }
    elsif ($measType eq "count")
    {
      DSRmeasures_calc_count($childDB, $dsSchema, $measureID, $dim, $useCond, $measure1, $conditional, $constant);
    }
    elsif ($measType eq "index")
    {
      DSRmeasures_calc_index($childDB, $dsSchema, $measureID, $dim, $measure, $structType, $structID);
    }
    elsif ($measType eq "index")
    {
      DSRmeasures_calc_index($childDB, $dsSchema, $measureID, $dim, $measure, $structType, $structID);
    }
    elsif ($measType eq "share")
    {
      DSRmeasures_calc_share($childDB, $dsSchema, $measureID, $dim, $measure, $structType, $structID);
    }
    elsif ($measType eq "calc")
    {
      DSRmeasures_calc_calc($childDB, $dsSchema, $measureID, $formula);
    }

    #remove this task from the jobs table
    DSRutil_clear_status($childDB);

    #exit the child process now that we're done calculating the measure
    exit;
  }

  print <<END_HTML;

          The calculated measure $measName has been created.

          <DIV CLASS="text-center">
            <BUTTON TYPE="button" CLASS="btn btn-primary" onClick="location.href='display.cld?ds=$dsID&dim=m&path=$measureID'"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

  $activity = "$first $last created calculated measure $measName ($measType) in $dsName";
  utils_audit($db, $userID, "Created calculated measure $measName ($measType)", $dsID, 0, 0);
  utils_slack($activity);


#EOF
