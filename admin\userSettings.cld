#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName User Settings</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item active">User Settings</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #create the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #connect to user login database
  $db = KAPutil_connect_to_database();

  print_html_header();

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-12 col-lg-11 col-xl-9"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">User Settings</DIV>
        <DIV CLASS="card-body">

          <DIV CLASS="container">
            <DIV CLASS="row">
              <DIV CLASS="col text-center">
                <A HREF="/app/admin/password.cld"><IMG BORDER="0" SRC="/icons/password.png"></A><BR>
                <A CLASS="text-decoration-none" HREF="/app/admin/password.cld">Change Password</A>
              </DIV>
END_HTML

  if ($acctType > 0)
  {
    print <<END_HTML;
              <DIV CLASS="col text-center">
                <A HREF="/app/admin/manageDS.cld"><IMG BORDER="0" SRC="/icons/dsmanagement.png"></A><BR>
                <A CLASS="text-decoration-none" HREF="/app/admin/manageDS.cld">Data Source Management</A>
              </DIV>

              <DIV CLASS="col text-center">
                <A HREF="/app/admin/userMgmt.cld"><IMG BORDER="0" SRC="/icons/manage_users.png"></A><BR>
                <A CLASS="text-decoration-none" HREF="/app/admin/userMgmt.cld">Viewer Management</A>
              </DIV>
END_HTML
  }

  print <<END_HTML;
            </DIV>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
