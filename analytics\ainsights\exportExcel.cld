#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;
use Excel::Writer::XLSX;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::AInsights::AInsights;
use Lib::AInsights::Utils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Export Elasticities to Excel</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">AInsights</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="insights.cld?pm=$priceModelID">$modelName</A></LI>
    <LI CLASS="breadcrumb-item active">Export Elasticities to Excel</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $priceModelID = $q->param('pm');
  $dsID = $q->param('ds');

  AInsights_Utils_initialize_constants($priceModelID);

  #connect to the database
  $db = KAPutil_connect_to_database();

  #make sure we have read privs for this model
  $privs = AInsights_rights($db, $userID, $priceModelID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to view this model.");
  }

  #get the name of the pricing model
  $dsSchema = "datasource_" . $dsID;
  $modelName = AInsights_ID_to_name($db, $priceModelID);
  $dsName = ds_id_to_name($db, $dsID);

  %prodNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "p");
  %geoNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "g");

  print_html_header();

  #build up the Excel workbook file name
  $tmpDSName = substr($dsName, 0, 32);
  $tmpElasticName = substr($modelName, 0, 32);
  $filename = "Koala Elasticity $tmpDSName" . " - " . $tmpElasticName . "_" . $userID . "_" . "$dsID.xlsx";

  #get rid of special characters we don't want in the file name
  $filename =~ s/\s+/ /g;
  $filename =~ s/\\//g;
  $filename =~ s/\///g;
  $filename =~ s/\*//g;
  $filename =~ s/\$//g;
  $filename =~ s/\&//g;
  $filename =~ s/\|//g;
  $filename =~ s/\?//g;
  $filename =~ s/\://g;
  $filename =~ s/\"//g;
  $filename =~ s/\'//g;
  $filename =~ s/\%//g;

  #make sure our sheet name won't be too long or have invalid characters
  $sheetName = substr($modelName, 0, 29);
  $sheetName =~ s/\\/ /g;
  $sheetName =~ s/\// /g;

  #create a new Excel workbook
  $workbook = Excel::Writer::XLSX->new("/opt/apache/htdocs/tmp/$filename");

  $workbook->set_optimization();

  $worksheet = $workbook->add_worksheet($sheetName);

  $formatHeader = $workbook->add_format();
  $formatHeader->set_bold();

  $formatValues = $workbook->add_format();
  $formatElasticity1 = $workbook->add_format();
  $formatElasticity2 = $workbook->add_format();
  $formatElasticity3 = $workbook->add_format();
  $formatElasticity4 = $workbook->add_format();
  $formatElasticity5 = $workbook->add_format();

  $formatValues->set_num_format('0.00');
  $formatElasticity1->set_num_format('0.00');
  $formatElasticity2->set_num_format('0.00');
  $formatElasticity3->set_num_format('0.00');
  $formatElasticity4->set_num_format('0.00');
  $formatElasticity5->set_num_format('0.00');

  $formatElasticity1->set_bg_color('#00AEEF');
  $formatElasticity2->set_bg_color('#8DC63F');
  $formatElasticity3->set_bg_color('#FFB100');
  $formatElasticity4->set_bg_color('#B21DAC');
  $formatElasticity5->set_bg_color('#DC0015');

  $formatElasticity1->set_color('white');
  $formatElasticity2->set_color('white');
  $formatElasticity3->set_color('white');
  $formatElasticity4->set_color('white');
  $formatElasticity5->set_color('white');

  #grab info about the analysis we're going to need for display
  $query = "SELECT geographies FROM analytics.pricing WHERE ID=$priceModelID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($geoIDStr) = $dbOutput->fetchrow_array;
  @geographies = split(',', $geoIDStr);

  #get an alpha-sorted list of products in the data source
  $query = "SELECT ID FROM $dsSchema.products ORDER BY name";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  while (($prodID) = $dbOutput->fetchrow_array)
  {
    $prodIDStr .= "$prodID,";
  }
  chop($prodIDStr);

  $query = "SELECT DISTINCT productID FROM $dsSchema.$AInsightsItemTable \
      WHERE productID NOT LIKE 'SHS_%' ORDER BY FIELD (productID, $prodIDStr)";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  $orderedProdIDStr = "";
  while (($prodID) = $dbOutput->fetchrow_array)
  {
    push(@products, $prodID);
    $orderedProdIDStr .= "$prodID,";
  }
  chop($orderedProdIDStr);

  #write out column headers (geographies)
  $colIdx = 1;
  foreach $geoID (@geographies)
  {
    $worksheet->write(0, $colIdx, $geoNameHash{$geoID}, $formatHeader);
    $colIdx++;
  }

  #write out product names and elasticity values
  $rowIdx = 0;

  $curProdID = 0;
  $query = "SELECT elasticity, modelConfidence, productID, geographyID, status \
      FROM $dsSchema.$AInsightsItemTable \
      ORDER BY FIELD(productID, $orderedProdIDStr), FIELD(geographyID, $geoIDStr)";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  while (($elasticity, $modelConfidence, $prodID, $geoID, $statusText) = $dbOutput->fetchrow_array)
  {
    if ($curProdID != $prodID)
    {
      $curProdID = $prodID;
      $colIdx = 1;
      $rowIdx++;
      $worksheet->write($rowIdx, 0, $prodNameHash{$prodID});
    }

    #don't show models that have a lower confidence than the user trusts
    if ($modelConfidence < $filterConfidence)
    {
      undef($elasticity);
    }

    #decide how to color background of cell
    if (!defined($elasticity))
    {
      $cellFormat = $formatValues;
    }
    elsif (($elasticity <= 0) && ($elasticity >= -0.75))
    {
      $cellFormat = $formatElasticity1;
    }
    elsif (($elasticity <= -0.75) && ($elasticity >= -1.25))
    {
      $cellFormat = $formatElasticity2;
    }
    elsif (($elasticity <= -1.25) && ($elasticity >= -1.75))
    {
      $cellFormat = $formatElasticity3;
    }
    elsif (($elasticity <= -1.75) && ($elasticity >= -2.25))
    {
      $cellFormat = $formatElasticity4;
    }
    elsif ($elasticity <= -2.25)
    {
      $cellFormat = $formatElasticity5;
    }

    $worksheet->write($rowIdx, $colIdx, $elasticity, $cellFormat);

    $colIdx++;
  }

  $workbook->close();

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-11 col-lg-8 col-xl-7"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Download to Excel</DIV>
        <DIV CLASS="card-body">

          <P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-success" TYPE="button" ID="download-btn" onClick="location.href='/tmp/$filename'"><I CLASS="bi bi-download"></I> Download Excel Workbook</BUTTON>

            <P>&nbsp;</P>
            <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='elasticity.cld?pm=$priceModelID'"><I CLASS="bi bi-check-lg"></I> Done</BUTTON>
          </DIV>

        </DIV>
      </DIV>

      </FORM>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

  AInsights_audit($db, $userID, $priceModelID, "Exported elasticities to Excel");
  $activity = "AINSIGHTS: $first $last exported elasticities in $modelName to Excel";
  utils_slack($activity);

#EOF
