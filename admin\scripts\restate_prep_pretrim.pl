#!/usr/bin/perl

#
# Reads in /opt/apache/app/Lib/PrepClientTrim.pm, updates any items in the
# mapping hash, and writes out a PrepClientTrim.new file for review
#

use lib "/opt/apache/app/";


%restateHash = (
"Albany Food" => "Albany/Schenectady/Troy SMM Food",
"Atlanta Conv" => "Atlanta SMM Conv",
"Atlanta Drug" => "Atlanta SMM Drug",
"Atlanta Food" => "Atlanta SMM Food",
"Atlanta xAOC" => "Atlanta SMM xAOC",
"Baltimore Food" => "Baltimore SMM Food",
"Birmingham Food" => "Birmingham/Anniston/Tuscaloosa SMM Food",
"Birmingham xAOC" => "Birmingham/Anniston/Tuscaloosa SMM xAOC",
"Boston Conv" => "Boston/Manchester SMM Conv",
"Boston Drug" => "Boston/Manchester SMM Drug",
"Boston Food" => "Boston/Manchester SMM Food",
"Boston xAOC" => "Boston/Manchester SMM xAOC",
"Buffalo Rochester Food" => "Buffalo SMM Food",
"Buffalo Rochester xAOC" => "Buffalo SMM xAOC",
"Charlotte Food" => "Charlotte SMM Food",
"Charlotte xAOC" => "Charlotte SMM xAOC",
"Chicago Conv" => "Chicago SMM Conv",
"Chicago Food" => "Chicago SMM Food",
"Chicago xAOC" => "Chicago SMM xAOC",
"Cincinnati Conv" => "Cincinnati SMM Conv",
"Cincinnati xAOC" => "Cincinnati SMM xAOC",
"Cleveland Conv" => "Cleveland/Akron/Canton SMM Conv",
"Cleveland Food" => "Cleveland/Akron/Canton SMM Food",
"Cleveland xAOC" => "Cleveland/Akron/Canton SMM xAOC",
"Columbus xAOC" => "Columbus OH SMM Food",
"Dallas/Ft. Worth Conv" => "Dallas/Ft. Worth SMM Conv",
"Dallas/Ft. Worth Food" => "Dallas/Ft. Worth SMM Food",
"Dallas/Ft. Worth xAOC" => "Dallas/Ft. Worth SMM xAOC",
"Denver Conv" => "Denver SMM Conv",
"Denver xAOC" => "Denver SMM xAOC",
"Des Moines Food" => "Des Moines/Ames SMM Food",
"Detroit Drug" => "Detroit SMM Drug",
"Detroit Food" => "Detroit SMM Food",
"Detroit xAOC" => "Detroit SMM xAOC",
"Grand Rapids Food" => "Grand Rapids SMM Food",
"Greenville Food" => "Greenville/Spartanburg SMM xAOC",
"Wilkes Barre/Scranton Food" => "Harrisburg/Lancaster SMM Food",
"Hartford New Haven Food" => "Hartford/New Haven SMM Food",
"Hartford New Haven xAOC" => "Hartford/New Haven SMM xAOC",
"Houston Conv" => "Houston SMM Conv",
"Houston Drug" => "Houston SMM Drug",
"Houston Food" => "Houston SMM Food",
"Houston xAOC" => "Houston SMM xAOC",
"Indianapolis Food" => "Indianapolis SMM Food",
"Indianapolis xAOC" => "Indianapolis SMM xAOC",
"Jacksonville Food" => "Jacksonville SMM Food",
"Kansas City Food" => "Kansas City SMM Food",
"Kansas City xAOC" => "Kansas City SMM xAOC",
"Las Vegas Food" => "Las Vegas SMM Food",
"Las Vegas xAOC" => "Las Vegas SMM xAOC",
"Little Rock Food" => "Little Rock/Pine Bluff SMM Food",
"Los Angeles Conv" => "Los Angeles SMM Conv",
"Los Angeles Drug" => "Los Angeles SMM Drug",
"Los Angeles Food" => "Los Angeles SMM Food",
"Los Angeles xAOC" => "Los Angeles SMM xAOC",
"Louisville xAOC" => "Louisville SMM xAOC",
"Memphis Food" => "Memphis SMM xAOC",
"Miami Conv" => "Miami/Ft. Lauderdale SMM Conv",
"Miami Food" => "Miami/West Palm Beach SMM Food",
"Miami xAOC" => "Miami/West Palm Beach SMM xAOC",
"Milwaukee Food" => "Milwaukee SMM Food",
"Milwaukee xAOC" => "Milwaukee SMM xAOC",
"Minneapolis Food" => "Minneapolis/St. Paul SMM Food",
"Minneapolis xAOC" => "Minneapolis/St. Paul SMM xAOC",
"Nashville Conv" => "Nashville SMM Conv",
"Nashville Food" => "Nashville SMM Food",
"Nashville xAOC" => "Nashville SMM xAOC",
"New Orleans Mobile Food" => "New Orleans SMM Food",
"New Orleans Mobile xAOC" => "New Orleans SMM xAOC",
"New York Conv" => "New York SMM Conv",
"New York Drug" => "New York SMM Drug",
"New York Food" => "New York SMM Food",
"New York xAOC" => "New York SMM xAOC",
"Oahu Food" => "Oahu Food",
"Oklahoma City Tulsa Food" => "Oklahoma City SMM Food",
"Omaha Food" => "Omaha SMM Food",
"Orlando Conv" => "Orlando/Daytona Beach/Melbourne SMM Conv",
"Orlando Food" => "Orlando/Daytona Beach/Melbourne SMM Food",
"Philadelphia Conv" => "Philadelphia SMM Conv",
"Philadelphia Drug" => "Philadelphia SMM Drug",
"Philadelphia Food" => "Philadelphia SMM Food",
"Philadelphia xAOC" => "Philadelphia SMM xAOC",
"Phoenix Conv" => "Phoenix/Prescott SMM Conv",
"Phoenix Food" => "Phoenix/Prescott SMM Food",
"Phoenix xAOC" => "Phoenix/Prescott SMM xAOC",
"Pittsburgh Drug" => "Pittsburgh SMM Drug",
"Pittsburgh Food" => "Pittsburgh SMM Food",
"Pittsburgh xAOC" => "Pittsburgh SMM xAOC",
"Portland Food" => "Portland OR SMM Food",
"Raleigh Durham Food" => "Raleigh/Durham/Fayetteville SMM Food",
"Raleigh Durham xAOC" => "Raleigh/Durham/Fayetteville SMM xAOC",
"Greater Maine Food" => "Rem US New England SMM Food",
"Richmond Norfolk Food" => "Richmond/Petersburg SMM Food",
"Sacramento Food" => "Sacramento/Stockton/Modesto SMM Food",
"Sacramento xAOC" => "Sacramento/Stockton/Modesto SMM xAOC",
"Salt Lake City Boise Food" => "Salt Lake City SMM Food",
"Salt Lake City Boise xAOC" => "Salt Lake City SMM xAOC",
"San Diego Food" => "San Diego SMM Food",
"San Francisco Conv" => "San Francisco/Oakland/San Jose SMM Conv",
"San Francisco Drug" => "San Francisco/Oakland/San Jose SMM Drug",
"San Francisco Food" => "San Francisco/Oakland/San Jose SMM Food",
"San Francisco xAOC" => "San Francisco/Oakland/San Jose SMM xAOC",
"Seattle Food" => "Seattle/Tacoma SMM Food",
"Seattle xAOC" => "Seattle/Tacoma SMM xAOC",
"St Louis Food" => "St. Louis SMM Food",
"St Louis xAOC" => "St. Louis SMM xAOC",
"Tampa Food" => "Tampa/Ft. Myers SMM Food",
"Tampa xAOC" => "Tampa/Ft. Myers SMM xAOC",
"Tampa Conv" => "Tampa/St. Petersburg/Sarasota SMM Conv",
"Washington DC Drug" => "Washington DC/Hagerstown SMM Drug",
"Washington DC Food" => "Washington DC/Hagerstown SMM Food",
"Washington DC xAOC" => "Washington DC/Hagerstown SMM xAOC",
"Giant Martins Total Rem" => "The Giant Company Total Rem",
"Giant Martins Total TA" => "The Giant Company Total TA",
"Giant Martins Total XAOC Rem" => "The Giant Company Total xAOC Rem",
"ALBSCO All Oth Denver Div TA" => "ALBSCO Denver All Other TA",
"ALBSCO Inland Empire TA" => "ALBSCO Inland Emp & Orange County TA",
"ALBSCO Inland Empire Rem" => "ALBSCO Inland Emp & Orange County Rem",
"ALBSCO All Oth Nor Cal Div TA" => "ALBSCO Nor Cal All Other TA",
"ALBSCO All Oth Nor Cal Div Rem" => "ALBSCO Nor Cal All Other Rem",
"ALBSCO All Oth Eastern Div TA" => "ALBSCO Eastern All Other TA",
"ALBSCO All Oth Eastern Div Rem" => "ALBSCO Eastern All Other Rem",
"ALBSCO All Oth Portland Div TA" => "ALBSCO Portland All Other TA",
"ALBSCO All Oth Portland Div Rem" => "ALBSCO Portland All Other Rem",
"ALBSCO All Oth Portland Div xAOC Rem" => "ALBSCO Portland All Other xAOC Rem",
"AWG Oklahoma City TA" => "AWG OK City/Dallas/Ft.Worth TA",
"AWG Oklahoma City Rem" => "AWG OK City/Dallas/Ft.Worth Rem",
"AWG Oklahoma City xAOC Rem" => "AWG OK City/Dallas/Ft.Worth xAOC Rem"
);

  chdir("/opt/apache/app/Lib/");

  #open the Perl module containing the pre-trim hashes
  open(INPUT, "PrepClientTrim.pm");

  #open the file we're writing out the new hashes to
  open(OUTPUT, ">PrepClientTrim.new");

  while ($line = <INPUT>)
  {

    #if the line looks like a hash key
    if ($line =~ m/^'(.*)' => 1(.)/)
    {
      $oldGeo = $1;
      $comma = $2;
      $oldGeo =~ s/\\//g;

      #replace the hash key, if there's a matching mapping
      if (length($restateHash{$oldGeo}) > 1)
      {
        $newGeo = $restateHash{$oldGeo};
      }
      else
      {
        $newGeo = $oldGeo;
      }

      #escape apostrophe characters
      $newGeo =~ s/\'/\\'/g;

      #assemble new hash key line
      $line = "'$newGeo' => 1";
      $line .= $comma . "\n";
    }

    print OUTPUT $line;
  }

#EOF
