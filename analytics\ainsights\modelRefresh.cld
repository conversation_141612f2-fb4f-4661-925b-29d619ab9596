#!/usr/bin/perl

use lib "/opt/apache/app/";


use CGI qw(:standard);
use CGI::Session;
use DBI;
use Text::CSV;

use Lib::DataSources;
use Lib::DSRUtils;
use Lib::KoalaConfig;
use Lib::AInsights::AInsights;
use Lib::AInsights::Utils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: AInsights Model Job Progress</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
let statusTimer = setInterval(function(){displayStatus()}, 1000);
let statCount = 0;

function displayStatus()
{
  let url = 'xhrRefreshStatus.cld?a=$priceModelID&ds=$dsID';

  \$.get(url, function(data, status)
  {
    let statusText = data;

    if (statusText.length < 5)
    {
      statusText = '0% Loading Data';
    }

    if (statusText.search('DONE') == 0)
    {
      \$('#progress-bar').css('width', '100%');
      document.getElementById('progress-bar').innerHTML = '100%';
      \$('#progressDiv').hide();
      \$('#detailedDiv').hide();
      clearInterval(statusTimer);
      location.href='overview.cld?pm=$priceModelID&ds=$dsID';
    }
    else
    {
      statElements = statusText.split('|');

      pct = statElements[0];
      statusText = statElements[1];

      if (statElements.length == 1)
      {
        pct = 0;
        statusText = statElements[0];
      }

      if (pct > 5)
      {
        \$('#progress-bar').css('width', pct+'%');
      }

      document.getElementById('progress-bar').innerHTML = pct + '%';
      document.getElementById('progressDiv').innerHTML = statusText;
    }

    if (statCount == 5)
    {
      clearInterval(statusTimer);
      statusTimer = setInterval(function(){displayStatus()}, 5000);
    }
    statCount++;
  });
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">AInsights</A></LI>
    <LI CLASS="breadcrumb-item">$modelName</LI>
    <LI CLASS="breadcrumb-item active">$actionHR Model</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------
#
# Output the HTML for the web page that displays the status dialog
#

sub print_status_html
{
  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-11 col-md-8 col-lg-6 col-xl-5"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">$actionHR AInsights Model</DIV>
        <DIV CLASS="card-body">

          <DIV CLASS="progress">
            <DIV ID="progress-bar" CLASS="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" STYLE="width:5%;">
              0%
            </DIV>
          </DIV>

          <P>
          <DIV CLASS="text-center">
            <DIV ID="progressDiv">Refreshing Model</DIV>
          </DIV>

          <P>&nbsp;</P>
          Koala can continue working in the background while you work on other things.

          <P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" ID="btnAsync" onClick="location.href='/app/analytics/ainsights/main.cld'"><I CLASS="bi bi-speedometer"></I> Be More Productive</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get the CGI input variables
  $priceModelID = $q->param('pm');
  $action = $q->param('a');
  $dsID = $q->param('ds');
  $name = $q->param('name');
  $desc = $q->param('desc');
  $ppgHierID = $q->param('p');
  $geographyIDstr = $q->param('g');
  $brandSegID = $q->param('bseg');
  $ownBrandID = $q->param('ob');
  $avgPriceMeasID = $q->param('avgPriceID');
  $unitSalesMeasID = $q->param('unitSalesID');
  $distMeasID = $q->param('distID');

  $comp1ID = $q->param('comp1');
  $comp2ID = $q->param('comp2');
  $updateAllComps = $q->param('comp-all');
  $compGeoID = $q->param('compGeo');

  @productIDs = split(' ', $productIDstr);
  @geographyIDs = split(' ', $geographyIDstr);

  #connect to database
  $db = KAPutil_connect_to_database();

  #validate the parameters we were passed
  if ($dsID < 1)
  {
    $dsID = AInsights_get_dsID($db, $priceModelID);
  }

  $dsName = ds_id_to_name($db, $dsID);
  if (length($name) < 1)
  {
    $modelName = AInsights_ID_to_name($db, $priceModelID);
  }
  else
  {
    $modelName = $name;
  }

  #make sure we have write privs for this pricing model
  $privs = AInsights_rights($db, $userID, $priceModelID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this model.");
  }

  AInsights_Utils_initialize_constants($priceModelID);

  #if we're rejoining an existing model build job
  if ($priceModelID > 0)
  {
    $query = "SELECT PID FROM app.jobs \
        WHERE analyticsID=$priceModelID AND operation='ANALYTICS-PRICE'";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($pid) = $dbOutput->fetchrow_array;
    if ($pid > 0)
    {
      $actionHR = "Updating";
      print_html_header();
      print_status_html();
      exit;
    }
  }

  #if we're editing an existing model, store any potentially updated info
  if (($priceModelID > 0) && ($action eq "e"))
  {
    #store the model info and get our ID
    $q_name = $db->quote($name);
    $q_desc = $db->quote($desc);
    $prodIDStr = join(',', @productIDs);
    $geoIDStr = join(',', @geographyIDs);
    $query = "UPDATE analytics.pricing \
        SET name=$q_name, description=$q_desc, ppgHierID='$ppgHierID', geographies='$geoIDStr', brandSegID=$brandSegID, ownBrandID=$ownBrandID, priceMeasureID=$avgPriceMeasID, unitMeasureID=$unitSalesMeasID, distMeasureID=$distMeasID \
        WHERE ID=$priceModelID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);
  }

  #if we're updating the competitors for an existing model
  if (($priceModelID > 0) && ($action eq "c"))
  {
    $dsSchema = "datasource_" . $dsID;

    #if we're resetting one or both of the competitors to be auto-detected
    if (($comp1ID eq "auto") || ($comp2ID eq "auto"))
    {
      $query = "UPDATE $dsSchema.$AInsightsCompTable SET source='auto' \
          WHERE level='brand'";
      if ($updateAllComps ne "on")
      {
        $query .= " AND geographyID=$compGeoID";
      }
      $status = $db->do($query);
      KAPutil_handle_db_err($db, $status, $query);
    }

    #get current competitors, and see if the analyst requested we change them
    ($curCompID1, $curCompID2) = AInsights_Utils_get_brand_comp_ids($db, $dsSchema, $compGeoID);

    if (($comp1ID > 0) && ($comp1ID != $curCompID1))
    {
      $query = "UPDATE $dsSchema.$AInsightsCompTable SET compID1=$comp1ID, source='user' \
          WHERE level='brand'";
      if ($updateAllComps ne "on")
      {
        $query .= " AND geographyID=$compGeoID";
      }
      $status = $db->do($query);
      KAPutil_handle_db_err($db, $status, $query);
    }

    if (($comp2ID > 0) && ($comp2ID != $curCompID2))
    {
      $query = "UPDATE $dsSchema.$AInsightsCompTable SET compID2=$comp2ID, source='user' \
          WHERE level='brand'";
      if ($updateAllComps ne "on")
      {
        $query .= " AND geographyID=$compGeoID";
      }
      $status = $db->do($query);
      KAPutil_handle_db_err($db, $status, $query);
    }
  }


  #if we weren't passed the ID of an existing pricing model, we need to
  #create a new one
  if ($priceModelID < 1)
  {
    $actionHR = "Creating";

    #store the model info and get our ID
    $q_name = $db->quote($name);
    $q_desc = $db->quote($desc);
    $prodIDStr = join(',', @productIDs);
    $geoIDStr = join(',', @geographyIDs);
    $query = "INSERT INTO analytics.pricing \
        (userID, name, description, dsID, ppgHierID, brandSegID, ownBrandID, geographies, priceMeasureID, unitMeasureID, distMeasureID) \
        VALUES ($userID, $q_name, $q_desc, $dsID, '$ppgHierID', $brandSegID, $ownBrandID, '$geoIDStr', $avgPriceMeasID, $unitSalesMeasID, $distMeasID)";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);
    $priceModelID = $db->{q{mysql_insertid}};

    $activity = "PRICING: $first $last created new pricing model $modelName in $dsName";
    utils_slack($activity);
  }
  else
  {
    $actionHR = "Refreshing";

    $activity = "AINSIGHTS: $first $last refreshed model $modelName in $dsName";
    utils_slack($activity);
  }

  print_html_header();

  $SIG{CHLD} = "IGNORE";
  if ($pid = fork)
  {
    #parent process - we're just going to finish up our display script

    print_status_html();
  }

  else
  {
    #child process - do the actual model creation/refresh

    #let Apache know not to wait on the child process
    close(STDIN);
    close(STDOUT);

    #redirect STDERR to the Koala error log
    close(STDERR);
    open(STDERR, ">>/opt/apache/htdocs/tmp/koala_error.log");
    select(STDERR);
    $| = 1;

    #we're in a new process, so we need a new connection to the database
    $db = KAPutil_connect_to_database();

    AInsights_build_model($db, $dsID, $priceModelID, $userID);
  }

#EOF
