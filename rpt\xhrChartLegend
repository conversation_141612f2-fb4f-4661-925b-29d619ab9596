#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::Reports;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $rptID = $q->param('rptID');
  $visID = $q->param('v');
  $showLegend = $q->param('showLegend');
  $legendItemFontColor = $q->param('legendItemFontColor');
  $legendPosition = $q->param('legendPosition');
  $legendItemFont = $q->param('legendItemFont');
  $legendItemFontSize = $q->param('legendItemFontSize');

  #fix up the CGI parameters from the submitted form
  if (defined($showLegend))
  {
    $showLegend = ($showLegend eq "false") ? "0" : "1";
  }

  $legendItemFontColor = "#" . $legendItemFontColor;

  $db = KAPutil_connect_to_database();

  #make sure we have edit privs for this report
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this report.");
  }

  #get the chart title details from the database
  $query = "SELECT design FROM visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($graphDesign) = $dbOutput->fetchrow_array;


  ########################################################################
  #
  # This code block is called on submit to save the changes
  #

  #if we're being called to save updated chart titles
  if (defined($showLegend))
  {
    if ($showLegend ne reports_chart_design_default("showLegend"))
    {
      $graphDesign = reports_set_style($graphDesign, "showLegend", $showLegend);
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "showLegend");
    }

    if (lc($legendItemFontColor) ne reports_chart_design_default("legendItemFontColor"))
    {
      $graphDesign = reports_set_style($graphDesign, "legendItemFontColor", $legendItemFontColor);
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "legendItemFontColor");
    }

    if ($legendPosition ne reports_chart_design_default("legendPosition"))
    {
      $graphDesign = reports_set_style($graphDesign, "legendPosition", $legendPosition);
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "legendPosition");
    }

    if ($legendItemFont ne reports_chart_design_default("legendItemFont"))
    {
      $graphDesign = reports_set_style($graphDesign, "legendItemFont", $legendItemFont);
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "legendItemFont");
    }

    if ($legendItemFontSize ne reports_chart_design_default("legendItemFontSize"))
    {
      $graphDesign = reports_set_style($graphDesign, "legendItemFontSize", $legendItemFontSize);
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "legendItemFontSize");
    }

    $q_graphDesign = $db->quote($graphDesign);
    $query = "UPDATE visuals SET design = $q_graphDesign WHERE ID=$visID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    $cubeName = cube_id_to_name($db, $rptID);
    $dsID = cube_get_ds_id($db, $rptID);
    $dsName = ds_id_to_name($db, $dsID);

    utils_audit($db, $userID, "Changed chart legend options", $dsID, $rptID, 0);
    $activity = "$first $last changed chart legend options for $cubeName in $dsName";
    utils_slack($activity);

    exit;
  }


  #########################################################################
  #
  # Everything after this point is called to display the chart layout dialog
  #

  #extract graph captions from design string
  $showLegend = reports_get_style($graphDesign, "showLegend");
  $legendItemFontColor = reports_get_style($graphDesign, "legendItemFontColor");
  $legendPosition = reports_get_style($graphDesign, "legendPosition");
  $legendItemFont = reports_get_style($graphDesign, "legendItemFont");
  $legendItemFontSize = reports_get_style($graphDesign, "legendItemFontSize");

  #set defaults
  if (!(defined($showLegend)))
  {
    $showLegend = "1";
  }
  if (length($legendItemFontColor) < 7)
  {
    $legendItemFontColor = "#333333";
  }
  if (!(defined($legendPosition)))
  {
    $legendPosition = "bottom";
  }
  if (length($legendItemFont) < 3)
  {
    $legendItemFont = "Helvetica";
  }
  if ($legendItemFontSize < 3)
  {
    $legendItemFontSize = "10";
  }
  $showLegend = ($showLegend eq "1") ? "CHECKED" : "";

  print <<END_HTML;
<SCRIPT>
function submitForm()
{
  let showLegend = \$("#showLegend").prop("checked");
  let legendItemFontColor = document.getElementById('legendItemFontColor').value;
  let legendPosition = document.getElementById('legendPosition').value;
  let legendItemFont = document.getElementById('legendItemFont').value;
  let legendItemFontSize = document.getElementById('legendItemFontSize').value;

  //knock # off of color strings
  legendItemFontColor = legendItemFontColor.substr(1);

  let url = "xhrChartLegend?rptID=$rptID&v=$visID&showLegend=" + showLegend  +
      "&legendItemFontColor=" + legendItemFontColor +
      "&legendPosition=" + legendPosition + "&legendItemFont=" + legendItemFont +
      "&legendItemFontSize=" + legendItemFontSize;

  \$.get(url, function(data, status)
  {
    location.href = "display.cld?rpt=$rptID&v=$visID";
  });
}



function revertDefaults()
{
  document.getElementById('showLegend').checked = true;
  document.getElementById('legendItemFontColor').value = "#333333";
  document.getElementById('legendPosition').value = "bottom";
  document.getElementById('legendItemFontSize').value = 10;
  document.getElementById('legendItemFont').value = "Helvetica";
}
</SCRIPT>

<DIV CLASS="modal-dialog" STYLE="overflow-y: initial !important;">
  <DIV CLASS="modal-content">
    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Legend</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">
      <FORM>
      <TABLE CLASS="mx-auto">
        <TR>
          <TD STYLE="text-align:right;">
            Show a legend for the chart:&nbsp;
          </TD>
          <TD>
            <DIV CLASS="form-check form-switch">
              <INPUT TYPE="checkbox" CLASS="form-check-input" NAME="showLegend" ID="showLegend" data-offstyle="secondary" $showLegend>
              <LABEL CLASS="form-check-label" FOR="showLegend">&nbsp;</LABEL>
            </DIV>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Position:&nbsp;
          </TD>
          <TD>
            <SELECT CLASS="form-select" NAME="legendPosition" ID="legendPosition">
              <OPTION VALUE="bottom">Bottom</OPTION>
              <OPTION VALUE="right">Right</OPTION>
            </SELECT>
            <SCRIPT>
              \$("select#legendPosition").val("$legendPosition");
            </SCRIPT>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Color:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="color" NAME="legendItemFontColor" ID="legendItemFontColor" VALUE="$legendItemFontColor" STYLE="width:3em;"></DIV>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Font family:&nbsp;
          </TD>
          <TD>
            <SELECT CLASS="form-select" NAME="legendItemFont" ID="legendItemFont">
              <OPTION VALUE="Arial">Arial</OPTION>
              <OPTION VALUE="Arial Black">Arial Black</OPTION>
              <OPTION VALUE="Comic Sans MS">Comic Sans MS</OPTION>
              <OPTION VALUE="Courier New">Courier New</OPTION>
              <OPTION VALUE="Helvetica">Helvetica</OPTION>
              <OPTION VALUE="Impact">Impact</OPTION>
              <OPTION VALUE="Tahoma">Tahoma</OPTION>
              <OPTION VALUE="Times New Roman">Times New Roman</OPTION>
              <OPTION VALUE="Verdana">Verdana</OPTION>
            </SELECT>
            <SCRIPT>
              \$("select#legendItemFont").val("$legendItemFont");
            </SCRIPT>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Text Size:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="number" NAME="legendItemFontSize" ID="legendItemFontSize" STYLE="width:5em;" VALUE="$legendItemFontSize" required>
          </TD>
        </TR>

        <TR>
          <TD COLSPAN="2" STYLE="text-align:center;">
            <P></P>
            <A CLASS="text-decoration-none" HREF="#" onClick="revertDefaults()">Revert to default</A>
          </TD>
        </TR>
      </TABLE>
      </FORM>

    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="submitForm()"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML

#EOF
