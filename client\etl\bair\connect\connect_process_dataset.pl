#!/usr/bin/perl


use Text::CSV_XS;

#
# Global variables controlling how large we're allowed to scale
#

$TELEMETRY = 1;

#NB: Starting point should be number of vCPUs in cloud
$MAXPROCS = 8;



#---------------------------------------------------------------------------
#
# Output log info to console and log file
#

sub DBG
{
  my ($date);

  my ($str) = @_;

  if ($TELEMETRY > 0)
  {
    $date = localtime();
    print STDERR "$date: $str\n";
  }
}



#---------------------------------------------------------------------------
#
# Determine number of currently running jobs, based on count of work files
#

sub count_running_jobs
{
  my ($procCount);


  $procCount = 0;
  opendir(DIRHANDLE, "/data2/work-connect");
  while (defined($filename = readdir(DIRHANDLE)))
  {
    if ($filename =~ m/(\d+)\.work$/i)
    {
      $procCount++;
    }
  }

  return($procCount);
}



#---------------------------------------------------------------------------
#
# Implement a retention period by clearing any data files that are over
# 60 days old.
#

sub clear_old_data
{
  opendir(DIRHANDLE, "/data2/bair");
  while (defined($filename = readdir(DIRHANDLE)))
  {
    if ($filename =~ m/.*\.zip$/i)
    {
      $fileEpochTime = (stat("/data2/bair/$filename"))[9];
      $timeDiff = time() - $fileEpochTime;
    }
  }

  #if the zip file is over 60 days, remove it
  $timeDiff = $timeDiff / 3600; #hours
  $timeDiff = $timeDiff / 24; #days
  if ($timeDiff > 60)
  {
    unlink("/data2/bair/$filename");
    DBG("Cleared stale data file $filename");
  }
}



#---------------------------------------------------------------------------
#
# Determine if the upload for the specified data set is complete:
#   * If the dimensions file has a last modified date of at least an hour ago
#

sub upload_complete
{
  my ($dimZipFileName, $fileEpochTime);

  my ($dataSetID) = @_;


  opendir(DIRHANDLE, "/data/bair");
  while (defined($filename = readdir(DIRHANDLE)))
  {
    if ($filename =~ m/.*?_$dataSetID\.zip$/i)
    {
      $fileEpochTime = (stat("/data/bair/$filename"))[9];
      $timeDiff = time() - $fileEpochTime;
    }
  }

  #if the dimension data file has been modified in the past hour, we're going
  #to wait to process the data set
  if ($timeDiff < 1800)
  {
    return(0);
  }
  else
  {
    return(1);
  }
}



#---------------------------------------------------------------------------


  #redirect STDERR to the IDW error log
  close(STDERR);
  open(STDERR, ">>/data2/work-connect/idw-intake.log");
  select(STDERR);
  $| = 1;


  DBG("Starting update load run");

  #remove data sets older than 90 days
  @files = glob("/data2/bair/*.zip");
  foreach $file (@files)
  {
    if (-M $file > 90)
    {
      unlink($file);
      DBG("Unlinked $file that's over 90 days old");
    }
  }

  #check for MAXJOBS, and exit if already max'd out
  $runningJobs = count_running_jobs();
  if ($runningJobs >= $MAXPROCS)
  {
    DBG("Already running $runningJobs, exiting");
    exit;
  }

  #look for *_dimension_nnnnnnn.zip files, and save IDs for consideration
  opendir(DIRHANDLE, "/data/bair");
  while (defined($filename = readdir(DIRHANDLE)))
  {

    #delete unneeded *.completed files
    if ($filename =~ m/.*\.completed$/i)
    {
      DBG("Removing unneeded $filename");
      unlink("/data/bair/$filename");
    }

    #delete unneeded *.gz files
    if ($filename =~ m/.*\.txt\.gz$/i)
    {
      DBG("Removing unneeded $filename");
      unlink("/data/bair/$filename");
    }


    if ($filename =~ m/.*_(\d+)\.zip$/i)
    {
      $dataSetID = $1;
      DBG("Found data set $dataSetID");
      push(@dataSets, $dataSetID);
    }
  }

  #look for a fully uploaded data set that isn't already being processed
  foreach $dataSetID (@dataSets)
  {

    #skip a data set that's currently being processed
    if (-e "/data2/work-connect/$dataSetID.work")
    {
      DBG("Skipping data set $dataSetID, already being processed");
      next;
    }

    #make sure the data set is completely uploaded
    if (upload_complete($dataSetID) < 1)
    {
      DBG("Skipping data set $dataSetID, still potentially being uploaded");
      next;
    }

    #we've found something valid to process, add it to our "ok to run" list
    push(@runnableDataSets, $dataSetID);
  }

  $dataSetID = $runnableDataSets[0];

  #if we didn't find any data sets to process
  if ($dataSetID < 1)
  {
    DBG("No new data sets to be processed, exiting");
    exit;
  }

  DBG("Processing data set $dataSetID");

  #write out temp file indicating data set is being processed
  $tempWorkFileName = "/data2/work-connect/$dataSetID.work";
  system("echo $$ > $tempWorkFileName");

  #find and uncompress the dimension info file
  opendir(DIRHANDLE, "/data/bair");
  while (defined($filename = readdir(DIRHANDLE)))
  {
    if (($filename =~ m/(.*?)_dimension_$dataSetID\.zip$/i) ||
        ($filename =~ m/(.*?)_$dataSetID\.zip$/i))
    {

      #get the analyst-specified data set name
      $dataSetName = $1;

      $fileSpec = "/data/bair/$filename";
      DBG("Uncompressing dimension data from $fileSpec");
      $zipOutput = `/usr/bin/unzip $fileSpec -d /data2/work-connect 2>&1`;

      #check for a failed checksum, alert analyst, cleanup, and exit
      if ($zipOutput =~ m/End-of-central-directory signature not found/)
      {
        DBG("ERROR: $filename is a corrupted zip file");

        #TODO: change this to generalized cleanup routine
        DBG("Unlinking bad zip file $fileSpec");
        unlink($fileSpec);

        DBG("Creating ERROR file to alert user to bad file");
        $fileSpec = "/data2/bair/$dataSetName.ERROR";
        open(OUTPUT, ">$fileSpec");
        close(OUTPUT);

        DBG("Terminating");
        exit;
      }
    }
  }

  DBG("Data set name is $dataSetName");

  #---------------------------------------------------------------------------

  #handle a "special" data set (just blindly take it with no pre-processing)
  if ($dataSetName =~ m/.*\-special/i)
  {
    $zipFileName = $dataSetName . ".zip";
    $checksumFileName = $dataSetName . "_checksum_" . $dataSetID . ".txt";
    $factsFileName = $dataSetName . ".csv";

    $fctFileName = $dataSetName . "_fct_" . $dataSetID . ".txt";

    #rename the data file to something nicer for auto processing
    rename("/data2/work-connect/$fctFileName", "/data2/work-connect/$factsFileName") 
      or DBG("ERROR $dataSetID: Unable to rename data file, $!");

    #zip the data files up for Data Prep usage
    DBG("$dataSetID Creating output zip file");
    chdir("/data2/work-connect/");
    `/usr/bin/zip $zipFileName $factsFileName 2>&1`;

    #move final zip file to Data Prep-accessible directory
    DBG("$dataSetID Renaming /data2/work-connect/$zipFileName to /data2/bair/$zipFileName");
    unlink("/data2/bair/$zipFileName");
    rename("/data2/work-connect/$zipFileName", "/data2/bair/$zipFileName")
        or DBG("ERROR $dataSetID: Unable to rename working zip file, $!");

    #remove the working files we no longer need
    unlink("/data2/work-connect/$factsFileName");
    unlink("/data2/work-connect/$checksumFileName");

    opendir(DIRHANDLE, "/data/bair");
    while (defined($filename = readdir(DIRHANDLE)))
    {
      if ($filename =~ m/.*?_$dataSetID.*\.zip$/i)
      {
        $fileSpec = "/data/bair/$filename";
        DBG("$dataSetID Clearing source file $fileSpec");
        unlink("$fileSpec")
            or DBG("ERROR $dataSetID: Unable to delete source file $fileSpec, $!");
      }
    }

    #delete the file indicating data set is being processed
    DBG("$dataSetID Removing work file $tempWorkFileName");
    unlink("$tempWorkFileName")
        or DBG("ERROR $dataSetID: Unable to delete working file, $!");

    DBG("Done");

    exit;
  }



  #----------------------------------------------------------------------------


  $csv = Text::CSV_XS->new( {binary => 1, eol => $/} );



  #read the market tables as a hash for internal expansion
  $geoFileName = $dataSetName . "_mrkt_ref_" . $dataSetID . ".txt";
  open(INPUT, "/data2/work-connect/$geoFileName") or
      die("Unable to open /data2/work-connect/$geoFileName, $!");

  #burn header line
  $line = <INPUT>;

  while ($line = <INPUT>)
  {
    $csv->parse($line);
    @columns = $csv->fields();
    $id = $columns[0];
    $val = $columns[1];
    $marketHash{$id} = $val;
  }


  #----------------------------------------------------------------------------


  #read the time period tables as hashes for internal expansion
  $timeFileName = $dataSetName . "_prd_ref_" . $dataSetID . ".txt";

  open(INPUT, "/data2/work-connect/$timeFileName") or
      die("Unable to open /data2/work-connect/$timeFileName, $!");

  #burn header line
  $line = <INPUT>;

  while ($line = <INPUT>)
  {
    $csv->parse($line);
    @columns = $csv->fields();
    $id = $columns[0];
    $val = $columns[1];
    $periodHash{$id} = $val;
  }


  #----------------------------------------------------------------------------


  #create temporary file to write facts out to
  $factsFileName = $dataSetName . ".csv";
  open(OUTPUT, ">/data2/work-connect/$factsFileName")
      or die ("Couldn't open /data2/work-connect/$factsFileName, $!");

  #find all potential data files, and push them into an array to be processed
  opendir(DIRHANDLE, "/data/bair");
  while (defined($filename = readdir(DIRHANDLE)))
  {
    if ($filename =~ m/.*?_fct_$dataSetID.*?\.zip$/i)
    {
      $fileSpec = "/data/bair/$filename";
      push(@factFileNames, $fileSpec);
    }
  }
  opendir(DIRHANDLE, "/data2/work-connect");
  while (defined($filename = readdir(DIRHANDLE)))
  {
    if ($filename =~ m/.*?_fct_$dataSetID\.txt$/i)
    {
      $fileSpec = "/data2/work-connect/$filename";
      push(@factFileNames, $fileSpec);
    }
  }

  #uncompress & process all of the fact data files in the data set
  $first = 1;
  foreach $fileSpec (@factFileNames)
  {
    DBG("$dataSetID Uncompressing & processing $fileSpec");
    if ($fileSpec =~ m/.*?\.zip$/i)
    {
      $cmd = "/usr/bin/unzip -p $fileSpec";
      open($INPUT, "-|", $cmd);
    }
    else
    {
      open($INPUT, "$fileSpec");
    }

    #keep the header line from the first file, burn it for all others
    $colref = $csv->getline($INPUT);
    @columns = @$colref;
    if ($first)
    {
      $first = 0;

      #detect location of dimension columns we're going to expand internally
      $idx = 0;
      foreach $colName (@columns)
      {
        if ($colName eq "Market Key")
        {
          $marketKeyIdx = $idx;
          $columns[$idx] = "Market Description";
        }
        elsif ($colName eq "Period Key")
        {
          $periodKeyIndex = $idx;
          $columns[$idx] = "Period Description";
        }
        $idx++;
      }

      $csv->print(OUTPUT, \@columns);
    }

    #process and concatenate the file
    while ($colref = $csv->getline($INPUT))
    {
      @columns = @$colref;

      #do inline expansion of market and time period references
      if (defined($marketKeyIdx))
      {
        $columns[$marketKeyIdx] = $marketHash{$columns[$marketKeyIdx]};
      }
      if (defined($periodKeyIndex))
      {
        $columns[$periodKeyIndex] = $periodHash{$columns[$periodKeyIndex]};
      }

      $csv->print(OUTPUT, \@columns);
    }
  }

  $zipFileName = $dataSetName . ".zip";
  $checksumFileName = $dataSetName . "_checksum_" . $dataSetID . ".txt";
  $prodFileName = $dataSetName . "_prdc_ref_" . $dataSetID . ".txt";
  $geoFileName = $dataSetName . "_mrkt_ref_" . $dataSetID . ".txt";
  $timeFileName = $dataSetName . "_prd_ref_" . $dataSetID . ".txt";

  #zip the data files up for Data Prep usage
  DBG("$dataSetID Creating output zip file");
  chdir("/data2/work-connect/");
  `/usr/bin/zip $zipFileName $factsFileName $prodFileName 2>&1`;

  `printf "@ $factsFileName\n@=facts.csv\n" | zipnote -w $zipFileName 2>&1`;
  `printf "@ $prodFileName\n@=product-lookup.csv\n" | zipnote -w $zipFileName 2>&1`;

  #move final zip file to Data Prep-accessible directory
  DBG("$dataSetID Renaming /data2/work-connect/$zipFileName to /data2/bair/$zipFileName");
  unlink("/data2/bair/$zipFileName");
  rename("/data2/work-connect/$zipFileName", "/data2/bair/$zipFileName")
      or DBG("ERROR $dataSetID: Unable to rename working zip file, $!");

  #remove the working files we no longer need
  unlink("/data2/work-connect/$factsFileName");
  unlink("/data2/work-connect/$checksumFileName");
  unlink("/data2/work-connect/$prodFileName");
  unlink("/data2/work-connect/$geoFileName");
  unlink("/data2/work-connect/$timeFileName");


  #delete all raw files belonging to the processed data set
  foreach $fileSpec (@factFileNames)
  {
    DBG("$dataSetID Clearing source file $fileSpec");
    unlink("$fileSpec");
  }

  opendir(DIRHANDLE, "/data/bair");
  while (defined($filename = readdir(DIRHANDLE)))
  {
    if ($filename =~ m/.*?_$dataSetID.*\.zip$/i)
    {
      $fileSpec = "/data/bair/$filename";
      DBG("$dataSetID Clearing source file $fileSpec");
      unlink("$fileSpec")
          or DBG("ERROR $dataSetID: Unable to delete source file $fileSpec, $!");
    }
  }


  #delete the file indicating data set is being processed
  DBG("$dataSetID Removing work file $tempWorkFileName");
  unlink("$tempWorkFileName")
      or DBG("ERROR $dataSetID: Unable to delete working file, $!");

  DBG("Done");


#EOF
