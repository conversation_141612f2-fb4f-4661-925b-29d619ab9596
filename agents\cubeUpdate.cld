#!/usr/bin/perl

use lib "/opt/apache/app/";

use DBI;
use Email::Valid;

use Lib::KoalaConfig;
use Lib::BuildCube;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::Social;
use Lib::WebUtils;


my $debug;



#-------------------------------------------------------------------------
#
# Output debug data, if enabled
#

sub DBG
{
  my ($str) = @_;


  if ($debug == 1)
  {
    print STDERR "$str\n";
  }
}



#-------------------------------------------------------------------------

  #figure out how many parallel jobs we're supposed to run, default=1
  $maxProcs = $Lib::KoalaConfig::maxCubeUpdateProcs;
  if ($maxProcs < 1)
  {
    $maxProcs = 1;
  }

  #determine if we're being run manually
  $debug = 0;
  if ($ARGV[0] eq "debug")
  {
    $debug = 1;
  }

  #redirect STDERR to the Koala error log
  if ($debug == 0)
  {
    close(STDOUT);
    close(STDERR);
    open(STDERR, ">>/opt/apache/htdocs/tmp/koala_error.log");
  }

  #connect to the master database
  $db = KAPutil_connect_to_database();

  #subtract running cube update jobs to see how many new ones we're going to run
  $query = "SELECT COUNT(*) FROM app.jobs \
      WHERE operation='CUBE-UPDATE' AND userID=0";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($runningJobs) = $dbOutput->fetchrow_array;

  $maxProcs = $maxProcs - $runningJobs;
  if (($maxProcs < 1) && ($debug == 0))
  {
    DBG("cubeUpdate: System is already running $runningJobs, no available slots");
    exit;
  }

  #grab every autoupdate DS's ID and last update timestamp, and hash them
  $query = "SELECT ID, UNIX_TIMESTAMP(lastModified) FROM dataSources \
      WHERE autoUpdateCubes = 1 AND deleted = 0";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  undef(%dsHash);
  while (($dsID, $lastUpdate) = $dbOutput->fetchrow_array)
  {

    #if it's been at least 2 hours since the data source was last modified
    $timeSinceUpdate = time() - $lastUpdate;
    $timeSinceUpdate = $timeSinceUpdate / 3600;
    if ($timeSinceUpdate > 2)
    {

      #add the data source to list to be considered for cube updates
      $dsHash{$dsID} = $lastUpdate;
    }
  }

  #remove data sources being used by other processes from consideration
  $query = "SELECT dsID FROM app.jobs";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  while (($dsID) = $dbOutput->fetchrow_array)
  {
    delete($dsHash{$dsID});
  }

  #cycle through every data cube on the system, determining which ones need
  #to be updated
  undef(%updateHash);
  $query = "SELECT ID, dsID, UNIX_TIMESTAMP(lastUpdate) FROM cubes \
      WHERE ISNULL(status) OR status='DONE' ORDER BY dsID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;

  DBG("Examining $status data cubes to see if they need to be refreshed");
  while (($cubeID, $cubeDS, $cubeUpdate) = $dbOutput->fetchrow_array)
  {

    #if the underlying data source has been updated more recently than the cube
    if ($dsHash{$cubeDS} > $cubeUpdate)
    {
      $updateHash{$cubeDS} = $updateHash{$cubeDS} . "$cubeID,";
    }
  }

  $processCount = 0;
  foreach $cubeDS (keys %updateHash)
  {
    $cubeIDStr = $updateHash{$cubeDS};

    DBG("Cubes $cubeIDStr in $cubeDS need to be refreshed");

    #assemble the name of the schema containing the cube
    $dsSchema = "datasource_" . $cubeDS;

    #if we haven't hit our maximum process limit
    if ($processCount < $maxProcs)
    {

      #fire off the child process
      if ($pid = fork())
      {
        #parent process

        #increment count of active processes
        $processCount++;

        #give everything 10 seconds to get initialized before going again
        sleep(10);
      }

      #else we're the child process
      else
      {
        $childDB = KAPutil_connect_to_database();

        @cubeIDs = split(',', $cubeIDStr);
        foreach $cubeID (@cubeIDs)
        {

          #make sure the cube hasn't been updated by another process since we
          #started running
          $query = "SELECT UNIX_TIMESTAMP(lastUpdate) FROM cubes WHERE ID=$cubeID";
          $dbOutput2 = $childDB->prepare($query);
          $dbOutput2->execute;
          ($cubeUpdate) = $dbOutput2->fetchrow_array;
          if ($cubeUpdate > $dsHash{$cubeDS})
          {
            DBG("Another process has already updated cube $cubeID, skipping");
            next;
          }

          #make sure somebody hasn't started a manual update of one of the cubes
          #in the data source since we started
          $query = "SELECT COUNT(*) FROM app.jobs \
              WHERE dsID=$cubeDS AND operation='CUBE-UPDATE'";
          $dbOutput2 = $childDB->prepare($query);
          $dbOutput2->execute;
          ($count) = $dbOutput2->fetchrow_array;
          if ($count > 0)
          {
            DBG("An update process is already running in DS $cubeDS");
            exit;
          }

          #if a DS-UPDATE process has started running in the report's data
          #source, let's stop updating reports (since they're all being
          #invalidated anyways)
          $query = "SELECT COUNT(*) FROM app.jobs \
              WHERE dsID=$cubeDS AND operation='DS-UPDATE'";
          $dbOutput2 = $childDB->prepare($query);
          $dbOutput2->execute;
          ($count) = $dbOutput2->fetchrow_array;
          if ($count > 0)
          {
            DBG("A data source update has started in $cubeDS, exiting until it finishes");
            exit;
          }

          #rebuild the cube
          DBG("Rebuilding $cubeID in $cubeDS");
          cube_build($childDB, $dsSchema, $cubeID, 0);
        }

        $dsName = ds_id_to_name($childDB, $cubeDS);
        $ownerID = ds_get_owner($childDB, $cubeDS);
        utils_slack("Background agent refreshed reports in $dsName");

        Social_feed_add_item($childDB, $ownerID, $cubeDS, 0, 0, "success", "rpt_update_agent");

        #we're done processing all cubes in this DS, so terminate process
        exit;
      }
    }

    #wait here until an empty process slot opens up
    if ($processCount >= $maxProcs)
    {
      wait();
      $processCount--;
    }
  }

  #wait here until the last cube building processes finish
  while ($processCount > 0)
  {
    wait();
    $processCount--;
  }


#EOF
