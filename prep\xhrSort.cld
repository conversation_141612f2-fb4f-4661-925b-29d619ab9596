#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepRecipes;
use Lib::PrepUtils;
use Lib::WebUtils;


#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $flowID = $q->param('f');
  $jobID = $q->param('j');
  $colID = $q->param('col');
  $action = $q->param('a');
  $order = $q->param('o');

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #make sure we have read privs for this data flow
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to sort this data flow.");
  }

  $flowName = prep_flow_id_to_name($prepDB, $flowID);

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #build name of column containing the text
  $column = "column_" . $colID;

  #get the currently defined column type
  $query = "SELECT name, type FROM $masterColTable WHERE ID=$colID";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($colName, $type) = $dbOutput->fetchrow_array;


  ########################################################################
  #
  # This code block is called on submit to change the column's type
  #

  if ($action eq "apply")
  {

    $query = "UPDATE prep.jobs SET sortCol='$order|$column' WHERE ID=$jobID";
    $prepDB->do($query);

    prep_audit($prepDB, $userID, "Sorted data by $colName", $flowID);
    utils_slack("PREP: $first $last sorted data by $colName in $flowName");

    exit;
  }

  #########################################################################


  print <<END_HTML;
<SCRIPT>
function submitForm()
{
  let order = document.getElementById('order').value;
  let url = "xhrSort.cld?f=$flowID&j=$jobID&col=$colID&a=apply&o=" + order;

  \$('#modal-sort').modal('hide');

  \$.get(url, function(data, status)
  {
    location.href = "flowViewData.cld?f=$flowID&j=$jobID";
  });
}
</SCRIPT>


<DIV CLASS="modal-dialog modal-lg">
  <DIV CLASS="modal-content">

    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Sort Data</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">
      <P>
      <DIV CLASS="row">
        <DIV CLASS="col-auto mt-2">
          Sort data by the value of $colName in
        </DIV>
        <DIV CLASS="col-auto">
          <SELECT CLASS="form-select" ID="order" NAME="order">
            <OPTION VALUE="a">ascending</OPTION>
            <OPTION VALUE="d">descending</OPTION>
          </SELECT>
        </DIV>
        <DIV CLASS="col-auto mt-2">
          order.
        </DIV>
      </DIV>
    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="submitForm()"><I CLASS="bi bi-sort-down-alt"></I> Sort</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML

  $query = "UPDATE prep.jobs SET lastAction=NOW() WHERE ID=$jobID";
  $prepDB->do($query);

#EOF
