#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;
use Excel::Writer::XLSX;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $rptID = $q->param('rptID');

  $db = KAPutil_connect_to_database();

  #make sure we have view privs for this report
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to export this report.");
  }

  #get the data cube details from the database
  $query = "SELECT dsID, name, measures FROM cubes WHERE ID=$rptID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  ($dsID, $name, $measures) = $dbOutput->fetchrow_array;

  $cubeName = cube_id_to_name($db, $rptID);
  $dsID = cube_get_ds_id($db, $rptID);
  $dsName = ds_id_to_name($db, $dsID);

  $query = "INSERT INTO audit.stats_cubes (cubeID, year, month, exportExcel) \
      VALUES ($rptID, YEAR(NOW()), MONTH(NOW()), 1) \
      ON DUPLICATE KEY UPDATE exportExcel=exportExcel+1";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);

  utils_audit($db, $userID, "Exported backing data cube to Excel", $dsID, $rptID, 0);
  $activity = "$first $last exported cube to Excel for $cubeName in $dsName";
  utils_slack($activity);

  #build up the temp filename
  $tmpDSName = substr($dsName, 0, 32);
  $tmpCubeName = substr($cubeName, 0, 32);
  $filename = "Koala $tmpDSName" . " - " . $tmpCubeName . "_" . $userID . "_" . "$dsID.xlsx";

  #get rid of special characters we don't want in the file name
  $filename =~ s/\s+/ /g;
  $filename =~ s/\\//g;
  $filename =~ s/\///g;
  $filename =~ s/\*//g;
  $filename =~ s/\$//g;
  $filename =~ s/\&//g;
  $filename =~ s/\|//g;
  $filename =~ s/\?//g;
  $filename =~ s/\://g;
  $filename =~ s/\"//g;
  $filename =~ s/\'//g;
  $filename =~ s/\%//g;
  $filename =~ s/\#//g;

  #create a new Excel workbook
  $workbook = Excel::Writer::XLSX->new("/opt/apache/htdocs/tmp/$filename");
  $workbook->set_optimization();

  #split measures CSV string into array
  @measures = split(',', $measures);

  #assemble datasource and cube names
  $dsSchema = "datasource_" . $dsID;
  $rptCube = "_rptcube_" . $rptID;

  #convert dimension IDs into human-readable names
  %geoNames = dsr_get_item_name_hash($db, $dsSchema, "g");
  %productNames = dsr_get_item_name_hash($db, $dsSchema, "p");
  %timeperiodNames = dsr_get_item_name_hash($db, $dsSchema, "t");
  %measureNames = dsr_get_item_name_hash($db, $dsSchema, "m");

  #make sure our sheet name isn't going to be too long
  $name = substr($name, 0, 29);

  #add worksheet with same name as report
  $worksheet1 = $workbook->add_worksheet($name);

  #define header format
  $formatHeadBlue = $workbook->add_format();
  $formatHeadBlue->set_bold(1);
  $formatHeadBlue->set_bg_color('blue');
  $formatHeadBlue->set_color('white');

  #write out header row
  $worksheet1->write('A1', 'Product', $formatHeadBlue);
  $worksheet1->write('B1', 'Geography', $formatHeadBlue);
  $worksheet1->write('C1', 'Time Period', $formatHeadBlue);
  $worksheet1->write('D1', 'Sort Time', $formatHeadBlue);
  $col = 4;
  foreach $measure (@measures)
  {
    $worksheet1->write(0, $col, $measureNames{$measure}, $formatHeadBlue);
    $col++;
  }

  #build up measure selection string
  $measureColStr = "";
  foreach $measure (@measures)
  {
    if ($measure =~ m/^\d+$/)
    {
      $measure = "measure_$measure";
    }
    $measureColStr .= $measure . ",";
  }
  chop($measureColStr);
  $measureColStr = "product, geography, time, sortTime, " . $measureColStr;

  #grab every row of data from the cube and output it
  $query = "SELECT $measureColStr FROM $dsSchema.$rptCube";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  @rowData = $dbOutput->fetchrow_array;
  $rowIndex = 1;
  while (defined(@rowData[0]))
  {
    $rowData[0] = $productNames{$rowData[0]};
    $rowData[1] = $geoNames{$rowData[1]};
    $rowData[2] = $timeperiodNames{$rowData[2]};
    $worksheet1->write_row($rowIndex, 0, \@rowData);
    $rowIndex++;
    @rowData = $dbOutput->fetchrow_array;
  }

  $workbook->close();

  print <<END_HTML;
<DIV CLASS="modal-dialog" STYLE="overflow-y: initial !important;">
  <DIV CLASS="modal-content">
    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Export Cube To Excel</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">

      <DIV CLASS="text-center">
        <BUTTON CLASS="btn btn-success" TYPE="button" onClick="location.href='/tmp/$filename'"><I CLASS="bi bi-download"></I> Download Excel Workbook</BUTTON>
      </DIV>

    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-primary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-check-lg"></I> Done</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML

#EOF
