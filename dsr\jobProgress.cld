#!/usr/bin/perl

use lib "/opt/apache/app/";


use CGI qw(:standard);
use CGI::Session;
use DBI;
use Text::CSV;

use Lib::DataSources;
use Lib::KoalaConfig;
use Lib::DSRCreateUpdate;
use Lib::DSRMeasures;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Data Source Job Progress</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
let statusTimer = setInterval(function(){displayStatus()}, 5000);
let started = 0;
let emptyCounts = 0;

function displayStatus()
{
  let url = '/app/dsr/xhrJobStatus.cld?ds=$dsID';

  \$.get(url, function(data, status)
  {
    statusText = data;

    document.getElementById('progressDiv').innerHTML = statusText;

    if (statusText.search('ERROR') >= 0)
    {
      clearInterval(statusTimer);
      \$('#btnAsync').text('Cancel');
      document.getElementById('progress-bar').style.visibility = 'hidden';
    }

    if (statusText.length < 2)
    {
      clearInterval(statusTimer);
      location.href='$Lib::KoalaConfig::kapHostURL/app/dsr/display.cld?ds=$dsID';
    }

  });
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item active">$dsName</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------
#
# Output the HTML for the web page that displays the status dialog
#

sub print_status_html
{
    print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-11 col-md-8 col-lg-6 col-xl-5"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">$dlgTitle</DIV>
        <DIV CLASS="card-body">

          <P>
          <DIV CLASS="progress">
            <DIV ID="progress-bar" CLASS="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" STYLE="width:100%;"></DIV>
          </DIV>

          <P>
          <CENTER>
            <DIV ID="progressDiv">Processing</DIV>
          </CENTER>

          <P>&nbsp;</P>

          Koala can continue working in the background while you work on other things.

          <P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" ID="btnAsync" onClick="location.href='/app/dsr/main.cld'"><I CLASS="bi bi-speedometer"></I> Be More Productive</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get the CGI input variables
  $dsID = $q->param('ds');

  #connect to database
  $db = KAPutil_connect_to_database();

  #validate the parameters we were passed
  if ($dsID < 1)
  {
    print_html_header();
    exit_error("Invalid data source - requested operation cannot be performed.");
  }

  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  #make sure we have read privs for this data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs eq "N")
  {
    exit_error("Permission denied.");
  }

  #figure out which operation is being performed
  $query = "SELECT operation FROM app.jobs \
      WHERE dsID=$dsID AND operation IN ('ROLLBACK', 'XFER-MEASURES', 'MERGE-ITEMS', 'FORCE-REFRESH', 'ADD-MEASURE', 'COPY-DS')";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ($operation) = $dbOutput->fetchrow_array;

  if ($operation eq "ROLLBACK")
  {
    $dlgTitle = "Rolling Back Update";
  }
  elsif ($operation eq "XFER-MEASURES")
  {
    $dlgTitle = "Transferring Structures";
  }
  elsif ($operation eq "MERGE-ITEMS")
  {
    $dlgTitle = "Merging Items";
  }
  elsif ($operation eq "FORCE-REFRESH")
  {
    $dlgTitle = "Forcing Calculation Refresh";
  }
  elsif ($operation eq "ADD-MEASURE")
  {
    $dlgTitle = "Adding New Calculated Measure";
  }
  elsif ($operation eq "COPY-DS")
  {
    $dlgTitle = "Copying Data Source";
  }

  #if the data source is already being updated by another process, join the
  #status stream
  print_status_html();


#EOF
