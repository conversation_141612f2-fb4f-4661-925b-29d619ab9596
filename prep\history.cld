#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($kapDB);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Data Flow History</TITLE>
<LINK REL="SHORTCUT ICON" HREF="$Lib::KoalaConfig::kapHostURL/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function showModal(resource)
{
  \$('#modal-telemetry .modal-content').html('');
  \$('#modal-telemetry').modal('show');
  \$('#modal-telemetry .modal-content').load(resource);
}
</SCRIPT>

</HEAD>

<BODY>

END_HTML

  print_html_navbar($kapDB, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="$Lib::KoalaConfig::kapHostURL/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?f=$flowID">Data Prep</A></LI>
    <LI CLASS="breadcrumb-item">$flowName</LI>
    <LI CLASS="breadcrumb-item active">History</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser and get the forecast to be deleted
  $q = new CGI;
  $flowID = $q->param('f');

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  $flowName = prep_flow_id_to_name($prepDB, $flowID);

  print_html_header();

  #make sure we have read privs for this data source
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to view this data flow's history.");
  }

  #get basic data flow info
  $query = "SELECT lastRun, source, sourceInfo FROM prep.flows WHERE ID=$flowID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  ($lastRun, $source, $sourceInfo) = $dbOutput->fetchrow_array;

  if (length($lastRun) < 1)
  {
    $lastRun = "(Never)";
  }

  if ($sourceInfo =~ m/^FTP=nielsen\|.*PATH=(.*).zip$/i)
  {
    $fileName = $1;
    if ($fileName =~ m/^(.*)-update$/)
    {
      $sourceInfo = "Update data set for $1 from Koala IDW for Nielsen";
    }
    else
    {
      $sourceInfo = "History data set for $fileName from Koala IDW for Nielsen";
    }
  }
  else
  {
    $sourceInfo = $source;
  }

  %userNameHash = utils_get_user_hash($kapDB);
  $userNameHash{0} = "Koala";

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-12 col-lg-11 col-xl-9"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Data Flow History</DIV>
        <DIV CLASS="card-body">

          <TABLE CLASS="table table-striped table-bordered table-sm">
            <TR>
              <TD STYLE='text-align:right; font-weight:bold;'>
                Last Data Flow Run:
              </TD>
              <TD STYLE='text-align:left;'>
                $lastRun
              </TD>
            </TR>
            <TR>
              <TD STYLE='text-align:right; font-weight:bold;'>
                Source Information:
              </TD>
              <TD STYLE='text-align:left;'>
                $sourceInfo
              </TD>
            </TR>
          </TABLE>

          <P>
          <DIV CLASS = "table-responsive" STYLE="height:400px; overflow:auto;">
            <TABLE CLASS="table table-striped table-sm">
END_HTML

  #grab all of the activity info related to this data flow
  $query = "SELECT timestamp, userID, action FROM prep.audit \
      WHERE flowID=$flowID ORDER BY timestamp DESC";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;

  #output HTML row for every line in activity audit log
  while (($timestamp, $userID, $action) = $dbOutput->fetchrow_array)
  {

    #generate detailed stats (if available) for a data flow job
    #TODO: refactor into internal subroutine
    $jobHistory = "";
    if ($action =~ m/^Ran data flow\|(\d+)/)
    {
      $jobID = $1;
      $query = "SELECT dataProcessed, recordsProcessed,
              elapsedTime, cloudLoad,
              filenames,
              successfulXforms, failedXforms,
              numProducts, numGeos, numTimes, numMeasures
          FROM prep.job_history WHERE jobID=$jobID";
      $dbOutput1 = $prepDB->prepare($query);
      $dbOutput1->execute;
      ($dataProcessed, $recordsProcessed, $runTime, $cloudLoad, $filenames, $successfulXforms, $failedXforms, $numProducts, $numGeos, $numTimes, $numMeasures) = $dbOutput1->fetchrow_array;
      if ($numProducts > 0)
      {
        $dataProcessed = PrepUtils_autoscale_data_size($dataProcessed);
        $recordsProcessed = prep_autoscale_number($recordsProcessed);

        if ($cloudLoad == 3)
        {
          $cloudLoad = "<SPAN CLASS='text-danger'>Overloaded</SPAN>";
        }
        elsif ($cloudLoad == 2)
        {
          $cloudLoad = "<SPAN CLASS='text-warning'>Heavy</SPAN>";
        }
        else
        {
          $cloudLoad = "<SPAN CLASS='text-success'>Normal</SPAN>";
        }

        if ($filenames =~ m/^Nielsen IDW (.*)\.zip/)
        {
          $filenames = "<CODE>$1</CODE> data set from Nielsen IDW"
        }
        else
        {
          @filenamesArr = split(/\|/, $filenames);
          $filenames = "";
          foreach $filename (@filenamesArr)
          {
            $filenames .= "<CODE>$filename</CODE><BR>";
          }
        }

        $jobHistory = <<END_HTML;
              <TABLE CLASS="table table-sm table-bordered">
                <TR>
                  <TD COLSPAN=2>
                    <SMALL>Data Processed:</SMALL><BR>
                    $dataProcessed
                  </TD>
                  <TD COLSPAN=2>
                    <SMALL>Records Processed:</SMALL><BR>
                    $recordsProcessed
                  <TD>
                </TR>
                <TR>
                  <TD COLSPAN=2>
                    <SMALL>Run Time:</SMALL><BR>
                    $runTime
                  </TD>
                  <TD COLSPAN=2>
                    <SMALL>Cloud Load Level:</SMALL><BR>
                    $cloudLoad
                  <TD>
                </TR>
                <TR>
                  <TD COLSPAN=4>
                    <SMALL>Source data:</SMALL><BR>
                    $filenames
                  </TD>
                </TR>
                <TR>
                  <TD COLSPAN=2>
                    <SMALL>Successful Recipe Transforms:</SMALL><BR>
                    <H5>$successfulXforms</H5>
                  </TD>
                  <TD COLSPAN=2>
                    <SMALL>Failed Recipe Transforms:</SMALL><BR>
                    <H5>$failedXforms</H5>
                  </TD>
                </TR>
                <TR>
                  <TD>
                    <SMALL>Products:</SMALL><BR>
                    <H5>$numProducts</H5>
                  </TD>
                  <TD>
                    <SMALL>Geographies:</SMALL><BR>
                    <H5>$numGeos</H5>
                  </TD>
                  <TD>
                    <SMALL>Time Periods:</SMALL><BR>
                    <H5>$numTimes</H5>
                  </TD>
                  <TD>
                    <SMALL>Measures:</SMALL><BR>
                    <H5>$numMeasures</H5>
                  </TD>
                </TR>
              </TABLE>
END_HTML
      }
    }

    if ($action =~ m/^(.*)\|(\d+)$/)
    {
      $jobID = $2;
      $action = "$1 <A CLASS='text-decoration-none' HREF='#' onClick=\"showModal('xhrTelemetry.cld?j=$jobID')\">View Telemetry</A>";
    }

    print <<END_HTML;
              <TR>
                <TD NOWRAP>$timestamp</TD>
                <TD NOWRAP>$userNameHash{$userID}</TD>
                <TD>
                  $action
                  $jobHistory
                </TD>
              </TR>
END_HTML

  }

  print <<END_HTML;
            </TABLE>
          </DIV>

          <P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" onClick="location.href='main.cld?f=$flowID'"><I CLASS="bi bi-check-lg"></I> Done</BUTTON>
          </DIV>

        </DIV>
      </DIV>

      <DIV ID="modal-telemetry" CLASS="modal" ROLE="dialog">
        <DIV CLASS="modal-dialog modal-lg" STYLE="overflow-y: initial !important;">
          <DIV CLASS="modal-content">
          </DIV>
        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

  $activity = "PREP: $first $last viewed history for $flowName";
  utils_slack($activity);


#EOF
