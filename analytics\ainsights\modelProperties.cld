#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::AInsights::AInsights;
use Lib::Users;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: AInsights Model Properties</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">AInsights</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="insights.cld?pm=$priceModelID">$pricingName</A></LI>
    <LI CLASS="breadcrumb-item active">Properties</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $priceModelID = $q->param('pm');

  #connect to the database
  $db = KAPutil_connect_to_database();

  #get the name of the pricing model
  $modelName = AInsights_ID_to_name($db, $priceModelID);

  print_html_header();

  #make sure we have privs to at least view this model
  $privs = AInsights_rights($db, $userID, $priceModelID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges on this model.");
  }

  $query = "SELECT userID, name, description, push FROM analytics.pricing \
      WHERE ID=$priceModelID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($ownerID, $name, $description, $pushToDS) = $dbOutput->fetchrow_array;

  if ($pushToDS == 1)
  {
    $pushToDS = "CHECKED";
  }
  else
  {
    $pushToDS = "";
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-11 col-lg-8 col-xl-7"> <!-- content -->

      <FORM METHOD="post" ACTION="modelPropertiesSave.cld">
      <INPUT TYPE="hidden" NAME="pm" VALUE="$priceModelID">
      <INPUT TYPE="hidden" NAME="oldOwnerID" VALUE="$ownerID">


      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">AInsights Model Properties</DIV>
        <DIV CLASS="card-body">

          <TABLE>
            <TR>
              <TD CLASS="text-end">
                <LABEL FOR="name">Name:&nbsp;</LABEL>
              </TD>
              <TD>
                <INPUT CLASS="form-control" TYPE="text" NAME="name" ID="name" VALUE="$name" STYLE="width:300px;" required>
              </TD>
            </TR>

            <TR>
              <TD CLASS="text-end">
                <LABEL FOR="dsDescription">Description:&nbsp;</LABEL>
              </TD>
              <TD>
                <INPUT CLASS="form-control" TYPE="text" NAME="dsDescription" ID="dsDescription" VALUE="$description" MAXLENGTH="1023">
              </TD>
            </TR>
            <TR>
              <TD CLASS="text-end">
                <LABEL FOR="dsOwner">Model Owner:&nbsp;</LABEL>
              </TD>
              <TD>
                <SELECT CLASS="form-select" NAME="dsOwner" ID="dsOwner">
END_HTML

  #get a list of all users in the private cloud, and display
  $query = "SELECT ID, first, last, orgID FROM users WHERE acctType > 0 \
      ORDER BY orgID, last";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  while (($id, $userFirst, $userLast, $userOrg) = $dbOutput->fetchrow_array)
  {
    $userOrg = Users_orgID_to_name($db, $userOrg);
    print(" <OPTION VALUE='$id'>$userFirst $userLast ($userOrg)</OPTION>\n");
  }

  print <<END_HTML;
                </SELECT>
                <SCRIPT>
                  \$('select#dsOwner').val('$ownerID');
                </SCRIPT>
              </TD>
            </TR>
          </TABLE>

          <P></P>
          (If you change the owner of this model to another user, you'll still retain read and write privileges.)

          <P>
          <DIV CLASS="form-check">
           <INPUT CLASS="form-check-input" TYPE="checkbox" NAME="push" id="push" $pushToDS>
           <LABEL CLASS="form-check-label" FOR="push">Automatically push updated elasticity values to the data source</LABEL>
          </DIV>

          <P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="location.href='main.cld'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT"><I CLASS="bi bi-save"></I> Save</BUTTON>
          </DIV>

        </DIV>
      </DIV>

      </FORM>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
