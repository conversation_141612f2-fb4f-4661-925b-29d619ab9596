#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSel;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName Data Selection</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<SCRIPT SRC="/fancytree-2.31.0/dist/jquery.fancytree-all-deps.min.js"></SCRIPT>
<SCRIPT SRC="/fancytree-2.31.0/dist/modules/jquery.fancytree.multi.js"></SCRIPT>
<SCRIPT SRC="/jsgrid/jsgrid.min.js"></SCRIPT>

<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/fancytree-2.31.0/dist/skin-win8/ui.fancytree.min.css" rel="stylesheet">
<LINK HREF="/jsgrid/jsgrid.min.css" REL="stylesheet">
<LINK HREF="/jsgrid/jsgrid-theme.min.css" REL="stylesheet">

<STYLE>
div.grid th
{
  background: #e2e3e5 !important;
}

.selected-row > td
{
  background: #c4e2ff !important;
  border-color: #c4e2ff;
}
</STYLE>


<SCRIPT>

function checkForm(form)
{
  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Please Wait...');
  return(true);
}

\$(function()
{
  \$('#itemtree').fancytree(
  {
    selectMode: 2,
    quicksearch: true,
    autoScroll: true,
    extensions: ['multi'],
    multi: {mode: 'sameParent'},
    dblclick: function(event, data)
    {
      data.node.setSelected();
      addSelection();
    },
    init: function(event, data)
    {
      let tree = data.tree;
      let segHierNode = tree.findFirst('Segmentation Hierarchies');
      if (segHierNode)
      {
        segHierNode.sortChildren(null, true);
      }
    },
    lazyLoad: function(event, data)
    {
      let node = data.node;
      data.result =
      {
        url: "/app/dsr/ajaxDimensionTrees.cld",
        data: {'ds': '$dsID', 'd': '$dim', mode: "children", parent: node.key},
        cache: false
      };
    },
    clickPaging: function(event, data)
    {
      data.node.replaceWith({url: data.node.data.url}).done(function()
      {
        //pagingNode replaced with data pulled from data source
      });
    },
    source: {url: '/app/dsr/ajaxDimensionTrees.cld?ds=$dsID&d=$dim&s=sel'}
  });
});

END_HTML

  $dataBlock = datasel_get_script_json($db, $rptID, $structType, $dim, $dsID);

  print <<END_HTML;
let gridData = [ $dataBlock ];

\$(document).ready(function()
{
  \$('#manualGrid').jsGrid(
  {
    width: '100%',
    height: '200px',
    sorting: true,
    autoload: true,
    loadIndication: true,
    multiselect: true,
    confirmDeleting: false,

    data: gridData,

    rowClick: function(args)
    {

      //Shift + selection
      if (args.event.shiftKey)
      {
        document.getSelection().removeAllRanges();

        let i = 0;
        let firstSelection = -1;
        while ((i < this.data.length) && (firstSelection < 0))
        {
          if (this.data[i].selected == 1)
          {
            firstSelection = i;
          }
          i++;
        }

        i = 0;
        let curSelection = -1;
        while ((i < this.data.length) && (curSelection < 0))
        {
          if (args.item.id == this.data[i].id)
          {
            curSelection = i;
          }
          i++;
        }

        clearAllSelections();

        let start, stop;
        if (curSelection > firstSelection)
        {
          start = firstSelection;
          end = curSelection;
        }
        else
        {
          end = firstSelection;
          start = curSelection;
        }

        for (i = start; i <= end; i++)
        {
          this.data[i].selected = 1;
          \$selectedRow = \$('#manualGrid').jsGrid('rowByItem', this.data[i]).closest('tr');
          \$selectedRow.addClass('selected-row');
        }

      }

      //Ctrl+selection
      else if (event.ctrlKey || event.altKey || event.metaKey)
      {
        args.item.selected = 1;
        \$selectedRow = \$(args.event.target).closest('tr');
        \$selectedRow.addClass('selected-row');
      }

      //single selection
      else
      {
        clearAllSelections();
        args.item.selected = 1;
        \$selectedRow = \$(args.event.target).closest('tr');
        \$selectedRow.addClass('selected-row');
      }
    },

    rowDoubleClick: function(args)
    {
      removeSelection();
    },

    fields: [
      {name: 'id', type: 'number', visible: false},
      {name: 'product', title: '$dimName', type: 'text', width: 420},
      {name: 'type', title: 'Type', type: 'text', width: 120}
    ]

  });
});



function clearAllSelections()
{
  let grid = \$('#manualGrid').jsGrid('option', 'data');

  for (let i = 0; i < grid.length; i++)
  {
    grid[i].selected = 0;
  }

  \$('#manualGrid tr').removeClass('selected-row');
}



function getSelectionStr()
{
  let grid = \$('#manualGrid').jsGrid('option', 'data');
  let selStr = '';

  for (let i = 0; i < grid.length; i++)
  {
    if (grid[i].selected == 1)
    {
      selStr = selStr + grid[i].id + ',';
    }
  }

  return(selStr);
}



function getSelectionIdx()
{
  let grid = \$('#manualGrid').jsGrid('option', 'data');
  let selStr = '';

  for (let i = 0; i < grid.length; i++)
  {
    if (grid[i].selected == 1)
    {
      selStr = selStr + i + ',';
    }
  }

  return(selStr);
}



function addSelection()
{
  let itemID, itemName, curNode;
  let extantIDs = [];

  let tree = \$('#itemtree').fancytree('getTree');
  let grid = \$('#manualGrid').jsGrid('option', 'data');
  let curNodes = tree.getSelectedNodes();

  //build array of all items already present in selection
  grid.forEach(function(item, index)
  {
    extantIDs.push(grid[index].id);
  });

  let i = 0;
  while (i < curNodes.length)
  {
    curNode = curNodes[i];
    itemID = curNode.key;
    itemName = curNode.title;

    if (extantIDs.indexOf(itemID) > -1)
    {
      i++;
      continue;
    }

    if (curNode.type == 'base')
    {
      \$('#manualGrid').jsGrid('insertItem', {id: itemID, product: itemName, type: 'Base Item'});
    }
    else if (curNode.type == 'aggr')
    {
      \$('#manualGrid').jsGrid('insertItem', {id: itemID, product: itemName, type: 'Aggregate'});
    }
    else if (curNode.type == 'list')
    {
      \$('#manualGrid').jsGrid('insertItem', {id: itemID, product: itemName, type: 'List'});
    }
    else if (curNode.type == 'listitem')
    {
      let baseID = itemID.match(/LIS_\\d+\\.(\\d+)/);

      if (baseID != null)
      {
        \$('#manualGrid').jsGrid('insertItem', {id: baseID[1], product: itemName, type: 'Base Item'});
      }
    }
    else if (curNode.type == 'attr')
    {
      \$('#manualGrid').jsGrid('insertItem', {id: itemID, product: itemName, type: 'Attribute'});
    }
    else if (curNode.type == 'seg')
    {
      \$('#manualGrid').jsGrid('insertItem', {id: itemID, product: itemName, type: 'Segmentation'});
    }
    else if (curNode.type == 'segment')
    {
      \$('#manualGrid').jsGrid('insertItem', {id: itemID, product: itemName, type: 'Segment'});
    }
    else if (curNode.type == 'segitem')
    {
      let baseID = itemID.match(/SMT_\\d+\\.(\\d+)/);

      if (baseID != null)
      {
        \$('#manualGrid').jsGrid('insertItem', {id: baseID[1], product: itemName, type: 'Base Item'});
      }
    }
    else if (curNode.type == 'segmenthierlevel')
    {
      \$('#manualGrid').jsGrid('insertItem', {id: itemID, product: itemName, type: 'Hierarchy Level'});
    }
    else if (curNode.type == 'segmenthieritem')
    {
      let baseID = itemID.match(/SHS_.*\\_(\\d+)/);
      \$('#manualGrid').jsGrid('insertItem', {id: baseID[1], product: itemName, type: 'Base Item'});
    }
    else if (itemID == 'all')
    {
      \$('#manualGrid').jsGrid('insertItem', {id: 'ALL', product: 'All Items', type: 'Base Items'});
    }
    else if (('$dim' == 't') && (curNode.type == 'header'))
    {
      let all = curNode.getChildren();
      for (let j = 0; j < all.length; j++)
      {
        let baseID = all[j].key;
        let baseName = all[j].title;

        if (extantIDs.indexOf(baseID) > -1)
        {
          continue;
        }

        \$('#manualGrid').jsGrid('insertItem', {id: baseID, product: baseName, type: 'Base Item'});
      }
    }

    i++;
  }

  \$selectedRow = \$('#manualGrid').jsGrid('rowByItem', grid[grid.length-1]).closest('tr');
  \$selectedRow[0].scrollIntoView();
}



function removeSelection()
{
  let grid = \$('#manualGrid').jsGrid('option', 'data');

  for (let i = grid.length - 1; i >= 0; i--)
  {
    if (grid[i].selected == 1)
    {
      \$('#manualGrid').jsGrid('deleteItem', grid[i]);
    }
  }
}



function submitForm()
{
  let grid = \$('#manualGrid').jsGrid('option', 'data');
  let form = document.getElementById('selForm');

  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Please Wait...');

  let val = '';
  for (let i = 0; i < grid.length; i++)
  {
    let item = grid[i].product;
    let id = grid[i].id;

    if (id.indexOf('H:') == 0)
    {
      val = val + id + ',';
    }
    else if (id.indexOf('SH:') == 0)
    {
      val = val + id + ',';
    }
    else if (id.indexOf('RECENT:') == 0)
    {
      val = val + id + ',';
    }
    else if (id.indexOf('RANGE:') == 0)
    {
      val = val + id + ',';
    }
    else if (id.indexOf('MATCH:') == 0)
    {
      val = val + id + ',';
    }
    else
    {
      val = val + 'M:' + id + ',';
    }
  }

  let hiddenField = document.createElement('input');
  hiddenField.setAttribute('type', 'hidden');
  hiddenField.setAttribute('name', 'selection');
  hiddenField.setAttribute('value', val);
  form.appendChild(hiddenField);

  form.submit();
}

</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  #output appropriate navigation header for the structure type we're editing
  if ($structType eq "a")
  {
    $dsName = ds_id_to_name($db, $dsID);
    $aggName = $structName;
    if (length($structName) < 1)
    {
      $aggName = DSRagg_id_to_name($db, $dsSchema, $dim, $structID);
    }
    print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/dsr/main.cld">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/dsr/display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Edit Aggregate $aggName</LI>
  </OL>
</NAV>
<P>
END_HTML
  }

  elsif ($structType eq "l")
  {
    $dsName = ds_id_to_name($db, $dsID);
    $listName = $structName;
    if (length($structName) < 1)
    {
      $listName = DSRlist_id_to_name($db, $dsSchema, $dim, $structID);
    }
    print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/dsr/main.cld">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/dsr/display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Edit List $listName</LI>
  </OL>
</NAV>
<P>
END_HTML
  }

  elsif ($structType eq "c")
  {
    $rptName = $structName;
    if (length($structName) < 1)
    {
      $rptName = cube_id_to_name($db, $rptID);
    }
    print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/rpt/main">Reports</A></LI>
    <LI CLASS="breadcrumb-item">$rptName</LI>
    <LI CLASS="breadcrumb-item active">Manual Data Selection</LI>
  </OL>
</NAV>
<P>
END_HTML
  }
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');
  $dim = $q->param('dim');
  $rptID = $q->param('rptID');
  $structType = $q->param('st');
  $structID = $q->param('sid');
  $structName = $q->param('name');

  if ($dim eq "p")
  {
    $dimName = "Products";
  }
  elsif ($dim eq "g")
  {
    $dimName = "Geographies";
  }
  elsif ($dim eq "t")
  {
    $dimName = "Time Periods";
  }
  elsif ($dim eq "m")
  {
    $dimName = "Measures";
  }

  $db = KAPutil_connect_to_database();

  $dsSchema = "datasource_" . $dsID;

  #if we're looking at a list or aggregate, set rptID to structID
  if (($structType eq "l") || ($structType eq "a"))
  {
    $rptID = $structID;
  }

  #figure out which script we're submitting our selections to
  if ($structType eq "l")
  {
    $postScript = "/app/dsr/listEdit.cld";
    $cancelScript = "/app/dsr/display.cld?ds=$dsID";
  }
  elsif ($structType eq "a")
  {
    $postScript = "/app/dsr/aggEdit.cld";
    $cancelScript = "/app/dsr/display.cld?ds=$dsID";
  }
  else
  {
    $postScript = "datasel.cld";
    $cancelScript = "datasel.cld?ds=$dsID&rptID=$rptID&dim=$dim";
  }

  print_html_header();

  #check our permissions
  if ($structType eq "c")
  {
    $privs = cube_rights($db, $userID, $rptID, $acctType);
    if ($privs ne "W")
    {
      exit_error("You don't have privileges to edit this report.");
    }
  }
  else
  {
    $privs = ds_rights($db, $userID, $dsID, $acctType);
    if ($privs ne "W")
    {
      exit_error("You don't have privileges to modify this data source.");
    }
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-11 col-lg-8 col-xl-7"> <!-- content -->

      <FORM NAME="selForm" ID="selForm" METHOD="post" ACTION="$postScript" onsubmit="return checkForm(this);">
      <INPUT TYPE="hidden" NAME="ds" VALUE="$dsID">
      <INPUT TYPE="hidden" NAME="rptID" VALUE="$rptID">
      <INPUT TYPE="hidden" NAME="dim" VALUE="$dim">
      <INPUT TYPE="hidden" NAME="st" VALUE="$structType">
      <INPUT TYPE="hidden" NAME="sid" VALUE="$structID">
      <INPUT TYPE="hidden" NAME="action" VALUE="s">
      <INPUT TYPE="hidden" NAME="name" VALUE="$structName">
      <INPUT TYPE="hidden" NAME="method" VALUE="M">

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Manual Selection</DIV>
        <DIV CLASS="card-body">

          <DIV CLASS="w-100" STYLE="height:200px; border:1px solid lightblue; overflow: auto;">
            <DIV id="itemtree" data-source="ajax"></DIV>
          </DIV>

          <P>
          <CENTER>
            <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="addSelection()" TITLE="Add the selected items"><SPAN CLASS="bi bi-chevron-down" STYLE="font-size:48px; text-align:center;"></SPAN></BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="removeSelection()" TITLE="Remove the selected items"><SPAN CLASS="bi bi-chevron-up" STYLE="font-size:48px; text-align:center;"></SPAN></BUTTON>
          </CENTER>

          <P></P>
          <DIV id="manualGrid" CLASS="grid mx-auto" STYLE="font-size:13px;"></DIV>

          <P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="location.href='$cancelScript'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="button" ID="btn-submit" onClick="submitForm()"><I CLASS="bi bi-save"></I> Save</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
