#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName Data Selection</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Please Wait...');
  return(true);
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  #output appropriate navigation header for the structure type we're editing
  if ($structType eq "a")
  {
    $dsName = ds_id_to_name($db, $dsID);
    $aggName = $structName;
    if (length($structName) < 1)
    {
      $aggName = DSRagg_id_to_name($db, $dsSchema, $dim, $structID);
    }
    print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/dsr/main.cld">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/dsr/display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Edit Aggregate $aggName</LI>
  </OL>
</NAV>
<P>
END_HTML
  }

  elsif ($structType eq "l")
  {
    $dsName = ds_id_to_name($db, $dsID);
    $listName = $structName;
    if (length($structName) < 1)
    {
      $listName = DSRlist_id_to_name($db, $dsSchema, $dim, $structID);
    }
    print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/dsr/main.cld">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/dsr/display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Edit List $listName</LI>
  </OL>
</NAV>
<P>
END_HTML
  }

  elsif ($structType eq "c")
  {
    $rptName = $structName;
    if (length($structName) < 1)
    {
      $rptName = cube_id_to_name($db, $rptID);
    }
    print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/rpt/main">Reports</A></LI>
    <LI CLASS="breadcrumb-item">$rptName</LI>
    <LI CLASS="breadcrumb-item active">Match Data Selection</LI>
  </OL>
</NAV>
<P>
END_HTML
  }
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser and get the forecast to be deleted
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  #get CGI parameters
  $dsID = $q->param('ds');
  $dim = $q->param('dim');
  $rptID = $q->param('rptID');
  $structType = $q->param('st');
  $structID = $q->param('sid');
  $structName = $q->param('name');
  $modifyItem = $q->param('modItem');

  get_cgi_session_info();

  #set human-readable dimension name
  if ($dim eq "p")
  {
    $dimName = "Products";
  }
  elsif ($dim eq "g")
  {
    $dimName = "Geographies";
  }
  elsif ($dim eq "t")
  {
    $dimName = "Time Periods";
  }

  #build data source schema name
  $dsSchema = "datasource_" . $dsID;

  #connect to the database
  $db = KAPutil_connect_to_database();

  #figure out which script we're submitting our selections to
  if ($structType eq "l")
  {
    $postScript = "/app/dsr/listEdit.cld";
    $cancelScript = "/app/dsr/display.cld?ds=$dsID";
  }
  elsif ($structType eq "a")
  {
    $postScript = "/app/dsr/aggEdit.cld";
    $cancelScript = "/app/dsr/display.cld?ds=$dsID";
  }
  else
  {
    $postScript = "datasel.cld";
    $cancelScript = "datasel.cld?ds=$dsID&rptID=$rptID&dim=$dim";
  }

  #if we're modifying an existing entry
  $matchTypeVal = "start";
  $selectionVal = "";
  if ($modifyItem =~ m/^MATCH\:(.*?) (.*)/)
  {
    $matchTypeVal = $1;
    $selectionVal = $2;
  }

  print_html_header();

  #check our permissions
  if ($structType eq "c")
  {
    $privs = cube_rights($db, $userID, $rptID, $acctType);
    if ($privs ne "W")
    {
      exit_error("You don't have privileges to edit this report.");
    }
  }
  else
  {
    $privs = ds_rights($db, $userID, $dsID, $acctType);
    if ($privs ne "W")
    {
      exit_error("You don't have privileges to modify this data source.");
    }
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-11 col-lg-8 col-xl-7"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Match Selection</DIV>
        <DIV CLASS="card-body">

          <FORM METHOD="post" ACTION="$postScript" onsubmit="return checkForm(this);">
          <INPUT TYPE="hidden" NAME="ds" VALUE="$dsID">
          <INPUT TYPE="hidden" NAME="rptID" VALUE="$rptID">
          <INPUT TYPE="hidden" NAME="dim" VALUE="$dim">
          <INPUT TYPE="hidden" NAME="st" VALUE="$structType">
          <INPUT TYPE="hidden" NAME="sid" VALUE="$structID">
          <INPUT TYPE="hidden" NAME="name" VALUE="$structName">
          <INPUT TYPE="hidden" NAME="action" VALUE="s">
          <INPUT TYPE="hidden" NAME="method" VALUE="MATCH">
          <INPUT TYPE="hidden" NAME="modItem" VALUE="$modifyItem">

          <DIV CLASS="row">
            <DIV CLASS="col-auto mt-1 gx-1 ms-2">
              Add $dimName that
            </DIV>
            <DIV CLASS="col-auto gx-1">
              <SELECT CLASS="form-select" NAME="matchType" ID="matchType" required>
                <OPTION VALUE="start">begin with</OPTION>
                <OPTION VALUE="contain">contain</OPTION>
                <OPTION VALUE="end">end with</OPTION>
                <OPTION VALUE="exact">exactly match</OPTION>
              </SELECT>
              <SCRIPT>
                \$('#matchType').val('$matchTypeVal');
              </SCRIPT>
            </DIV>
            <DIV CLASS="col-auto mt-1 gx-1">
              the characters
            </DIV>
            <DIV CLASS="col-auto gx-1">
              <INPUT TYPE="text" CLASS="form-control" NAME="selection" ID="selection" STYLE="width:150px;" VALUE="$selectionVal" required>
            </DIV>
          </DIV>

          <P>&nbsp;</P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="location.href='$cancelScript'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="submit" ID="btn-submit"><I CLASS="bi bi-save"></I> Save</BUTTON>
          </DIV>

          </FORM>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
