#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;
use Text::CSV;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::WebUtils;


#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($kapDB);

  print <<END_HTML;
<DOCCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Applying Validation Rules</TITLE>
<LINK REL="SHORTCUT ICON" HREF="$Lib::KoalaConfig::kapHostURL/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
let statusTimer = setInterval(function(){displayStatus()}, 1000);
let statCount = 0;

function displayStatus()
{
  const url = 'ajaxAPI.cld?svc=job_op_status&j=$jobID';

  \$.get(url, function(data, status)
  {
    let statusText = data;
    let statElements = statusText.split('|');

    if (statusText.search('DONE') == 0)
    {
      \$('#progress-bar').css('width', '100%');
      clearInterval(statusTimer);
      location.href='flowViewData.cld?f=$flowID&j=$jobID';
      return;
    }

    let opTitle = statElements[0];
    let opPct = statElements[1];
    let opDetails = statElements[2];
    let opTimeEstimate = statElements[3];
    let opExtra = statElements[4];
    document.getElementById('div-op-title').innerHTML = opTitle;
    if (opPct.length > 0)
    {
      document.getElementById('progress-bar').innerHTML = opPct + '%';
      \$('#progress-bar').css('width', opPct+'%');
    }
    document.getElementById('div-op-details').innerHTML = opDetails;
    document.getElementById('div-op-extra').innerHTML = opExtra;
  });

  if (statCount == 5)
  {
    clearInterval(statusTimer);
    statusTimer = setInterval(function(){displayStatus()}, 5000);
  }
  statCount++;
}
</SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($kapDB, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="$Lib::KoalaConfig::kapHostURL/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">Data Prep</A></LI>
    <LI CLASS="breadcrumb-item">$flowName</LI>
    <LI CLASS="breadcrumb-item active">Apply Validation Rules</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------
#
# Output the HTML for the web page that displays the status dialog
#

sub print_status_html
{
  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-11 col-md-8 col-lg-6 col-xl-5"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Applying Validation Rules</DIV>
        <DIV CLASS="card-body">

          <H5 ID="div-op-title"></H5>
          <DIV CLASS="progress" style="height:25px;">
            <DIV ID="progress-bar" CLASS="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" STYLE="width:0%;">
            </DIV>
          </DIV>

          <P>
          <DIV ID="div-op-details"></DIV>
          <DIV ID="div-op-extra"></DIV>

          <P>&nbsp;</P>
        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get the CGI input variables
  $flowID = $q->param('f');
  $jobID = $q->param('j');
  $invalidAction = $q->param('invalidAction');

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  $flowName = prep_flow_id_to_name($prepDB, $flowID);

  print_html_header();

  #make sure we have write privs for this data flow
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to change validation settings in this data flow.");
  }

  #set the initial data loading status for the job
  $query = "UPDATE prep.jobs SET opInfo='0|Applying validation rules' WHERE ID=$jobID";
  $prepDB->do($query);

  prep_audit($prepDB, $userID, "Updated and applied validation rules", $flowID);
  utils_slack("PREP: $first $last updated and applied validation rules in $flowName");

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #get a hash of every column name in the flow for this job
  %colNames = prep_flow_get_column_hash($prepDB, $flowID, $jobID);

  #make sure there's a row in the validation table for each column
  #NB: we're doing this up here so we can just write simple SQL UPDATE queries
  #    throughout the rest of the script
  foreach $colID (keys %colNames)
  {
    $q_name = $prepDB->quote($colNames{$colID});
    $query = "INSERT INTO prep.validation (flowID, name) \
        VALUES ($flowID, $q_name) \
        ON DUPLICATE KEY UPDATE flowID=$flowID";
    $prepDB->do($query);
  }

  #pre-set column values to default states
  $query = "UPDATE prep.validation \
      SET present=0, blank=0, minLength=NULL, maxLength=NULL, minVal=NULL, maxVal=NULL, matches=NULL \
      WHERE flowID=$flowID";
  $prepDB->do($query);

  #save our post-validation action to take on invalid Rows
  $query = "UPDATE prep.flows SET invalidAction='$invalidAction' WHERE ID=$flowID";
  $prepDB->do($query);

  #grab the validation settings for each column and save them
  @params = $q->param;
  foreach $param (@params)
  {

    #get the value for the current parameter
    $val = $q->param($param);
    if ($val eq "on")
    {
      $val = 1;
    }
    $q_val = $prepDB->quote($val);

    #if the value of the parameter is blank, it's really NULL
    if (length($val) < 1)
    {
      $q_val = $val = "NULL";
    }

    #if it's a "must be present" setting
    if ($param =~ m/^present_(\d+)$/)
    {
      $name = $colNames{$1};
      $q_name = $prepDB->quote($name);
      $query = "UPDATE prep.validation SET present=$q_val \
          WHERE flowID=$flowID AND name=$q_name";
      $prepDB->do($query);
    }

    #if it's a "blanks OK" setting
    elsif ($param =~ m/^blanks_(\d+)$/)
    {
      $name = $colNames{$1};
      $q_name = $prepDB->quote($name);
      $query = "UPDATE prep.validation SET blank=$q_val \
          WHERE flowID=$flowID AND name=$q_name";
      $prepDB->do($query);
    }

    #if it's a min val setting
    elsif ($param =~ m/^min_(\d+)$/)
    {
      $name = $colNames{$1};
      $q_name = $prepDB->quote($name);
      $query = "UPDATE prep.validation SET minVal=$q_val \
          WHERE flowID=$flowID AND name=$q_name";
      $prepDB->do($query);
    }

    #if it's a max val setting
    elsif ($param =~ m/^max_(\d+)$/)
    {
      $name = $colNames{$1};
      $q_name = $prepDB->quote($name);
      $query = "UPDATE prep.validation SET maxVal=$q_val \
          WHERE flowID=$flowID AND name=$q_name";
      $prepDB->do($query);
    }

    #if it's a min length setting
    elsif ($param =~ m/^minl_(\d+)$/)
    {
      $name = $colNames{$1};
      $q_name = $prepDB->quote($name);
      $query = "UPDATE prep.validation SET minLength=$q_val \
          WHERE flowID=$flowID AND name=$q_name";
      $prepDB->do($query);
    }

    #if it's a max length setting
    elsif ($param =~ m/^maxl_(\d+)$/)
    {
      $name = $colNames{$1};
      $q_name = $prepDB->quote($name);
      $query = "UPDATE prep.validation SET maxLength=$q_val \
          WHERE flowID=$flowID AND name=$q_name";
      $prepDB->do($query);
    }

    #if it's a match rule
    elsif ($param =~ m/^match_(\d+)$/)
    {

      #if we were given a string to match against
      $tmp = "mval_" . $1;
      $mval = $q->param($tmp);
      if (length($mval) > 0)
      {
        $name = $colNames{$1};
        $q_name = $prepDB->quote($name);
        $q_match = $prepDB->quote("$val $mval");
        $query = "UPDATE prep.validation SET matches=$q_match \
            WHERE flowID=$flowID AND name=$q_name";
        $prepDB->do($query);
      }
    }
  }

  #fork a new process to do the actual data loading in the background
  $SIG{CHLD} = "IGNORE";
  if ($pid = fork())
  {
    #parent process

    print_status_html();
  }
  else
  {
    #child process

    #let Apache know not to wait on the child process
    close(STDIN);
    close(STDOUT);

    #redirect STDERR to the Koala error log
    close(STDERR);
    open(STDERR, ">>/opt/apache/htdocs/tmp/koala_error.log");
    select(STDERR);
    $| = 1;

    #reconnect to the database
    $prepDB = PrepUtils_connect_to_database();
    $kapDB = KAPutil_connect_to_database();
    prep_flow_validate($prepDB, $kapDB, $flowID, $jobID, 1);

    #we're done processing, so clear out opInfo
    $query = "UPDATE prep.jobs SET PID=NULL, opInfo='DONE', lastAction=NOW(), state='LOADED' \
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

#EOF
