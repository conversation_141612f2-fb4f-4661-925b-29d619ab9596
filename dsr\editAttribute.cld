#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  #output Content-type header
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  #output HTML
  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: $readableAction Attribute</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Please Wait...');
  return(true);
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">$readableAction Attribute</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');
  $dim = $q->param('dim');
  $attrID = $q->param('attr');

  #sanitize parameters
  $dsID = utils_sanitize_integer($dsID);
  $dim = utils_sanitize_dim($dim);
  $attrID = utils_sanitize_string($attrID);

  #if we received a fully-qualified attribute, strip it to the numerical ID
  if ($attrID =~ m/ATT_(\d+)/)
  {
    $attrID = $1;
  }

  if ($attrID > 0)
  {
    $readableAction = "Edit";
  }
  else
  {
    $readableAction = "New";
  }

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsSchema = "datasource_" . $dsID;
  $dsName = ds_id_to_name($db, $dsID);

  $dimName = KAPutil_get_dim_name_singular($dim, 1);
  $structDB = KAPutil_get_dim_stub_name($dim);

  #build the name of the database table holding attribute info for our dim
  $dbName = $structDB . "attributes";

  #if we got a fully-qualified attribute ID, strip it to a numerical ID
  if ($attrID =~ m/ATT_(\d+)/)
  {
    $attrID = $1;
  }

  $query = "SELECT name FROM $dsSchema.$dbName WHERE ID=$attrID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($name) = $dbOutput->fetchrow_array;

  print_html_header();

  #make sure we have write privs for this data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit attributes in this data source.");
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-10 col-lg-7 col-xl-6"> <!-- content -->

      <FORM METHOD="post" ACTION="editAttributeSave.cld" onsubmit="return checkForm(this);">
      <INPUT TYPE="hidden" NAME="dsID" VALUE="$dsID">
      <INPUT TYPE="hidden" NAME="dim" VALUE="$dim">
      <INPUT TYPE="hidden" NAME="attr" VALUE="$attrID">
      <INPUT TYPE="hidden" NAME="readableAction" VALUE="$readableAction">

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">$readableAction Attribute</DIV>
        <DIV CLASS="card-body">

          <LABEL FOR="name">Name:</LABEL>
          <INPUT CLASS="form-control" TYPE="text" NAME="name" ID="name" VALUE="$name" required>

          <P>&nbsp;<P>

          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="location.href='/app/dsr/display.cld?ds=$dsID&dim=$dim'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-submit"><I CLASS="bi bi-save"></I> Save</BUTTON>
          </DIV>

        </DIV>
      </DIV>

      </FORM>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
