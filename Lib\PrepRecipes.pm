
package Lib::PrepRecipes;

use lib "/opt/apache/app/";

use Exporter;
use Text::CSV;

use Lib::PrepFlows;
use Lib::PrepUtils;

our @ISA = ('Exporter');

our @EXPORT = qw(
    &prep_recipe_add_step
    &prep_recipe_edit_step
    &prep_recipe_step_text
    &prep_recipe_parse_date
    &prep_recipe_step_up
    &prep_recipe_step_down
    &prep_recipe_step_remove
    &prep_recipe_trans_col_type
    &prep_recipe_trans_col_name
    &prep_recipe_trans_col_split
    &prep_recipe_trans_col_split_length
    &prep_recipe_trans_col_lookup
    &prep_recipe_trans_col_insert
    &prep_recipe_trans_col_copy
    &prep_recipe_trans_col_merge
    &prep_recipe_trans_col_discard
    &prep_recipe_trans_col_discard_empty
    &prep_recipe_trans_col_trim_values
    &prep_recipe_trans_col_trim_data
    &prep_recipe_trans_col_product_match
    &prep_recipe_trans_col_index
    &prep_recipe_trans_cell_append_prepend
    &prep_recipe_trans_cell_set
    &prep_recipe_trans_cell_replace
    &prep_recipe_trans_cell_search_replace
    &prep_recipe_trans_cell_round
    &prep_recipe_trans_cell_ceiling
    &prep_recipe_trans_cell_floor
    &prep_recipe_trans_cell_nazero
    &prep_recipe_trans_cell_html
    &prep_recipe_trans_cell_space
    &prep_recipe_trans_cell_case
    &prep_recipe_trans_cell_date
    &prep_recipe_trans_cell_enrich_date
    &prep_recipe_trans_cell_upc
    &prep_recipe_trans_cell_weights
    &prep_recipe_apply
);



my %columnTypeNames = (
    "product" => "Product",
    "geography" => "Geography",
    "time" => "Time Period",
    "upc" => "UPC/SKU",
    "pseg" => "Product Segmentation",
    "gseg" => "Geography Segmentation",
    "tseg" => "Time Segmentation",
    "pattr" => "Product Attribute",
    "gattr" => "Geography Attribute",
    "tattr" => "Time Attribute",
    "palias" => "Product Alias",
    "galias" => "Geography Alias",
    "talias" => "Time Alias",
    "measure" => "Measure",
);




#-------------------------------------------------------------------------
#
# Parse a supplied date string and return type, duration, and end date
#

sub prep_recipe_parse_date
{
  my ($query, $dbOutput, $type, $duration, $day, $month, $year, $status);
  my ($endDate);
  my (@dateVals);

  my ($prepDB, $time) = @_;


  #handle whackadoo Nielsen date formats
  if ($time =~ m/^year ago (.*) thru (.*)$/i)
  {
    $time = "$1 ENDING $2";
  }
  if ($time =~ m/^year ago (.*) ending (.*)$/i)
  {
    $time = "$1 ENDING $2";
  }
  if ($time =~ m/($1) YAG (.*\d\d\d\d\.1)/)
  {
    $time = "$1 ENDING $2";
  }
  if ($time =~ m/year to date thru week ending/i)
  {
    $time = "CUSTOM";
  }
  if ($time =~ m/^cal yr (\d+) W\/E (.*)$/i)
  {
    $time = "52 WEEKS ENDING $2";
  }

  #handle custom Nielsen year-to-date formats
  if ($time =~ m/^cytd /i)
  {
    $time = "CUSTOM";
  }

  #handle "4/5 Week: June 21, 2015, Year Ago"
  if ($time =~ m/4\/5 week: (.*)/i)
  {
    $time = "4 Week Ending $1";
  }

  #if the time starts with "Latest" or "Current", strip it off
  if ($time =~ m/Latest (.*)/i)
  {
    $time = $1;
  }
  if ($time =~ m/Current (.*)/i)
  {
    $time = $1;
  }

  #handle raw data that uses "WEEK ENDING" to imply a duration of 1
  if (!($time =~ m/^[0-9]/))
  {
    $time = "1 $time";
  }

  #extract the numerical duration value
  if ($time =~ m/([0-9]+)\s(.*)/)
  {
    $duration = $1;
    $time = $2;
  }

  #if the period type is a year
  if ($time =~ m/^year ending|years ending/i)
  {
    $type = "year";
  }

  #if the period type is a month
  elsif ($time =~ m/^month ending|months ending/i)
  {
    $type = "month";
  }

  #if the period type is a week
  elsif ($time =~ m/^week ending|weeks ending|wk ending|wks ending|wks curr|we|wk|w\/e/i)
  {
    $type = "week";
  }

  #if the period type is a day
  elsif ($time =~ m/^day ending|days ending|day/i)
  {
    $type = "day";
  }

  #if the period type is an hour
  elsif ($time =~ m/^hour ending|hours ending/i)
  {
    $type = "hour";
  }

  #custom time period
  else
  {
    undef($type);
    undef($duration);
  }

  #extract the ending date and time
  if ($time =~ m/(\d\d\d\d)\/(\d\d)\/(\d\d)/)
  {           #2010/12/31
    $year = $1;
    $month = $2;
    $day = $3;
  }
  elsif ($time =~ m/(\d\d\d\d)\-(\d\d)\-(\d\d)/)
  {           #2022-01-15
    $year = $1;
    $month = $2;
    $day = $3;
  }
  elsif ($time =~ m/([0-9]+)\/([0-9]+)\/([0-9]+)/)
  {           #12/31/2010 and 12/31/10
    $month = $1;
    $day = $2;
    $year = $3;
  }
  elsif ($time =~ m/([0-9]+)\-([0-9]+)\-([0-9]+)/)
  {           #12-31-2010 and 12-31-10
    $month = $1;
    $day = $2;
    $year = $3;
  }
  elsif ($time =~ m/ (\w+) (\d{1,2}), (\d{4})/)
  {           #Nov 2, 2011
    $month = $1;
    $day = $2;
    $year = $3;
    if ($month =~ /^Jan/)
    {
      $month = 1;
    }
    elsif ($month =~ /^Feb/)
    {
      $month = 2;
    }
    elsif ($month =~ /^Mar/)
    {
      $month = 3;
    }
    elsif ($month =~ /^Apr/)
    {
      $month = 4;
    }
    elsif ($month =~ /^May/)
    {
      $month = 5;
    }
    elsif ($month =~ /^Jun/)
    {
      $month = 6;
    }
    elsif ($month =~ /^Jul/)
    {
      $month = 7;
    }
    elsif ($month =~ /^Aug/)
    {
      $month = 8;
    }
    elsif ($month =~ /^Sep/)
    {
      $month = 9;
    }
    elsif ($month =~ /^Oct/)
    {
      $month = 10;
    }
    elsif ($month =~ /^Nov/)
    {
      $month = 11;
    }
    elsif ($month =~ /^Dec/)
    {
      $month = 12;
    }
  }
  elsif ($time =~ m/ (\w\w\w)(\d\d)(\d\d)\.\d/)
  {           #Aug0214.4
    $month = $1;
    $day = $2;
    $year = $3;
    $year = "20" . $year; #Y2K? What's that? ;-)
    if ($month =~ m/Jan/i)
    {
      $month = 1;
    }
    elsif ($month =~ m/Feb/i)
    {
      $month = 2;
    }
    elsif ($month =~ /Mar/i)
    {
      $month = 3;
    }
    elsif ($month =~ /Apr/i)
    {
      $month = 4;
    }
    elsif ($month =~ /May/i)
    {
      $month = 5;
    }
    elsif ($month =~ /Jun/i)
    {
      $month = 6;
    }
    elsif ($month =~ /Jul/i)
    {
      $month = 7;
    }
    elsif ($month =~ /Aug/i)
    {
      $month = 8;
    }
    elsif ($month =~ /Sep/i)
    {
      $month = 9;
    }
    elsif ($month =~ /Oct/i)
    {
      $month = 10;
    }
    elsif ($month =~ /Nov/i)
    {
      $month = 11;
    }
    elsif ($month =~ /Dec/i)
    {
      $month = 12;
    }
  }

  #handle non-Y2K compliant year strings
  if ($year < 100)
  {
    $year = "20" . $year;
  }

  #if this is a "year ago" period, subtract 52 weeks (let the DB do it)
  if ($time =~ m/year ago$/i)
  {
    $query = "SELECT DATE_SUB('$year-$month-$day 00:00:00', INTERVAL 52 WEEK)";
    $dbOutput = $prepDB->prepare($query);
    $status = $dbOutput->execute;
    PrepUtils_handle_db_err($prepDB, $status, $query);
    $endDate = $dbOutput->fetchrow_array;
  }
  else
  {
    $endDate = "$year-$month-$day 00:00:00";
  }

  #if it's a custom time period, wipe out any garbage we tried to parse
  if (!defined($type))
  {
    undef($duration);
  }

  @dateVals = ($duration, $type, $endDate);

  return(@dateVals);
}



#-------------------------------------------------------------------------
#
# Rearrange the recipe steps for the selected flow so the selected step is
# executed one step before it previously was, shifting other steps down
#

sub prep_recipe_step_up
{
  my ($topStep, $query, $dbOutput, $action, $q_action, $status);

  my ($prepDB, $flowID, $step) = @_;


  $topStep = $step - 1;

  #if we're already at the top, we're done
  if ($topStep < 1)
  {
    return;
  }

  #get the step that's being moved down
  $query = "SELECT action FROM prep.recipes
      WHERE flowID=$flowID AND step=$topStep";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($action) = $dbOutput->fetchrow_array;

  #temporarily remove the step that's being moved down (avoids PK issue in DB)
  $query = "DELETE FROM prep.recipes WHERE flowID=$flowID AND step=$topStep";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #move the specified step up in the list by decrementing its step #
  $query = "UPDATE prep.recipes SET step=$topStep
      WHERE flowID=$flowID AND step=$step";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #put the old topStep back in its new position
  $q_action = $prepDB->quote($action);
  $query = "INSERT INTO prep.recipes (flowID, step, action)
      VALUES ($flowID, $step, $q_action)";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);
}



#-------------------------------------------------------------------------
#
# Rearrange the recipe steps for the selected flow so the selected step is
# executed one step after it previously was, shifting other steps up
#

sub prep_recipe_step_down
{
  my ($bottomStep, $query, $dbOutput, $lastStep, $action, $q_action, $status);

  my ($prepDB, $flowID, $step) = @_;


  $bottomStep = $step + 1;

  #if we're already at the bottom, we're done
  $query = "SELECT step FROM prep.recipes WHERE flowID=$flowID
      ORDER BY step DESC LIMIT 1";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($lastStep) = $dbOutput->fetchrow_array;
  if ($bottomStep > $lastStep)
  {
    return;
  }

  #get the step that's being moved up
  $query = "SELECT action FROM prep.recipes
      WHERE flowID=$flowID AND step=$bottomStep";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($action) = $dbOutput->fetchrow_array;

  #temporarily remove the step that's being moved up (avoids PK issue in DB)
  $query = "DELETE FROM prep.recipes WHERE flowID=$flowID AND step=$bottomStep";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #move the specified step down in the list by incrementing its step #
  $query = "UPDATE prep.recipes SET step=$bottomStep
      WHERE flowID=$flowID AND step=$step";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #put the old bottomStep back in its new position
  $q_action = $prepDB->quote($action);
  $query = "INSERT INTO prep.recipes (flowID, step, action)
      VALUES ($flowID, $step, $q_action)";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);
}



#-------------------------------------------------------------------------
#
# Remove the specified recipe step from the specified flow, and decrement
# all of the following step numbers to "fill in" the recipe.
#

sub prep_recipe_step_remove
{
  my ($query, $status);

  my ($prepDB, $flowID, $step) = @_;


  #delete the step that's being removed
  $query = "DELETE FROM prep.recipes WHERE flowID=$flowID and step=$step";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #decrement all of the steps that come after the deleted step
  $query = "UPDATE prep.recipes SET step=step-1
      WHERE flowID=$flowID AND step > $step";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);
}



#-------------------------------------------------------------------------
#
# Add the specified step to the recipe for the specified data flow. Returns
# the step ID on success, -1 on failure.
#

sub prep_recipe_add_step
{
  my ($query, $dbOutput, $stepID, $q_step, $status);

  my ($prepDB, $flowID, $step) = @_;


  #get the step ID for our new recipe step
  $query = "SELECT step FROM prep.recipes WHERE flowID=$flowID
      ORDER BY step DESC LIMIT 1";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($stepID) = $dbOutput->fetchrow_array;
  $stepID = $stepID + 1;

  #add the step to the recipe for the selected flow
  $q_step = $prepDB->quote($step);
  $query = "INSERT INTO prep.recipes (flowID, step, action)
      VALUES ($flowID, $stepID, $q_step)";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  return($stepID);
}



#-------------------------------------------------------------------------
#
# "Edit" the specified step by replacing the current step with the specified
# step ID with the newly supplied action string.
#

sub prep_recipe_edit_step
{
  my ($query, $dbOutput, $stepID, $q_action, $status);

  my ($prepDB, $flowID, $stepID, $action) = @_;


  $q_action = $prepDB->quote($action);
  $query = "UPDATE prep.recipes SET action=$q_action
      WHERE flowID=$flowID AND step=$stepID";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);
}



#-------------------------------------------------------------------------
#
# Convert the supplied machine-readable recipe step to human-readable text.
#

sub prep_recipe_step_text
{
  my ($readable);

  my ($action, $html) = @_;


  if ($action =~ m/^TRANS-COL-TYPE\|COL=(.*?)\|TYPE=(.*)$/)
  {
    $readable = "Transform column $1 to a $columnTypeNames{$2} column";
  }

  elsif ($action =~ m/^TRANS-COL-NAME\|COL=(.*?)\|NAME=(.*)$/)
  {
    $readable = "Rename column $1 to $2";
  }

  elsif ($action =~ m/^TRANS-COL-SPLIT\|COL=(.*?)\|SEP=(.*?)\|OTHER=(.*)$/)
  {
    $splitChar = $2;
    if (length($3) > 0)
    {
      $splitChar = $3;
    }
    $readable = "Split the $1 column into multiple columns on the $splitChar character";
  }

  elsif ($action =~ m/^TRANS-COL-SPLIT-LENGTH\|COL=(.*?)\|FIELDS=(.*)$/)
  {
    $readable = "Split the $1 column into multiple columns of field lengths $2";
  }

  elsif ($action =~ m/^TRANS-COL-LOOKUP\|COL=(.*?)\|TABLE=(.*?)\|TAB=(.*)$/)
  {
    if (length($3) > 0)
    {
      $readable = "Lookup values in the $3 tab of $2 based on the $1 column";
    }
    else
    {
      $readable = "Lookup values in $2 based on the $1 column";
    }
  }

  elsif ($action =~ m/^TRANS-COL-INSERT\|COL=(.*?)\|OP=(.*?)\|NUM=(.*)$/)
  {
    $readable = "Insert a new column containing the value of $1 $2 $3";
  }

  elsif ($action =~ m/^TRANS-COL-INSERT\|COL=(.*?)\|PRE=(.*?)\|APP=(.*)$/)
  {
    $readable = "Insert a new column based on $1, prepended by '$2' and appended by '$3'";
  }

  elsif ($action =~ m/^TRANS-COL-INSERT\|COL=(.*?)\|TYPE=(.*)$/)
  {
    $readable = "Insert a new $columnTypeNames{$2} column named $1";
  }

  elsif ($action =~ m/^TRANS-COL-COPY\|COL=(.*?)\|OP=(.*?)\|NUM=(.*)$/)
  {
    $readable = "Copy $1 containing the value of $1 $2 $3";
  }

  elsif ($action =~ m/^TRANS-COL-COPY\|COL=(.*?)\|PRE=(.*?)\|APP=(.*)$/)
  {
    $readable = "Copy $1, prepended by '$2' and appended by '$3'";
  }

  elsif ($action =~ m/^TRANS-COL-MERGE\|COL=(.*?)\|MERGE=(.*?)\|INNER=(.*)$/)
  {
    $readable = "Merge column $1 with $2, separated by '$3'";
  }

  elsif ($action =~ m/^TRANS-COL-DISCARD\|COL=(.*)$/)
  {
    $readable = "Discard column $1";
  }

  elsif ($action =~ m/^TRANS-COL-DISCARD-EMPTY\|$/)
  {
    $readable = "Discard empty columns";
  }

  elsif ($action =~ m/^TRANS-COL-TRIM-VALUES\|COL=(.*?)\|OP=(.*?)\|MATCH=(.*?)\|VALUE=(.*)$/)
  {
    $readable = "Trim ($2) values where column $1 $3 $4";
  }

  elsif ($action =~ m/^TRANS-COL-TRIM-DATA\|COL=(.*?)\|OP=(.*?)\|DATA=(.*)$/)
  {
    if ($html < 1)
    {
      $readable = "Trim ($2) data where column $1 is one of $3";
    }
    else
    {
      $readable = "Trim ($2) data where column $1 is one of:<P>\n";
      $valStr = $3;

      #split the data value string into the array of data values we're trimming
      $csv = Text::CSV->new( {binary => 1} );
      $csv->parse($valStr);
      @values = $csv->fields();
      @values = sort(@values);

      #output an HTML table containing the values
      $readable .= <<END_HTML;
      <TABLE CLASS="table table-sm table-striped table-bordered">
END_HTML

      foreach $value (@values)
      {
        $readable .= <<END_HTML;
        <TR><TD>$value</TD></TR>
END_HTML
      }

      $readable .= <<END_HTML;
      </TABLE>
END_HTML
    }
  }

  elsif ($action =~ m/^TRANS-COL-PRODUCT-MATCH\|COL=(.*)\|DS=(.*?)\|UNMATCHED=(.*?)\|$/)
  {
    $readable = "Match products for $1";
  }

  elsif ($action =~ m/^TRANS-CELL-APPEND-PREPEND\|COL=(.*?)\|PREPEND=(.*?)\|APPEND=(.*?)$/)
  {
    $readable = "Prepend values in $1 with $2, append with $3";
  }

  elsif ($action =~ m/^TRANS-CELL-SET\|COL=(.*?)\|CHECK=(.*?)\|OP=(.*?)\|MATCH=(.*?)\|REPL=(.*)$/)
  {
    $readable = "Set value in $1 to $5 where $2 $3 $4";
  }

  elsif ($action =~ m/^TRANS-CELL-REPLACE\|COL=(.*)\|CHECK=(.*?)\|OP=(.*?)\|MATCH=(.*)\|REPL=(.*)$/)
  {
    $readable = "Replace values in $1 to the corresponding value from $5 where $2 $3 $4";
  }

  elsif ($action =~ m/^TRANS-CELL-SEARCH-REPLACE\|COL=(.*)\|OP=(.*?)\|SEARCH=(.*)\|REPL=(.*)$/)
  {
    $readable = "Search in $1 for values $2 with '$3' and replace them with '$4'";
  }

  elsif ($action =~ m/^TRANS-CELL-ROUND\|COL=(.*?)\|DEC=(\d+)$/)
  {
    $readable = "Round values in $1 to $2 decimal places";
  }

  elsif ($action =~ m/^TRANS-CELL-CEILING\|COL=(.*)$/)
  {
    $readable = "Ceiling values in $1";
  }

  elsif ($action =~ m/^TRANS-CELL-FLOOR\|COL=(.*)$/)
  {
    $readable = "Floor values in $1";
  }

  elsif ($action =~ m/^TRANS-CELL-NAZERO\|COL=(.*)$/)
  {
    $readable = "Convert NAs to zeroes in $1";
  }

  elsif ($action =~ m/^TRANS-CELL-HTML\|COL=(.*)$/)
  {
    $readable = "Convert any HTML in $1 to text";
  }

  elsif ($action =~ m/^TRANS-CELL-SPACE\|COL=(.*?)\|COMP=(.*?)\|TRIM=(.*)$/)
  {
    $readable = "Clean up white space in $1";
  }

  elsif ($action =~ m/^TRANS-CELL-CASE\|COL=(.*?)\|CASE=(.*)$/)
  {
    $readable = "Convert text in $1 to $2 case";
  }

  elsif ($action =~ m/^TRANS-CELL-DATE\|COL=(.*?)\|TIME=(.*?)\|DUR=(.*)$/)
  {
    $readable = "Re-format dates in $1";
  }

  elsif ($action =~ m/^TRANS-CELL-ENRICH-DATE\|COL=(.*?)\|DUR=(.*?)\|TYPE=(.*?)\|OVERWRITE=(.*?)\|ROUNDDAY=(.*)$/)
  {
    $readable = "CPG enrich dates in $1";
  }

  elsif ($action =~ m/^TRANS-CELL-UPC\|COL=(.*?)\|OP=(.*?)\|LENGTH=(.*?)\|$/)
  {
    if ($2 eq "trimSizeLeadZeroes")
    {
      $readable = "Trim leading zeroes from $1 to a length of $3";
    }
    elsif ($2 eq "trimSizeBegin")
    {
      $readable = "Trim leading digits from $1 to a length of $3";
    }
    elsif ($2 eq "trimSizeTrailZeroes")
    {
      $readable = "Trim trailing zeroes from $1 to a length of $3";
    }
    elsif ($2 eq "trimSizeEnd")
    {
      $readable = "Trim trailing digits from $1 to a length of $3";
    }
    elsif ($2 eq "addSizeLeadZeroes")
    {
      $readable = "Add leading zeroes to $1 to reach a length of $3";
    }
    elsif ($2 eq "addSizeTrailZeroes")
    {
      $readable = "Add trailing zeroes to $1 to reach a length of $3";
    }
    elsif ($2 eq "trimLeadZeroes")
    {
      $readable = "Trim all leading zeroes from $1";
    }
    elsif ($2 eq "trimTrailZeroes")
    {
      $readable = "Trim all trailing zeroes from $1";
    }
    elsif ($2 eq "removeHyphens")
    {
      $readable = "Remove hyphens from $1";
    }
    else
    {
      $readable = "Re-format UPCs in $1";
    }
  }

  else
  {
    $readable = $action;
  }

  return($readable);
}



#-------------------------------------------------------------------------
#
# Apply the "set column type" column transform
#

sub prep_recipe_trans_col_type
{
  my ($query, $masterColTable, $dbOutput, $action, $colName, $colType);
  my ($q_colName, $status, $oldType, $masterTable, $colID);

  my ($prepDB, $flowID, $jobID, $stepID, $noUpdate) = @_;


  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs SET PID=$$, opInfo='-|Changing column type'
        WHERE ID=$jobID";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);
  }

  #build the table name for the master columns table for the job
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #get the details of the action we're performing
  $query = "SELECT action FROM prep.recipes WHERE flowID=$flowID AND step=$stepID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($action) = $dbOutput->fetchrow_array;

  #extract our transform info from the recipe step, error out if bad step
  if ($action =~ m/^TRANS-COL-TYPE\|COL=(.*?)\|TYPE=(.*)$/)
  {
    $colName = $1;
    $colType = $2;
  }
  else
  {
    return(-1);
  }

  $startTime = PrepUtils_get_current_timestamp($prepDB);

  $query = "SELECT rowCount FROM prep.jobs WHERE ID=$jobID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($rowCount) = $dbOutput->fetchrow_array;
  $rowCount = prep_autoscale_number($rowCount);

  #set the "title" of the operation for UI
  PrepUtils_set_job_op_title($prepDB, $jobID, "Changing column type");
  PrepUtils_set_job_op_details($prepDB, $jobID, "<B>Column:</B> $colName");
  PrepUtils_set_job_op_extra($prepDB, $jobID, "Changing type to $columnTypeNames{$colType} for $rowCount data points");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "101");

  #get the "old" type of the column
  $q_colName = $prepDB->quote($colName);
  $query = "SELECT ID, type FROM $masterColTable WHERE name=$q_colName";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($colID, $oldType) = $dbOutput->fetchrow_array;

  #change the column type
  $query = "UPDATE $masterColTable SET type='$colType' WHERE name=$q_colName";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #turn off SQL binary logging for performance benefits, since we're not
  #actually replicating this data
  $query = "SET sql_log_bin = 0";
  $prepDB->do($query);
  $query = "SET SESSION transaction_isolation='READ-UNCOMMITTED'";
  $prepDB->do($query);

  #if we went from a text field to a measure, change column to double
  if ($colType eq "measure")
  {
    $query = "ALTER TABLE $masterTable ADD xform_tmp DOUBLE DEFAULT NULL";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);

    $query = "UPDATE IGNORE $masterTable
        SET xform_tmp = CAST(column_$colID AS DECIMAL(20,5))";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);

    $query = "ALTER TABLE $masterTable DROP COLUMN column_$colID";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);

    $query = "ALTER TABLE $masterTable
        CHANGE COLUMN xform_tmp column_$colID DOUBLE DEFAULT NULL";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);
  }

  #else if we went from a measure to a text field
  elsif ($oldType eq "measure")
  {
    $query = "ALTER TABLE $masterTable
        CHANGE COLUMN column_$colID column_$colID VARCHAR(127), LOCK=EXCLUSIVE";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);
  }

  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs
        SET PID=NULL, opInfo='DONE', state='LOADED', lastAction=NOW() \
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");
  PrepUtils_set_job_op_details($prepDB, $jobID, "");
  PrepUtils_set_job_op_extra($prepDB, $jobID, "");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "");

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);
  PrepUtils_increment_successful_xforms($prepDB, $jobID);

  flow_telemetry($prepDB, $jobID, "Changed type of $colName to $colType");

  return(0);
}



#-------------------------------------------------------------------------
#
# Apply the "rename" column transform
#

sub prep_recipe_trans_col_name
{
  my ($query, $masterColTable, $dbOutput, $action, $oldColName, $newColName);
  my ($q_oldColName, $q_newColName, $status);

  my ($prepDB, $flowID, $jobID, $stepID, $noUpdate) = @_;


  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs SET PID=$$, opInfo='Renaming column' \
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #build the table name for the master columns table for the job
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #get the details of the action we're performing
  $query = "SELECT action FROM prep.recipes
      WHERE flowID=$flowID AND step=$stepID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($action) = $dbOutput->fetchrow_array;

  #extract our names from the recipe step, error out if bad step
  if ($action =~ m/^TRANS-COL-NAME\|COL=(.*?)\|NAME=(.*)$/)
  {
    $oldColName = $1;
    $newColName = $2;
  }
  else
  {
    return(-1);
  }

  #set the "title" of the operation for UI
  PrepUtils_set_job_op_title($prepDB, $jobID, "Renaming column $oldColName to $newColName");
  $startTime = PrepUtils_get_current_timestamp($prepDB);

  #rename the column
  $q_oldColName = $prepDB->quote($oldColName);
  $q_newColName = $prepDB->quote($newColName);
  $query = "UPDATE $masterColTable SET name=$q_newColName WHERE name=$q_oldColName";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs
        SET PID=NULL, opInfo='DONE', state='LOADED', lastAction=NOW()
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);
  PrepUtils_increment_successful_xforms($prepDB, $jobID);

  flow_telemetry($prepDB, $jobID, "Changed name of $oldColName to $newColName");

  return(0);
}



#-------------------------------------------------------------------------
#
# Apply the "split by separator" column transform
#

sub prep_recipe_trans_col_split
{
  my ($query, $dbOutput, $action, $colName, $sepChar, $otherChar, $column);
  my ($masterTable, $masterColTable, $q_colName, $colID, $val, $maxCols, $i);
  my ($newColName, $masterColID, $idx, $subQ, $tmp, $q_tmp, $q_val, $status);
  my (@vals, @masterCols);
  my ($opInfo, $q_opInfo, $numOps, $opsDone, $pct);
  my (%valHash);

  my ($prepDB, $flowID, $jobID, $stepID, $noUpdate) = @_;


  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs SET PID=$$, opInfo='0|Splitting columns' \
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #get the details of the action we're performing
  $query = "SELECT action FROM prep.recipes
      WHERE flowID=$flowID AND step=$stepID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($action) = $dbOutput->fetchrow_array;

  #extract our transform info from the recipe step, error out if bad step
  if ($action =~ m/^TRANS-COL-SPLIT\|COL=(.*?)\|SEP=(.*?)\|OTHER=(.*)$/)
  {
    $colName = $1;
    $sepChar = $2;
    $otherChar = $3;
  }
  else
  {
    return(-1);
  }

  $startTime = PrepUtils_get_current_timestamp($prepDB);

  $query = "SELECT rowCount FROM prep.jobs WHERE ID=$jobID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($rowCount) = $dbOutput->fetchrow_array;
  $rowCount = prep_autoscale_number($rowCount);

  #set the "title" of the operation for UI
  PrepUtils_set_job_op_title($prepDB, $jobID, "Splitting column by separators");
  PrepUtils_set_job_op_details($prepDB, $jobID, "<B>Column:</B> $colName");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "0");

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #get the ID for the column we're splitting
  $q_colName = $prepDB->quote($colName);
  $query = "SELECT ID FROM $masterColTable WHERE name=$q_colName";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($colID) = $dbOutput->fetchrow_array;
  $column = "column_$colID";

  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs SET opInfo='1|Analyzing column values'
        WHERE ID=$jobID";
    $prepDB->do($query);

    PrepUtils_set_job_op_extra($prepDB, $jobID, "Analyzing column values");
    PrepUtils_set_job_op_pct($prepDB, $jobID, "1");
  }

  #grab every unique string in the column
  $query = "SELECT DISTINCT $column FROM $masterTable
      WHERE $column IS NOT NULL";
  $dbOutput = $prepDB->prepare($query);
  $status = $numOps = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);

  $maxCols = 0;
  while (($val) = $dbOutput->fetchrow_array)
  {

    #split the column value by the user-supplied delimiter
    if ($sepChar eq "comma")
    {
      @vals = split(',', $val);
    }
    elsif ($sepChar eq "space")
    {
      @vals = split(/\s/, $val);
    }
    elsif ($sepChar eq "tab")
    {
      @vals = split(/\t/, $val);
    }
    elsif ($sepChar eq "pipe")
    {
      @vals = split(/\|/, $val);
    }
    elsif ($sepChar eq "other")
    {
      if ($otherChar eq '(')
      {
        @vals = split(m/\(/, $val);
      }
      elsif ($otherChar eq ')')
      {
        @vals = split(m/\)/, $val);
      }
      else
      {
        @vals = split($otherChar, $val);
      }
    }

    #keep track of how many new columns we need to create
    if ($maxCols < scalar(@vals))
    {
      $maxCols = scalar(@vals);
    }

    #add the split values to the hash we'll use to populate the new columns
    @{$valHash{$val}} = @vals;
  }

  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs SET opInfo='2|Adding new columns' WHERE ID=$jobID";
    $prepDB->do($query);

    PrepUtils_set_job_op_extra($prepDB, $jobID, "Adding new columns");
    PrepUtils_set_job_op_pct($prepDB, $jobID, "2");
  }

  #create the new columns
  for ($i = 1; $i <= $maxCols; $i++)
  {
    $newColName = "$colName $i";
    $q_colName = $prepDB->quote($newColName);
    $query = "INSERT INTO $masterColTable (name, type) VALUES ($q_colName, 'pseg')";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);

    #get the ID for the new column in the master column table
    $masterColID = $prepDB->{q{mysql_insertid}};

    #keep track of the new column IDs for later use in SQL UPDATE
    push(@masterCols, "column_$masterColID");
  }

  #build up the ALTER TABLE query for the master table
  $query = "ALTER TABLE $masterTable ";
  foreach $colName (@masterCols)
  {
    $query .= "ADD COLUMN $colName VARCHAR(127),";
  }
  chop($query);
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #turn off SQL binary logging for performance benefits, since we're not
  #actually replicating this data
  $query = "SET sql_log_bin = 0";
  $prepDB->do($query);
  $query = "SET SESSION transaction_isolation='READ-UNCOMMITTED'";
  $prepDB->do($query);

  #use the hash of value arrays to populate the new columns
  PrepUtils_set_job_op_extra($prepDB, $jobID, "Splitting $rowCount data fields");
  $opsDone = 0;
  foreach $val (keys %valHash)
  {

    #update detailed status info
    $opsDone++;
    if (($noUpdate < 1) && (($opsDone % 10) == 0))
    {
      $pct = ($opsDone / $numOps) * 100;
      $pct = int($pct);

      $opInfo = "$pct|Populating new columns";
      $q_opInfo = $prepDB->quote($opInfo);
      $query = "UPDATE prep.jobs SET opInfo=$q_opInfo WHERE ID=$jobID";
      $prepDB->do($query);
      PrepUtils_set_job_op_pct($prepDB, $jobID, "$pct");
    }

    #build the col=val subquery for our UPDATE statement
    $idx = 0;
    $subQ = "";
    foreach $tmp (@{$valHash{$val}})
    {
      $q_tmp = $prepDB->quote($tmp);
      $subQ .= "$masterCols[$idx] = $q_tmp,";
      $idx++;
    }
    chop($subQ);

    $q_val = $prepDB->quote($val);
    $query = "UPDATE $masterTable SET $subQ WHERE $column = $q_val";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);
  }

  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs
        SET PID=NULL, opInfo='DONE', state='LOADED', lastAction=NOW()
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");
  PrepUtils_set_job_op_details($prepDB, $jobID, "");
  PrepUtils_set_job_op_extra($prepDB, $jobID, "");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "");

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);
  PrepUtils_increment_successful_xforms($prepDB, $jobID);

  flow_telemetry($prepDB, $jobID, "Split $colName on $sepChar delimiter");

  return(0);
}



#-------------------------------------------------------------------------
#
# Apply the "split column by field length" column transform
#

sub prep_recipe_trans_col_split_length
{
  my ($query, $dbOutput, $action, $colName, $fieldSpec, $masterTable, $val);
  my ($masterColTable, $q_colName, $colID, $column, $start, $length, $tmp, $i);
  my ($newColName, $masterColID, $idx, $subQ, $q_tmp, $q_val, $status);
  my ($numOps, $opsDone, $pct, $opInfo, $q_opInfo);
  my (@lengths, @vals, @masterCols);
  my (%valHash);

  my ($prepDB, $flowID, $jobID, $stepID, $noUpdate) = @_;


  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs SET PID=$$, opInfo='0|Splitting column by length'
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #get the details of the action we're performing
  $query = "SELECT action FROM prep.recipes WHERE flowID=$flowID AND step=$stepID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($action) = $dbOutput->fetchrow_array;

  #extract our transform info from the recipe step, error out if bad step
  if ($action =~ m/^TRANS-COL-SPLIT-LENGTH\|COL=(.*?)\|FIELDS=(.*)$/)
  {
    $colName = $1;
    $fieldSpec = $2;
  }
  else
  {
    return(-1);
  }

  $startTime = PrepUtils_get_current_timestamp($prepDB);

  $query = "SELECT rowCount FROM prep.jobs WHERE ID=$jobID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($rowCount) = $dbOutput->fetchrow_array;
  $rowCount = prep_autoscale_number($rowCount);

  #set the "title" of the operation for UI
  PrepUtils_set_job_op_title($prepDB, $jobID, "Splitting $colName by field lengths");
  PrepUtils_set_job_op_details($prepDB, $jobID, "<B>Column:</B> $colName");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "0");

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #get the ID for the column we're splitting
  $q_colName = $prepDB->quote($colName);
  $query = "SELECT ID FROM $masterColTable WHERE name=$q_colName";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($colID) = $dbOutput->fetchrow_array;
  $column = "column_$colID";

  #get the length of each field from the user-supplied spec
  @lengths = split(',', $fieldSpec);

  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs SET opInfo='1|Analyzing column values'
        WHERE ID=$jobID";
    $prepDB->do($query);

    PrepUtils_set_job_op_extra($prepDB, $jobID, "Analyzing column values");
    PrepUtils_set_job_op_pct($prepDB, $jobID, "1");
  }

  #grab every unique string in the column
  $query = "SELECT DISTINCT $column FROM $masterTable WHERE $column IS NOT NULL";
  $dbOutput = $prepDB->prepare($query);
  $status = $numOps = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);

  while (($val) = $dbOutput->fetchrow_array)
  {

    #for each field length, split the value into multiple new columns
    #NB: substr assumes 0-indexed values
    $start = 0;
    undef(@vals);
    foreach $length (@lengths)
    {
      $tmp = substr($val, $start, $length);
      push(@vals, $tmp);

      $start += $length;      #set start for next field
    }

    #add the split values to the hash we'll use to populate the new columns
    @{$valHash{$val}} = @vals;
  }

  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs SET opInfo='2|Adding new columns' WHERE ID=$jobID";
    $prepDB->do($query);

    PrepUtils_set_job_op_extra($prepDB, $jobID, "Adding new columns");
    PrepUtils_set_job_op_pct($prepDB, $jobID, "2");
  }

  #create the new columns
  for ($i = 1; $i <= scalar(@lengths); $i++)
  {
    $newColName = "$colName $i";
    $q_colName = $prepDB->quote($newColName);
    $query = "INSERT INTO $masterColTable (name, type) VALUES ($q_colName, 'pseg')";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);

    #get the ID for the new column in the master column table
    $masterColID = $prepDB->{q{mysql_insertid}};

    #keep track of the new column IDs for later use in SQL UPDATE
    push(@masterCols, "column_$masterColID");
  }

  #turn off SQL binary logging for performance benefits, since we're not
  #actually replicating this data
  $query = "SET sql_log_bin = 0";
  $prepDB->do($query);
  $query = "SET SESSION transaction_isolation='READ-UNCOMMITTED'";
  $prepDB->do($query);

  #build up the ALTER TABLE query for the master table
  $query = "ALTER TABLE $masterTable ";
  foreach $colName (@masterCols)
  {
    $query .= "ADD COLUMN $colName VARCHAR(127),";
  }
  chop($query);
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #use the hash of value arrays to populate the new columns
  PrepUtils_set_job_op_extra($prepDB, $jobID, "Splitting $rowCount data fields");
  $opsDone = 0;
  foreach $val (keys %valHash)
  {

    #update detailed status info
    $opsDone++;
    if (($noUpdate < 1) && (($opsDone % 10) == 0))
    {
      $pct = ($opsDone / $numOps) * 100;
      $pct = int($pct);

      $opInfo = "$pct|Populating new columns";
      $q_opInfo = $prepDB->quote($opInfo);
      $query = "UPDATE prep.jobs SET opInfo=$q_opInfo WHERE ID=$jobID";
      $prepDB->do($query);
      PrepUtils_set_job_op_pct($prepDB, $jobID, "$pct");
    }

    #build the col=val subquery for our UPDATE statement
    $idx = 0;
    $subQ = "";
    foreach $tmp (@{$valHash{$val}})
    {
      $q_tmp = $prepDB->quote($tmp);
      $subQ .= "$masterCols[$idx] = $q_tmp,";
      $idx++;
    }
    chop($subQ);

    $q_val = $prepDB->quote($val);
    $query = "UPDATE $masterTable SET $subQ WHERE $column = $q_val";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);
  }

  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs
        SET PID=NULL, opInfo='DONE', state='LOADED', lastAction=NOW()
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");
  PrepUtils_set_job_op_details($prepDB, $jobID, "");
  PrepUtils_set_job_op_extra($prepDB, $jobID, "");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "");

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);
  PrepUtils_increment_successful_xforms($prepDB, $jobID);

  flow_telemetry($prepDB, $jobID, "Split $colName on field lengths");

  return(0);
}



#-------------------------------------------------------------------------
#
# Apply the "lookup table" column transform
#

sub prep_recipe_trans_col_lookup
{
  my ($query, $dbOutput, $action, $colName, $lookupFile, $masterTable);
  my ($masterColTable, $q_colName, $colID, $column, $lookupTable, $lookupTab);
  my ($lookupColTable, $lookupColName, $lookupSubQ, $masterSubQ, $idx);
  my ($lookupColID, $masterColID, $val, $q_val, $db_match, $status, $subQ);
  my ($q_lookupFile, $q_lookupTab, $tabSQL, $masterColName, $colType, $pct);
  my ($q_colType, $lookupsTotal, $opInfo, $q_opInfo, $statusDB);
  my (@masterCols, @lookupVals, %lookupsSQL, $lookupsDone);
  my (%dataKeys);

  my ($prepDB, $flowID, $jobID, $stepID, $noUpdate) = @_;


  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs SET PID=$$, opInfo='0|Looking up column values'
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #get the details of the action we're performing
  $query = "SELECT action FROM prep.recipes WHERE flowID=$flowID AND step=$stepID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($action) = $dbOutput->fetchrow_array;

  #extract our transform info from the recipe step, error out if bad step
  if ($action =~ m/^TRANS-COL-LOOKUP\|COL=(.*?)\|TABLE=(.*)$/)
  {
    $colName = $1;
    $lookupFile = $2;
  }
  else
  {
    return(-1);
  }

  $startTime = PrepUtils_get_current_timestamp($prepDB);

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #get the ID for the column we're transforming
  $q_colName = $prepDB->quote($colName);
  $query = "SELECT ID FROM $masterColTable WHERE name=$q_colName";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($colID) = $dbOutput->fetchrow_array;
  $column = "column_$colID";

  #get our lookup file and tab
  $lookupFile =~ m/^(.*?)\|TAB=(.*)$/;
  $lookupFile = $1;
  $lookupTab = $2;

  #set the "title" of the operation for UI
  PrepUtils_set_job_op_title($prepDB, $jobID, "Looking up values for $colName");

  #convert our lookup table name and tab name to IDs (if they exist)
  $q_lookupFile = $prepDB->quote($lookupFile);
  if (length($lookupTab) > 0)
  {
    $q_lookupTab = $prepDB->quote($lookupTab);
    $tabSQL = "tabName = $q_lookupTab";
  }
  else
  {
    $tabSQL = "ISNULL(tabName)";
  }
  $query = "SELECT ID, tabID FROM prep.files
      WHERE userFilename = $q_lookupFile AND $tabSQL AND jobID=$jobID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($lookupFile, $lookupTab) = $dbOutput->fetchrow_array;

  #make sure we found matching IDs
  if ($lookupFile < 1)
  {
    return(-1);
  }

  $lookupTable = "prep_data.$jobID" . "_" . $lookupFile . "_" . $lookupTab;
  $lookupColTable = "prep_data.$jobID" . "_" . $lookupFile . "_" . $lookupTab . "_cols";

  #get number of records in job for UI display
  $query = "SELECT rowCount FROM prep.jobs WHERE ID=$jobID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($rowCount) = $dbOutput->fetchrow_array;
  $recordCount = prep_autoscale_number($rowCount);

  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs SET opInfo='1|Adding new columns for lookup data'
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #get the columns in the lookup table (besides the key column)
  $query = "SELECT ID, name FROM $lookupColTable WHERE ID > 0 ORDER BY ID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #create columns in the master table for each column in the lookup table,
  #maintaining a mapping between the two IDs as we go
  $lookupSubQ = "";
  $masterSubQ = "";
  while (($lookupColID, $lookupColName) = $dbOutput->fetchrow_array)
  {

    #get the new column's CPG data type
    $colType = prep_flow_detect_column_type_by_name($lookupColName);
    if (length($colType) < 1)
    {
      $colType = "pseg";
    }

    #create a new column in the master table for the lookup column
    $q_colName = $prepDB->quote($lookupColName);
    $q_colType = $prepDB->quote($colType);
    $query = "INSERT INTO $masterColTable (name, type)
        VALUES ($q_colName, $q_colType)";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);

    #get the ID for the new column in the master column table
    $masterColID = $prepDB->{q{mysql_insertid}};

    #subqueries we're going to use to match up table columns in a bit
    $lookupSubQ .= "column_" . $lookupColID . ",";
    $masterSubQ .= "column_" . $masterColID . ",";
    push(@masterCols, "column_$masterColID");
  }
  chop($lookupSubQ);
  chop($masterSubQ);

  $colCount = scalar(@masterCols);
  PrepUtils_set_job_op_details($prepDB, $jobID, "Adding $colCount new columns for lookup data");
  PrepUtils_set_job_op_pct($prepDB, $jobID, 1);

  #turn off SQL binary logging for performance benefits, since we're not
  #actually replicating this data
  $query = "SET sql_log_bin = 0";
  $prepDB->do($query);
  $query = "SET SESSION transaction_isolation='READ-UNCOMMITTED'";
  $prepDB->do($query);

  #build up the ALTER TABLE query for the master table
  $query = "ALTER TABLE $masterTable ";
  foreach $colName (@masterCols)
  {
    $query .= "ADD COLUMN $colName VARCHAR(127), ";
  }

  $query .= " LOCK=EXCLUSIVE";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs SET opInfo='2|Analyzing lookup values' WHERE ID=$jobID";
    $prepDB->do($query);
  }
  PrepUtils_set_job_op_details($prepDB, $jobID, "Analyzing lookup values");
  PrepUtils_set_job_op_pct($prepDB, $jobID, 2);

  #if there isn't already an index on the match column, create one
  $tmp = $jobID . "_master";
  $query = "SELECT * FROM information_schema.statistics WHERE table_schema='prep_data'
    AND table_name='$tmp' AND column_name='$column';";
  $dbOutput = $prepDB->prepare($query);
  $status = $lookupsTotal = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  if ($status < 1)
  {
    $query = "CREATE INDEX idx_$column ON $masterTable ($column)
        ALGORITHM=INPLACE LOCK=EXCLUSIVE";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);
  }

  #grab every unique key in the match column from the master table
  undef(%dataKeys);
  $query = "SELECT DISTINCT $column FROM $masterTable WHERE $column IS NOT NULL";
  $dbOutput = $prepDB->prepare($query);
  $status = $lookupsTotal = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  while (($val) = $dbOutput->fetchrow_array)
  {
    $dataKeys{$val} = 1;
  }

  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs SET opInfo='2|Performing lookups' WHERE ID=$jobID";
    $prepDB->do($query);
  }
  PrepUtils_set_job_op_details($prepDB, $jobID, "Collating lookup values");

  #create an array of the SQL UPDATE statements
  foreach $val (keys %dataKeys)
  {

    #grab matching values from lookup table
    $q_val = $prepDB->quote($val);
    $query = "SELECT $lookupSubQ FROM $lookupTable WHERE column_0 = $q_val";
    $db_match = $prepDB->prepare($query);
    $status = $db_match->execute;
    PrepUtils_handle_db_err($prepDB, $status, $query);

    #if we found a match
    if ($status > 0)
    {

      #get the matching values
      (@lookupVals) = $db_match->fetchrow_array;

      #quote all of the matching values
      @lookupVals = map{$prepDB->quote($_)} @lookupVals;

      #build the col=val subquery for the SQL UPDATE statement
      $idx = 0;
      $subQ = "";
      foreach $masterColName (@masterCols)
      {

        #if we have a value for the current column in the lookup table
        if ($lookupVals[$idx] ne "''")
        {
          $subQ .= "$masterColName=$lookupVals[$idx],";
        }
        $idx++;
      }
      chop($subQ);

      $query = "UPDATE $masterTable SET $subQ WHERE $column = $q_val";
      $lookupsSQL{$val} = $query;
    }
  }

  #cycle through the lookup UPDATE statements
  #NB: we're going to do 20 UPDATEs inside a transaction to try to avoid a
  #    bunch of small InnoDB buffer flushes to disk

  #create a separate DB connection to use for updating user status table
  #(otherwise we have to do crazy locking synchronization)
  $statusDB = PrepUtils_connect_to_database();

  #turn off autocommit so InnoDB won't auto-flush after each statement
  $prepDB->do("START TRANSACTION");
  foreach $val (keys %lookupsSQL)
  {

    if ($lookupsDone % 10 == 0)
    {
      PrepUtils_set_job_op_details($statusDB, $jobID, "Adding lookup data for <TT>$val</TT> on $recordCount records");
      $pct = (($lookupsDone / $lookupsTotal) * 98) + 2;
      $pct = int($pct);
      PrepUtils_set_job_op_pct($statusDB, $jobID, $pct);
      $todoCount = $lookupsTotal - $lookupsDone;
      $todoCount = prep_autoscale_number($todoCount);
      PrepUtils_set_job_op_extra($statusDB, $jobID, "<B>Lookups remaining:</B> $todoCount");
    }

    #do the actual lookup query
    $query = $lookupsSQL{$val};
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);

    $lookupsDone++;

    #commit the changes every 20 UPDATE statements
    if (($lookupsDone % 20) == 0)
    {
      $prepDB->do("COMMIT");
      $prepDB->do("START TRANSACTION");
    }

    #update detailed status info
    if (($noUpdate < 1) && (($lookupsDone % 10) == 0))
    {
      $pct = ($lookupsDone / $lookupsTotal) * 100;
      $pct = int($pct);

      $opInfo = "$pct|Performing lookups";
      $q_opInfo = $statusDB->quote($opInfo);
      $query = "UPDATE prep.jobs SET opInfo=$q_opInfo WHERE ID=$jobID";
      $statusDB->do($query);
    }
  }

  #commit anything remaining and turn auto-commit back on
  $prepDB->do("COMMIT");

  $statusDB->disconnect;

  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs
        SET PID=NULL, opInfo='DONE', state='LOADED', lastAction=NOW()
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #clear detailed op stats
  PrepUtils_set_job_op_details($prepDB, $jobID, "");
  PrepUtils_set_job_op_title($prepDB, $jobID, "");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "");
  PrepUtils_set_job_op_extra($prepDB, $jobID, "");

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);
  PrepUtils_increment_successful_xforms($prepDB, $jobID);

  flow_telemetry($prepDB, $jobID, "Did lookup on $colName in $lookupFile");

  return(0);
}



#-------------------------------------------------------------------------
#
# Apply the "insert" column transform
#

sub prep_recipe_trans_col_insert
{
  my ($query, $dbOutput, $action, $colType, $colName, $newColName, $status);
  my ($masterTable, $masterColTable, $q_colName, $masterColID, $newColumn);

  my ($prepDB, $flowID, $jobID, $stepID, $noUpdate) = @_;


  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs SET PID=$$, opInfo='-|Inserting new column'
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #get the details of the action we're performing
  $query = "SELECT action FROM prep.recipes WHERE flowID=$flowID AND step=$stepID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($action) = $dbOutput->fetchrow_array;

  #extract our transform info from the recipe step, error out if bad step
  if ($action =~ m/^TRANS-COL-INSERT\|COL=(.*?)\|TYPE=(.*)$/)
  {
    $newColName = $1;
    $colType = $2;
  }
  else
  {
    return(-1);
  }

  $startTime = PrepUtils_get_current_timestamp($prepDB);

  #set the "title" of the operation for UI
  PrepUtils_set_job_op_title($prepDB, $jobID, "Inserting new column $colName");

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #insert the new column and set type
  $q_colName = $prepDB->quote($newColName);
  $query = "INSERT INTO $masterColTable (name, type) VALUES ($q_colName, '$colType')";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #get the ID for the new column in the master column table
  $masterColID = $prepDB->{q{mysql_insertid}};
  $newColumn = "column_" . $masterColID;

  #add the new column to the master data table
  if ($colType eq "measure")
  {
    $query = "ALTER TABLE $masterTable
        ADD COLUMN column_$masterColID DOUBLE";
  }
  else
  {
    $query = "ALTER TABLE $masterTable
        ADD COLUMN column_$masterColID VARCHAR(127)";
  }
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs
        SET PID=NULL, opInfo='DONE', state='LOADED', lastAction=NOW()
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");
  PrepUtils_increment_successful_xforms($prepDB, $jobID);

  flow_telemetry($prepDB, $jobID, "Inserted new column $newColName");

  return(0);
}



#-------------------------------------------------------------------------
#
# Apply the "copy" column transform
#

sub prep_recipe_trans_col_copy
{
  my ($query, $dbOutput, $action, $colType, $colName, $operation, $numVal);
  my ($prepend, $append, $masterTable, $masterColTable, $q_colName, $colID);
  my ($column, $newColName, $masterColID, $newColumn, $status, $q_prepend);
  my ($q_append);

  my ($prepDB, $flowID, $jobID, $stepID, $noUpdate) = @_;


  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs SET PID=$$, opInfo='0|Copying column'
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #get the details of the action we're performing
  $query = "SELECT action FROM prep.recipes WHERE flowID=$flowID AND step=$stepID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($action) = $dbOutput->fetchrow_array;

  #extract our transform info from the recipe step, error out if bad step
  if ($action =~ m/^TRANS-COL-COPY\|COL=(.*?)\|OP=(.*?)\|NUM=(.*)$/)
  {
    $colType = "measure";
    $colName = $1;
    $operation = $2;
    $numVal = $3;
  }
  elsif ($action =~ m/^TRANS-COL-COPY\|COL=(.*?)\|PRE=(.*?)\|APP=(.*)$/)
  {
    $colType = "text";
    $colName = $1;
    $prepend = $2;
    $append = $3;
  }
  else
  {
    return(-1);
  }

  $startTime = PrepUtils_get_current_timestamp($prepDB);

  #set the "title" of the operation for UI
  PrepUtils_set_job_op_title($prepDB, $jobID, "Copying column $colName");

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #get the ID for the column we're transforming
  $q_colName = $prepDB->quote($colName);
  $query = "SELECT ID FROM $masterColTable WHERE name=$q_colName";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($colID) = $dbOutput->fetchrow_array;
  $column = "column_$colID";

  #turn off SQL binary logging for performance benefits, since we're not
  #actually replicating this data
  $query = "SET sql_log_bin = 0";
  $prepDB->do($query);
  $query = "SET SESSION transaction_isolation='READ-UNCOMMITTED'";
  $prepDB->do($query);

  #insert the new column and populate with values based on basis column

  #if it's a measure column
  if ($colType eq "measure")
  {
    #add the new column to the job
    $newColName = "$colName 1";
    $q_colName = $prepDB->quote($newColName);
    $query = "INSERT INTO $masterColTable (name, type)
        VALUES ($q_colName, 'measure')";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);

    #get the ID for the new column in the master column table
    $masterColID = $prepDB->{q{mysql_insertid}};
    $newColumn = "column_" . $masterColID;

    #add the new column to the master data table
    $query = "ALTER TABLE $masterTable
        ADD COLUMN column_$masterColID DOUBLE";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);

    if ($operation eq "add")
    {
      $operation = "+";
    }
    elsif ($operation eq "sub")
    {
      $operation = "-";
    }
    elsif ($operation eq "mul")
    {
      $operation = "*";
    }
    elsif ($operation eq "div")
    {
      $operation = "/";
    }

    if (length($numVal) < 1)
    {
      $operation = "";
    }

    if ($noUpdate < 1)
    {
      $query = "UPDATE prep.jobs SET opInfo='50|Populating column data'
          WHERE ID=$jobID";
      $prepDB->do($query);
    }

    #calculate the values for the new column
    $query = "UPDATE $masterTable SET $newColumn = $column $operation $numVal";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);
  }

  #else we're dealing with a non-measure column
  else
  {

    #add the new column to the job
    $newColName = "$colName 1";
    $q_colName = $prepDB->quote($newColName);
    $query = "INSERT INTO $masterColTable (name, type) VALUES ($q_colName, 'pseg')";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);

    #get the ID for the new column in the master column table
    $masterColID = $prepDB->{q{mysql_insertid}};
    $newColumn = "column_" . $masterColID;

    #add the new column to the master data table
    $query = "ALTER TABLE $masterTable
        ADD COLUMN column_$masterColID VARCHAR(127)";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);

    if ($noUpdate < 1)
    {
      $query = "UPDATE prep.jobs SET opInfo='50|Populating column data'
          WHERE ID=$jobID";
      $prepDB->do($query);
    }

    #set the values for the new column
    $q_prepend = $prepDB->quote($prepend);
    $q_append = $prepDB->quote($append);
    $query = "UPDATE $masterTable
        SET $newColumn = CONCAT($q_prepend, $column, $q_append)";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);
  }

  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs
        SET PID=NULL, opInfo='DONE', state='LOADED', lastAction=NOW()
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);
  PrepUtils_increment_successful_xforms($prepDB, $jobID);

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");

  flow_telemetry($prepDB, $jobID, "Copied column based on $colName");

  return(0);
}



#-------------------------------------------------------------------------
#
# Apply the "merge" column transform
#

sub prep_recipe_trans_col_merge
{
  my ($query, $dbOutput, $action, $colName, $mergeColName, $innerText);
  my ($masterTable, $masterColTable, $q_colName, $colID, $column, $status);
  my ($q_mergeColName, $mergeCol, $mergeColumn, $q_innerText, $newColName);
  my ($q_newColName, $newColID, $newCol);

  my ($prepDB, $flowID, $jobID, $stepID, $noUpdate) = @_;


  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs SET PID=$$, opInfo='-|Merging columns'
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #get the details of the action we're performing
  $query = "SELECT action FROM prep.recipes WHERE flowID=$flowID AND step=$stepID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($action) = $dbOutput->fetchrow_array;

  #extract our transform info from the recipe step, error out if bad step
  if ($action =~ m/^TRANS-COL-MERGE\|COL=(.*?)\|MERGE=(.*?)\|INNER=(.*)$/)
  {
    $colName = $1;
    $mergeColName = $2;
    $innerText = $3;
  }
  else
  {
    return(-1);
  }

  $startTime = PrepUtils_get_current_timestamp($prepDB);

  #set the "title" of the operation for UI
  PrepUtils_set_job_op_title($prepDB, $jobID, "Merging columns $colName and $mergeColName");

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #get the ID for the 1st column we're merging
  $q_colName = $prepDB->quote($colName);
  $query = "SELECT ID FROM $masterColTable WHERE name=$q_colName";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($colID) = $dbOutput->fetchrow_array;
  $column = "column_$colID";

  #get the ID for the 2nd column we're merging
  $q_mergeColName = $prepDB->quote($mergeColName);
  $query = "SELECT ID FROM $masterColTable WHERE name=$q_mergeColName";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($mergeCol) = $dbOutput->fetchrow_array;

  #create the new column we're merging into
  $newColName = "$colName $mergeColName";
  $q_newColName = $prepDB->quote($newColName);
  $query = "INSERT INTO $masterColTable (name, type) VALUES ($q_newColName, 'pseg')";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);
  $newColID = $prepDB->{q{mysql_insertid}};
  $newCol = "column_$newColID";

  $query = "ALTER TABLE $masterTable ADD COLUMN $newCol VARCHAR(127)";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #turn off SQL binary logging for performance benefits, since we're not
  #actually replicating this data
  $query = "SET sql_log_bin = 0";
  $prepDB->do($query);
  $query = "SET SESSION transaction_isolation='READ-UNCOMMITTED'";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #perform the column merge transfor
  $mergeColumn = "column_" . $mergeCol;
  $q_innerText = $prepDB->quote($innerText);

  #merge the two columns together
  $query = "UPDATE IGNORE $masterTable
      SET $newCol = CONCAT($column, $q_innerText, $mergeColumn)";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs
        SET PID=NULL, opInfo='DONE', state='LOADED', lastAction=NOW()
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);
  PrepUtils_increment_successful_xforms($prepDB, $jobID);

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");

  flow_telemetry($prepDB, $jobID, "Merged $colName and $mergeColName");

  return(0);
}



#-------------------------------------------------------------------------
#
# Apply the "discard" column transform
#

sub prep_recipe_trans_col_discard
{
  my ($query, $dbOutput, $action, $colName, $masterTable, $masterColTable);
  my ($q_colName, $colID, $column, $status);

  my ($prepDB, $flowID, $jobID, $stepID, $noUpdate) = @_;


  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs SET PID=$$, opInfo='-|Discarding column'
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #get the details of the action we're performing
  $query = "SELECT action FROM prep.recipes WHERE flowID=$flowID AND step=$stepID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($action) = $dbOutput->fetchrow_array;

  #extract our transform info from the recipe step, error out if bad step
  if ($action =~ m/^TRANS-COL-DISCARD\|COL=(.*)$/)
  {
    $colName = $1;
  }
  else
  {
    return(-1);
  }

  $startTime = PrepUtils_get_current_timestamp($prepDB);

  $query = "SELECT rowCount FROM prep.jobs WHERE ID=$jobID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($rowCount) = $dbOutput->fetchrow_array;
  $rowCount = prep_autoscale_number($rowCount);

  #set the "title" of the operation for UI
  PrepUtils_set_job_op_title($prepDB, $jobID, "Discarding column $colName");
  PrepUtils_set_job_op_extra($prepDB, $jobID, "Discarding $rowCount data points");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "50");

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #get the ID/name for the column we're dropping
  $q_colName = $prepDB->quote($colName);
  $query = "SELECT ID FROM $masterColTable WHERE name=$q_colName";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($colID) = $dbOutput->fetchrow_array;
  $column = "column_$colID";

  #discard the column
  $query = "DELETE FROM $masterColTable WHERE ID=$colID";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  $query = "ALTER TABLE $masterTable DROP COLUMN $column";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs
        SET PID=NULL, opInfo='DONE', state='LOADED', lastAction=NOW()
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");
  PrepUtils_set_job_op_details($prepDB, $jobID, "");
  PrepUtils_set_job_op_extra($prepDB, $jobID, "");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "");

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);
  PrepUtils_increment_successful_xforms($prepDB, $jobID);

  flow_telemetry($prepDB, $jobID, "Discarded column $colName");

  return(0);
}



#-------------------------------------------------------------------------
#
# Apply the "discard empties" column transform
#

sub prep_recipe_trans_col_discard_empty
{
  my ($query, $dbOutput, $action, $colName, $masterTable, $masterColTable);
  my ($status, $colID, $column);
  my (@checkCols);

  my ($prepDB, $flowID, $jobID, $stepID, $noUpdate) = @_;


  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs SET PID=$$, opInfo='-|Discarding empty columns'
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #get the details of the action we're performing
  $query = "SELECT action FROM prep.recipes WHERE flowID=$flowID AND step=$stepID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($action) = $dbOutput->fetchrow_array;

  #extract our transform info from the recipe step, error out if bad step
  if ($action =~ m/^TRANS-COL-DISCARD-EMPTY\|$/)
  {
    $colName = $1;
  }
  else
  {
    return(-1);
  }

  $startTime = PrepUtils_get_current_timestamp($prepDB);

  #set the "title" of the operation for UI
  PrepUtils_set_job_op_title($prepDB, $jobID, "Discarding empty columns");

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #grab the list of all segment/attribute columns in the job
  $query = "SELECT ID FROM $masterColTable
      WHERE type LIKE '%seg' OR type LIKE '%attr'";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  while (($colID) = $dbOutput->fetchrow_array)
  {
    push(@checkCols, $colID);
  }

  #cycle through seg/attr columns, looking for fields with no real data in them
  foreach $colID (@checkCols)
  {
    $column = "column_" . $colID;

    #look for any non-blank field in the column
    $query = "SELECT $column FROM $masterTable
        WHERE NOT ISNULL($column) AND $column NOT IN ('NOT STATED', 'NOT APPLICABLE', 'NA')
        LIMIT 1";
    $dbOutput = $prepDB->prepare($query);
    $status = $dbOutput->execute;
    PrepUtils_handle_db_err($prepDB, $status, $query);

    #if we didn't find any non-blank data, delete the column
    if ($status < 1)
    {
      $query = "DELETE FROM $masterColTable WHERE ID=$colID";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);

      $query = "ALTER TABLE $masterTable DROP COLUMN $column";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);
    }
  }

  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs \
        SET PID=NULL, opInfo='DONE', state='LOADED', lastAction=NOW()
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);
  PrepUtils_increment_successful_xforms($prepDB, $jobID);

  flow_telemetry($prepDB, $jobID, "Discarded empty columns");

  return(0);
}



#-------------------------------------------------------------------------
#
# Apply the "trim values" column transform
#

sub prep_recipe_trans_col_trim_values
{
  my ($query, $dbOutput, $action, $colName, $matchOp, $matchText, $status);
  my ($masterTable, $masterColTable, $q_colName, $colID, $column, $matchSQL);
  my ($trimOp, $rowCount);

  my ($prepDB, $flowID, $jobID, $stepID, $noUpdate) = @_;


  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs SET PID=$$, opInfo='-|Trimming data' WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #get the details of the action we're performing
  $query = "SELECT action FROM prep.recipes WHERE flowID=$flowID AND step=$stepID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($action) = $dbOutput->fetchrow_array;

  #extract our transform info from the recipe step, error out if bad step
  if ($action =~ m/^TRANS-COL-TRIM-VALUES\|COL=(.*)\|OP=(.*?)\|MATCH=(.*)\|VALUE=(.*)$/)
  {
    $colName = $1;
    $trimOp = $2;
    $matchOp = $3;
    $matchText = $4;
  }
  else
  {
    return(-1);
  }

  $startTime = PrepUtils_get_current_timestamp($prepDB);

  #set the "title" of the operation for UI
  PrepUtils_set_job_op_title($prepDB, $jobID, "Trimming values from $colName");

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #get the ID for the column we're using to trim data
  $q_colName = $prepDB->quote($colName);
  $query = "SELECT ID FROM $masterColTable WHERE name=$q_colName";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($colID) = $dbOutput->fetchrow_array;
  $column = "column_$colID";

  #build up the SQL match string we're going to use to discard
  $matchSQL = "";
  if ($matchOp eq "begins")
  {
    $matchSQL = $matchText . "%";
    $matchSQL = $prepDB->quote($matchSQL);
    $matchSQL = "LIKE $matchSQL";
  }
  elsif ($matchOp eq "ends")
  {
    $matchSQL = "%" . $matchText;
    $matchSQL = $prepDB->quote($matchSQL);
    $matchSQL = "LIKE $matchSQL";
  }
  elsif ($matchOp eq "contains")
  {
    $matchSQL = "%" . $matchText . "%";
    $matchSQL = $prepDB->quote($matchSQL);
    $matchSQL = "LIKE $matchSQL";
  }
  elsif ($matchOp eq "is")
  {
    $matchSQL = $matchText;
    $matchSQL = $prepDB->quote($matchSQL);
    $matchSQL = "= $matchSQL";
  }
  elsif ($matchOp eq "does not begin")
  {
    $matchSQL = $matchText . "%";
    $matchSQL = $prepDB->quote($matchSQL);
    $matchSQL = "NOT LIKE $matchSQL";
  }
  elsif ($matchOp eq "does not end")
  {
    $matchSQL = "%" . $matchText;
    $matchSQL = $prepDB->quote($matchSQL);
    $matchSQL = "NOT LIKE $matchSQL";
  }
  elsif ($matchOp eq "does not contain")
  {
    $matchSQL = "%" . $matchText . "%";
    $matchSQL = $prepDB->quote($matchSQL);
    $matchSQL = "NOT LIKE $matchSQL";
  }

  #if the user wants us to keep matching entries, we just need to reverse
  #the conditional in our match SQL
  if ($trimOp eq "keep")
  {
    if ($matchSQL =~ m/^NOT (.*)$/)
    {
      $matchSQL = $1;
    }
    else
    {
      $matchSQL = "NOT $matchSQL";
    }
  }

  #turn off SQL binary logging for performance benefits, since we're not
  #actually replicating this data
  $query = "SET sql_log_bin = 0";
  $prepDB->do($query);
  $query = "SET SESSION transaction_isolation='READ-UNCOMMITTED'";
  $prepDB->do($query);

  #discard rows that match the trim condition
  $query = "DELETE FROM $masterTable WHERE $column $matchSQL LIMIT 10000";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);
  while ($status > 0)
  {
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);
  }

  #calculate the new number of data rows in the master table, and store it
  $query = "SELECT COUNT(*) FROM $masterTable";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($rowCount) = $dbOutput->fetchrow_array;

  $query = "UPDATE prep.jobs SET rowCount=$rowCount WHERE ID=$jobID";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs
        SET PID=NULL, opInfo='DONE', state='LOADED', lastAction=NOW()
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);
  PrepUtils_increment_successful_xforms($prepDB, $jobID);

  flow_telemetry($prepDB, $jobID, "Trimmed values based on $colName");

  return(0);
}



#-------------------------------------------------------------------------
#
# Apply the "trim data" column transform
#

sub prep_recipe_trans_col_trim_data
{
  my ($query, $dbOutput, $colName, $matchOp, $masterTable, $action, $val);
  my ($dataValues, $masterColTable, $q_colName, $colID, $column, $q_val);
  my ($opInfo, $q_opInfo, $numOps, $opsDone, $pct, $rowCount, $status);
  my ($hugeTrim, $idxUPCCol, $idxGeoCol, $idxTimeCol, $csv, $trimValues);
  my ($tmpTable, $q_values);
  my (@values, @tmp, @allValues, @delArray);
  my (%valueHash);

  my ($prepDB, $flowID, $jobID, $stepID, $noUpdate) = @_;


  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs SET PID=$$, opInfo='0|Trimming data' WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #get the details of the action we're performing
  $query = "SELECT action FROM prep.recipes WHERE flowID=$flowID AND step=$stepID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($action) = $dbOutput->fetchrow_array;

  #extract our transform info from the recipe step, error out if bad step
  if ($action =~ m/^TRANS-COL-TRIM-DATA\|COL=(.*)\|OP=(.*)\|DATA=(.*)$/)
  {
    $colName = $1;
    $matchOp = $2;
    $dataValues = $3;
  }
  else
  {
    return(-1);
  }

  $startTime = PrepUtils_get_current_timestamp($prepDB);

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";
  $tmpTable = "prep_data.$jobID" . "_tmp";

  #get the ID for the column we're using to trim data
  $q_colName = $prepDB->quote($colName);
  $query = "SELECT ID FROM $masterColTable WHERE name=$q_colName";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($colID) = $dbOutput->fetchrow_array;
  $column = "column_$colID";

  #split the data value string into the array of data values we're trimming
  $csv = Text::CSV->new( {binary => 1} );
  $csv->parse($dataValues);
  @values = $csv->fields();

  #get a list of all unique values in the column before the trim, and store
  #it for future use in edit operations
  $query = "SELECT DISTINCT $column FROM $masterTable";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  undef(@allValues);
  while (($val) = $dbOutput->fetchrow_array)
  {
    push(@allValues, $val);
  }

  $csv->combine(@allValues);
  $trimValues = $csv->string;
  $q_values = $prepDB->quote($trimValues);
  $query = "INSERT INTO prep.trim_values (flowID, name, `values`)
      VALUES ($flowID, $q_colName, $q_values)
      ON DUPLICATE KEY UPDATE `values`=$q_values";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #if we want to keep the specified values, we need to "invert" the array
  #so it contains all of the column values that weren't specified
  if ($matchOp eq "keep")
  {

    #turn the values array into a hash
    %valueHash = map {$_ => 1} @values;

    #grab all of the column values, and store those we don't want to keep
    foreach $val (@allValues)
    {
      if ($valueHash{$val} != 1)
      {
        push(@tmp, $val);
      }
    }
    push(@tmp, "(empty)");

    @values = @tmp;
  }

  #set the "title" of the operation for UI
  $valueCount = scalar(@values);
  PrepUtils_set_job_op_title($prepDB, $jobID, "Trimming $valueCount values from $colName");

  #turn off SQL binary logging for performance benefits, since we're not
  #actually replicating this data
  $query = "SET sql_log_bin = 0";
  $prepDB->do($query);
  $query = "SET SESSION transaction_isolation='READ-UNCOMMITTED'";
  $prepDB->do($query);

  #detect if we're doing a "huge trim", so we can apply some additional
  #performance enhancements
  $hugeTrim = 0;
  if (scalar(@values) > 99)
  {
    flow_telemetry($prepDB, $jobID, "Detected very large trim operation");
    $hugeTrim = 1;

    PrepUtils_set_job_op_pct($prepDB, $jobID, 1);
    PrepUtils_set_job_op_details($prepDB, $jobID, "Clearing old column analysis info");

    #get the IDs of columns that we've definitely built indices on
    $query = "SELECT ID FROM $masterColTable WHERE type='UPC'";
    $dbOutput = $prepDB->prepare($query);
    $status = $dbOutput->execute;
    PrepUtils_handle_db_err($prepDB, $status, $query);
    ($idxUPCCol) = $dbOutput->fetchrow_array;

    $query = "SELECT ID FROM $masterColTable WHERE type='time'";
    $dbOutput = $prepDB->prepare($query);
    $status = $dbOutput->execute;
    PrepUtils_handle_db_err($prepDB, $status, $query);
    ($idxTimeCol) = $dbOutput->fetchrow_array;

    $query = "SELECT ID FROM $masterColTable WHERE type='geography'";
    $dbOutput = $prepDB->prepare($query);
    $status = $dbOutput->execute;
    PrepUtils_handle_db_err($prepDB, $status, $query);
    ($idxGeoCol) = $dbOutput->fetchrow_array;

    #drop default indices that aren't used by current query
    if ($colID != $idxUPCCol)
    {
      flow_telemetry($prepDB, $jobID, "Clearing UPC index");
      $query = "ALTER TABLE $masterTable
          DROP INDEX idx_upc, ALGORITHM=INPLACE, LOCK=EXCLUSIVE";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);
    }
    if ($colID != $idxTimeCol)
    {
      flow_telemetry($prepDB, $jobID, "Clearing Time index");
      $query = "ALTER TABLE $masterTable
          DROP INDEX idx_time, ALGORITHM=INPLACE, LOCK=EXCLUSIVE";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);
    }
    if ($colID != $idxGeoCol)
    {
      flow_telemetry($prepDB, $jobID, "Clearing geography index");
      $query = "ALTER TABLE $masterTable
          DROP INDEX idx_geography, ALGORITHM=INPLACE, LOCK=EXCLUSIVE";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);
    }
  }

  #detect if we need to index/analyze the column first
  $query = "SELECT rowCount FROM prep.jobs WHERE ID=$jobID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($rowCount) = $dbOutput->fetchrow_array;
  $recordCount = prep_autoscale_number($rowCount);

  #if there's more than million rows, we probably need an index
  if ($rowCount > 999_999)
  {

    #see if we already have an index
    $query = "SHOW INDEX FROM $masterTable WHERE Column_name='$column'";
    $dbOutput = $prepDB->prepare($query);
    $status = $dbOutput->execute;
    PrepUtils_handle_db_err($prepDB, $status, $query);

    #if we don't already have an index, let's create one
    if ($status < 1)
    {
      PrepUtils_set_job_op_pct($prepDB, $jobID, 2);
      PrepUtils_set_job_op_details($prepDB, $jobID, "Analyzing data in trim column");

      if ($noUpdate < 1)
      {
        $query = "UPDATE prep.jobs SET opInfo='1|Creating index on trim data'
            WHERE ID=$jobID";
        $prepDB->do($query);
      }

      $query = "CREATE INDEX idx_$colID ON $masterTable ($column)
          ALGORITHM=INPLACE LOCK=EXCLUSIVE";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);
    }
  }

  #get the highest record ID number in the data table
  $query = "SELECT MAX(ID) FROM $masterTable";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($maxID) = $dbOutput->fetchrow_array;

  $numOps = scalar(@values);
  $opsDone = 0;
  undef(@delArray);
  foreach $val (@values)
  {

    #update really detailed operation status info
    if (($opsDone % 10) == 0)
    {
      PrepUtils_set_job_op_details($prepDB, $jobID, "Trimming $val from $recordCount records");
      $pct = int((($opsDone / $numOps) * 96) + 2);
      PrepUtils_set_job_op_pct($prepDB, $jobID, $pct);
      $todoCount = $numOps - $opsDone;
      $todoCount = prep_autoscale_number($todoCount);
      PrepUtils_set_job_op_extra($prepDB, $jobID, "Trims remaining: $todoCount");
    }

    #handle "(empty)" (aka NULL) trim requests
    if ($val eq "(empty)")
    {
      $query = "DELETE FROM $masterTable WHERE ISNULL($column)";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);
    }

    #else it's just a "regular" value to be deleted
    #NB: use the primary key to scan 100K rows at a time for performance
    #    reasons
    else
    {
      $q_val = $prepDB->quote($val);

      $start = 0;
      while ($start <= $maxID)
      {
        $end = $start + 100_000;

        $query = "DELETE FROM $masterTable
            WHERE $column = $q_val AND ID > $start AND ID <= $end";
        $status = $prepDB->do($query);
        PrepUtils_handle_db_err($prepDB, $status, $query);

        $start = $end;
      }
    }

    #update detailed status info
    $opsDone++;
    if (($noUpdate < 1) && (($opsDone % 10) == 0))
    {
      $pct = ($opsDone / $numOps) * 100;
      $pct = abs(int($pct) - 1);
      $opInfo = "$pct|Trimming data";
      $q_opInfo = $prepDB->quote($opInfo);
      $query = "UPDATE prep.jobs SET opInfo=$q_opInfo WHERE ID=$jobID";
      $prepDB->do($query);
    }
  }

  #if we dropped them for performance, optimize and re-create the indexes
  if ($hugeTrim == 1)
  {
    PrepUtils_set_job_op_pct($prepDB, $jobID, 98);
    PrepUtils_set_job_op_details($prepDB, $jobID, "Optimizing layout of remaining data");

    flow_telemetry($prepDB, $jobID, "Optimizing data layout");
    $query = "OPTIMIZE TABLE $masterTable";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);

    if ($colID != $idxUPCCol)
    {
      flow_telemetry($prepDB, $jobID, "Recreating UPC index");
      $query = "CREATE INDEX idx_upc ON $masterTable (column_$idxUPCCol)
          ALGORITHM=INPLACE LOCK=EXCLUSIVE";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);
    }
    if ($colID != $idxTimeCol)
    {
      flow_telemetry($prepDB, $jobID, "Recreating time index");
      $query = "CREATE INDEX idx_time ON $masterTable (column_$idxTimeCol)
          ALGORITHM=INPLACE LOCK=EXCLUSIVE";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);
    }
    if ($colID != $idxGeoCol)
    {
      flow_telemetry($prepDB, $jobID, "Recreating geography index");
      $query = "CREATE INDEX idx_geography ON $masterTable (column_$idxGeoCol)
          ALGORITHM=INPLACE LOCK=EXCLUSIVE";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);
    }
  }

  #calculate the new number of data rows in the master table, and store it
  PrepUtils_set_job_op_pct($prepDB, $jobID, 99);
  PrepUtils_set_job_op_details($prepDB, $jobID, "Generating remaining record count");

  $query = "SELECT COUNT(*) FROM $masterTable";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($rowCount) = $dbOutput->fetchrow_array;

  $query = "UPDATE prep.jobs SET rowCount=$rowCount WHERE ID=$jobID";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs
        SET PID=NULL, opInfo='DONE', state='LOADED', lastAction=NOW(), opDetails=NULL
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");
  PrepUtils_set_job_op_details($prepDB, $jobID, "");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "");
  PrepUtils_set_job_op_extra($prepDB, $jobID, "");

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);
  PrepUtils_increment_successful_xforms($prepDB, $jobID);

  flow_telemetry($prepDB, $jobID, "Trimmed data based on $colName");

  return(0);
}



#-------------------------------------------------------------------------
#
# Apply the "match products" column transform
#

sub prep_recipe_trans_col_product_match
{
  my ($query, $dbOutput, $status, $action, $colName, $newColID);
  my (%itemNameMatchHash);

  my ($prepDB, $flowID, $jobID, $stepID, $noUpdate) = @_;


  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs SET PID=$$, opInfo='0|Matching dimension items' WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #get the details of the action we're performing
  $query = "SELECT action FROM prep.recipes WHERE flowID=$flowID AND step=$stepID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($action) = $dbOutput->fetchrow_array;

  #extract our transform info from the recipe step, error out if bad step
  if ($action =~ m/^TRANS-COL-PRODUCT-MATCH\|COL=(.*)\|DS=(.*?)\|UNMATCHED=(.*?)\|$/)
  {
    $colName = $1;
    $matchDSID = $2;
    $unmatchedAction = $3;
  }
  else
  {
    return(-1);
  }

  $startTime = PrepUtils_get_current_timestamp($prepDB);

  #set the "title" of the operation for UI
  PrepUtils_set_job_op_title($prepDB, $jobID, "Matching products in $colName");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "0");

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";
  $tmpTable = "prep_data.$jobID" . "_tmp";

  #get the ID for the column we're using to match data
  $q_colName = $prepDB->quote($colName);
  $query = "SELECT ID FROM $masterColTable WHERE name=$q_colName";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($colID) = $dbOutput->fetchrow_array;
  $column = "column_$colID";

  #if a "matched" column doesn't already exist, create it
  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs
        SET opInfo='2|Adding new matched product column' WHERE ID=$jobID";
    $prepDB->do($query);

    PrepUtils_set_job_op_extra($prepDB, $jobID, "Adding new matched product column");
    PrepUtils_set_job_op_pct($prepDB, $jobID, "2");
  }

  #if the matched items column exists, wipe it clean, otherwise create it
  $newColName = "Matched Products";
  $query = "SELECT ID FROM $masterColTable WHERE name='$newColName'";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($newColID) = $dbOutput->fetchrow_array;

  if ($newColID > 0)
  {
    $query = "UPDATE $masterTable SET column_$newColID = NULL";
    $status = $dbOutput->execute;
    PrepUtils_handle_db_err($prepDB, $status, $query);
  }
  else
  {
    $query = "INSERT INTO $masterColTable (name, type) VALUES ('$newColName', 'product')";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);
    $newColID = $prepDB->{q{mysql_insertid}};
    $query = "ALTER TABLE $masterTable ADD COLUMN column_$newColID VARCHAR(127)";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);
  }

  #mark the original dimension item column as an attribute column
  $query = "UPDATE $masterColTable SET type='pattr' WHERE ID=$colID";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #populate match column using stored matches (created by AI and user)
  $query = "SELECT flowItem, dsItem FROM prep.dim_matches WHERE flowID=$flowID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  while (($flowItem, $dsItem) = $dbOutput->fetchrow_array)
  {
    $itemNameMatchHash{$flowItem} = $dsItem;
  }

  foreach $flowItem (keys %itemNameMatchHash)
  {
    $q_flowItem = $prepDB->quote($flowItem);
    $q_dsItem = $prepDB->quote($itemNameMatchHash{$flowItem});
    $query = "UPDATE $masterTable SET column_$newColID = $q_dsItem
        WHERE $column = $q_flowItem";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);
  }

  #anything that wasn't matched keeps its original value (implying it gets
  #created as a new item in the data source)
  $query = "UPDATE $masterTable SET column_$newColID = $column
      WHERE ISNULL(column_$newColID)";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs
        SET PID=NULL, opInfo='DONE', state='LOADED', lastAction=NOW(), opDetails=NULL
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");
  PrepUtils_set_job_op_details($prepDB, $jobID, "");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "");
  PrepUtils_set_job_op_extra($prepDB, $jobID, "");

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);
  PrepUtils_increment_successful_xforms($prepDB, $jobID);

  flow_telemetry($prepDB, $jobID, "Matched products in $colName");

  return(0);
}



#-------------------------------------------------------------------------
#
# Apply the index/analyze column transform
#

sub prep_recipe_trans_col_index
{
  my ($query, $masterTable, $masterColTable);
  my ($column, $status, $colName);

  my ($prepDB, $flowID, $jobID, $colID, $noUpdate) = @_;


  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs SET PID=$$, opInfo='-|Indexing/analyzing data'
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  $startTime = PrepUtils_get_current_timestamp($prepDB);

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  $query = "SELECT name FROM $masterColTable WHERE ID=$colID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($colName) = $dbOutput->fetchrow_array;

  #set the "title" of the operation for UI
  PrepUtils_set_job_op_title($prepDB, $jobID, "Analyzing column $colName");

  $column = "column_$colID";

  #create an index on the values in the selected column
  $query = "CREATE INDEX idx_$colID ON $masterTable ($column)
      ALGORITHM=INPLACE LOCK=EXCLUSIVE";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs
        SET PID=NULL, opInfo='DONE', state='LOADED', lastAction=NOW()
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);

  flow_telemetry($prepDB, $jobID, "Indexed/analyzed values in $colName");

  return(0);
}



#-------------------------------------------------------------------------
#
# Apply the "append/prepend" cell transform
#

sub prep_recipe_trans_cell_append_prepend
{
  my ($query, $dbOutput, $action, $colName, $masterTable, $masterColTable);
  my ($q_colName, $colID, $column, $status, $prepend, $append, $q_append);
  my ($q_prepend);

  my ($prepDB, $flowID, $jobID, $stepID, $noUpdate) = @_;


  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs SET PID=$$, opInfo='-|Appending/prepending values'
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #get the details of the action we're performing
  $query = "SELECT action FROM prep.recipes WHERE flowID=$flowID AND step=$stepID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($action) = $dbOutput->fetchrow_array;

  #extract our transform info from the recipe step, error out if bad step
  if ($action =~ m/^TRANS-CELL-APPEND-PREPEND\|COL=(.*?)\|PREPEND=(.*?)\|APPEND=(.*)$/)
  {
    $colName = $1;
    $prepend = $2;
    $append = $3;
  }
  else
  {
    return(-1);
  }

  $startTime = PrepUtils_get_current_timestamp($prepDB);

  $query = "SELECT rowCount FROM prep.jobs WHERE ID=$jobID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($rowCount) = $dbOutput->fetchrow_array;
  $rowCount = prep_autoscale_number($rowCount);

  #set the "title" of the operation for UI
  PrepUtils_set_job_op_title($prepDB, $jobID, "Appending/prepending data");
  PrepUtils_set_job_op_details($prepDB, $jobID, "<B>Column:</B> $colName");
  PrepUtils_set_job_op_extra($prepDB, $jobID, "Updating $rowCount data points");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "101");

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #get the ID for the column we're transforming
  $q_colName = $prepDB->quote($colName);
  $query = "SELECT ID FROM $masterColTable WHERE name=$q_colName";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($colID) = $dbOutput->fetchrow_array;
  $column = "column_$colID";

  #turn off SQL binary logging for performance benefits, since we're not
  #actually replicating this data
  $query = "SET sql_log_bin = 0";
  $prepDB->do($query);
  $query = "SET SESSION transaction_isolation='READ-UNCOMMITTED'";
  $prepDB->do($query);

  #concatenate the values in the selected columns
  $q_append = $prepDB->quote($append);
  $q_prepend = $prepDB->quote($prepend);
  $query = "UPDATE $masterTable SET $column = CONCAT($q_prepend, $column, $q_append)";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs
        SET PID=NULL, opInfo='DONE', state='LOADED', lastAction=NOW()
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");
  PrepUtils_set_job_op_details($prepDB, $jobID, "");
  PrepUtils_set_job_op_extra($prepDB, $jobID, "");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "");

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);
  PrepUtils_increment_successful_xforms($prepDB, $jobID);

  flow_telemetry($prepDB, $jobID, "Appended/prepended to values in $colName");

  return(0);
}



#-------------------------------------------------------------------------
#
# Apply the "set values" cell transform
#

sub prep_recipe_trans_cell_set
{
  my ($query, $dbOutput, $action, $colName, $matchOp, $matchText, $status);
  my ($masterTable, $masterColTable, $q_colName, $colID, $column, $matchSQL);
  my ($checkCol, $q_checkCol, $replaceText, $checkColID, $q_expr);
  my ($q_replaceText);

  my ($prepDB, $flowID, $jobID, $stepID, $noUpdate) = @_;


  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs SET PID=$$, opInfo='-|Setting values' WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #get the details of the action we're performing
  $query = "SELECT action FROM prep.recipes WHERE flowID=$flowID AND step=$stepID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($action) = $dbOutput->fetchrow_array;

  #extract our transform info from the recipe step, error out if bad step
  if ($action =~ m/^TRANS-CELL-SET\|COL=(.*)\|CHECK=(.*?)\|OP=(.*?)\|MATCH=(.*)\|REPL=(.*)$/)
  {
    $colName = $1;
    $checkCol = $2;
    $matchOp = $3;
    $matchText = $4;
    $replaceText = $5;
  }
  else
  {
    return(-1);
  }

  $startTime = PrepUtils_get_current_timestamp($prepDB);

  #set the "title" of the operation for UI
  PrepUtils_set_job_op_title($prepDB, $jobID, "Setting cell values in $colName");

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #get the ID for the column we're replacing values in
  $q_colName = $prepDB->quote($colName);
  $query = "SELECT ID FROM $masterColTable WHERE name=$q_colName";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($colID) = $dbOutput->fetchrow_array;
  $column = "column_$colID";

  #get the ID for the column we're checking values against
  $q_checkCol = $prepDB->quote($checkCol);
  $query = "SELECT ID FROM $masterColTable WHERE name=$q_checkCol";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($checkColID) = $dbOutput->fetchrow_array;
  $checkCol = "column_$checkColID";

  #build up the SQL match string we're going to use to match
  $matchSQL = "";
  if ($matchOp eq "begins")
  {
    $matchSQL = $matchText . "%";
    $matchSQL = $prepDB->quote($matchSQL);
    $matchSQL = "$checkCol LIKE $matchSQL";
  }
  elsif ($matchOp eq "ends")
  {
    $matchSQL = "%" . $matchText;
    $matchSQL = $prepDB->quote($matchSQL);
    $matchSQL = "$checkCol LIKE $matchSQL";
  }
  elsif ($matchOp eq "contains")
  {
    $matchSQL = "%" . $matchText . "%";
    $matchSQL = $prepDB->quote($matchSQL);
    $matchSQL = "$checkCol LIKE $matchSQL";
  }
  elsif ($matchOp eq "is")
  {
    $matchSQL = $matchText;
    $matchSQL = $prepDB->quote($matchSQL);
    $matchSQL = "$checkCol = $matchSQL";
  }
  elsif ($matchOp eq "does not begin")
  {
    $matchSQL = $matchText . "%";
    $matchSQL = $prepDB->quote($matchSQL);
    $matchSQL = "$checkCol NOT LIKE $matchSQL";
  }
  elsif ($matchOp eq "does not end")
  {
    $matchSQL = "%" . $matchText;
    $matchSQL = $prepDB->quote($matchSQL);
    $matchSQL = "$checkCol NOT LIKE $matchSQL";
  }
  elsif ($matchOp eq "does not contain")
  {
    $matchSQL = "%" . $matchText . "%";
    $matchSQL = $prepDB->quote($matchSQL);
    $matchSQL = "$checkCol NOT LIKE $matchSQL";
  }
  elsif ($matchOp eq "is blank")
  {
    $matchSQL = "ISNULL($checkCol)"
  }
  elsif ($matchOp eq "matches")
  {
    $q_expr = $prepDB->quote($matchText);
    $matchSQL = "$checkCol REGEXP $q_expr";
  }

  #turn off SQL binary logging for performance benefits, since we're not
  #actually replicating this data
  $query = "SET sql_log_bin = 0";
  $prepDB->do($query);
  $query = "SET SESSION transaction_isolation='READ-UNCOMMITTED'";
  $prepDB->do($query);

  #set the cell values where the check column meets the condition
  $q_replaceText = $prepDB->quote($replaceText);
  $query = "UPDATE $masterTable SET $column = $q_replaceText WHERE $matchSQL";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs
        SET PID=NULL, opInfo='DONE', state='LOADED', lastAction=NOW()
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);
  PrepUtils_increment_successful_xforms($prepDB, $jobID);

  flow_telemetry($prepDB, $jobID, "Set values in $colName");

  return(0);
}



#-------------------------------------------------------------------------
#
# Apply the "replace values" cell transform
#

sub prep_recipe_trans_cell_replace
{
  my ($query, $dbOutput, $action, $colName, $matchOp, $matchText, $status);
  my ($masterTable, $masterColTable, $q_colName, $colID, $column, $matchSQL);
  my ($replaceCol, $q_expr, $checkCol, $q_checkCol, $checkColID);
  my ($q_replaceCol, $replaceColID);

  my ($prepDB, $flowID, $jobID, $stepID, $noUpdate) = @_;


  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs SET PID=$$, opInfo='-|Replacing values'
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #get the details of the action we're performing
  $query = "SELECT action FROM prep.recipes WHERE flowID=$flowID AND step=$stepID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($action) = $dbOutput->fetchrow_array;

  #extract our transform info from the recipe step, error out if bad step
  if ($action =~ m/^TRANS-CELL-REPLACE\|COL=(.*)\|CHECK=(.*?)\|OP=(.*?)\|MATCH=(.*)\|REPL=(.*)$/)
  {
    $colName = $1;
    $checkCol = $2;
    $matchOp = $3;
    $matchText = $4;
    $replaceCol = $5;
  }
  else
  {
    return(-1);
  }

  $startTime = PrepUtils_get_current_timestamp($prepDB);

  #set the "title" of the operation for UI
  PrepUtils_set_job_op_title($prepDB, $jobID, "Replacing cell values in $colName");

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #get the ID for the column we're replacing values in
  $q_colName = $prepDB->quote($colName);
  $query = "SELECT ID FROM $masterColTable WHERE name=$q_colName";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($colID) = $dbOutput->fetchrow_array;
  $column = "column_$colID";

  #get the ID for the column we're checking values against
  $q_checkCol = $prepDB->quote($checkCol);
  $query = "SELECT ID FROM $masterColTable WHERE name=$q_checkCol";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($checkColID) = $dbOutput->fetchrow_array;
  $checkCol = "column_$checkColID";

  #get the ID for the column we're sourcing replacement values from
  $q_replaceCol = $prepDB->quote($replaceCol);
  $query = "SELECT ID FROM $masterColTable WHERE name=$q_replaceCol";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($replaceColID) = $dbOutput->fetchrow_array;
  $replaceCol = "column_$replaceColID";

  #build up the SQL match string we're going to use to match
  $matchSQL = "";
  if ($matchOp eq "begins")
  {
    $matchSQL = $matchText . "%";
    $matchSQL = $prepDB->quote($matchSQL);
    $matchSQL = "$checkCol LIKE $matchSQL";
  }
  elsif ($matchOp eq "ends")
  {
    $matchSQL = "%" . $matchText;
    $matchSQL = $prepDB->quote($matchSQL);
    $matchSQL = "$checkCol LIKE $matchSQL";
  }
  elsif ($matchOp eq "contains")
  {
    $matchSQL = "%" . $matchText . "%";
    $matchSQL = $prepDB->quote($matchSQL);
    $matchSQL = "$checkCol LIKE $matchSQL";
  }
  elsif ($matchOp eq "is")
  {
    $matchSQL = $matchText;
    $matchSQL = $prepDB->quote($matchSQL);
    $matchSQL = "$checkCol = $matchSQL";
  }
  elsif ($matchOp eq "does not begin")
  {
    $matchSQL = $matchText . "%";
    $matchSQL = $prepDB->quote($matchSQL);
    $matchSQL = "$checkCol NOT LIKE $matchSQL";
  }
  elsif ($matchOp eq "does not end")
  {
    $matchSQL = "%" . $matchText;
    $matchSQL = $prepDB->quote($matchSQL);
    $matchSQL = "$checkCol NOT LIKE $matchSQL";
  }
  elsif ($matchOp eq "does not contain")
  {
    $matchSQL = "%" . $matchText . "%";
    $matchSQL = $prepDB->quote($matchSQL);
    $matchSQL = "$checkCol NOT LIKE $matchSQL";
  }
  elsif ($matchOp eq "is blank")
  {
    $matchSQL = "ISNULL($checkCol)"
  }
  elsif ($matchOp eq "matches")
  {
    $q_expr = $prepDB->quote($matchText);
    $matchSQL = "$checkCol REGEXP $q_expr";
  }

  #turn off SQL binary logging for performance benefits, since we're not
  #actually replicating this data
  $query = "SET sql_log_bin = 0";
  $prepDB->do($query);
  $query = "SET SESSION transaction_isolation='READ-UNCOMMITTED'";
  $prepDB->do($query);

  #set the cell values where the check column meets the condition
  $query = "UPDATE $masterTable SET $column = $replaceCol WHERE $matchSQL";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs
        SET PID=NULL, opInfo='DONE', state='LOADED', lastAction=NOW()
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);
  PrepUtils_increment_successful_xforms($prepDB, $jobID);

  flow_telemetry($prepDB, $jobID, "Replaced values in $colName");

  return(0);
}



#-------------------------------------------------------------------------
#
# Apply the "search & replace values" cell transform
#

sub prep_recipe_trans_cell_search_replace
{
  my ($query, $dbOutput, $action, $colName, $searchOp, $matchText, $status);

  my ($prepDB, $flowID, $jobID, $stepID, $noUpdate) = @_;


  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs SET PID=$$, opInfo='-|Searching & replacing values'
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #get the details of the action we're performing
  $query = "SELECT action FROM prep.recipes WHERE flowID=$flowID AND step=$stepID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($action) = $dbOutput->fetchrow_array;

  #extract our transform info from the recipe step, error out if bad step
  if ($action =~ m/^TRANS-CELL-SEARCH-REPLACE\|COL=(.*)\|OP=(.*?)\|SEARCH=(.*)\|REPL=(.*)$/)
  {
    $colName = $1;
    $searchOp = $2;
    $searchText = $3;
    $replaceText = $4;
  }
  else
  {
    return(-1);
  }

  $startTime = PrepUtils_get_current_timestamp($prepDB);

  #set the "title" of the operation for UI
  PrepUtils_set_job_op_title($prepDB, $jobID, "Searching & replacing cell values in $colName");

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #get the ID for the column we're replacing values in
  $q_colName = $prepDB->quote($colName);
  $query = "SELECT ID FROM $masterColTable WHERE name=$q_colName";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($colID) = $dbOutput->fetchrow_array;
  $column = "column_$colID";

  #build up the SQL match string we're going to use to match
  $matchSQL = "";
  if ($searchOp eq "anywhere")
  {
    $matchSQL = "%" . $searchText . "%";
    $matchSQL = $prepDB->quote($matchSQL);
  }
  elsif ($searchOp eq "beginning")
  {
    $matchSQL = $matchText . "%";
    $matchSQL = $prepDB->quote($matchSQL);
  }
  elsif ($searchOp eq "ending")
  {
    $matchSQL = "%" . $matchText;
    $matchSQL = $prepDB->quote($matchSQL);
  }

  #turn off SQL binary logging for performance benefits, since we're not
  #actually replicating this data
  $query = "SET sql_log_bin = 0";
  $prepDB->do($query);
  $query = "SET SESSION transaction_isolation='READ-UNCOMMITTED'";
  $prepDB->do($query);

  #grab all of the distinct values that match our search condition
  $query = "SELECT DISTINCT $column FROM $masterTable
      WHERE $column LIKE $matchSQL";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  while (($val) = $dbOutput->fetchrow_array)
  {
    $oldVal = $val;
    if ($searchOp eq "anywhere")
    {
      $val =~ s/$searchText/$replaceText/g;
    }
    elsif ($searchOp eq "beginning")
    {
      if ($val =~ m/^$searchText(.*)/)
      {
        $val = $replaceText . $1;
      }
    }
    elsif ($searchOp eq "ending")
    {
      if ($val =~ m/^(.*)$searchText$/)
      {
        $val = $1 . $replaceText;
      }
    }

    $q_val = $prepDB->quote($val);
    $q_oldVal = $prepDB->quote($oldVal);
    $query = "UPDATE $masterTable SET $column = $q_val WHERE $column = $q_oldVal";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);
  }

  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs
        SET PID=NULL, opInfo='DONE', state='LOADED', lastAction=NOW()
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);
  PrepUtils_increment_successful_xforms($prepDB, $jobID);

  flow_telemetry($prepDB, $jobID, "Searched & replaced values in $colName");

  return(0);
}



#-------------------------------------------------------------------------
#
# Apply the "round" cell transform
#

sub prep_recipe_trans_cell_round
{
  my ($query, $dbOutput, $action, $colName, $decimals, $masterTable);
  my ($masterColTable, $q_colName, $colID, $column, $status);

  my ($prepDB, $flowID, $jobID, $stepID, $noUpdate) = @_;


  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs SET PID=$$, opInfo='-|Rounding values'
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #get the details of the action we're performing
  $query = "SELECT action FROM prep.recipes WHERE flowID=$flowID AND step=$stepID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($action) = $dbOutput->fetchrow_array;

  #extract our transform info from the recipe step, error out if bad step
  if ($action =~ m/^TRANS-CELL-ROUND\|COL=(.*?)\|DEC=(\d+)$/)
  {
    $colName = $1;
    $decimals = $2;
  }
  else
  {
    return(-1);
  }

  $startTime = PrepUtils_get_current_timestamp($prepDB);

  #set the "title" of the operation for UI
  PrepUtils_set_job_op_title($prepDB, $jobID, "Rounding cell values in $colName");

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #get the ID for the column we're transforming
  $q_colName = $prepDB->quote($colName);
  $query = "SELECT ID FROM $masterColTable WHERE name=$q_colName";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($colID) = $dbOutput->fetchrow_array;
  $column = "column_$colID";

  #round the values in the selected column
  if ($decimals < 0)
  {
    $decimals = 0;
  }
  elsif ($decimals > 9)
  {
    $decimals = 9;
  }

  #turn off SQL binary logging for performance benefits, since we're not
  #actually replicating this data
  $query = "SET sql_log_bin = 0";
  $prepDB->do($query);
  $query = "SET SESSION transaction_isolation='READ-UNCOMMITTED'";
  $prepDB->do($query);

  $query = "UPDATE $masterTable SET $column = ROUND($column, $decimals)";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs
        SET PID=NULL, opInfo='DONE', state='LOADED', lastAction=NOW()
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);
  PrepUtils_increment_successful_xforms($prepDB, $jobID);

  flow_telemetry($prepDB, $jobID, "Rounded values in $colName");

  return(0);
}



#-------------------------------------------------------------------------
#
# Apply the "ceiling" cell transform
#

sub prep_recipe_trans_cell_ceiling
{
  my ($query, $dbOutput, $action, $colName, $masterTable, $masterColTable);
  my ($q_colName, $colID, $column, $status);

  my ($prepDB, $flowID, $jobID, $stepID, $noUpdate) = @_;


  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs SET PID=$$, opInfo='-|Ceiling values' WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #get the details of the action we're performing
  $query = "SELECT action FROM prep.recipes WHERE flowID=$flowID AND step=$stepID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($action) = $dbOutput->fetchrow_array;

  #extract our transform info from the recipe step, error out if bad step
  if ($action =~ m/^TRANS-CELL-CEILING\|COL=(.*)$/)
  {
    $colName = $1;
  }
  else
  {
    return(-1);
  }

  $startTime = PrepUtils_get_current_timestamp($prepDB);

  #set the "title" of the operation for UI
  PrepUtils_set_job_op_title($prepDB, $jobID, "Ceiling cell values in $colName");

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #get the ID for the column we're transforming
  $q_colName = $prepDB->quote($colName);
  $query = "SELECT ID FROM $masterColTable WHERE name=$q_colName";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($colID) = $dbOutput->fetchrow_array;
  $column = "column_$colID";

  #turn off SQL binary logging for performance benefits, since we're not
  #actually replicating this data
  $query = "SET sql_log_bin = 0";
  $prepDB->do($query);
  $query = "SET SESSION transaction_isolation='READ-UNCOMMITTED'";
  $prepDB->do($query);

  #ceiling the values in the selected column
  $query = "UPDATE $masterTable SET $column = CEILING($column)";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs
        SET PID=NULL, opInfo='DONE', state='LOADED', lastAction=NOW()
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);
  PrepUtils_increment_successful_xforms($prepDB, $jobID);

  flow_telemetry($prepDB, $jobID, "Applied ceiling to values in $colName");

  return(0);
}



#-------------------------------------------------------------------------
#
# Apply the "floor" cell transform
#

sub prep_recipe_trans_cell_floor
{
  my ($query, $dbOutput, $action, $colName, $masterTable, $masterColTable);
  my ($q_colName, $colID, $column, $status);

  my ($prepDB, $flowID, $jobID, $stepID, $noUpdate) = @_;


  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs SET PID=$$, opInfo='-|Flooring values'
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #get the details of the action we're performing
  $query = "SELECT action FROM prep.recipes WHERE flowID=$flowID AND step=$stepID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($action) = $dbOutput->fetchrow_array;

  #extract our transform info from the recipe step, error out if bad step
  if ($action =~ m/^TRANS-CELL-FLOOR\|COL=(.*)$/)
  {
    $colName = $1;
  }
  else
  {
    return(-1);
  }

  $startTime = PrepUtils_get_current_timestamp($prepDB);

  #set the "title" of the operation for UI
  PrepUtils_set_job_op_title($prepDB, $jobID, "Flooring cell values in $colName");

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #get the ID for the column we're transforming
  $q_colName = $prepDB->quote($colName);
  $query = "SELECT ID FROM $masterColTable WHERE name=$q_colName";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($colID) = $dbOutput->fetchrow_array;
  $column = "column_$colID";

  #turn off SQL binary logging for performance benefits, since we're not
  #actually replicating this data
  $query = "SET sql_log_bin = 0";
  $prepDB->do($query);
  $query = "SET SESSION transaction_isolation='READ-UNCOMMITTED'";
  $prepDB->do($query);

  #floor the values in the selected column
  $query = "UPDATE $masterTable SET $column = FLOOR($column)";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs
        SET PID=NULL, opInfo='DONE', state='LOADED', lastAction=NOW()
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);
  PrepUtils_increment_successful_xforms($prepDB, $jobID);

  flow_telemetry($prepDB, $jobID, "Applied floor to values in $colName");

  return(0);
}



#-------------------------------------------------------------------------
#
# Apply the "NA to zero" cell transform
#

sub prep_recipe_trans_cell_nazero
{
  my ($query, $dbOutput, $action, $colName, $masterTable, $masterColTable);
  my ($q_colName, $colID, $column, $status);

  my ($prepDB, $flowID, $jobID, $stepID, $noUpdate) = @_;


  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs SET PID=$$, opInfo='-|Converting NAs to zeroes'
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #get the details of the action we're performing
  $query = "SELECT action FROM prep.recipes WHERE flowID=$flowID AND step=$stepID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($action) = $dbOutput->fetchrow_array;

  #extract our transform info from the recipe step, error out if bad step
  if ($action =~ m/^TRANS-CELL-NAZERO\|COL=(.*)$/)
  {
    $colName = $1;
  }
  else
  {
    return(-1);
  }

  $startTime = PrepUtils_get_current_timestamp($prepDB);

  $query = "SELECT rowCount FROM prep.jobs WHERE ID=$jobID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($rowCount) = $dbOutput->fetchrow_array;
  $rowCount = prep_autoscale_number($rowCount);

  #set the "title" of the operation for status UI
  PrepUtils_set_job_op_title($prepDB, $jobID, "Converting NAs to zeroes");
  PrepUtils_set_job_op_details($prepDB, $jobID, "<B>Column:</B> $colName");
  PrepUtils_set_job_op_extra($prepDB, $jobID, "Updating $rowCount data points");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "101");

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #get the ID for the column we're transforming
  $q_colName = $prepDB->quote($colName);
  $query = "SELECT ID FROM $masterColTable WHERE name=$q_colName";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($colID) = $dbOutput->fetchrow_array;
  $column = "column_$colID";

  #turn off SQL binary logging for performance benefits, since we're not
  #actually replicating this data
  $query = "SET sql_log_bin = 0";
  $prepDB->do($query);
  $query = "SET SESSION transaction_isolation='READ-UNCOMMITTED'";
  $prepDB->do($query);

  #transform NAs (NULLs) to zeroes
  $query = "UPDATE $masterTable SET $column = 0 WHERE $column IS NULL";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs
        SET PID=NULL, opInfo='DONE', state='LOADED', lastAction=NOW()
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");
  PrepUtils_set_job_op_details($prepDB, $jobID, "");
  PrepUtils_set_job_op_extra($prepDB, $jobID, "");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "");

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);
  PrepUtils_increment_successful_xforms($prepDB, $jobID);

  flow_telemetry($prepDB, $jobID, "Transformed NAs to zeroes in $colName");

  return(0);
}



#-------------------------------------------------------------------------
#
# Apply the "strip HTML" cell transform
#

sub prep_recipe_trans_cell_html
{
  my ($query, $dbOutput, $action, $colName, $masterTable, $masterColTable);
  my ($q_colName, $colID, $column, $val, $origVal, $q_val, $q_origVal);
  my ($status, $numOps, $opsDone, $pct, $opInfo, $q_opInfo);

  my ($prepDB, $flowID, $jobID, $stepID, $noUpdate) = @_;


  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs SET PID=$$, opInfo='0|Stripping HTML tags'
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #get the details of the action we're performing
  $query = "SELECT action FROM prep.recipes WHERE flowID=$flowID AND step=$stepID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($action) = $dbOutput->fetchrow_array;

  #extract our transform info from the recipe step, error out if bad step
  if ($action =~ m/^TRANS-CELL-HTML\|COL=(.*)$/)
  {
    $colName = $1;
  }
  else
  {
    return(-1);
  }

  $startTime = PrepUtils_get_current_timestamp($prepDB);

  #set the "title" of the operation for UI
  PrepUtils_set_job_op_title($prepDB, $jobID, "Stripping HTML tags from cells in $colName");

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #get the ID for the column we're transforming
  $q_colName = $prepDB->quote($colName);
  $query = "SELECT ID FROM $masterColTable WHERE name=$q_colName";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($colID) = $dbOutput->fetchrow_array;
  $column = "column_$colID";

  #strip HTML from cell values in selected column
  $query = "SELECT DISTINCT $column FROM $masterTable WHERE $column IS NOT NULL";
  $dbOutput = $prepDB->prepare($query);
  $status = $numOps = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #turn off SQL binary logging for performance benefits, since we're not
  #actually replicating this data
  $query = "SET sql_log_bin = 0";
  $prepDB->do($query);
  $query = "SET SESSION transaction_isolation='READ-UNCOMMITTED'";
  $prepDB->do($query);

  while (($val) = $dbOutput->fetchrow_array)
  {

    #update detailed status info
    $opsDone++;
    if (($noUpdate < 1) && (($opsDone % 10) == 0))
    {
      $pct = ($opsDone / $numOps) * 100;
      $pct = int($pct);

      $opInfo = "$pct|Stripping HTML tags";
      $q_opInfo = $prepDB->quote($opInfo);
      $query = "UPDATE prep.jobs SET opInfo=$q_opInfo WHERE ID=$jobID";
      $prepDB->do($query);
    }

    $origVal = $val;

    #strip anything between enclosing angle brackets
    $val =~ s/<.+?>//g;

    if ($val ne $origVal)
    {
      $q_val = $prepDB->quote($val);
      $q_origVal = $prepDB->quote($origVal);
      $query = "UPDATE $masterTable SET $column=$q_val WHERE $column=$q_origVal";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);
    }
  }

  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs
        SET PID=NULL, opInfo='DONE', state='LOADED', lastAction=NOW()
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);
  PrepUtils_increment_successful_xforms($prepDB, $jobID);

  flow_telemetry($prepDB, $jobID, "Stripped HTML from $colName");

  return(0);
}



#-------------------------------------------------------------------------
#
# Apply the "clean up white space" cell transform
#

sub prep_recipe_trans_cell_space
{
  my ($query, $dbOutput, $action, $colName, $compressWS, $trimWS, $origVal);
  my ($masterTable, $masterColTable, $q_colName, $colID, $column, $val);
  my ($status, $q_origVal, $q_val, $numOps, $pct, $opsDone, $q_opInfo);
  my ($opInfo);

  my ($prepDB, $flowID, $jobID, $stepID, $noUpdate) = @_;


  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs SET PID=$$, opInfo='0|Cleaning up whitespace'
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #get the details of the action we're performing
  $query = "SELECT action FROM prep.recipes WHERE flowID=$flowID AND step=$stepID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($action) = $dbOutput->fetchrow_array;

  #extract our transform info from the recipe step, error out if bad step
  if ($action =~ m/^TRANS-CELL-SPACE\|COL=(.*?)\|COMP=(.*?)\|TRIM=(.*)$/)
  {
    $colName = $1;
    $compressWS = $2;
    $trimWS = $3;
  }
  else
  {
    return(-1);
  }

  $startTime = PrepUtils_get_current_timestamp($prepDB);

  #set the "title" of the operation for UI
  PrepUtils_set_job_op_title($prepDB, $jobID, "Cleaning up white space in $colName");

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #get the ID for the column we're transforming
  $q_colName = $prepDB->quote($colName);
  $query = "SELECT ID FROM $masterColTable WHERE name=$q_colName";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($colID) = $dbOutput->fetchrow_array;
  $column = "column_$colID";

  #format the whitespace of cells in the column
  $query = "SELECT DISTINCT $column FROM $masterTable WHERE $column IS NOT NULL";
  $dbOutput = $prepDB->prepare($query);
  $status = $numOps = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #turn off SQL binary logging for performance benefits, since we're not
  #actually replicating this data
  $query = "SET sql_log_bin = 0";
  $prepDB->do($query);
  $query = "SET SESSION transaction_isolation='READ-UNCOMMITTED'";
  $prepDB->do($query);

  $opsDone = 0;
  while (($val) = $dbOutput->fetchrow_array)
  {

    #update detailed status info
    $opsDone++;
    if (($noUpdate < 1) && (($opsDone % 10) == 0))
    {
      $pct = ($opsDone / $numOps) * 100;
      $pct = int($pct);

      $opInfo = "$pct|Cleaning up whitespace";
      $q_opInfo = $prepDB->quote($opInfo);
      $query = "UPDATE prep.jobs SET opInfo=$q_opInfo WHERE ID=$jobID";
      $prepDB->do($query);
    }

    $origVal = $val;

    #if we're compressing white space
    if ($compressWS eq "on")
    {
      $val =~ s/\s+/ /g;
    }

    #if we're trimming white space off the front and end of values
    if ($trimWS eq "on")
    {
      $val =~ s/^\s+//;
      $val=~ s/\s+$//;
    }

    $q_val = $prepDB->quote($val);
    $q_origVal = $prepDB->quote($origVal);
    $query = "UPDATE $masterTable SET $column=$q_val WHERE $column=$q_origVal";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);
  }

  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs
        SET PID=NULL, opInfo='DONE', state='LOADED', lastAction=NOW()
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);
  PrepUtils_increment_successful_xforms($prepDB, $jobID);

  flow_telemetry($prepDB, $jobID, "Cleaned up white space in $colName");

  return(0);
}



#-------------------------------------------------------------------------
#
# Apply the "set capitalization style" cell transform
#

sub prep_recipe_trans_cell_case
{
  my ($query, $dbOutput, $action, $colName, $case, $masterTable, $val);
  my ($masterColTable, $q_colName, $colID, $column, $origVal, $status);
  my ($q_val, $q_origVal, $numOps, $opsDone, $opInfo, $q_opInfo, $pct);

  my ($prepDB, $flowID, $jobID, $stepID, $noUpdate) = @_;


  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs SET PID=$$, opInfo='0|Setting capitalization style'
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #get the details of the action we're performing
  $query = "SELECT action FROM prep.recipes WHERE flowID=$flowID AND step=$stepID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($action) = $dbOutput->fetchrow_array;

  #extract our transform info from the recipe step, error out if bad step
  if ($action =~ m/^TRANS-CELL-CASE\|COL=(.*?)\|CASE=(.*)$/)
  {
    $colName = $1;
    $case = $2;
  }
  else
  {
    return(-1);
  }

  $startTime = PrepUtils_get_current_timestamp($prepDB);

  #set the "title" of the operation for UI
  PrepUtils_set_job_op_title($prepDB, $jobID, "Changing cell values in $colName to $case case");

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #get the ID for the column we're transforming
  $q_colName = $prepDB->quote($colName);
  $query = "SELECT ID FROM $masterColTable WHERE name=$q_colName";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($colID) = $dbOutput->fetchrow_array;
  $column = "column_$colID";

  #format the case of cells in the column
  $query = "SELECT DISTINCT $column FROM $masterTable WHERE $column IS NOT NULL";
  $dbOutput = $prepDB->prepare($query);
  $status = $numOps = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #turn off SQL binary logging for performance benefits, since we're not
  #actually replicating this data
  $query = "SET sql_log_bin = 0";
  $prepDB->do($query);
  $query = "SET SESSION transaction_isolation='READ-UNCOMMITTED'";
  $prepDB->do($query);

  $opsDone = 0;
  while (($val) = $dbOutput->fetchrow_array)
  {

    #update detailed status info
    $opsDone++;
    if (($noUpdate < 1) && (($opsDone % 10) == 0))
    {
      $pct = ($opsDone / $numOps) * 100;
      $pct = int($pct);

      $opInfo = "$pct|Setting capitalization style";
      $q_opInfo = $prepDB->quote($opInfo);
      $query = "UPDATE prep.jobs SET opInfo=$q_opInfo WHERE ID=$jobID";
      $prepDB->do($query);
    }

    $origVal = $val;

    #if we're converting to upper case
    if ($case eq "upper")
    {
      $val = uc($val);
    }
    elsif ($case eq "lower")
    {
      $val = lc($val);
    }
    elsif ($case eq "title")
    {
      $val =~ s/([^\s\w]*)(\S+)/$1\u\L$2/g;
    }

    $q_val = $prepDB->quote($val);
    $q_origVal = $prepDB->quote($origVal);
    $query = "UPDATE $masterTable SET $column=$q_val WHERE $column=$q_origVal";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);
  }

  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs
        SET PID=NULL, opInfo='DONE', state='LOADED', lastAction=NOW()
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);
  PrepUtils_increment_successful_xforms($prepDB, $jobID);

  flow_telemetry($prepDB, $jobID, "Transformed case style in $colName");

  return(0);
}



#-------------------------------------------------------------------------
#
# Apply the "format dates" cell transform
#

sub prep_recipe_trans_cell_date
{
  my ($query, $dbOutput, $action, $colName, $timeFormat, $durationFormat);
  my ($masterTable, $masterColTable, $q_colName, $colID, $column, $time, $type);
  my ($endDate, $duration, $origTime, $q_origTime, $q_time, $sqlDateFmt);
  my ($db_date, $status, $pct, $opInfo, $numOps, $opsDone, $q_opInfo);
  my (@dateVals);

  my ($prepDB, $flowID, $jobID, $stepID, $noUpdate) = @_;


  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs SET PID=$$, opInfo='0|Formatting dates'
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #get the details of the action we're performing
  $query = "SELECT action FROM prep.recipes WHERE flowID=$flowID AND step=$stepID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($action) = $dbOutput->fetchrow_array;

  #extract our transform info from the recipe step, error out if bad step
  if ($action =~ m/^TRANS-CELL-DATE\|COL=(.*?)\|TIME=(.*?)\|DUR=(.*)$/)
  {
    $colName = $1;
    $timeFormat = $2;
    $durationFormat = $3;
  }
  else
  {
    return(-1);
  }

  $startTime = PrepUtils_get_current_timestamp($prepDB);

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #get the ID for the column we're transforming
  $q_colName = $prepDB->quote($colName);
  $query = "SELECT ID FROM $masterColTable WHERE name=$q_colName";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($colID) = $dbOutput->fetchrow_array;
  $column = "column_$colID";

  #set the "title" of the operation for UI
  PrepUtils_set_job_op_title($prepDB, $jobID, "Formatting dates in $colName");

  #format date strings

  #build our MySQL data format string based on user selection
  if ($timeFormat == 1)
  {
    $sqlDateFmt = "%c/%e/%Y";
  }
  elsif ($timeFormat == 2)
  {
    $sqlDateFmt = "%c/%e/%y";
  }
  elsif ($timeFormat == 3)
  {
    $sqlDateFmt = "%c/%e";
  }
  elsif ($timeFormat == 4)
  {
    $sqlDateFmt = "%b %e, %Y";
  }
  elsif ($timeFormat == 5)
  {
    $sqlDateFmt = "%M %e, %Y";
  }
  elsif ($timeFormat == 6)
  {
    $sqlDateFmt = "%e-%b-%Y";
  }
  elsif ($timeFormat == 7)
  {
    $sqlDateFmt = "%m/%d/%Y";
  }
  elsif ($timeFormat == 8)
  {
    $sqlDateFmt = "%m/%d/%y";
  }

  $query = "SELECT DISTINCT $column FROM $masterTable WHERE $column IS NOT NULL";
  $dbOutput = $prepDB->prepare($query);
  $status = $numOps = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #turn off SQL binary logging for performance benefits, since we're not
  #actually replicating this data
  $query = "SET sql_log_bin = 0";
  $prepDB->do($query);
  $query = "SET SESSION transaction_isolation='READ-UNCOMMITTED'";
  $prepDB->do($query);

  $opsDone = 0;
  while (($time) = $dbOutput->fetchrow_array)
  {

    #update detailed status info
    $opsDone++;
    if (($noUpdate < 1) && (($opsDone % 10) == 0))
    {
      $pct = ($opsDone / $numOps) * 100;
      $pct = int($pct);

      $opInfo = "$pct|Formatting dates";
      $q_opInfo = $prepDB->quote($opInfo);
      $query = "UPDATE prep.jobs SET opInfo=$q_opInfo WHERE ID=$jobID";
      $prepDB->do($query);
    }

    $origTime = $time;

    #parse the (possible) date string
    @dateVals = prep_recipe_parse_date($prepDB, $time);
    $duration = $dateVals[0];
    $type = $dateVals[1];
    $endDate = $dateVals[2];

    #if we were able to parse the date string
    if (defined($duration))
    {

      #cheat and the database to reformat the date for us
      $query = "SELECT DATE_FORMAT('$endDate', '$sqlDateFmt') FROM prep.flows";
      $db_date = $prepDB->prepare($query);
      $status = $db_date->execute;
      PrepUtils_handle_db_err($prepDB, $status, $query);
      ($endDate) = $db_date->fetchrow_array;

      #if we're "n Weeks Ending" or "n Weeks"
      if (($durationFormat == 1) || ($durationFormat == 2))
      {
        $time = "$duration ";

        if ($type eq "year")
        {
          $time .= "Year";
        }
        elsif ($type eq "month")
        {
          $time .= "Month";
        }
        elsif ($type eq "week")
        {
          $time .= "Week";
        }
        elsif ($type eq "day")
        {
          $time .= "Day";
        }

        #make the time period type plural, if needed
        if ($duration > 1)
        {
          $time .= "s";
        }

        $time .= " ";
      }

      #if we need to append an "Ending"
      if ($durationFormat == 1)
      {
        $time .= "Ending ";
      }

      #handle "n WE " style
      if ($durationFormat == 3)
      {
        $time = "$duration ";

        if ($type eq "year")
        {
          $time .= "YE ";
        }
        elsif ($type eq "month")
        {
          $time .= "ME ";
        }
        elsif ($type eq "week")
        {
          $time .= "WE ";
        }
        elsif ($type eq "day")
        {
          $time .= "DE ";
        }
      }

      #handle no duration style
      if ($durationFormat == 4)
      {
        $time = "";
      }

      #end date goes on the end of the alias regardless of formatting
      $time .= $endDate;

      $q_time = $prepDB->quote($time);
      $q_origTime = $prepDB->quote($origTime);
      $query = "UPDATE $masterTable SET $column=$q_time WHERE $column=$q_origTime";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);
    }
  }

  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs
        SET PID=NULL, opInfo='DONE', state='LOADED', lastAction=NOW()
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);
  PrepUtils_increment_successful_xforms($prepDB, $jobID);

  flow_telemetry($prepDB, $jobID, "Formatted dates in $colName");

  return(0);
}



#-------------------------------------------------------------------------
#
# Apply the "enrich dates" cell transform
#

sub prep_recipe_trans_cell_enrich_date
{
  my ($query, $dbOutput, $time, $type, $duration, $valEndDate, $overwrite);
  my ($valType, $valDuration, $origTime, $time, $opInfo, $q_opInfo, $pct);
  my ($dayOfWeek, $valDayOfWeek, $dateDiff, $dbOutput1, $numOps, $opsDone);
  my ($colID, $column, $masterTable, $q_time, $q_origTime, $colName, $status);
  my ($q_colName, $masterColTable, $action, $roundingDirection);
  my (@dateVals);

  my ($prepDB, $flowID, $jobID, $stepID, $noUpdate) = @_;


  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs SET PID=$$, opInfo='0|Formatting dates'
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #get the details of the action we're performing
  $query = "SELECT action FROM prep.recipes WHERE flowID=$flowID AND step=$stepID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($action) = $dbOutput->fetchrow_array;

  #extract our transform info from the recipe step, error out if bad step
  if ($action =~ m/^TRANS-CELL-ENRICH-DATE\|COL=(.*?)\|DUR=(.*?)\|TYPE=(.*?)\|OVERWRITE=(.*?)\|ROUNDDAY=(.*?)\|DIR=(.*)$/)
  {
    $colName = $1;
    $duration = $2;
    $type = $3;
    $overwrite = $4;
    $dayOfWeek = $5;
    $roundingDirection = $6;
  }
  else
  {
    return(-1);
  }

  $startTime = PrepUtils_get_current_timestamp($prepDB);

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #get the ID for the column we're transforming
  $q_colName = $prepDB->quote($colName);
  $query = "SELECT ID FROM $masterColTable WHERE name=$q_colName";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($colID) = $dbOutput->fetchrow_array;
  $column = "column_$colID";

  #set the "title" of the operation for UI
  PrepUtils_set_job_op_title($prepDB, $jobID, "Enriching date values in $colName");

  #grab a list of all date strings from the master table
  $query = "SELECT DISTINCT $column FROM $masterTable WHERE $column IS NOT NULL";
  $dbOutput = $prepDB->prepare($query);
  $status = $numOps = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #turn off SQL binary logging for performance benefits, since we're not
  #actually replicating this data
  $query = "SET sql_log_bin = 0";
  $prepDB->do($query);
  $query = "SET SESSION transaction_isolation='READ-UNCOMMITTED'";
  $prepDB->do($query);

  $opsDone = 0;
  while (($time) = $dbOutput->fetchrow_array)
  {

    #update detailed status info
    $opsDone++;
    if (($noUpdate < 1) && (($opsDone % 10) == 0))
    {
      $pct = ($opsDone / $numOps) * 100;
      $pct = int($pct);

      $opInfo = "$pct|Enriching dates";
      $q_opInfo = $prepDB->quote($opInfo);
      $query = "UPDATE prep.jobs SET opInfo=$q_opInfo WHERE ID=$jobID";
      $prepDB->do($query);
    }

    #save the original date string for later update comparison matching
    $origTime = $time;

    #if we're turning bare dates into CPG dates
    if (length($type) != 0)
    {

      #parse the (possible) date string
      @dateVals = prep_recipe_parse_date($prepDB, $time);
      $valDuration = $dateVals[0];
      $valType = $dateVals[1];
      $valEndDate = $dateVals[2];

      #if the date doesn't have a type/duration, add one if we're supposed to
      if (($overwrite eq "on") || ((!defined($valType)) || (!defined($valDuration))))
      {
        $time = "$duration $type ending $time";
      }
    }

    #parse the (possible) date string
    @dateVals = prep_recipe_parse_date($prepDB, $time);
    $valDuration = $dateVals[0];
    $valType = $dateVals[1];
    $valEndDate = $dateVals[2];

    #if we were able to parse the date string and we're rounding up the date
    if ((defined($valDuration)) && ($dayOfWeek > 0))
    {
      $query = "SELECT DAYOFWEEK('$valEndDate') FROM prep.flows";
      $dbOutput1 = $prepDB->prepare($query);
      $status = $dbOutput1->execute;
      PrepUtils_handle_db_err($prepDB, $status, $query);
      ($valDayOfWeek) = $dbOutput1->fetchrow_array;

      if ($roundingDirection eq "next")
      {
        $dateDiff = (7 - $valDayOfWeek) + $dayOfWeek;
      }
      elsif ($roundingDirection eq "prev")
      {
        if ($dayOfWeek > $valDayOfWeek)
        {
          $dateDiff = (7 - $dayOfWeek) + $valDayOfWeek;
        }
        else
        {
          $dateDiff = $valDayOfWeek - $dayOfWeek;
        }
        $dateDiff = 0 - $dateDiff;
      }

      if ($dateDiff != 0)
      {
        $query = "SELECT DATE(DATE_ADD('$valEndDate', INTERVAL $dateDiff DAY))
            FROM prep.flows";
        $dbOutput1 = $prepDB->prepare($query);
        $status = $dbOutput1->execute;
        PrepUtils_handle_db_err($prepDB, $status, $query);
        ($valEndDate) = $dbOutput1->fetchrow_array;
      }

      if ($valEndDate =~ m/^(\d+)-(\d+)-(\d+)/)
      {
        $valEndDate = "$2-$3-$1";
      }

      if ((defined($valType)) && (defined($valDuration)))
      {
        $time = "$valDuration $valType ending $valEndDate";
      }
      else
      {
        $time = $valEndDate;
      }
    }

    $q_time = $prepDB->quote($time);
    $q_origTime = $prepDB->quote($origTime);
    $query = "UPDATE $masterTable SET $column=$q_time WHERE $column=$q_origTime";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);
  }

  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs
        SET PID=NULL, opInfo='DONE', state='LOADED', lastAction=NOW()
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);
  PrepUtils_increment_successful_xforms($prepDB, $jobID);

  flow_telemetry($prepDB, $jobID, "Enriched dates in $colName");

  return(0);
}



#-------------------------------------------------------------------------
#
# Apply the selected "format UPC" cell transform
#

sub prep_recipe_trans_cell_upc
{
  my ($query, $dbOutput, $action, $colName, $timeFormat, $durationFormat);
  my ($masterTable, $masterColTable, $q_colName, $colID, $column, $time, $type);
  my ($endDate, $duration, $origTime, $q_origTime, $q_time, $sqlDateFmt);
  my ($db_date, $status, $pct, $opInfo, $numOps, $opsDone, $q_opInfo);
  my (@dateVals);

  my ($prepDB, $flowID, $jobID, $stepID, $noUpdate) = @_;


  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs SET PID=$$, opInfo='0|Formatting UPCs'
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #get the details of the action we're performing
  $query = "SELECT action FROM prep.recipes WHERE flowID=$flowID AND step=$stepID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($action) = $dbOutput->fetchrow_array;

  #extract our transform info from the recipe step, error out if bad step
  if ($action =~ m/^TRANS-CELL-UPC\|COL=(.*?)\|OP=(.*?)\|LENGTH=(.*?)\|$/)
  {
    $colName = $1;
    $formatOp = $2;
    $length = $3;
  }
  elsif ($action =~ m/^TRANS-CELL-UPC\|COL=(.*?)\|OP=(.*?)\|$/)
  {
    $colName = $1;
    $formatOp = $2;
  }
  else
  {
    return(-1);
  }

  $startTime = PrepUtils_get_current_timestamp($prepDB);

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #get the ID for the column we're transforming
  $q_colName = $prepDB->quote($colName);
  $query = "SELECT ID FROM $masterColTable WHERE name=$q_colName";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($colID) = $dbOutput->fetchrow_array;
  $column = "column_$colID";

  #set the "title" of the operation for UI
  PrepUtils_set_job_op_title($prepDB, $jobID, "Formatting UPCs in $colName");

  #turn off SQL binary logging for performance benefits, since we're not
  #actually replicating this data
  $query = "SET sql_log_bin = 0";
  $prepDB->do($query);
  $query = "SET SESSION transaction_isolation='READ-UNCOMMITTED'";
  $prepDB->do($query);

  #format UPCs
  $query = "SELECT DISTINCT $column FROM $masterTable WHERE $column IS NOT NULL";
  $dbOutput = $prepDB->prepare($query);
  $status = $numOps = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);

  $opsDone = 0;
  while (($upc) = $dbOutput->fetchrow_array)
  {

    #update detailed status info
    $opsDone++;
    if (($noUpdate < 1) && (($opsDone % 10) == 0))
    {
      $pct = ($opsDone / $numOps) * 100;
      $pct = int($pct);

      $opInfo = "$pct|Formatting UPCs";
      $q_opInfo = $prepDB->quote($opInfo);
      $query = "UPDATE prep.jobs SET opInfo=$q_opInfo WHERE ID=$jobID";
      $prepDB->do($query);
    }


    #trim zeroes off the beginning to cut the UPC down to specified size
    if ($formatOp eq "trimSizeLeadZeroes")
    {
      $newUPC = $upc;
      while ((length($newUPC) > $length) && ($newUPC =~ m/^0(.*)$/))
      {
        $newUPC = $1;
      }

      if ($upc ne $newUPC)
      {
        $q_upc = $prepDB->quote($upc);
        $q_newUPC = $prepDB->quote($newUPC);
        $query = "UPDATE $masterTable SET $column=$q_newUPC WHERE $column=$q_upc";
        $status = $prepDB->do($query);
        PrepUtils_handle_db_err($prepDB, $status, $query);
      }
    }

    #trim digits off the beginning to cut the UPC down to specified size
    elsif ($formatOp eq "trimSizeBegin")
    {
      $newUPC = $upc;
      if (length($newUPC) > $length)
      {
        $tmp = 0 - $length;
        $newUPC = substr($newUPC, $tmp);
      }

      if ($upc ne $newUPC)
      {
        $q_upc = $prepDB->quote($upc);
        $q_newUPC = $prepDB->quote($newUPC);
        $query = "UPDATE $masterTable SET $column=$q_newUPC WHERE $column=$q_upc";
        $status = $prepDB->do($query);
        PrepUtils_handle_db_err($prepDB, $status, $query);
      }
    }

    #trim zeroes off the end to cut the UPC down to specified size
    elsif ($formatOp eq "trimSizeTrailZeroes")
    {
      $newUPC = $upc;
      while ((length($newUPC) > $length) && ($newUPC =~ m/^(.*)0$/))
      {
        $newUPC = $1;
      }

      if ($upc ne $newUPC)
      {
        $q_upc = $prepDB->quote($upc);
        $q_newUPC = $prepDB->quote($newUPC);
        $query = "UPDATE $masterTable SET $column=$q_newUPC WHERE $column=$q_upc";
        $status = $prepDB->do($query);
        PrepUtils_handle_db_err($prepDB, $status, $query);
      }
    }

    #trim digits off the end to cut the UPC down to the specified size
    elsif ($formatOp eq "trimSizeEnd")
    {
      $newUPC = $upc;

      if (length($newUPC) > $length)
      {
        $newUPC = substr($newUPC, 0, $length);
      }

      if ($upc ne $newUPC)
      {
        $q_upc = $prepDB->quote($upc);
        $q_newUPC = $prepDB->quote($newUPC);
        $query = "UPDATE $masterTable SET $column=$q_newUPC WHERE $column=$q_upc";
        $status = $prepDB->do($query);
        PrepUtils_handle_db_err($prepDB, $status, $query);
      }
    }

    #prepend zeroes to build the UPC up to the specified size
    elsif ($formatOp eq "addSizeLeadZeroes")
    {
      $newUPC = $upc;

      if (length($upc) < $length)
      {
        $zeroesToAdd = $length - length($upc);
        for ($i = 0; $i < $zeroesToAdd; $i++)
        {
          $newUPC = "0" . $newUPC;
        }
      }

      if ($upc ne $newUPC)
      {
        $q_upc = $prepDB->quote($upc);
        $q_newUPC = $prepDB->quote($newUPC);
        $query = "UPDATE $masterTable SET $column=$q_newUPC WHERE $column=$q_upc";
        $status = $prepDB->do($query);
        PrepUtils_handle_db_err($prepDB, $status, $query);
      }
    }

    #append zeroes to build the UPC up to the specified size
    elsif ($formatOp eq "addSizeTrailZeroes")
    {
      $newUPC = $upc;

      if (length($upc) < $length)
      {
        $zeroesToAdd = $length - length($upc);
        for ($i = 0; $i < $zeroesToAdd; $i++)
        {
          $newUPC = $newUPC . "0";
        }
      }

      if ($upc ne $newUPC)
      {
        $q_upc = $prepDB->quote($upc);
        $q_newUPC = $prepDB->quote($newUPC);
        $query = "UPDATE $masterTable SET $column=$q_newUPC WHERE $column=$q_upc";
        $status = $prepDB->do($query);
        PrepUtils_handle_db_err($prepDB, $status, $query);
      }
    }

    #strip all leading zeroes
    elsif ($formatOp eq "trimLeadZeroes")
    {
      $upc =~ m/^0*(.*)$/;
      $newUPC = $1;

      if ($upc ne $newUPC)
      {
        $q_upc = $prepDB->quote($upc);
        $q_newUPC = $prepDB->quote($newUPC);
        $query = "UPDATE $masterTable SET $column=$q_newUPC WHERE $column=$q_upc";
        $status = $prepDB->do($query);
        PrepUtils_handle_db_err($prepDB, $status, $query);
      }
    }

    #strip all trailing zeroes
    elsif ($formatOp eq "trimTrailZeroes")
    {
      $upc =~ m/^(.*?)0*$/;
      $newUPC = $1;

      if ($upc ne $newUPC)
      {
        $q_upc = $prepDB->quote($upc);
        $q_newUPC = $prepDB->quote($newUPC);
        $query = "UPDATE $masterTable SET $column=$q_newUPC WHERE $column=$q_upc";
        $status = $prepDB->do($query);
        PrepUtils_handle_db_err($prepDB, $status, $query);
      }
    }

    #remove any hyphens in the UPC
    elsif ($formatOp eq "removeHyphens")
    {
      $newUPC = $upc;
      $newUPC =~ s/\-//g;

      if ($upc ne $newUPC)
      {
        $q_upc = $prepDB->quote($upc);
        $q_newUPC = $prepDB->quote($newUPC);
        $query = "UPDATE $masterTable SET $column=$q_newUPC WHERE $column=$q_upc";
        $status = $prepDB->do($query);
        PrepUtils_handle_db_err($prepDB, $status, $query);
      }
    }

  }

  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs
        SET PID=NULL, opInfo='DONE', state='LOADED', lastAction=NOW()
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);
  PrepUtils_increment_successful_xforms($prepDB, $jobID);

  flow_telemetry($prepDB, $jobID, "Formatted UPCs in $colName");

  return(0);
}



#-------------------------------------------------------------------------
#
# Apply the selected "normalize weights and measures" cell transform
#

sub prep_recipe_trans_cell_weights
{
  my ($query, $dbOutput, $action, $colName);

  my ($prepDB, $flowID, $jobID, $stepID, $noUpdate) = @_;


  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs
        SET PID=$$, opInfo='0|Normalizing weights and measures'
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #get the details of the action we're performing
  $query = "SELECT action FROM prep.recipes WHERE flowID=$flowID AND step=$stepID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($action) = $dbOutput->fetchrow_array;

  #extract our transform info from the recipe step, error out if bad step
  if ($action =~ m/^TRANS-CELL-WEIGHTS\|COL=(.*?)\|SYSTEM=(.*?)\|VOL=(.*?)\|WEIGHT=(.*?)\|DIM=(.*?)\|$/)
  {
    $colName = $1;
    $measSystem = $2;
    $volPref = $3;
    $weightPref = $4;
    $dimPref = $5;
  }
  else
  {
    return(-1);
  }

  $startTime = PrepUtils_get_current_timestamp($prepDB);

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #get the ID for the column we're transforming
  $q_colName = $prepDB->quote($colName);
  $query = "SELECT ID FROM $masterColTable WHERE name=$q_colName";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($colID) = $dbOutput->fetchrow_array;
  $column = "column_$colID";

  #set the "title" of the operation for UI
  PrepUtils_set_job_op_title($prepDB, $jobID, "Normalizing weights and measures in $colName");

  #turn off SQL binary logging for performance benefits, since we're not
  #actually replicating this data
  $query = "SET sql_log_bin = 0";
  $prepDB->do($query);
  $query = "SET SESSION transaction_isolation='READ-UNCOMMITTED'";
  $prepDB->do($query);

  #normalize weights and measures
  $query = "SELECT DISTINCT $column FROM $masterTable WHERE $column IS NOT NULL";
  $dbOutput = $prepDB->prepare($query);
  $status = $numOps = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);

  $opsDone = 0;
  while (($measValStr) = $dbOutput->fetchrow_array)
  {

    #update detailed status info
    $opsDone++;
    if (($noUpdate < 1) && (($opsDone % 10) == 0))
    {
      $pct = ($opsDone / $numOps) * 100;
      $pct = int($pct);

      $opInfo = "$pct|Formatting Weights & Measures";
      $q_opInfo = $prepDB->quote($opInfo);
      $query = "UPDATE prep.jobs SET opInfo=$q_opInfo WHERE ID=$jobID";
      $prepDB->do($query);
    }

    $newMeasValStr = "";

    #if we're converting from standard to metric
    if ($measSystem eq "metric")
    {
      if (($measValStr =~ m/([\d+\.]+) fluid ounce/i) ||
          ($measValStr =~ m/([\d+\.]+) fl oz/i))
      {
        $val = $1;
        if ($volPref eq "ml")
        {
          $val = $val * 29.5735;
          $val = sprintf("%.0f", $val);
          $newMeasValStr = "$val mL";
        }
        elsif ($volPref eq "l")
        {
          $val = $val * 0.0295735000007998;
          $val = sprintf("%.1f", $val);
          $newMeasValStr = "$val liter";
        }
      }

      elsif (($measValStr =~ m/([\d+\.]+) ounce/i) ||
          ($measValStr =~ m/([\d+\.]+) oz/i))
      {
        $val = $1;
        if ($weightPref eq "g")
        {
          $val = $val * 28.3495;
          $val = sprintf("%.0f", $val);
          $newMeasValStr = "$val g";
        }
        elsif ($weightPref eq "kg")
        {
          $val = $val / 35.274;
          $val = sprintf("%.1f", $val);
          $newMeasValStr = "$val kg";
        }
      }

      elsif (($measValStr =~ m/([\d+\.]+) inch/i) ||
          ($measValStr =~ m/([\d+\.]+) in/i))
      {
        $val = $1;
        if ($dimPref eq "mm")
        {
          $val = $val * 25.4;
          $val = sprintf("%.0f", $val);
          $newMeasValStr = "$val mm";
        }
        elsif ($dimPref eq "cm")
        {
          $val = $val * 2.54;
          $val = sprintf("%.1f", $val);
          $newMeasValStr = "$val cm";
        }
      }
    }

    #else we're converting from metric to standard
    else
    {
      if (($measValStr =~ m/([\d+\.]+) (mL)/i) ||
          ($measValStr =~ m/([\d+\.]+) (liter)/i))
      {
        $val = $1;
        if (lc($2) eq "liter")
        {
          $val = $val * 1000;
        }
        if ($volPref eq "floz")
        {
          $val = $val / 29.574;
          $val = sprintf("%.1f", $val);
          if ($val =~ m/(\d+)\.0/)
          {
            $val = $1;
          }
          $newMeasValStr = "$val fluid ounce";
        }
        elsif ($volPref eq "gal")
        {
          $val = $val / 3785;
          $val = sprintf("%.1f", $val);
          $newMeasValStr = "$val gallon";
        }
      }

      elsif (($measValStr =~ m/([\d+\.]+) gram/i) ||
          ($measValStr =~ m/([\d+\.]+) g/i))
      {
        $val = $1;
        if ($weightPref eq "oz")
        {
          $val = $val / 28.35;
          $val = sprintf("%.0f", $val);
          if ($val =~ m/(\d+)\.0/)
          {
            $val = $1;
          }
          $newMeasValStr = "$val ounce";
        }
        elsif ($weightPref eq "lbs")
        {
          $val = $val / 454;
          $val = sprintf("%.1f", $val);
          $newMeasValStr = "$val lb";
        }
      }

      elsif (($measValStr =~ m/([\d+\.]+) (mm)/i) ||
          ($measValStr =~ m/([\d+\.]+) (cm)/i))
      {
        $val = $1;
        if (lc($2) eq "cm")
        {
          $val = $val * 10;;
        }
        if ($dimPref eq "in")
        {
          $val = $val / 25.4;
          $val = sprintf("%.0f", $val);
          $newMeasValStr = "$val mm";
        }
        elsif ($dimPref eq "ft")
        {
          $val = $val / 305;
          $val = sprintf("%.1f", $val);
          $newMeasValStr = "$val cm";
        }
      }
    }

    #if we came up with a new measure value string, put it in place
    if (length($newMeasValStr) > 0)
    {
      $q_measVal = $prepDB->quote($measValStr);
      $q_newMeasVal = $prepDB->quote($newMeasValStr);
      $query = "UPDATE $masterTable SET $column=$q_newMeasVal
          WHERE $column=$q_measVal";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);
    }
  }

  if ($noUpdate < 1)
  {
    $query = "UPDATE prep.jobs
        SET PID=NULL, opInfo='DONE', state='LOADED', lastAction=NOW()
        WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);
  PrepUtils_increment_successful_xforms($prepDB, $jobID);

  flow_telemetry($prepDB, $jobID, "Normalized weights and measures in $colName");

  return(0);
}



#-------------------------------------------------------------------------
#
# Apply all of the steps in the transform recipe for the selected data flow
# to the selected job.
#

sub prep_recipe_apply
{
  my ($query, $dbOutput, $step, $action, $numOps, $opsDone, $pct, $opInfo);
  my ($q_opInfo, $runningJobs, $userID, $status, $state);

  my ($prepDB, $kapDB, $flowID, $jobID) = @_;


  flow_telemetry($prepDB, $jobID, "Applying data transform recipe");
  PrepUtils_store_scaled_cloud_load($prepDB, $jobID);

  #check the job's state to make sure it's OK for us to run (should be after
  #the COLUMN-TYPES operation)
  $query = "SELECT opInfo, state, userID FROM prep.jobs WHERE ID=$jobID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($opInfo, $state, $userID) = $dbOutput->fetchrow_array;
  if ($state ne "COLUMN-TYPES")
  {
    flow_telemetry($prepDB, $jobID, "WARNING: recipe_apply called out of sequence: $state");
    return;
  }

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "Applying data transform recipe");

  #if the system is fully loaded, back off and wait for an open process slot
  $okToRun = prep_flow_available_job_slot($prepDB, $kapDB, $jobID, $userID);
  while (!$okToRun)
  {
    $query = "UPDATE prep.jobs
        SET PID=$$, opInfo='0|Waiting in processing queue', state='RECIPE-WAIT'
        WHERE ID=$jobID";
    $prepDB->do($query);

    PrepUtils_set_job_op_details($prepDB, $jobID, "Waiting in processing queue");

    sleep(120);

    $okToRun = prep_flow_available_job_slot($prepDB, $kapDB, $jobID, $userID);
  }

  #et initial status
  $query = "UPDATE prep.jobs
      SET PID=$$, opInfo='0|Applying data transforms', state='RECIPE-APPLY'
      WHERE ID=$jobID";
  $prepDB->do($query);

  #get all of the flow's recipe steps, in order
  $query = "SELECT step, action FROM prep.recipes
      WHERE flowID=$flowID ORDER BY step";
  $dbOutput = $prepDB->prepare($query);
  $status = $numOps = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  #$numOps++;

  #run through each recipe step
  $opsDone = 0;
  while (($step, $action) = $dbOutput->fetchrow_array)
  {

    #update detailed status info
    $pct = ($opsDone / $numOps) * 100;
    $pct = int($pct);
    $opInfo = "$pct|Applying data transforms";
    $q_opInfo = $prepDB->quote($opInfo);
    $query = "UPDATE prep.jobs SET opInfo=$q_opInfo, lastAction=NOW()
        WHERE ID=$jobID";
    $prepDB->do($query);

    PrepUtils_set_job_op_pct($prepDB, $jobID, "$pct");

    $opsDone++;

    if ($action =~ m/^TRANS-COL-TYPE\|/)
    {
      prep_recipe_trans_col_type($prepDB, $flowID, $jobID, $step, 1);
    }
    elsif ($action =~ m/^TRANS-COL-NAME\|/)
    {
      prep_recipe_trans_col_name($prepDB, $flowID, $jobID, $step, 1);
    }
    elsif ($action =~ m/^TRANS-COL-SPLIT\|/)
    {
      prep_recipe_trans_col_split($prepDB, $flowID, $jobID, $step, 1);
    }
    elsif ($action =~ m/^TRANS-COL-SPLIT-LENGTH\|/)
    {
      prep_recipe_trans_col_split_length($prepDB, $flowID, $jobID, $step, 1);
    }
    elsif ($action =~ m/^TRANS-COL-LOOKUP\|/)
    {
      prep_recipe_trans_col_lookup($prepDB, $flowID, $jobID, $step, 1);
    }
    elsif ($action =~ m/^TRANS-COL-INSERT\|/)
    {
      prep_recipe_trans_col_insert($prepDB, $flowID, $jobID, $step, 1);
    }
    elsif ($action =~ m/^TRANS-COL-COPY\|/)
    {
      prep_recipe_trans_col_copy($prepDB, $flowID, $jobID, $step, 1);
    }
    elsif ($action =~ m/^TRANS-COL-MERGE\|/)
    {
      prep_recipe_trans_col_merge($prepDB, $flowID, $jobID, $step, 1);
    }
    elsif ($action =~ m/^TRANS-COL-DISCARD\|/)
    {
      prep_recipe_trans_col_discard($prepDB, $flowID, $jobID, $step, 1);
    }
    elsif ($action =~ m/^TRANS-COL-DISCARD-EMPTY\|/)
    {
      prep_recipe_trans_col_discard_empty($prepDB, $flowID, $jobID, $step, 1);
    }
    elsif ($action =~ m/^TRANS-COL-TRIM-VALUES\|/)
    {
      prep_recipe_trans_col_trim_values($prepDB, $flowID, $jobID, $step, 1);
    }
    elsif ($action =~ m/^TRANS-COL-TRIM-DATA\|/)
    {
      prep_recipe_trans_col_trim_data($prepDB, $flowID, $jobID, $step, 1);
    }
    elsif ($action =~ m/^TRANS-COL-PRODUCT-MATCH\|/)
    {
      prep_recipe_trans_col_product_match($prepDB, $flowID, $jobID, $step, 1);
    }
    elsif ($action =~ m/^TRANS-CELL-APPEND-PREPEND\|/)
    {
      prep_recipe_trans_cell_append_prepend($prepDB, $flowID, $jobID, $step, 1);
    }
    elsif ($action =~ m/^TRANS-CELL-SET\|/)
    {
      prep_recipe_trans_cell_set($prepDB, $flowID, $jobID, $step, 1);
    }
    elsif ($action =~ m/^TRANS-CELL-REPLACE\|/)
    {
      prep_recipe_trans_cell_replace($prepDB, $flowID, $jobID, $step, 1);
    }
    elsif ($action =~ m/^TRANS-CELL-SEARCH-REPLACE\|/)
    {
      prep_recipe_trans_cell_search_replace($prepDB, $flowID, $jobID, $step, 1);
    }
    elsif ($action =~ m/^TRANS-CELL-ROUND\|/)
    {
      prep_recipe_trans_cell_round($prepDB, $flowID, $jobID, $step, 1);
    }
    elsif ($action =~ m/^TRANS-CELL-CEILING\|/)
    {
      prep_recipe_trans_cell_ceiling($prepDB, $flowID, $jobID, $step, 1);
    }
    elsif ($action =~ m/^TRANS-CELL-FLOOR\|/)
    {
      prep_recipe_trans_cell_floor($prepDB, $flowID, $jobID, $step, 1);
    }
    elsif ($action =~ m/^TRANS-CELL-NAZERO\|/)
    {
      prep_recipe_trans_cell_nazero($prepDB, $flowID, $jobID, $step, 1);
    }
    elsif ($action =~ m/^TRANS-CELL-HTML\|/)
    {
      prep_recipe_trans_cell_html($prepDB, $flowID, $jobID, $step, 1);
    }
    elsif ($action =~ m/^TRANS-CELL-SPACE\|/)
    {
      prep_recipe_trans_cell_space($prepDB, $flowID, $jobID, $step, 1);
    }
    elsif ($action =~ m/^TRANS-CELL-CASE\|/)
    {
      prep_recipe_trans_cell_case($prepDB, $flowID, $jobID, $step, 1);
    }
    elsif ($action =~ m/^TRANS-CELL-DATE\|/)
    {
      prep_recipe_trans_cell_date($prepDB, $flowID, $jobID, $step, 1);
    }
    elsif ($action =~ m/^TRANS-CELL-ENRICH-DATE\|/)
    {
      prep_recipe_trans_cell_enrich_date($prepDB, $flowID, $jobID, $step, 1);
    }
    elsif ($action =~ m/^TRANS-CELL-UPC\|/)
    {
      $status = prep_recipe_trans_cell_upc($prepDB, $flowID, $jobID, $step, 1);
    }
    elsif ($action =~ m/^TRANS-CELL-WEIGHTS\|/)
    {
      $status = prep_recipe_trans_cell_weights($prepDB, $flowID, $jobID, $step, 1);
    }
  }

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");
  PrepUtils_set_job_op_details($prepDB, $jobID, "");
  PrepUtils_set_job_op_extra($prepDB, $jobID, "");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "");
  PrepUtils_set_job_op_speed($prepDB, $jobID, "");

  flow_telemetry($prepDB, $jobID, "Done applying data transform recipe");

  $query = "UPDATE prep.jobs SET PID=NULL, opInfo='DONE', lastAction=NOW()
      WHERE ID=$jobID";
  $prepDB->do($query);
}



#-------------------------------------------------------------------------


1;
