#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRMeasures;
use Lib::DSRUtils;
use Lib::Reports;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get the CGI input variables
  $rptID = $q->param('rpt');
  $visID = $q->param('v');

  #get any "fixed" dimensions (used for expanded reports)
  $fProd = $q->param('p');
  $fGeo = $q->param('g');
  $fTime = $q->param('t');

  $db = KAPutil_connect_to_database();

  #if we're being called as part of a PPT export
  if ($userID < 1)
  {
    $userID = $q->param('u');
    $query = "SELECT acctType FROM app.users WHERE ID=$userID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($acctType) = $dbOutput->fetchrow_array;
  }

  #NB: graph_x contains the dimension we're using as the data set

  #get the list of axes & selected dimension items from the database
  $query = "SELECT dsID, design, graph_x FROM visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($dsID, $graphDesign, $graph_x) = $dbOutput->fetchrow_array;

  ($productIDstring, $geographyIDstring, $timeIDstring, $measureIDstring) = reports_get_selected_items($db, $visID, $userID, $acctType);

  #get slicer configuration
  $query = "SELECT slicers FROM cubes WHERE ID=$rptID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($slicersStr) = $dbOutput->fetchrow_array;

  print("Content-type: text/plain\n\n");

  #assemble report cube name
  $dsSchema = "datasource_" . $dsID;
  $rptCube = "_rptcube_" . $rptID;

  #get the ID of the measure we're graphing
  $measureIDstring =~ m/(\d+)/;
  $measureID = $1;

  #if we're dealing with a fixed dimension (used for expanded reports)
  if (length($fProd) > 0)
  {
    $productIDstring = $fProd;
  }
  if (length($fGeo) > 0)
  {
    $geographyIDstring = $fGeo;
  }
  if (length($fTime) > 0)
  {
    $timeIDstring = $fTime;
  }

  @selProds = split(/,/, $productIDstring);
  @selGeos = split(/,/, $geographyIDstring);
  @selTimes = split(/,/, $timeIDstring);
  @selMeasures = split(/,/, $measureIDstring);

  #fetch the item names for our data set
  %setNames = dsr_get_item_name_hash($db, $dsSchema, $graph_x);

  #escape any special characters in our category & set names
  foreach $id (keys %setNames)
  {
    $setNames{$id} =~ s/\'//g;
  }

  #create an array of the categories we need to display, and go ahead and set
  #up our "single selection" dimension IDs while we're at it
  if ($graph_x eq "p")
  {
    @setIDs = split(/,/, $productIDstring);

    $q_geography = $db->quote($selGeos[0]);
    $q_time = $db->quote($selTimes[0]);
  }
  elsif ($graph_x eq "g")
  {
    @setIDs = split(/,/, $geographyIDstring);

    $q_product = $db->quote($selProds[0]);
    $q_time = $db->quote($selTimes[0]);
  }
  elsif ($graph_x eq "t")
  {
    @setIDs = split(/,/, $timeIDstring);

    $q_product = $db->quote($selProds[0]);
    $q_geography = $db->quote($selGeos[0]);
  }

  #build up the SQL string we're going to use for selections
  $itemStr = "";
  foreach $item (@setIDs)
  {
    $itemStr .= "'$item',";
  }
  chop($itemStr);


  # ------- parse graph design info ----------

  #handle a "locked" measure for the graph
  if ($graphDesign =~ m/,measure:(\d+),/)
  {
    $lockedMeasure = $1;
    if ($lockedMeasure > 0)
    {
      $measureID = $lockedMeasure;
    }
  }

  #handle a caption (chart title)
  if ($graphDesign =~ m/,caption:\"(.*?)\",/)
  {
    $caption = $1;
  }

  #handle a subcaption (chart subtitle)
  if ($graphDesign =~ m/,subcaption:\"(.*?)\",/)
  {
    $subcaption = $1;
  }


  $captionFontColor = reports_get_style($graphDesign, "captionFontColor");
  if (length($captionFontColor) < 1)
  {
    $captionFontColor = reports_chart_design_default("captionFontColor");
  }
  $captionFontColor = "captionFontColor='$captionFontColor'";

  $captionAlignment = reports_get_style($graphDesign, "captionAlignment");
  if (length($captionAlignment) < 1)
  {
    $captionAlignment = reports_chart_design_default("captionAlignment");
  }
  $captionAlignment = "captionAlignment='$captionAlignment'";

  $captionFontSize = reports_get_style($graphDesign, "captionFontSize");
  if (length($captionFontSize) < 1)
  {
    $captionFontSize = reports_chart_design_default("captionFontSize");
  }
  $captionFontSize = "captionFontSize='$captionFontSize'";

  $captionFont = reports_get_style($graphDesign, "captionFont");
  if (length($captionFont) > 3)
  {
    $captionFont = "captionFont='$captionFont'";
  }


  #handle background color
  $bgColor = reports_get_style($graphDesign, "bgColor");
  if (length($bgColor) < 6)
  {
    $bgColor = reports_chart_design_default("bgColor");
  }
  $canvasBgColor = "canvasBgColor='$bgColor'";
  $bgColor = "bgColor='$bgColor'";


  #handle a border (color & thickness)
  $showBorder = reports_get_style($graphDesign, "showBorder");
  if (length($showBorder) < 1)
  {
    $showBorder = reports_chart_design_default("showBorder");
  }
  $showBorder = "showBorder='$showBorder'";

  $borderColor = reports_get_style($graphDesign, "borderColor");
  if (length($borderColor) < 6)
  {
    $borderColor = reports_chart_design_default("borderColor");
  }
  $borderColor = "borderColor='$valueFontColor'";

  $borderThickness = reports_get_style($graphDesign, "borderThickness");
  if (length($borderThickness) < 1)
  {
    $borderThickness = reports_chart_design_default("borderThickness");
  }
  $borderThickness = "borderThickness='$borderThickness'";


  #handle graph legend styling
  $showLegend = reports_get_style($graphDesign, "showLegend");
  if (length($showLegend) < 1)
  {
    $showLegend = reports_chart_design_default("showLegend");
  }
  $showLegend = "showLegend='$showLegend'";

  $legendItemFontColor = reports_get_style($graphDesign, "legendItemFontColor");
  if (length($legendItemFontColor) < 6)
  {
    $legendItemFontColor = reports_chart_design_default("legendItemFontColor");
  }
  $legendItemFontColor = "legendItemFontColor='$legendItemFontColor'";

  $legendPosition = reports_get_style($graphDesign, "legendPosition");
  if (length($legendPosition) < 1)
  {
    $legendPosition = reports_chart_design_default("legendPosition");
  }
  $legendPosition = "legendPosition='$legendPosition'";

  $legendItemFont = reports_get_style($graphDesign, "legendItemFont");
  if (length($legendItemFont) < 1)
  {
    $legendItemFont = reports_chart_design_default("legendItemFont");
  }
  $legendItemFont = "legendItemFont='$legendItemFont'";

  $legendItemFontSize = reports_get_style($graphDesign, "legendItemFontSize");
  if (length($legendItemFontSize) < 1)
  {
    $legendItemFontSize = reports_chart_design_default("legendItemFontSize");
  }
  $legendItemFontSize = "legendItemFontSize='$legendItemFontSize'";


  #handle data label styling
  $showLabels = reports_get_style($graphDesign, "showLabels");
  if (length($showLabels) < 1)
  {
    $showLabels = reports_chart_design_default("showLabels");
  }
  $showChildLabels = "showChildLabels='$showLabels'";
  $showLabels = "showLabels='$showLabels'";

  $labelFontColor = reports_get_style($graphDesign, "labelFontColor");
  if (length($labelFontColor) < 1)
  {
    $labelFontColor = reports_chart_design_default("labelFontColor");
  }
  $labelFontColor = "labelFontColor='$labelFontColor'";

  $labelDisplay = reports_get_style($graphDesign, "labelDisplay");
  if (length($labelDisplay) < 1)
  {
    $labelDisplay = reports_chart_design_default("labelDisplay");
  }
  $labelDisplay = "labelDisplay='$labelDisplay'";

  $labelStep = reports_get_style($graphDesign, "labelStep");
  if (length($labelStep) < 1)
  {
    $labelStep = reports_chart_design_default("labelStep");
  }
  $labelStep = "labelStep='$labelStep'";

  $labelFontSize = reports_get_style($graphDesign, "labelFontSize");
  if (length($labelFontSize) < 1)
  {
    $labelFontSize = reports_chart_design_default("labelFontSize");
  }
  $labelFontSize = "labelFontSize='$labelFontSize'";

  $labelFont = reports_get_style($graphDesign, "labelFont");
  if (length($labelFont) > 1)
  {
    $labelFont = "labelFont='$labelFont'";
  }


  #handle data value styling
  $showValues = reports_get_style($graphDesign, "showValues");
  if (length($showValues) < 1)
  {
    $showValues = reports_chart_design_default("showValues");
  }
  $showValues = "showValues='$showValues'";

  $valueFontColor = reports_get_style($graphDesign, "valueFontColor");
  if (length($valueFontColor) < 6)
  {
    $valueFontColor = reports_chart_design_default("valueFontColor");
  }
  $valueFontColor = "valueFontColor='$valueFontColor'";

  $formatNumberScale = reports_get_style($graphDesign, "formatNumberScale");
  if (length($formatNumberScale) < 1)
  {
    $formatNumberScale = reports_chart_design_default("formatNumberScale");
  }
  $formatNumberScale = "formatNumberScale='$formatNumberScale'";

  $decimals = reports_get_style($graphDesign, "decimals");
  if (length($decimals) < 1)
  {
    $decimals = reports_chart_design_default("decimals");
  }
  $decimals = "decimals='$decimals'";

  $placeValuesInside = reports_get_style($graphDesign, "placeValuesInside");
  if (length($placeValuesInside) < 1)
  {
    $placeValuesInside = reports_chart_design_default("placeValuesInside");
  }
  $placeValuesInside = "placeValuesInside='$placeValuesInside'";

  $valueFontSize = reports_get_style($graphDesign, "valueFontSize");
  if (length($valueFontSize) < 1)
  {
    $valueFontSize = reports_chart_design_default("valueFontSize");
  }
  $valueFontSize = "valueFontSize='$valueFontSize'";

  $valueFont = reports_get_style($graphDesign, "valueFont");
  if (length($valueFont) > 3)
  {
    $valueFont = "valueFont='$valueFont'";
  }

  $showValuesBg = reports_get_style($graphDesign, "showValuesBg");
  if ($showValuesBg > 0)
  {
    $valueBgColor = reports_get_style($graphDesign, "valueBgColor");
    $valueBgColor = "valueBgColor='$valueBgColor'";
    $valueBgAlpha = reports_get_style($graphDesign, "valueBgAlpha");
    $valueBgAlpha = 100 - $valueBgAlpha;
    $valueBgAlpha = "valueBgAlpha='$valueBgAlpha'";
  }


  #handle color gradient start/end points
  $minColor = reports_get_style($graphDesign, "minColor");
  $maxColor = reports_get_style($graphDesign, "maxColor");
  if (length($minColor) < 5)
  {
    $minColor = "fd625e";
  }
  if (length($maxColor) < 5)
  {
    $maxColor = "01b8aa";
  }


  #see if we're being asked to generate an "All Others" slice
  $allOthers = reports_get_style($graphDesign, "allOthers");

  #if the titles have expandable tag(s), expand them
  $caption = reports_expand_dim_tags($db, $dsSchema, $caption, $productIDstring, $geographyIDstring, $timeIDstring, $measureIDstring);
  $subcaption = reports_expand_dim_tags($db, $dsSchema, $subcaption, $productIDstring, $geographyIDstring, $timeIDstring, $measureIDstring);

  # ------- end of design parsing ----------


  #get our measure format info, and apply as much of it as we can
  $formatStr = DSRmeasures_get_format($db, $dsSchema, $measureID);
  @formats = split(',', $formatStr);
  if (length($decimals < 1))
  {
    $decimals = "decimals='$formats[0]'"
  }

  $sValdecimals = $formats[0];
  $formatNumber = $formats[1];
  $numberPrefix = "";
  $numberSuffix = "";
  if ($formats[2] == 1)
  {
    $numberPrefix = "\$";
  }
  elsif ($formats[2] == 2)
  {
    $numberSuffix = "%";
  }

  #print out the XML chart info
  print <<XML_LABEL;
<chart
  theme='zune'
  animation='0'

  $bgColor
  $canvasBgColor
  canvasBgAlpha='0'

  $showBorder
  $borderColor
  $borderThickness

  $showLegend
  $legendPosition
  $legendItemFontColor
  $legendItemFont
  $legendItemFontSize

  caption='$caption'
  subcaption='$subcaption'
  $captionFontColor
  $captionAlignment
  $captionFontSize
  $captionFont

  $showLabels
  $showChildLabels
  $labelFontColor
  $labelDisplay
  $labelFontSize
  $labelFont
  labelGlow='0'

  $showValues
  $valueFontColor
  $formatNumberScale
  $decimals
  $placeValuesInside
  $valueFontSize
  $valueFont
  $valueBgColor
  $valueBgAlpha

  numberPrefix='$numberPrefix'
  numberSuffix='$numberSuffix'
  plottooltext="\$label $numberPrefix\$svalue">
XML_LABEL

  #if our dimension series is anything other than measure, set the measure
  #NB: If our series is measures, we'll set them below and overwrite this
  $measureCol = "measure_" . $measureID;

  #if the user has specified a slicer, let's add it to the where clause
  if ($graph_x eq "p")
  {
    @slicers = split(',', $slicersStr);
    foreach $slicer (@slicers)
    {
      $slicer =~ m/(PSEG_\d+):(\d+)/;
      $segID = $1;
      $segmentID = $2;

      if ($segmentID > 0)
      {
        $tmp = "SMT_$segmentID";
        $segmentName = $db->quote($setNames{$tmp});
        $whereClause .= " AND $segID = $segmentName";
      }
    }
  }


  ### chart filtering implementation ###

  $filterMeas1 = reports_get_style($graphDesign, "filterMeas1");
  $filterOp1 = reports_get_style($graphDesign, "filterOp1");
  $filterNum1 = reports_get_style($graphDesign, "filterNum1");

  if (($filterMeas1 > 0) && (length($filterNum1) > 0))
  {
    $filterMeas1 = "measure_" . $filterMeas1;

    if ($filterOp1 eq "gt")
    {
      $filterOp1 = ">";
      $whereClause .= " AND $filterMeas1 > $filterNum1";
    }
    elsif ($filterOp1 eq "lt")
    {
      $filterOp1 = "<";
      $whereClause .= " AND $filterMeas1 < $filterNum1";
    }
    elsif ($filterOp1 eq "eq")
    {
      $filterOp1 = "=";
      $whereClause .= " AND $filterMeas1 = $filterNum1";
    }

    elsif ($filterOp1 eq "top")
    {

      #gate the number of top items we're going to show
      if ($filterNum1 > 5000)
      {
        $filterNum1 = 5000;
      }
      elsif ($filterNum1 < 0)
      {
        $filterNum1 = 1;
      }
      $whereClause .= " ORDER BY $filterMeas1 DESC LIMIT $filterNum1";
    }

    elsif ($filterOp1 eq "bottom")
    {

      #gate the number of top items we're going to show
      if ($filterNum1 > 5000)
      {
        $filterNum1 = 5000;
      }
      elsif ($filterNum1 < 0)
      {
        $filterNum1 = 1;
      }

      $whereClause .= " ORDER BY $filterMeas1 ASC LIMIT $filterNum1";
    }
  }

  ### end chart filtering code ###


  #FC forces us to come up with min/max/sum data for sizing/gradient
  if ($graph_x eq "p")
  {
    $query = "SELECT SUM(ABS($measureCol)), MIN($measureCol), MAX($measureCol) \
        FROM $dsSchema.$rptCube \
        WHERE product IN ($itemStr) AND geography=$q_geography AND time=$q_time $whereClause";
  }
  elsif ($graph_x eq "g")
  {
    $query = "SELECT SUM(ABS($measureCol)), MIN($measureCol), MAX($measureCol) \
        FROM $dsSchema.$rptCube \
        WHERE product=$q_product AND geography IN ($itemStr) AND time=$q_time $whereClause";
  }
  elsif ($graph_x eq "t")
  {
    $query = "SELECT SUM(ABS($measureCol)), MIN($measureCol), MAX($measureCol) \
        FROM $dsSchema.$rptCube \
        WHERE product=$q_product AND geography=$q_geography AND time IN ($itemStr) $whereClause";
  }

  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($totalVal, $minVal, $maxVal) = $dbOutput->fetchrow_array;

  #get the displayed measure's name
  $measureName = KAPutil_get_item_ID_name($db, $dsSchema, "m", $measureID);

  $sprintfFormatStr = "%." . $sValdecimals . "f";
  $minVal = sprintf("$sprintfFormatStr", $minVal);
  $maxVal = sprintf("$sprintfFormatStr", $maxVal);

  print <<XML_LABEL;
    <colorrange gradient='1' minvalue="$minVal" code="$minColor">
        <color code="$maxColor" maxvalue="$maxVal" />
    </colorrange>
XML_LABEL

  print("<set label='$measureName' value='$totalVal'>\n");

  #set up our item/value query based on the display dimension
  if ($graph_x eq "p")
  {
    $query = "SELECT product, $measureCol \
        FROM $dsSchema.$rptCube \
        WHERE product IN ($itemStr) AND geography=$q_geography AND time=$q_time $whereClause";
  }
  elsif ($graph_x eq "g")
  {
    $query = "SELECT geography, $measureCol \
        FROM $dsSchema.$rptCube \
        WHERE product=$q_product AND geography IN ($itemStr) AND time=$q_time $whereClause";
  }
  elsif ($graph_x eq "t")
  {
    $query = "SELECT time, $measureCol \
        FROM $dsSchema.$rptCube \
        WHERE product=$q_product AND geography=$q_geography AND time IN ($itemStr) $whereClause";
  }

  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  #cycle through the results, outputting each as a slice in the tree map
  while (($itemID, $value) = $dbOutput->fetchrow_array)
  {
    $displayed .= "'$itemID',";

    $sprintfFormatStr = "%." . $sValdecimals . "f";
    $value = sprintf("$sprintfFormatStr", $value);

    $svalue = $value;
    $value = abs($value);
    print(" <set label='$setNames{$itemID}' value='$value' svalue='$svalue'/>\n");
  }
  chop($displayed);

  #finally, generate an "All Others" slice if requested by user
  if ($allOthers eq "true")
  {
    if ($graph_x eq "p")
    {
      $query = "SELECT SUM($measureCol) FROM $dsSchema.$rptCube \
          WHERE product NOT IN ($displayed) AND geography=$q_geography AND time=$q_time";
    }
    elsif ($graph_x eq "g")
    {
      $query = "SELECT SUM($measureCol) FROM $dsSchema.$rptCube \
          WHERE product=$q_product AND geography NOT IN ($displayed) AND time=$q_time";
    }
    elsif ($graph_x eq "t")
    {
      $query = "SELECT SUM($measureCol) FROM $dsSchema.$rptCube \
          WHERE product=$q_product AND geography=$q_geography AND time NOT IN ($displayed)";
    }
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($total) = $dbOutput->fetchrow_array;

    $total = sprintf("%.2f", $total);
    if (($total > 1) || ($total < -1))
    {
      $total = int($total);
    }

    $svalue = $total;
    $total = abs($total);

    print(" <set label='All Others' value='$total' svalue='$svalue' />\n");
  }

  print("</set>\n");
  print("</chart>\n");


#EOF
