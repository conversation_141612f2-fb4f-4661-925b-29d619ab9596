#!/usr/bin/perl

use Text::CSV;

#Import C&S Key Foods Spin data for ESM-Ferolie

  open(INPUT, "$ARGV[0]");
  open(OUTPUT, ">$ARGV[1]");


  #instantiate our CSV object
  $csv = Text::CSV->new( {binary => 1} );

  #grab the month/year combo from the first line
  $line = <INPUT>;

  $line =~ m/^(.*?),.*/;
  $timeperiod = "1 month ending $1";

  #parse the header line
  $line = <INPUT>;
  $csv->parse($line);
  @columns = $csv->fields();

  $idx = 0;
  foreach $header (@columns)
  {
    if ($header eq "customer")
    {
      $columns[$idx] = "pattr:customer";
    }
    elsif ($header eq "fac")
    {
      $columns[$idx] = "pattr:fac";
    }
    elsif ($header eq "itemno")
    {
      $columns[$idx] = "pattr:itemno";
      $itemNumCol = $idx;
    }
    elsif ($header eq "itemupc")
    {
      $columns[$idx] = "UPC";
      $upcCol = $idx;
    }
    elsif ($header eq "vendno")
    {
      $columns[$idx] = "pseg:vendno";
    }
    elsif ($header eq "vendor")
    {
      $columns[$idx] = "pseg:vendor";
    }
    elsif ($header eq "itemdesc")
    {
      $columns[$idx] = "Product";
      $prodCol = $idx;
    }
    elsif ($header eq "linkcode")
    {
      $columns[$idx] = "pattr:linkcode";
    }
    elsif ($header eq "total")
    {
      $columns[$idx] = "garbage";
    }
    elsif ($header =~ m/monthtotal/i)
    {
      $columns[$idx] = "total cases";
    }
    $idx++;
  }

  #push the new shipped measure and date columns on the front of the headers
  @tmp = ('Time Period', 'Geography');
  push(@tmp, @columns);
  @columns = @tmp;

  #output headers
  $csv->combine(@columns);
  $line = $csv->string();
  print OUTPUT "$line\n";

  while ($line = <INPUT>)
  {
    $csv->parse($line);
    @columns = $csv->fields();

    if (length($columns[$prodCol]) < 2)
    {
      next;
    }

    #strip leading asterisk if present
    if ($columns[$prodCol] =~ m/^\*(.*)/)
    {
      $columns[$prodCol] = $1;
    }

    #format the UPC (assumes 10 digit UPC)
    $columns[$upcCol] = sprintf("%010d", $columns[$upcCol]);
    $columns[$upcCol] =~ m/(\d\d\d\d\d)(\d\d\d\d\d)$/;
    $upc = "0-$1-$2-$2";
    $columns[$upcCol] = $upc;

    #add the ITEM# to the end of the product name
    $columns[$prodCol] = "$columns[$prodCol] $columns[$itemNumCol]";

    #push the dims on front of the line of data
    @tmp = ($timeperiod , "Associated");
    push(@tmp, @columns);
    @columns = @tmp;

    #output the line to the temp file
    $csv->combine(@columns);
    $line = $csv->string();
    print OUTPUT "$line\n";
  }

  close(OUTPUT);
  close(INPUT);
