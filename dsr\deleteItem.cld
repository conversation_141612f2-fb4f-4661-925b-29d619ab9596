#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  #output Content-type header
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  #output HTML
  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Delete Item</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">$readableCapAction $readableType</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('dsID');
  $dim = $q->param('dim');
  $itemID = $q->param('item');
  $type = $q->param('type');
  $merged = $q->param('merged');
  $readableType = $q->param('readableType');
  $readableAction = $q->param('readableAction');
  $readableCapAction = $q->param('readableCapAction');
  $delSegItems = $q->param('delsegitems');
  $baseItems = $q->param('baseitems');

  #sanitize parameters
  $dsID = utils_sanitize_integer($dsID);
  $dim = utils_sanitize_dim($dim);


  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  #make sure we have write privs for the data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to delete items from this data source.");
  }

  $dsSchema = "datasource_" . $dsID;
  $dsName = ds_id_to_name($db, $dsID);
  %itemNameHash = dsr_get_item_name_hash($db, $dsSchema, $dim);

  #get our dimension name for display purposes/db table name for SELECT
  if ($dim eq "p")
  {
    $factID = "productID";
  }
  elsif ($dim eq "g")
  {
    $factID = "geographyID";
  }
  elsif ($dim eq "t")
  {
    $factID = "timeID";
  }

  $structDB = KAPutil_get_dim_stub_name($dim);
  $dimDB = KAPutil_get_dim_db_name($dim);
  $dimName = KAPutil_get_dim_name_singular($dim, 1);

  #by default, we want to tell the user we "deleted" the item unless we
  #unmerged it
  if ($merged == 1)
  {
    $readableAction = "unmerged";
  }
  else
  {
    $readableAction = "deleted";
  }

  #handle special case of deleting segments, when we might also be asked to
  #delete the products contained in the segments
  if (($type ne "base") && ($itemID =~ m/^SMT_(\d+)/))
  {
    @segmentIDs = split(',', $itemID);
    $dbName = $structDB . "segment";
    $dbItemName = $structDB . "segment_item";
    foreach $segmentID (@segmentIDs)
    {
      $nameStr .= "$itemNameHash{$segmentID},";
      $segmentID =~ m/^SMT_(\d+)/;
      $segmentID = $1;

      $query = "DELETE FROM $dsSchema.$dbName WHERE ID=$segmentID";
      $status = $db->do($query);
      KAPutil_handle_db_err($db, $status, $query);

      $query = "DELETE FROM $dsSchema.$dbItemName WHERE segmentID=$segmentID";
      $status = $db->do($query);
      KAPutil_handle_db_err($db, $status, $query);

      $nameStr .= "$itemNameHash{$itemID},";
    }
    chop($nameStr);
    utils_audit($db, $userID, "Deleted segments $nameStr", $dsID, 0, 0);
    $activity = "$first $last deleted segments $nameStr in $dsName";
    utils_slack($activity);

    #now let the rest of the code handle deleting the base items, if requested
    if ($delSegItems eq "on")
    {
      $type = "base";
      $itemID = $baseItems;
    }
  }

  #delete item from data source (the background processor will pull it from
  #data cubes over time)
  if (($type eq "base") && ($merged == 1))
  {
    utils_audit($db, $userID, "Unmerged product $itemNameHash{$itemID}", $dsID, 0, 0);
    $activity = "$first $last unmerged product $itemNameHash{$itemID} in $dsName";
    DSRmergedprod_unwind($db, $dsSchema, $itemID);
  }

  elsif (($type eq "base") && ($dim eq "m"))
  {
    @baseItems = split(',', $itemID);
    $baseNameStr = "";

    foreach $id (@baseItems)
    {
      $query = "DELETE FROM $dsSchema.$dimDB WHERE ID=$id";
      $status = $db->do($query);
      KAPutil_handle_db_err($db, $status, $query);

      #if the base item is a measure, drop its column from the facts table
      $measureCol = "measure_$id";
      $query = "ALTER TABLE $dsSchema.facts DROP COLUMN $measureCol";
      $status = $db->do($query);
      KAPutil_handle_db_err($db, $status, $query);
    }

    chop($baseNameStr);  chop($baseNameStr);
    $activity = "$first $last deleted measure(s) $baseNameStr from $dsName";;
    utils_audit($db, $userID, "Deleted measure(s) $baseNameStr", $dsID, 0, 0);
  }

  elsif ($type eq "base")
  {

    #NB: we're effectively rebuilding the string to avoid comma issues
    @baseItems = split(',', $itemID);
    $baseItemIDStr = join(',', @baseItems);
    $baseNameStr = "";

    $query = "DELETE FROM $dsSchema.$dimDB WHERE ID IN ($baseItemIDStr)";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    #delete the item from any segmentations it might be in
    $dbName = $structDB . "segment_item";
    $query = "DELETE FROM $dsSchema.$dbName WHERE itemID IN ($baseItemIDStr)";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    #remove any attributes associated with the item
    $dbName = $structDB . "attribute_values";
    $query = "DELETE FROM $dsSchema.$dbName WHERE itemID IN ($baseItemIDStr)";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    $query = "DELETE FROM $dsSchema.facts WHERE $factID IN ($baseItemIDStr)";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    foreach $id (@baseItems)
    {
      $baseNameStr .= $itemNameHash{$id} . ", ";
    }

    chop($baseNameStr);  chop($baseNameStr);
    $activity = "$first $last deleted base item(s) $baseNameStr from $dsName";;
    utils_audit($db, $userID, "Deleted base item(s) $baseNameStr", $dsID, 0, 0);
  }
  elsif ($type eq "attr")
  {
    $tmpID = "ATT_" . $itemID;
    utils_audit($db, $userID, "Deleted attribute $itemNameHash{$tmpID}", $dsID, 0, 0);
    $activity = "$first $last deleted attribute $itemNameHash{$tmpID} from $dsName";

    $dbName = $structDB . "attributes";
    $query = "DELETE FROM $dsSchema.$dbName WHERE ID=$itemID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    $dbName = $structDB . "attribute_values";
    $query = "DELETE FROM $dsSchema.$dbName WHERE attributeID=$itemID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);
  }
  elsif ($type eq "list")
  {
    $tmpID = "LIS_" . $itemID;
    utils_audit($db, $userID, "Deleted list $itemNameHash{$tmpID}", $dsID, 0, 0);
    $activity = "$first $last deleted list $itemNameHash{$tmpID} from $dsName";

    $dbName = $structDB . "list";
    $query = "DELETE FROM $dsSchema.$dbName WHERE ID=$itemID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);
  }

  elsif ($type eq "aggr")
  {
    $tmpID = "AGG_" . $itemID;
    utils_audit($db, $userID, "Deleted aggregate $itemNameHash{$tmpID}", $dsID, 0, 0);
    $activity = "$first $last deleted aggregate $itemNameHash{$tmpID} from $dsName";

    $dbName = $structDB . "aggregate";
    $query = "DELETE FROM $dsSchema.$dbName WHERE ID=$itemID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);
  }

  elsif ($type eq "seg")
  {
    $tmpID = "SEG_" . $itemID;
    utils_audit($db, $userID, "Deleted segmentation $itemNameHash{$tmpID}", $dsID, 0, 0);
    $activity = "$first $last deleted segmentation $itemNameHash{$tmpID} from $dsName";

    $dbName = $structDB . "segmentation";
    $query = "DELETE FROM $dsSchema.$dbName WHERE ID=$itemID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    $dbName = $structDB . "segment";
    $query = "DELETE FROM $dsSchema.$dbName WHERE segmentationID=$itemID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    $dbName = $structDB . "segment_item";
    $query = "DELETE FROM $dsSchema.$dbName WHERE segmentationID=$itemID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    $dbName = $structDB . "seg_rules";
    $query = "DELETE FROM $dsSchema.$dbName WHERE segmentationID=$itemID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);
  }

  elsif ($type eq "segmenthier")
  {
    $tmpID = "SHS_" . $itemID;
    utils_audit($db, $userID, "Deleted segmentation hierarchy $itemNameHash{$tmpID}", $dsID, 0, 0);
    $activity = "$first $last deleted segmentation hierarchy $itemNameHash{$tmpID} from $dsName";

    $dbName = $structDB . "seghierarchy";
    $query = "DELETE FROM $dsSchema.$dbName WHERE ID=$itemID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);
  }

  #make the readable action past-tense
  $readableAction = $readableAction;

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-10 col-lg-7 col-xl-6"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">$readableCapAction $readableType</DIV>
        <DIV CLASS="card-body">

          The item has been $readableAction.

          <P>&nbsp;<P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" onClick="location.href='display.cld?ds=$dsID&dim=$dim'"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

  $query = "UPDATE dataSources SET lastModified=NOW() WHERE ID=$dsID";
  $db->do($query);

  utils_slack($activity);

#EOF
