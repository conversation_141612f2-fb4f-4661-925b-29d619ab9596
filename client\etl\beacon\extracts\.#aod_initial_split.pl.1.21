#!/usr/bin/perl


use Text::CSV_XS;


#
# Global variables controlling how large we're allowed to scale
#

$NUMCORES = 8;

#for the initial split of category files, using every core but 1 seems
#to be most efficient
$MAXFACTSPLITPROCS = $NUMCORES - 1;

#NB: On 0.25 IOPS endurance, 2 seems like the best balance of CPU/IO use
$MAXPROCS = 8;

#Max number of zip processes that can be run at a time to compress category
#files [rule of thumb is 0.75 * # of vCPUs
$MAXCOMPRESSIONPROCS = 8;


#---------------------------------------------------------------------------
#
# Output log info to console and log file
#


sub DBG
{
  my ($date);

  my ($str) = @_;

  $date = localtime();
  print DBGFILE "$date: $str\n";
  print("$date: $str\n");
}



#---------------------------------------------------------------------------
#
# Runs through the specified archive, splitting it by category
#

sub split_compressed_archive
{
  my ($filename) = @_;

  DBG("Start splitting of $filename");

  #get the name of the facts table from inside the archive
  $filename =~ m/(.*)\.tar\.gz/;
  $factsFilename = $1 . ".txt";


  #open the compressed data stream for reading
  $cmd = "tar -O -zxf /data/nielsen/$filename $factsFilename";
  $status = open(INPUT, "-|", $cmd);
  if ($status < 1)
  {
    DBG("ERROR Unable to open pipe to $cmd, $!");
    exit;
  }

  #read and parse the header line
  $upcColIdx = -1;
  $catColIdx = -1;
  $headerLine = <INPUT>;
  chomp($headerLine);

  @headers = split('\|', $headerLine);
  $idx = 0;
  foreach $header (@headers)
  {
    if ($header eq "UPC")
    {
      $upcColIdx = $idx;
    }
    elsif ($header eq "BC CATEGORY")
    {
      $catColIdx = $idx;
    }

    $idx++;
  }

  #build up our standard file header line
  shift(@headers);
  shift(@headers);
  shift(@headers);
  undef(@tmp);
  $tmp[0] = "UPC";
  $tmp[1] = "BC CATEGORY";
  push(@tmp, @headers);
  $csv->combine(@tmp);
  $headerLine = $csv->string();

  #output the header file (we concat it onto the top of the category data
  #files) if this is our first run
  if ($fileCount eq 1)
  {
    $status = open(OUTPUT, ">/data2/work/headers.csv");
    if ($status < 1)
    {
      DBG("ERROR Unable to open headers.csv for writing, $!");
      exit;
    }
    print OUTPUT "$headerLine";
    close(OUTPUT);
  }


  #cycle through the data file lines, putting each in the correct category
  #file
  #NB: This is a tight loop that's going to get executed billions of times,
  #    so there's a lot of silliness related to avoiding string copies and so
  #    on. You may be tempted to refactor for readability - DON'T!!!!
  $count = 0;
  while ($line = <INPUT>)
  {

    #check for status update every 1M lines of data
    if ($count % 1000000 == 0)
    {
      DBG("$count: $filename ($fileCount/$totalFiles)");
    }

    chomp($line);

    #parse the pipe-separated line
    @columns = split(/\|/, $line);

    #get our UPC and category
    $upc = $columns[$upcColIdx];
    $category = $columns[$catColIdx];

    #see if we have an open file descriptor, create one if not
    if (!defined($fileHandleHash{$category}))
    {

      #turn the category name into a suitable file name
      $catFilename = lc($category);
      $catFilename =~ s/ /_/g;
      $catFilename =~ s/\./_/g;
      $catFilename =~ s/\//_/g;
      $catFilename =~ s/\'/_/g;
      $catFilename =~ s/\,/_/g;
      $catFilename =~ s/\&/_/g;
      $catFilename =~ s/__/_/g;
      $catFilename = $catFilename . "-$fileCount.csv";

      DBG("Creating new category file $catFilename");

      #open/append to the category data file
      $status = open($fileHandleHash{$category}, ">>/data2/work/$catFilename");
      if ($status < 1)
      {
        DBG("ERROR Unable to open $catFilename, $!");
        exit;
      }
    }


    #knock the upc, cat, and duplicate date info off the front of the source
    shift(@columns);
    shift(@columns);
    shift(@columns);

    #build up our line of data to match normalized format
    undef(@tmp);
    $tmp[0] = $upc;
    $tmp[1] = $category;
    push(@tmp, @columns);

    #turn the line into correctly escaped CSV and write it out to cat file
    $csv->print($fileHandleHash{$category}, \@tmp);

    $count++;
  }

  #close all of the open file handles to flush out to disk
  foreach $cat (keys %fileHandleHash)
  {
    close($fileHandleHash{$cat});
  }


  DBG("Done processing $filename");
}



#---------------------------------------------------------------------------
#
# Open the characteristics file, and split it by category.
#

sub split_characteristics_file
{

  my ($csv);
  my (%fileHandleHash);

  DBG("Splitting product characteristics file");

  #instantiate our CSV object
  $csv = Text::CSV_XS->new( {binary => 1, eol => $/} );

  open(INPUT, "/data/nielsen/Beacon_Product_Ref_prdc_ref_1247154.txt") or die("FATAL: Beacon_Product_Ref_prdc_ref_1247154.txt, $!");

  #read and parse the header line
  $headerLine = <INPUT>;
  chomp($headerLine);
  @headers = split('\|', $headerLine);
  $idx = 0;
  foreach $header (@headers)
  {
    if ($header eq "BC CATEGORY")
    {
      $catColIdx = $idx;
    }

    $idx++;
  }

  #build up our header line
  $csv->combine(@headers);
  $headerLine = $csv->string();


  #cycle through the characteristics file lines, putting each in the correct
  #category characteristics file
  while ($line = <INPUT>)
  {

    chomp($line);

    #parse the pipe-separated line
    @columns = split(/\|/, $line);

    #get our category
    $category = $columns[$catColIdx];

    #see if we have an open file descriptor for the cat, create one if not
    if (!defined($fileHandleHash{$category}))
    {

      #turn the category name into a suitable file name
      $catFilename = lc($category);
      $catFilename =~ s/ /_/g;
      $catFilename =~ s/\./_/g;
      $catFilename =~ s/\//_/g;
      $catFilename =~ s/\'/_/g;
      $catFilename =~ s/\,/_/g;
      $catFilename =~ s/\&/_/g;
      $catFilename =~ s/__/_/g;
      $catFilename = $catFilename . "-prodlookup.txt";

      #determine if the file already exists (it already has a header line)
      $outputHeader = 1;
      if ( -e "/data2/work/$catFilename")
      {
        $outputHeader = 0;
      }

      #open/append to the category data file
      $status = open($fileHandleHash{$category}, ">>/data2/work/$catFilename");
      if ($status < 1)
      {
        DBG("ERROR Couldn't open $catFilename, $!");
        exit;
      }

      #output the header line if we need to
      if ($outputHeader)
      {
        print {$fileHandleHash{$category}} "$headerLine";
      }

    }

    #turn the line into correctly escaped CSV and write it out to cat file
    $csv->print($fileHandleHash{$category}, \@columns);

  }

  #close all of the open file handles to flush out to disk
  foreach $cat (keys %fileHandleHash)
  {
    close($fileHandleHash{$cat});
  }

}



#---------------------------------------------------------------------------
#
# Remove unused characteristics columns from category-level product info
# files
#

sub prune_characteristics_file
{

  my ($fileStub) = @_;

  DBG("Pruning characteristics for $fileStub");

  #open input file
  $inputFileName = "/data2/work/$fileStub-prodlookup.txt";
  $status = open(INPUT, $inputFileName);
  if ($status < 1)
  {
    DBG("ERROR Couldn't open $inputFileName: $!");
    exit;
  }

  #read and parse header line
  $line = <INPUT>;
  $csv->parse($line);
  @headers = $csv->fields();


  #run through the file, reading each line and detecting if a particular column
  #of characteristics data is actually being used
  undef(@usedCols);
  while ($line = <INPUT>)
  {
    $csv->parse($line);
    @columns = $csv->fields();

    #run through each column of data, and mark "used" if not NA or NS
    $idx = 0;
    foreach $colData (@columns)
    {
      if ((!($colData =~ /NOT APPLICABLE/)) && (!($colData =~ /NOT STATED/)))
      {
        $usedCols[$idx] = 1;
      }

      $idx++;
    }

  }
  close(INPUT);

  #open our output file
  $outputFileName = "/data2/work/$fileStub-prodlookup.csv";
  open(OUTPUT, ">$outputFileName");

  #build and output our new header with unused columns stripped out
  $idx = 0;
  undef(@newHeaders);
  foreach $col (@headers)
  {
    if ($usedCols[$idx] > 0)
    {
      push(@newHeaders, $headers[$idx]);
    }

    $idx++;
  }
  $csv->print(OUTPUT, \@newHeaders);

  #reopen input file
  open(INPUT, "$inputFileName");

  #burn headers line
  $line = <INPUT>;

  #now, cycle through every line of the input file and knock out unused columns
  while ($line = <INPUT>)
  {
    $csv->parse($line);
    @columns = $csv->fields();

    #run through each column of data, and only keep those that are "used"
    $idx = 0;
    undef(@newLine);
    foreach $colData (@columns)
    {
      if ($usedCols[$idx] > 0)
      {

        #blank out NA/NS to save space
=pod
        if (($colData =~ /NOT APPLICABLE/) || ($colData =~ /NOT STATED/))
        {
          $colData = "";
        }
=cut

        push(@newLine, $colData);
      }

      $idx++;
    }

    $csv->print(OUTPUT, \@newLine);

  }

  #flush the pruned characteristics file to disk
  close(OUTPUT);

  #delete the old source characteristics file
  unlink($inputFileName);

}



#---------------------------------------------------------------------------

  open(DBGFILE, ">/data2/work/run.log") or die("Unable to open log file, $!");

  DBG("Starting initial load run");

  #remove old files left over from a previous run
  opendir(DIRHANDLE, "/data2/work");
  while (defined($filename = readdir(DIRHANDLE)))
  {
    if ($filename =~ m/\.txt$|\.zip$|\.csv$/i)
    {
      DBG("Unlinking old /data2/work/$filename");
      unlink("/data2/work/$filename");
    }
  }


  #get a list of the Nielsen-uploaded data archives
  opendir(DIRHANDLE, "/data/nielsen");
  while (defined($filename = readdir(DIRHANDLE)))
  {
    if ($filename =~ m/^Beacon_History_.*_fct_.*\.tar\.gz$/i)
    {
      print("Found source file $filename\n");
      push(@zipFiles, $filename);
    }
  }



  #
  # Split compressed facts files on category definition
  #


  #instantiate our CSV object
  $csv = Text::CSV_XS->new( {binary => 1, eol => $/} );

  #run through each uploaded archive file, splitting data rows out to
  #separate category files
  $fileCount = 0;
  $totalFiles = scalar(@zipFiles);
  $processCount = 0;
  foreach $archiveFilename (@zipFiles)
  {

    #if we haven't hit our maximum process limit
    if ($processCount < $MAXFACTSPLITPROCS)
    {

      $fileCount++;

      #fire off the child process
      if ($pid = fork)
      {
        #parent process

        #increment count of active processes
        $processCount++;

        #give gzip and tar a bit of a head start before kicking off another proc
        sleep(10);
      }

      #else we're the child process
      else
      {
        split_compressed_archive($archiveFilename);
        exit;
      }

    }

    #wait here until an empty process slot opens up
    if ($processCount >= $MAXFACTSPLITPROCS)
    {
      wait();
      $processCount--;
    }

  }

  #wait here until the last splitter processes finish
  while ($processCount > 0)
  {
    wait();
    $processCount--;
  }


  #
  # Join the per-process CSV files back together
  #

  #NB: the category data files will have names like "nuts-1.csv, nuts-2.csv"
  #    and so on.  We're concat'ing all of them together, with the headers at
  #    the top
  chdir("/data2/work");
  opendir(DIRHANDLE, "/data2/work");
  undef(%tmp);
  while (defined($filename = readdir(DIRHANDLE)))
  {
    if ($filename =~ m/^(.*)\-\d+\.csv$/i)
    {
      $tmp{$1} = 1;
    }
  }

  foreach $filename (keys %tmp)
  {
    DBG("Merging category data for $filename");
    `/usr/bin/cat headers.csv > $filename.csv`;
    `/usr/bin/cat $filename-*.csv >> $filename.csv`;
    `rm $filename-*.csv`;
  }



  #
  # Split the product characteristics file by category
  #

  split_characteristics_file();



  #
  # Remove unused characteristics from each prodlookup file
  #

  #get a list of our per-category CSV files
  opendir(DIRHANDLE, "/data2/work");
  while (defined($filename = readdir(DIRHANDLE)))
  {
    if ($filename =~ m/^(.*)\.csv$/i)
    {
      push(@catFiles, $1);
    }
  }

  #run through each category's product info file, pruning unused
  #characteristics
  $fileCount = 0;
  $totalFiles = scalar(@catFiles);
  $processCount = 0;

  foreach $fileStub (@catFiles)
  {

    $fileCount++;

    #if we haven't hit our maximum process limit
    if ($processCount < $MAXCOMPRESSIONPROCS)
    {

      #fire off the child process
      if ($pid = fork)
      {
        #parent process

        #increment count of active processes
        $processCount++;

      }

      #else we're the child process
      else
      {

        prune_characteristics_file($fileStub);

        exit;
      }

    }

    #wait here until an empty process slot opens up
    if ($processCount >= $MAXCOMPRESSIONPROCS)
    {
      wait();
      $processCount--;
    }
  }

  #wait here until the last pruning processes finish
  while ($processCount > 0)
  {
    wait();
    $processCount--;
  }


  #
  # Compress the category files
  #


  #run through each uploaded archive file, zipping together financial and 
  #characteristic data for each category
  $fileCount = 0;
  $totalFiles = scalar(@catFiles);
  $processCount = 0;

  chdir("/data2/work");
  foreach $fileStub (@catFiles)
  {

    $fileCount++;

    #if we haven't hit our maximum process limit
    if ($processCount < $MAXCOMPRESSIONPROCS)
    {

      #fire off the child process
      if ($pid = fork)
      {
        #parent process

        #increment count of active processes
        $processCount++;

        sleep(1);
      }

      #else we're the child process
      else
      {
        DBG("Compressing $fileStub ($fileCount / $totalFiles)");
        `zip $fileStub.zip $fileStub.csv $fileStub-prodlookup.csv`;
        unlink("$fileStub.csv");
        unlink("$fileStub-prodlookup.csv");

        exit;
      }

    }

    #wait here until an empty process slot opens up
    if ($processCount >= $MAXCOMPRESSIONPROCS)
    {
      wait();
      $processCount--;
    }

  }

  #wait here until the last compression processes finish
  while ($processCount > 0)
  {
    wait();
    $processCount--;
  }


  #
  # Move finished zip files from working directory to accessible location
  #

  DBG("Moving finished zip files to beacon directory");

  `mv /data2/work/*.zip /data2/beacon`;

  DBG("Done");


#EOF
