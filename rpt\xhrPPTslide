#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSel;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::ExcelReports;
use Lib::Reports;
use Lib::WebUtils;


#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $rptID = $q->param('rptID');

  $db = KAPutil_connect_to_database();

  #make sure we have view privs for this report
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to export this report.");
  }

  $cubeName = cube_id_to_name($db, $rptID);
  $dsID = cube_get_ds_id($db, $rptID);
  $dsName = ds_id_to_name($db, $dsID);

  $dsSchema = "datasource_" . $dsID;

  $query = "INSERT INTO audit.stats_cubes (cubeID, year, month, exportPPT) VALUES ($rptID, YEAR(NOW()), MONTH(NOW()), 1) ON DUPLICATE KEY UPDATE exportPPT=exportPPT+1";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);

  utils_audit($db, $userID, "Exported as PowerPoint slide", $dsID, $rptID, 0);

  $activity = "$first $last exported as a PowerPoint slide $cubeName in $dsName";
  utils_slack($activity);

  #decide how long we should wait for PhantomJS to render the report based on
  #number of items in the data source
  %prodNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "p");
  $prodCount = scalar(keys %prodNameHash);
  $waitTime = 5000;
  if ($prodCount > 5000)
  {
    $waitTime = 10000;
  }


  #build up the temp filenames
  $tmpBase = $last . $userID;
  $tmpBase =~ s/\s+//g;
  $tmpBase = $tmpBase . "_" . $rptID;
  $filename = "$tmpBase.pptx";
  $scriptName = "$tmpBase.js";
  $imgName = "$tmpBase.png";

  #make a copy of the template PPT file to the tmp directory
  system("cp export_template.pptx /opt/apache/htdocs/tmp/$filename");

  #change our working directory to the platform web-accessible tmp
  chdir("/opt/apache/htdocs/tmp");

  #get the screenshot of the report
  system("/opt/google/chrome/google-chrome --headless --run-all-compositor-stages-before-draw --virtual-time-budget=5000 --screenshot=/opt/apache/htdocs/tmp/$imgName --window-size=1280,720 --user-data-dir=/opt/apache/htdocs/tmp/ \"http://$Lib::KoalaConfig::hostname/app/rpt/display.cld?rpt=$rptID&c=1&l=1&u=$userID\"");

  #unzip the PPTX file (OpenDocument XML format expected)
  system("unzip -qq /opt/apache/htdocs/tmp/$filename -d /opt/apache/htdocs/tmp/$tmpBase");

  #delete the template file
  system("rm $filename");

  #copy the screenshot of the report into the PPTX structure
  system("cp $imgName $tmpBase/ppt/media/image1.png");

  #re-zip the updated PPTX structure into the PPTX file
  chdir("$tmpBase");
  system("zip -qr ../$filename *");
  chdir("..");

  #delete the PhantomJS JS, screenshot image, and PPTX structure
  system("rm -rf $tmpBase");
  system("rm $imgName");


  print <<END_HTML;
<DIV CLASS="modal-dialog" STYLE="overflow-y: initial !important;">
  <DIV CLASS="modal-content">
    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Export PowerPoint Slide</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">
      <DIV CLASS="text-center">
        <BUTTON CLASS="btn btn-success" TYPE="button" onClick="location.href='/tmp/$filename'"><I CLASS="bi bi-download"></I> Download PowerPoint Slide</BUTTON>
      </DIV>
    </DIV>

    <DIV CLASS="modal-footer">
      <DIV CLASS="text-center">
        <BUTTON CLASS="btn btn-primary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-check-lg"></I> Done</BUTTON>
      </DIV>
    </DIV>

  </DIV>
</DIV>
END_HTML


#EOF
