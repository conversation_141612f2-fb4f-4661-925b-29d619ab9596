#!/usr/bin/perl

use lib "/opt/apache/app/";

use Text::CSV_XS;
use DateTime::Duration;
use DBI;

use Lib::KoalaConfig;


#
# Global variables controlling how large we're allowed to scale
#

#NB: On 0.25 IOPS endurance, 2 seems like the best balance of CPU/IO use
$MAXPROCS = 7;



#---------------------------------------------------------------------------
#
# Output log info to console and log file
#


sub DBG
{
  my ($date);

  my ($str) = @_;

  $date = localtime();
  print DBGFILE "$date: $str\n";
  print("$str\n");
}



#---------------------------------------------------------------------------
#
# Runs through the specified archive, splitting it by category
#

sub split_compressed_archive
{
  my ($filename) = @_;

  DBG("Start splitting of $filename");

  #get the name of the facts table from inside the archive
  $filename =~ m/(.*)\.tar\.gz/;
  $factsFilename = $1 . ".txt";


  #open the compressed data stream for reading
  $cmd = "tar -O -zxf /data/nielsen/$filename $factsFilename";
  open(INPUT, "-|", $cmd);

  #read and parse the header line
  $upcColIdx = -1;
  $catColIdx = -1;
  $headerLine = <INPUT>;
  chomp($headerLine);

  @headers = split('\|', $headerLine);
  $idx = 0;
  foreach $header (@headers)
  {
    if ($header eq "UPC")
    {
      $upcColIdx = $idx;
    }
    elsif ($header eq "BC CATEGORY")
    {
      $catColIdx = $idx;
    }

    $idx++;
  }

  #build up our standard file header line
  shift(@headers);
  shift(@headers);
  shift(@headers);
  undef(@tmp);
  $tmp[0] = "UPC";
  $tmp[1] = "BC CATEGORY";
  push(@tmp, @headers);
  $csv->combine(@tmp);
  $headerLine = $csv->string();

  #output the header file (we concat it onto the top of the category data
  #files) if this is our first run
  if ($fileCount eq 1)
  {
    open(OUTPUT, ">/data2/work/headers.csv") or die ("Couldn't open /data2/work/headers.csv, $!");
    print OUTPUT "$headerLine";
    close(OUTPUT);
  }

  #cycle through the data file lines, putting each in the correct category
  #file
  #NB: This is a tight loop that's going to get executed billions of times,
  #    so there's a lot of silliness related to avoiding string copies and so
  #    on. You may be tempted to refactor for readability - DON'T!!!!
  $count = 0;
  while ($line = <INPUT>)
  {

    #check for status update every 1M lines of data
    if ($count % 1000000 == 0)
    {
#      $pct = ($count / $recordCounts{$filename}) * 100;
#      $pct = sprintf("%.1f", $pct);
#      DBG("$count ($pct%): $filename ($fileCount/$totalFiles)");
      DBG("$count: $filename ($fileCount/$totalFiles)");
    }

    chomp($line);

    #parse the pipe-separated line
    @columns = split('\|', $line);

    #get our UPC and category
    $upc = $columns[$upcColIdx];
    $category = $columns[$catColIdx];

    #see if we have an open file descriptor, create one if not
    if (!defined($fileHandleHash{$category}))
    {

      #turn the category name into a suitable file name
      $catFilename = lc($category);
      $catFilename =~ s/ /_/g;
      $catFilename =~ s/\./_/g;
      $catFilename =~ s/\//_/g;
      $catFilename =~ s/\'/_/g;
      $catFilename =~ s/\,/_/g;
      $catFilename =~ s/\&/_/g;
      $catFilename =~ s/__/_/g;
      $catFilename = $catFilename . "-$fileCount.csv";

      DBG("Creating new category file $catFilename");

      #open/append to the category data file
      open($fileHandleHash{$category}, ">>/data2/work/$catFilename");
    }


    #knock the upc, cat, and duplicate date info off the front of the source
    shift(@columns);
    shift(@columns);
    shift(@columns);

    #build up our line of data to match normalized format
    undef(@tmp);
    $tmp[0] = $upc;
    $tmp[1] = $category;
    push(@tmp, @columns);

    #turn the line into correctly escaped CSV and write it out to cat file
    $csv->print($fileHandleHash{$category}, \@tmp);

    $count++;
  }

  #close all of the open file handles to flush out to disk
  foreach $cat (keys %fileHandleHash)
  {
    close($fileHandleHash{$cat});
  }


  DBG("Done processing $filename");
}



#---------------------------------------------------------------------------
#
# Open the characteristics file, and split it by category.
#

sub split_characteristics_file
{

  my ($csv);
  my (%fileHandleHash);

  DBG("Splitting product characteristics file");

  #instantiate our CSV object
  $csv = Text::CSV_XS->new( {binary => 1, eol => $/} );

  open(INPUT, "/data/nielsen/Beacon_Product_Ref_prdc_ref_1326157.txt") or die("Unable to to open characteristics file, $!\n");

  #read and parse the header line
  $headerLine = <INPUT>;
  chomp($headerLine);
  @headers = split('\|', $headerLine);
  $idx = 0;
  foreach $header (@headers)
  {
    if ($header eq "BC CATEGORY")
    {
      $catColIdx = $idx;
    }

    $idx++;
  }

  #build up our header line
  $csv->combine(@headers);
  $headerLine = $csv->string();


  #cycle through the characteristics file lines, putting each in the correct
  #category characteristics file
  while ($line = <INPUT>)
  {

    chomp($line);

    #parse the pipe-separated line
    @columns = split(/\|/, $line);

    #get our category
    $category = $columns[$catColIdx];

    #see if we have an open file descriptor for the cat, create one if not
    if (!defined($fileHandleHash{$category}))
    {

      #turn the category name into a suitable file name
      $catFilename = lc($category);
      $catFilename =~ s/ /_/g;
      $catFilename =~ s/\./_/g;
      $catFilename =~ s/\//_/g;
      $catFilename =~ s/\'/_/g;
      $catFilename =~ s/\,/_/g;
      $catFilename =~ s/\&/_/g;
      $catFilename =~ s/__/_/g;
      $catFilename = $catFilename . "-prodlookup.txt";

      #determine if the file already exists (it already has a header line)
      $outputHeader = 1;
      if ( -e "/data2/work/$catFilename")
      {
        $outputHeader = 0;
      }

      #open/append to the category data file
      open($fileHandleHash{$category}, ">>/data2/work/$catFilename");

      #output the header line if we need to
      if ($outputHeader)
      {
        print {$fileHandleHash{$category}} "$headerLine";
      }

    }

    #turn the line into correctly escaped CSV and write it out to cat file
    $csv->print($fileHandleHash{$category}, \@columns);

  }

  #close all of the open file handles to flush out to disk
  foreach $cat (keys %fileHandleHash)
  {
    close($fileHandleHash{$cat});
  }

}



#---------------------------------------------------------------------------
#
# Remove unused characteristics columns from category-level product info
# files
#

sub prune_characteristics_file
{

  my ($fileStub) = @_;

  DBG("Pruning characteristics for $fileStub");

  #open input file
  $inputFileName = "/data2/work/$fileStub-prodlookup.txt";
  open(INPUT, $inputFileName) or die ("Couldn't open $inputFileName: $!\n");

  #read and parse header line
  $line = <INPUT>;
  $csv->parse($line);
  @headers = $csv->fields();


  #run through the file, reading each line and detecting if a particular column
  #of characteristics data is actually being used
  undef(@usedCols);
  while ($line = <INPUT>)
  {
    $csv->parse($line);
    @columns = $csv->fields();

    #run through each column of data, and mark "used" if not NA or NS
    $idx = 0;
    foreach $colData (@columns)
    {
      if ((!($colData =~ /NOT APPLICABLE/)) && (!($colData =~ /NOT STATED/)))
      {
        $usedCols[$idx] = 1;
      }

      $idx++;
    }

  }
  close(INPUT);

  #open our output file
  $outputFileName = "/data2/work/$fileStub-prodlookup.csv";
  open(OUTPUT, ">$outputFileName");

  #build and output our new header with unused columns stripped out
  $idx = 0;
  undef(@newHeaders);
  foreach $col (@headers)
  {
    if ($usedCols[$idx] > 0)
    {
      push(@newHeaders, $headers[$idx]);
    }

    $idx++;
  }
  $csv->print(OUTPUT, \@newHeaders);

  #reopen input file
  open(INPUT, "$inputFileName");

  #burn headers line
  $line = <INPUT>;

  #now, cycle through every line of the input file and knock out unused columns
  while ($line = <INPUT>)
  {
    $csv->parse($line);
    @columns = $csv->fields();

    #run through each column of data, and only keep those that are "used"
    $idx = 0;
    undef(@newLine);
    foreach $colData (@columns)
    {
      if ($usedCols[$idx] > 0)
      {

        #blank out NA/NS to save space
=pod
        if (($colData =~ /NOT APPLICABLE/) || ($colData =~ /NOT STATED/))
        {
          $colData = "";
        }
=cut

        push(@newLine, $colData);
      }

      $idx++;
    }

    $csv->print(OUTPUT, \@newLine);

  }

  #flush the pruned characteristics file to disk
  close(OUTPUT);

  #delete the old source characteristics file
  unlink($inputFileName);

}



#---------------------------------------------------------------------------
#
#

sub rewrite_history_file
{

  my ($fileStub) = @_;

  $zipFilename = $fileStub . ".zip";
  $factsFilename = $fileStub . ".csv";
  $lookupFilename = $fileStub . "-prodlookup.csv";
  $tempFilename = $fileStub . "-work.csv";

  DBG("Rewriting history for $fileStub ($fileCount/$totalFiles)");


  #if we don't already have a category file, it's a new category introduced
  #by Nielsen this month - let's just save our existing file, and call it good
  if (!(-f $zipFilename))
  {
    $updateFile = $fileStub . "-update.zip";
    DBG("Looks like $fileStub is a new category - copying $updateFile to $zipFilename");
    `/usr/bin/cp /data2/beacon/$updateFile /data2/beacon/$zipFilename`;

    return;
  }


  #uncompress the existing facts data
  DBG("Uncompressing $zipFilename");
  `/usr/bin/unzip -o $zipFilename $factsFilename`;

  #rewrite the file, removing any lines that match the list of date strings
  #we want to pull from the history because they're over 108 weeks old or
  #being replaced
  DBG("Removing old/overwritten data from $factsFilename");
  open(INPUT, "/data2/beacon/$factsFilename") or die("Unable to open $factsFilename, $!");
  open(OUTPUT, ">/data2/beacon/$tempFilename") or die("Unable to open $tempFilename, $!");

  while ($line = <INPUT>)
  {
    $csv->parse($line);
    @columns = $csv->fields();

    $dateStr = $columns[2];
    if ($deleteHash{$dateStr} < 1)
    {
      print OUTPUT $line;
    }
  }

  close(INPUT);
  close(OUTPUT);
  unlink("/data2/beacon/$factsFilename");
  rename("/data2/beacon/$tempFilename", "/data2/beacon/$factsFilename");


  #concatenate the new category data to the end of the history file
  DBG("Adding updated data to history file $factsFilename");
  open(INPUT, "/data2/work/$factsFilename") or die("Unable to open /data2/work/$factsFilename, $!");
  open(OUTPUT, ">>/data2/beacon/$factsFilename") or die("Unable to open /data2/beacon/$factsFilename, $!");

  #burn history line from update file (we already have it)
  $line = <INPUT>;

  while ($line = <INPUT>)
  {
    print OUTPUT $line;
  }

  close(INPUT);
  close(OUTPUT);

  #copy the new characteristics lookup file to the beacon directory
  DBG("Copying new product info lookup file $lookupFilename");
  `/usr/bin/cp /data2/work/$lookupFilename /data2/beacon/$lookupFilename`;

  #compress the updated history file and new product characteristics file
  DBG("Compressing updated history file for $fileStub");
  `/usr/bin/zip $zipFilename-1 $factsFilename $lookupFilename`;

  #delete our temporary working files
  unlink("/data2/beacon/$factsFilename");
  unlink("/data2/beacon/$lookupFilename");

  #replace the old compressed history archive
  rename("/data2/beacon/$zipFilename-1", "/data2/beacon/$zipFilename");
}



#---------------------------------------------------------------------------


  open(DBGFILE, ">/data/nielsen/update-run.log") or die("Unable to open log file, $!");

  #
  # XXX XXX Detect update upload XXX XXX
  #





  DBG("Starting update load run");

  #have Slack notify Koala employees that the load is starting
  $payload = "{\"channel\": \"#devops\", \"username\": \"Beacon United Nielsen IDW\", \"text\": \"Starting split of monthly data\"}";
  `/usr/bin/curl -s -X POST --data-urlencode 'payload=$payload' *****************************************************************************`;


  #
  # Clean up from any previous runs
  #


  #remove old files left over from a previous run
  opendir(DIRHANDLE, "/data2/work");
  while (defined($filename = readdir(DIRHANDLE)))
  {
    if ($filename =~ m/\.txt$|\.zip$|\.csv$/i)
    {
      DBG("Unlinking old /data2/work/$filename");
      unlink("/data2/work/$filename");
    }

  }


  #get a list of the Nielsen-uploaded data update files
  opendir(DIRHANDLE, "/data/nielsen");
  while (defined($filename = readdir(DIRHANDLE)))
  {
    if ($filename =~ m/^Beacon_Monthly.*\.tar\.gz$/i)
    {
      print("Found source file $filename\n");
      push(@zipFiles, $filename);
    }
  }



  #
  # Split compressed facts files on category definition
  #


  #instantiate our CSV object
  $csv = Text::CSV_XS->new( {binary => 1, eol => $/} );

  #run through each uploaded data file, splitting data rows out to
  #separate category files
  $fileCount = 0;
  $totalFiles = scalar(@zipFiles);
  $processCount = 0;
  foreach $archiveFilename (@zipFiles)
  {

    #if we haven't hit our maximum process limit
    if ($processCount < $MAXPROCS)
    {

      $fileCount++;

      #fire off the child process
      if ($pid = fork)
      {
        #parent process

        #increment count of active processes
        $processCount++;

        #give gzip and tar a bit of a head start before kicking off another proc
        sleep(10);
      }

      #else we're the child process
      else
      {
        split_compressed_archive($archiveFilename);
        exit;
      }

    }

    #wait here until an empty process slot opens up
    if ($processCount >= $MAXPROCS)
    {
      wait();
      $processCount--;
    }

  }

  #wait here until the last splitter processes finish
  while ($processCount > 0)
  {
    wait();
    $processCount--;
  }



  #
  # Join the per-process CSV files back together
  #

  #NB: the category data files will have names like "nuts-1.csv, nuts-2.csv"
  #    and so on.  We're concat'ing all of them together, with the headers at
  #    the top
  chdir("/data2/work");
  opendir(DIRHANDLE, "/data2/work");
  undef(%tmp);
  while (defined($filename = readdir(DIRHANDLE)))
  {
    if ($filename =~ m/^(.*)\-\d+\.csv$/i)
    {
      $tmp{$1} = 1;
    }
  }

  foreach $filename (keys %tmp)
  {
    DBG("Merging category data for $filename");
    `/usr/bin/cat headers.csv > $filename.csv`;
    `/usr/bin/cat $filename-*.csv >> $filename.csv`;
    `rm $filename-*.csv`;
  }


  #
  # Split the product characteristics file by category
  #

  split_characteristics_file();



  #
  # Remove unused characteristics from each prodlookup file
  #

  #get a list of our per-category CSV file root names (we'll use this to
  #construct filenames from this point forward)
  opendir(DIRHANDLE, "/data2/work");
  while (defined($filename = readdir(DIRHANDLE)))
  {
    if (($filename =~ m/^(.*)\.csv$/i) && (!($filename =~ m/prodlookup\.csv$/i)))
    {
      push(@catFiles, $1);
    }
  }

  #run through each category's product info file, pruning unused
  #characteristics
  $fileCount = 0;
  $totalFiles = scalar(@catFiles);
  $processCount = 0;

  foreach $fileStub (@catFiles)
  {

    $fileCount++;

    #if we haven't hit our maximum process limit
    if ($processCount < $MAXPROCS)
    {

      #fire off the child process
      if ($pid = fork)
      {
        #parent process

        #increment count of active processes
        $processCount++;

      }

      #else we're the child process
      else
      {

        prune_characteristics_file($fileStub);

        exit;
      }

    }

    #wait here until an empty process slot opens up
    if ($processCount >= $MAXPROCS)
    {
      wait();
      $processCount--;
    }
  }

  #wait here until the last pruning processes finish
  while ($processCount > 0)
  {
    wait();
    $processCount--;
  }



  #
  # Compress the category update files
  #


  #run through each uploaded archive file, splitting data rows out to
  #separate category files
  $fileCount = 0;
  $totalFiles = scalar(@catFiles);
  $processCount = 0;

  chdir("/data/work");
  foreach $fileStub (@catFiles)
  {

    $fileCount++;

    #if we haven't hit our maximum process limit
    if ($processCount < $MAXPROCS)
    {

      #fire off the child process
      if ($pid = fork)
      {
        #parent process

        #increment count of active processes
        $processCount++;

        sleep(1);
      }

      #else we're the child process
      else
      {
        DBG("Compressing $fileStub");
        `zip $fileStub-update.zip $fileStub.csv $fileStub-prodlookup.csv`;

        exit;
      }

    }

    #wait here until an empty process slot opens up
    if ($processCount >= $MAXPROCS)
    {
      wait();
      $processCount--;
    }

  }

  #wait here until the last compression processes finish
  while ($processCount > 0)
  {
    wait();
    $processCount--;
  }

  DBG("Moving finished zip files to beacon directory");
  `mv /data2/work/*.zip /data2/beacon`;


  DBG("Setting all daily Data Prep flows to re-run");
  $prepDB = DBI->connect($Lib::KoalaConfig::prepDBServer, 'app', $Lib::KoalaConfig::password);
  $query = "UPDATE prep.schedule SET lastRun = DATE_SUB(NOW(), INTERVAL 2 DAY) WHERE sched='daily'";
  $prepDB->do($query);


  #have Slack notify Koala employees that the monthly split is done
  $payload = "{\"channel\": \"#devops\", \"username\": \"Beacon United Nielsen IDW\", \"text\": \"Finished split of monthly data\"}";
  `/usr/bin/curl -s -X POST --data-urlencode 'payload=$payload' *****************************************************************************`;

  $payload = "{\"channel\": \"#devops\", \"username\": \"Beacon United Nielsen IDW\", \"text\": \"Starting update of 108 week data sets\"}";
  `/usr/bin/curl -s -X POST --data-urlencode 'payload=$payload' *****************************************************************************`;



  #get a list of our per-category CSV file root names (we'll use this to
  #construct filenames from this point forward)
  undef(@catFiles);
  opendir(DIRHANDLE, "/data2/work");
  while (defined($filename = readdir(DIRHANDLE)))
  {
    if (($filename =~ m/^(.*)\.csv$/i) && (!($filename =~ m/prodlookup\.csv$/i)))
    {
      push(@catFiles, $1);
    }
  }
  $csv = Text::CSV_XS->new( {binary => 1, eol => $/} );



  #
  # Determine which 8 time periods we need to pull from history zip files
  #

  DBG("Determining which time periods are contained in the update...");

  #see which time periods are contained in 5 more-or-less random category
  #update files
  $idx = 0;
  undef(%dateHash);
  while ($idx < 1)
  {
    $fileStub = $catFiles[$idx];
    $filename = "$fileStub.csv";

    DBG("Scanning $filename for time periods");

    open(INPUT, "/data2/work/$filename") or die("Unable to open $filename, $!");

    #burn the header line
    $line = <INPUT>;

    #run through the file, keeping track of which dates are present
    while ($line = <INPUT>)
    {
      $csv->parse($line);
      @columns = $csv->fields();

      $dateStr = $columns[2];
      $dateHash{$dateStr} = 1;
    }

    close(INPUT);

    $idx++;
  }

  #build up sortable hash of dates
  foreach $dateStr (keys %dateHash)
  {
    delete($dateHash{$dateStr});

    $dateStr =~ m/^.*? .*? (\d+)\/(\d+)\/(\d+)$/;
    $str = "$3-$1-$2";
    $dateHash{$str} = $dateStr;
  }


  if (keys(%dateHash) != 8)
  {
    DBG("ERROR! The expected 8 time periods are not in this update");
    exit;
  }

  #run through the sorted dates - first month of data should be overwritten in
  #data history files, second month should be added while dates 108 weeks back
  #from second month should be deleted
  #NB: This effectively means we want to delete 8 weeks of data from the
  #    existing history file (the 4 on the front and the 4 on the back)
  undef(%deleteHash);
  $count = 1;
  foreach $dateKey (sort {$dateHash{$a} cmp $dateHash{$b}} keys %dateHash)
  {

    $dateStr = $dateHash{$dateKey};
    $dateKey =~ m/(\d+)\-(\d+)\-(\d+)/;
    $year = $1;
    $y2kYear = "20" . $year;
    $month = $2;
    $day = $3;

    #we're going to overwrite the first (oldest) 4 dates
    if ($count <= 4)
    {
      DBG("Going to overwrite date $dateStr");
      $deleteHash{$dateStr} = 1;
    }

    #else we're going to delete the 108-weeks ago date
    else
    {
      DBG("Going to delete 108 weeks ago from $dateStr");

      $dt = DateTime->new(year => $y2kYear, month => $month, day => $day);
      $delDate = $dt->subtract( weeks => 108 );
      if ($delDate =~ m/^20(\d+)\-(\d+)\-(\d+)T/)
      {
        $delDate = "1 W/E $2/$3/$1";
        DBG("Going to delete $delDate");
        $deleteHash{$delDate} = 1;
      }
    }

    $count++;
  }


  #
  # Run through each of the historical category files, deleting old time
  # periods, concatenating new data, and recompressing. We're going to do
  # this mostly in-place to avoid messing with any potentially running
  # updates in Data Prep.
  #

  #cycle through each category we have an update for
  $fileCount = 0;
  $totalFiles = scalar(@catFiles);
  $processCount = 0;

  chdir("/data2/beacon");
  foreach $fileStub (@catFiles)
  {

    $fileCount++;

    #if we haven't hit our maximum process limit
    if ($processCount < $MAXPROCS)
    {

      #fire off the child process
      if ($pid = fork)
      {
        #parent process

        #increment count of active processes
        $processCount++;

      }

      #else we're the child process
      else
      {

        rewrite_history_file($fileStub);

        exit;
      }

    }

    #wait here until an empty process slot opens up
    if ($processCount >= $MAXPROCS)
    {
      wait();
      $processCount--;
    }

  }

  #wait here until the last rewrite processes finish
  while ($processCount > 0)
  {
    wait();
    $processCount--;
  }


  #
  # Mark the source files we used for this update "done" by prepending their
  # names with an underscore
  #

  DBG("Marking our source files for this run as done");
  foreach $archiveFilename (@zipFiles)
  {
    rename("/data/nielsen/$archiveFilename", "/data/nielsen/__$archiveFilename");
  }


  #
  # Remove the working files from /data2/work/
  #

  DBG("Removing temporary working files");
  opendir(DIRHANDLE, "/data2/work");
  while (defined($filename = readdir(DIRHANDLE)))
  {
    if ($filename =~ m/\.txt$|\.zip$|\.csv$/i)
    {
      DBG("Unlinking old /data2/work/$filename");
      unlink("/data2/work/$filename");
    }

  }


  DBG("Done");

  $payload = "{\"channel\": \"#devops\", \"username\": \"Beacon United Nielsen IDW\", \"text\": \"Done updating 108 week data sets\"}";
  `/usr/bin/curl -s -X POST --data-urlencode 'payload=$payload' *****************************************************************************`;



#EOF
