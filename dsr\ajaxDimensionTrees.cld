#!/usr/bin/perl

#NB: for performance reasons, we do a *ton* of bare SQL queries in this code
#   that you may find yourself tempted to refactor into separate API calls.
#
#   DO NOT DO THAT. DO NOT DO THAT. DO NOT DO THAT.
#
#   The performance of this script and its queries are highly user visible, and
#   even minor changes can lead to unacceptably long UI delays and even
#   timeouts for data sources with lots of items. The current query sets have
#   been carefully developed and tested over years.
#

use lib "/opt/apache/app/";

use CGI::Session;
use CGI qw(:standard);
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRMeasures;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;


#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get the data source and dimension to supply tree data for
  $dsID = $q->param('ds');
  $dim = $q->param('d');
  $caller = $q->param('s');
  $mode = $q->param('mode');
  $parent = $q->param('parent');
  $start = $q->param('start');

  #sanitize parameters
  $dsID = utils_sanitize_integer($dsID);
  if (!defined($dsID))
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
  $dim = utils_sanitize_dim($dim);
  if (!defined($dim))
  {
    print("Status: 403 Not authorized\n");
    exit;
  }

  $dsSchema = "datasource_" . $dsID;

  #connect to the database
  $db = KAPutil_connect_to_database();

  print("Expires: -1\n");
  print("Cache-Control: private, max-age=0");
  print("Content-type: application/json\n\n");

  #make sure we have read privs for this data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs eq "N")
  {
    print("Status: 403 Not authorized\n");
    exit;
  }

  #figure out which dimension we're supposed to display and set DB table names
  $dimName = KAPutil_get_dim_name($dim, 1);
  $dimension = KAPutil_get_dim_db_name($dim);
  $dbStub = KAPutil_get_dim_stub_name($dim);


  #----------------------------------------------------------------------------

  #if we're being called by a "lazy loading" tree to display the contents of a
  #specific segment
  if (($mode eq "children") && ($parent =~ m/^SMT_(\d+)/))
  {
    $segmentID = $1;

    #get IDs of items in the segment
    $itemIDStr = "";
    $dbName = $dbStub . "segment_item";
    $query = "SELECT itemID FROM $dsSchema.$dbName WHERE segmentID=$segmentID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while ($itemID = $dbOutput->fetchrow_array)
    {
      $itemIDStr .= "$itemID,";
    }
    chop($itemIDStr);

    #get alpha-ordered list of item names in the segment
    $query = "SELECT ID, IFNULL(alias,name) as dispName FROM $dsSchema.$dimension \
        WHERE ID IN ($itemIDStr) ORDER BY dispName";
    $dbOutput = $db->prepare($query);
    $totalItems = $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);

    $count = 0;
    print("[\n");
    while (($itemID, $name) = $dbOutput->fetchrow_array)
    {
      $name =~ s/\\//g;
      $name =~ s/\"/\\"/g;

      print("{\"key\":\"SMT_$segmentID.$itemID\", \"title\":\"$name\", \"type\":\"segitem\"}");

      $count++;

      if ($count < $totalItems)
      {
        print(",\n");
      }
      else
      {
        print("\n");
      }
    }

    print("]\n");

    exit;
  }



  #----------------------------------------------------------------------


  #if we're being called by a "lazy loading" tree to display the contents of a
  #segmentation hierarchy level
  if (($mode eq "children") && ($parent =~ m/^SHS_\d+_(.*)/))
  {

    $segmentStr = $1;
    @segmentIDs = split('_', $segmentStr);

    #get a list of all items that are members of the top level segment
    $dbName = $dbStub . "segment_item";
    $segmentID = $segmentIDs[0];
    $query = "SELECT itemID FROM $dsSchema.$dbName WHERE segmentID=$segmentID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($itemID) = $dbOutput->fetchrow_array)
    {
      $itemIDStr .= "$itemID,";
    }
    chop($itemIDStr);
    shift(@segmentIDs);

    #cycle through any remaining hierarchy levels, cutting down items as we go
    foreach $segmentID (@segmentIDs)
    {
      $query = "SELECT itemID FROM $dsSchema.$dbName \
          WHERE segmentID=$segmentID AND itemID IN ($itemIDStr)";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      KAPutil_handle_db_err($db, $status, $query);

      $itemIDStr = "";
      while (($itemID) = $dbOutput->fetchrow_array)
      {
        $itemIDStr .= "$itemID,";
      }
      chop($itemIDStr);
    }

    #get alpha-ordered list of item names in the hierarchy level
    $query = "SELECT ID, IFNULL(alias,name) AS dispName FROM $dsSchema.$dimension \
        WHERE ID IN ($itemIDStr) ORDER BY dispName";
    $dbOutput = $db->prepare($query);
    $totalItems = $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);

    $count = 0;
    print("[\n");
    while (($itemID, $name) = $dbOutput->fetchrow_array)
    {
      $name =~ s/\\//g;
      $name =~ s/\"/\\"/g;

      print("{\"key\":\"SMT_$segmentID.$itemID\", \"title\":\"$name\", \"type\":\"segitem\"}");

      $count++;

      if ($count < $totalItems)
      {
        print(",\n");
      }
      else
      {
        print("\n");
      }
    }

    print("]\n");

    exit;
  }



  #----------------------------------------------------------------------

  #if we're being called by a very large product dimension tree to display
  #additional products, 10K at a time
  if ($mode eq "paging")
  {

    if ($start < 10_000)
    {
      exit;
    }

    $query = "SELECT ID, name, IFNULL(alias,name) AS dispName, merged \
        FROM $dsSchema.$dimension WHERE merged < 2 \
        ORDER BY dispName LIMIT $start, 10000";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    $totalProdCount = $status;

    $prodCount = 1;
    $first = 1;
    print("[\n");
    while (($id, $baseName, $name, $merged) = $dbOutput->fetchrow_array)
    {
      #escape "special" characters
      $name =~ s/\\//g;
      $name =~ s/\"/\\"/g;
      $baseName =~ s/\\//g;
      $baseName =~ s/\"/\\"/g;

      #include the base name (for tooltip) if the name is aliased
      if ($baseName ne $name)
      {
        $baseName = "\"tooltip\":\"$baseName\",";
      }
      elsif (length($name) > 90)
      {
        $name = substr($baseName, 0, 50);
        $name .= "...";
        $name .= substr($baseName, -37);
        $baseName = "";
      }
      else
      {
        $baseName = "";
      }

      if ($first == 1)
      {
        $first = 0;
      }
      else
      {
        print(",\n");
      }

      #NB: merged products are 0 (not merged), 1 (parent), or 2 (child)
      if ($merged == 0)
      {
        print("{\"key\": \"$id\", \"title\": \"$name\", $baseName \"type\": \"base\", \"merged\": \"0\"}");
      }

      elsif ($merged == 1)
      {
        print("{\"key\":\"$id\", \"title\":\"$name\", $baseName \"type\":\"base\", \"merged\":\"1\", \"folder\":true, \"children\":[\n");

        $mergedItemCount = 1;
        @memberIDs = DSRmergedprod_get_members($db, $dsSchema, $id);
        foreach $memberID (@memberIDs)
        {
          print("{\"key\":\"$memberID\", \"title\":\"$itemNameHash{$memberID}\", $baseName \"type\":\"base\", \"merged\":\"2\"}");
          if ($mergedItemCount < scalar(@memberIDs))
          {
            print(",\n");
          }
          $mergedItemCount++;
        }

        print("]}");
      }

      $prodCount++;
    }

    #if the data source has more remaining products, output a "More..." paging node
    if ($totalProdCount >= 10_000)
    {
      $start += 10_000;
      print(",\n{\"title\": \"More...\", \"color\": \"blue\", \"statusNodeType\": \"paging\", \"icon\": \"false\", \"url\": \"/app/dsr/ajaxDimensionTrees.cld?ds=$dsID&d=p&s=dsr&mode=paging&start=$start\"}\n");
    }

    print("]\n");

    exit;
  }



  #----------------------------------------------------------------------

  #get item names for dimension we're going to display
  if ($dim eq "p")
  {
    %itemNameHash = dsr_get_item_name_hash($db, $dsSchema, "p");
  }
  elsif ($dim eq "g")
  {
    %itemNameHash = dsr_get_item_name_hash($db, $dsSchema, "g");
  }
  elsif ($dim eq "t")
  {
    %itemNameHash = dsr_get_item_name_hash($db, $dsSchema, "t");
  }
  elsif ($dim eq "m")
  {
    %itemNameHash = dsr_get_item_name_hash($db, $dsSchema, "m");
    %measureInfoHash = DSRmeasures_get_calcmeasure_hash($db, $dsSchema);
  }

  #get the list of all items for the specified dimension
  print("[{\"key\": \"all\", \"title\": \"All\", \"type\": \"header\", \"folder\": true, \"children\": [\n");

  #handle base time periods - arrange in sub-tree by period type and duration
  if ($dim eq "t")
  {
    #this hash is used to keep track of which duration-type branches have been
    #created
    undef(%timeBranchHash);

    $query = "SELECT ID, name, IFNULL(alias, name), duration, type \
        FROM $dsSchema.$dimension ORDER BY type,duration,endDate LIMIT 5000";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);

    #if there aren't any time periods in the data source
    if ($status < 1)
    {
      print("]}]\n");
      exit;
    }

    $first = 1;
    while (($id, $baseName, $name, $duration, $type) = $dbOutput->fetchrow_array)
    {
      #if we don't already have a parent tree branch, create it
      if ($type == 0)
      {
        $key = "custom";
      }
      else
      {
        $key = "$duration-$type";
      }

      if ($timeBranchHash{$key} != 1)
      {

        #note that we're the first item in this folder
        $firstFolderItem = 1;

        #if we aren't the first type/duration folder, close out the previous one
        if ($first != 1)
        {
          print("]},\n");
        }

        #turn the type number into a human-readable string
        if ($type == 10)
        {
          $typeString = "Year";
        }
        elsif ($type == 20)
        {
          $typeString = "Month";
        }
        elsif ($type == 30)
        {
          $typeString = "Week";
        }
        elsif ($type == 40)
        {
          $typeString = "Day";
        }
        elsif ($type == 50)
        {
          $typeString = "Hour";
        }
        elsif ($type == 0)
        {
          $typeString = "Custom Time Period";
        }

        #add the branch to the tree
        $branchName = "$duration $typeString";
        print(" {\"key\":\"$key\", \"title\": \"$branchName\", \"type\":\"header\", \"folder\": true, \"children\": [\n");
        $timeBranchHash{$key} = 1;
      }

      #escape "special" characters
      $name =~ s/\\//g;
      $name =~ s/\"/\\"/g;

      #include the base name (for tooltip) if the name is aliased
      if ($baseName ne $name)
      {
        $baseName =~ s/\\//g;
        $baseName =~ s/\"/\\"/g;
        $baseName = "\"tooltip\":\"$baseName\",";
      }
      else
      {
        $baseName = "";
      }

      if ($first == 1)
      {
        $first = 0;
        $firstFolderItem = 0;
      }
      elsif (($first == 0) && ($firstFolderItem == 1))
      {
        $firstFolderItem = 0;
      }
      else
      {
        if ($firstFolderItem == 0) #if we aren't first item in folder
        {
          print(",\n");
        }
        $firstFolderItem = 0;
      }

      print("  {\"key\":\"$id\", \"title\":\"$name\", $baseName \"type\":\"base\"}");
    }

    print("]}\n");
    print(" ]}\n");
  }

  #products
  elsif ($dim eq "p")
  {

    $query = "SELECT ID, name, IFNULL(alias,name) AS dispName, merged \
        FROM $dsSchema.$dimension WHERE merged < 2 ORDER BY dispName LIMIT 25000";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    $totalProdCount = $status;

    $prodCount = 1;
    $first = 1;
    while (($id, $baseName, $name, $merged) = $dbOutput->fetchrow_array)
    {
      #escape "special" characters
      $name =~ s/\\//g;
      $name =~ s/\"/\\"/g;
      $name =~ s/\t/ /g;
      $baseName =~ s/\\//g;
      $baseName =~ s/\"/\\"/g;
      $baseName =~ s/\t/ /g;

      #include the base name (for tooltip) if the name is aliased
      if ($baseName ne $name)
      {
        $baseName = "\"tooltip\":\"$baseName\",";
      }
      elsif (($totalProdCount > 10_000) && (length($name) > 90))
      {
        $name = substr($baseName, 0, 50);
        $name .= "...";
        $name .= substr($baseName, -37);
        $baseName = "";
      }
      else
      {
        $baseName = "";
      }

      if ($first == 1)
      {
        $first = 0;
      }
      else
      {
        print(",\n");
      }

      #NB: merged products are 0 (not merged), 1 (parent), or 2 (child)
      if ($merged == 0)
      {
        print("{\"key\": \"$id\", \"title\": \"$name\", $baseName \"type\": \"base\", \"merged\": \"0\"}");
      }

      elsif ($merged == 1)
      {
        print("{\"key\":\"$id\", \"title\":\"$name\", $baseName \"type\":\"base\", \"merged\":\"1\", \"folder\":true, \"children\":[\n");

        $mergedItemCount = 1;
        @memberIDs = DSRmergedprod_get_members($db, $dsSchema, $id);
        foreach $memberID (@memberIDs)
        {
           print("{\"key\":\"$memberID\", \"title\":\"$itemNameHash{$memberID}\", $baseName \"type\":\"base\", \"merged\":\"2\"}");
          if ($mergedItemCount < scalar(@memberIDs))
          {
            print(",\n");
          }
          $mergedItemCount++;
        }
        print("]}");
      }

      $prodCount++;
    }

    #if the data source has more than 25K products, output a "More..." paging node
    if ($totalProdCount >= 25_000)
    {
      print(",\n{\"title\": \"More...\", \"statusNodeType\": \"paging\", \"icon\": \"false\", \"url\": \"/app/dsr/ajaxDimensionTrees.cld?ds=$dsID&d=p&s=dsr&mode=paging&start=25000\"}\n");
    }

    print("]}\n");
  }

  #geography
  elsif ($dim eq "g")
  {

    $query = "SELECT ID, name, IFNULL(alias,name) AS dispName \
        FROM $dsSchema.$dimension ORDER BY dispName LIMIT 10000";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);

    $first = 1;
    while (($id, $baseName, $name) = $dbOutput->fetchrow_array)
    {
      #escape "special" characters
      $name =~ s/\\//g;
      $name =~ s/\"/\\"/g;

      #include the base name (for tooltip) if the name is aliased
      if ($baseName ne $name)
      {
        $baseName =~ s/\\//g;
        $baseName =~ s/\"/\\"/g;
        $baseName = "\"tooltip\":\"$baseName\",";
      }
      else
      {
        $baseName = "";
      }

      if ($first == 1)
      {
        $first = 0;
      }
      else
      {
        print(",\n");
      }

      print("{\"key\":\"$id\", \"title\":\"$name\", $baseName \"type\":\"base\"}");
    }

    print("]}\n");
  }

  #measures
  elsif ($dim eq "m")
  {
    $query = "SELECT ID, name, IFNULL(alias,name) AS dispName, calculation, calcBeforeAgg, prodAggRule, geoAggRule, timeAggRule, UNIX_TIMESTAMP(lastCalc) \
        FROM $dsSchema.$dimension ORDER BY dispName";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);

    $first = 1;
    while (($id, $baseName, $name, $calculation, $calcBeforeAgg, $prodAggRule, $geoAggRule, $timeAggRule, $lastCalc) = $dbOutput->fetchrow_array)
    {

      #escape "special" characters
      $name =~ s/\\//g;
      $name =~ s/\"/\\"/g;

      #include the base name (for tooltip) if the name is aliased
      if ($baseName ne $name)
      {
        $baseName =~ s/\\//g;
        $baseName =~ s/\"/\\"/g;
        $baseName = "\"tooltip\":\"$baseName\",";
      }
      else
      {
        $baseName = "";
      }

      #if we're a calculated measure, display the type of calculation
      if (length($calculation) > 2)
      {

        #if the calculation is still calculating
        if ($lastCalc < 1)
        {
          $name = "$name (Calculating...)";
        }

        #else tell the user what kind of calc measure it us
        else
        {
          $calculation =~ m/^(.*?)\|/;
          $calcType = $1;
          $calcType = DSRmeasures_get_calc_type_name($calcType);
          $name = "$name ($calcType)";
        }
      }

      #see if we're undefined, so we can display the measure as red in the tree
      if ((length($calculation) > 2) && ($calcBeforeAgg == 0))
      {
        $name = $name;
      }
      elsif ((length($prodAggRule) < 2) || (length($geoAggRule) < 2) || (length($timeAggRule) < 2))
      {
        $name = "<FONT COLOR='red'>$name</FONT>";
      }
      elsif (($prodAggRule eq "None") || ($geoAggRule eq "None") || ($timeAggRule eq "None"))
      {
        $name = "<FONT COLOR='red'>$name</FONT>";
      }

      if ($first == 1)
      {
        $first = 0;
      }
      else
      {
        print(",\n");
      }

      if (length($calculation) > 2)
      {
        print("{\"key\":\"$id\", \"title\":\"$name\", $baseName \"type\":\"base\", \"icon\":\"/icons/dijitLeafCalc.png\"}");
      }
      else
      {
        print("{\"key\":\"$id\", \"title\":\"$name\", $baseName \"type\":\"base\"}");
      }
    }
    print("]}\n");
  }

  #get the list of dimension attributes from the database
  $dbname = "$dsSchema.$dbStub" . "attributes";
  $query = "SELECT ID, name FROM $dbname ORDER BY name";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  ($id, $name) = $dbOutput->fetchrow_array;

  #if there are any attributes for this dimension...
  if ((defined($id)) && ($caller ne "sel"))
  {

    $json = ",\n{\"key\":\"attributes\", \"title\":\"Attributes\", \"type\":\"header\", \"folder\":true,\"children\":[\n";

    while (defined($id))
    {
      #escape "special" XML characters
      $name =~ s/\\//g;
      $name =~ s/\"/\\"/g;

      $json = $json . "{\"key\":\"ATT_$id\", \"title\":\"$name\", \"type\":\"attr\"},\n";
      ($id, $name) = $dbOutput->fetchrow_array;
    }
    chop($json);  chop($json);

    print("$json ]}\n");
  }

  #get the dimension's lists from the database
  $dbname = "$dsSchema.$dbStub" . "list";
  $query = "SELECT ID, name, members FROM $dbname ORDER BY name";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  ($id, $name, $members) = $dbOutput->fetchrow_array;

  #if there are any lists for this dimension...
  if (defined($id))
  {
    $listsFound = 1;

    print(",\n{\"key\":\"lists\", \"title\":\"Lists\", \"type\":\"header\", \"folder\":true, \"children\":[\n");

    #output the tree folder node for the list
    while (defined($id))
    {
      #escape "special" XML characters
      $name =~ s/\\//g;
      $name =~ s/\"/\\"/g;

      print("{\"key\":\"LIS_$id\", \"title\":\"$name\", \"type\":\"list\", \"folder\":true, \"children\":[\n");

      #get the items contained in this list

      @listMembers = split(',', $members);
      $count = 0;
      foreach $itemID (@listMembers)
      {
        $name = $itemNameHash{$itemID};
        $name =~ s/"//g;
        $name =~ s/\\//g;

        if (($dim eq "m") && (defined($measureInfoHash{$itemID})))
        {
          $name = $name . " ($measureInfoHash{$itemID})";
          print("{\"key\":\"LIS_$id.$itemID\", \"title\":\"$name\", \"type\":\"listitem\", \"icon\":\"/icons/dijitLeafCalc.png\"}");
        }
        else
        {
          print("{\"key\":\"LIS_$id.$itemID\", \"title\":\"$name\", \"type\":\"listitem\"}");
        }

        $count++;
        if ($count < scalar(@listMembers))
        {
          print(",\n");
        }
        else
        {
          print("\n");
        }

      }

      ($id, $name, $members) = $dbOutput->fetchrow_array;

      #close the list-level JSON
      if ($id < 1)
      {
        print("]}");
      }
      else
      {
        print("]},\n");
      }
    }

    #close Lists branch
    print("]}\n");
  }


  #if we're in the measure's dimension and we were called by the data selector,
  #we want to display all segmentations and attributes from all dimensions as
  #valid choices
  if (($caller eq "sel") && ($dim eq "m"))
  {
    $headerPrinted = 0;
    $attrFound = 0;
    $json = "";

    #get the list of product attributes from the data source
    $query = "SELECT ID, name FROM $dsSchema.product_attributes ORDER BY name";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);

    ($id, $name) = $dbOutput->fetchrow_array;
    if (defined($id))
    {
      $attrFound = 1;

      if ($headerPrinted == 0)
      {
        $json .= ",{\"key\":\"attributes\", \"title\":\"Attributes\", \"type\":\"header\", \"folder\":true, \"children\":[\n";
        $headerPrinted = 1;
      }

      while (defined($id))
      {
        #escape "special" XML characters
        $name =~ s/\\//g;
        $name =~ s/\"/\\"/g;

        $json .= "{\"key\":\"PATT_$id\", \"title\":\"$name\", \"type\":\"attr\"},\n";
        ($id, $name) = $dbOutput->fetchrow_array;
      }
    }

    #get the list of geography attributes from the data source
    $query = "SELECT ID, name FROM $dsSchema.geography_attributes ORDER BY name";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);

    ($id, $name) = $dbOutput->fetchrow_array;
    if (defined($id))
    {
      $attrFound = 1;

      if ($headerPrinted == 0)
      {
        $json .= "{\"key\":\"attributes\", \"title\":\"Attributes\", \"type\":\"header\", \"folder\":true, \"children\":[\n";
        $headerPrinted = 1;
      }

      while (defined($id))
      {
        #escape "special" XML characters
        $name =~ s/\\//g;
        $name =~ s/\"/\\"/g;

        $json = $json . "{\"key\":\"GATT_$id\", \"title\":\"$name\", \"type\":\"attr\"},\n";
        ($id, $name) = $dbOutput->fetchrow_array;
      }
    }

    if ($attrFound == 1)
    {
      chop($json);  chop($json);
      $json = $json . "]},\n";
    }

    $headerPrinted = 0;
    $segsFound = 0;

    #get the list of product segmentations from the data source
    $query = "SELECT ID, name FROM $dsSchema.product_segmentation ORDER BY name";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);

    ($id, $name) = $dbOutput->fetchrow_array;
    if (defined($id))
    {
      $segsFound = 1;

      if ($headerPrinted == 0)
      {
        if ($attrFound == 0)

        {
          $json .= ",";
        }
        $json .= "{\"key\":\"segmentations\", \"title\":\"Segmentations\", \"type\":\"header\", \"folder\":true, \"children\":[\n";
        $headerPrinted = 1;
      }

      while (defined($id))
      {
        #escape "special" XML characters
        $name =~ s/\\//g;
        $name =~ s/\"/\\"/g;

        $json .= "{\"key\":\"PSEG_$id\", \"title\":\"$name\", \"type\":\"seg\"},\n";
        ($id, $name) = $dbOutput->fetchrow_array;
      }
    }

    #get the list of geography segmentations from the data source
    $query = "SELECT ID, name FROM $dsSchema.geography_segmentation ORDER BY name";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);

    ($id, $name) = $dbOutput->fetchrow_array;
    if (defined($id))
    {
      $segsFound = 1;

      if ($headerPrinted == 0)
      {
        $json .= "{\"key\":\"segmentations\", \"title\":\"Segmentations\", \"type\":\"header\", \"folder\":true, \"children\":[\n";
        $headerPrinted = 1;
      }

      while (defined($id))
      {
        #escape "special" XML characters
        $name =~ s/\\//g;
        $name =~ s/\"/\\"/g;

        $json .= "{\"key\":\"GSEG_$id\", \"title\":\"$name\", \"type\":\"seg\"},\n";
        ($id, $name) = $dbOutput->fetchrow_array;
      }
    }

    if ($segsFound == 1)
    {
      chop($json);  chop($json);
      $json .= "]},\n";
    }

    chop($json);  chop($json);
    print "$json";
  }


  #all structures past this point aren't valid for the measure dimension
  if ($dim eq "m")
  {
    #terminate the tree JSON
    print("]\n");

    exit;
  }


  #get the dimension's aggregates from the database
  $dbname = "$dsSchema.$dbStub" . "aggregate";
  $query = "SELECT ID, name, addMembers, subtractMembers FROM $dbname ORDER BY name";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  ($id, $name, $members, $subMembers) = $dbOutput->fetchrow_array;

  #if there are any aggregates for this dimension...
  if (defined($id))
  {

    print(",\n{\"key\":\"aggregates\", \"title\":\"Aggregates\", \"type\":\"header\", \"folder\":true, \"children\":[\n");

    #output the tree folder node for the list
    while (defined($id))
    {
      #escape "special" XML characters
      $name =~ s/\\//g;
      $name =~ s/\"/\\"/g;

      print("{\"key\":\"AGG_$id\", \"title\":\"$name\", \"type\":\"aggr\", \"folder\":true, \"children\":[\n");

      #get the items contained in this aggregate

      @listMembers = split(',', $members);
      @listSubMembers = split(',', $subMembers);
      push(@listMembers, @listSubMembers);
      $count = 0;
      foreach $itemID (@listMembers)
      {
        $name = $itemNameHash{$itemID};

        #escape "special" XML characters
        $name =~ s/\\//g;
        $name =~ s/\"/\\"/g;

        print("{\"key\":\"AGG_$id.$itemID\", \"title\":\"$name\", \"type\":\"aggitem\"}");

        $count++;
        if ($count < scalar(@listMembers))
        {
          print(",\n");
        }
        else
        {
          print("\n");
        }
      }

      ($id, $name, $members) = $dbOutput->fetchrow_array;

      #close the aggregate-level JSON
      if ($id < 1)
      {
        print("]}");
      }
      else
      {
        print("]},\n");
      }
    }

    #close Aggregates branch
    print("]}\n");
  }

  #get the list of dimension segmentations from the database
  %segNameHash = DSRsegmentation_get_segmentations_hash($db, $dsSchema, $dim);
  $segCount = keys(%segNameHash);

  #if there are any segmentations for this dimension...
  if ($segCount > 0)
  {

    #NB: we want to avoid displaying an expando next to empty segments (our
    #    users depend on that visual cue), so build a hash of segments that
    #    contain items
    $dbName = "$dsSchema.$dbStub" . "segment_item";
    $query = "SELECT DISTINCT segmentID FROM $dbName";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);

    while (($segmentID) = $dbOutput->fetchrow_array)
    {
      $segmentChildHash{$segmentID} = 1;
    }

    print(",\n{\"key\":\"segmentations\", \"title\":\"Segmentations\", \"type\":\"header\", \"folder\":true, \"children\":[\n");

    #output the tree folder node for the segmentation
    $segsCompleted = 0;
    foreach $segID (sort {$segNameHash{$a} cmp $segNameHash{$b}} keys %segNameHash)
    {
      $segName = $segNameHash{$segID};

      #escape "special" XML characters
      $segName =~ s/\\//g;
      $segName =~ s/\"/\\"/g;

      print("  {\"key\":\"SEG_$segID\", \"title\":\"$segName\", \"type\":\"seg\", \"folder\":true, \"children\":[\n");

      #get the segments contained in the segmentation and output them as folder
      #nodes
      $dbname = "$dsSchema.$dbStub" . "segment";
      $query = "SELECT ID, name FROM $dbname WHERE segmentationID=$segID ORDER BY name";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      KAPutil_handle_db_err($db, $status, $query);
      $segmentCount = $status;

      #handle empty segmentations (no segments)
      if ($segmentCount < 1)
      {
        print("    {\"key\":\"SMT_0\", \"title\":\"<EM>(Empty)</EM>\", \"type\":\"header\"}\n");
      }

      $segmentsCompleted = 0;
      while (($segmentID, $segmentName) = $dbOutput->fetchrow_array)
      {
        #escape "special" XML characters
        $segmentName =~ s/\\//g;
        $segmentName =~ s/\"/\\"/g;

        #if the segment is empty
        if ($segmentChildHash{$segmentID} < 1)
        {
          print("    {\"key\":\"SMT_$segmentID\", \"title\":\"$segmentName\", \"type\":\"segment\", \"folder\":true");
        }

        #else the segment contains items to be displayed
        else
        {
          print("    {\"key\":\"SMT_$segmentID\", \"title\":\"$segmentName\", \"type\":\"segment\", \"folder\":true, \"lazy\":true");
        }

        $segmentsCompleted++;

        #close the segment-level JSON
        if ($segmentsCompleted == $segmentCount)
        {
          print("}\n");
        }
        else
        {
          print("},\n");
        }
      }

      $segsCompleted++;

      #if we're done close the segmentation-level JSON
      if ($segsCompleted == $segCount)
      {
        print("  ]}");
      }
      else
      {
        print("  ]},\n");
      }
    }

    print("\n]}\n");
  }


  #get the dimension's segmentation hierarchies from the database
  $dbname = "$dsSchema.$dbStub" . "seghierarchy";
  $query = "SELECT ID, name, segmentations FROM $dbname ORDER BY name";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  ($segHierID, $name, $segmentations) = $dbOutput->fetchrow_array;

  #if there are any hierarchies for this dimension...
  if (defined($segHierID))
  {

    #get hash of all segment names in the data source
    %segmentNameHash = DSRseg_get_segments_hash($db, $dsSchema, $dim);

    print(",\n{\"key\":\"segmentationhiers\", \"title\":\"Segmentation Hierarchies\", \"type\":\"header\", \"folder\":true, \"children\":[\n");

    #output the tree folder node for the list
    while (defined($segHierID))
    {

      #escape "special" XML characters
      $name =~ s/\\//g;
      $name =~ s/\"/\\"/g;

      print("{\"key\":\"SHS_$segHierID\", \"title\":\"$name\", \"type\":\"segmenthier\", \"folder\":true, \"children\":[\n");

      #get the item membership "chain" for this seg hierarchy
      %chains = DSRseghier_get_item_chain_hash($db, $dsSchema, $dim, $segHierID);

      #if the hierarchy is invalidly defined
      if (scalar(%chains) < 1)
      {
        print("{\"key\":\"ERROR\", \"title\":\"Invalid Hierarchy\", \"type\":\"error\"}");
        print("]}\n");

        ($segHierID, $name, $segmentations) = $dbOutput->fetchrow_array;
        if ($segHierID > 0)
        {
          print(",\n");
        }

        next;
      }

      #clear out the hash we use to keep track of which tree nodes we've
      #already created
      #NB: We use this hash to output the hierarchy in alpha order at each
      #    level. The keys are a concatenation of the name and level ID
      #    that can be sorted to output an in-order tree. The values are the
      #    actual JSON statements that build the tree.
      #    EG: Alpha SHS_1_18 ->{id:'SHS_1_18', name:'Alpha', ...
      undef(%nodeHash);

      #add folder and item nodes for each item in the chain
      foreach $itemID (keys %chains)
      {

        #split the chain into its segments at each level
        @segs = split('_', $chains{$itemID});

        #create the tree folders at each level (if we haven't already)
        $id = "SHS_$segHierID";
        $levelNum = 0;
        foreach $levelID (@segs)
        {
          $parentID = $id;
          $id = $id . "_$levelID";
          $name = $segmentNameHash{$levelID};

          #zero-pad our hash key (have to do this to make sorting work as
          #expected below)
          $hashKey = "";
          @tmp = split('_', $id);
          foreach $tmp (@tmp)
          {
            if ($tmp =~ m/^S/)
            {
              $hashKey = $tmp;
            }
            else
            {
              $tmp = sprintf("%05d", $tmp);
              $hashKey .= "_" . $tmp;
            }
          }

          if (!(defined($nodeHash{$hashKey})))
          {

            #escape "special" XML characters
            $name =~ s/\\//g;
            $name =~ s/\"/\\"/g;

            if ($levelNum < (scalar(@segs) - 1))
            {
              $val = "{\"key\":\"$id\", \"title\":\"$name\", \"type\":\"segmenthierlevel\", \"folder\":true, \"children\":[\n";
            }
            else
            {
              $val = "{\"key\":\"$id\", \"title\":\"$name\", \"type\":\"segmenthierlevel\", \"folder\":true, \"lazy\":true}";
            }

            $nodeHash{$hashKey} = $val;
          }

          $levelNum++;
        }
      }

      #NB: we're keeping careful track of how many levels deep in the tree we
      #    are now versus where we were for the last item so we can output
      #    the correct number of JSON closures
      $prevLazy = 0;
      $curLazy = 0;
      $curLevel = 0;
      $prevLevel = 0;
      foreach $key (sort keys %nodeHash)
      {
        $json = $nodeHash{$key};

        $prevLazy = $curLazy;
        $prevLevel = $curLevel;

        $curLevel = () = $key =~ m/\_/g;

        #determine if we're a "lazy loading" bottom level
        $curLazy = 0;
        if ($json =~ m/\"lazy\":true}/)
        {
          $curLazy = 1;
        }

        #if we're a "lazy loading" bottom level and so was the previous line
        if (($curLazy == 1) && ($prevLazy == 1))
        {
          print(",\n");
        }

        #else if we're not a lazy-loading bottom level but the previous line was
        elsif (($curLazy == 0) && ($prevLazy == 1))
        {
          print("\n");
          print("]}\n");
        }

        #handle closing out any levels above us
        $delta = $prevLevel - $curLevel;
        $delta--;
        if ($delta == 0)
        {
          print(",");
        }
        elsif ($delta > 0)
        {
          while ($delta > 0)
          {
            print("]}\n");
            $delta--;
          }
          print(",");
        }

        print("$json");
      }

      #close off the json, accounting for however many levels deep we are now
      $delta = () = $json =~ m/\_/g;
      $delta--;
      while ($delta > 0)
      {
        print("]}\n");
        $delta--;
      }

      ($segHierID, $name, $segmentations) = $dbOutput->fetchrow_array;

      #close the seghier-level JSON
      if ($segHierID < 1)
      {
        #NO-OP
      }
      else
      {
        print(",\n");
      }

    }

    #terminate segmentation hierarchies tree JSON
    print("]}\n");
  }


  #terminate the tree JSON
  print("]\n");



#EOF
