#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($kapDB);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Set Parsing Options</TITLE>
<LINK REL="SHORTCUT ICON" HREF="$Lib::KoalaConfig::kapHostURL/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Saving...');
  return(true);
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($kapDB, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="$Lib::KoalaConfig::kapHostURL/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">Data Prep</A></LI>
    <LI CLASS="breadcrumb-item">$flowName</LI>
    <LI CLASS="breadcrumb-item active">Set Parsing Options</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $flowID = $q->param('f');
  $jobID = $q->param('j');
  $action = $q->param('a');

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #perform strict state checking, otherwise return user to flowOpen
  $query = "SELECT state FROM prep.jobs WHERE ID=$jobID";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($state) = $dbOutput->fetchrow_array;

  #we should be in NESTED-CONVERT state when we're initially called
  if (($state ne "NESTED-CONVERT") && ($state ne "PARSE-WAIT"))
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: flowOpen.cld?f=$flowID\n\n");
    exit;
  }

  $flowName = prep_flow_id_to_name($prepDB, $flowID);

  print_html_header();

  #make sure we have write privs for this data source
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this data flow.");
  }

  #set our state in case the user needs to resume
  $query = "UPDATE prep.jobs SET state='PARSE-WAIT' WHERE ID=$jobID";
  $prepDB->do($query);

  #set default parsing options
  $separator = "comma";
  $skipTop = "";
  $skipTopRows = 0;
  $headers = "CHECKED";
  $headerRows = 1;
  $compressWS = "CHECKED";
  $trimWS = "CHECKED";
  $blanks = "na";
  $mktTrim = "1";

  if ($action eq "e")
  {
    $actionVal = "Edit";

    #pull pre-defined parsing options from database
    $query = "SELECT parseOptions FROM prep.flows WHERE ID=$flowID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($parseOptions) = $dbOutput->fetchrow_array;

    if (($parseOptions =~ m/^SEPARATOR=(.*?)\|SEPCHAR=(.*?)\|SKIPTOP=(.*?)\|SKIPTOPROWS=(.*?)\|HEADERS=(.*?)\|HEADERROWS=(.*?)\|COMPRESSWS=(.*?)\|TRIMWS=(.*?)\|BLANKSZEROES=(.*?)\|MKTTRIM=(.*)$/) ||
        ($parseOptions =~ m/^SEPARATOR=(.*?)\|SEPCHAR=(.*?)\|SKIPTOP=(.*?)\|SKIPTOPROWS=(.*?)\|HEADERS=(.*?)\|HEADERROWS=(.*?)\|COMPRESSWS=(.*?)\|TRIMWS=(.*?)\|BLANKSZEROES=(.*)$/))
    {
      $separator = $1;
      $sepChar = $2;
      $skipTop = $3;
      $skipTopRows = $4;
      $headers = $5;
      $headerRows = $6;
      $compressWS = $7;
      $trimWS = $8;
      $blanks = $9;
      $mktTrim = $10;

      if ($separator ne "other")
      {
        $sepChar = "";
      }
      if ($skipTop eq "on")
      {
        $skiptop = "CHECKED";
      }
      if ($headers eq "on")
      {
        $headers = "CHECKED";
      }
      if ($compressWS eq "on")
      {
        $compressWS = "CHECKED";
      }
      if ($trimWS eq "on")
      {
        $trimWS = "CHECKED";
      }
      if ($blanks == 0)
      {
        $blanks = "na";
      }
      else
      {
        $blanks = "zeroes";
      }
      if (length($mktTrim) < 1)
      {
        $mktTrim = "0";
      }
    }
  }
  else
  {
    $actionVal = "New";
  }

  if ($mktTrim eq "1")
  {
    $email = lc($email);
    if ($email =~ m/archpointgroup/)
    {
      $mktTrim = "archpoint";
    }
    elsif ($email =~ m/bbiteam/)
    {
      $mktTrim = "bbi";
    }
    elsif ($email =~ m/esmferolie/)
    {
      $mktTrim = "esm";
    }
    elsif ($email =~ m/integrityfood/)
    {
      $mktTrim = "integrity";
    }
    elsif ($email =~ m/ritewayfoods/)
    {
      $mktTrim = "riteway";
    }
    else
    {
      $mktTrim = 0;
    }
  }

  #determine if we're pulling from an IDW - don't show pre-trim if it's from
  #another source
  $query = "SELECT sourceInfo FROM prep.flows WHERE ID=$flowID";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($sourceInfo) = $dbOutput->fetchrow_array;

  if ($sourceInfo =~ m/^FTP=nielsen/i)
  {
    $showPreTrim = 1;
  }
  else
  {
    $mktTrim = 0;
  }

  print <<END_HTML;
<FORM METHOD="post" ACTION="flowLoadRawData.cld" onsubmit="return checkForm(this);">
<INPUT TYPE="hidden" NAME="f" VALUE="$flowID">
<INPUT TYPE="hidden" NAME="j" VALUE="$jobID">
<INPUT TYPE="hidden" NAME="a" VALUE="$action">

<DIV CLASS="card border-primary mx-auto" STYLE="width:800px;">
  <DIV CLASS="card-header bg-primary text-white">Parsing Options</DIV>
  <DIV CLASS="card-body">
    <TABLE>
      <TR>
        <TD STYLE="vertical-align:top;">

          <TABLE CLASS="table table-sm">
            <THEAD><TR>
              <TH NOWRAP>Column Separators:</TH>
            </TR></THEAD>
            <TR>
              <TD>
                <DIV CLASS="form-check">
                  <INPUT CLASS="form-check-input" TYPE="radio" NAME="separator" ID="comma" VALUE="comma" CHECKED>
                  <LABEL CLASS="form-check-label" FOR="comma">Commas (CSV)</LABEL>
                </DIV>
              </TD>
            </TR>
            <TR>
              <TD>
                <DIV CLASS="form-check">
                  <INPUT CLASS="form-check-input" TYPE="radio" NAME="separator" ID="tab" VALUE="tab">
                  <LABEL CLASS="form-check-label" FOR="tab">Tabs (TSV)</LABEL>
                </DIV>
              </TD>
            </TR>
            <TR>
              <TD>
                <DIV CLASS="form-check">
                  <INPUT CLASS="form-check-input" TYPE="radio" NAME="separator" ID="space" VALUE="space">
                  <LABEL CLASS="form-check-label" FOR="space">Spaces</LABEL>
                </DIV>
              </TD>
            </TR>
            <TR>
              <TD>
                <DIV CLASS="form-check">
                  <INPUT CLASS="form-check-input" TYPE="radio" NAME="separator" ID="other" VALUE="other">
                  <LABEL CLASS="form-check-label" FOR="other">Other character: <INPUT TYPE="text" CLASS="form-control" STYLE="width:5em;" NAME="sepchar" VALUE="$sepChar"></LABEL>
                </DIV>
              </TD>
            </TR>
          </TABLE>
          <SCRIPT>
            \$('#$separator').attr('checked', true);
          </SCRIPT>
        </TD>

        <TD STYLE="border-right: solid 1px #007bff;">
          &nbsp;
        </TD>

        <TD STYLE="vertical-align:top;">
          <TABLE CLASS="table table-sm">
            <THEAD><TR>
              <TH>Row Layout:</TH>
            </TR></THEAD>
            <TR>
              <TD CLASS="form-group form-inline">
                <DIV CLASS="form-check">
                  <INPUT CLASS="form-check-input" name="skiptop" ID="skiptop" TYPE="checkbox" $skiptop>
                  <LABEL CLASS="form-check-label" FOR="skiptop"></LABEL>Skip the first <INPUT TYPE="number" CLASS="form-control" NAME="skiptoprows" VALUE="$skipTopRows" STYLE="width:4em;"> rows of data
                </DIV>
              </TD>
            </TR>
            <TR>
              <TD CLASS="form-group form-inline">
                <DIV CLASS="form-check">
                  <INPUT CLASS="form-check-input" name="headers" ID="headers" TYPE="checkbox" $headers>
                  <LABEL CLASS="form-check-label" FOR="headers"></LABEL>Treat the next <INPUT TYPE="number" CLASS="form-control" NAME="headerrows" VALUE="$headerRows" STYLE="width:4em;"> rows as headers
                </DIV>
              </TD>
            </TR>
            <TR>
              <TD CLASS="form-group form-inline">
                <DIV CLASS="form-check">
                  <INPUT CLASS="form-check-input" name="footers" ID="footers" TYPE="checkbox">
                  <LABEL CLASS="form-check-label" FOR="footers"></LABEL>Discard the last <INPUT TYPE="number" CLASS="form-control" NAME="footerrows" VALUE="1" STYLE="width:4em;"> rows of data
                </DIV>
              </TD>
            </TR>
          </TABLE>
        </TD>

        <TD STYLE="border-right: solid 1px #007bff;">
         &nbsp;
        </TD>

        <TD STYLE="vertical-align:top;">
          <TABLE CLASS="table table-sm">
            <THEAD><TR>
              <TH>White Space:</TH>
            </TR></THEAD>
            <TR>
              <TD>
                <DIV CLASS="form-check">
                  <INPUT CLASS="form-check-input" name="compressWS" ID="compressWS" TYPE="checkbox" $compressWS>
                  <LABEL CLASS="form-check-label" FOR="compressWS">Compress whitespace</LABEL>
                </DIV>
              </TD>
            </TR>
            <TR>
              <TD>
                <DIV CLASS="form-check">
                  <INPUT CLASS="form-check-input" name="trimWS" ID="trimWS" TYPE="checkbox" $trimWS>
                  <LABEL CLASS="form-check-label" FOR="trimWS">Trim trailing & leading whitespace</LABEL>
                </DIV>
              </TD>
            </TR>
          </TABLE>
        </TD>

        <TD STYLE="border-right: solid 1px #007bff;">
         &nbsp;
        </TD>

        <TD STYLE="vertical-align:top;">
          <TABLE CLASS="table table-sm">
            <THEAD><TR>
              <TH>Blanks:</TH>
            </TR></THEAD>
            <TR>
              <TD>
                <DIV CLASS="form-check">
                  <INPUT CLASS="form-check-input" TYPE="radio" NAME="blanks" ID="na" VALUE="na">
                  <LABEL CLASS="form-check-label" FOR="na">Treat blanks as N/A</LABEL>
                </DIV>
              </TD>
            </TR>
            <TR>
              <TD>
                <DIV CLASS="form-check">
                  <INPUT CLASS="form-check-input" TYPE="radio" NAME="blanks" ID="zeroes" VALUE="zeroes">
                  <LABEL CLASS="form-check-label" FOR="zeroes">Treat blanks as zeroes</LABEL>
                </DIV>
              </TD>
            </TR>
          </TABLE>
          <SCRIPT>
            \$('#$blanks').attr('checked', true);
          </SCRIPT>
        </TD>
      </TR>
    </TABLE>
END_HTML

  if ($showPreTrim == 1)
  {
    print <<END_HTML;
    <P>
    <TABLE CLASS="table table-condensed">
      <TR>
        <TD>
          <SELECT CLASS="form-select" NAME="mktTrim" ID="mktTrim" STYLE="width:20em;">
            <OPTION VALUE="0">Use all markets</OPTION>
            <OPTION VALUE="archpoint">Pre-trim for ArchPoint markets</OPTION>
            <OPTION VALUE="apac">Pre-trim for ArchPoint/Acadian markets</OPTION>
            <OPTION VALUE="aprw">Pre-trim for ArchPoint/RiteWay markets</OPTION>
            <OPTION VALUE="bair">Pre-trim for BAIR markets</OPTION>
            <OPTION VALUE="bbi">Pre-trim for BBI markets</OPTON>
            <OPTION VALUE="esm">Pre-trim for ESM Ferolie markets</OPTION>
            <OPTION VALUE="integrity">Pre-trim for Integrity markets</OPTION>
            <OPTION VALUE="riteway">Pre-trim for RiteWay markets</OPTION>
END_HTML

    if ($orgName eq "Koala Software")
    {
      print <<END_HTML;
            <OPTION VALUE="koalamix">Koala Test Mixed Markets</OPTION>
            <OPTION VALUE="koalaf">Koala Test National Food</OPTION>
            <OPTION VALUE="koalad">Koala Test National Drug</OPTION>
            <OPTION VALUE="koalam">Koala Test National Mass</OPTION>
            <OPTION VALUE="koalasmm">Koala Test SMMs</OPTION>
END_HTML
    }
    print <<END_HTML;
          </SELECT>
          <SCRIPT>
            \$('select#mktTrim').val('$mktTrim');
          </SCRIPT>
        </TD>
      </TR>
    </TABLE>
END_HTML
}

  print <<END_HTML;
    <P>
    <DIV CLASS="text-center">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" onclick="location.href='main.cld'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-submit">Next <I CLASS="bi bi-arrow-right"></I></BUTTON>
    </DIV>

  </DIV>
</DIV>

</FORM>
END_HTML

  print_html_footer();

#EOF
