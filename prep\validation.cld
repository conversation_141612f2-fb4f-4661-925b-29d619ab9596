#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::WebUtils;


#-------------------------------------------------------------------------------

my %columnTypeNames = (
    "product" => "Product",
    "geography" => "Geography",
    "time" => "Time Period",
    "upc" => "UPC/SKU",
    "pseg" => "Product Segmentation",
    "gseg" => "Geography Segmentation",
    "tseg" => "Time Segmentation",
    "pattr" => "Product Attribute",
    "gattr" => "Geography Attribute",
    "tattr" => "Time Attribute",
    "palias" => "Product Alias",
    "galias" => "Geography Alias",
    "talias" => "Time Alias",
    "measure" => "Measure",
  );



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($kapDB);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Data Validation</TITLE>
<LINK REL="SHORTCUT ICON" HREF="$Lib::KoalaConfig::kapHostURL/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$("#btn-submit").prop('disabled', true);
  \$("#btn-submit").text("Please Wait...");
  return(true);
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($kapDB, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="$Lib::KoalaConfig::kapHostURL/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">Data Prep</A></LI>
    <LI CLASS="breadcrumb-item">$flowName</LI>
    <LI CLASS="breadcrumb-item active">Validation</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $flowID = $q->param('f');
  $jobID = $q->param('j');

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #make sure we have write privs for this data source
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to change validation settings for this data flow.");
  }

  $flowName = prep_flow_id_to_name($prepDB, $flowID);

  print_html_header();

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

	#get our action to take on invalid database
	$query = "SELECT invalidAction FROM prep.flows WHERE ID=$flowID";
	$dbOutput = $prepDB->prepare($query);
	$dbOutput->execute;
	($invalidAction) = $dbOutput->fetchrow_array;

  #output the panel header HTML
  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col-xs-12 col-sm-12 col-md-12 col-lg-12 col-xl-12"> <!-- content -->

      <FORM METHOD="post" ACTION="validateApply.cld" onsubmit="return checkForm(this);">
      <INPUT TYPE="hidden" NAME="f" VALUE="$flowID">
      <INPUT TYPE="hidden" NAME="j" VALUE="$jobID">

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Data Validation</DIV>
        <DIV CLASS="card-body">

          <P>
          <DIV CLASS="row">
            <DIV CLASS="col-auto mt-2">
              <B>Action to take on invalid data:</B>
            </DIV>
            <DIV CLASS="col-auto">
              <SELECT NAME="invalidAction" ID="invalidAction" CLASS="form-select mx-1">
                <OPTION VALUE="warn">Halt data processing and warn me</OPTION>
                <OPTION VALUE="ignore">Ignore errors and continue processing</OPTION>
                <OPTION VALUE="remove">Remove invalid data and continue processing</OPTION>
              </SELECT>
              <SCRIPT>
                \$("select#invalidAction").val("$invalidAction");
              </SCRIPT>
            </DIV>
          </DIV>
          </P>

          <P>&nbsp;</P>

          <DIV CLASS="table-responsive">
            <TABLE CLASS="table table-sm table-striped">
END_HTML

  #get IDs of columns in display order
  @orderedCols = prep_flow_order_columns($prepDB, $flowID, $jobID);
  $colSelStr = join(',', @orderedCols);

  #get a hash of every column name, then reverse it to hash ID by name
  %colNames = prep_flow_get_column_hash($prepDB, $flowID, $jobID);
  %colIDs = reverse(%colNames);

  #initialize state of "blanks ok" hash to allow them in all fields
  foreach $colID (keys %colNames)
  {
    $blanksHash{$colID} = "CHECKED";
  }

  #get current validation statistics for each column, and store in hashes
  $query = "SELECT name, present, blank, minLength, maxLength, minVal, maxVal, matches \
      FROM prep.validation WHERE flowID=$flowID";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;

  while (($name, $present, $blank, $minLength, $maxLength, $minVal, $maxVal, $matches) = $dbOutput->fetchrow_array)
  {
    $colID = $colIDs{$name};

    #set checked state for "must be present"
    if ($present == 1)
    {
      $presentHash{$colID} = "CHECKED";
    }

    #set checked state for "blanks OK" - checked by default
    if ($blank == 0)
    {
      $blanksHash{$colID} = "";
    }

    $minLengthHash{$colID} = $minLength;
    $maxLengthHash{$colID} = $maxLength;

    $minValHash{$colID} = $minVal;
    $maxValHash{$colID} = $maxVal;

    $matchOpHash{$colID} = "contains";
    if (length($matches) > 3)
    {
      $matches =~ m/^(.*?) (.*)$/;
      $matchOpHash{$colID} = $1;
      $matchValHash{$colID} = $2;
    }
  }

  #grab the name and type of every column in this job for the flow
  $query = "SELECT ID, name, type FROM $masterColTable ORDER BY FIELD(ID, $colSelStr)";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;

  #run through each column in the job
  while (($colID, $colName, $colType) = $dbOutput->fetchrow_array)
  {

    #output "must be present" & "blanks OK" options
    print <<END_HTML;
              <TR>
                <TD><B>$colName</B></TD>
                <TD>$columnTypeNames{$colType}</TD>
                <TD NOWRAP>
                  <DIV CLASS="form-check">
                    <INPUT CLASS="form-check-input" name="present_$colID" ID="present_$colID" TYPE="checkbox" $presentHash{$colID}>
                    <LABEL CLASS="form-check-label" FOR="present_$colID">Must be present</LABEL>
                  </DIV>
                </TD>
                <TD NOWRAP>
                  <DIV CLASS="form-check">
                    <INPUT CLASS="form-check-input" name="blanks_$colID" ID="blanks_$colID" TYPE="checkbox" $blanksHash{$colID}>
                    <LABEL CLASS="form-check-label" FOR="blanks_$colID">Blanks OK</LABEL>
                  </DIV>
                </TD>
END_HTML

    #if we're a measure output min & max value inputs
    if ($colType eq "measure")
    {
      print <<END_HTML;
                <TD COLSPAN="2" CLASS="form-inline">
                  <INPUT NAME="min_$colID" ID="min_$colID" TYPE="number" placeholder="Min" CLASS="form-control mx-1" STYLE="width:6em;" VALUE="$minValHash{$colID}">
                  to
                  <INPUT NAME="max_$colID" ID="max_$colID" TYPE="number" placeholder="Max" CLASS="form-control mx-1" STYLE="width:6em;" VALUE="$maxValHash{$colID}">
                </TD>
                <TD>&nbsp;</TD>
END_HTML
    }

    #else we're a text field: output min & max length fields, and match fields
    else
    {
      print <<END_HTML;
                <TD CLASS="form-inline">
                  <INPUT NAME="minl_$colID" ID="minl_$colID" TYPE="number" placeholder="Min Length" CLASS="form-control" STYLE="width:8em;" VALUE="$minLengthHash{$colID}">
                  to
                  <INPUT NAME="maxl_$colID" ID="maxl_$colID" TYPE="number" placeholder="Max Length" CLASS="form-control" STYLE="width:8em;" VALUE="$maxLengthHash{$colID}">
                </TD>
                <TD>
                  <SELECT NAME="match_$colID" ID="match_$colID" CLASS="form-select">
                    <OPTION VALUE="contains">Contains</OPTION>
                    <OPTION VALUE="starts">Starts with</OPTION>
                    <OPTION VALUE="ends">Ends with</OPTION>
                  </SELECT>
                  <SCRIPT>
                    \$("select#match_$colID").val("$matchOpHash{$colID}");
                  </SCRIPT>
                </TD>
                <TD>
                  <INPUT TYPE="text" NAME="mval_$colID" ID="mval_$colID" CLASS="form-control" VALUE="$matchValHash{$colID}">
                </TD>
END_HTML
    }

    #close out table row
    print(" </TR>\n");
  }

    print <<END_HTML;
            </TABLE>
          </DIV>

          <P>
          <DIV CLASS="text-center">
            <BUTTON TYPE="button" CLASS="btn btn-secondary" onclick="location.href='flowViewData.cld?f=$flowID&j=$jobID'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-submit"><I CLASS="bi bi-hand-thumbs-up"></I> Validate</BUTTON>
          </DIV>


          </DIV>
        </DIV>

      </FORM>

      <P>
    </DIV>  <!-- content -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
