#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DataSel;
use Lib::DSRUtils;
use Lib::Reports;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $rptID = $q->param('rptID');
  $visID = $q->param('v');
  $action = $q->param('action');
  $lockProducts = $q->param('lockProducts');
  $lockGeographies = $q->param('lockGeographies');
  $lockTimes = $q->param('lockTimes');
  $lockMeasures = $q->param('lockMeasures');

  #fix up the CGI parameters from the submitted form
  if (defined($lockProducts))
  {
    $lockProducts = ($lockProducts eq "false") ? "0" : "1";
    $lockGeographies = ($lockGeographies eq "false") ? "0" : "1";
    $lockTimes = ($lockTimes eq "false") ? "0" : "1";
    $lockMeasures = ($lockMeasures eq "false") ? "0" : "1";
  }

  $db = KAPutil_connect_to_database();

  #make sure we have edit privs for this report
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this report.");
  }

  #get the main chart display details from the database
  $query = "SELECT design FROM visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($graphDesign) = $dbOutput->fetchrow_array;


  ########################################################################
  #
  # This code block is called on submit to save the changes
  #

  #if we're being called to save a new locked measure
  if (defined($lockProducts))
  {
    if ($lockProducts != 0)
    {
      $graphDesign = reports_set_style($graphDesign, "lockProducts", $lockProducts);
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "lockProducts");
    }

    if ($lockGeographies != 0)
    {
      $graphDesign = reports_set_style($graphDesign, "lockGeographies", $lockGeographies);
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "lockGeographies");
    }

    if ($lockTimes != 0)
    {
      $graphDesign = reports_set_style($graphDesign, "lockTimes", $lockTimes);
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "lockTimes");
    }

    if ($lockMeasures != 0)
    {
      $graphDesign = reports_set_style($graphDesign, "lockMeasures", $lockMeasures);
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "lockMeasures");
    }

    $q_graphDesign = $db->quote($graphDesign);
    $query = "UPDATE visuals SET design = $q_graphDesign WHERE ID=$visID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    $cubeName = cube_id_to_name($db, $rptID);
    $dsID = cube_get_ds_id($db, $rptID);
    $dsName = ds_id_to_name($db, $dsID);

    utils_audit($db, $userID, "Changed locked chart selections", $dsID, $rptID, 0);
    $activity = "$first $last changed locked chart selections for $cubeName in $dsName";
    utils_slack($activity);

    exit;
  }


  #########################################################################
  #
  # Everything after this point is called to display the locked measure dialog
  #

  $lockProducts = reports_get_style($graphDesign, "lockProducts");
  $lockGeographies = reports_get_style($graphDesign, "lockGeographies");
  $lockTimes = reports_get_style($graphDesign, "lockTimes");
  $lockMeasures = reports_get_style($graphDesign, "lockMeasures");

  $lockProducts = ($lockProducts eq "1") ? "CHECKED" : "";
  $lockGeographies = ($lockGeographies eq "1") ? "CHECKED" : "";
  $lockTimes = ($lockTimes eq "1") ? "CHECKED" : "";
  $lockMeasures = ($lockMeasures eq "1") ? "CHECKED" : "";

  print <<END_HTML;
<SCRIPT>
function submitForm()
{
  let lockProducts = \$("#lockProducts").prop("checked");
  let lockGeographies = \$("#lockGeographies").prop("checked");
  let lockTimes = \$("#lockTimes").prop("checked");
  let lockMeasures = \$("#lockMeasures").prop("checked");

  let url = "xhrChartLockDataSel?rptID=$rptID&v=$visID&lockProducts=" + lockProducts +
      "&lockGeographies=" + lockGeographies + "&lockTimes=" + lockTimes +
      "&lockMeasures=" + lockMeasures;

  \$.get(url, function(data, status)
  {
    location.href = "display.cld?rpt=$rptID&v=$visID";
  });
}
</SCRIPT>

<DIV CLASS="modal-dialog" STYLE="overflow-y: initial !important;">
  <DIV CLASS="modal-content">
    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Lock Data Selection</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">
      <FORM>
      <P>
        You can lock the current data selections for one or more dimensions in this visualization. Any changes made in the data selector won't affect the locked dimensions.
      </P>

      <P>
      <STRONG>Locked dimensions:</STRONG>

      <DIV CLASS="form-check">
        <INPUT CLASS="form-check-input" TYPE="checkbox" NAME="lockProducts" ID="lockProducts" $lockProducts>
        <LABEL CLASS="form-check-label" FOR="lockProducts">Products</LABEL>
      </DIV>
      <DIV CLASS="form-check">
        <INPUT CLASS="form-check-input" TYPE="checkbox" NAME="lockGeographies" ID="lockGeographies" $lockGeographies>
        <LABEL CLASS="form-check-label" FOR="lockGeographies">Geographies</LABEL>
      </DIV>
      <DIV CLASS="form-check">
        <INPUT CLASS="form-check-input" TYPE="checkbox" NAME="lockTimes" ID="lockTimes" $lockTimes>
        <LABEL CLASS="form-check-label" FOR="lockTimes">Times</LABEL>
      </DIV>
      <DIV CLASS="form-check">
        <INPUT CLASS="form-check-input" TYPE="checkbox" NAME="lockMeasures" ID="lockMeasures" $lockMeasures>
        <LABEL CLASS="form-check-label" FOR="lockMeasures">Measures</LABEL>
      </DIV>
      </FORM>

    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="submitForm()"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML

#EOF
