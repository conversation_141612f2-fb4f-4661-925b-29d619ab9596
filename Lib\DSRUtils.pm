package Lib::DSRUtils;

use lib "/opt/apache/app/";

use Exporter;
use Lib::DSRstructures;
use Lib::KoalaConfig;


our @ISA = ('Exporter');

our @EXPORT = qw(
    &KAPutil_handle_db_err
    &KAPutil_connect_to_database
    &KAPutil_db_table_exists
    &KAPutil_db_delete_table
    &KAPutil_get_user_org_id
    &KAPutil_get_org_quota_used
    &KAPutil_get_dim_name
    &KAPutil_get_dim_name_singular
    &KAPutil_get_dim_db_name
    &KAPutil_get_dim_stub_name
    &dsr_get_itemIDs_array
    &KAPutil_get_item_type
    &KAPutil_get_item_ID_name
    &KAPutil_base_name_to_ID
    &KAPutil_job_store_status
    &KAPutil_job_update_state
    &KAPutil_job_update_status
    &DSRutil_operation_ok
    &DSRutil_clear_status
    &KAPutil_get_load_avg
    &KAPutil_get_user_jobs
    &dsr_get_item_name_hash
    &dsr_get_base_item_name_hash
    &dsr_get_aliases_hash
    &dsr_get_aliases_hash_by_name
    &get_measure_name_hash
    &get_measure_ID_hash
    &DSRutil_get_time_aliases_format
    &DSRutil_build_time_aliases
  );


#scoped-in cached results for performance with very large data sources
our %productNameHash;
our $productNameHashSchema;
our $productNameHashAliases;




#-------------------------------------------------------------------------------
#
# Handle a database error of some kind during a utility function call
#

sub KAPutil_handle_db_err
{
  my ($date);

  my ($db, $status, $text) = @_;


  if (!defined($status))
  {
    $date = localtime();
    print STDERR "$date: $text\n";
    if ($db->errstr =~ m/^MySQL server has gone away/)
    {
      die("Lost connection to database, terminating");
    }
  }
}



#-------------------------------------------------------------------------------
#
# Connect to the Koala Analytics database
#

sub KAPutil_connect_to_database
{
  my ($db);

  $db = DBI->connect($Lib::KoalaConfig::dbServer, 'app', $Lib::KoalaConfig::password);

  return($db);
}



#-------------------------------------------------------------------------------
#
# Determine if the specified database table exists or not
#

sub KAPutil_db_table_exists
{
  my ($exists, $query, $dbOutput, $status);

  my ($db, $dsSchema, $tableName) = @_;


  $query = "SELECT * FROM information_schema.tables \
      WHERE table_schema = '$dsSchema' AND table_name = '$tableName' LIMIT 1";
  $dbOutput = $db->prepare($query);
  $exists = $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  if ($exists < 1)
  {
    $exists = 0;
  }

  return($exists);
}



#-------------------------------------------------------------------------------
#
# Drop the specified table from the database.
#

sub KAPutil_db_delete_table
{
  my ($query, $status);

  my ($db, $dsSchema, $tableName) = @_;


  $query = "DROP TABLE IF EXISTS $dsSchema.$tableName";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------------
#
# Get the specified user's organization ID
#

sub KAPutil_get_user_org_id
{
  my ($query, $dbOutput, $status, $orgID);

  my ($db, $userID) = @_;

  if ($userID < 1)
  {
    return(0);
  }

  $query = "SELECT orgID FROM app.users WHERE ID=$userID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($orgID) = $dbOutput->fetchrow_array;

  return($orgID);
}



#-------------------------------------------------------------------------------
#
# Return the percentage of used quota for the specified user's organization.
# Use an org-specific data volume if one exists, otherwise use the default
# disk volume.
#

sub KAPutil_get_org_quota_used
{
  my ($query, $dbOutput, $status, $orgID, $quotaPct);

  my ($db, $userID) = @_;


  #determine if we're using an external table space for capacity/IO reasons
  $orgID = KAPutil_get_user_org_id($db, $userID);

  $query = "SELECT dataQuotaUsage FROM app.orgs WHERE ID=$orgID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($quotaPct) = $dbOutput->fetchrow_array;

  #if we're just using the main volume, pull that number instead
  if ($quotaPct < 1)
  {
    $query = "SELECT value FROM app.config WHERE name='analytics_storage_usage'";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($quotaPct) = $dbOutput->fetchrow_array;
  }

  return($quotaPct);
}



#-------------------------------------------------------------------------------
#
# Return the human-readable name for the specified dimension code
#

sub KAPutil_get_dim_name
{
  my ($dimName);

  my ($dim, $capitalize) = @_;


  if ($dim eq "p")
  {
    $dimName = ($capitalize > 0) ? "Products" : "products";
  }
  elsif ($dim eq "g")
  {
    $dimName = ($capitalize > 0) ? "Geographies" : "geographies";
  }
  elsif ($dim eq "t")
  {
    $dimName = ($capitalize > 0) ? "Time Periods" : "time periods";
  }
  elsif ($dim eq "m")
  {
    $dimName = ($capitalize > 0) ? "Measures" : "measures";
  }
  else
  {
    undef($dimName);
  }

  return($dimName);
}



#-------------------------------------------------------------------------------
#
# Return the human-readable singular name for the specified dimension code
#

sub KAPutil_get_dim_name_singular
{
  my ($dimName);

  my ($dim, $capitalize) = @_;


  if ($dim eq "p")
  {
    $dimName = ($capitalize > 0) ? "Product" : "product";
  }
  elsif ($dim eq "g")
  {
    $dimName = ($capitalize > 0) ? "Geography" : "geography";
  }
  elsif ($dim eq "t")
  {
    $dimName = ($capitalize > 0) ? "Time Period" : "time period";
  }
  elsif ($dim eq "m")
  {
    $dimName = ($capitalize > 0) ? "Measure" : "measure";
  }
  else
  {
    undef($dimName);
  }

  return($dimName);
}



#-------------------------------------------------------------------------------
#
# Return the database name for the specified dimension code
#

sub KAPutil_get_dim_db_name
{
  my ($dimDB);

  my ($dim) = @_;


  if ($dim eq "p")
  {
    $dimDB = "products";
  }
  elsif ($dim eq "g")
  {
    $dimDB = "geographies";
  }
  elsif ($dim eq "t")
  {
    $dimDB = "timeperiods";
  }
  elsif ($dim eq "m")
  {
    $dimDB = "measures";
  }
  else
  {
    undef($dimDB);
  }

  return($dimDB);
}



#-------------------------------------------------------------------------------
#
# Get the "stub" name for a dimension's database tables
#

sub KAPutil_get_dim_stub_name
{
  my ($dbStub);

  my ($dim) = @_;


  if ($dim eq "p")
  {
    $dbStub = "product_";
  }
  elsif ($dim eq "g")
  {
    $dbStub = "geography_";
  }
  elsif ($dim eq "t")
  {
    $dbStub = "time_";
  }
  elsif ($dim eq "m")
  {
    $dbStub = "measure_";
  }
  else
  {
    undef($dbStub);
  }

  return($dbStub);
}



#-------------------------------------------------------------------------------
#
# Get all of the item IDs for the specified dimension from the specified data
# source, and return as array.
#

sub dsr_get_itemIDs_array
{
  my ($query, $dbOutput, $status, $dbName, $id);
  my (@itemIDs);

  my ($db, $dsSchema, $dim) = @_;


  $dbName = KAPutil_get_dim_db_name($dim);

  if ($dim eq "t")
  {
    $query = "SELECT ID FROM $dsSchema.$dbName ORDER BY duration, type, endDate";
  }
  elsif ($dim eq "p")
  {
    $query = "SELECT ID FROM $dsSchema.$dbName WHERE merged < 2 ORDER BY name";
  }
  else
  {
    $query = "SELECT ID FROM $dsSchema.$dbName ORDER BY name";
  }

  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  #run through query results, building up array of item IDs
  while (($id) = $dbOutput->fetchrow_array)
  {
    push(@itemIDs, $id);
  }

  return(@itemIDs);
}



#-------------------------------------------------------------------------------
#
# Return a text string describing what type an item is based on its ID
#

sub KAPutil_get_item_type
{
  my ($itemType);

  my ($id) = @_;


  #default is Base Item
  $itemType = "Base Item";

  if ($id =~ m/^ATT_/)
  {
    $itemType = "Attribute";
  }
  elsif ($id =~ m/^LIS_\d+\.\d+/)
  {
    $itemType = "List Item";
  }
  elsif ($id =~ m/^LIS_\d+/)
  {
    $itemType = "List";
  }
  elsif ($id =~ m/^AGG_\d+\.\d+/)
  {
    $itemType = "Aggregate Item";
  }
  elsif ($id =~ m/^AGG_\d+/)
  {
    $itemType = "Aggregate";
  }
  elsif ($id =~ m/^SMT_\d+\.\d+/)
  {
    $itemType = "Segment Item";
  }
  elsif ($id =~ m/^SMT_\d+/)
  {
    $itemType = "Segment";
  }
  elsif ($id =~ m/^SEG_\d+/)
  {
    $itemType = "Segmentation";
  }
  elsif ($id =~ m/^SHS_\d+_\d+/)
  {
    $itemType = "Hierarchy Level";
  }
  elsif ($id =~ m/^SHS_\d+/)
  {
    $itemType = "Segmentation Hierarchy";
  }

  return($itemType);
}



#-------------------------------------------------------------------------------
#
# Return the name associated with the specified item ID
#

sub KAPutil_get_item_ID_name
{
  my ($query, $dbOutput, $val, $dbName, $status, $ellipsis, $dbStub, $base);
  my (%dimNames);

  my ($db, $schema, $dim, $itemID) = @_;


  #determine appropriate items DB name for our dimension
  $base = 1;
  $dbName = KAPutil_get_dim_db_name($dim);
  $dbStub = KAPutil_get_dim_stub_name($dim);

  #strip extraneous leading/trailing comma from ID string
  if ($itemID =~ m/(.*),$/)
  {
    $itemID = $1;
  }
  if ($itemID =~ m/^,(.*)/)
  {
    $itemID = $1;
  }

  #handle being passed more than one item to get a name for
  if ($itemID =~ m/(.*?),.*/)
  {
    $itemID = $1;
    $ellipsis = ", ...";
  }

  #strip any apostrophes around the values
  if ($itemID =~ m/\'(.*)\'/)
  {
    $itemID = $1;
  }

  #handle getting the name for a segment
  if ($itemID =~ m/^SMT_(\d+)/)
  {
    $itemID = $1;
    $dbName = $dbStub . "segment";
    $base = 0;
  }

  #handle getting the name for an aggregate
  if ($itemID =~ m/^AGG_(\d+)/)
  {
    $itemID = $1;
    $dbName = $dbStub . "aggregate";
    $base = 0;
  }

  #handle getting the name for a hierarchy
  if ($itemID =~ m/^SHS_(\d+)/)
  {
    %dimNames = dsr_get_item_name_hash($db, $schema, "p");
    return($dimNames{$itemID});
  }

  #if we're looking up a base item, account for aliases
  if ($base == 1)
  {
    $query = "SELECT COALESCE(alias, name) FROM $schema.$dbName WHERE ID=$itemID";
  }
  else
  {
    $query = "SELECT name FROM $schema.$dbName WHERE ID=$itemID";
  }
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  ($val) = $dbOutput->fetchrow_array;

  $val .= $ellipsis;

  return($val);
}



#-------------------------------------------------------------------------------
#
# Return the ID associated with the specified base item name
#

sub KAPutil_base_name_to_ID
{
  my ($query, $dbOutput, $id, $dbName, $q_itemName, $status);

  my ($db, $schema, $dim, $itemName) = @_;


  $dbName = KAPutil_get_dim_db_name($dim);

  $q_itemName = $db->quote($itemName);

  $query = "SELECT ID FROM $schema.$dbName WHERE name LIKE $q_itemName";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  ($id) = $dbOutput->fetchrow_array;

  return($id);
}



#-------------------------------------------------------------------------------
#
# Stores an operation status - returns 0 if the specified operation might
# conflict with an already-running operation.
#

sub KAPutil_job_store_status
{
  my ($query, $status, $q_op, $q_status, $dbOutput);

  my ($db, $userID, $dsID, $cubeID, $operation, $status) = @_;


  $q_op = $db->quote($operation);
  $q_status = $db->quote($status);

  #handle a special case: a cube build running inside an auto reports process
  if ($operation eq "CUBE-UPDATE")
  {
    $query = "SELECT operation FROM app.jobs \
        WHERE PID=$$ AND node='$Lib::KoalaConfig::nodeName'";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($runningOp) = $dbOutput->fetchrow_array;
    if ($runningOp eq "AUTO-RPTS")
    {
      $query = "UPDATE app.jobs SET cubeID=$cubeID, lastAction=NOW() \
          WHERE PID=$$ AND node='$Lib::KoalaConfig::nodeName'";
      $status = $db->do($query);
      KAPutil_handle_db_err($db, $status, $query);
      return(1);
    }
  }

  $query = "INSERT INTO app.jobs \
      (PID, node, userID, dsID, cubeID, lastAction, operation, status) \
      VALUES ($$, '$Lib::KoalaConfig::nodeName', $userID, $dsID, $cubeID, NOW(), $q_op, $q_status)";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);

  return(1);
}



#-------------------------------------------------------------------------------
#
# Uses data from the currently running jobs table to determine if the
# specified operation is ok (if it can run without causing conflicts).
# Returns 1 if OK to proceed, 0 if not.
#

sub DSRutil_operation_ok
{
  my ($query, $status, $dbOutput, $opsString, $operation);

  my ($db, $dsID, $cubeID, $requestedOp) = @_;


  $query = "SELECT operation, status FROM app.jobs WHERE dsID=$dsID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  while (($operation, $status) = $dbOutput->fetchrow_array)
  {
    $opsString .= "$operation,";
  }

  #operations that modify DS structure block everything else
  if (($opsString =~ m/DS-UPDATE/) || ($opsString =~ m/ROLLBACK/) ||
      ($opsString =~ m/XFER-STRUCTS/) || ($opsString =~ m/XFER-MEASURES/) ||
      ($opsString =~ m/FORCE-REFRESH/) || ($opsString =~ m/COPY-DS/) ||
      ($opsString =~ m/ADD-MEASURE/) || ($opsString =~ m/OPTIMIZE/) ||
      ($opsString =~ m/BACKUP/) || ($opsString =~ m/MERGE-ITEMS/))
  {
    return(0);
  }

  #don't let a data source be double-exported to ODBC
  if ($requestedOp eq "ODBC")
  {
    if ($opsString =~ m/ODBC/)
    {
      return(0);
    }
  }

  #a data source can't be updated if it's being exported to ODBC
  if ($requestedOp eq "DS-UPDATE")
  {
    if ($opsString =~ m/ODBC/)
    {
      return(0);
    }
  }

  #a rollback can't happen if anything else is using the data source
  if ($requestedOp eq "ROLLBACK")
  {
    if (length($opsString) > 0)
    {
      return(0);
    }
  }

  #a calculated measure can't be added if anything else is using the DS
  if ($requestedOp eq "ADD-MEASURE")
  {
    if (length($opsString) > 0)
    {
      return(0);
    }
  }

  #a structure xfer shouldn't be done if anything else is using the DS
  if (($requestedOp eq "XFER-STRUCTS") || ($requestedOp eq "XFER-MEASURES"))
  {
    if (length($opsString) > 0)
    {
      return(0);
    }
  }

  #items can't be merged if anything else is using the DS
  if ($requestedOp eq "MERGE-ITEMS")
  {
    if (length($opsString) > 0)
    {
      return(0);
    }
  }

  #a data source can't be optimized if anything else is using it
  if ($requestedOp eq "OPTIMIZE")
  {
    if (length($opsString) > 0)
    {
      return(0);
    }
  }

  #a forced refresh can't happen if anything else is using the data source
  if ($requestedOp eq "FORCE-REFRESH")
  {
    if (length($opsString) > 0)
    {
      return(0);
    }
  }

  #auto reports can happen as long as DS structure isn't changing and another
  #cube update isn't happening in the same data source
  if ($requestedOp eq "AUTO-RPTS")
  {
    #NO-OP
  }

  #cube updates can happen as long as an auto report isn't being generated
  if ($requestedOp eq "CUBE-UPDATE")
  {
    #NO-OP
  }

  #elasticity model updates can happen as long as DS isn't changing, and another
  #process isn't refreshing the same model
  if ($requestedOp eq "ANALYTICS-PRICE")
  {
    #NO-OP
  }

  #forecast updates can happen as long as DS isn't changing
  if ($requestedOp eq "FORECAST")
  {
    #NO-OP
  }

  #all checks cleared, so we're good to go
  return(1);


=pod
  DS-UPDATE
  ODBC
  CUBE-UPDATE
  REPORT-EXPORT
  ROLLBACK
  XFER-STRUCTS
  XFER-MEASURES
  MERGE-ITEMS
  FORCE-REFRESH
  COPY-DS
  ADD-MEASURE
  BACKUP
  OPTIMIZE

  ANALYTICS-PRICE

  FORECAST
=cut
}



#-------------------------------------------------------------------------------
#
# Updates an operational state for a background job in Koala.
#

sub KAPutil_job_update_state
{
  my ($query, $q_state, $status);

  my ($db, $state) = @_;


  $q_state = $db->quote($state);
  $query = "UPDATE app.jobs SET state=$q_state \
      WHERE PID=$$ AND node='$Lib::KoalaConfig::nodeName'";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);

  return(1);
}



#-------------------------------------------------------------------------------
#
# Stores displayable status for a background job in Koala.
#

sub KAPutil_job_update_status
{
  my ($query, $q_status, $status);

  my ($db, $statusTxt) = @_;

  $q_status = $db->quote($statusTxt);
  $query = "UPDATE app.jobs SET status=$q_status, lastAction=NOW() \
      WHERE PID=$$ AND node='$Lib::KoalaConfig::nodeName'";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);

  return(1);
}



#-------------------------------------------------------------------------------
#
# Clears the operation status associated with the current process
#

sub DSRutil_clear_status
{
  my ($query, $status, $whereClause);

  my ($db, $operation) = @_;


  #if we're being told to only remove jobs of a certain operation type
  if (length($operation) > 0)
  {
    $whereClause = "AND operation='$operation'";
  }

  $query = "DELETE FROM app.jobs WHERE PID=$$ AND node='$Lib::KoalaConfig::nodeName' $whereClause";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------------
#
# Return the load average for the KAP cloud instance.
#

sub KAPutil_get_load_avg
{
  my ($query, $dbOutput, $status, $runningJobs, $loadAvg);

  my ($db) = @_;


  $query = "SELECT COUNT(*) FROM app.jobs";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($runningJobs) = $dbOutput->fetchrow_array;

  $loadAvg = $runningJobs / $Lib::KoalaConfig::cores;

  return($loadAvg);
}



#-------------------------------------------------------------------------------
#
# Return the number of jobs currently being run by the specified user
#

sub KAPutil_get_user_jobs
{
  my ($query, $dbOutput, $status, $runningJobs);

  my ($db, $userID) = @_;


  $query = "SELECT COUNT(PID) FROM app.jobs where userID=$userID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($runningJobs) = $dbOutput->fetchrow_array;

  return($runningJobs);
}



#-------------------------------------------------------------------------
#
# Get all of the item names for the specified dimension in the specified data
# source and return them in a hash indexed by their DSR IDs.
#

sub dsr_get_item_name_hash
{
  my ($query, $dbOutput, $id, $name, $itemDB, $dimDB, $dbName);
  my ($itemID, $segmentations, $segHierID, $segmentationID, $levelNum);
  my ($levelID, $parentID, $key, $status, $start, $i, $qualID, $level);
  my ($namePattern);
  my (@segmentationIDs, @segHierNames, @segs, @tmp);
  my (%itemNames, %segHierIDs, %chains, %nodeHash, %attrHash, %segHash);
  my (%segHierNamePatterns, %namePatternHash);

  my ($db, $dsSchema, $dim, $noAliases) = @_;


  #if we have a cached product names hash available that's valid
  if (($dim eq "p") && (%productNameHash))
  {
    if (($productNameHashSchema eq $dsSchema) && ($productNameHashAliases eq $noAliases))
    {
      return(%productNameHash);
    }
  }

  undef(%itemNames);

  if ($dsSchema =~ m/^\d+$/)
  {
    $dsSchema = "datasource_" . $dsSchema;
  }

  $dimDB = KAPutil_get_dim_stub_name($dim);
  $itemDB = KAPutil_get_dim_db_name($dim);

  #add base item names to hash
  if ($noAliases > 0)
  {
    $query = "SELECT ID, name FROM $dsSchema.$itemDB";
  }
  else
  {
    $query = "SELECT ID, IFNULL(alias, name) FROM $dsSchema.$itemDB";
  }
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  while (($id, $name) = $dbOutput->fetchrow_array)
  {
    $itemNames{$id} = $name;
  }

  #add aggregate names to hash
  if ($dim ne "m")
  {
    $dbName = $dimDB . "aggregate";
    $query = "SELECT ID, name FROM $dsSchema.$dbName";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);

    while (($id, $name) = $dbOutput->fetchrow_array)
    {
      $id = "AGG_" . $id;
      $itemNames{$id} = $name;
    }
  }

  #add list names to hash
  $dbName = $dimDB . "list";
  $query = "SELECT ID, name FROM $dsSchema.$dbName";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  while (($id, $name) = $dbOutput->fetchrow_array)
  {
    $id = "LIS_" . $id;
    $itemNames{$id} = $name;
  }

  #if we're the measure dim, add all attribute and segmentation names to hash
  if ($dim eq "m")
  {
    %attrHash = Lib::DSRstructures::DSRattr_get_attributes_hash($db, $dsSchema, "p");
    foreach $id (keys %attrHash)
    {
      $qualID = "PATT_" . $id;
      $itemNames{$qualID} = $attrHash{$id};
    }
    %attrHash = Lib::DSRstructures::DSRattr_get_attributes_hash($db, $dsSchema, "g");
    foreach $id (keys %attrHash)
    {
      $qualID = "GATT_" . $id;
      $itemNames{$qualID} = $attrHash{$id};
    }
    %attrHash = Lib::DSRstructures::DSRattr_get_attributes_hash($db, $dsSchema, "t");
    foreach $id (keys %attrHash)
    {
      $qualID = "TATT_" . $id;
      $itemNames{$qualID} = $attrHash{$id};
    }

    %segHash = Lib::DSRstructures::DSRsegmentation_get_segmentations_hash($db, $dsSchema, "p");
    foreach $id (keys %segHash)
    {
      $qualID = "PSEG_" . $id;
      $itemNames{$qualID} = $segHash{$id};
    }
    %segHash = Lib::DSRstructures::DSRsegmentation_get_segmentations_hash($db, $dsSchema, "g");
    foreach $id (keys %segHash)
    {
      $qualID = "GSEG_" . $id;
      $itemNames{$qualID} = $segHash{$id};
    }
    %segHash = Lib::DSRstructures::DSRsegmentation_get_segmentations_hash($db, $dsSchema, "t");
    foreach $id (keys %segHash)
    {
      $qualID = "TSEG_" . $id;
      $itemNames{$qualID} = $segHash{$id};
    }
  }

  if (!($db->ping))
  {
    $db = KAPutil_connect_to_database();
  }

  #add segmentation names to hash
  if ($dim ne "m")
  {
    $dbName = $dimDB . "segmentation";
    $query = "SELECT ID, name FROM $dsSchema.$dbName";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);

    while (($id, $name) = $dbOutput->fetchrow_array)
    {
      $id = "SEG_" . $id;
      $itemNames{$id} = $name;
    }

    #add segment names to hash
    $dbName = $dimDB . "segment";
    $query = "SELECT ID, name FROM $dsSchema.$dbName";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);

    while (($id, $name) = $dbOutput->fetchrow_array)
    {
      $id = "SMT_" . $id;
      $itemNames{$id} = $name;
    }

    #add segment hierarchy names to hash
    $dbName = $dimDB . "seghierarchy";
    $query = "SELECT ID, name, segmentations, namePattern FROM $dsSchema.$dbName";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);

    while (($id, $name, $segmentations, $namePattern) = $dbOutput->fetchrow_array)
    {
      $segHierNamePatterns{$id} = $namePattern;
      $segHierIDs{$id} = $segmentations;
      $id = "SHS_" . $id;
      $itemNames{$id} = $name;
    }

    #add segmentation hierarchy level names to hash
    foreach $segHierID (keys %segHierIDs)
    {

      #load up array of segment name hashes for each level in the hierarchy
      @segmentationIDs = split(',', $segHierIDs{$segHierID});
      undef(@segHierNames);
      foreach $segmentationID (@segmentationIDs)
      {
        my %levelHash = Lib::DSRstructures::DSRseg_get_segments_hash($db, $dsSchema, $dim, $segmentationID);
        push(@segHierNames, \%levelHash);
      }

      #get the item membership "chain" for this seg hierarchy
      %chains = Lib::DSRstructures::DSRseghier_get_item_chain_hash($db, $dsSchema, $dim, $segHierID);
      undef(%nodeHash);

      #turn the naming pattern string into a level-indexed hash
      #NB: this depends on the left-most level index in the naming pattern
      #    being the first segmentation we're including a name for. It's assumed
      #    that everything after this first segmentation is included in the name
      undef(%namePatternHash);
      @tmp = split(',', $segHierNamePatterns{$segHierID});
      foreach $level (@tmp)
      {
        if ($level =~ m/^(\d+):(\d+)/)
        {
          $namePatternHash{$1} = $2;
        }
      }

      #build up and add segment level names for every segmentation in the chain
      foreach $itemID (keys %chains)
      {

        #split the chain into its segments at each level
        @segs = split('_', $chains{$itemID});

        #create the hierarchy names at each level
        $id = "SHS_$segHierID";
        $levelNum = 0;
        $name = "";
        foreach $levelID (@segs)
        {
          $parentID = $id;
          $id .= "_$levelID";

          $name = "";

          #figure out where in the naming pattern we should start
          $start = $namePatternHash{$levelNum+1};

          #if we don't have a defined pattern, use the default of "all levels"
          if (!defined($start))
          {
            $start = 1;
          }

          #decrement by 1 to handle 0-indexed levelNum array
          $start--;

          #add the segment name for each level we need for the pattern
          for ($i=$start; $i < $levelNum+1; $i++)
          {
            $name .= " " . $segHierNames[$i]->{$segs[$i]};
          }

          $key = $name . " $id";
          if (!(defined($nodeHash{$key})))
          {
            $itemNames{$id} = $name;
            $nodeHash{$key} = 1;
          }

          $levelNum++;
        }
      }
    }
  }

  #if we went to the trouble to build a giant product name hash, cache it
  if ($dim eq "p")
  {
    %productNameHash = %itemNames;
    $productNameHashSchema = $dsSchema;
    $productNameHashAliases = $noAliases;
  }

  return(%itemNames);
}



#-------------------------------------------------------------------------
#
# Get all of the base item names for the specified dimension in the data
# source and return them in a hash indexed by their DSR IDs.
#

sub dsr_get_base_item_name_hash
{
  my ($query, $dbOutput, $id, $name, $itemDB, $dimDB, $dbName, $status);
  my (%itemNames);

  my ($db, $dsSchema, $dim, $noAliases) = @_;


  undef(%itemNames);

  $itemDB = KAPutil_get_dim_db_name($dim);

  #add base item names to hash
  if ($noAliases > 0)
  {
    $query = "SELECT ID, name FROM $dsSchema.$itemDB";
  }
  else
  {
    $query = "SELECT ID, IFNULL(alias, name) FROM $dsSchema.$itemDB";
  }
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  while (($id, $name) = $dbOutput->fetchrow_array)
  {
    $itemNames{$id} = $name;
  }

  return(%itemNames);
}



#-------------------------------------------------------------------------
#
# Get all of the aliases for the specified dimension in the data
# source and return them in a hash indexed by their base item's ID.
#

sub dsr_get_aliases_hash
{
  my ($query, $dbOutput, $id, $alias, $itemDB, $dimDB, $dbName, $status);
  my (%aliasNames);

  my ($db, $dsSchema, $dim) = @_;


  undef(%aliasNames);

  $itemDB = KAPutil_get_dim_db_name($dim);

  #add aliases to hash
  $query = "SELECT ID, alias FROM $dsSchema.$itemDB WHERE alias IS NOT NULL";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  while (($id, $alias) = $dbOutput->fetchrow_array)
  {
    $aliasNames{$id} = $alias;
  }

  return(%aliasNames);
}



#-------------------------------------------------------------------------
#
# Get all of the aliases for the specified dimension in the data
# source and return them in a hash indexed by their base item's name.
#

sub dsr_get_aliases_hash_by_name
{
  my ($query, $dbOutput, $name, $alias, $itemDB, $dimDB, $dbName, $status);
  my (%aliasNames);

  my ($db, $dsSchema, $dim) = @_;


  undef(%aliasNames);

  $itemDB = KAPutil_get_dim_db_name($dim);

  #add aliases to hash
  $query = "SELECT name, alias FROM $dsSchema.$itemDB WHERE alias IS NOT NULL";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  while (($name, $alias) = $dbOutput->fetchrow_array)
  {
    $aliasNames{$name} = $alias;
  }

  return(%aliasNames);
}


#-------------------------------------------------------------------------
#
# Get all of the measure names for the specified data source and return
# them in a hash indexed by their DSR IDs. We don't return measures that
# are currently being calculated, and if the undefinedAggRule flag is passed,
# we don't return base measures that have undefined aggregation rules
#

sub get_measure_name_hash
{
  my ($query, $dbOutput, $id, $name, $calculation, $lastCalc, $status);
  my ($prodAggRule, $geoAggRule, $timeAggRule);
  my (%val);

  my ($schema, $db, $undefinedAggRule) = @_;


  undef(%val);

  $query = "SELECT ID, name, calculation, prodAggRule, geoAggRule, timeAggRule, UNIX_TIMESTAMP(lastCalc) \
      FROM $schema.measures";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  while (($id, $name, $calculation, $prodAggRule, $geoAggRule, $timeAggRule, $lastCalc) = $dbOutput->fetchrow_array)
  {

    #make "None" be the same as undefined for agg rules
    if ($prodAggRule eq "None")
    {
      $prodAggRule = "";
    }
    if ($geoAggRule eq "None")
    {
      $geoAggRule = "";
    }
    if ($timeAggRule eq "None")
    {
      $timeAggRule = "";
    }

    #if we're a calculated measure and we aren't currently calculating
    if ((length($calculation) > 2) && ($lastCalc > 0))
    {
      $val{$id} = $name;
    }

    #else if we're a base measure and we care about agg rules
    elsif ((length($calculation) < 2) && ($undefinedAggRule == 1))
    {
      if ((length($prodAggRule) > 1) && (length($geoAggRule) > 1) &&
          (length($timeAggRule) > 1))
      {
        $val{$id} = $name;
      }
    }

    #else if we're a base measure and we don't care about agg rules
    elsif ((length($calculation) < 2) && ($undefinedAggRule < 1))
    {
      $val{$id} = $name;
    }
  }

  return(%val);
}



#-------------------------------------------------------------------------
#
# Get all of the measure IDs for the specified data source and return
# them in a hash indexed by their names.
#

sub get_measure_ID_hash
{
  my ($query, $dbOutput, $id, $name, $status);
  my (%val);

  my ($schema, $db) = @_;


  undef(%val);

  $query = "SELECT ID, name FROM $schema.measures";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  while (($id, $name) = $dbOutput->fetchrow_array)
  {
    $name = lc($name);
    $val{$name} = $id;
  }

  return(%val);
}



#-------------------------------------------------------------------------
#
# Get the formatting settings for a global time alias, and return them in
# a hash reference.
#

sub DSRutil_get_time_aliases_format
{
  my ($dsSchema, $query, $dbOutput, $durationFormat, $timeFormat, $overwrite);
  my ($status, $sqlDateFmt, $timeAlias);
  my (%timeFormatOpts);

  my ($db, $dsID) = @_;


  $dsSchema = "datasource_" . $dsID;
  undef(%timeFormatOpts);

  #get our time period global alias settings for the specified DS
  $query = "SELECT timeAlias FROM dataSources WHERE ID=$dsID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($timeAlias) = $dbOutput->fetchrow_array;

  #if this DS doesn't have any global time aliases defined, we're done
  if ($timeAlias =~ m/^(\d+)\|(\d+)\|(\d+)$/)
  {
    $durationFormat = $1;
    $timeFormat = $2;
    $overwrite = $3;
  }
  else
  {
    return;
  }

  #build our MySQL data format string based on user selection
  if ($timeFormat == 1)
  {
    $sqlDateFmt = "%c/%e/%Y";
  }
  elsif ($timeFormat == 2)
  {
    $sqlDateFmt = "%c/%e/%y";
  }
  elsif ($timeFormat == 3)
  {
    $sqlDateFmt = "%c/%e";
  }
  elsif ($timeFormat == 4)
  {
    $sqlDateFmt = "%b %e, %Y";
  }
  elsif ($timeFormat == 5)
  {
    $sqlDateFmt = "%M %e, %Y";
  }
  elsif ($timeFormat == 6)
  {
    $sqlDateFmt = "%e-%b-%Y";
  }
  elsif ($timeFormat == 7)
  {
    $sqlDateFmt = "%m/%d/%Y";
  }
  elsif ($timeFormat == 8)
  {
    $sqlDateFmt = "%m/%d/%y";
  }

  $timeFormatOpts{'durationFormat'} = $durationFormat;
  $timeFormatOpts{'overwrite'} = $overwrite;
  $timeFormatOpts{'timeFormat'} = $timeFormat;
  $timeFormatOpts{'sqlDateFmt'} = $sqlDateFmt;

  return(\%timeFormatOpts);
}


#-------------------------------------------------------------------------
#
# Build and put in place aliases for every time period in the specified
# data source
#

sub DSRutil_build_time_aliases
{
  my ($query, $dbOutput, $status, $dsSchema, $newAlias, $q_newAlias);
  my ($alias, $duration, $type, $endDate, $timeID, $sqlDateFmt, $timeAlias);
  my ($timeFormatOpts);

  my ($db, $dsID, $durationFormat, $timeFormat, $overwrite) = @_;


  $dsSchema = "datasource_" . $dsID;

  $timeFormatOpts = DSRutil_get_time_aliases_format($db, $dsID);
  $timeFormat = $timeFormatOpts->{'timeFormat'};
  $durationFormat = $timeFormatOpts->{'durationFormat'};
  $overwrite = $timeFormatOpts->{'overwrite'};
  $sqlDateFmt = $timeFormatOpts->{'sqlDateFmt'};

  #if this DS doesn't have any global time aliases defined, we're done
  if (length($timeFormat) < 1)
  {
    return;
  }

  #run through every time period in the DS
  $query = "SELECT ID, alias, duration, type, DATE_FORMAT(endDate, '$sqlDateFmt') \
      FROM $dsSchema.timeperiods";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  while (($timeID, $alias, $duration, $type, $endDate) = $dbOutput->fetchrow_array)
  {

    #skiop custom time periods
    if (($duration < 1) || ($type < 1))
    {
      next;
    }

    #if we're "n Weeks Ending" or "n Weeks"
    if (($durationFormat == 1) || ($durationFormat == 2))
    {
      $newAlias = "$duration ";

      if ($type == 10)
      {
        $newAlias .= "Year";
      }
      elsif ($type == 20)
      {
        $newAlias .= "Month";
      }
      elsif ($type == 30)
      {
        $newAlias .= "Week";
      }
      elsif ($type == 40)
      {
        $newAlias .= "Day";
      }

      #make the time period type plural, if needed
      if ($duration > 1)
      {
        $newAlias .= "s";
      }

      $newAlias .= " ";
    }

    #if we need to append an "Ending"
    if ($durationFormat == 1)
    {
      $newAlias .= "Ending ";
    }

    #handle "n WE " style
    if ($durationFormat == 3)
    {
      $newAlias = "$duration ";

      if ($type == 10)
      {
        $newAlias .= "YE ";
      }
      elsif ($type == 20)
      {
        $newAlias .= "ME ";
      }
      elsif ($type == 30)
      {
        $newAlias .= "WE ";
      }
      elsif ($type == 20)
      {
        $newAlias .= "DE ";
      }
    }

    #handle no duration style
    if ($durationFormat == 4)
    {
      $newAlias = "";
    }

    #end date goes on the end of the alias regardless of formatting
    $newAlias .= $endDate;

    $q_newAlias = $db->quote($newAlias);
    $query = "UPDATE $dsSchema.timeperiods SET alias=$q_newAlias WHERE ID=$timeID";

    #if overwrite is on, put the new alias in place no matter what
    if ($overwrite == 1)
    {
      $status = $db->do($query);
      KAPutil_handle_db_err($db, $status, $query);
    }

    #if overwrite is off but there isn't currently any alias for this time...
    elsif (length($alias) < 2)
    {
      $status = $db->do($query);
      KAPutil_handle_db_err($db, $status, $query);
    }
  }
}



#-------------------------------------------------------------------------


1;
