#!/usr/bin/perl


  #open source data file
  open(INPUT, "/data/nielsen/Beacon_Product_Ref_prdc_ref_1326157.txt");
  open(OUTPUT, ">/data/nielsen/rpt.txt");

  print OUTPUT "Super Category|Manufacturer|Brand|SKU Count\n";

  #burn header line
  $line = <INPUT>;

  #cycle through data file, collecting list of super cats and their brands
  $count = 0;
  while ($line = <INPUT>)
  {
    @colData = split('\|', $line);
    $superCat = $colData[2];
    $brandHigh = $colData[9];
    $manufacturer = $colData[11];

    $key = "$superCat|$manufacturer|$brandHigh";
    $dataHash{$key}++;
    $count++;

    if ($count % 1000 == 0)
    {
      print STDERR "$count...";
    }
  }

  foreach $key (sort keys %dataHash)
  {
    print OUTPUT "$key|$dataHash{$key}\n";
  }

  close(INPUT);
  close(OUTPUT);

#EOF
