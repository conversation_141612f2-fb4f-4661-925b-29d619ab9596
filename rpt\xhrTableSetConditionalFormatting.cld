#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DSRUtils;
use Lib::Reports;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $rptID = $q->param('rptID');
  $visID = $q->param('e');

  $condName = $q->param('cd');
  $condMeasID = $q->param('m');
  $the_rule = $q->param('r');
  $the_number = $q->param('n');
  $the_number2 = $q->param('nn');
  $condColor = $q->param('c');

  $db = KAPutil_connect_to_database();

  #make sure we have edit privs for this report
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this report.");
  }

  #get the element's current design string
  $query = "SELECT design FROM app.visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute();
  KAPutil_handle_db_err($db, $status, $query);
  ($design) = $dbOutput->fetchrow_array;

  if ($condMeasID eq "delete_this_item")
  {
    $the_name = $condName;
    $conditional_formatting = $condMeasID;
  }
  elsif ($condName eq "new_rule")
  {
    #split into array and loop searching for conditional formatting rules, if found plus 1 for the new rules name
    my @design_array = split(',', $design);
    $the_name_number = 1;
    foreach $a (@design_array)
    {
      if (index($a, "condFormat") != -1)
      {
        my @aa = split(':', $a);
        $new_a = @aa->[0];
        my @aaa = split('condFormat', $new_a);
        $the_num = @aaa->[1];
        if ($the_num >= $the_name_number)
        {
          $the_name_number = $the_num + 1;
        }
      }
    }

    $the_name = "condFormat$the_name_number";
    $combined_number = $the_number;
    if ($the_number2 ne "na")
    {
      $combined_number = "$combined_number $the_number2";
    }
    $conditional_formatting = "$condMeasID $the_rule $combined_number $condColor";
  }

  else
  {
    $the_name = $condName;
    $combined_number = $the_number;
    if ($the_number2 ne "na")
    {
      $combined_number = "$combined_number $the_number2";
    }
    $conditional_formatting = "$condMeasID $the_rule $combined_number $condColor";
  }

  if ($conditional_formatting eq "delete_this_item")
  {
    $design = reports_remove_style($design, $the_name);
  }
  else
  {
    $design = reports_set_style($design, $the_name, $conditional_formatting);
  }

  $q_design = $db->quote($design);

  $query = "UPDATE app.visuals SET design = $q_design WHERE ID=$visID";
  $db->do($query);


#EOF
