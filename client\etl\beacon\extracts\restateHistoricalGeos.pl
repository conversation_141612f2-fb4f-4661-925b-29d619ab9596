#!/usr/bin/perl


use Text::CSV_XS;

#
# Global variables controlling how large we're allowed to scale
#

#NB: On 0.25 IOPS endurance, 2 seems like the best balance of CPU/IO use
$MAXPROCS = 8;


%restateHash = (
"ALBSCO Acme Div Rem" => "ALBSCO Acme Rem",
"ALBSCO Acme Div TA" => "ALBSCO Acme TA",
"ALBSCO Acme Div xAOC Rem" => "ALBSCO Acme xAOC Rem",
"ALBSCO Eastern Div Rem" => "ALBSCO Eastern Rem",
"ALBSCO Eastern Div TA" => "ALBSCO Eastern TA",
"ALBSCO Eastern Div xAOC Rem" => "ALBSCO Eastern xAOC Rem",
);



#---------------------------------------------------------------------------
#
# Output log info to console and log file
#

sub DBG
{
  my ($date);

  my ($str) = @_;

  $date = localtime();
  print("$date: $str\n");
}



#---------------------------------------------------------------------------
#
#

sub restate_history_file
{

  my ($fileStub) = @_;

  $zipFilename = $fileStub . ".zip";
  $factsFilename = $fileStub . ".csv";
  $lookupFilename = $fileStub . "-prodlookup.csv";
  $tempFilename = $fileStub . "-work.csv";

  DBG("Restating geographies for $fileStub ($fileCount/$totalFiles)");

  #get the read/write times for the file
  #NB: we want to set our finished file to have a matching modify time so
  #    we don't accidentally fire off a bunch of Data Prep scheduler processes
  #    for no reason.
  ($zipReadTime, $zipWriteTime) = (stat($zipFilename))[8,9];

  #uncompress the existing facts data
  DBG("Uncompressing $zipFilename");
  `/usr/bin/unzip -o $zipFilename $factsFilename $lookupFilename`;

  #restate the geographies in the file
  DBG("Restating geographies in $factsFilename");
  open(INPUT, "/data2/hold/$factsFilename") or die("Unable to open $factsFilename, $!");
  open(OUTPUT, ">/data2/hold/$tempFilename") or die("Unable to open $tempFilename, $!");

  while ($line = <INPUT>)
  {
    $csv->parse($line);
    @columns = $csv->fields();


    if (length($restateHash{$columns[3]}) > 1)
    {
      $columns[3] = $restateHash{$columns[3]};
      $csv->combine(@columns);
      $line = $csv->string;
    }


=pod
    #normally we'd apply a hash-based map here, but this particular
    #restatement is easier to deal with by using heuristics
    if ($columns[3] =~ m/^ALB\/SFY (.*)$/)
    {
      $columns[3] = "ALBSCO $1";
      $csv->combine(@columns);
      $line = $csv->string;
    }
    if ($columns[3] =~ m/^SUPERVALU (.*)$/)
    {
      $columns[3] = "LEGACY SV $1";
      $csv->combine(@columns);
      $line = $csv->string;
    }
=cut

=pod
    if ($columns[3] =~ m/^(LEGACY SV .*) Div (.*)$/)
    {
      $columns[3] = "$1 Division $2";
      $csv->combine(@columns);
      $line = $csv->string;
    }

    if ($columns[3] =~ m/^CVS (Northeast|Total) (Comp Mkt)$/)
    {
      $columns[3] = "CVS $1 Corp $2";
      $csv->combine(@columns);
      $line = $csv->string;
    }
    elsif ($columns[3] =~ m/^CVS (Northeast|Total) (xAOC Rem)$/)
    {
      $columns[3] = "CVS $1 Corp $2";
      $csv->combine(@columns);
      $line = $csv->string;
    }
    elsif ($columns[3] =~ m/^CVS (Northeast|Total) (Rem)$/)
    {
      $columns[3] = "CVS $1 Corp $2";
      $csv->combine(@columns);
      $line = $csv->string;
    }
    elsif ($columns[3] =~ m/^CVS (Northeast|Total) (TA)$/)
    {
      $columns[3] = "CVS $1 Corp $2";
      $csv->combine(@columns);
      $line = $csv->string;
    }
=cut

    print OUTPUT $line;
  }

  close(INPUT);
  close(OUTPUT);
  unlink("/data2/hold/$factsFilename");
  rename("/data2/hold/$tempFilename", "/data2/hold/$factsFilename");

  #compress the updated history file and new product characteristics file
  DBG("Compressing updated history file for $fileStub");
  `/usr/bin/zip $zipFilename-1 $factsFilename $lookupFilename`;

  #delete our temporary working files
  unlink("/data2/hold/$factsFilename");
  unlink("/data2/hold/$lookupFilename");

  #replace the old compressed history archive
  rename("/data2/hold/$zipFilename-1", "/data2/hold/$zipFilename");

  #reset the file's access/modify times
#  utime($zipReadTime, $zipWriteTime, "/data2/hold/$zipFilename");
}



#---------------------------------------------------------------------------

  DBG("Starting update load run");

  # Clean up from any previous runs
  $csv = Text::CSV_XS->new( {binary => 1, eol => $/} );

  #get a list of all of our historical data sets
  undef(@catFiles);
  opendir(DIRHANDLE, "/data2/hold");
  while (defined($filename = readdir(DIRHANDLE)))
  {
    if (($filename =~ m/^(.*)\.zip$/i) && (!($filename =~ m/update\.zip$/i)))
    {
      push(@catFiles, $1);
    }
  }

  # Run through each of the category files, restating geography values, and
  # recompressing.

  #cycle through each category we have an update for
  $fileCount = 0;
  $totalFiles = scalar(@catFiles);
  $processCount = 0;

  chdir("/data2/hold");
  foreach $fileStub (@catFiles)
  {

    $fileCount++;

    #if we haven't hit our maximum process limit
    if ($processCount < $MAXPROCS)
    {

      #fire off the child process
      if ($pid = fork())
      {
        #parent process

        #increment count of active processes
        $processCount++;
      }

      #else we're the child process
      else
      {
        restate_history_file($fileStub);
        exit;
      }
    }

    #wait here until an empty process slot opens up
    if ($processCount >= $MAXPROCS)
    {
      wait();
      $processCount--;
    }
  }

  #wait here until the last rewrite processes finish
  while ($processCount > 0)
  {
    wait();
    $processCount--;
  }

  DBG("Done");


#EOF
