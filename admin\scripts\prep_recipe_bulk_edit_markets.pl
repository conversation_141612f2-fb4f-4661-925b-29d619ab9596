#!/usr/bin/perl

use lib "/opt/apache/app/";

use DBI;
use Text::CSV;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::PrepRecipes;
use Lib::PrepUtils;
use Lib::WebUtils;


  $prepDB = PrepUtils_connect_to_database();

  #grab the ID and name of every data flow on the system using AOD extract
  #and where the flow belongs to a RW user and isn't in a list of data
  #sources that <PERSON> wants us to leave alone
  $query = "SELECT ID, name FROM prep.flows \
      WHERE sourceInfo LIKE 'FTP=nielsen%' AND userID IN (20,37,55,68) AND dsID NOT IN (3241, 3640, 3242, 4677, 4731, 4679, 4680, 3556, 4673)";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  while (($flowID, $flowName) = $dbOutput->fetchrow_array)
  {
    $flowNameHash{$flowID} = $flowName;
  }

  $csv = Text::CSV->new( {binary => 1} );

  #cycle through every data flow's recipe, looking for a market trim step
  foreach $flowID (keys %flowNameHash)
  {
    $query = "SELECT step, action FROM prep.recipes \
        WHERE flowID=$flowID AND action LIKE 'TRANS-COL-TRIM-DATA|COL=Market Display Name|%'";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;

    while (($stepID, $action) = $dbOutput->fetchrow_array)
    {

      print("$flowNameHash{$flowID}\n---------------------------\n");
      print("$action\n\n\n");

      prep_recipe_step_remove($prepDB, $flowID, $stepID);
print STDERR "prep_recipe_step_remove($prepDB, $flowID, $stepID)\n";
    }
  }


#EOF
