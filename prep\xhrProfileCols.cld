#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::WebUtils;


#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $flowID = $q->param('f');
  $jobID = $q->param('j');

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #make sure we have read privs for this data flow
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to analyze this data flow.");
  }

  $flowName = prep_flow_id_to_name($prepDB, $flowID);

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterTableName = $jobID . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #get the row count for the job (used for lots of stats calculations below)
  $query = "SELECT rowCount FROM prep.jobs WHERE ID=$jobID";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($rowCount) = $dbOutput->fetchrow_array;

  print <<END_HTML;
<DIV CLASS="modal-dialog modal-lg">
  <DIV CLASS="modal-content">

    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Column Profile</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">
      <DIV STYLE="height:400px; overflow:auto;">

        <DIV CLASS="table-responsive">
          <TABLE CLASS="table table-sm table-hover">
            <THEAD><TR>
              <TH>Name</TH>
              <TH>Empties</TH>
              <TH>Valid</TH>
              <TH>Invalid</TH>
              <TH>Unique Values</TH>
            </TR></THEAD>
END_HTML

  $query = "SELECT ID, name FROM $masterColTable \
      WHERE type='product' OR type='geography' OR type='time' OR type='upc' \
      ORDER BY name";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;

  while (($colID, $colName) = $dbOutput->fetchrow_array)
  {
    $query = "SELECT COUNT(*) FROM $masterTable WHERE ISNULL(column_$colID)";
    $stat_output = $prepDB->prepare($query);
    $stat_output->execute;
    ($emptyCount) = $stat_output->fetchrow_array;

    $query = "SELECT COUNT(column_$colID) as count FROM $masterTable \
        GROUP BY column_$colID";
    $stat_output = $prepDB->prepare($query);
    $uniques = $stat_output->execute;

    $query = "SELECT COUNT(*) WHERE valid=1 FROM $masterTable";
    $stat_output = $prepDB->prepare($query);
    $stat_output->execute;
    ($invalid) = $stat_output->fetchrow_array;
    $valid = $rowCount - $invalid;

    print <<END_HTML;
            <TR>
              <TD>$colName</TD>
              <TD>$emptyCount</TD>
              <TD>$valid</TD>
              <TD>$invalid</TD>
              <TD>$uniques</TD>
            </TR>
END_HTML
  }

  print <<END_HTML;
          </TABLE>
        </DIV>

        <P>
        <DIV CLASS="table-responsive">
          <TABLE CLASS="table table-sm table-hover">
            <THEAD><TR>
              <TH>Name</TH>
              <TH>Empties</TH>
              <TH>Valid</TH>
              <TH>Invalid</TH>
              <TH>Unique Values</TH>
            </TR></THEAD>
END_HTML

  $query = "SELECT ID, name FROM $masterColTable \
      WHERE type='pseg' OR type='gseg' OR type='tseg' \
      ORDER BY name";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;

  while (($colID, $colName) = $dbOutput->fetchrow_array)
  {
    $query = "SELECT COUNT(*) FROM $masterTable WHERE ISNULL(column_$colID)";
    $stat_output = $prepDB->prepare($query);
    $stat_output->execute;
    ($emptyCount) = $stat_output->fetchrow_array;

    $query = "SELECT COUNT(*) WHERE valid=1 FROM $masterTable";
    $stat_output = $prepDB->prepare($query);
    $stat_output->execute;
    ($invalid) = $stat_output->fetchrow_array;
    $valid = $rowCount - $invalid;

    print <<END_HTML;
            <TR>
              <TD>$colName</TD>
              <TD>$emptyCount</TD>
              <TD>$valid</TD>
              <TD>$invalid</TD>
              <TD>$uniques</TD>
            </TR>
END_HTML
  }

  print <<END_HTML;
          </TABLE>
        </DIV>

        <P>
        <DIV CLASS="table-responsive">
          <TABLE CLASS="table table-sm table-hover">
            <THEAD><TR>
            <TH>Name</TH>
            <TH>Empties</TH>
            <TH>Valid</TH>
            <TH>Invalid</TH>
            <TH>Min</TH>
            <TH>Average</TH>
            <TH>Max</TH>
            <TH>Std Dev</TH>
          </TR></THEAD>
END_HTML

  $query = "SELECT ID, name FROM $masterColTable WHERE type='measure' ORDER BY name";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;

  while (($colID, $colName) = $dbOutput->fetchrow_array)
  {
    $col = "column_$colID";

    $query = "SELECT COUNT(*) FROM $masterTable WHERE ISNULL($col)";
    $stat_output = $prepDB->prepare($query);
    $stat_output->execute;
    ($emptyCount) = $stat_output->fetchrow_array;

    $query = "SELECT MIN($col), MAX($col), AVG($col), STD($col) FROM $masterTable;";
    $stat_output = $prepDB->prepare($query);
    $stat_output->execute;
    ($min, $max, $avg, $stddev) = $stat_output->fetchrow_array;
    $min = sprintf("%.2f", $min);
    $max = sprintf("%.2f", $max);
    $avg = sprintf("%.2f", $avg);
    $stddev = sprintf("%.2f", $stddev);

    $query = "SELECT COUNT(*) WHERE valid=1 FROM $masterTable";
    $stat_output = $prepDB->prepare($query);
    $stat_output->execute;
    ($invalid) = $stat_output->fetchrow_array;
    $valid = $rowCount - $invalid;

    print <<END_HTML;
          <TR>
            <TD>$colName</TD>
            <TD>$emptyCount</TD>
            <TD>$valid</TD>
            <TD>$invalid</TD>
            <TD>$min</TD>
            <TD>$avg</TD>
            <TD>$max</TD>
            <TD>$stddev</TD>
          </TR>
END_HTML
  }

  print <<END_HTML;
        </TABLE>
      </DIV>

    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-primary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-check-lg"></I> Done</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML

  $query = "UPDATE prep.jobs SET lastAction=NOW() WHERE ID=$jobID";
  $prepDB->do($query);

  prep_audit($prepDB, $userID, "Profiled columns", $flowID);
  utils_slack("PREP: $first $last profiled columns in $flowName");

#EOF
