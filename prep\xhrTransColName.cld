#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepRecipes;
use Lib::PrepUtils;
use Lib::WebUtils;


#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $flowID = $q->param('f');
  $jobID = $q->param('j');
  $colID = $q->param('col');
  $action = $q->param('a');
  $newColName = $q->param('n');

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #make sure we have write privs for this data flow
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this data flow.");
  }

  $flowName = prep_flow_id_to_name($prepDB, $flowID);

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #build name of column containing the text
  $column = "column_" . $colID;

  #get the current colun name
  $query = "SELECT name FROM $masterColTable WHERE ID=$colID";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($colName) = $dbOutput->fetchrow_array;


  ########################################################################
  #
  # This code block is called on submit to change the column's name
  #

  if ($action eq "apply")
  {

    $step = "TRANS-COL-NAME|COL=$colName|NAME=$newColName";

    $stepID = prep_recipe_add_step($prepDB, $flowID, $step);

    $status = prep_recipe_trans_col_name($prepDB, $flowID, $jobID, $stepID);

    prep_audit($prepDB, $userID, "Renamed column $colName to $newColName", $flowID);
    utils_slack("PREP: $first $last renamed column $colName to $newColName in $flowName");

    exit;
  }


  #########################################################################

  print <<END_HTML;
<SCRIPT>
function submitForm()
{
  let colName = document.getElementById('colName').value;
  let url = "xhrTransColName.cld?f=$flowID&j=$jobID&col=$colID&a=apply&n=" + colName;

  \$("#btn-submit").prop('disabled', true);
  \$("#btn-submit").text("Please Wait...");

  \$('#modal-trans-col-name').modal('hide');

  \$.get(url, function(data, status)
  {
    location.href = "flowViewData.cld?f=$flowID&j=$jobID";
  });
}
</SCRIPT>


<DIV CLASS="modal-dialog">
  <DIV CLASS="modal-content">

    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Rename Column</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">
      <LABEL FOR="colName">Column Name:</LABEL>
      <INPUT TYPE="text" CLASS="form-control" ID="colName" VALUE="$colName" minlength=1 required>
    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="button" ID="btn-submit" onClick="submitForm()"><I CLASS="bi bi-pencil-square"></I> Apply</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML

#EOF
