#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::Reports;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $rptID = $q->param('rptID');
  $visID = $q->param('v');
  $caption = $q->param('caption');
  $subcaption = $q->param('subcaption');
  $captionFontColor = $q->param('captionFontColor');
  $captionAlignment = $q->param('captionAlignment');
  $captionFontSize = $q->param('captionFontSize');
  $captionFont = $q->param('captionFont');
  $xAxisName = $q->param('x');
  $yAxisName = $q->param('y');
  $xAxisNameFontSize = $q->param('xAxisNameFontSize');
  $yAxisNameFontSize = $q->param('yAxisNameFontSize');
  $action = $q->param('action');

  $captionFontColor = "#" . $captionFontColor;

  $db = KAPutil_connect_to_database();

  #make sure we have edit privs for this report
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this report.");
  }

  #get the chart title details from the database
  $query = "SELECT design FROM visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($graphDesign) = $dbOutput->fetchrow_array;


  ########################################################################
  #
  # This code block is called on submit to save the changes
  #

  #if we're being called to save updated chart titles
  if ($action eq "edit")
  {

    if (length($caption) > 0)
    {
      $graphDesign = reports_set_captions($graphDesign, "caption", $caption);
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "caption");
    }

    if (length($subcaption) > 0)
    {
      $graphDesign = reports_set_captions($graphDesign, "subcaption", $subcaption);
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "subcaption");
    }

    if (lc($captionFontColor) ne reports_chart_design_default("captionFontColor"))
    {
      $graphDesign = reports_set_style($graphDesign, "captionFontColor", $captionFontColor);
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "captionFontColor");
    }

    if ($captionAlignment ne reports_chart_design_default("captionAlignment"))
    {
      $graphDesign = reports_set_style($graphDesign, "captionAlignment", $captionAlignment);
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "captionAlignment");
    }

    if ($captionFontSize ne reports_chart_design_default("captionFontSize"))
    {
      $graphDesign = reports_set_style($graphDesign, "captionFontSize", $captionFontSize);
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "captionFontSize");
    }

    if ($captionFont eq "Helvetica")
    {
      $graphDesign = reports_remove_style($graphDesign, "captionFont");
    }
    else
    {
      $graphDesign = reports_set_style($graphDesign, "captionFont", $captionFont);
    }

    if (length($xAxisName) > 0)
    {
      $graphDesign = reports_set_style($graphDesign, "xAxisName", "\"$xAxisName\"");
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "xAxisName");
    }

    if (length($yAxisName) > 0)
    {
      $graphDesign = reports_set_style($graphDesign, "yAxisName", "\"$yAxisName\"");
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "yAxisName");
    }

    if ($xAxisNameFontSize ne reports_chart_design_default("xAxisNameFontSize"))
    {
      $graphDesign = reports_set_style($graphDesign, "xAxisNameFontSize", $xAxisNameFontSize);
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "xAxisNameFontSize");
    }

    if ($yAxisNameFontSize ne reports_chart_design_default("yAxisNameFontSize"))
    {
      $graphDesign = reports_set_style($graphDesign, "yAxisNameFontSize", $yAxisNameFontSize);
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "yAxisNameFontSize");
    }

    $q_graphDesign = $db->quote($graphDesign);
    $query = "UPDATE visuals SET design = $q_graphDesign WHERE ID=$visID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    $cubeName = cube_id_to_name($db, $rptID);
    $dsID = cube_get_ds_id($db, $rptID);
    $dsName = ds_id_to_name($db, $dsID);

    utils_audit($db, $userID, "Changed chart titles", $dsID, $rptID, 0);
    $activity = "$first $last changed chart titles for $cubeName in $dsName";
    utils_slack($activity);

    exit;
  }


  #########################################################################
  #
  # Everything after this point is called to display the chart layout dialog
  #

  #extract graph captions from design string
  if ($graphDesign =~ m/,caption:"(.*?)",/)
  {
    $caption = $1;
  }
  if ($graphDesign =~ m/,subcaption:"(.*?)",/)
  {
    $subcaption = $1;
  }
  $captionFontColor = reports_get_style($graphDesign, "captionFontColor");
  $captionAlignment = reports_get_style($graphDesign, "captionAlignment");
  $captionFontSize = reports_get_style($graphDesign, "captionFontSize");
  $captionFont = reports_get_style($graphDesign, "captionFont");

  #set appropriate defaults
  if (length($captionFontColor) < 7)
  {
    $captionFontColor = "#333333";
  }
  if (length($captionAlignment) < 2)
  {
    $captionAlignment = "center";
  }
  if ($captionFontSize < 3)
  {
    $captionFontSize = "14";
  }
  if (length($captionFont) < 3)
  {
    $captionFont = "Helvetica";
  }

  if ($graphDesign =~ m/,xAxisName:"(.*?)",/)
  {
    $xAxisName = $1;
  }
  if ($graphDesign =~ m/,yAxisName:"(.*?)",/)
  {
    $yAxisName = $1;
  }
  $xAxisNameFontSize = reports_get_style($graphDesign, "xAxisNameFontSize");
  $yAxisNameFontSize = reports_get_style($graphDesign, "yAxisNameFontSize");

  if ($xAxisNameFontSize < 3)
  {
    $xAxisNameFontSize = "10";
  }
  if ($yAxisNameFontSize < 3)
  {
    $yAxisNameFontSize = "10";
  }

  print <<END_HTML;
<SCRIPT>
captionAlignment = "$captionAlignment";

function submitForm()
{
  let caption = document.getElementById('caption').value;
  let subcaption = document.getElementById('subcaption').value;
  let captionFontColor = document.getElementById('captionFontColor').value;
  let captionFontSize = document.getElementById('captionFontSize').value;
  let captionFont = document.getElementById('captionFont').value;
  let xAxis = document.getElementById('xAxisTitle').value;
  let xAxisNameFontSize = document.getElementById('xAxisNameFontSize').value;
  let yAxis = document.getElementById('yAxisTitle').value;
  let yAxisNameFontSize = document.getElementById('yAxisNameFontSize').value;

  //knock # off of color strings
  captionFontColor = captionFontColor.substr(1);

  let url = "xhrChartTitles?rptID=$rptID&v=$visID&caption=" + caption +
      "&subcaption=" + subcaption + "&captionFontColor=" + captionFontColor +
      "&captionAlignment=" + captionAlignment +
      "&captionFontSize=" + captionFontSize + "&captionFont=" + captionFont +
      "&x=" + xAxis + "&xAxisNameFontSize=" + xAxisNameFontSize +
      "&y=" + yAxis + "&yAxisNameFontSize=" + yAxisNameFontSize + "&action=edit";
  url = encodeURI(url);

  \$.get(url, function(data, status)
  {
    location.href = "display.cld?rpt=$rptID&v=$visID";
  });
}



function alignUI(alignment)
{
  let titleAlignLeft = document.getElementById('titleAlignLeft');
  let titleAlignCenter = document.getElementById('titleAlignCenter');
  let titleAlignRight = document.getElementById('titleAlignRight');

  if (alignment == "left")
  {
    titleAlignLeft.style.color = "blue";
    titleAlignCenter.style.color = "darkgray";
    titleAlignRight.style.color = "darkgray";
  }
  else if (alignment == "center")
  {
    titleAlignLeft.style.color = "darkgray";
    titleAlignCenter.style.color = "blue";
    titleAlignRight.style.color = "darkgray";
  }
  else if (alignment == "right")
  {
    titleAlignLeft.style.color = "darkgray";
    titleAlignCenter.style.color = "darkgray";
    titleAlignRight.style.color = "blue";
  }

  captionAlignment = alignment;
}
</SCRIPT>

<DIV CLASS="modal-dialog" STYLE="overflow-y: initial !important;">
  <DIV CLASS="modal-content">
    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Chart Titles</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">
      <FORM>
      <TABLE CLASS="mx-auto">
        <TR>
          <TD STYLE="text-align:right;">Title Text:&nbsp;</TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="text" NAME="caption" ID="caption" MAXLENGTH="128" STYLE="width:300px;"  VALUE="$caption">
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Color:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="color" NAME="captionFontColor" ID="captionFontColor" VALUE="$captionFontColor" STYLE="width:50px;"></DIV>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">Alignment:&nbsp;</TD>
          <TD STYLE="text-align:left;">
            <SPAN ID="titleAlignLeft" CLASS="bi bi-text-left" STYLE="font-size:24px; color:darkgray;" onClick="alignUI('left');"></SPAN>
            &nbsp;
            <SPAN ID="titleAlignCenter" CLASS="bi bi-text-center" STYLE="font-size:24px; color:darkgray;" onClick="alignUI('center');"></SPAN>
            &nbsp;
            <SPAN ID="titleAlignRight" CLASS="bi bi-text-right" STYLE="font-size:24px; color:darkgray;" onClick="alignUI('right');"></SPAN>
            <SCRIPT>
              alignUI("$captionAlignment");
            </SCRIPT>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Text size:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="number" NAME="captionFontSize" ID="captionFontSize" STYLE="width:75px;" VALUE="$captionFontSize" min=3>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Font family:&nbsp;
          </TD>
          <TD>
            <SELECT CLASS="form-select" NAME="captionFont" ID="captionFont">
              <OPTION VALUE="Arial">Arial</OPTION>
              <OPTION VALUE="Arial Black">Arial Black</OPTION>
              <OPTION VALUE="Comic Sans MS">Comic Sans MS</OPTION>
              <OPTION VALUE="Courier New">Courier New</OPTION>
              <OPTION VALUE="Helvetica">Helvetica</OPTION>
              <OPTION VALUE="Impact">Impact</OPTION>
              <OPTION VALUE="Tahoma">Tahoma</OPTION>
              <OPTION VALUE="Times New Roman">Times New Roman</OPTION>
              <OPTION VALUE="Verdana">Verdana</OPTION>
            </SELECT>
            <SCRIPT>
              \$("select#captionFont").val("$captionFont");
            </SCRIPT>
          </TD>
        </TR>

        <TR>
          <TD>&nbsp;</TD><TD>&nbsp;</TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">Subtitle Text:&nbsp;</TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="text" NAME="subcaption" ID="subcaption" MAXLENGTH="128" STYLE="width:300px;"  VALUE="$subcaption">
          </TD>
        </TR>

        <TR>
          <TD>&nbsp;</TD><TD>&nbsp;</TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">X-Axis Title:&nbsp;</TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="text" NAME="xAxisTitle" ID="xAxisTitle" MAXLENGTH="128" VALUE="$xAxisName">
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            X-axis text size:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="number" NAME="xAxisNameFontSize" ID="xAxisNameFontSize" STYLE="width:75px;" VALUE="$xAxisNameFontSize" min=3>
          </TD>
        </TR>

        <TR>
          <TD>&nbsp;</TD><TD>&nbsp;</TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">Y-Axis Title:&nbsp;</TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="text" NAME="yAxisTitle" ID="yAxisTitle" MAXLENGTH="128" VALUE="$yAxisName">
          </TD>
        </TR>
        <TR>
          <TD STYLE="text-align:right;">
            Y-axis text size:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="number" NAME="yAxisNameFontSize" ID="yAxisNameFontSize" STYLE="width:75px;" VALUE="$yAxisNameFontSize" min=3>
          </TD>
        </TR>
      </TABLE>
      </FORM>

    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="submitForm()"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML

#EOF
