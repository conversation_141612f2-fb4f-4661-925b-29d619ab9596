#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::DataSources;
use Lib::AInsights::AInsights;
use Lib::AInsights::Utils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  #output Content-type header
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  #output HTML
  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: AInsights</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<SCRIPT SRC="/fusioncharts-3.18.0/js/fusioncharts.js"></SCRIPT>
<SCRIPT SRC="/fusioncharts-3.18.0/js/themes/fusioncharts.theme.zune.js"></SCRIPT>

<SCRIPT>
let vpHeight = window.innerHeight - 50;
if (vpHeight < 400)
{
  vpHeight = 400;
}
</SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">AInsights</A></LI>
    <LI CLASS="breadcrumb-item active">$modelName</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $priceModelID = $q->param('pm');
  $geoID = $q->param('g');

  #connect to the database
  $db = KAPutil_connect_to_database();

  #if the model is currently being rebuilt
  $query = "SELECT PID FROM app.jobs \
      WHERE analyticsID=$priceModelID AND operation='ANALYTICS-PRICE'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($pid) = $dbOutput->fetchrow_array;
  if ($pid > 0)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: /app/analytics/pricing/modelRefresh.cld?ds=$dsID&pm=$priceModelID\n\n");
    exit;
  }

  $modelName = AInsights_ID_to_name($db, $priceModelID);
  $dsName = ds_id_to_name($db, $dsID);
  $dsID = AInsights_get_dsID($db, $priceModelID);

  $dsSchema = "datasource_" . $dsID;

  print_html_header();

  #make sure we have at least read privs for this pricing model
  $privs = AInsights_rights($db, $userID, $priceModelID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to view this pricing model.");
  }

  %geoNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "g");

  AInsights_Utils_initialize_constants($priceModelID);

  $ownBrandID = AInsights_Utils_get_own_brand_id($db, $priceModelID);
  $brandSegID = AInsights_Utils_get_brand_seg_id($db, $priceModelID);
  @geoIDs = AInsights_Utils_get_model_geo_ids($db, $priceModelID);
  $HR_endDate = AInsights_Utils_get_most_recent_human_readable_end_date($db, $dsSchema);

  %brandNameHash = DSRseg_get_segments_hash($db, $dsSchema, "p", $brandSegID);
  $ownBrandName = $brandNameHash{$ownBrandID};

  #if we weren't passed a geography to display
  if ($geoID < 1)
  {

    #see if we have one to try in the analyst's cookie
    $geoID = $session->param("priceModelGeoSelection.$priceModelID");
  }
  else
  {
    $session->param("priceModelGeoSelection.$priceModelID", "$geoID");
  }

  #output off-canvas data selector
  print <<END_HTML;
  <DIV CLASS="offcanvas offcanvas-start" TABINDEX="-1" ID="offcanvas-data-selector">
  <DIV CLASS="offcanvas-header">
    <H5 CLASS="offcanvas-title" ID="offcanvas-label-data-selector"></H5>
    <BUTTON TYPE="button" CLASS="btn-close text-reset" data-bs-dismiss="offcanvas"></BUTTON>
  </DIV>
  <DIV CLASS="offcanvas-body">
    <DIV>
      <DIV CLASS="card">
        <DIV CLASS="card-header">Focus Geography</DIV>
        <DIV CLASS="card-body">
         <DIV CLASS="list-group">
           <A HREF="overview.cld?pm=$priceModelID&g=$availableGeoID" CLASS="list-group-item list-group-item-action active"><B>Overview</B></A>
END_HTML

  #determine which geographies didn't distribute our brand in the past 52 wks
  $query = "SELECT geographyID FROM $dsSchema.$AInsightsBrandTable \
      WHERE brandID=$ownBrandID AND ISNULL(avgDist52)";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  while (($id) = $dbOutput->fetchrow_array)
  {
    $noDistGeosHash{$id} = 1;
  }

  foreach $availableGeoID (@geoIDs)
  {
    $htmlListClass = "";
    if ($noDistGeosHash{$availableGeoID} > 0)
    {
      $htmlListClass = "list-group-item-secondary";
    }

    print <<END_HTML;
           <A HREF="insights.cld?pm=$priceModelID&g=$availableGeoID" CLASS="list-group-item list-group-item-action $htmlListClass">$geoNameHash{$availableGeoID}</A>
END_HTML
  }

  print <<END_HTML;
          </DIV>
        </DIV>
      </DIV>

    </DIV>
  </DIV>
</DIV>
END_HTML

  print <<END_HTML;
<DIV CLASS="container-fluid">

  <DIV CLASS="row">
    <DIV CLASS="col">

      <H3><A HREF="#" CLASS="text-decoration-none" data-bs-toggle="offcanvas" data-bs-target="#offcanvas-data-selector">Overview of $ownBrandName</A></H3>
      <H6>52 Weeks Ending $HR_endDate</H6>
      <P>&nbsp;</P>

    </DIV>
  </DIV>

  <DIV CLASS="row">
    <DIV CLASS="col">
END_HTML

  $units = AInsights_Utils_get_brand_calc_value($db, $dsSchema, 'units', $ownBrandID, 0);
  $unitsChg = AInsights_Utils_get_brand_calc_value($db, $dsSchema, 'units_chg', $ownBrandID, 0);
  $unitsPctChg = AInsights_Utils_get_brand_calc_value($db, $dsSchema, 'units_pct_chg', $ownBrandID, 0);

  $dispUnits = AInsights_Utils_html_format_number($units, 0, 0, 1);
  $dispUnitsChg = AInsights_Utils_html_format_number($unitsChg, 0, 0, 1);
  $dispUnitsPctChg = AInsights_Utils_html_format_number($unitsPctChg, 1, 0, 1);
  $dispUnitsPctChgBg = AInsights_Utils_get_kpi_html_bgcolor($unitsPctChg);

  $dollars = AInsights_Utils_get_brand_calc_value($db, $dsSchema, 'dollars', $ownBrandID, 0);
  $dollarsChg = AInsights_Utils_get_brand_calc_value($db, $dsSchema, 'dollars_chg', $ownBrandID, 0);
  $dollarsPctChg = AInsights_Utils_get_brand_calc_value($db, $dsSchema, 'dollars_pct_chg', $ownBrandID, 0);

  $dispDollars = AInsights_Utils_html_format_currency_kpi($dollars);
  $dispDollarsChg = AInsights_Utils_html_format_currency_kpi($dollarsChg);
  $dispDollarsPctChg = AInsights_Utils_html_format_number($dollarsPctChg, 1, 0, 1);
  $dispDollarsPctChgBg = AInsights_Utils_get_kpi_html_bgcolor($dollarsPctChg);

  $promoUnits = AInsights_Utils_get_brand_calc_value($db, $dsSchema, 'promounits', $ownBrandID, 0);
  $promoUnitsChg = AInsights_Utils_get_brand_calc_value($db, $dsSchema, 'promounits_chg', $ownBrandID, 0);
  $promoUnitsPctChg = AInsights_Utils_get_brand_calc_value($db, $dsSchema, 'promounits_pct_chg', $ownBrandID, 0);

  $dispPromoUnits = AInsights_Utils_html_format_number($promoUnits, 0, 0, 1);
  $dispPromoUnitsChg = AInsights_Utils_html_format_number($promoUnitsChg, 0, 0, 1);
  $dispPromoUnitsPctChg = AInsights_Utils_html_format_number($promoUnitsPctChg, 1, 0, 1);
  $dispPromoUnitsPctChgBg = AInsights_Utils_get_kpi_html_bgcolor($promoUnitsPctChg);

  $nonPromoUnits = AInsights_Utils_get_brand_calc_value($db, $dsSchema, 'nonpromounits', $ownBrandID, 0);
  $nonPromoUnitsChg = AInsights_Utils_get_brand_calc_value($db, $dsSchema, 'nonpromounits_chg', $ownBrandID, 0);
  $nonPromoUnitsPctChg = AInsights_Utils_get_brand_calc_value($db, $dsSchema, 'nonpromounits_pct_chg', $ownBrandID, 0);

  $dispNonPromoUnits = AInsights_Utils_html_format_number($nonPromoUnits, 0, 0, 1);
  $dispNonPromoUnitsChg = AInsights_Utils_html_format_number($nonPromoUnitsChg, 0, 0, 1);
  $dispNonPromoUnitsPctChg = AInsights_Utils_html_format_number($nonPromoUnitsPctChg, 1, 0, 1);
  $dispNonPromoUnitsPctChgBg = AInsights_Utils_get_kpi_html_bgcolor($nonPromoUnitsPctChg);

  $dist = AInsights_Utils_get_brand_calc_value($db, $dsSchema, 'dist', $ownBrandID, 0);
  $distChg = AInsights_Utils_get_brand_calc_value($db, $dsSchema, 'dist_chg', $ownBrandID, 0);
  $distPctChg = AInsights_Utils_get_brand_calc_value($db, $dsSchema, 'dist_pct_chg', $ownBrandID, 0);

  $dispDist = AInsights_Utils_html_format_number($dist, 1, 0, 1);
  $dispDistChg = AInsights_Utils_html_format_number($distChg, 1, 0, 1);
  $dispDistPctChg = AInsights_Utils_html_format_number($distPctChg, 1, 0, 1);
  $dispDistPctChgBg = AInsights_Utils_get_kpi_html_bgcolor($distPctChg);

  $velocity = AInsights_Utils_get_brand_calc_value($db, $dsSchema, 'velocity', $ownBrandID, 0);
  $velocityChg = AInsights_Utils_get_brand_calc_value($db, $dsSchema, 'velocity_chg', $ownBrandID, 0);
  $velocityPctChg = AInsights_Utils_get_brand_calc_value($db, $dsSchema, 'velocity_pct_chg', $ownBrandID, 0);

  $dispVelocity = AInsights_Utils_html_format_number($velocity, 1, 0, 1);
  $dispVelocityChg = AInsights_Utils_html_format_number($velocityChg, 1, 0, 1);
  $dispVelocityPctChg = AInsights_Utils_html_format_number($velocityPctChg, 1, 0, 1);
  $dispVelocityPctChgBg = AInsights_Utils_get_kpi_html_bgcolor($velocityPctChg);

  $avgPrice = AInsights_Utils_get_brand_calc_value($db, $dsSchema, 'avgprice', $ownBrandID, 0);
  $avgPriceChg = AInsights_Utils_get_brand_calc_value($db, $dsSchema, 'avgprice_chg', $ownBrandID, 0);
  $avgPricePctChg = AInsights_Utils_get_brand_calc_value($db, $dsSchema, 'avgprice_pct_chg', $ownBrandID, 0);

  $dispAvgPrice = AInsights_Utils_html_format_currency_kpi($avgPrice, 1);
  $dispAvgPriceChg = AInsights_Utils_html_format_currency_kpi($avgPriceChg, 1);
  $dispAvgPricePctChg = AInsights_Utils_html_format_number($avgPricePctChg, 1, 0, 1);


  #output summary KPIs and insights
  print <<END_HTML;
      <P>
      <DIV CLASS="card">
        <DIV CLASS="card-body">

          <DIV CLASS="card">
            <DIV CLASS="card-body">
              <DIV CLASS="row">
                <DIV CLASS="col">
                  <DIV CLASS="card bg-info text-white mx-auto" STYLE="width:10em;">
                    <DIV CLASS="card-body p-2">
                      <H5 CLASS="card-title text-center">$dispUnits</H5>
                      <P CLASS="card-text text-center text-white">Unit Sales</P>
                    </DIV>
                  </DIV>
                </DIV>
                <DIV CLASS="col">
                  <DIV CLASS="card $dispUnitsPctChgBg text-white mx-auto" STYLE="width:10em;">
                    <DIV CLASS="card-body p-2">
                      <H5 CLASS="card-title text-center">$dispUnitsChg</H5>
                      <P CLASS="card-text text-center text-white">Unit Sales Chg</P>
                    </DIV>
                  </DIV>
                </DIV>
                <DIV CLASS="col">
                  <DIV CLASS="card $dispUnitsPctChgBg text-white mx-auto" STYLE="width:10em;">
                    <DIV CLASS="card-body p-2">
                      <H5 CLASS="card-title text-center">$dispUnitsPctChg%</H5>
                      <P CLASS="card-text text-center text-white">Unit Sales % Chg</P>
                    </DIV>
                  </DIV>
                </DIV>
                <DIV CLASS="col-5">
                  <P CLASS="placeholder-glow">
                    <SPAN CLASS="placeholder placeholder-lg bg-success col-7 my-2"></SPAN>
                    <SPAN CLASS="placeholder placeholder-lg bg-success col-4 my-2"></SPAN>
                    <SPAN CLASS="placeholder placeholder-lg bg-success col-4 my-2"></SPAN>
                    <SPAN CLASS="placeholder placeholder-lg bg-success col-6 my-2"></SPAN>
                    <SPAN CLASS="placeholder placeholder-lg bg-success col-8 my-2"></SPAN>
                  </P>
                </DIV>
              </DIV>

              <DIV CLASS="row mt-1">
                <DIV CLASS="col">
                  <DIV CLASS="card bg-info text-white mx-auto mr-1" STYLE="width:10em;">
                    <DIV CLASS="card-body p-2">
                      <H5 CLASS="card-title text-center">$dispDollars</H5>
                      <P CLASS="card-text text-center text-white">Dollar Sales</P>
                    </DIV>
                  </DIV>
                </DIV>
                <DIV CLASS="col">
                  <DIV CLASS="card $dispDollarsPctChgBg text-white mx-auto" STYLE="width:10em;">
                    <DIV CLASS="card-body p-2">
                      <H5 CLASS="card-title text-center">$dispDollarsChg</H5>
                      <P CLASS="card-text text-center text-white">Dollar Sales Chg</P>
                    </DIV>
                  </DIV>
                </DIV>
                <DIV CLASS="col">
                  <DIV CLASS="card $dispDollarsPctChgBg text-white mx-auto" STYLE="width:10em;">
                    <DIV CLASS="card-body p-2">
                      <H5 CLASS="card-title text-center">$dispDollarsPctChg%</H5>
                      <P CLASS="card-text text-center text-white">Dollar Sales % Chg</P>
                    </DIV>
                  </DIV>
                </DIV>
                <DIV CLASS="col-5">

                </DIV>
              </DIV>

            </DIV>
          </DIV>

          <DIV CLASS="card">
            <DIV CLASS="card-body">
              <DIV CLASS="row">
                <DIV CLASS="col">
                  <DIV CLASS="card bg-info text-white mx-auto" STYLE="width:10em;">
                    <DIV CLASS="card-body p-2">
                      <H5 CLASS="card-title text-center">$dispPromoUnits</H5>
                      <P CLASS="card-text text-center text-white">Promo Units</P>
                    </DIV>
                  </DIV>
                </DIV>
                <DIV CLASS="col">
                  <DIV CLASS="card $dispPromoUnitsPctChgBg text-white mx-auto" STYLE="width:10em;">
                    <DIV CLASS="card-body p-2">
                      <H5 CLASS="card-title text-center">$dispPromoUnitsChg</H5>
                      <P CLASS="card-text text-center text-white">Promo Units Chg</P>
                    </DIV>
                  </DIV>
                </DIV>
                <DIV CLASS="col">
                  <DIV CLASS="card $dispPromoUnitsPctChgBg text-white mx-auto" STYLE="width:10em;">
                    <DIV CLASS="card-body p-2">
                      <H5 CLASS="card-title text-center">$dispPromoUnitsPctChg%</H5>
                      <P CLASS="card-text text-center text-white">Promo Units % Chg</P>
                    </DIV>
                  </DIV>
                </DIV>
                <DIV CLASS="col-5">
                  <P CLASS="placeholder-glow">
                    <SPAN CLASS="placeholder placeholder-lg bg-success col-7 my-2"></SPAN>
                    <SPAN CLASS="placeholder placeholder-lg bg-success col-4 my-2"></SPAN>
                    <SPAN CLASS="placeholder placeholder-lg bg-success col-4 my-2"></SPAN>
                    <SPAN CLASS="placeholder placeholder-lg bg-success col-6 my-2"></SPAN>
                    <SPAN CLASS="placeholder placeholder-lg bg-success col-8 my-2"></SPAN>
                  </P>
                </DIV>
              </DIV>

              <DIV CLASS="row mt-1">
                <DIV CLASS="col">
                  <DIV CLASS="card bg-info text-white mx-auto mr-1" STYLE="width:10em;">
                    <DIV CLASS="card-body p-2">
                      <H5 CLASS="card-title text-center">$dispNonPromoUnits</H5>
                      <P CLASS="card-text text-center text-white">Non-Promo Units</P>
                    </DIV>
                  </DIV>
                </DIV>
                <DIV CLASS="col">
                  <DIV CLASS="card $dispNonPromoUnitsPctChgBg text-white mx-auto" STYLE="width:10em;">
                    <DIV CLASS="card-body p-2">
                      <H5 CLASS="card-title text-center">$dispNonPromoUnitsChg</H5>
                      <P CLASS="card-text text-center text-white">Non-Promo Chg</P>
                    </DIV>
                  </DIV>
                </DIV>
                <DIV CLASS="col">
                  <DIV CLASS="card $dispNonPromoUnitsPctChgBg text-white mx-auto" STYLE="width:10em;">
                    <DIV CLASS="card-body p-2">
                      <H5 CLASS="card-title text-center">$dispNonPromoUnitsPctChg%</H5>
                      <P CLASS="card-text text-center text-white">Non-Promo % Chg</P>
                    </DIV>
                  </DIV>
                </DIV>
                <DIV CLASS="col-5">

                </DIV>
              </DIV>

            </DIV>
          </DIV>


          <DIV CLASS="card">
            <DIV CLASS="card-body">
              <DIV CLASS="row">
                <DIV CLASS="col">
                  <DIV CLASS="card bg-info text-white mx-auto" STYLE="width:10em;">
                    <DIV CLASS="card-body p-2">
                      <H5 CLASS="card-title text-center">$dispDist</H5>
                      <P CLASS="card-text text-center text-white">Distribution</P>
                    </DIV>
                  </DIV>
                </DIV>
                <DIV CLASS="col">
                  <DIV CLASS="card $dispDistPctChgBg text-white mx-auto" STYLE="width:10em;">
                    <DIV CLASS="card-body p-2">
                      <H5 CLASS="card-title text-center">$dispDistChg</H5>
                      <P CLASS="card-text text-center text-white">Distribution Chg</P>
                    </DIV>
                  </DIV>
                </DIV>
                <DIV CLASS="col">
                  <DIV CLASS="card $dispDistPctChgBg text-white mx-auto" STYLE="width:10em;">
                    <DIV CLASS="card-body p-2">
                      <H5 CLASS="card-title text-center">$dispDistPctChg%</H5>
                      <P CLASS="card-text text-center text-white">Distribution % Chg</P>
                    </DIV>
                  </DIV>
                </DIV>
                <DIV CLASS="col-5">
                  <P CLASS="placeholder-glow">
                    <SPAN CLASS="placeholder placeholder-lg bg-success col-7 my-2"></SPAN>
                    <SPAN CLASS="placeholder placeholder-lg bg-success col-4 my-2"></SPAN>
                    <SPAN CLASS="placeholder placeholder-lg bg-success col-4 my-2"></SPAN>
                    <SPAN CLASS="placeholder placeholder-lg bg-success col-6 my-2"></SPAN>
                    <SPAN CLASS="placeholder placeholder-lg bg-success col-8 my-2"></SPAN>
                  </P>
                </DIV>
              </DIV>

              <DIV CLASS="row mt-1">
                <DIV CLASS="col">
                  <DIV CLASS="card bg-info text-white mx-auto mr-1" STYLE="width:10em;">
                    <DIV CLASS="card-body p-2">
                      <H5 CLASS="card-title text-center">$dispVelocity</H5>
                      <P CLASS="card-text text-center text-white">Velocity</P>
                    </DIV>
                  </DIV>
                </DIV>
                <DIV CLASS="col">
                  <DIV CLASS="card $dispVelocityPctChgBg text-white mx-auto" STYLE="width:10em;">
                    <DIV CLASS="card-body p-2">
                      <H5 CLASS="card-title text-center">$dispVelocityChg</H5>
                      <P CLASS="card-text text-center text-white">Velocity Chg</P>
                    </DIV>
                  </DIV>
                </DIV>
                <DIV CLASS="col">
                  <DIV CLASS="card $dispVelocityPctChgBg text-white mx-auto" STYLE="width:10em;">
                    <DIV CLASS="card-body p-2">
                      <H5 CLASS="card-title text-center">$dispVelocityPctChg%</H5>
                      <P CLASS="card-text text-center text-white">Velocity % Chg</P>
                    </DIV>
                  </DIV>
                </DIV>
                <DIV CLASS="col-5">

                </DIV>
              </DIV>

              <DIV CLASS="row mt-1">
                <DIV CLASS="col">
                  <DIV CLASS="card bg-info text-white mx-auto mr-1" STYLE="width:10em;">
                    <DIV CLASS="card-body p-2">
                      <H5 CLASS="card-title text-center">$dispAvgPrice</H5>
                      <P CLASS="card-text text-center text-white">Avg Price</P>
                    </DIV>
                  </DIV>
                </DIV>
                <DIV CLASS="col">
                  <DIV CLASS="card bg-info text-white mx-auto" STYLE="width:10em;">
                    <DIV CLASS="card-body p-2">
                      <H5 CLASS="card-title text-center">$dispAvgPriceChg</H5>
                      <P CLASS="card-text text-center text-white">Avg Price Chg</P>
                    </DIV>
                  </DIV>
                </DIV>
                <DIV CLASS="col">
                  <DIV CLASS="card bg-info text-white mx-auto" STYLE="width:10em;">
                    <DIV CLASS="card-body p-2">
                      <H5 CLASS="card-title text-center">$dispAvgPricePctChg%</H5>
                      <P CLASS="card-text text-center text-white">Avg Price % Chg</P>
                    </DIV>
                  </DIV>
                </DIV>
                <DIV CLASS="col-5">

                </DIV>
              </DIV>


            </DIV>
          </DIV>

        </DIV> <!-- outer card body -->
      </DIV>  <!-- outer card -->
END_HTML


  #output summary grid for brand across all geos
  print <<END_HTML;
      <P>
      <DIV CLASS="card">
        <DIV CLASS="card-body">
          <TABLE CLASS="table table-bordered table-striped">
            <TR>
              <TH>Geography</TH>
              <TH>Unit Sales</TH>
              <TH>Dollar Sales</TH>
              <TH>Share</TH>
              <TH>Velocity</TH>
            </TR>
END_HTML

  #build up hashes of values to be displayed in summary KPI table
  $query = "SELECT geographyID, value FROM $dsSchema.$AInsightsBrandCalcTable \
      WHERE brandID=$ownBrandID AND name = 'units_pct_chg_year'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  while (($id, $value) = $dbOutput->fetchrow_array)
  {
    $unitPctChgBrandColorHash{$id} = AInsights_Utils_get_kpi_html_bgcolor($value);
    $icon = AInsights_Utils_get_kpi_html_icon($value);
    $value = sprintf("%.1f%", $value);
    $unitPctChgBrandHash{$id} = "$icon $value";
  }

  $query = "SELECT geographyID, value FROM $dsSchema.$AInsightsBrandCalcTable \
      WHERE brandID=$ownBrandID AND name = 'dollars_pct_chg_year'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  while (($id, $value) = $dbOutput->fetchrow_array)
  {
    $dollarsPctChgBrandColorHash{$id} = AInsights_Utils_get_kpi_html_bgcolor($value);
    $icon = AInsights_Utils_get_kpi_html_icon($value);
    $value = sprintf("%.1f%", $value);
    $dollarsPctChgBrandHash{$id} = "$icon $value";
  }

  $query = "SELECT geographyID, value FROM $dsSchema.$AInsightsBrandCalcTable \
      WHERE brandID=$ownBrandID AND name = 'unit_share_pct_chg_year'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  while (($id, $value) = $dbOutput->fetchrow_array)
  {
    $sharePctChgBrandColorHash{$id} = AInsights_Utils_get_kpi_html_bgcolor($value);
    $icon = AInsights_Utils_get_kpi_html_icon($value);
    $value = sprintf("%.1f%", $value);
    $sharePctChgBrandHash{$id} = "$icon $value";
  }

  $query = "SELECT geographyID, value FROM $dsSchema.$AInsightsBrandCalcTable \
      WHERE brandID=$ownBrandID AND name = 'velocity_pct_chg_year'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  while (($id, $value) = $dbOutput->fetchrow_array)
  {
    $velocityPctChgBrandColorHash{$id} = AInsights_Utils_get_kpi_html_bgcolor($value);
    $icon = AInsights_Utils_get_kpi_html_icon($value);
    $value = sprintf("%.1f%", $value);
    $velocityPctChgBrandHash{$id} = "$icon $value";
  }

  foreach $id (sort {$geoNameHash{$a} cmp $geoNameHash{$b}} keys %geoNameHash)
  {
    if (length($dollarsPctChgBrandColorHash{$id}))
    {
      print <<END_HTML;
            <TR>
              <TD><A HREF="insights.cld?pm=$priceModelID&g=$id" CLASS="text-decoration-none">$geoNameHash{$id}</A></TD>
              <TD CLASS="text-white $unitPctChgBrandColorHash{$id}">$unitPctChgBrandHash{$id}</TD>
              <TD CLASS="text-white $dollarsPctChgBrandColorHash{$id}">$dollarsPctChgBrandHash{$id}</TD>
              <TD CLASS="text-white $sharePctChgBrandColorHash{$id}">$sharePctChgBrandHash{$id}</TD>
              <TD CLASS="text-white $velocityPctChgBrandColorHash{$id}">$velocityPctChgBrandHash{$id}</TD>
            </TR>
END_HTML
    }
  }
  print <<END_HTML;
          </TABLE>
        </DIV>
      </DIV>
END_HTML

  print <<END_HTML;
    </DIV>
  </DIV>
</DIV>
<P>
END_HTML

  print_html_footer();

  AInsights_audit($db, $userID, $priceModelID, "Viewed details for $prodNameHash{$ppgID} in $geoNameHash{$geoID}");
  $activity = "AInsights: $first $last viewed model details for $prodNameHash{$ppgID} / $geoNameHash{$geoID} in pricing model $modelName in $dsName";
  utils_slack($activity);

#EOF
