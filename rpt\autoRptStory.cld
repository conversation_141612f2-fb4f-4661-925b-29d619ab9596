#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Automated Reporting Story Selection</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/rpt/main">Reports</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Select Story</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser and get the forecast to be deleted
  $q = new CGI;
  $dsID = $q->param('ds');

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  $db = KAPutil_connect_to_database();

  #get our data source name
  $dsName = ds_id_to_name($db, $dsID);

  #if there's already an auto report process running in this DS, join the
  #status stream
  $query = "SELECT operation FROM app.jobs WHERE dsID=$dsID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($operation) = $dbOutput->fetchrow_array;
  if ($operation eq "AUTO-RPTS")
  {
    print($session->header());
    print("<HTML><HEAD><META HTTP-EQUIV='refresh' CONTENT='0; URL=autoRptBuild.cld?ds=$dsID'></HEAD></HTML>\n");
    exit;
  }

  print_html_header();

  #make sure we have write privs for this data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to create reports in this data source.");
  }

  print <<END_HTML;
<P>&nbsp;</P>
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col">

      <DIV CLASS="row">

      <DIV CLASS="col">
          <DIV CLASS="card border-primary h-100">
            <DIV CLASS="card-header bg-primary text-white">Category Overview</DIV>
            <DIV CLASS="card-body">
              <IMG CLASS="img-thumbnail mx-auto d-block" SRC="/images/autorpt_catov_story.png">
              <I>13 reports</I>

              <P></P>
              Analyze your brand's performance across product, price, place, and promotion.
            </DIV>
            <DIV CLASS="card-footer border-white bg-white text-end">
              <BUTTON CLASS="btn btn-primary" TYPE="button" onclick="location.href='autoRptStoryBoard.cld?ds=$dsID&s=catov'">Choose Reports <I CLASS="bi bi-arrow-right"></I></BUTTON>
            </DIV>
          </DIV>
        </DIV>

        <DIV CLASS="col">
          <DIV CLASS="card border-primary h-100">
            <DIV CLASS="card-header bg-primary text-white">Topline Business Review</DIV>
            <DIV CLASS="card-body">
              <IMG CLASS="img-thumbnail mx-auto d-block" SRC="/images/autorpt_tlbr_story.png">
              <I>6 reports</I>

              <P></P>
              See how your categories, brands, and items are selling versus competitors across markets.
            </DIV>
            <DIV CLASS="card-footer border-white bg-white text-end">
              <BUTTON CLASS="btn btn-primary" TYPE="button" onclick="location.href='autoRptStoryBoard.cld?ds=$dsID&s=tlbr'">Choose Reports <I CLASS="bi bi-arrow-right"></I></BUTTON>
            </DIV>
          </DIV>
        </DIV>

      </DIV>  <!-- card row -->

      <P>&nbsp;</P>

      <DIV CLASS="row">

        <DIV CLASS="col">
          <DIV CLASS="card border-primary h-100">
            <DIV CLASS="card-header bg-primary text-white">Baseline Drivers</DIV>
            <DIV CLASS="card-body">
            <IMG CLASS="img-thumbnail mx-auto d-block" SRC="/images/autorpt_bldr_story.png">
              <I>6 reports</I>

              <P></P>
              Examine the drivers of your every day business.
            </DIV>
            <DIV CLASS="card-footer border-white bg-white text-end">
              <BUTTON CLASS="btn btn-primary" TYPE="button" onclick="location.href='autoRptStoryBoard.cld?ds=$dsID&s=bldr'">Choose Reports <I CLASS="bi bi-arrow-right"></I></BUTTON>
            </DIV>
          </DIV>
        </DIV>

        <DIV CLASS="col">
          <DIV CLASS="card border-primary h-100">
            <DIV CLASS="card-header bg-primary text-white">Incremental Drivers</DIV>
            <DIV CLASS="card-body">
              <IMG CLASS="img-thumbnail mx-auto d-block" SRC="/images/autorpt_incr_story.png">
              <I>8 reports</I>

              <P></P>
              Determine how promotional tools impact your incremental business.
            </DIV>
            <DIV CLASS="card-footer border-white bg-white text-end">
              <BUTTON CLASS="btn btn-primary" TYPE="button" onclick="location.href='autoRptStoryBoard.cld?ds=$dsID&s=incdr'">Choose Reports <I CLASS="bi bi-arrow-right"></I></BUTTON>
            </DIV>
          </DIV>
        </DIV>

      </DIV>  <!-- card row -->
END_HTML

  if ($Lib::KoalaConfig::cloudtype eq "dev")
  {
    print <<END_HTML;

END_HTML
  }

  print <<END_HTML;
    </DIV>  <!-- col -->

  </DIV>  <!-- row -->

  <DIV CLASS="row">
    <DIV CLASS="col">
      <P>&nbsp;</P>
      <DIV CLASS="text-center">
        <BUTTON CLASS="btn btn-secondary" TYPE="button" onclick="location.href='main?ds=$dsID'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      </DIV>
    </DIV>
  </DIV>

</DIV> <!-- container -->
<P>
END_HTML

  print_html_footer();

#EOF
