#!/usr/bin/perl

use Text::CSV;

#load ESM's custom AOD download format


  #instantiate our CSV object
  $csv = Text::CSV->new( {binary => 1} );

  open(INPUT, "$ARGV[0]");
  open(OUTPUT, ">$ARGV[1]");
print STDERR "$ARGV[0] -> $ARGV[1]\n";


  #burn first header line
  $line = <INPUT>;

  #parse the 2nd header line (contains time period)
  $line = <INPUT>;
  $csv->parse($line);
  @tmp = $csv->fields();
  $tmp[0] =~ m/Time : (.*)$/;
  $time = $1;

  #parse the 3rd header line (contains geography)
  $line = <INPUT>;
  $csv->parse($line);
  @tmp = $csv->fields();
  $tmp[0] =~ m/Geography : (.*)$/;
  $geography = $1;

  if (length($geography) < 2)
  {
    $geography = "UNKNOWN";
  }
  if (length($time) < 2)
  {
    $time = "UNKNOWN";
  }


  #grab the top-level header line (each item other than product has 3 under it)
  $line = <INPUT>;
  $csv->parse($line);
  @topHeaders = $csv->fields();
  shift(@topHeaders);	#knock off "Product" column

  #burn the secondary header line (we're assuming consistent format)
  $line = <INPUT>;

  #start building up our actual header line for Koala
  $headers[0] = "Geo";
  $headers[1] = "Time";
  $headers[2] = "UPC";
  $headers[3] = "Product";
  foreach $topHeader (@topHeaders)
  {
    if ($topHeader =~ m/ACV Weighted/)
    {
      $topHeader = "ACV Weighted Distribution";
    }

    if (length($topHeader) > 2)
    {

      if (($topHeader =~ m/flavor/i) || ($topHeader =~ m/brand name/i))
      {
        $topHeader = $topHeader;
      }
      else
      {
        $topHeader = "meas:$topHeader";
      }
      push(@headers, "$topHeader");
      push(@headers, "$topHeader YA");
      push(@headers, "$topHeader % Change vs YA");
    }
  }

  #output the headers
  $csv->combine(@headers);
  $line = $csv->string();
  print OUTPUT "$line\n";


  while ($line = <INPUT>)
  {

    $csv->parse($line);
    @columns = $csv->fields();

    #extract the UPC from the end of the product name
    $product = $columns[0];
    $upc = "";
    if ($product =~ m/^.* (\d+)$/)
    {
      $upc = $1;
    }

    #skip rows without UPCs
    if (length($upc) < 9)
    {
      next;
    }


    @tmp = ($geography, $time, $upc);
    push(@tmp, @columns);

    $csv->combine(@tmp);
    $line = $csv->string();

    print OUTPUT "$line\n";

  }

