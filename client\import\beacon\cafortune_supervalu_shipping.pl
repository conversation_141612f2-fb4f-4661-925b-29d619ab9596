#!/usr/bin/perl

use Text::CSV;

#Import shipping data from SuperValu Harbor shipping database for CA Fortune

  #instantiate our CSV object
  $csv = Text::CSV->new( {binary => 1} );

  open(INPUT, "$ARGV[0]");
  open(OUTPUT, ">$ARGV[1]");

  #burn unneeded header info
  $line = <INPUT>;
  while (!($line =~ m/^Invoice Week/))
  {
    $line = <INPUT>;

    if (!(defined($line)))
    {
      exit;
    }
  }

  #line should now be the header line, so parse it
  $csv->parse($line);
  @columns = $csv->fields();

  $idx = 0;
  foreach $header (@columns)
  {
    if ($header =~ m/Invoice Week/)
    {
      $columns[$idx] = "Time";
    }
    elsif ($header =~ m/SVDCName/)
    {
      $columns[$idx] = "GATTR:SVDCName";
    }
    elsif ($header =~ m/CustNbr/)
    {
      $columns[$idx] = "GATTR:CustNbr";
    }
    elsif ($header =~ m/Name/)
    {
      $columns[$idx] = "Geography";
    }
    elsif ($header =~ m/Group/)
    {
      $columns[$idx] = "Geography";
    }
    elsif ($header =~ m/City/)
    {
      $columns[$idx] = "GATTR:City";
    }
    elsif ($header =~ m/State/)
    {
      $columns[$idx] = "GATTR:State";
    }
    elsif ($header =~ m/UPCCase/)
    {
      $columns[$idx] = "UPC";
    }
    elsif ($header =~ m/SVItemCd/)
    {
      $columns[$idx] = "PATTR:SVItemCd";
    }
    elsif ($header =~ m/SVBrand/)
    {
      $columns[$idx] = "PSEG:SVBrand";
    }
    elsif ($header =~ m/SVDescription/)
    {
      $columns[$idx] = "Product";
    }

    $idx++;
  }

  #output the headers
  $csv->combine(@columns);
  $line = $csv->string();
  print OUTPUT "$line\n";

  while ($line = <INPUT>)
  {
    $csv->parse($line);
    @columns = $csv->fields();

    $columns[0] =~ m/^.*\-(.*)$/;
    $columns[0] = "1 WE $1";

    $csv->combine(@columns);
    $line = $csv->string();

    print OUTPUT "$line\n";
  }

