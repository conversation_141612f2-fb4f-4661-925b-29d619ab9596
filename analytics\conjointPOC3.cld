#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::DataSources;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Visual Conjoint Analysis POC</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<SCRIPT SRC="/fusioncharts-3.18.0/js/fusioncharts.js"></SCRIPT>
<SCRIPT SRC="/fusioncharts-3.18.0/js/themes/fusioncharts.theme.zune.js"></SCRIPT>

<SCRIPT>
let gridHeight = window.innerHeight - 200;
if (gridHeight < 250)
{
  gridHeight = 250;
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">Retail Analytics</A></LI>
    <LI CLASS="breadcrumb-item active">Visual Conjoint Analysis POC</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');
  $hierarchyID = $q->param('h');
  $geoID = $q->param('g');

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsSchema = "datasource_" . $dsID;

  %prodNameHash = dsr_get_item_name_hash($db, $dsSchema, "p");
  %geoNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "g");

  print_html_header();

  #make sure we have reaad privs for this data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to analyze this data source.");
  }

  #get the name of the geography where the analysis is being run
  $geoName = $geoNameHash{$geoID};

  #get the ID of the unit sales measure
  $query = "SELECT ID FROM $dsSchema.measures WHERE name LIKE 'Units'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  if ($status < 1)
  {
    exit_error("Can't find a Units measure in this data source");
  }
  ($unitMeasID) = $dbOutput->fetchrow_array;

  #get the list of items that have been carried in the specified geography in the
  #past 6 months
  $query = "SELECT DISTINCT productID FROM $dsSchema.facts \
      WHERE geographyID=$geoID AND measure_$unitMeasID > 0";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  while (($prodID) = $dbOutput->fetchrow_array)
  {
    $productSet{$prodID} = 1;
  }

  #get the definition of the hierarchy
  $query = "SELECT name, segmentations FROM $dsSchema.product_seghierarchy \
      WHERE ID=$hierarchyID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ($hierName, $hierDef) = $dbOutput->fetchrow_array;
  @hierLevels = split(',', $hierDef);

  #get the item membership "chain" for this seg hierarchy
  %chains = DSRseghier_get_item_chain_hash($db, $dsSchema, "p", $hierarchyID);

  $level = 1;
  foreach $seg (@hierLevels)
  {

    #build up a regex pattern to pull the chain at our level
    $pattern = "^(";
    for ($i = 0; $i < $level; $i++)
    {
      $pattern .= "\\d+_";
    }
    chop($pattern); #knock-off trailing underscore
    $pattern .= ")";

    foreach $itemID (keys %chains)
    {
      $chains{$itemID} =~ m/$pattern/;
      $itemLevelCount{$1}++;
    }

    $level++;
  }

  #run through the level/value hash, creating JSON for each segment
  $sunburstJSON = "";
  foreach $level (keys %itemLevelCount)
  {
    if ($level =~ m/^(.*)_/)
    {
      $parent = $1;
    }
    else
    {
      $parent = "";
    }

    if ($level =~ m/.*?(\d+)$/)
    {
      $segmentID = $1;
    }
    else
    {
      $segmentID = "";
    }
    $fqSegmentID = "SMT_$segmentID";
    $segmentName = $prodNameHash{$fqSegmentID};
    $segmentName =~ s/\'/\\'/g;
    $segmentItemCount = $itemLevelCount{$level};

    #segments in first level are root-level, so no parents
    if (length($parent) < 1)
    {
      $sunburstJSON .= "{id: '$level', name: '$segmentName', value: '$segmentItemCount'},\n";
    }
    else
    {
      $sunburstJSON .= "{id: '$level', parent: '$parent', name: '$segmentName', value: '$segmentItemCount'},\n";
    }
  }

  #run through the master hash, creating JSON for each item
  foreach $itemID (keys %chains)
  {

    #make sure the product was carried by the selected retailer
    if ($productSet{$itemID} < 1)
    {
      next;
    }

    $id = "item_" . $itemID;
    $parent = $chains{$itemID};
    $name = $prodNameHash{$itemID};
    $sunburstJSON .= "{id: '$id', parent: '$parent', name: '$name', value: '1'},\n";
  }
  chop($sunburstJSON);  chop($sunburstJSON);

  print <<END_HTML;
<DIV CLASS="container-fluid">

  <DIV CLASS="row">
    <DIV CLASS="col text-center">

      <P>&nbsp;</P>
      <DIV CLASS="card">
        <DIV CLASS="card-body">

          <DIV ID="div-chart-conjoint-sunburst"></DIV>
<SCRIPT>
FusionCharts.ready(function()
{
  let conjointChart = new FusionCharts(
  {
    type: 'sunburst',
    renderAt: 'div-chart-conjoint-sunburst',
    width: '100%', height: gridHeight,
    dataFormat: 'json',
    dataSource:
    {
      'chart':
      {
        'caption': 'Assortment Analysis at $geoName',
        'captionFontSize': '24',
        'captionFontColor': '333333',
        'baseFontColor': 'ffffff',
        'tooltipcolor': '000000',
        'theme': 'fusion'
      },
      'data': [ $sunburstJSON ]
    }
  });
  conjointChart.render();
});
 </SCRIPT>
        </DIV>
      </DIV>

    </DIV>
  </DIV>

</DIV>
END_HTML

  print_html_footer();

#EOF
