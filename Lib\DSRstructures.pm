package Lib::DSRstructures;

use lib "/opt/apache/app";

use Exporter;
use Storable qw(dclone);

use Lib::DSRUtils;


our @ISA = ('Exporter');

our @EXPORT = qw(
    &DSRstructure_expand
    &DSRstructure_extract_id
    &DSRlist_expand_items_array
    &DSRlist_expand_items
    &DSRlist_id_to_name
    &DSRagg_expand_items
    &DSRagg_get_aggregates_hash
    &DSRagg_id_to_name
    &DSRagg_get_recent_date
    &DSRsegmentation_get_segmentations_hash
    &DSRsegmentation_get_segmentations_array
    &DSRsegmentation_expand_segments_array
    &DSRsegmentation_expand_segments
    &DSRsegmentation_expand_items
    &DSRsegmentation_update_linked_segs
    &DSRsegmentation_apply_seg_rules
    &DSRsegment_expand_items
    &DSRseg_get_item_membership
    &DSRseg_get_segments_hash
    &DSRseg_get_segments_array
    &DSRseg_get_segitems_array
    &DSRseg_get_unsegmented_items
    &DSRseg_get_segment_membership_hash
    &DSRseg_get_segment_item_hash
    &DSRseghier_id_to_name
    &DSRseghier_get_segs_array
    &DSRseghier_get_item_chain_hash
    &DSRsegHier_expand_segments
    &DSRseghier_expand_items
    &DSRseghier_get_level_names_hash
    &DSRmergedprod_create
    &DSRmergedprod_update
    &DSRmergedprod_get_primary
    &DSRmergedprod_get_members
    &DSRmergedprod_unwind
    &DSRattr_get_attributes_hash
    &DSRattr_get_values_hash);


#-------------------------------------------------------------------------------

#Variables global to this module (used for hashing segment memberships to cut
#down on db queries while constructing segmentation hierarchy chains)

my $_cacheDSID;

my @_cachedSegMemberships;



#-------------------------------------------------------------------------------
#
# Handle a database error of some kind during DSR structure operations
#

sub struct_db_err
{
  my ($date);

  my ($db, $status, $text) = @_;


  if (!defined($status))
  {
    $date = localtime();
    print STDERR "$date: $text\n";
    if ($db->errstr =~ m/^MySQL server has gone away/)
    {
      die("Lost connection to database, terminating");
    }
  }
}



#-------------------------------------------------------------------------------
#
# Clear out our persistent caches if we're working in a new DS
#

sub DSRstructures_cache_ok
{
  my ($id);

  my ($dsID) = @_;


  if ($dsID =~ m/datasource_(\d+)/)
  {
    $dsID = $1;
  }

  #if we're starting work in a new data source
  if ($_cacheDSID != $dsID)
  {
    $_cacheDSID = $dsID;

    #clear out all hashed segmentation memberships
    foreach $id (@_cachedSegMemberships)
    {
      if (defined($_cachedSegMemberships[$id]))
      {
        undef($_cachedSegMemberships[$id]);
      }
    }
    undef(@_cachedSegMemberships);
  }
}



#-------------------------------------------------------------------------------
#
# Expand any DSR structure into its component base items (this is a meta
# function that can be used to generically expand any structure without
# having to constantly recreate the conditional tree to decide which
# expansion function to call)
#

sub DSRstructure_expand
{
  my ($itemIDs);

  my ($db, $dsSchema, $dim, $structID) = @_;


  if ($structID =~ m/^AGG_\d+/)
  {
    $itemIDs = DSRagg_expand_items($db, $dsSchema, $dim, $structID);
  }
  elsif ($structID =~ m/SMT_\d+/)
  {
    $itemIDs = DSRsegment_expand_items($db, $dsSchema, $dim, $structID);
  }
  elsif ($structID =~ m/SEG_\d+/)
  {
    $itemIDs = DSRsegmentation_expand_items($db, $dsSchema, $dim, $structID);
  }
  elsif ($structID =~ m/SHS_\d+/)
  {
    $itemIDs = DSRseghier_expand_items($db, $dsSchema, $dim, $structID);
  }
  else
  {
    $itemIDs = $structID;
  }

  return($itemIDs);
}



#-------------------------------------------------------------------------------
#
# Accept a (possibly) fully-qualified structure ID (e.g., "AGG_1") and
# return just the integer ID. If the ID is already just an integer, return
# unchanged.
#

sub DSRstructure_extract_id
{
  my ($id);

  my ($structID) = @_;

  if ($structID =~ m/^[A-Z]+_(\d+)$/)
  {
    $id = $1;
  }
  else
  {
    $id = $structID;
  }

  return($id);
}



#-------------------------------------------------------------------------------
#
# "Expand" a list into the base items that it's comprised of
#

sub DSRlist_expand_items_array
{
  my ($query, $dbOutput, $status, $dbName, $members);
  my (@listMembers);

  my ($db, $dsSchema, $dim, $listID) = @_;


  #figure out the table containing lists for our dimension
  if ($dim eq "p")
  {
    $dbName = "product_list";
  }
  elsif ($dim eq "g")
  {
    $dbName = "geography_list";
  }
  elsif ($dim eq "t")
  {
    $dbName = "time_list";
  }
  elsif ($dim eq "m")
  {
    $dbName = "measure_list";
  }

  #if we were handed the ID in the form LIS_###, strip off the prepend
  if ($listID =~ m/^LIS_(\d+)/)
  {
    $listID = $1;
  }

  $query = "SELECT members FROM $dsSchema.$dbName WHERE ID=$listID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);

  ($members) = $dbOutput->fetchrow_array;

  @listMembers = split(/,/, $members);

  return(@listMembers);
}



#-------------------------------------------------------------------------------
#
# "Expand" a list into the base items that it's comprised of
#

sub DSRlist_expand_items
{
  my ($query, $dbOutput, $status, $dbName, $members);
  my ($listMembers);

  my ($db, $dsSchema, $dim, $listID) = @_;


  #figure out the table containing lists for our dimension
  if ($dim eq "p")
  {
    $dbName = "product_list";
  }
  elsif ($dim eq "g")
  {
    $dbName = "geography_list";
  }
  elsif ($dim eq "t")
  {
    $dbName = "time_list";
  }
  elsif ($dim eq "m")
  {
    $dbName = "measure_list";
  }

  #if we were handed the ID in the form LIS_###, strip off the prepend
  if ($listID =~ m/^LIS_(\d+)/)
  {
    $listID = $1;
  }

  $query = "SELECT members FROM $dsSchema.$dbName WHERE ID=$listID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);

  ($listMembers) = $dbOutput->fetchrow_array;

  return($listMembers);
}



#-------------------------------------------------------------------------------
#
# Return the name of the list that has the specified DSR ID
#

sub DSRlist_id_to_name
{
  my ($dbName, $query, $dbOutput, $name, $status);

  my ($db, $dsSchema, $dim, $listID) = @_;


  #parameter check
  if (length($dsSchema) < 5)
  {
    return("");
  }

  #figure out the table containing lists for our dimension
  if ($dim eq "p")
  {
    $dbName = "product_list";
  }
  elsif ($dim eq "g")
  {
    $dbName = "geography_list";
  }
  elsif ($dim eq "t")
  {
    $dbName = "time_list";
  }
  elsif ($dim eq "m")
  {
    $dbName = "measure_list";
  }
  else
  {
    return("");
  }

  #if we were handed the ID in the form LIS_###, strip off the prepend
  if ($listID =~ m/^LIS_(\d+)/)
  {
    $listID = $1;
  }

  $query = "SELECT name FROM $dsSchema.$dbName WHERE ID=$listID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);

  ($name) = $dbOutput->fetchrow_array;

  return($name);
}



#-------------------------------------------------------------------------------
#
# "Expand" an aggregate into the member items it's comprised of. If the
# aggregate contains hierarchy items (like segments), expand them out into
# their base items, too.
#

sub DSRagg_expand_items
{
  my ($query, $dbOutput, $status, $dbName, $members);
  my ($aggMemberString, $aggMember, $tmp);
  my (@aggMembers);

  my ($db, $dsSchema, $dim, $aggID) = @_;


  #figure out the table containing aggregates for our dimension
  if ($dim eq "p")
  {
    $dbName = "product_aggregate";
  }
  elsif ($dim eq "g")
  {
    $dbName = "geography_aggregate";
  }
  elsif ($dim eq "t")
  {
    $dbName = "time_aggregate";
  }

  #if we were handed the ID in the form AGG_###, strip off the prepend
  if ($aggID =~ m/^AGG_(\d+)/)
  {
    $aggID = $1;
  }

  $query = "SELECT addMembers FROM $dsSchema.$dbName WHERE ID=$aggID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);
  ($aggMemberString) = $dbOutput->fetchrow_array;

  @aggMembers = split(',', $aggMemberString);
  $aggMemberString = "";

  #if our aggregate contains segments, expand them into their base items
  foreach $aggMember (@aggMembers)
  {
    if ($aggMember =~ m/SMT_(\d+)/)
    {
      $tmp = DSRsegment_expand_items($db, $dsSchema, $dim, $aggMember);
      if (length($tmp) > 0)
      {
        $aggMemberString .= "$tmp,";
      }
    }
    elsif ($aggMember =~ m/AGG_(\d+)/)
    {
      #avoid circular expansion (an aggregate containing itself)
      if ($aggID eq $1)
      {
        next;
      }
      $tmp = DSRagg_expand_items($db, $dsSchema, $dim, $aggMember);
      if (length($tmp) > 0)
      {
        $aggMemberString .= "$tmp,";
      }
    }
    elsif ($aggMember =~ m/SHS_\d+/)
    {
      $tmp = DSRseghier_expand_items($db, $dsSchema, $dim, $aggMember);
      if (length($tmp) > 0)
      {
        $aggMemberString .= "$tmp,";
      }
    }
    else
    {
      $aggMemberString .= "$aggMember,";
    }
  }

  #chop trailing comma
  chop($aggMemberString);

  return($aggMemberString);
}



#-------------------------------------------------------------------------------
#
# Return a hash of all aggregates in a data source for the specified
# dimension, indexed by ID
#

sub DSRagg_get_aggregates_hash
{
  my ($query, $dbOutput, $status, $dbName, $id, $aggID, $name);
  my (%aggHash);

  my ($db, $dsSchema, $dim) = @_;


  undef(%aggHash);

  #figure out the table containing aggregates for our dimension
  if ($dim eq "p")
  {
    $dbName = "product_aggregate";
  }
  elsif ($dim eq "g")
  {
    $dbName = "geography_aggregate";
  }
  elsif ($dim eq "t")
  {
    $dbName = "time_aggregate";
  }

  #if we were handed the ID in the form AGG_###, strip off the prepend
  if ($aggID =~ m/^AGG_(\d+)/)
  {
    $aggID = $1;
  }

  $query = "SELECT ID, name FROM $dsSchema.$dbName ORDER BY name";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);

  while (($id, $name) = $dbOutput->fetchrow_array)
  {
    $aggHash{$id} = $name;
  }

  return(%aggHash);
}



#-------------------------------------------------------------------------------
#
# Return the name of the aggregate that has the specified DSR ID
#

sub DSRagg_id_to_name
{
  my ($dbName, $query, $dbOutput, $name, $status);

  my ($db, $dsSchema, $dim, $aggID) = @_;


  #figure out the table containing aggregates for our dimension
  if ($dim eq "p")
  {
    $dbName = "product_aggregate";
  }
  elsif ($dim eq "g")
  {
    $dbName = "geography_aggregate";
  }
  elsif ($dim eq "t")
  {
    $dbName = "time_aggregate";
  }

  #if we were handed the ID in the form AGG_###, strip off the prepend
  if ($aggID =~ m/^AGG_(\d+)/)
  {
    $aggID = $1;
  }

  $query = "SELECT name FROM $dsSchema.$dbName WHERE ID=$aggID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);

  ($name) = $dbOutput->fetchrow_array;

  return($name);
}



#-------------------------------------------------------------------------------
#
# Return the date string associated with the most recent time period in the
# specified time aggregate (used for dynamically adding the end date to the
# aggregate name).
#

sub DSRagg_get_recent_date
{
  my ($dbName, $query, $dbOutput, $addMembers, $status, $endDate, $dsID);
  my ($sqlDateFmt, $timeAlias);

  my ($db, $dsSchema, $aggID) = @_;


  #turn the schema name into a data source ID
  if ($dsSchema =~ m/^datasource_(\d+)/)
  {
    $dsID = $1;
  }

  #if we were handed the ID in the form AGG_###, strip off the prepend
  if ($aggID =~ m/^AGG_(\d+)/)
  {
    $aggID = $1;
  }

  #get the list of time periods included in the aggregate
  $query = "SELECT addMembers FROM $dsSchema.time_aggregate WHERE ID=$aggID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);
  ($addMembers) = $dbOutput->fetchrow_array;

  #if the aggregate is empty, there's no ending time period
  if (length($addMembers) < 1)
  {
    return;
  }

  $timeFormatOpts = DSRutil_get_time_aliases_format($db, $dsID);
  $sqlDateFmt = $timeFormatOpts->{'sqlDateFmt'};

  #if this DS doesn't have any global time aliases defined, we're done
  if (length($sqlDateFmt) < 1)
  {
    $sqlDateFmt = "%c/%e/%Y";
  }

  #get the end date of the most recent time period in the aggregate
  $query = "SELECT DATE_FORMAT(endDate, '$sqlDateFmt') \
      FROM $dsSchema.timeperiods \
      WHERE ID IN ($addMembers) ORDER BY endDate DESC LIMIT 1";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);
  ($endDate) = $dbOutput->fetchrow_array;

  return($endDate);
}



#-------------------------------------------------------------------------------
#
# Return a hash of all segmentations in a data source for the specified
#

sub DSRsegmentation_get_segmentations_hash
{
  my ($query, $dbOutput, $status, $dbName, $segID, $segName);
  my (%segHash);

  my ($db, $dsSchema, $dim) = @_;


  if ($dsSchema =~ m/^\d+$/)
  {
    $dsSchema = "datasource_" . $dsSchema;
  }

  #figure out the table containing aggregates for our dimension
  if ($dim eq "p")
  {
    $dbName = "product_segmentation";
  }
  elsif ($dim eq "g")
  {
    $dbName = "geography_segmentation";
  }
  elsif ($dim eq "t")
  {
    $dbName = "time_segmentation";
  }

  $query = "SELECT ID, name FROM $dsSchema.$dbName";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);

  while (($segID, $segName) = $dbOutput->fetchrow_array)
  {
    $segHash{$segID} = $segName;
  }

  return(%segHash);
}



#-------------------------------------------------------------------------------
#
# Return an array of all segmentations in a data source for the specified
# dimension in alpha-sorted by name
#

sub DSRsegmentation_get_segmentations_array
{
  my ($query, $dbOutput, $status, $dbName, $id, $name);
  my (@segArray);

  my ($db, $dsSchema, $dim) = @_;


  undef(@segArray);

  #figure out the table containing aggregates for our dimension
  if ($dim eq "p")
  {
    $dbName = "product_segmentation";
  }
  elsif ($dim eq "g")
  {
    $dbName = "geography_segmentation";
  }
  elsif ($dim eq "t")
  {
    $dbName = "time_segmentation";
  }

  $query = "SELECT ID FROM $dsSchema.$dbName ORDER BY name";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);

  while (($id) = $dbOutput->fetchrow_array)
  {
    push(@segArray, $id);
  }

  return(@segArray);
}



#-------------------------------------------------------------------------------
#
# "Expand" a segmentation into the member segments it's comprised of
#

sub DSRsegmentation_expand_segments
{
  my ($query, $dbOutput, $status, $dbName, $members);
  my ($segMembers, $segmentID);

  my ($db, $dsSchema, $dim, $segmentationID) = @_;


  #figure out the table containing segmentations for our dimension
  if ($dim eq "p")
  {
    $dbName = "product_segment";
  }
  elsif ($dim eq "g")
  {
    $dbName = "geography_segment";
  }
  elsif ($dim eq "t")
  {
    $dbName = "time_segment";
  }

  #if we were handed the ID in the form SEG_###, strip off the prepend
  if ($segmentationID =~ m/^SEG_(\d+)/)
  {
    $segmentationID = $1;
  }

  $query = "SELECT ID FROM $dsSchema.$dbName WHERE segmentationID=$segmentationID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);

  $segMembers = "";
  while (($segmentID) = $dbOutput->fetchrow_array)
  {
    $segMembers .= "$segmentID,";
  }

  #trim trailing comma
  chop($segMembers);

  return($segMembers);
}



#-------------------------------------------------------------------------------
#
# "Expand" a segmentation into all of the items it's comprised of
#

sub DSRsegmentation_expand_items
{
  my ($segItems, $segmentID, $tmp, $query, $dbOutput, $itemID, $dbName);
  my ($status);
  my (@segments);

  my ($db, $dsSchema, $dim, $segmentationID) = @_;


  #figure out the table containing segmentation membership for our dimension
  if ($dim eq "p")
  {
    $dbName = "product_segment_item";
  }
  elsif ($dim eq "g")
  {
    $dbName = "geography_segment_item";
  }
  elsif ($dim eq "t")
  {
    $dbName = "time_segment_item";
  }

  if ($segmentationID =~ m/^SEG_(\d+)/)
  {
    $segmentationID = $1;
  }

  #grab every base item that's a member of the specified segmentation
  $query = "SELECT itemID FROM $dsSchema.$dbName WHERE segmentationID=$segmentationID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);

  #build up a CSV string of the item IDs
  undef($segItems);
  while (($itemID) = $dbOutput->fetchrow_array)
  {
    $segItems = $segItems . $itemID . ",";
  }

  #chop off trailing comma
  chop($segItems);

  return($segItems);
}



#-------------------------------------------------------------------------------
#
# Determine if the specified segmentation has any linked child segmentations,
# and if so, update them. (Called at the end of the segment save process to
# handle "linked segmentations" - segmentations that have unsegmented items
# set according to their values in a master segmentation)
#

sub DSRsegmentation_update_linked_segs
{
  my ($dimDB, $dbName, $query, $dbOutput, $status, $id, $linkedSegID, $itemID);
  my ($parentSegmentID, $parentSegmentName, $q_name, $childSegID);
  my (@linkedSegs, @unsegmentedItems);
  my (%parentSegmentsHash, %parentMembershipHash);

  my ($db, $dsSchema, $dim, $segmentationID) = @_;


 #figure out the table name root for our dimension
  if ($dim eq "p")
  {
    $dimDB = "product_";
  }
  elsif ($dim eq "g")
  {
    $dimDB = "geography_";
  }
  elsif ($dim eq "t")
  {
    $dimDB = "time_";
  }

  #get every item's segment membership in the parent segmentation
  %parentMembershipHash = DSRseg_get_segment_membership_hash($db, $dsSchema, $dim, $segmentationID);

  #get the hash of segments contained in the parent segmentation
  %parentSegmentsHash = DSRseg_get_segments_hash($db, $dsSchema, $dim, $segmentationID);

  #get an array of every segmentation ID that's linked to this parent seg
  $dbName = $dimDB . "segmentation";
  $query = "SELECT ID FROM $dsSchema.$dbName WHERE parentID=$segmentationID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);
  undef(@linkedSegs);
  while (($id) = $dbOutput->fetchrow_array)
  {
    push(@linkedSegs, $id);
  }

  #for every linked segmentation
  foreach $linkedSegID (@linkedSegs)
  {

    #get an array of all unsegmented items in the child segmentation
    @unsegmentedItems = DSRseg_get_unsegmented_items($db, $dsSchema, $dim, $linkedSegID);

    #for each unsegmented item
    foreach $itemID (@unsegmentedItems)
    {

      #get the name of the item's segment
      $parentSegmentID = $parentMembershipHash{$itemID};
      $parentSegmentName = $parentSegmentsHash{$parentSegmentID};

      #if it's still unsegmented in the parent, move on to the next item
      if (length($parentSegmentName) < 1)
      {
        next;
      }

      #get the ID for the segment of the same name in the child segmentation
      $dbName = $dimDB . "segment";
      $q_name = $db->quote($parentSegmentName);
      $query = "SELECT ID FROM $dsSchema.$dbName \
          WHERE name=$q_name AND segmentationID=$linkedSegID";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      struct_db_err($db, $status, $query);
      ($childSegID) = $dbOutput->fetchrow_array;

      #if a segment of that name doesn't already exist in the child, create it
      if (!defined($childSegID))
      {
        $query = "INSERT INTO $dsSchema.$dbName (segmentationID, name) \
            VALUES ($linkedSegID, $q_name)";
        $status = $db->do($query);
        struct_db_err($db, $status, $query);

        #get the newly inserted segment's ID
        $childSegID = $db->{q{mysql_insertid}};
      }

      #assign the item to that segment
      $dbName = $dimDB . "segment_item";
      $query = "INSERT INTO $dsSchema.$dbName (segmentationID, segmentID, itemID) \
          VALUES($linkedSegID, $childSegID, $itemID)";
      $status = $db->do($query);
      struct_db_err($db, $status, $query);

    }
  }
}



#-------------------------------------------------------------------------------
#
# "Expand" a segmentation into the member segments it's comprised of
#

sub DSRsegmentation_expand_segments_array
{
  my ($query, $dbOutput, $status, $dbName, $members);
  my ($segMembers, $segmentID);
  my (@segMembersArray);

  my ($db, $dsSchema, $dim, $segmentationID) = @_;


  #figure out the table containing segmentations for our dimension
  if ($dim eq "p")
  {
    $dbName = "product_segment";
  }
  elsif ($dim eq "g")
  {
    $dbName = "geography_segment";
  }
  elsif ($dim eq "t")
  {
    $dbName = "time_segment";
  }

  #if we were handed the ID in the form SEG_###, strip off the prepend
  if ($segmentationID =~ m/^SEG_(\d+)/)
  {
    $segmentationID = $1;
  }

  $query = "SELECT ID FROM $dsSchema.$dbName \
      WHERE segmentationID=$segmentationID ORDER BY name";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);

  $segMembers = "";
  while (($segmentID) = $dbOutput->fetchrow_array)
  {
    $segMembers = $segMembers . "SMT_" . "$segmentID,";
  }

  #trim trailing comma
  chop($segMembers);

  @segMembersArray = split(/,/, $segMembers);

  return(@segMembersArray);
}



#-------------------------------------------------------------------------------
#
# Apply any segment rules that exist to the specified segmentation
#

sub DSRsegmentation_apply_seg_rules
{
  my ($dbStub, $dbDim, $dbName, $query, $dbOutput, $segmentID, $rule);
  my ($matchSide, $matchType, $matchStr, $q_matchStr, $match_output);
  my ($itemID, $segmentationID, $attrID, $matchRegex, $id, $matchSeg);
  my ($matchSegment, $matchNumVal, $matchNumValUpper, $matchOp, $val);
  my ($segmentIDstr, $dbOutput1, $matchSegmentID, $sourceSegID);
  my ($srcSegmentID, $srcSegmentName, $q_segName, $destSegmentID);
  my ($filter1, $filter2, $filterSeg, $filterValStr, $status);
  my (@unsegmentedItems, @segmentMembers, @matchedSegs, @matchedItems);
  my (@matchSegments);
  my (%unsegItemHash, %attrValHash, %segmentHash, %membershipHash);
  my (%srcSegmentsHash, %curSegmentsHash, %segmentCheckHash);
  my (%filter1Hash, %filter2Hash);

  my ($db, $dsSchema, $dim, $segID) = @_;


  if ($dim eq "p")
  {
    $dbStub = "product_";
    $dbDim = "products";
  }
  elsif ($dim eq "g")
  {
    $dbStub = "geography_";
    $dbDim = "geographies";
  }
  elsif ($dim eq "t")
  {
    $dbStub = "time_";
    $dbDim = "timeperiods";
  }

  #grab any segmentation rules that exist for the specified segmentation
  $dbName = $dbStub . "seg_rules";
  $query = "SELECT segmentID, rule, filter1, filter2 FROM $dsSchema.$dbName \
      WHERE segmentationID=$segID ORDER BY step";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);

  #get the "check" hash we're going to use to make sure the rules' target
  #segment exists
  %segmentCheckHash = DSRseg_get_segments_hash($db, $dsSchema, $dim, $segID);

  #get a name-indexed hash of every unsegmented item in the data source
  @unsegmentedItems = DSRseg_get_unsegmented_items($db, $dsSchema, $dim, $segID);
  %unsegItemHash = map { $_ => 1 } @unsegmentedItems;

  #cycle through each segmentation rule we found
  while (($segmentID, $rule, $filter1, $filter2) = $dbOutput->fetchrow_array)
  {

    #make sure the target segment exists
    if ((length($segmentCheckHash{$segmentID}) < 1) && (!($rule =~ m/^SEGFILLDOWN/)))
    {
      next;
    }

    #if we're applying extra filters, get the list of items we can process
    undef(%filter1Hash);
    undef(%filter2Hash);
    if ($filter1 =~ m/^(\d+) (.*)$/)
    {
      $filterSeg = $1;
      $filterValStr = $2;

      $query = "SELECT itemID FROM $dsSchema.product_segment_item \
          WHERE segmentationID=$filterSeg AND segmentID IN ($filterValStr)";
      $match_output = $db->prepare($query);
      $status = $match_output->execute;
      struct_db_err($db, $status, $query);
      while (($itemID) = $match_output->fetchrow_array)
      {
        $filter1Hash{$itemID} = 1;
      }

      if ($filter2 =~ m/^(\d+) (.*)$/)
      {
        $filterSeg = $1;
        $filterValStr = $2;

        $query = "SELECT itemID FROM $dsSchema.product_segment_item \
            WHERE segmentationID=$filterSeg AND segmentID IN ($filterValStr)";
        $match_output = $db->prepare($query);
        $status = $match_output->execute;
        struct_db_err($db, $status, $query);
        while (($itemID) = $match_output->fetchrow_array)
        {
          $filter2Hash{$itemID} = 1;
        }
      }
    }
    #end of filtering setup code


    #process TEXT matching rules
    if ($rule =~ m/^TEXT (.*?) (.*?) (.*)$/)
    {
      $matchSide = $1;
      $matchType = $2;
      $matchStr = $3;

      #SQL-ify our match string
      if ($matchType eq "begin")
      {
        $matchStr = $matchStr . "%";
      }
      elsif ($matchType eq "end")
      {
        $matchStr = "%" . $matchStr;
      }
      else
      {
        $matchStr = "%" . $matchStr . "%";
      }

      #grab every item from the data source that matches the specified string
      $q_matchStr = $db->quote($matchStr);
      $query = "SELECT ID FROM $dsSchema.$dbDim WHERE name LIKE $q_matchStr";
      $match_output = $db->prepare($query);
      $status = $match_output->execute;
      struct_db_err($db, $status, $query);

      #run through the list of itemIDs that match the search string
      while (($itemID) = $match_output->fetchrow_array)
      {

        #if we're applying additional filtering
        if ((%filter1Hash) && ($filter1Hash{$itemID} != 1))
        {
          next;
        }
        if ((%filter2Hash) && ($filter2Hash{$itemID} != 1))
        {
          next;
        }

        #if we're only matching unsegmented items
        if (($matchSide eq "unsegmented") && ($unsegItemHash{$itemID} == 1))
        {
          $dbName = $dbStub . "segment_item";
          $query = "INSERT INTO $dsSchema.$dbName \
              (segmentationID, segmentID, itemID) \
              VALUES ($segID, $segmentID, $itemID)";
          $status = $db->do($query);
          struct_db_err($db, $status, $query);

          delete($unsegItemHash{$itemID});
        }

        #we're only matching segmented items
        elsif (($matchSide eq "segmented") && ($unsegItemHash{$itemID} != 1))
        {
          $dbName = $dbStub . "segment_item";
          $query = "UPDATE $dsSchema.$dbName SET segmentID=$segmentID \
              WHERE segmentationID=$segID AND itemID=$itemID";
          $status = $db->do($query);
          struct_db_err($db, $status, $query);
        }

        #we're matching any items
        elsif ($matchSide eq "any")
        {
          $dbName = $dbStub . "segment_item";
          $query = "INSERT INTO $dsSchema.$dbName \
              (segmentationID, segmentID, itemID) \
              VALUES ($segID, $segmentID, $itemID) \
              ON DUPLICATE KEY UPDATE segmentID=$segmentID";
          $status = $db->do($query);
          struct_db_err($db, $status, $query);

          delete($unsegItemHash{$itemID});
        }
      }
    }

    #process attribute matching rules
    if ($rule =~ m/^ATTR (.*?) (.*?) (.*?) (.*)$/)
    {
      $matchSide = $1;
      $attrID = $2;
      $matchType = $3;
      $matchStr = $4;

      #grab the hash of values for the selected attribute field
      %attrValHash = DSRattr_get_values_hash($db, $dsSchema, $dim, $attrID);

      #regex-ify our match string
      if ($matchType eq "begin")
      {
        $matchRegex = "^" . $matchStr;
      }
      elsif ($matchType eq "end")
      {
        $matchRegex = $matchStr . "\$";
      }
      else
      {
        $matchRegex = $matchStr;
      }

      #run through the attribute vals, and undef any that don't match
      foreach $id (keys %attrValHash)
      {
        if (!($attrValHash{$id} =~ m/$matchRegex/))
        {
          undef($attrValHash{$id});
        }
      }

      #run through the list of itemIDs with attrs that match the search string
      foreach $itemID (keys %attrValHash)
      {

        if (!defined($attrValHash{$itemID}))
        {
          next;
        }

        #if we're applying additional filtering
        if ((%filter1Hash) && ($filter1Hash{$itemID} != 1))
        {
          next;
        }
        if ((%filter2Hash) && ($filter2Hash{$itemID} != 1))
        {
          next;
        }

        #if we're only matching unsegmented items
        if (($matchSide eq "unsegmented") && ($unsegItemHash{$itemID} == 1))
        {
          $dbName = $dbStub . "segment_item";
          $query = "INSERT INTO $dsSchema.$dbName \
              (segmentationID, segmentID, itemID) \
              VALUES ($segID, $segmentID, $itemID)";
          $status = $db->do($query);
          struct_db_err($db, $status, $query);

          delete($unsegItemHash{$itemID});
        }

        #we're only matching segmented items
        elsif (($matchSide eq "segmented") && ($unsegItemHash{$itemID} != 1))
        {
          $dbName = $dbStub . "segment_item";
          $query = "UPDATE $dsSchema.$dbName SET segmentID=$segmentID \
              WHERE segmentationID=$segID AND itemID=$itemID";
          $status = $db->do($query);
          struct_db_err($db, $status, $query);
          delete($unsegItemHash{$itemID});
        }

        #we're matching any items
        elsif ($matchSide eq "any")
        {
          $dbName = $dbStub . "segment_item";
          $query = "INSERT INTO $dsSchema.$dbName \
              (segmentationID, segmentID, itemID) \
              VALUES ($segID, $segmentID, $itemID) \
              ON DUPLICATE KEY UPDATE segmentID=$segmentID";
          $status = $db->do($query);
          struct_db_err($db, $status, $query);

          delete($unsegItemHash{$itemID});
        }
      }
    }

    #process segment name matching rules
    if ($rule =~ m/^SEGMATCH (.*?) (.*?) (.*?) (.*)$/)
    {
      $matchSide = $1;
      $matchSeg = $2;
      $matchType = $3;
      $matchStr = $4;

      #get a hash of all segment names in the specified segmentation
      %segmentHash = DSRseg_get_segments_hash($db, $dsSchema, $dim, $matchSeg);

      #regex-ify our match string
      if ($matchType eq "begin")
      {
        $matchRegex = "^" . $matchStr;
      }
      elsif ($matchType eq "end")
      {
        $matchRegex = $matchStr . "\$";
      }
      else
      {
        $matchRegex = $matchStr;
      }

      #run through the segment names, and undef any that don't match
      foreach $id (keys %segmentHash)
      {
        if (!($segmentHash{$id} =~ m/$matchRegex/))
        {
          delete($segmentHash{$id});
        }
      }

      #turn the matching segment IDs into a comma-separated list
      $segmentIDstr = "";
      foreach $id (keys %segmentHash)
      {
        $segmentIDstr .= "$id,";
      }
      chop($segmentIDstr);

      #get the list of all items in the segments that matched
      $dbName = $dbStub . "segment_item";
      $query = "SELECT itemID FROM $dsSchema.$dbName \
          WHERE segmentID IN ($segmentIDstr)";
      $dbOutput1 = $db->prepare($query);
      $status = $dbOutput1->execute;
      struct_db_err($db, $status, $query);

      while (($itemID) = $dbOutput1->fetchrow_array)
      {

        #if we're applying additional filtering
        if ((%filter1Hash) && ($filter1Hash{$itemID} != 1))
        {
          next;
        }
        if ((%filter2Hash) && ($filter2Hash{$itemID} != 1))
        {
          next;
        }

        #if we're only matching unsegmented items
        if (($matchSide eq "unsegmented") && ($unsegItemHash{$itemID} == 1))
        {
          $dbName = $dbStub . "segment_item";
          $query = "INSERT INTO $dsSchema.$dbName \
              (segmentationID, segmentID, itemID) \
              VALUES ($segID, $segmentID, $itemID)";
          $status = $db->do($query);
          struct_db_err($db, $status, $query);

          delete($unsegItemHash{$itemID});
        }

        #we're only matching segmented items
        elsif (($matchSide eq "segmented") && ($unsegItemHash{$itemID} != 1))
        {
          $dbName = $dbStub . "segment_item";
          $query = "UPDATE $dsSchema.$dbName SET segmentID=$segmentID \
              WHERE segmentationID=$segID AND itemID=$itemID";
          $status = $db->do($query);
          struct_db_err($db, $status, $query);
        }

        #we're matching any items
        elsif ($matchSide eq "any")
        {
          $dbName = $dbStub . "segment_item";
          $query = "INSERT INTO $dsSchema.$dbName \
              (segmentationID, segmentID, itemID) \
              VALUES ($segID, $segmentID, $itemID) \
              ON DUPLICATE KEY UPDATE segmentID=$segmentID";
          $status = $db->do($query);
          struct_db_err($db, $status, $query);

          delete($unsegItemHash{$itemID});
        }
      }
    }

    #process segment membership rules
    if ($rule =~ m/^SEG (.*?) (.*?) (.*)$/)
    {
      $matchSide = $1;
      $matchSeg = $2;
      $matchSegment = $3;

      #for each segment we want to match in the source segmentation
      @matchSegments = split(",", $matchSegment);
      foreach $matchSegmentID (@matchSegments)
      {

        #get an array of all items contained in the specified segment
        @segmentMembers = DSRseg_get_segitems_array($db, $dsSchema, $dim, $matchSeg, $matchSegmentID);

        #run through the array of item IDs that are in the segment
        foreach $itemID (@segmentMembers)
        {

          #if we're applying additional filtering
          if ((%filter1Hash) && ($filter1Hash{$itemID} != 1))
          {
            next;
          }
          if ((%filter2Hash) && ($filter2Hash{$itemID} != 1))
          {
            next;
          }

          #if we're only matching unsegmented items
          if (($matchSide eq "unsegmented") && ($unsegItemHash{$itemID} == 1))
          {
            $dbName = $dbStub . "segment_item";
            $query = "INSERT INTO $dsSchema.$dbName \
                (segmentationID, segmentID, itemID) \
                VALUES ($segID, $segmentID, $itemID)";
            $status = $db->do($query);
            struct_db_err($db, $status, $query);

           delete($unsegItemHash{$itemID});
          }

          #we're only matching segmented items
          elsif (($matchSide eq "segmented") && ($unsegItemHash{$itemID} != 1))
          {
            $dbName = $dbStub . "segment_item";
            $query = "UPDATE $dsSchema.$dbName SET segmentID=$segmentID \
                WHERE segmentationID=$segID AND itemID=$itemID";
            $status = $db->do($query);
            struct_db_err($db, $status, $query);
          }

          #we're matching any items
          elsif ($matchSide eq "any")
          {
            $dbName = $dbStub . "segment_item";
            $query = "INSERT INTO $dsSchema.$dbName \
                (segmentationID, segmentID, itemID) \
                VALUES ($segID, $segmentID, $itemID) \
                ON DUPLICATE KEY UPDATE segmentID=$segmentID";
            $status = $db->do($query);
            struct_db_err($db, $status, $query);

            delete($unsegItemHash{$itemID});
          }
        }

      }
    }

    #process segment value rules
    if ($rule =~ m/^SEGVAL (.*?) (.*?) (.*?) (.*?) (.*)$/)
    {
      $matchSide = $1;
      $matchSeg = $2;
      $matchOp = $3;
      $matchNumVal = $4;
      $matchNumValUpper = $5;

      #get a hash of all segment names in the specified segmentation
      %segmentHash = DSRseg_get_segments_hash($db, $dsSchema, $dim, $matchSeg);

      #run through the segments, looking for matches
      undef(@matchedSegs);
      foreach $id (keys %segmentHash)
      {

        #extract the numerical part of the segment name, if there is one
        if ($segmentHash{$id} =~ m/([\.\d]+)/)
        {
          $val = $1;
        }
        else
        {
          next;
        }

        #look for a match against our current rule
        if ($matchOp eq "gt")
        {
          if ($val > $matchNumVal)
          {
            push(@matchedSegs, $id);
          }
        }
        elsif ($matchOp eq "lt")
        {
          if ($val < $matchNumVal)
          {
            push(@matchedSegs, $id);
          }
        }
        elsif ($matchOp eq "bt")
        {
          if (($val >= $matchNumVal) && ($val <= $matchNumValUpper))
          {
            push(@matchedSegs, $id);
          }
        }
        elsif ($matchOp eq "eq")
        {
          if ($val eq $matchNumVal)
          {
            push(@matchedSegs, $id);
          }
        }
        elsif ($matchOp eq "ge")
        {
          if ($val >= $matchNumVal)
          {
            push(@matchedSegs, $id);
          }
        }
        elsif ($matchOp eq "le")
        {
          if ($val <= $matchNumVal)
          {
            push(@matchedSegs, $id);
          }
        }

      }

      #build up an array of all items contained in the matching segment(s)
      undef(@matchedItems);
      foreach $id (@matchedSegs)
      {
        @segmentMembers = DSRseg_get_segitems_array($db, $dsSchema, $dim, $matchSeg, $id);
        push(@matchedItems, @segmentMembers);
      }

      #run through the array of item IDs in the matched segments
      foreach $itemID (@matchedItems)
      {

        #if we're applying additional filtering
        if ((%filter1Hash) && ($filter1Hash{$itemID} != 1))
        {
          next;
        }
        if ((%filter2Hash) && ($filter2Hash{$itemID} != 1))
        {
          next;
        }

        #if we're only matching unsegmented items
        if (($matchSide eq "unsegmented") && ($unsegItemHash{$itemID} == 1))
        {
          $dbName = $dbStub . "segment_item";
          $query = "INSERT INTO $dsSchema.$dbName \
              (segmentationID, segmentID, itemID) \
              VALUES ($segID, $segmentID, $itemID)";
          $status = $db->do($query);
          struct_db_err($db, $status, $query);

          delete($unsegItemHash{$itemID});
        }

        #we're only matching segmented items
        elsif (($matchSide eq "segmented") && ($unsegItemHash{$itemID} != 1))
        {
          $dbName = $dbStub . "segment_item";
          $query = "UPDATE $dsSchema.$dbName SET segmentID=$segmentID \
              WHERE segmentationID=$segID AND itemID=$itemID";
          $status = $db->do($query);
          struct_db_err($db, $status, $query);
        }

        #we're matching any items
        elsif ($matchSide eq "any")
        {
          $dbName = $dbStub . "segment_item";
          $query = "INSERT INTO $dsSchema.$dbName \
              (segmentationID, segmentID, itemID) \
              VALUES ($segID, $segmentID, $itemID) \
              ON DUPLICATE KEY UPDATE segmentID=$segmentID";
          $status = $db->do($query);
          struct_db_err($db, $status, $query);

          delete($unsegItemHash{$itemID});
        }
      }
    }

    #process segmentation fill-down rules
    if ($rule =~ m/^SEGFILLDOWN (.*)$/)
    {
      $sourceSegID = $1;

      #get the membership of items in the source segmentation
      %membershipHash = DSRseg_get_segment_membership_hash($db, $dsSchema, $dim, $sourceSegID);

      #get a hash of segment names from the source
      %srcSegmentsHash = DSRseg_get_segments_hash($db, $dsSchema, $dim, $sourceSegID);

      #get a hash of our current segmentation's segments, and reverse it
      %curSegmentsHash = DSRseg_get_segments_hash($db, $dsSchema, $dim, $segID);
      %curSegmentsHash = reverse(%curSegmentsHash);

      #get all remaining unsegmented items in our segmentation
      @unsegmentedItems = DSRseg_get_unsegmented_items($db, $dsSchema, $dim, $segID);

      #put any unsegmented items into the same segment they're in in the source
      #segmentation
      foreach $itemID (@unsegmentedItems)
      {

        #if we're applying additional filtering
        if ((%filter1Hash) && ($filter1Hash{$itemID} != 1))
        {
          next;
        }
        if ((%filter2Hash) && ($filter2Hash{$itemID} != 1))
        {
          next;
        }

        #get the ID of the segment our item is a member of in the source
        $srcSegmentID = $membershipHash{$itemID};

        #get the name of the segment our item is a member of
        $srcSegmentName = $srcSegmentsHash{$srcSegmentID};

        #if the source segment name is blank (e.g., invalid or unsegmented)
        if (length($srcSegmentName) < 1)
        {
          next;
        }

        #see if we already have a matching segment in the destination segment
        $destSegmentID = $curSegmentsHash{$srcSegmentName};

        #if we don't already have a segment with that name, add it
        if ($destSegmentID < 1)
        {
          $q_segName = $db->quote($srcSegmentName);
          $dbName = $dbStub . "segment";
          $query = "INSERT INTO $dsSchema.$dbName (segmentationID, name) \
              VALUES ($segID, $q_segName)";
          $status = $db->do($query);
          struct_db_err($db, $status, $query);

          #and now add it to our hash for future use in processing this rule
          $destSegmentID = $db->{q{mysql_insertid}};
          $curSegmentsHash{$srcSegmentName} = $destSegmentID;
        }

        #segment our unsegmented item to match
        $dbName = $dbStub . "segment_item";
        $query = "INSERT INTO $dsSchema.$dbName \
            (segmentationID, segmentID, itemID) \
            VALUES ($segID, $destSegmentID, $itemID)";
        $status = $db->do($query);
        struct_db_err($db, $status, $query);
      }
    }

    #process catch-all rules
    if ($rule =~ m/^CATCHALL$/)
    {

      #get all remaining unsegmented items in the segmentation
      @unsegmentedItems = DSRseg_get_unsegmented_items($db, $dsSchema, $dim, $segID);

      #put any we found in the specified segment
      $dbName = $dbStub . "segment_item";
      foreach $itemID (@unsegmentedItems)
      {

        #if we're applying additional filtering
        if ((%filter1Hash) && ($filter1Hash{$itemID} != 1))
        {
          next;
        }
        if ((%filter2Hash) && ($filter2Hash{$itemID} != 1))
        {
          next;
        }

        $query = "INSERT INTO $dsSchema.$dbName \
            (segmentationID, segmentID, itemID) \
            VALUES ($segID, $segmentID, $itemID) ON DUPLICATE KEY UPDATE segmentID=$segmentID";
        $status = $db->do($query);
        struct_db_err($db, $status, $query);
      }
    }
  }
}



#-------------------------------------------------------------------------------
#
# "Expand" a segment into the member items it's comprised of
#

sub DSRsegment_expand_items
{
  my ($query, $dbOutput, $status, $dbName, $members);
  my ($segMembers, $itemID);

  my ($db, $dsSchema, $dim, $segmentID) = @_;


  #figure out the table containing aggregates for our dimension
  if ($dim eq "p")
  {
    $dbName = "product_segment_item";
  }
  elsif ($dim eq "g")
  {
    $dbName = "geography_segment_item";
  }
  elsif ($dim eq "t")
  {
    $dbName = "time_segment_item";
  }

  #if we were handed the ID in the form SMT_###, strip off the prepend
  if ($segmentID =~ m/^SMT_(\d+)/)
  {
    $segmentID = $1;
  }

  $query = "SELECT itemID FROM $dsSchema.$dbName WHERE segmentID=$segmentID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);

  $segMembers = "";
  while (($itemID) = $dbOutput->fetchrow_array)
  {
    $segMembers .= "$itemID,";
  }

  #trim trailing comma
  chop($segMembers);

  return($segMembers);
}



#-------------------------------------------------------------------------------
#
# Return the ID of the segment the specified item belongs to in the specified
# segmentation. Returns "undefined" if the item is not a member of a segment.
#

sub DSRseg_get_item_membership
{
  my ($query, $dbOutput, $status, $dbName, $id, $segmentID);

  my ($db, $dsSchema, $dim, $segID, $itemID) = @_;


  undef($segmentID);

  #if we're anything but a base item, return undefined
  if (!($itemID =~ m/^\d+$/))
  {
    return($segmentID);
  }

  if ($dim eq "p")
  {
    $dbName = "product_segment_item";
  }
  elsif ($dim eq "g")
  {
    $dbName = "geography_segment_item";
  }
  elsif ($dim eq "t")
  {
    $dbName = "time_segment_item";
  }

  $query = "SELECT segmentID FROM $dsSchema.$dbName \
      WHERE segmentationID=$segID AND itemID=$itemID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);

  ($segmentID) = $dbOutput->fetchrow_array;

  return($segmentID);
}



#-------------------------------------------------------------------------------
#
# Create a hash of a segmentation's segment names and IDs, hashed by ID. If
# no segmentation ID is supplied, add every segment in the data source to the
# hash.
#

sub DSRseg_get_segments_hash
{
  my ($query, $dbOutput, $status, $dbName, $id, $name, $whereClause);
  my (%segmentsHash);

  my ($db, $dsSchema, $dim, $segID) = @_;


  if ($dim eq "p")
  {
    $dbName = "product_segment";
  }
  elsif ($dim eq "g")
  {
    $dbName = "geography_segment";
  }
  elsif ($dim eq "t")
  {
    $dbName = "time_segment";
  }

  if ($segID > 0)
  {
    $whereClause = "WHERE segmentationID=$segID";
  }

  $query = "SELECT id, name FROM $dsSchema.$dbName $whereClause";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);

  while (($id, $name) = $dbOutput->fetchrow_array)
  {
    $segmentsHash{$id} = $name;
  }

  return(%segmentsHash);
}



#-------------------------------------------------------------------------------
#
# Create an array of a segmentation's segment IDs, alphasorted by name
#

sub DSRseg_get_segments_array
{
  my ($query, $dbOutput, $status, $dbName, $id, $name);
  my (@segmentsArray);

  my ($db, $dsSchema, $dim, $segID) = @_;


  undef(@segmentsArray);

  if ($dim eq "p")
  {
    $dbName = "product_segment";
  }
  elsif ($dim eq "g")
  {
    $dbName = "geography_segment";
  }
  elsif ($dim eq "t")
  {
    $dbName = "time_segment";
  }

  $query = "SELECT ID FROM $dsSchema.$dbName WHERE segmentationID=$segID \
      ORDER BY name";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);

  while (($id) = $dbOutput->fetchrow_array)
  {
    push(@segmentsArray, $id);
  }

  return(@segmentsArray);
}



#-------------------------------------------------------------------------------
#
# Create a return a hash of every base item's segment membership for the
# specified segmentation.
#

sub DSRseg_get_segment_membership_hash
{
  my ($query, $dbOutput, $status, $dbName, $id, $name, $itemID, $segmentID);
  my (%membershipHash);

  my ($db, $dsSchema, $dim, $segmentationID) = @_;


  if ($dim eq "p")
  {
    $dbName = "product_segment_item";
  }
  elsif ($dim eq "g")
  {
    $dbName = "geography_segment_item";
  }
  elsif ($dim eq "t")
  {
    $dbName = "time_segment_item";
  }

  $query = "SELECT itemID, segmentID FROM $dsSchema.$dbName \
      WHERE segmentationID=$segmentationID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);

  while (($itemID, $segmentID) = $dbOutput->fetchrow_array)
  {
    $membershipHash{$itemID} = $segmentID;
  }

  return(%membershipHash);
}



#-------------------------------------------------------------------------------
#
# Create and return a hash of comma-separated items IDs, keyed by segment IDs,
# for the specified segmentation. Used for UI to display segmentation trees.
#

sub DSRseg_get_segment_item_hash
{
  my ($query, $dbOutput, $status, $dbName, $id, $name, $itemID, $segmentID);
  my ($dimDB);
  my (%segmentHash);

  my ($db, $dsSchema, $dim, $segmentationID) = @_;


  if ($dim eq "p")
  {
    $dbName = "product_segment_item";
    $dimDB = "products";
  }
  elsif ($dim eq "g")
  {
    $dbName = "geography_segment_item";
    $dimDB = "geographies";
  }
  elsif ($dim eq "t")
  {
    $dbName = "time_segment_item";
    $dimDB = "timeperiods";
  }

  $query = "SELECT $dsSchema.$dbName.itemID, $dsSchema.$dbName.segmentID FROM $dsSchema.$dbName \
      INNER JOIN $dsSchema.$dimDB on $dsSchema.$dbName.itemID = $dsSchema.$dimDB.ID \
      WHERE $dsSchema.$dbName.segmentationID = $segmentationID ORDER BY $dsSchema.$dimDB.name";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);

  while (($itemID, $segmentID) = $dbOutput->fetchrow_array)
  {
    $segmentHash{$segmentID} .= $itemID . ",";
  }

  #trim off trailing commas
  foreach $segmentID (keys %segmentHash)
  {
    chop($segmentHash{$segmentID});
  }

  return(%segmentHash);
}



#-------------------------------------------------------------------------------
#
# Create an array of a segment's member item IDs
#

sub DSRseg_get_segitems_array
{
  my ($query, $dbOutput, $status, $dbName, $id);
  my (@itemIDs);

  my ($db, $dsSchema, $dim, $segID, $segmentID) = @_;


  if ($dim eq "p")
  {
    $dbName = "product_segment_item";
  }
  elsif ($dim eq "g")
  {
    $dbName = "geography_segment_item";
  }
  elsif ($dim eq "t")
  {
    $dbName = "time_segment_item";
  }

  $query = "SELECT itemID FROM $dsSchema.$dbName \
      WHERE segmentationID=$segID AND segmentID=$segmentID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);

  while (($id) = $dbOutput->fetchrow_array)
  {
    push(@itemIDs, $id);
  }

  return(@itemIDs);
}



#-------------------------------------------------------------------------------
#
# Create an array of every base item in the dimension that isn't assigned
# a segment in the specified segmentation. Handy for certain operations like
# creating an "All Others" segment (need to identify which items in a dimension
# already belong to a segmentation).
#

sub DSRseg_get_unsegmented_items
{
  my ($query, $dbOutput, $status, $dbName, $id, $itemID);
  my (@itemIDs, @unsegmentedIDs);
  my (%segItemsHash);

  my ($db, $dsSchema, $dim, $segID) = @_;


  #get an array of every base item ID in the specified dimension
  @itemIDs = dsr_get_itemIDs_array($db, $dsSchema, $dim);

  #determine which database holds segment info for our dimension
  if ($dim eq "p")
  {
    $dbName = "product_segment_item";
  }
  elsif ($dim eq "g")
  {
    $dbName = "geography_segment_item";
  }
  elsif ($dim eq "t")
  {
    $dbName = "time_segment_item";
  }

  #get hash of all items already in the segmentation
  undef(%segItemsHash);
  $query = "SELECT itemID FROM $dsSchema.$dbName WHERE segmentationID = $segID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);

  while (($id) = $dbOutput->fetchrow_array)
  {
    $segItemsHash{$id} = 1;
  }

  #run through the list of all items, and add any that aren't already segmented
  #to the array of unsegmented items
  foreach $itemID (@itemIDs)
  {
    if (!($segItemsHash{$itemID}))
    {
      push(@unsegmentedIDs, $itemID);
    }
  }

  return(@unsegmentedIDs);
}



#-------------------------------------------------------------------------------
#
# Return the name of the segmentation hierarchy that has the specified ID
#

sub DSRseghier_id_to_name
{
  my ($dbName, $query, $dbOutput, $name, $status);

  my ($db, $dsSchema, $dim, $segHierID) = @_;


  #figure out the table containing segmentations for our dimension
  if ($dim eq "p")
  {
    $dbName = "product_seghierarchy";
  }
  elsif ($dim eq "g")
  {
    $dbName = "geography_seghierarchy";
  }
  elsif ($dim eq "t")
  {
    $dbName = "time_seghierarchy";
  }

  #if we were handed the ID in the form SEG_nnn, strip off the prepend
  if ($segHierID =~ m/^SHS_(\d+)/)
  {
    $segHierID = $1;
  }

  $query = "SELECT name FROM $dsSchema.$dbName WHERE ID=$segHierID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);

  ($name) = $dbOutput->fetchrow_array;

  return($name);
}



#-------------------------------------------------------------------------------
#
# Return an array of the IDs of the segmentations that make up the
# hierarchy (in the order they're used by the hierarchy).
#

sub DSRseghier_get_segs_array
{
  my ($query, $dbOutput, $dbName, $segLevelStr);
  my (@segLevelIDs);

  my ($db, $dsSchema, $segHierID) = @_;

  $query = "SELECT segmentations FROM $dsSchema.product_seghierarchy \
      WHERE ID=$segHierID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);
  ($segLevelStr) = $dbOutput->fetchrow_array;
  @segLevelIDs = split(',', $segLevelStr);

  return(@segLevelIDs);
}



#-------------------------------------------------------------------------------
#
# Return the segmentation hierarchy "chain" for every item in the specified
# data source for the specified segmentation hierarchy. The chain is just
# a delimited string with the segment ID the item belongs to at each level
# in the hierarchy.
# For example, the chain "1_4_2" might mean that a particular item is a
# member of the "Alpha" brand segment (segmentID=1), the "Cherry" flavor
# segment (segmentID-4), and the "Can" package segment (segmentID=2).
#

sub DSRseghier_get_item_chain_hash
{
  my ($query, $dbOutput, $dbName, $segmentationIDs, $segmentationID);
  my ($itemID, $status, $count, $segCount);
  my (@segmentations);
  my (%membershipHash, %chain);

  my ($db, $dsSchema, $dim, $segHierID) = @_;


  #check to make sure our cache is OK to use (DS hasn't changed)
  DSRstructures_cache_ok($dsSchema);

  #determine which database holds segment hierarchy info for our dimension
  if ($dim eq "p")
  {
    $dbName = "product_seghierarchy";
  }
  elsif ($dim eq "g")
  {
    $dbName = "geography_seghierarchy";
  }
  elsif ($dim eq "t")
  {
    $dbName = "time_seghierarchy";
  }

  if (($segHierID =~ m/^SHS_(\d+)/) || ($segHierID =~ m/^SHR_(\d+)/))
  {
    $segHierID = $1;
  }

  #get the in-order list of the segmentations that make up the hierarchy
  $query = "SELECT segmentations FROM $dsSchema.$dbName WHERE ID=$segHierID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);
  ($segmentationIDs) = $dbOutput->fetchrow_array;

  #turn the list of segmentations into an in-order array
  @segmentations = split(',', $segmentationIDs);

  #for every segmentation, add each item's segmentID to its chain
  foreach $segmentationID (@segmentations)
  {

    #get a hash of every base item's segment membership in the segmentation,
    #and cache it for potential future use
    #NB: because we're storing a reference to the hash, we need to deep clone
    #   (dclone) it to keep the next call from overwriting the values. This is
    #   perl trickery - don't touch it until fully reading and understanding the
    #   dclone docs.
    if ((keys %{$_cachedSegMemberships[$segmentationID]}) < 1)
    {
      %membershipHash = DSRseg_get_segment_membership_hash($db, $dsSchema, $dim, $segmentationID);
      $_cachedSegMemberships[$segmentationID] = dclone(\%membershipHash);
    }

    #add the memberships from this level to the chain
    foreach $itemID (keys %{$_cachedSegMemberships[$segmentationID]})
    {
      $chain{$itemID} .= $_cachedSegMemberships[$segmentationID]->{$itemID} . "_";
    }
  }

  #trim out items that aren't members of segments in all segmentations in the
  #hierarchy
  $segCount = scalar(@segmentations);
  foreach $itemID (keys %chain)
  {
    $count = ($chain{$itemID} =~ tr/_//);
    if ($count ne $segCount)
    {
      delete($chain{$itemID});
    }
  }

  #the above loops leave an extra "_" at the end of each chain - chop it
  foreach $itemID (keys %chain)
  {
    chop($chain{$itemID});
  }

  return(%chain);
}



#-------------------------------------------------------------------------------
#
# Return array of the segmentation hierarchy "chains" for the specified level in
# the specified segmentation hierarchy, for the specified segments
# Primarily used during cube build process.
#

sub DSRsegHier_expand_segments
{
  my ($query, $dbOutput, $dbName, $segmentationID, $segmentationIDs, $i, $key);
  my ($levelIdx, $done, $levelRegEx, $matchLevelIdx, $segmentID, $regex);
  my ($chainStr, $id, $status);
  my (@chain, @segmentations, @segments, @segmentRegExes, @segHierSegments);
  my (%itemNameHash, %itemChain, %levelChains, %tmp);

  my ($db, $dsSchema, $dim, $segHierID, $segHierLevelID, $segmentationID, $segmentStr) = @_;


  #if we were handed the ID in the form SHS_###, strip off the prepend
  if (($segHierID =~ m/^SHS_(\d+)/) || ($segHierID =~ m/^SHR_(\d+)/))
  {
    $segHierID = $1;
  }

  #get our item names (for sorting by name before returning)
  %itemNameHash = dsr_get_item_name_hash($db, $dsSchema, $dim);

  #determine which database holds segment hierarchy info for our dimension
  if ($dim eq "p")
  {
    $dbName = "product_seghierarchy";
  }
  elsif ($dim eq "g")
  {
    $dbName = "geography_seghierarchy";
  }
  elsif ($dim eq "t")
  {
    $dbName = "time_seghierarchy";
  }

  #start by getting the item-level chain for the segmentation hierarchy (this
  #saves us lots of duplicate code here, and guarantees we only get hierarchy
  #items that have items eventually associated with them)
  %itemChain = DSRseghier_get_item_chain_hash($db, $dsSchema, $dim, $segHierID);

  #figure out which step in the tree is our requested hierarchy level
  $query = "SELECT segmentations FROM $dsSchema.$dbName WHERE ID=$segHierID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);
  ($segmentationIDs) = $dbOutput->fetchrow_array;

  @segmentations = split(',', $segmentationIDs);

  #figure out which tree level corresponds to the specified segmentation level
  $levelIdx = 0;
  $done = 0;
  while (($done != 1) && ($levelIdx < 128))
  {
    if ($segmentations[$levelIdx] eq $segHierLevelID)
    {
      $done = 1;
    }
    else
    {
      $levelIdx++;
    }
  }

  #if we didn't find a matching segmentation level, return empty array
  if ($done == 0)
  {
    undef(@segHierSegments);
    return(@segHierSegments);
  }

  #build up the regex we use to cut all of the chains down to the level we want
  $levelRegEx = "(";
  for ($i = 0; $i <= $levelIdx; $i++)
  {
    $levelRegEx .= "\\d+_";
  }
  chop($levelRegEx);
  $levelRegEx .= ").*";

  #run through the hash of all chains, cut them down to the level we want, and
  #build a hash of those segment chains
  undef(%levelChains);
  foreach $key (keys %itemChain)
  {
    if ($itemChain{$key} =~ m/$levelRegEx/)
    {
      $levelChains{$1} = 1;
    }
  }

  #figure out which tree level corresponds to the segmentation level that the
  #user wants to match segments against
  $matchLevelIdx = 0;
  $done = 0;
  while ($done != 1)
  {
    if ($segmentations[$matchLevelIdx] eq $segmentationID)
    {
      $done = 1;
    }
    elsif ($matchLevelIdx > 20)
    {
      undef(@segHierSegments);
      return(@segHierSegments);
    }
    else
    {
      $matchLevelIdx++;
    }
  }

  #construct an array of regexes that will match the chains that fit the user's
  #criteria for segments
  @segments = split(' ', $segmentStr);
  undef(@segmentRegExes);
  foreach $segmentID (@segments)
  {
    $regex = "^";
    for ($i = 0; $i < $matchLevelIdx; $i++)
    {
      $regex .= "\\d+_";
    }
    $regex .= $segmentID;
    $regex .= "(_.*)?\$";

    push(@segmentRegExes, $regex);
  }

  #run our segment chains against our array of regexes, and discard any chains
  #that don't match
  foreach $chainStr (keys %levelChains)
  {
    foreach $regex (@segmentRegExes)
    {
      if ($chainStr =~ m/$regex/)
      {
        $levelChains{$chainStr} = 10;
      }
    }
  }

  #build a hash of our hierarchy level names indexed by their IDs
  undef(%tmp);
  foreach $chainStr (keys %levelChains)
  {
    if ($levelChains{$chainStr} == 10)
    {
      $id = "SHS_$segHierID" . "_" .$chainStr;
      $tmp{$id} = $itemNameHash{$id};
    }
  }

  #convert the hash to an array, in name order
  undef(@segHierSegments);
  foreach $id (sort {$tmp{$a} cmp $tmp{$b}} keys %tmp)
  {
    push(@segHierSegments, $id);
  }

  return(@segHierSegments);
}



#-------------------------------------------------------------------------------
#
# "Expand" a segment hierarchy "virtual segment" into the base items that it
# contains.
#

sub DSRseghier_expand_items
{
  my ($query, $dbOutput, $status, $dbName, $segMembers, $level, $segHierID);
  my ($segmentationStr, $memberStr, $itemID, $filterIDStr, $idx, $curID);
  my (@segmentations, @segmentIDs, @filterIDs, @members);
  my (%filterHash);

  my ($db, $dsSchema, $dim, $shsID) = @_;


  #figure out the table containing aggregates for our dimension
  if ($dim eq "p")
  {
    $dbName = "product_seghierarchy";
  }
  elsif ($dim eq "g")
  {
    $dbName = "geography_seghierarchy";
  }
  elsif ($dim eq "t")
  {
    $dbName = "time_seghierarchy";
  }

  #if we were handed the ID in the form SHS_###, strip off the prepend
  if ($shsID =~ m/^SHS_(\d+)/)
  {
    $segHierID = $1;
  }

  #get the IDs of the segmentations that make up each level of the hierarchy
  $query = "SELECT segmentations FROM $dsSchema.$dbName WHERE ID=$segHierID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);

  ($segmentationStr) = $dbOutput->fetchrow_array;
  @segmentations = split(',', $segmentationStr);

  #chop down the SHS_xx_xx_xx chain we were passed into its segments
  $shsID =~ m/^SHS_\d+_(.*)/;
  @segmentIDs = split('_', $1);

  #build up the list of base items at this level in the hierarchy by getting
  #the members of the segment at each level, and removing any that aren't
  #in every level we've seen so far.

  #start by getting the members at the first level
  $memberStr = DSRsegment_expand_items($db, $dsSchema, $dim, $segmentIDs[0]);
  @members = split(',', $memberStr);

  #now cycle through each remaining level, removing members that don't exist
  $level = 1;
  while (defined($segmentIDs[$level]))
  {

    #build a hash of all the item IDs in the current level's segment
    $filterIDStr = DSRsegment_expand_items($db, $dsSchema, $dim, $segmentIDs[$level]);
    @filterIDs = split(',', $filterIDStr);
    undef(%filterHash);
    foreach $itemID (@filterIDs)
    {
      $filterHash{$itemID} = 1;
    }

    #cycle through every element in the members array, and remove any item IDs
    #that don't appear in the current level's segment
    $idx = 0;
    while (defined($members[$idx]))
    {
      $curID = $members[$idx];

      if ($filterHash{$curID} != 1)
      {
        splice(@members, $idx, 1);
      }
      else
      {
        $idx++;
      }
    }

    $level++;
  }

  $segMembers = join(',', @members);
  return($segMembers);
}



#-------------------------------------------------------------------------------
#
# Create the facts table entries for the specified merged item; unsegment
# child items from any segmentation they may be a part of
#

sub DSRmergedprod_create
{
  my ($query, $dbOutput, $mergedItemsStr, $timeID, $geoID, $measureQuery);
  my ($measureVal, $measureCol, $measureID, $aggRule, $status, $primaryProd);
  my ($segmentationID, $segmentID, $id, $listID, $script, $origScript, $aggID);
  my ($attributeID, $value, $alias, $q_alias, $SQLmeasureNames, $measureValues);
  my ($val);
  my (@geoIDs, @timeIDs, @measureIDs, @childIDs, @measureVals);

  my ($db, $dsSchema, $mergedID) = @_;


  #get the child product IDs, in CSV string form
  $query = "SELECT primaryProd, mergedProds FROM $dsSchema.products_merged \
      WHERE mergedID=$mergedID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);
  ($primaryProd, $mergedItemsStr) = $dbOutput->fetchrow_array;

  #set the merged meta product to have the primary product's alias
  $query = "SELECT alias FROM $dsSchema.products WHERE ID=$primaryProd";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);
  ($alias) = $dbOutput->fetchrow_array;

  $q_alias = $db->quote($alias);
  $query = "UPDATE $dsSchema.products SET alias=$q_alias WHERE ID=$mergedID";
  $status = $db->do($query);
  struct_db_err($db, $status, $query);

  #put the merged meta product into the primary product's segments
  #NB: if this is called when rebuilding a merged item, it's effectively a
  #    no-op since the primaryProd is no longer segmented
  $query = "SELECT segmentationID, segmentID FROM $dsSchema.product_segment_item \
      WHERE itemID=$primaryProd";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);

  while (($segmentationID, $segmentID) = $dbOutput->fetchrow_array)
  {
    $query = "INSERT INTO $dsSchema.product_segment_item \
        (segmentationID, segmentID, itemID) \
        VALUES ($segmentationID, $segmentID, $mergedID)";
    $status = $db->do($query);
    struct_db_err($db, $status, $query);
  }

  #remove the child merged items from all segmentations (if they're left behind,
  #they'll effectively double the aggregated values of the segment for those
  #products)
  @childIDs = split(',', $mergedItemsStr);
  foreach $id (@childIDs)
  {
    $query = "DELETE FROM $dsSchema.product_segment_item WHERE itemID = $id";
    $status = $db->do($query);
    struct_db_err($db, $status, $query);
  }

  #put the merged meta product in place of the primary product in lists, remove
  #child merged items from lists
  $query = "SELECT ID, script FROM $dsSchema.product_list";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);

  while (($listID, $script) = $dbOutput->fetchrow_array)
  {
    $origScript = $script;
    $script =~ s/M:$primaryProd\,/M:$mergedID\,/;
    $script =~ s/M:$primaryProd$/M:$mergedID/;

    foreach $id (@childIDs)
    {
      $script =~ s/M:$id\,//;
      $script =~ s/M:$id$//;
    }

    #if we altered the list definition script
    if ($origScript ne $script)
    {
      $query = "UPDATE $dsSchema.product_list SET script='$script' WHERE ID=$listID";
      $status = $db->do($query);
      struct_db_err($db, $status, $query);
      Lib::DataSel::datasel_expand_script($db, $dsSchema, $listID, "p", "l");
    }
  }

  #put the merged meta product in place of the primary product in aggregates,
  #remove child merged items from aggregates
  $query = "SELECT ID, addScript FROM $dsSchema.product_aggregate";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);

  while (($aggID, $script) = $dbOutput->fetchrow_array)
  {
    $origScript = $script;
    $script =~ s/M:$primaryProd\,/M:$mergedID\,/;
    $script =~ s/M:$primaryProd$/M:$mergedID/;

    foreach $id (@childIDs)
    {
      $script =~ s/M:$id\,//;
      $script =~ s/M:$id$//;
    }

    #if we altered the aggregate definition script
    if ($origScript ne $script)
    {
      $query = "UPDATE $dsSchema.product_aggregate SET addScript='$script' \
          WHERE ID=$aggID";
      $status = $db->do($query);
      struct_db_err($db, $status, $query);
      Lib::DataSel::datasel_expand_script($db, $dsSchema, $aggID, "p", "a");
      Lib::DataSel::datasel_expand_script($db, $dsSchema, $aggID, "p", "as");
    }
  }

  #use the primary product's attributes (esp UPC) as the attributes for the
  #new merged product
  $query = "SELECT attributeID, value FROM $dsSchema.product_attribute_values \
      WHERE itemID=$primaryProd";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);

  while (($attributeID, $value) = $dbOutput->fetchrow_array)
  {
    $query = "INSERT INTO $dsSchema.product_attribute_values \
        (attributeID, itemID, value) VALUES ($attributeID, $mergedID, '$value') \
        ON DUPLICATE KEY UPDATE value='$value'";
    $status = $db->do($query);
    struct_db_err($db, $status, $query);
  }

  #get an array of every base geography
  undef(@geoIDs);
  $query = "SELECT ID FROM $dsSchema.geographies";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);
  while (($geoID) = $dbOutput->fetchrow_array)
  {
    push(@geoIDs, $geoID);
  }

  #get an array of every base time
  undef(@timeIDs);
  $query = "SELECT ID FROM $dsSchema.timeperiods";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);
  while (($timeID) = $dbOutput->fetchrow_array)
  {
    push(@timeIDs, $timeID);
  }

  #get an array of every measure
  undef(@measureIDs);
  $query = "SELECT ID FROM $dsSchema.measures";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);
  while (($measureID) = $dbOutput->fetchrow_array)
  {
    push(@measureIDs, $measureID);
  }

  #create VALUES portion of SQL query we use to insert agg'd values
  foreach $measureID (@measureIDs)
  {
    $measureCol = "measure_" . $measureID;
    $SQLmeasureNames .= "$measureCol,";

    $aggRule = Lib::DSRMeasures::DSRmeasures_get_agg_rule($db, $dsSchema, $measureID, "merge");

    #if the measure doesn't have an agg rule defined, set it to NULL
    if ((!defined($aggRule)) || ($aggRule eq "None"))
    {
      $measureQuery .= "NULL,";
    }
    else
    {
      $measureQuery .= "$aggRule($measureCol),";
    }
  }
  chop($SQLmeasureNames);
  chop($measureQuery);

  #foreach combo of time and geo, create the merged item and insert into facts
  foreach $geoID (@geoIDs)
  {
    foreach $timeID (@timeIDs)
    {
        $query = "SELECT $measureQuery FROM $dsSchema.facts \
            WHERE productID IN ($mergedItemsStr) AND geographyID=$geoID AND timeID=$timeID";

        $dbOutput = $db->prepare($query);
        $status = $dbOutput->execute;
        struct_db_err($db, $status, $query);
        @measureVals = $dbOutput->fetchrow_array;

        $measureValues = "";
        foreach $val (@measureVals)
        {
          if (length($val) < 1)
          {
            $val = "NULL";
          }
          $measureValues .= "$val,";
        }
        chop($measureValues);

      #insert the aggregated measure values into the table
      $query = "INSERT INTO $dsSchema.facts \
          (productID, geographyID, timeID, $SQLmeasureNames) \
          VALUES ($mergedID, $geoID, $timeID, $measureValues)";
      $status = $db->do($query);
      struct_db_err($db, $status, $query);
    }
  }
}



#-------------------------------------------------------------------------------
#
# Re-calculate the values for every merged item in the specified data
# source. Called after a data source is updated to incorporate any changes
# made to the underlying product item data.
# NB: Make sure this is called before recalculating any calculated measures.
#

sub DSRmergedprod_update
{
  my ($query, $dbOutput, $mergedID, $status);
  my (@mergedProds);

  my ($db, $dsSchema, $dsUpdateID) = @_;


  #pull the list of merged products from the data source
  $query = "SELECT ID FROM $dsSchema.products WHERE merged=1";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);

  undef(@mergedProds);
  while (($mergedID) = $dbOutput->fetchrow_array)
  {
    push(@mergedProds, $mergedID);
  }

  #foreach merged product, delete its entries from the facts table
  foreach $mergedID (@mergedProds)
  {
    $query = "DELETE FROM $dsSchema.facts WHERE productID=$mergedID";
    $status = $db->do($query);
    struct_db_err($db, $status, $query);
  }

  #foreach merged product, recalculate it
  foreach $mergedID (@mergedProds)
  {
    DSRmergedprod_create($db, $dsSchema, $mergedID);

    #if we're doing this as part of a DS update, set the update ID
    if ($dsUpdateID > 0)
    {
      $query = "UPDATE $dsSchema.facts SET updateID=$dsUpdateID \
          WHERE productID=$mergedID";
      $status = $db->do($query);
      struct_db_err($db, $status, $query);
    }
  }
}



#-------------------------------------------------------------------------------
#
# Get the master (parent) item of the specified merged (child) item. If the item
# ID we're supplied isn't a child product, return undefined.
#

sub DSRmergedprod_get_primary
{
  my ($query, $dbOutput, $primaryID, $status);

  my ($db, $dsSchema, $productID) = @_;


  $query = "SELECT mergedID FROM $dsSchema.products_merged \
      WHERE mergedProds LIKE '%,$productID,%' OR mergedProds LIKE '$productID,%' OR mergedProds LIKE '%,$productID' LIMIT 1";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);
  ($primaryID) = $dbOutput->fetchrow_array;

  return($primaryID);
}



#-------------------------------------------------------------------------------
#
# Return an array of the base items that make up a merged item. If the item ID
# we're supplied isn't a merged item, return undefined.
#

sub DSRmergedprod_get_members
{
  my ($query, $dbOutput, $productIDs, $status);
  my (@members);

  my ($db, $dsSchema, $mergedID) = @_;


  undef(@members);

  $query = "SELECT mergedProds FROM $dsSchema.products_merged \
      WHERE mergedID = $mergedID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);
  ($productIDs) = $dbOutput->fetchrow_array;

  @members = split(',', $productIDs);

  return(@members);
}



#-------------------------------------------------------------------------------
#
# Unwind a merged product
#

sub DSRmergedprod_unwind
{
  my ($query, $dbOutput, $mergedIDsStr, $mergedID, $status, $primaryProd);
  my ($segmentationID, $segmentID, $listID, $script, $origScript, $aggID, $id);
  my (@mergedIDs);

  my ($db, $dsSchema, $mergedID) = @_;


  #get the IDs of the products that are merged together
  $query = "SELECT primaryProd, mergedProds FROM $dsSchema.products_merged \
      WHERE mergedID = $mergedID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);
  ($primaryProd, $mergedIDsStr) = $dbOutput->fetchrow_array;

  @mergedIDs = split(',', $mergedIDsStr);

  #put the primary product back in its segments, unsegment everything else
  $query = "SELECT segmentationID, segmentID FROM $dsSchema.product_segment_item \
      WHERE itemID=$mergedID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);

  while (($segmentationID, $segmentID) = $dbOutput->fetchrow_array)
  {
    $query = "INSERT INTO $dsSchema.product_segment_item \
        (segmentationID, segmentID, itemID) \
        VALUES ($segmentationID, $segmentID, $primaryProd)";
    $status = $db->do($query);
    struct_db_err($db, $status, $query);

    $query = "DELETE FROM $dsSchema.product_segment_item WHERE itemID=$mergedID";
    $status = $db->do($query);
    struct_db_err($db, $status, $query);
  }

  #remove any attribute data we carried over for the merged item
  $query = "DELETE FROM $dsSchema.product_attribute_values WHERE itemID=$mergedID";
  $status = $db->do($query);
  struct_db_err($db, $status, $query);

  #put the primary product back in its lists, remove merged meta product
  $query = "SELECT ID, script FROM $dsSchema.product_list";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);

  while (($listID, $script) = $dbOutput->fetchrow_array)
  {
    $origScript = $script;
    $script =~ s/M:$mergedID\,/M:$primaryProd\,/;
    $script =~ s/M:$mergedID$/M:$primaryProd/;

    #if we altered the list definition script
    if ($origScript ne $script)
    {
      $query = "UPDATE $dsSchema.product_list SET script='$script' WHERE ID=$listID";
      $status = $db->do($query);
      struct_db_err($db, $status, $query);
      Lib::DataSel::datasel_expand_script($db, $dsSchema, $listID, "p", "l");
    }
  }

  #put the primary product back in its aggregates, remove merged meta product
  $query = "SELECT ID, addScript FROM $dsSchema.product_aggregate";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);

  while (($aggID, $script) = $dbOutput->fetchrow_array)
  {
    $origScript = $script;
    $script =~ s/M:$mergedID\,/M:$primaryProd\,/;
    $script =~ s/M:$mergedID$/M:$primaryProd/;

    #if we altered the list definition script
    if ($origScript ne $script)
    {
      $query = "UPDATE $dsSchema.product_aggregate SET addScript='$script' \
          WHERE ID=$aggID";
      $status = $db->do($query);
      struct_db_err($db, $status, $query);
      Lib::DataSel::datasel_expand_script($db, $dsSchema, $aggID, "p", "a");
      Lib::DataSel::datasel_expand_script($db, $dsSchema, $aggID, "p", "as");
    }
  }

  #mark all of the merged products as unmerged
  foreach $id (@mergedIDs)
  {
    $query = "UPDATE $dsSchema.products SET merged = 0 WHERE ID = $id";
    $status = $db->do($query);
    struct_db_err($db, $status, $query);
  }

  #delete the merged product definition
  $query = "DELETE FROM $dsSchema.products_merged WHERE mergedID = $mergedID";
  $status = $db->do($query);
  struct_db_err($db, $status, $query);

  #delete the merged product
  $query = "DELETE FROM $dsSchema.products WHERE ID=$mergedID";
  $status = $db->do($query);
  struct_db_err($db, $status, $query);

  #pull the merged product rows from the facts table
  $query = "DELETE FROM $dsSchema.facts WHERE productID = $mergedID";
  $status = $db->do($query);
  struct_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------------
#
# Return a hash of all attributes in a data source for the specified dim
#

sub DSRattr_get_attributes_hash
{
  my ($query, $dbOutput, $status, $dbName, $id, $name);
  my (%attrHash);

  my ($db, $dsSchema, $dim) = @_;


  undef(%attrHash);

  #figure out the table containing aggregates for our dimension
  if ($dim eq "p")
  {
    $dbName = "product_attributes";
  }
  elsif ($dim eq "g")
  {
    $dbName = "geography_attributes";
  }
  elsif ($dim eq "t")
  {
    $dbName = "time_attributes";
  }

  $query = "SELECT ID, name FROM $dsSchema.$dbName order by name";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);

  while (($id, $name) = $dbOutput->fetchrow_array)
  {
    $attrHash{$id} = $name;
  }

  return(%attrHash);
}



#-------------------------------------------------------------------------------
#
# Get a hash of values for the specified attribute in the specified dimension.
# The hash is keyed by the ID of the base items in the specified dimension.
#

sub DSRattr_get_values_hash
{
  my ($query, $dbOutput, $status, $dbName, $value, $itemID);
  my (%attrValuesHash);

  my ($db, $dsSchema, $dim, $attrID) = @_;


  #determine which database holds attribute info for our dimension
  if ($dim eq "p")
  {
    $dbName = "product_attribute_values";
  }
  elsif ($dim eq "g")
  {
    $dbName = "geography_attribute_values";
  }
  elsif ($dim eq "t")
  {
    $dbName = "time_attribute_values";
  }

  #extract all values for the specified attribute
  $query = "SELECT itemID, value FROM $dsSchema.$dbName WHERE attributeID = $attrID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  struct_db_err($db, $status, $query);

  while (($itemID, $value) = $dbOutput->fetchrow_array)
  {
    $attrValuesHash{$itemID} = $value;
  }

  return(%attrValuesHash);
}


#-------------------------------------------------------------------------------


1;
