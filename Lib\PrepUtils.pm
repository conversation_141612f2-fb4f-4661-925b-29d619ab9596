
package Lib::PrepUtils;

use lib "/opt/apache/app/";


use Exporter;


our @ISA = ('Exporter');

our @EXPORT = qw(
    &PrepUtils_handle_db_err
    &PrepUtils_execute_with_retry
    &PrepUtils_send_exception_email
    &PrepUtils_connect_to_database
    &PrepUtils_get_flow_owner
    &PrepUtils_get_current_timestamp
    &PrepUtils_set_job_op_title
    &PrepUtils_set_job_op_details
    &PrepUtils_set_job_op_pct
    &PrepUtils_set_job_op_speed
    &PrepUtils_set_job_op_extra
    &PrepUtils_determine_data_provider
    &PrepUtils_determine_column_sql_type
    &PrepUtils_active_job_count
    &PrepUtils_increment_job_run_time
    &PrepUtils_increment_successful_xforms
    &PrepUtils_store_scaled_cloud_load
    &PrepUtils_autoscale_data_size
  );

#-------------------------------------------------------------------------
#
# Send simple email notification for exceptions
#
# PRODUCTION SETUP REQUIRED FOR EMAIL TO WORK:
#
# OPTION 1: Install Local Mail System (Recommended)
# Ubuntu/Debian: sudo apt-get install mailutils postfix
# CentOS/RHEL:   sudo yum install mailx postfix
#
# OPTION 2: Configure External SMTP Relay
# Edit /etc/postfix/main.cf and add:
#   relayhost = [smtp.gmail.com]:587
#   smtp_sasl_auth_enable = yes
#   smtp_sasl_password_maps = hash:/etc/postfix/sasl_passwd
#   smtp_sasl_security_options = noanonymous
#   smtp_tls_security_level = encrypt
#
# OPTION 3: Use Gmail SMTP (if allowed by organization)
# Create app <NAME_EMAIL>
# Configure postfix to relay through Gmail
#
# TEST EMAIL SETUP:
# echo "Test message" | mail -s "Test Subject" <EMAIL>
#
# TROUBLESHOOTING:
# - Check /var/log/mail.log for email delivery issues
# - Verify firewall allows SMTP traffic (port 25/587)
# - Check DNS MX records for recipient domain
# - Ensure proper SPF/DKIM configuration to avoid spam filters
#

sub PrepUtils_send_exception_email
{
  my ($error_type, $error_message, $query, $location) = @_;

  # Simple email sending using system sendmail
  my $to_email = '<EMAIL>';
  my $from_email = 'support\@koala-corp.com';
  my $subject = "[PREP-SYSTEM] Exception Alert - $error_type";
  my $timestamp = localtime();
  my $hostname = $ENV{HOSTNAME} || $ENV{COMPUTERNAME} || 'unknown';

  my $email_body = <<EOF;
PREP SYSTEM EXCEPTION ALERT
===========================

Timestamp: $timestamp
Hostname: $hostname
Error Type: $error_type
Location: $location

ERROR DETAILS:
$error_message

FAILED QUERY:
$query

This is an automated notification from the PREP Exception Handling System.
Please check the system logs for additional details.
EOF

  # Try multiple email methods
  my $email_sent = 0;

  eval {
    # Method 1: Try system mail command (Linux/Unix)
    if (-x '/usr/bin/mail' || -x '/bin/mail') {
      open(my $mail, '|-', 'mail', '-s', $subject, $to_email) or die "Cannot open mail: $!";
      print $mail "From: $from_email\n";
      print $mail $email_body;
      close($mail);
      print STDERR "$timestamp: Exception email sent to $to_email via system mail\n";
      $email_sent = 1;
    }
    # Method 2: Try sendmail directly
    elsif (-x '/usr/sbin/sendmail' || -x '/usr/lib/sendmail') {
      my $sendmail_path = (-x '/usr/sbin/sendmail') ? '/usr/sbin/sendmail' : '/usr/lib/sendmail';
      open(my $sendmail, '|-', "$sendmail_path -t") or die "Cannot open sendmail: $!";
      print $sendmail "To: $to_email\n";
      print $sendmail "From: $from_email\n";
      print $sendmail "Subject: $subject\n";
      print $sendmail "\n";
      print $sendmail $email_body;
      close($sendmail);
      print STDERR "$timestamp: Exception email sent to $to_email via sendmail\n";
      $email_sent = 1;
    }
    # Method 3: Try SMTP using Perl modules (if available)
    elsif (eval { require Net::SMTP; 1; }) {
      my $smtp = Net::SMTP->new('localhost', Timeout => 30);
      if ($smtp) {
        $smtp->mail($from_email);
        $smtp->to($to_email);
        $smtp->data();
        $smtp->datasend("To: $to_email\n");
        $smtp->datasend("From: $from_email\n");
        $smtp->datasend("Subject: $subject\n");
        $smtp->datasend("\n");
        $smtp->datasend($email_body);
        $smtp->dataend();
        $smtp->quit;
        print STDERR "$timestamp: Exception email sent to $to_email via SMTP\n";
        $email_sent = 1;
      }
    }
    # Method 4: Write to email queue file for external processing
    else {
      my $email_queue_file = "/tmp/prep_email_queue.txt";
      open(my $queue, '>>', $email_queue_file) or die "Cannot open email queue: $!";
      print $queue "=" x 60 . "\n";
      print $queue "EMAIL QUEUED: $timestamp\n";
      print $queue "TO: $to_email\n";
      print $queue "FROM: $from_email\n";
      print $queue "SUBJECT: $subject\n";
      print $queue "BODY:\n$email_body\n";
      print $queue "=" x 60 . "\n\n";
      close($queue);
      print STDERR "$timestamp: Email queued for $to_email in $email_queue_file\n";
      $email_sent = 1;
    }
  };

  if ($@) {
    print STDERR "$timestamp: Failed to send email notification: $@\n";
    # Fallback: Write to error log file
    my $error_log = "/tmp/prep_email_errors.log";
    eval {
      open(my $log, '>>', $error_log);
      print $log "$timestamp: EMAIL SEND FAILED\n";
      print $log "TO: $to_email\n";
      print $log "ERROR: $@\n";
      print $log "ORIGINAL ERROR: $error_type - $error_message\n";
      print $log "-" x 50 . "\n";
      close($log);
    };
  }

  # Always log the attempt
  if (!$email_sent) {
    print STDERR "$timestamp: Email notification attempted but no working mail system found\n";
    print STDERR "$timestamp: Error details: $error_type - $error_message\n";
    print STDERR "$timestamp: Target email: $to_email\n";
  }
}

#-------------------------------------------------------------------------
#
# Handle a database error of some kind during data prep utility operations

sub PrepUtils_handle_db_err
{
 my ($date, $errMsg, $retryCount, $maxRetries, $retryDelay);

 my ($prepDB, $status, $text, $options) = @_;

 # Set default retry parameters
 $maxRetries = $options->{max_retries} || 3;
 $retryDelay = $options->{retry_delay} || 2;
 $retryCount = $options->{retry_count} || 0;

 if (!defined($status))
 {
   $date = localtime();
   $errMsg = $prepDB->errstr || "Unknown database error";

   # Log the error with more details
   print STDERR "$date: Database error: $errMsg\n";
   print STDERR "$date: Query: $text\n";
   print STDERR "$date: Retry attempt: $retryCount of $maxRetries\n";

   # Send email notification for database errors
   PrepUtils_send_exception_email("Database Error", $errMsg, $text, "PrepUtils_handle_db_err");

   # Handle different types of database errors
   if ($errMsg =~ m/^MySQL server has gone away/)
   {
     # Log the error but don't terminate immediately
     print STDERR "$date: Lost connection to database, attempting to reconnect...\n";

     # Try to reconnect with retry logic
     for my $attempt (1..$maxRetries) {
       eval {
         $prepDB = DBI->connect($Lib::KoalaConfig::prepDBServer, 'app', $Lib::KoalaConfig::password);
       };

       if ($@) {
         print STDERR "$date: Reconnection attempt $attempt failed: $@\n";
         if ($attempt < $maxRetries) {
           print STDERR "$date: Waiting $retryDelay seconds before retry...\n";
           sleep($retryDelay);
         }
       } else {
         # If reconnection succeeds, log it
         print STDERR "$date: Successfully reconnected to database on attempt $attempt\n";
         return 0;
       }
     }

     # If all reconnection attempts failed, terminate
     die("Lost connection to database and all reconnection attempts failed, terminating");
   }

   # For deadlock errors, implement retry logic
   if ($errMsg =~ m/Deadlock found/) {
     print STDERR "$date: Deadlock detected, operation should be retried\n";
     if ($retryCount < $maxRetries) {
       print STDERR "$date: Will retry operation after $retryDelay seconds\n";
       sleep($retryDelay);
       return -1; # Signal that operation should be retried
     } else {
       print STDERR "$date: Maximum deadlock retries exceeded\n";
     }
   }

   # For timeout errors
   if ($errMsg =~ m/timeout|timed out/i) {
     print STDERR "$date: Database timeout detected\n";
     if ($retryCount < $maxRetries) {
       print STDERR "$date: Will retry operation after $retryDelay seconds\n";
       sleep($retryDelay);
       return -1; # Signal that operation should be retried
     } else {
       print STDERR "$date: Maximum timeout retries exceeded\n";
     }
   }

   # For lock wait timeout
   if ($errMsg =~ m/Lock wait timeout exceeded/) {
     print STDERR "$date: Lock wait timeout exceeded\n";
     if ($retryCount < $maxRetries) {
       print STDERR "$date: Will retry operation after $retryDelay seconds\n";
       sleep($retryDelay);
       return -1; # Signal that operation should be retried
     } else {
       print STDERR "$date: Maximum lock timeout retries exceeded\n";
     }
   }
 }

 return $status;
}



#-------------------------------------------------------------------------
#
# Execute SQL with automatic retry logic for transient errors
#

sub PrepUtils_execute_with_retry
{
  my ($prepDB, $query, $operation_type, $options) = @_;
  my ($status, $result, $retryCount, $maxRetries, $retryDelay, $date);

  # Set default retry parameters
  $maxRetries = $options->{max_retries} || 3;
  $retryDelay = $options->{retry_delay} || 2;
  $retryCount = 0;

  while ($retryCount <= $maxRetries) {
    eval {
      if ($operation_type eq 'prepare_execute') {
        my $dbOutput = $prepDB->prepare($query);
        $status = $dbOutput->execute;
        $result = $dbOutput;
      } elsif ($operation_type eq 'do') {
        $status = $prepDB->do($query);
        $result = $status;
      } else {
        die("Unknown operation type: $operation_type");
      }
    };

    if ($@) {
      $date = localtime();
      print STDERR "$date: Exception during SQL execution (attempt $retryCount): $@\n";
      print STDERR "$date: Query: $query\n";

      # Send email notification for SQL execution errors
      PrepUtils_send_exception_email("SQL Execution Error", $@, $query, "PrepUtils_execute_with_retry");

      if ($retryCount < $maxRetries) {
        $retryCount++;
        print STDERR "$date: Retrying in $retryDelay seconds...\n";
        sleep($retryDelay);
        next;
      } else {
        die("SQL execution failed after $maxRetries attempts: $@");
      }
    }

    # Check the result and handle database errors
    my $error_options = {
      max_retries => $maxRetries,
      retry_delay => $retryDelay,
      retry_count => $retryCount
    };

    my $error_result = PrepUtils_handle_db_err($prepDB, $status, $query, $error_options);

    if ($error_result == -1) {
      # Retry requested by error handler
      $retryCount++;
      if ($retryCount <= $maxRetries) {
        next;
      } else {
        die("SQL execution failed after $maxRetries retries due to persistent database errors");
      }
    } elsif (!defined($error_result)) {
      # Fatal error occurred
      die("Fatal database error occurred");
    } else {
      # Success or handled error
      return $result;
    }
  }

  die("SQL execution failed after $maxRetries attempts");
}



#-------------------------------------------------------------------------------
#
# Connect to the Koala Data Prep database
#

sub PrepUtils_connect_to_database
{
  my ($db, $date, $maxRetries, $retryCount, $retryDelay);

  # Set retry parameters
  $maxRetries = 3;
  $retryCount = 0;
  $retryDelay = 5; # seconds

  while ($retryCount < $maxRetries) {
    # Try to connect to the database
    eval {
      $db = DBI->connect($Lib::KoalaConfig::prepDBServer, 'app', $Lib::KoalaConfig::password,
                         { RaiseError => 0, PrintError => 0 });
    };

    # If connection succeeded, return the database handle
    if ($db && !$@) {
      # If this was a retry, log the successful reconnection
      if ($retryCount > 0) {
        $date = localtime();
        print STDERR "$date: Successfully connected to database after $retryCount retries\n";
      }
      return $db;
    }

    # Connection failed, log the error and retry
    $date = localtime();
    print STDERR "$date: Database connection failed: " . ($@ || "Unknown error") . "\n";

    # Increment retry counter and wait before retrying
    $retryCount++;
    if ($retryCount < $maxRetries) {
      print STDERR "$date: Retrying connection in $retryDelay seconds (attempt $retryCount of $maxRetries)...\n";
      sleep($retryDelay);
    }
  }

  # If we've exhausted all retries, log a final error
  $date = localtime();
  print STDERR "$date: Failed to connect to database after $maxRetries attempts\n";

  return $db; # Will be undef if all connection attempts failed
}



#-------------------------------------------------------------------------------
#
# Return the user ID of the specified flow's owner
#

sub PrepUtils_get_flow_owner
{
  my ($query, $status, $dbOutput, $ownerID);

  my ($prepDB, $flowID) = @_;


  $query = "SELECT userID FROM prep.flows WHERE ID=$flowID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($ownerID) = $dbOutput->fetchrow_array;

  return($ownerID);
}



#-------------------------------------------------------------------------------
#
# Return the current timestamp, in SQL format.
#

sub PrepUtils_get_current_timestamp
{
  my ($query, $status, $dbOutput, $timestamp);

  my ($prepDB) = @_;


  $query = "SELECT NOW() FROM prep.flows";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($timestamp) = $dbOutput->fetchrow_array;

  return($timestamp);
}



#-------------------------------------------------------------------------
#
# Sets the "title" of an operation (displayed at top of status dialogs in UI).
#

sub PrepUtils_set_job_op_title
{
  my ($query, $q_opTitle, $status);

  my ($prepDB, $jobID, $opTitle) = @_;


  #if called with no opTitle, clear whatever's there
  if (length($opTitle) < 1)
  {
    $query = "UPDATE prep.jobs SET opTitle=NULL WHERE ID=$jobID";
  }
  else
  {
    $q_opTitle = $prepDB->quote($opTitle);
    $query = "UPDATE prep.jobs SET opTitle=$q_opTitle WHERE ID=$jobID";
  }

  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);
}



#-------------------------------------------------------------------------
#
# Sets the details of an operation (displayed in status dialogs in UI).
#

sub PrepUtils_set_job_op_details
{
  my ($query, $q_opDetails, $status);

  my ($prepDB, $jobID, $opDetails) = @_;


  #if called with no opDetails, clear whatever's there
  if (length($opDetails) < 1)
  {
    $query = "UPDATE prep.jobs SET opDetails=NULL WHERE ID=$jobID";
  }
  else
  {
    $q_opDetails = $prepDB->quote($opDetails);
    $query = "UPDATE prep.jobs SET opDetails=$q_opDetails WHERE ID=$jobID";
  }

  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);
}



#-------------------------------------------------------------------------
#
# Sets the % completion of an operation (displayed in status dialogs in UI).
#

sub PrepUtils_set_job_op_pct
{
  my ($query, $q_opPct, $status);

  my ($prepDB, $jobID, $opPct) = @_;


  #if called with no opDetails, clear whatever's there
  if (length($opPct) < 1)
  {
    $query = "UPDATE prep.jobs SET opPctComplete=NULL WHERE ID=$jobID";
  }
  else
  {
    if ($opPct < 0)
    {
      $opPct = 0;
    }
    $query = "UPDATE prep.jobs SET opPctComplete=$opPct WHERE ID=$jobID";
  }

  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);
}



#-------------------------------------------------------------------------
#
# Sets the speed (ops/second) of an operation (used in live UI graphs)
#

sub PrepUtils_set_job_op_speed
{
  my ($query, $q_opSpeed, $status);

  my ($prepDB, $jobID, $opSpeed) = @_;


  #if called with no opDetails, clear whatever's there
  if (length($opSpeed) < 1)
  {
    $query = "UPDATE prep.jobs SET opSpeed=NULL WHERE ID=$jobID";
  }
  else
  {
    $q_opSpeed = $prepDB->quote($opSpeed);
    $query = "UPDATE prep.jobs SET opSpeed=$q_opSpeed WHERE ID=$jobID";
  }

  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);
}



#-------------------------------------------------------------------------
#
# Sets the extra details of an operation (displayed in status dialogs in UI).
#

sub PrepUtils_set_job_op_extra
{
  my ($query, $q_opExtra, $status);

  my ($prepDB, $jobID, $opExtra) = @_;


  #if called with no opDetails, clear whatever's there
  if (length($opExtra) < 1)
  {
    $query = "UPDATE prep.jobs SET opExtra=NULL WHERE ID=$jobID";
  }
  else
  {
    $q_opExtra = $prepDB->quote($opExtra);
    $query = "UPDATE prep.jobs SET opExtra=$q_opExtra WHERE ID=$jobID";
  }

  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);
}



#-------------------------------------------------------------------------
#
# Attempt to determine the provider of a data set (Nielsen AOD, IRI, etc.) by
# examining column names and other metadata.
# NB: If we can figure out the data provider, we can apply data set-specific
#   optimizations.
#

sub PrepUtils_determine_data_provider
{
  my ($dataProvider, $colName, $colNamesSearchStr);

  my (@colNames) = @_;


  #our default is "who knows?"
  $dataProvider = "NA";

  #cycle through the list of column names, looking for something we know
  foreach $colName (@colNames)
  {

    #look for columns that are Nielsen AOD-specific
    if (($colName eq "BC CATEGORY") || ($colName eq "BC DEPARTMENT") ||
        ($colName eq "BC SEGMENT") || ($colName eq "BC SUB CATEGORY") ||
        ($colName eq "BC SUPER CATEGORY"))
    {
      $dataProvider = "Nielsen AOD";
    }
  }

  #if we still haven't had any luck, try to infer based on a combination of
  #available column names
  if ($dataProvider eq "NA")
  {
    $colNamesSearchStr = join('|', @colNames);

    if (($colNamesSearchStr =~ m/Feat w\/o Disp Units/) &&
        ($colNamesSearchStr =~ m/Disp w\/o Feat Units/) &&
        ($colNamesSearchStr =~ m/Feat & Disp Units/) &&
        ($colNamesSearchStr =~ m/Price Decr Units/))
    {
      $dataProvider = "Nielsen AOD";
    }
  }

  return($dataProvider);
}



#-------------------------------------------------------------------------
#
# If we know things about a specific data set (especially which columns
# contain numerical measures), return the SQL snippet needed to define
# the column type.
# NB: This lets us both increase the number of columns we can handle in a
#   given data set, and possibly improve performance a bit (especially on older
#   MySQL implementations)
#

sub PrepUtils_determine_column_sql_type
{
  my ($colSQLType);

  my ($dataProvider, $colName) = @_;


  my %nielsenAODColumns = (
    "\$" => "measure",
    "\$ ya" => "measure",
    "%acv reach" => "measure",
    "%acv reach ya" => "measure",
    "base \$" => "measure",
    "base units" => "measure",
    "disp w/o feat \$" => "measure",
    "disp w/o feat units" => "measure",
    "feat & disp \$" => "measure",
    "feat & disp units" => "measure",
    "feat w/o disp \$" => "measure",
    "feat w/o disp units" => "measure",
    "number of stores" => "measure",
    "number of stores selling" => "measure",
    "price decr \$" => "measure",
    "price decr units" => "measure",
    "subsidized \$" => "measure",
    "subsidized units" => "measure",
    "units" => "measure",
    "units ya" => "measure",
  );


  #if we don't know anything about the data set, use the default VARCHAR
  if ($dataProvider eq "NA")
  {
    $colSQLType = "VARCHAR(127)";
  }

  if ($dataProvider = "Nielsen AOD")
  {
    if ($nielsenAODColumns{lc($colName)} eq "measure")
    {
      $colSQLType = "DOUBLE DEFAULT NULL";
    }
    else
    {
      $colSQLType = "VARCHAR(127)";
    }
  }

  return($colSQLType);
}



#-------------------------------------------------------------------------
#
# Return a count of currently active jobs (used to prevent the customer's
# data prep cloud from becoming saturated).
#

sub PrepUtils_active_job_count
{
  my ($query, $dbOutput, $status, $state, $activeJobs);

  my ($prepDB) = @_;


  #get a list of all active jobs on the cloud
  $query = "SELECT state FROM prep.jobs WHERE state NOT IN ('LOADED', 'ERROR')";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #run through the list of jobs, and determine which are currently running or
  #waiting
  $activeJobs = 0;
  while (($state) = $dbOutput->fetchrow_array)
  {

    #jobs waiting for user input don't count as active
    if (($state eq "PARSE-WAIT") || ($state eq "DATATYPE-WAIT"))
    {
      next;
    }
    $activeJobs++;
  }

  return($activeJobs);
}



#-------------------------------------------------------------------------
#
# Determines a "scaled" cloud load number (1 is normal, 2 heavy, 3 overused)
# while a job was running and stores it in the job_history table.
#

sub PrepUtils_store_scaled_cloud_load
{
  my ($query, $status, $activeJobs, $cloudLoad);

  my ($prepDB, $jobID) = @_;


  $activeJobs = PrepUtils_active_job_count($prepDB);
  $cloudLoad = 0;
  if ($activeJobs <= ($Lib::KoalaConfig::prepCores * 0.75))
  {
    $cloudLoad = 1;
  }
  elsif ($activeJobs <= ($Lib::KoalaConfig::prepCores + 1))
  {
    $cloudLoad = 2;
  }
  else
  {
    $cloudLoad = 3;
  }

  $query = "UPDATE prep.job_history
      SET cloudLoad = GREATEST($cloudLoad, cloudLoad)
      WHERE jobID=$jobID";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);
}



#-------------------------------------------------------------------------
#
# Calculate the amount of time elapsed since the start time, and increment the
# specified job's run time by that amount. Used to keep track of how much
# processing time a job used.
#

sub PrepUtils_increment_job_run_time
{
  my ($query, $status);

  my ($prepDB, $jobID, $startTime) = @_;


  $query = "UPDATE prep.job_history
      SET elapsedTime = elapsedTime + TIMEDIFF(NOW(), '$startTime')
      WHERE jobID=$jobID";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);
}



#-------------------------------------------------------------------------
#
# Increment the number of successful transforms accomplished by the current job.
#

sub PrepUtils_increment_successful_xforms
{
  my ($query, $status);

  my ($prepDB, $jobID) = @_;


  $query = "UPDATE prep.job_history
      SET successfulXforms = successfulXforms + 1
      WHERE jobID=$jobID";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);
}



#-------------------------------------------------------------------------
#
# "Autoscale" a data size value to make readability easier for end users.
#

sub PrepUtils_autoscale_data_size
{
  my ($val) = @_;


  #handle billions
  if ($val > 1_000_000_000)
  {
    $val = $val / 1_000_000_000;
    $val = sprintf("%.1f", $val);
    $val = "$val" . " GB";
  }
  elsif ($val > 1_000_000)
  {
    $val = $val / 1_000_000;
    $val = sprintf("%.1f", $val);
    $val = "$val" . " MB";
  }
  elsif ($val > 1000)
  {
    $val = $val / 1000;
    $val = sprintf("%.1f", $val);
    $val = "$val" . " KB";
  }

  #knock .0 off end of value
  if ($val =~ m/^(.*)\.0(.)$/)
  {
    $val = $1 . $2;
  }

  return($val);
}


#-------------------------------------------------------------------------


1;
