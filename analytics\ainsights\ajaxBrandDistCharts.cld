#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::AInsights::AInsights;
use Lib::AInsights::Utils;

#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if ((length($email) < 1) && (length($credentials) < 1))
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #get the CGI input variables
  $priceModelID = $q->param('pm');
  $chart = $q->param('c');
  $geoID = $q->param('g');
  $credentials = $q->param('auth');

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/plain\n\n");

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsID = AInsights_get_dsID($db, $priceModelID);

  $dsSchema = "datasource_" . $dsID;

  AInsights_Utils_initialize_constants($priceModelID);

  #get displayable names for our DSR dimensions
  %timeperiodNames = dsr_get_item_name_hash($db, $dsSchema, "t");

  $ownBrandID = AInsights_Utils_get_own_brand_id($db, $priceModelID);
  $brandSegID = AInsights_Utils_get_brand_seg_id($db, $priceModelID);

  %brandNameHash = DSRseg_get_segments_hash($db, $dsSchema, "p", $brandSegID);
  $ownBrandName = $brandNameHash{$ownBrandID};

  #get competitive brands
  ($compID1, $compID2) = AInsights_Utils_get_brand_comp_ids($db, $dsSchema, $geoID);
  $compBrand1Name = $brandNameHash{$compID1};
  $compBrand2Name = $brandNameHash{$compID2};

  #if we're doing the overview line chart
  if ($chart eq "trends")
  {

    #figure out the chrono-order of the time periods we want to graph
    $query = "SELECT ID, endDate FROM $dsSchema.timeperiods \
        WHERE duration=1 AND type=30 ORDER BY endDate DESC LIMIT 52";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($timeID, $endDate) = $dbOutput->fetchrow_array)
    {
      $timeIDStr .= "'$timeID',";
      push(@orderedTimeIDs, $timeID);

      $endDate =~ m/^(.*?) /;
      $endDateHash{$timeID} = $1;
    }
    chop($timeIDStr);
    @orderedTimeIDs = reverse(@orderedTimeIDs);

    $jsonData = <<JSON_LABEL;
{
  "chart":
  {
    "theme": "zune",
    "animation": "0",
    "drawanchors": "0",
    "yaxisname": "Distribution Level (\%ACV Reach)",
    "yaxisnamefontsize": "14",
    "yAxisValueFontSize": "12",
    "numvdivlines": "10",
    "divlinealpha": "30",
    "labelpadding": "10",
    "labelstep": "4",
    "labelfontsize": "12",
    "labeldisplay": "rotate",
    "slantlabel": "1",
    "legendfontsize": "12",
    "useEllipsesWhenOverflow": "0",
    "yaxisvaluespadding": "10",
    "showvalues": "0"
  },
JSON_LABEL

    #output time periods (X axis values)
    $jsonData .= <<JSON_LABEL;
  "categories": [
    {
      "category": [
JSON_LABEL

    foreach $timeID (@orderedTimeIDs)
    {
    $jsonData .= <<JSON_LABEL;
        {
          "label": "$endDateHash{$timeID}"
        },
JSON_LABEL
    }
    chop($jsonData);  chop($jsonData);

    $jsonData .= <<JSON_LABEL;
      ]
    }
  ],
  "dataset": [

JSON_LABEL

    #output the data set for the category
    undef(%distValueHash);
    $query = "SELECT timeID, avgDist FROM $dsSchema.$AInsightsBrandCube \
        WHERE brandID=0 AND geographyID=$geoID AND timeID IN ($timeIDStr)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($timeID, $distVal) = $dbOutput->fetchrow_array)
    {
      $distValueHash{$timeID} = $distVal;
    }

    if ($status > 0)
    {

      $jsonData .= <<JSON_LABEL;
  {
    "seriesname": "Category Average",
    "data": [
JSON_LABEL

      foreach $timeID (@orderedTimeIDs)
      {
        $distVal = $distValueHash{$timeID};
        $jsonData .= "{ \"value\": \"$distVal\"},\n";
      }
      chop($jsonData);  chop($jsonData);

      $jsonData .= <<JSON_LABEL;
    ]
  }
JSON_LABEL
    }

    #output the data set for our brand (if there is data)
    undef(%distValueHash);
    $query = "SELECT timeID, avgDist FROM $dsSchema.$AInsightsBrandCube \
        WHERE brandID=$ownBrandID AND geographyID=$geoID AND timeID IN ($timeIDStr)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($timeID, $distVal) = $dbOutput->fetchrow_array)
    {
      $distValueHash{$timeID} = $distVal;
    }

    if ($status > 0)
    {
      $jsonData .= <<JSON_LABEL;
  ,{
    "seriesname": "$ownBrandName",
    "data": [
JSON_LABEL

      foreach $timeID (@orderedTimeIDs)
      {
        $distVal = $distValueHash{$timeID};
        $jsonData .= "{ \"value\": \"$distVal\"},\n";
      }
      chop($jsonData);  chop($jsonData);

      $jsonData .= <<JSON_LABEL;
    ]
  }
JSON_LABEL
    }

    #output the data set for our first key competitor
    undef(%distValueHash);
    $query = "SELECT timeID, avgDist FROM $dsSchema.$AInsightsBrandCube \
        WHERE brandID=$compID1 AND geographyID=$geoID AND timeID IN ($timeIDStr)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($timeID, $distVal) = $dbOutput->fetchrow_array)
    {
      $distValueHash{$timeID} = $distVal;
    }

    if ($status > 0)
    {

      $jsonData .= <<JSON_LABEL;
  ,{
    "seriesname": "$compBrand1Name",
    "data": [
JSON_LABEL

      foreach $timeID (@orderedTimeIDs)
      {
        $distVal = $distValueHash{$timeID};
        $jsonData .= "{ \"value\": \"$distVal\"},\n";
      }
      chop($jsonData);  chop($jsonData);

      $jsonData .= <<JSON_LABEL;
    ]
  }
JSON_LABEL
    }

    #output the data set for our second key competitor
    undef(%distValueHash);
    $query = "SELECT timeID, avgDist FROM $dsSchema.$AInsightsBrandCube \
        WHERE brandID=$compID2 AND geographyID=$geoID AND timeID IN ($timeIDStr)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($timeID, $distVal) = $dbOutput->fetchrow_array)
    {
      $distValueHash{$timeID} = $distVal;
    }

    if ($status > 0)
    {

      $jsonData .= <<JSON_LABEL;
  ,{
    "seriesname": "$compBrand2Name",
    "data": [
JSON_LABEL

      foreach $timeID (@orderedTimeIDs)
      {
        $distVal = $distValueHash{$timeID};
        $jsonData .= "{ \"value\": \"$distVal\"},\n";
      }
      chop($jsonData);  chop($jsonData);

      $jsonData .= <<JSON_LABEL;
    ]
  }
JSON_LABEL
    }

    $jsonData .= <<JSON_LABEL;
  ]
}
JSON_LABEL

    print $jsonData;
  }


  #----------------- Distribution of all Brands -------------------

  if ($chart eq "brands_dist")
  {
    $brandNameHash{0} = "Category Average";

    #get category's average dist for color coding decisions
    $query = "SELECT avgDist52 FROM $dsSchema.$AInsightsBrandTable \
        WHERE brandID=0 AND geographyID=$geoID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($catAvgDist) = $dbOutput->fetchrow_array;

    $jsonData = <<JSON_LABEL;
{
  "chart":
  {
    "theme": "zune",
    "yaxisname": "\%ACV Reach",
    "yaxisnamefontsize": "14",
    "yAxisValueFontSize": "12",
    "showlegend": "0",
    "labelFontSize": "12",
    "valueFontSize": "12",
    "valueFontColor": "#808080",
    "placeValuesInside": "0",
    "decimals": "1"
  },
  "data": [
JSON_LABEL

    #build hash of our distribution in each geography, maintain total
    $query = "SELECT brandID, avgDist52 FROM $dsSchema.$AInsightsBrandTable \
        WHERE geographyID=$geoID AND avgDist52 > 0 ORDER BY avgDist52 DESC";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($brandID, $dist) = $dbOutput->fetchrow_array)
    {
      if ($brandID == 0)
      {
        $colorCode = "#00AEEF";
      }
      elsif ($dist >= $catAvgDist)
      {
        $colorCode = "#198754";
      }
      elsif ((($catAvgDist - $dist) / $catAvgDist) < 0.25)
      {
        $colorCode = "#FFC107";
      }
      else
      {
        $colorCode = "#DC3545";
      }

      if ($brandID == $ownBrandID)
      {
        $brandNameHash{$brandID} = "<b>$brandNameHash{$brandID}</b>";
      }

      $jsonData .= "{\"label\": \"$brandNameHash{$brandID}\", \"value\": \"$dist\", \"color\": \"$colorCode\"},\n";
    }
    chop($jsonData);  chop($jsonData);

    $jsonData .= <<JSON_LABEL;
  ]
}
JSON_LABEL

    print($jsonData);
  }



  #----------------- Own Brand Geo Distribution -------------------

  if ($chart eq "brand_geos")
  {
    %geoBaseNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "g");

    #build hash of category's average dist in each geo for color coding decisions
    $query = "SELECT geographyID, avgDist52 FROM $dsSchema.$AInsightsBrandTable WHERE brandID=0";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($geoID, $dist) = $dbOutput->fetchrow_array)
    {
      $geoAvgDist{$geoID} = $dist;
    }

    $jsonData = <<JSON_LABEL;
{
  "chart":
  {
    "theme": "zune",
    "yaxisname": "\%ACV Reach",
    "yaxisnamefontsize": "14",
    "yAxisValueFontSize": "12",
    "showlegend": "0",
    "labelFontSize": "12",
    "valueFontSize": "12",
    "valueFontColor": "#808080",
    "placeValuesInside": "0",
    "decimals": "1"
  },
  "data": [
JSON_LABEL

    $query = "SELECT geographyID, avgDist52 FROM $dsSchema.$AInsightsBrandTable \
        WHERE brandID=$ownBrandID AND avgDist52 > 0 ORDER BY avgDist52 DESC";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($geoID, $dist) = $dbOutput->fetchrow_array)
    {
      if ($dist >= $geoAvgDist{$geoID})
      {
        $colorCode = "#198754";
      }
      elsif ((($geoAvgDist{$geoID} - $dist) / $geoAvgDist{$geoID}) < 0.25)
      {
        $colorCode = "#FFC107";
      }
      else
      {
        $colorCode = "#DC3545";
      }
      $jsonData .= "{\"label\": \"$geoBaseNameHash{$geoID}\", \"value\": \"$dist\", \"color\": \"$colorCode\"},\n";
    }
    chop($jsonData);  chop($jsonData);

    $jsonData .= <<JSON_LABEL;
  ]
}
JSON_LABEL

    print($jsonData);
  }



  #----------------- Own Brand Item Distribution -------------------

  if ($chart eq "brand_items")
  {
    %prodBaseNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "p");

    #get category's average dist for color coding decisions
    $query = "SELECT avgDist52 FROM $dsSchema.$AInsightsBrandTable \
        WHERE brandID=0 AND geographyID=$geoID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($catAvgDist) = $dbOutput->fetchrow_array;

    $jsonData = <<JSON_LABEL;
{
  "chart":
  {
    "theme": "zune",
    "yaxisname": "\%ACV Reach",
    "yaxisnamefontsize": "14",
    "yAxisValueFontSize": "12",
    "showlegend": "0",
    "labelFontSize": "12",
    "valueFontSize": "12",
    "valueFontColor": "#808080",
    "placeValuesInside": "0",
    "decimals": "1"
  },
  "data": [
JSON_LABEL

    $query = "SELECT productID, avgDist52 FROM $dsSchema.$AInsightsItemTable \
        WHERE brandID=$ownBrandID AND avgDist52 > 0 AND geographyID=$geoID \
        ORDER BY avgDist52 DESC";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($prodID, $dist) = $dbOutput->fetchrow_array)
    {
      if ($dist >= $catAvgDist)
      {
        $colorCode = "#198754";
      }
      elsif ((($catAvgDist - $dist) / $catAvgDist) < 0.25)
      {
        $colorCode = "#FFC107";
      }
      else
      {
        $colorCode = "#DC3545";
      }
      $jsonData .= "{\"label\": \"$prodBaseNameHash{$prodID}\", \"value\": \"$dist\", \"color\": \"$colorCode\"},\n";
    }
    chop($jsonData);  chop($jsonData);

    $jsonData .= <<JSON_LABEL;
  ]
}
JSON_LABEL

    print($jsonData);
  }



  #----------------- Velocities of all Brands -------------------

  if ($chart eq "brands_velocity")
  {
    $brandNameHash{0} = "Category Average";

    #get category's average dist for color coding decisions
    $query = "SELECT velocity52 FROM $dsSchema.$AInsightsBrandTable \
        WHERE brandID=0 AND geographyID=$geoID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($catAvgVelocity) = $dbOutput->fetchrow_array;

    $jsonData = <<JSON_LABEL;
{
  "chart":
  {
    "theme": "zune",
    "yaxisname": "Units / Pt Dist",
    "yaxisnamefontsize": "14",
    "yAxisValueFontSize": "12",
    "showlegend": "0",
    "labelFontSize": "12",
    "valueFontSize": "12",
    "valueFontColor": "#808080",
    "placeValuesInside": "0",
    "decimals": "1"
  },
  "data": [
JSON_LABEL

    #build hash of our distribution in each geography, maintain total
    $query = "SELECT brandID, velocity52 FROM $dsSchema.$AInsightsBrandTable \
        WHERE geographyID=$geoID AND velocity52 > 0 ORDER BY velocity52 DESC";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($brandID, $velocity) = $dbOutput->fetchrow_array)
    {
      if ($brandID == 0)
      {
        $colorCode = "#00AEEF";
      }
      elsif ($velocity >= $catAvgVelocity)
      {
        $colorCode = "#198754";
      }
      elsif ((($catAvgDist - $velocity) / $catAvgVelocity) < 0.25)
      {
        $colorCode = "#FFC107";
      }
      else
      {
        $colorCode = "#DC3545";
      }

      if ($brandID == $ownBrandID)
      {
        $brandNameHash{$brandID} = "<b>$brandNameHash{$brandID}</b>";
      }

      $jsonData .= "{\"label\": \"$brandNameHash{$brandID}\", \"value\": \"$velocity\", \"color\": \"$colorCode\"},\n";
    }
    chop($jsonData);  chop($jsonData);

    $jsonData .= <<JSON_LABEL;
  ]
}
JSON_LABEL

    print($jsonData);
  }



  #----------------- Own Brand Geo Velocities -------------------

  if ($chart eq "velocity_geos")
  {
    %geoBaseNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "g");

    #build hash of category's average dist in each geo for color coding decisions
    $query = "SELECT geographyID, velocity52 FROM $dsSchema.$AInsightsBrandTable WHERE brandID=0";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($geoID, $velocity) = $dbOutput->fetchrow_array)
    {
      $geoAvgVelocity{$geoID} = $velocity;
    }

    $jsonData = <<JSON_LABEL;
{
  "chart":
  {
    "theme": "zune",
    "yaxisname": "Units / Pt Dist",
    "yaxisnamefontsize": "14",
    "yAxisValueFontSize": "12",
    "showlegend": "0",
    "labelFontSize": "12",
    "valueFontSize": "12",
    "valueFontColor": "#808080",
    "placeValuesInside": "0",
    "decimals": "1"
  },
  "data": [
JSON_LABEL

    $query = "SELECT geographyID, velocity52 FROM $dsSchema.$AInsightsBrandTable \
        WHERE brandID=$ownBrandID AND velocity52 > 0 ORDER BY velocity52 DESC";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($geoID, $velocity) = $dbOutput->fetchrow_array)
    {
      if ($velocity >= $geoAvgVelocity{$geoID})
      {
        $colorCode = "#198754";
      }
      elsif ((($geoAvgVelocity{$geoID} - $velocity) / $geoAvgVelocity{$geoID}) < 0.25)
      {
        $colorCode = "#FFC107";
      }
      else
      {
        $colorCode = "#DC3545";
      }
      $jsonData .= "{\"label\": \"$geoBaseNameHash{$geoID}\", \"value\": \"$velocity\", \"color\": \"$colorCode\"},\n";
    }
    chop($jsonData);  chop($jsonData);

    $jsonData .= <<JSON_LABEL;
  ]
}
JSON_LABEL

    print($jsonData);
  }


  #----------------- Own Brand Item Velocities -------------------

  if ($chart eq "velocity_items")
  {
    %prodBaseNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "p");

    #get category's average dist for color coding decisions
    $query = "SELECT velocity52 FROM $dsSchema.$AInsightsBrandTable \
        WHERE brandID=0 AND geographyID=$geoID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($catAvgVelocity) = $dbOutput->fetchrow_array;

    $jsonData = <<JSON_LABEL;
{
  "chart":
  {
    "theme": "zune",
    "yaxisname": "Units / Pt Dist",
    "yaxisnamefontsize": "14",
    "yAxisValueFontSize": "12",
    "showlegend": "0",
    "labelFontSize": "12",
    "valueFontSize": "12",
    "valueFontColor": "#808080",
    "placeValuesInside": "0",
    "decimals": "1"
  },
  "data": [
JSON_LABEL

    $query = "SELECT productID, velocity52 FROM $dsSchema.$AInsightsItemTable \
        WHERE brandID=$ownBrandID AND velocity52 > 0 AND geographyID=$geoID \
        ORDER BY velocity52 DESC";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($prodID, $velocity) = $dbOutput->fetchrow_array)
    {
      if ($velocity >= $catAvgVelocity)
      {
        $colorCode = "#198754";
      }
      elsif ((($catAvgVelocity - $velocity) / $catAvgVelocity) < 0.25)
      {
        $colorCode = "#FFC107";
      }
      else
      {
        $colorCode = "#DC3545";
      }
      $jsonData .= "{\"label\": \"$prodBaseNameHash{$prodID}\", \"value\": \"$velocity\", \"color\": \"$colorCode\"},\n";
    }
    chop($jsonData);  chop($jsonData);

    $jsonData .= <<JSON_LABEL;
  ]
}
JSON_LABEL

    print($jsonData);
  }


  #----------------- Category Assortment -------------------

  if ($chart eq "cat_assort")
  {
    %productBaseNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "p");

    %brandItemHash = DSRseg_get_segment_membership_hash($db, $dsSchema, "p", $brandSegID);

    #NB: the distribution values of items don't actually roll up to the brand
    #   values (they're averages), so we need to scale them to keep the brand
    #   slices the right relative size.
    $query = "SELECT brandID, SUM(avgDist52) FROM $dsSchema.$AInsightsItemTable \
        WHERE geographyID=$geoID AND avgDist52 > 1 GROUP BY brandID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($brandID, $dist) = $dbOutput->fetchrow_array)
    {
      $brandItemSumDistHash{$brandID} = $dist;
    }

    #build brand-level JSON
    $query = "SELECT brandID, name, avgDist52 \
        FROM $dsSchema.$AInsightsBrandTable \
        WHERE geographyID=$geoID AND avgDist52 > 1 AND brandID > 0";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($brandID, $brandName, $avgDist) = $dbOutput->fetchrow_array)
    {
      $brandDistLevel{$brandID} = $avgDist;
      $avgDist = sprintf("%.1f", $avgDist);
      $sunburstJSON .= "{\"id\": \"SMT_$brandID\", \"name\": \"$brandName\", \"value\": \"$avgDist\"},\n";
    }

    #build item-level JSON
    $query = "SELECT productID, avgDist52 \
        FROM $dsSchema.$AInsightsItemTable \
        WHERE geographyID=$geoID AND avgDist52 > 1";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($productID, $avgDist) = $dbOutput->fetchrow_array)
    {
      $valPct = $avgDist / $brandItemSumDistHash{$brandItemHash{$productID}};
      $val = $valPct * $brandDistLevel{$brandItemHash{$productID}};
      $avgDist = sprintf("%.1f", $avgDist);
      $name = $productBaseNameHash{$productID};
      $sunburstJSON .= "{\"id\": \"$productID\", \"parent\": \"SMT_$brandItemHash{$productID}\", \"name\": \"$name\", \"value\": \"$val\", \"tooltext\": \"\$label\"},\n";
    }
    chop($sunburstJSON);  chop($sunburstJSON);

    $jsonData = <<JSON_LABEL;
{
  "chart":
  {
    "baseFontColor": "ffffff",
    "theme": "zune",
    "innerRadius": "0"
  },
  "data": [ $sunburstJSON ]
}
JSON_LABEL

    print($jsonData);
  }


  #----------------- Competitive Flow -------------------

  if ($chart eq "comp_flow")
  {

    #grab any brands that our distribution shifts to when we decrease dist levels
    $query = "SELECT brandID, value FROM $dsSchema.$AInsightsBrandCalcTable \
        WHERE geographyID=$geoID AND name='dist_correlation_own_brand'";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($brandID, $value) = $dbOutput->fetchrow_array)
    {
      if ($brandID == 0)
      {
        $brandName = "Rest of Category";
      }
      else
      {
        $brandName = $brandNameHash{$brandID};
      }
      $flowValueHash{$brandName} = abs($value);
    }

    $jsonData = <<JSON_LABEL;
{
  "chart":
  {
    "theme": "zune",
    "animation": "0",
    "nodeLabelFontSize": "12",
    "showLegend": "0",
    "orientation": "horizontal",
    "linkalpha": 30,
    "linkhoveralpha": 60,
    "nodelabelposition": "start"
  },
  "nodes":
  [
    {
      "label": "$brandNameHash{$ownBrandID}"
    },
JSON_LABEL

    foreach $brandName (keys %flowValueHash)
    {
      $jsonData .= <<JSON_LABEL;
    {
      "label": "$brandName"
    },
JSON_LABEL
    }
    chop($jsonData);  chop($jsonData);

    $jsonData .= <<JSON_LABEL;
  ],
  "links":
  [
JSON_LABEL

    foreach $brandName (keys %flowValueHash)
    {
      $jsonData .= <<JSON_LABEL;
      {
        "from": "$brandNameHash{$ownBrandID}",
        "to": "$brandName",
        "value": $flowValueHash{$brandName}
      },
JSON_LABEL
    }
    chop($jsonData);  chop($jsonData);


    $jsonData .= <<JSON_LABEL;
  ]
}
JSON_LABEL

  print($jsonData);
}

#EOF
