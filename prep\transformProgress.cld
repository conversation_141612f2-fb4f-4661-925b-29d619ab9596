#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;
use Text::CSV;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepRecipes;
use Lib::PrepUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($kapDB);

  print <<END_HTML;
<DOCCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Transforming Data</TITLE>
<LINK REL="SHORTCUT ICON" HREF="$Lib::KoalaConfig::kapHostURL/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
let statusTimer = setInterval(function(){displayStatus()}, 1000);
let statCount = 0;

function displayStatus()
{
  const url = 'ajaxAPI.cld?svc=job_op_status&j=$jobID';

  \$.get(url, function(data, status)
  {
    let statusText = data;

    if (statusText.search('DONE') == 0)
    {
      \$('#progress-bar').css('width', '100%');
      clearInterval(statusTimer);
      location.href='$uri';
      return;
    }

    let statElements = statusText.split('|');

    let opTitle = statElements[0];
    let opPct = statElements[1];
    let opDetails = statElements[2];
    let opTimeEstimate = statElements[3];
    let opExtra = statElements[4];
    document.getElementById('div-op-title').innerHTML = opTitle;
    if (opPct == "101")
    {
      document.getElementById('progress-bar').innerHTML = "Working...";
      \$('#progress-bar').css('width', '100%');
    }
    else if (opPct.length > 0)
    {
      document.getElementById('progress-bar').innerHTML = opPct + '%';
      \$('#progress-bar').css('width', opPct+'%');
    }
    document.getElementById('div-op-details').innerHTML = opDetails;
    document.getElementById('div-op-extra').innerHTML = opExtra;
  });

  if (statCount > 5)
  {
    clearInterval(statusTimer);
    statusTimer = setInterval(function(){displayStatus()}, 5000);
  }
  statCount++;
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($kapDB, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="$Lib::KoalaConfig::kapHostURL/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">Data Prep</A></LI>
    <LI CLASS="breadcrumb-item">$flowName</LI>
    <LI CLASS="breadcrumb-item active">Transforming Data</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------
#
# Output the HTML for the web page that displays the status dialog
#

sub print_status_html
{
  print <<END_HTML;
  <DIV CLASS="container">

    <DIV CLASS="row">

      <DIV CLASS="col"></DIV>  <!-- spacing -->

      <DIV CLASS="col-xs-12 col-sm-11 col-md-8 col-lg-6 col-xl-5"> <!-- content -->

        <DIV CLASS="card border-primary mx-auto">
          <DIV CLASS="card-header bg-primary text-white">Transforming Data</DIV>
          <DIV CLASS="card-body">

            <H5 ID="div-op-title"></H5>
            <DIV CLASS="progress" style="height:25px;">
              <DIV ID="progress-bar" CLASS="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" STYLE="width:0%;">
              </DIV>
            </DIV>

            <P>
            <DIV ID="div-op-details"></DIV>
            <DIV ID="div-op-extra"></DIV>

            <P>&nbsp;</P>
            <DIV CLASS="text-center">
              <BUTTON CLASS="btn btn-primary" ID="btnAsync" onClick="location.href='main.cld'"><I CLASS="bi bi-speedometer"></I> Be More Productive</BUTTON>
            </DIV>
          </DIV>
        </DIV>

      </DIV>  <!-- content -->

      <DIV CLASS="col"></DIV>  <!-- spacing -->

    </DIV>  <!-- row -->
  </DIV> <!-- container -->
END_HTML

  print_html_footer();
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get the CGI input variables
  $flowID = $q->param('f');
  $jobID = $q->param('j');
  $colID = $q->param('col');
  $action = $q->param('a');
  $uri = $q->param('uri');

  if (length($uri) < 5)
  {
    $uri = "flowViewData.cld?f=$flowID&j=$jobID";
  }

  #connect to the database
  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  $flowName = prep_flow_id_to_name($prepDB, $flowID);

  #if we're rejoining an existing transform job
  if (length($action) < 1)
  {
    $query = "SELECT state FROM prep.jobs WHERE ID=$jobID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($state) = $dbOutput->fetchrow_array;
    if ($state eq "TRANSFORM-DATA")
    {
      print_html_header();
      print_status_html();
      exit;
    }
  }

  #make sure we have write privs for this data flow
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs ne "W")
  {
    print_html_header();
    exit_error("You don't have privileges to run transforms in this data flow.");
  }

  #make sure the prep cloud isn't out of storage
  $usagePct = prep_flow_storage($acctType);
  if ($usagePct > 99)
  {
    print_html_header();
    exit_error("Your Koala Data Prep cloud is out of storage - please contact your Koala account manager to order more.")
  }

  #store our state in the database
  if ($jobID > 0)
  {
    $query = "UPDATE prep.jobs SET state='TRANSFORM-DATA' WHERE ID=$jobID";
    $prepDB->do($query);
  }

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #build name of column we're transforming
  $column = "column_" . $colID;

  #instantiate our CSV object
  $csv = Text::CSV->new( {binary => 1} );


  # -------------------------

  #if we're being asked to append/prepend values
  if ($action eq "TRANS-CELL-APPEND-PREPEND")
  {

    #get CGI parameters specific to this operation
    $prepend = $q->param('prepend');
    $append = $q->param('append');

    #get the name of the column we're ronding values in
    $query = "SELECT name FROM $masterColTable WHERE ID=$colID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($colName) = $dbOutput->fetchrow_array;

    $step = "TRANS-CELL-APPEND-PREPEND|COL=$colName|PREPEND=$prepend|APPEND=$append";

    $stepID = prep_recipe_add_step($prepDB, $flowID, $step);

    prep_audit($prepDB, $userID, "Added new recipe step to append/prepend values in $colName", $flowID);
    utils_slack("PREP: $first $last added a new recipe step to append/prepend values in $colName in $flowName");
  }


  # -------------------------

  #if we're being asked to set values
  elsif ($action eq "TRANS-CELL-SET")
  {

    #get CGI parameters specific to this operation
    $checkCol = $q->param('checkCol');
    $matchOp = $q->param('matchOp');
    $matchText = $q->param('matchText');
    $replaceText = $q->param('replaceText');

    #get the name of the column we're ronding values in
    $query = "SELECT name FROM $masterColTable WHERE ID=$colID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($colName) = $dbOutput->fetchrow_array;

    $step = "TRANS-CELL-SET|COL=$colName|CHECK=$checkCol|OP=$matchOp|MATCH=$matchText|REPL=$replaceText";

    $stepID = prep_recipe_add_step($prepDB, $flowID, $step);

    prep_audit($prepDB, $userID, "Added new recipe step to set values in $colName", $flowID);
    utils_slack("PREP: $first $last added a new recipe step to set values in $colName in $flowName");
  }


  # -------------------------

  #if we're being asked to replace values
  elsif ($action eq "TRANS-CELL-REPLACE")
  {

    #get CGI parameters specific to this operation
    $checkCol = $q->param('checkCol');
    $matchOp = $q->param('matchOp');
    $matchText = $q->param('matchText');
    $replaceCol = $q->param('replaceCol');

    #get the name of the column we're replacing values in
    $query = "SELECT name FROM $masterColTable WHERE ID=$colID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($colName) = $dbOutput->fetchrow_array;

    $step = "TRANS-CELL-REPLACE|COL=$colName|CHECK=$checkCol|OP=$matchOp|MATCH=$matchText|REPL=$replaceCol";

    $stepID = prep_recipe_add_step($prepDB, $flowID, $step);

    prep_audit($prepDB, $userID, "Added new recipe step to replace values in $colName", $flowID);
    utils_slack("PREP: $first $last added a new recipe step to replace values in $colName in $flowName");
  }


  # -------------------------

  #if we're being asked to search & replace values
  elsif ($action eq "TRANS-CELL-SEARCH-REPLACE")
  {

    #get CGI parameters specific to this operation
    $searchText = $q->param('searchText');
    $searchOp = $q->param('searchOp');
    $replaceText = $q->param('replaceText');

    #get the name of the column we're replacing values in
    $query = "SELECT name FROM $masterColTable WHERE ID=$colID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($colName) = $dbOutput->fetchrow_array;

    $step = "TRANS-CELL-SEARCH-REPLACE|COL=$colName|OP=$searchOp|SEARCH=$searchText|REPL=$replaceText";

    $stepID = prep_recipe_add_step($prepDB, $flowID, $step);

    prep_audit($prepDB, $userID, "Added new recipe step to replace values in $colName $searchOp with $searchText with $replaceText", $flowID);
    utils_slack("PREP: $first $last added a new recipe step to replace values in $colName $searchOp with $searchtext with $replaceText in $flowName");
  }


  # -------------------------

  #if we're being asked to round values
  elsif ($action eq "TRANS-CELL-ROUND")
  {

    #get CGI parameters specific to this operation
    $decimals = $q->param('decimals');

    #get the name of the column we're ronding values in
    $query = "SELECT name FROM $masterColTable WHERE ID=$colID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($colName) = $dbOutput->fetchrow_array;

    $step = "TRANS-CELL-ROUND|COL=$colName|DEC=$decimals";

    $stepID = prep_recipe_add_step($prepDB, $flowID, $step);

    prep_audit($prepDB, $userID, "Added new recipe step to round values in $colName", $flowID);
    utils_slack("PREP: $first $last added a new recipe step to round values in $colName in $flowName");
  }


  # -------------------------

  #if we're being asked to ceiling values
  elsif ($action eq "TRANS-CELL-CEILING")
  {

    #get the name of the column we're ceiling
    $query = "SELECT name FROM $masterColTable WHERE ID=$colID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($colName) = $dbOutput->fetchrow_array;

    $step = "TRANS-CELL-CEILING|COL=$colName";

    $stepID = prep_recipe_add_step($prepDB, $flowID, $step);

    prep_audit($prepDB, $userID, "Added new recipe step to ceiling values in $colName", $flowID);
    utils_slack("PREP: $first $last added a new recipe step to ceiling values in $colName in $flowName");
  }


  # -------------------------

  #if we're being asked to floor values
  elsif ($action eq "TRANS-CELL-FLOOR")
  {

    #get the name of the column we're flooring
    $query = "SELECT name FROM $masterColTable WHERE ID=$colID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($colName) = $dbOutput->fetchrow_array;

    $step = "TRANS-CELL-FLOOR|COL=$colName";

    $stepID = prep_recipe_add_step($prepDB, $flowID, $step);

    prep_audit($prepDB, $userID, "Added new recipe step to floor values in $colName", $flowID);
    utils_slack("PREP: $first $last added a new recipe step to floor values in $colName in $flowName");
  }


  # -------------------------

  #if we're being asked to convert NAs to zeroes
  elsif ($action eq "TRANS-CELL-NAZERO")
  {

    #get the name of the column we're flooring
    $query = "SELECT name FROM $masterColTable WHERE ID=$colID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($colName) = $dbOutput->fetchrow_array;

    $step = "TRANS-CELL-NAZERO|COL=$colName";

    $stepID = prep_recipe_add_step($prepDB, $flowID, $step);

    prep_audit($prepDB, $userID, "Added new recipe step to convert NAs to zeroes in $colName", $flowID);
    utils_slack("PREP: $first $last added new recipe step to convert NAs to zeroes in $colName in $flowName");
  }


  # -------------------------

  #if we're being asked to strip HTML tags from cells' contents
  elsif ($action eq "TRANS-CELL-HTML")
  {

    #get the name of the column we're changing cap styles on
    $query = "SELECT name FROM $masterColTable WHERE ID=$colID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($colName) = $dbOutput->fetchrow_array;

    $step = "TRANS-CELL-HTML|COL=$colName";

    $stepID = prep_recipe_add_step($prepDB, $flowID, $step);

    prep_audit($prepDB, $userID, "Added new recipe step to strip HTML tags from cells in $colName", $flowID);
    utils_slack("PREP: $first $last added a new recipe step to strip HTML tags from cells in $colName in $flowName");
  }


  # -------------------------

  #if we're being asked to change cell's capitalization style
  elsif ($action eq "TRANS-CELL-SPACE")
  {

    #get CGI parameters specific to this operation
    $compressWS = $q->param('compressWS');
    $trimWS = $q->param('trimWS');

    #get the name of the column we're changing cap styles on
    $query = "SELECT name FROM $masterColTable WHERE ID=$colID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($colName) = $dbOutput->fetchrow_array;

    $step = "TRANS-CELL-SPACE|COL=$colName|COMP=$compressWS|TRIM=$trimWS";

    $stepID = prep_recipe_add_step($prepDB, $flowID, $step);

    prep_audit($prepDB, $userID, "Added new recipe step to clean up whitespace in $colName", $flowID);
    utils_slack("PREP: $first $last added a new recipe step to clean up whitespace in $colName in $flowName");
  }


  # -------------------------

  #if we're being asked to change cell's capitalization style
  elsif ($action eq "TRANS-CELL-CASE")
  {

    #get CGI parameters specific to this operation
    $case = $q->param('case');

    #get the name of the column we're changing cap styles on
    $query = "SELECT name FROM $masterColTable WHERE ID=$colID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($colName) = $dbOutput->fetchrow_array;

    $step = "TRANS-CELL-CASE|COL=$colName|CASE=$case";

    $stepID = prep_recipe_add_step($prepDB, $flowID, $step);

    prep_audit($prepDB, $userID, "Added new recipe step to set capitalization style of $colName", $flowID);
    utils_slack("PREP: $first $last added a new recipe step to set capitalization style of $colName in $flowName");
  }


  # -------------------------

  #if we're being asked to change cells' date format
  elsif ($action eq "TRANS-CELL-DATE")
  {

    #get CGI parameters specific to this operation
    $durationFormat = $q->param('durationFormat');
    $timeFormat = $q->param('timeFormat');

    #get the name of the column we're changing cap styles on
    $query = "SELECT name FROM $masterColTable WHERE ID=$colID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($colName) = $dbOutput->fetchrow_array;

    $step = "TRANS-CELL-DATE|COL=$colName|TIME=$timeFormat|DUR=$durationFormat";

    $stepID = prep_recipe_add_step($prepDB, $flowID, $step);

    prep_audit($prepDB, $userID, "Added new recipe step to format date values in $colName", $flowID);
    utils_slack("PREP: $first $last added a new recipe step to format date values in $colName in $flowName");
  }


  # -------------------------

  #if we're being asked to "enrich" a date with CPG qualities
  elsif ($action eq "TRANS-CELL-ENRICH-DATE")
  {

    #get CGI parameters specific to this operation
    $cpgdates = $q->param('cpgdates');
    $duration = $q->param('duration');
    $type = $q->param('type');
    $overwrite = $q->param('overwrite');
    $roundDates = $q->param('roundDates');
    $dayOfWeek = $q->param('dayOfWeek');
    $roundingDirection = $q->param('roundingDirection');

    if ($cpgdates ne "on")
    {
      $duration = 0;
      $type = 0;
    }
    if ($roundDates ne "on")
    {
      $dayOfWeek = 0;
    }

    #get the name of the column we're changing cap styles on
    $query = "SELECT name FROM $masterColTable WHERE ID=$colID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($colName) = $dbOutput->fetchrow_array;

    $step = "TRANS-CELL-ENRICH-DATE|COL=$colName|DUR=$duration|TYPE=$type|OVERWRITE=$overwrite|ROUNDDAY=$dayOfWeek|DIR=$roundingDirection";

    $stepID = prep_recipe_add_step($prepDB, $flowID, $step);

    prep_audit($prepDB, $userID, "Added new recipe step to enrich CPG dates in $colName", $flowID);
    utils_slack("PREP: $first $last added a new recipe step to enrich CPG dates in $colName in $flowName");
  }


  # -------------------------

  #if we're being asked to change cells' UPC format
  elsif ($action eq "TRANS-CELL-UPC")
  {

    #get CGI parameters specific to this operation
    $upcOp = $q->param('op');
    $upcNormalizationOp = $q->param('normOp');
    $trimLength = $q->param('trim-length');

    if (length($upcNormalizationOp) > 2)
    {
      $upcOp = $upcNormalizationOp;
    }

    #get the name of the column we're formatting UPCs in
    $query = "SELECT name FROM $masterColTable WHERE ID=$colID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($colName) = $dbOutput->fetchrow_array;

    $step = "TRANS-CELL-UPC|COL=$colName|OP=$upcOp|LENGTH=$trimLength|";

    $stepID = prep_recipe_add_step($prepDB, $flowID, $step);

    prep_audit($prepDB, $userID, "Added new recipe step to format UPC values in $colName", $flowID);
    utils_slack("PREP: $first $last added a new recipe step to format UPC values in $colName in $flowName");
  }


  # -------------------------

  #if we're being asked to normalize weights and measures
  elsif ($action eq "TRANS-CELL-WEIGHTS")
  {

    #get CGI parameters specific to this operation
    $measSystem = $q->param('system');
    if ($measSystem eq "standard")
    {
      $volPref = $q->param('volPrefStd');
      $weightPref = $q->param('wghtPrefStd');
      $dimPref = $q->param('dimPrefStd');
    }
    else
    {
      $volPref = $q->param('volPrefMetric');
      $weightPref = $q->param('wghtPrefMetric');
      $dimPref = $q->param('dimPrefMetric');
    }

    #get the name of the column we're normalizing
    $query = "SELECT name FROM $masterColTable WHERE ID=$colID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($colName) = $dbOutput->fetchrow_array;

    $step = "TRANS-CELL-WEIGHTS|COL=$colName|SYSTEM=$measSystem|VOL=$volPref|WEIGHT=$weightPref|DIM=$dimPref|";

    $stepID = prep_recipe_add_step($prepDB, $flowID, $step);

    prep_audit($prepDB, $userID, "Added new recipe step to normalize weights and measures in $colName", $flowID);
    utils_slack("PREP: $first $last added a new recipe step to normalize weights and measures in $colName in $flowName");
  }


  # -------------------------

  #if we're being asked to change the column's type
  elsif ($action eq "TRANS-COL-TYPE")
  {

    #get CGI parameters specific to this operation
    $colType = $q->param('colType');

    #get the name of the column we're splitting
    $query = "SELECT name FROM $masterColTable WHERE ID=$colID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($colName) = $dbOutput->fetchrow_array;

    $step = "TRANS-COL-TYPE|COL=$colName|TYPE=$colType";

    $stepID = prep_recipe_add_step($prepDB, $flowID, $step);

    prep_audit($prepDB, $userID, "Added new recipe step to change type of $colName to $colType", $flowID);
    utils_slack("PREP: $first $last added a new recipe step to change type of $colName to $colType in $flowName");
  }


  # -------------------------

  #if we're being asked to add & perform a new split by separator step
  elsif ($action eq "TRANS-COL-SPLIT")
  {

    #get CGI parameters specific to this operation
    $sepChar = $q->param('sepChar');
    $otherChar = $q->param('otherChar');

    #get the name of the column we're splitting
    $query = "SELECT name FROM $masterColTable WHERE ID=$colID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($colName) = $dbOutput->fetchrow_array;

    $step = "TRANS-COL-SPLIT|COL=$colName|SEP=$sepChar|OTHER=$otherChar";

    $stepID = prep_recipe_add_step($prepDB, $flowID, $step);

    prep_audit($prepDB, $userID, "Added new recipe step to split $colName on $sepChar", $flowID);
    utils_slack("PREP: $first $last added a new recipe step to split $colname on $sepChar in $flowName");
  }


  # -------------------------

  #if we're being asked to add & perform a new split by length step
  elsif ($action eq "TRANS-COL-SPLIT-LENGTH")
  {

    #get CGI parameters specific to this operation
    $fieldSpec = $q->param('fieldSpec');

    #get the name of the column we're splitting
    $query = "SELECT name FROM $masterColTable WHERE ID=$colID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($colName) = $dbOutput->fetchrow_array;

    $step = "TRANS-COL-SPLIT-LENGTH|COL=$colName|FIELDS=$fieldSpec";

    $stepID = prep_recipe_add_step($prepDB, $flowID, $step);

    prep_audit($prepDB, $userID, "Split column $colName on field lengths $fieldSpec", $flowID);
    utils_slack("PREP: $first $last added recipe step to split $colName on field lengths $fieldSpec in $flowName");
  }


  # -------------------------

  #if we're being asked to add & perform a new VLOOKUP step
  elsif ($action eq "TRANS-COL-LOOKUP")
  {

    #get CGI parameters specific to this operation
    $lookupFile = $q->param('t');

    #get the lookup column name
    $query = "SELECT name FROM $masterColTable WHERE ID=$colID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($colName) = $dbOutput->fetchrow_array;

    $step = "TRANS-COL-LOOKUP|COL=$colName|TABLE=$lookupFile";

    $stepID = prep_recipe_add_step($prepDB, $flowID, $step);

    if ($lookupFile =~ m/^(.*)\|TAB=/)
    {
      $lookupFile = $1;
    }
    prep_audit($prepDB, $userID, "Added new recipe step to do a lookup in $lookupFile on the $colName column", $flowID);
    utils_slack("PREP: $first $last added new recipe step to do a lookup in $lookupFile on $colName in $flowName");
  }


  # -------------------------

  #if we're being asked to add & perform a new insert column step
  elsif ($action eq "TRANS-COL-INSERT")
  {

    #get CGI parameters specific to this operation
    $newColName = $q->param('newColName');
    $colType = $q->param('colType');

    $step = "TRANS-COL-INSERT|COL=$newColName|TYPE=$colType";

    $stepID = prep_recipe_add_step($prepDB, $flowID, $step);

    prep_audit($prepDB, $userID, "Added new recipe step to insert a $colType column named $newColName", $flowID);
    utils_slack("PREP: $first $last added new recipe step to insert a $colType column named $newColName in $flowName");
  }


  # -------------------------

  #if we're being asked to add & perform a new copy column step
  elsif ($action eq "TRANS-COL-COPY")
  {

    #get CGI parameters specific to this operation
    $operation = $q->param('operation');
    $numVal = $q->param('numVal');
    $prepend = $q->param('prepend');
    $append = $q->param('append');

    #get the lookup column name
    $query = "SELECT name, type FROM $masterColTable WHERE ID=$colID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($colName, $colType) = $dbOutput->fetchrow_array;

    if ($colType eq "measure")
    {
      $step = "TRANS-COL-COPY|COL=$colName|OP=$operation|NUM=$numVal";
    }
    else
    {
      $step = "TRANS-COL-COPY|COL=$colName|PRE=$prepend|APP=$append";
    }

    $stepID = prep_recipe_add_step($prepDB, $flowID, $step);

    prep_audit($prepDB, $userID, "Added new recipe step to copy column $colName", $flowID);
    utils_slack("PREP: $first $last added a new recipe step to copy column $colName in $flowName");
  }


  # -------------------------

  #if we're being asked to merge columns
  elsif ($action eq "TRANS-COL-MERGE")
  {

    #get CGI parameters specific to this operation
    $innerText = $q->param('innerText');
    $mergeCol = $q->param('mergeCol');

    #get the merged column name
    $query = "SELECT name FROM $masterColTable WHERE ID=$colID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($colName) = $dbOutput->fetchrow_array;

    $step = "TRANS-COL-MERGE|COL=$colName|MERGE=$mergeCol|INNER=$innerText";

    $stepID = prep_recipe_add_step($prepDB, $flowID, $step);

    prep_audit($prepDB, $userID, "Added new recipe step to merge columns $colName and $mergeCol", $flowID);
    utils_slack("PREP: $first $last added a new recipe step to merge columns $colName and $mergeCol in $flowName");
  }


  # -------------------------

  #if we're being asked to add & perform a new column discard step
  elsif ($action eq "TRANS-COL-DISCARD")
  {

    #get the discarded column name
    $query = "SELECT name FROM $masterColTable WHERE ID=$colID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($colName) = $dbOutput->fetchrow_array;

    $step = "TRANS-COL-DISCARD|COL=$colName";

    $stepID = prep_recipe_add_step($prepDB, $flowID, $step);

    prep_audit($prepDB, $userID, "Added new recipe step to discard column $colName", $flowID);
    utils_slack("PREP: $first $last added new recipe step to discard column $colName in $flowName");
  }


  # -------------------------

  #if we're being asked to add & perform a new empty column discard step
  elsif ($action eq "TRANS-COL-DISCARD-EMPTY")
  {

    $step = "TRANS-COL-DISCARD-EMPTY|";

    $stepID = prep_recipe_add_step($prepDB, $flowID, $step);

    prep_audit($prepDB, $userID, "Added new recipe step to discard empty columns", $flowID);
    utils_slack("PREP: $first $last added new recipe step to discard empty columns in $flowName");
  }


  # -------------------------

  #if we're being asked to trim rows based on a column value
  elsif ($action eq "TRANS-COL-TRIM-VALUES")
  {
    $trimOp = $q->param('trimOp');
    $matchOp = $q->param('matchOp');
    $matchText = $q->param('matchText');

    #get the discarded column name
    $query = "SELECT name FROM $masterColTable WHERE ID=$colID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($colName) = $dbOutput->fetchrow_array;

    $step = "TRANS-COL-TRIM-VALUES|COL=$colName|OP=$trimOp|MATCH=$matchOp|VALUE=$matchText";

    $stepID = prep_recipe_add_step($prepDB, $flowID, $step);

    prep_audit($prepDB, $userID, "Added new recipe step to trim ($trimOp) values where $colName $matchOp $matchText", $flowID);
    utils_slack("PREP: $first $last added new recipe step to trim ($trimOp) values where $colName $matchOp $matchText in $flowName");
  }


  # -------------------------

  #else if we're being asked to add & perform a new data trimming step
  elsif ($action eq "TRANS-COL-TRIM-DATA")
  {

    #get CGI parameters specific to this operation
    $lookupFile = $q->param('t');
    $matchOp = $q->param('matchOp');
    $stepID = $q->param('s');
    $colName = $q->param('colName');

    undef(@tmp);
    @names = $q->param;
    foreach $name (@names)
    {
      if ($name =~ m/^T (.*)$/)
      {
        $val = $q->param($name);
        if ($val eq "on")
        {
          push(@tmp, $1);
        }
      }
    }
    $csv->combine(@tmp);
    $dataFields = $csv->string;

    #if we weren't supplied a column name to trim from, go get it
    if (length($colName) < 1)
    {
      $query = "SELECT name FROM $masterColTable WHERE ID=$colID";
      $dbOutput = $prepDB->prepare($query);
      $dbOutput->execute;
      ($colName) = $dbOutput->fetchrow_array;
    }

    $step = "TRANS-COL-TRIM-DATA|COL=$colName|OP=$matchOp|DATA=$dataFields";

    #if we're editing an existing step, save it and redirect back to edit recipe
    if ($stepID > 0)
    {
      prep_recipe_edit_step($prepDB, $flowID, $stepID, $step);

      prep_audit($prepDB, $userID, "Edited a recipe step to trim data in $colName", $flowID);
      utils_slack("PREP: $first $last edited a recipe step to trim data in $colName in $flowName");

      print("Status: 302 Moved temporarily\n");
      print("Location: /app/prep/recipeEdit.cld?f=$flowID\n\n");
      exit;
    }

    $stepID = prep_recipe_add_step($prepDB, $flowID, $step);

    prep_audit($prepDB, $userID, "Added new recipe step to trim data in $colName", $flowID);
    utils_slack("PREP: $first $last added new recipe step to trim data in $colName in $flowName");
  }


  # -------------------------

  #if we're being asked to match up products with a data source
  elsif ($action eq "TRANS-COL-PRODUCT-MATCH")
  {
    $matchDSID = $q->param('ds');
    $unmatchedAction = $q->param('unmatched');

    #get the discarded column name
    $query = "SELECT name FROM $masterColTable WHERE ID=$colID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($colName) = $dbOutput->fetchrow_array;

    $step = "TRANS-COL-PRODUCT-MATCH|COL=$colName|DS=$matchDSID|UNMATCHED=$unmatchedAction|";

    #make sure we aren't adding a duplicate recipe step (which will just dupe
    #the effort for the same result)
    $q_step = $prepDB->quote($step);
    $query = "SELECT step FROM prep.recipes WHERE flowID=$flowID AND action=$q_step";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($stepID) = $dbOutput->fetchrow_array;
    if ($stepID < 1)
    {
      $stepID = prep_recipe_add_step($prepDB, $flowID, $step);
    }

    prep_audit($prepDB, $userID, "Added new recipe step to match products in $colName", $flowID);
    utils_slack("PREP: $first $last added new recipe step to match products in $colName in $flowName");
  }


  # -------------------------

  #else if we're being asked to build an SQL index on a column
  elsif ($action eq "TRANS-COL-INDEX")
  {

    #get the column name we're trimming from
    $query = "SELECT name FROM $masterColTable WHERE ID=$colID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($colName) = $dbOutput->fetchrow_array;

    #NB: we don't store this as a recipe step - we do it on-demand from the
    #    trim data and analyze operations

    prep_audit($prepDB, $userID, "Indexed/analyzed $colName", $flowID);
    utils_slack("PREP: $first $last indexed/analyzed $colName in $flowName");
  }


  # -------------------------

  print_html_header();

  #fork a new process to do the actual data transformation in the background
  $SIG{CHLD} = "IGNORE";
  if ($pid = fork())
  {
    #parent process

    print_status_html();
  }
  else
  {
    #child process

    #let Apache know not to wait on the child process
    close(STDIN);
    close(STDOUT);

    #redirect STDERR to the Koala error log
    close(STDERR);
    open(STDERR, ">>/opt/apache/htdocs/tmp/koala_error.log");
    select(STDERR);
    $| = 1;

    #reconnect to the database
    $kapDB = KAPutil_connect_to_database();
    $prepDB = PrepUtils_connect_to_database();

    #if the system is fully loaded, back off and wait for an open process slot
    $okToRun = prep_flow_available_job_slot($prepDB, $kapDB, $jobID, $userID);
    while (!$okToRun)
    {
      $query = "UPDATE prep.jobs \
          SET PID=$$, opInfo='0|Waiting in processing queue', state='TRANSFORM-DATA-WAIT' WHERE ID=$jobID";
      $prepDB->do($query);
      PrepUtils_set_job_op_details($prepDB, $jobID, "Waiting in processing queue");

      sleep(120);

      $okToRun = prep_flow_available_job_slot($prepDB, $kapDB, $jobID, $userID);
    }

    #set initial status
    $opInfo = "0|Transforming data";
    $q_opInfo = $prepDB->quote($opInfo);
    $query = "UPDATE prep.jobs \
        SET PID=$$, opInfo=$q_opInfo, state='TRANSFORM-DATA' WHERE ID=$jobID";
    $prepDB->do($query);

    #perform the requested recipe operation in the background
    if ($action eq "TRANS-CELL-APPEND-PREPEND")
    {
      $status = prep_recipe_trans_cell_append_prepend($prepDB, $flowID, $jobID, $stepID);
    }
    elsif ($action eq "TRANS-CELL-SET")
    {
      $status = prep_recipe_trans_cell_set($prepDB, $flowID, $jobID, $stepID);
    }
    elsif ($action eq "TRANS-CELL-REPLACE")
    {
      $status = prep_recipe_trans_cell_replace($prepDB, $flowID, $jobID, $stepID);
    }
    elsif ($action eq "TRANS-CELL-SEARCH-REPLACE")
    {
      $status = prep_recipe_trans_cell_search_replace($prepDB, $flowID, $jobID, $stepID);
    }
    elsif ($action eq "TRANS-CELL-ROUND")
    {
      $status = prep_recipe_trans_cell_round($prepDB, $flowID, $jobID, $stepID);
    }
    elsif ($action eq "TRANS-CELL-CEILING")
    {
      $status = prep_recipe_trans_cell_ceiling($prepDB, $flowID, $jobID, $stepID);
    }
    elsif ($action eq "TRANS-CELL-FLOOR")
    {
      $status = prep_recipe_trans_cell_floor($prepDB, $flowID, $jobID, $stepID);
    }
    elsif ($action eq "TRANS-CELL-NAZERO")
    {
      $status = prep_recipe_trans_cell_nazero($prepDB, $flowID, $jobID, $stepID);
    }
    elsif ($action eq "TRANS-CELL-HTML")
    {
      $status = prep_recipe_trans_cell_html($prepDB, $flowID, $jobID, $stepID);
    }
    elsif ($action eq "TRANS-CELL-SPACE")
    {
      $status = prep_recipe_trans_cell_space($prepDB, $flowID, $jobID, $stepID);
    }
    elsif ($action eq "TRANS-CELL-CASE")
    {
      $status = prep_recipe_trans_cell_case($prepDB, $flowID, $jobID, $stepID);
    }
    elsif ($action eq "TRANS-CELL-DATE")
    {
      $status = prep_recipe_trans_cell_date($prepDB, $flowID, $jobID, $stepID);
    }
    elsif ($action eq "TRANS-CELL-ENRICH-DATE")
    {
      $status = prep_recipe_trans_cell_enrich_date($prepDB, $flowID, $jobID, $stepID);
    }
    elsif ($action eq "TRANS-CELL-UPC")
    {
      $status = prep_recipe_trans_cell_upc($prepDB, $flowID, $jobID, $stepID);
    }
    elsif ($action eq "TRANS-CELL-WEIGHTS")
    {
      $status = prep_recipe_trans_cell_weights($prepDB, $flowID, $jobID, $stepID);
    }
    elsif ($action eq "TRANS-COL-TYPE")
    {
      $status = prep_recipe_trans_col_type($prepDB, $flowID, $jobID, $stepID);
    }
    elsif ($action eq "TRANS-COL-SPLIT")
    {
      $status = prep_recipe_trans_col_split($prepDB, $flowID, $jobID, $stepID);
    }
    elsif ($action eq "TRANS-COL-SPLIT-LENGTH")
    {
      $status = prep_recipe_trans_col_split_length($prepDB, $flowID, $jobID, $stepID);
    }
    elsif ($action eq "TRANS-COL-LOOKUP")
    {
      $status = prep_recipe_trans_col_lookup($prepDB, $flowID, $jobID, $stepID);
    }
    elsif ($action eq "TRANS-COL-INSERT")
    {
      $status = prep_recipe_trans_col_insert($prepDB, $flowID, $jobID, $stepID);
    }
    elsif ($action eq "TRANS-COL-COPY")
    {
      $status = prep_recipe_trans_col_copy($prepDB, $flowID, $jobID, $stepID);
    }
    elsif ($action eq "TRANS-COL-MERGE")
    {
      $status = prep_recipe_trans_col_merge($prepDB, $flowID, $jobID, $stepID);
    }
    elsif ($action eq "TRANS-COL-DISCARD")
    {
      $status = prep_recipe_trans_col_discard($prepDB, $flowID, $jobID, $stepID);
    }
    elsif ($action eq "TRANS-COL-DISCARD-EMPTY")
    {
      $status = prep_recipe_trans_col_discard_empty($prepDB, $flowID, $jobID, $stepID);
    }
    elsif ($action eq "TRANS-COL-TRIM-VALUES")
    {
      $status = prep_recipe_trans_col_trim_values($prepDB, $flowID, $jobID, $stepID);
    }
    elsif ($action eq "TRANS-COL-TRIM-DATA")
    {
      $status = prep_recipe_trans_col_trim_data($prepDB, $flowID, $jobID, $stepID);
    }
    elsif ($action eq "TRANS-COL-PRODUCT-MATCH")
    {
      $status = prep_recipe_trans_col_product_match($prepDB, $flowID, $jobID, $stepID);
    }
    elsif ($action eq "TRANS-COL-INDEX")
    {
      $status = prep_recipe_trans_col_index($prepDB, $flowID, $jobID, $colID);
    }

    #if a fatal error occurred (as opposed to a warning)
    if ($status < 0)
    {
      $query = "UPDATE prep.jobs \
          SET PID=NULL, opInfo='DONE', state='ERR|Fatal error performing a recipe step', lastAction=NOW() \
          WHERE ID=$jobID";
      $prepDB->do($query);
    }
  }

#EOF
