#!/usr/bin/perl

#
# Deletes the specified time periods from all data sources that are
# fed by Data Prep.
# Usually used when Nielsen stops reporting a given market/set of markets.
#

use lib "/opt/apache/app/";

use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::PrepUtils;

  @geoDeleteArray = ('Ingles Total Rem', 'Ingles Total TA', 'Ingles Total xAOC Rem');

  #connect to the database
  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #grab the ID and name of every data source being fed by Prep from the AOD
  #IDW on the system
  $query = "SELECT DISTINCT dsID FROM prep.flows \
      WHERE sourceInfo LIKE 'FTP=nielsen|%' AND dsID > 0";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  $dsStr = "";
  while (($dsID) = $dbOutput->fetchrow_array)
  {
    $dsStr .= "$dsID,";
  }
  chop($dsStr);

  $query = "SELECT ID, name FROM app.dataSources WHERE ID IN ($dsStr)";
  $dbOutput = $kapDB->prepare($query);
  $dbOutput->execute;
  while (($dsID, $dsName) = $dbOutput->fetchrow_array)
  {
    $dsNameHash{$dsID} = $dsName;
  }

  #cycle through every data source, looking for geographies to be removed
  foreach $dsID (sort keys %dsNameHash)
  {
    $dsSchema = "datasource_" . $dsID;
    print("\n\n$dsID     $dsNameHash{$dsID}\n");
    print("--------------------------------------------------------------\n");

    foreach $geoName (@geoDeleteArray)
    {
      $q_geoName = $kapDB->quote($geoName);

      #start by finding a matching geography in the data source
      $query = "SELECT ID FROM $dsSchema.geographies WHERE name = $q_geoName";
      $dbOutput = $kapDB->prepare($query);
      $dbOutput->execute;
      ($geoID) = $dbOutput->fetchrow_array;

      #if we didn't find a matching old geography, dump it and move on
      if ($geoID < 1)
      {
        #print("$oldGeo not found, skipped\n");
        next;
      }

      #delete the old geography
      $query = "DELETE FROM $dsSchema.facts WHERE geographyID=$geoID";
      $kapDB->do($query);

      #remove the (now duplicate) geography
      $query = "DELETE FROM $dsSchema.geographies WHERE ID=$geoID";
      $kapDB->do($query);

      print("Found and deleted obsolete geography $geoName\n");
      $kapDB->do("UPDATE dataSources SET lastModified=NOW() WHERE ID=$dsID");
    }
  }

#EOF
