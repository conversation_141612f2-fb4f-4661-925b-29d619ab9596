#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::AInsights::AInsights;
use Lib::AInsights::Utils;

#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/plain\n\n");

  #get the CGI input variables
  $priceModelID = $q->param('pm');
  $chart = $q->param('c');
  $geoID = $q->param('g');

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsID = AInsights_get_dsID($db, $priceModelID);

  $dsSchema = "datasource_" . $dsID;

  AInsights_Utils_initialize_constants($priceModelID);

  #get displayable names for our DSR dimensions
  %timeperiodNames = dsr_get_item_name_hash($db, $dsSchema, "t");

  $ownBrandID = AInsights_Utils_get_own_brand_id($db, $priceModelID);
  $brandSegID = AInsights_Utils_get_brand_seg_id($db, $priceModelID);

  %brandNameHash = DSRseg_get_segments_hash($db, $dsSchema, "p", $brandSegID);
  $ownBrandName = $brandNameHash{$ownBrandID};

  #get competitive brands
  ($compID1, $compID2) = AInsights_Utils_get_brand_comp_ids($db, $dsSchema, $geoID);
  $compBrand1Name = $brandNameHash{$compID1};
  $compBrand2Name = $brandNameHash{$compID2};

  #if we're doing the unit trend line chart
  if ($chart eq "unit_trnd")
  {

    #figure out the chrono-order of the time periods we want to graph
    $query = "SELECT ID, endDate FROM $dsSchema.timeperiods \
        WHERE duration=1 AND type=30 ORDER BY endDate DESC LIMIT 52";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($timeID, $endDate) = $dbOutput->fetchrow_array)
    {
      $timeIDStr .= "'$timeID',";
      push(@orderedTimeIDs, $timeID);

      $endDate =~ m/^(.*?) /;
      $endDateHash{$timeID} = $1;
    }
    chop($timeIDStr);
    @orderedTimeIDs = reverse(@orderedTimeIDs);

    $jsonData = <<JSON_LABEL;
{
  "chart":
  {
    "theme": "zune",
    "animation": "0",
    "drawanchors": "0",
    "yaxisname": "Units Sold",
    "yaxisnamefontsize": "14",
    "yAxisValueFontSize": "12",
    "numvdivlines": "10",
    "divlinealpha": "30",
    "labelpadding": "10",
    "labelstep": "4",
    "labelfontsize": "12",
    "labeldisplay": "rotate",
    "slantlabel": "1",
    "legenditemfontsize": "12",
    "useEllipsesWhenOverflow": "0",
    "yaxisvaluespadding": "10",
    "showvalues": "0"
  },
JSON_LABEL

    #output time periods (X axis values)
    $jsonData .= <<JSON_LABEL;
  "categories": [
    {
      "category": [
JSON_LABEL

    foreach $timeID (@orderedTimeIDs)
    {
    $jsonData .= <<JSON_LABEL;
        {
          "label": "$endDateHash{$timeID}"
        },
JSON_LABEL
    }
    chop($jsonData);  chop($jsonData);

    $jsonData .= <<JSON_LABEL;
      ]
    }
  ],
  "dataset": [

JSON_LABEL

    #output the data set for the category
    undef(%unitsValueHash);
    $query = "SELECT timeID, units FROM $dsSchema.$AInsightsBrandCube \
        WHERE brandID=0 AND geographyID=$geoID AND timeID IN ($timeIDStr)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($timeID, $units) = $dbOutput->fetchrow_array)
    {
      $unitsValueHash{$timeID} = $units;
    }

    if ($status > 0)
    {

      $jsonData .= <<JSON_LABEL;
  {
    "seriesname": "Category Average",
    "data": [
JSON_LABEL

      foreach $timeID (@orderedTimeIDs)
      {
        $units = $unitsValueHash{$timeID};
        $jsonData .= "{ \"value\": \"$units\"},\n";
      }
      chop($jsonData);  chop($jsonData);

      $jsonData .= <<JSON_LABEL;
    ]
  }
JSON_LABEL
    }


    #output the data set for our brand (if there is data)
    undef(%unitsValueHash);
    $query = "SELECT timeID, units FROM $dsSchema.$AInsightsBrandCube \
        WHERE brandID=$ownBrandID AND geographyID=$geoID AND timeID IN ($timeIDStr)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($timeID, $units) = $dbOutput->fetchrow_array)
    {
      $unitsValueHash{$timeID} = $units;
    }

    if ($status > 0)
    {
      $jsonData .= <<JSON_LABEL;
  ,{
    "seriesname": "$ownBrandName",
    "data": [
JSON_LABEL

      foreach $timeID (@orderedTimeIDs)
      {
        $units = $unitsValueHash{$timeID};
        $jsonData .= "{ \"value\": \"$units\"},\n";
      }
      chop($jsonData);  chop($jsonData);

      $jsonData .= <<JSON_LABEL;
    ]
  }
JSON_LABEL
    }

    #output the data set for our first key competitor
    undef(%unitsValueHash);
    $query = "SELECT timeID, units FROM $dsSchema.$AInsightsBrandCube \
        WHERE brandID=$compID1 AND geographyID=$geoID AND timeID IN ($timeIDStr)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($timeID, $units) = $dbOutput->fetchrow_array)
    {
      $unitsValueHash{$timeID} = $units;
    }

    if ($status > 0)
    {

      $jsonData .= <<JSON_LABEL;
  ,{
    "seriesname": "$compBrand1Name",
    "data": [
JSON_LABEL

      foreach $timeID (@orderedTimeIDs)
      {
        $units = $unitsValueHash{$timeID};
        $jsonData .= "{ \"value\": \"$units\"},\n";
      }
      chop($jsonData);  chop($jsonData);

      $jsonData .= <<JSON_LABEL;
    ]
  }
JSON_LABEL
    }

    #output the data set for our second key competitor
    undef(%unitsValueHash);
    $query = "SELECT timeID, units FROM $dsSchema.$AInsightsBrandCube \
        WHERE brandID=$compID2 AND geographyID=$geoID AND timeID IN ($timeIDStr)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($timeID, $units) = $dbOutput->fetchrow_array)
    {
      $unitsValueHash{$timeID} = $units;
    }

    if ($status > 0)
    {

      $jsonData .= <<JSON_LABEL;
  ,{
    "seriesname": "$compBrand2Name",
    "data": [
JSON_LABEL

      foreach $timeID (@orderedTimeIDs)
      {
        $units = $unitsValueHash{$timeID};
        $jsonData .= "{ \"value\": \"$units\"},\n";
      }
      chop($jsonData);  chop($jsonData);

      $jsonData .= <<JSON_LABEL;
    ]
  }
JSON_LABEL
    }

    $jsonData .= <<JSON_LABEL;
  ]
}
JSON_LABEL

    print $jsonData;
  }


  #----------------- Unit Sales of all Brands -------------------

  if ($chart eq "brands_units")
  {
    $brandNameHash{0} = "Category Average";

    #get category's average dist for color coding decisions
    $query = "SELECT units52 FROM $dsSchema.$AInsightsBrandTable \
        WHERE geographyID=$geoID AND brandID=0";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($catAvgUnitSales) = $dbOutput->fetchrow_array;

    $jsonData = <<JSON_LABEL;
{
  "chart":
  {
    "theme": "zune",
    "yaxisname": "Units Sold",
    "yaxisnamefontsize": "14",
    "yAxisValueFontSize": "12",
    "showlegend": "0",
    "labelFontSize": "12",
    "valueFontSize": "12",
    "valueFontColor": "#808080",
    "placeValuesInside": "0",
    "decimals": "0"
  },
  "data": [
JSON_LABEL

    $query = "SELECT brandID, units52 FROM $dsSchema.$AInsightsBrandTable \
        WHERE geographyID=$geoID AND units52 > 0 ORDER BY units52 DESC";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($brandID, $units) = $dbOutput->fetchrow_array)
    {
      if ($brandID == 0)
      {
        $colorCode = "#00AEEF";
        $units = $catAvgUnitSales;
      }
      elsif ($units >= $catAvgUnitSales)
      {
        $colorCode = "#198754";
      }
      elsif ((($catAvgUnitSales - $units) / $catAvgUnitSales) < 0.25)
      {
        $colorCode = "#FFC107";
      }
      else
      {
        $colorCode = "#DC3545";
      }

      if ($brandID == $ownBrandID)
      {
        $brandNameHash{$brandID} = "<b>$brandNameHash{$brandID}</b>";
      }

      $jsonData .= "{\"label\": \"$brandNameHash{$brandID}\", \"value\": \"$units\", \"color\": \"$colorCode\"},\n";
    }
    chop($jsonData);  chop($jsonData);

    $jsonData .= <<JSON_LABEL;
  ]
}
JSON_LABEL

    print($jsonData);
  }



  #---------------------- Brand Share --------------------------

  if ($chart eq "brand_share")
  {
    $jsonData = <<JSON_LABEL;
{
  "chart":
  {
    "theme": "zune",
    "showlegend": "0",
    "labelFontSize": "12",
    "valueFontSize": "12",
    "decimals": "1"
  },
  "data": [
JSON_LABEL

    $query = "SELECT brandID, share52 FROM $dsSchema.$AInsightsBrandTable
        WHERE geographyID=$geoID AND share52 > 0 ORDER BY share52 DESC";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($brandID, $share) = $dbOutput->fetchrow_array)
    {
      if ($brandID == 0)
      {
        next;
      }

      if ($brandID == $ownBrandID)
      {
        $brandNameHash{$brandID} = "<b>$brandNameHash{$brandID}</b>";
      }

      $jsonData .= "{\"label\": \"$brandNameHash{$brandID}\", \"value\": \"$share\", \"color\": \"$colorCode\"},\n";
    }
    chop($jsonData);  chop($jsonData);

    $jsonData .= <<JSON_LABEL;
  ]
}
JSON_LABEL

    print($jsonData);
  }



  #----------------- Own Brand Unit Sales Across Geos -------------------

  if ($chart eq "brand_geos")
  {
    %geoBaseNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "g");

    #build hash of category's average dist in each geo for color coding decisions
    $query = "SELECT geographyID, units52 FROM $dsSchema.$AInsightsBrandTable WHERE brandID=0";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($id, $units) = $dbOutput->fetchrow_array)
    {
      $geoAvgUnits{$id} = $units;
    }

    $jsonData = <<JSON_LABEL;
{
  "chart":
  {
    "theme": "zune",
    "yaxisname": "Units Sold",
    "yaxisnamefontsize": "14",
    "yAxisValueFontSize": "12",
    "showlegend": "0",
    "labelFontSize": "12",
    "valueFontSize": "12",
    "valueFontColor": "#808080",
    "placeValuesInside": "0",
    "decimals": "1"
  },
  "data": [
JSON_LABEL

    $query = "SELECT geographyID, units52 FROM $dsSchema.$AInsightsBrandTable \
        WHERE brandID=$ownBrandID AND units52 > 0 ORDER BY units52 DESC";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($id, $units) = $dbOutput->fetchrow_array)
    {
      if ($units >= $geoAvgUnits{$id})
      {
        $colorCode = "#198754";
      }
      elsif ((($geoAvgUnits{$id} - $units) / $geoAvgUnits{$id}) < 0.25)
      {
        $colorCode = "#FFC107";
      }
      else
      {
        $colorCode = "#DC3545";
      }

      if ($id == $geoID)
      {
        $geoBaseNameHash{$id} = "<b>$geoBaseNameHash{$id}</b>";
      }

      $jsonData .= "{\"label\": \"$geoBaseNameHash{$id}\", \"value\": \"$units\", \"color\": \"$colorCode\"},\n";
    }
    chop($jsonData);  chop($jsonData);

    $jsonData .= <<JSON_LABEL;
  ]
}
JSON_LABEL

    print($jsonData);
  }



  #----------------- Own Brand Item Sales -------------------

  if ($chart eq "brand_items")
  {
    %prodBaseNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "p");

    #get category's average dist for color coding decisions
    $query = "SELECT units52 FROM $dsSchema.$AInsightsBrandTable \
        WHERE brandID=0 AND geographyID=$geoID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($catAvgUnits) = $dbOutput->fetchrow_array;

    $jsonData = <<JSON_LABEL;
{
  "chart":
  {
    "theme": "zune",
    "yaxisname": "Units Sold",
    "yaxisnamefontsize": "14",
    "yAxisValueFontSize": "12",
    "showlegend": "0",
    "labelFontSize": "12",
    "valueFontSize": "12",
    "valueFontColor": "#808080",
    "placeValuesInside": "0",
    "decimals": "1"
  },
  "data": [
JSON_LABEL

    $query = "SELECT productID, units52 FROM $dsSchema.$AInsightsItemTable \
        WHERE brandID=$ownBrandID AND units52 > 0 AND geographyID=$geoID \
        ORDER BY units52 DESC";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($prodID, $units) = $dbOutput->fetchrow_array)
    {
      if ($units >= $catAvgUnits)
      {
        $colorCode = "#198754";
      }
      elsif ((($catAvgUnits - $units) / $catAvgUnits) < 0.25)
      {
        $colorCode = "#FFC107";
      }
      else
      {
        $colorCode = "#DC3545";
      }
      $jsonData .= "{\"label\": \"$prodBaseNameHash{$prodID}\", \"value\": \"$units\", \"color\": \"$colorCode\"},\n";
    }
    chop($jsonData);  chop($jsonData);

    $jsonData .= <<JSON_LABEL;
  ]
}
JSON_LABEL

    print($jsonData);
  }


  #----------------- Competitive Flow -------------------

  if ($chart eq "comp_flow")
  {

    #grab any brands that our unit sales shift to when ours decreases
    $query = "SELECT brandID, value FROM $dsSchema.$AInsightsBrandCalcTable \
        WHERE geographyID=$geoID AND name='sales_correlation_own_brand'";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($brandID, $value) = $dbOutput->fetchrow_array)
    {
      if ($brandID == 0)
      {
        $brandName = "Rest of Category";
      }
      else
      {
        $brandName = $brandNameHash{$brandID};
      }
      $flowValueHash{$brandName} = abs($value);
    }

    $jsonData = <<JSON_LABEL;
{
  "chart":
  {
    "theme": "zune",
    "nodeLabelFontSize": "12",
    "showLegend": "0",
    "orientation": "horizontal",
    "linkalpha": 30,
    "linkhoveralpha": 60,
    "nodelabelposition": "start"
  },
  "nodes":
  [
    {
      "label": "$brandNameHash{$ownBrandID}"
    },
JSON_LABEL

    foreach $brandName (keys %flowValueHash)
    {
      $jsonData .= <<JSON_LABEL;
    {
      "label": "$brandName"
    },
JSON_LABEL
    }
    chop($jsonData);  chop($jsonData);

    $jsonData .= <<JSON_LABEL;
  ],
  "links":
  [
JSON_LABEL

    foreach $brandName (keys %flowValueHash)
    {
      $jsonData .= <<JSON_LABEL;
      {
        "from": "$brandNameHash{$ownBrandID}",
        "to": "$brandName",
        "value": $flowValueHash{$brandName}
      },
JSON_LABEL
    }
    chop($jsonData);  chop($jsonData);


    $jsonData .= <<JSON_LABEL;
  ]
}
JSON_LABEL

  print($jsonData);
}


#EOF
