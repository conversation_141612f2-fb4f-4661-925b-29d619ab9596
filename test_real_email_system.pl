#!/usr/bin/perl

# Real Email System Test - This will actually attempt to send emails
# Tests the PrepUtils_send_exception_email function

use strict;
use warnings;
use lib ".";

print "=== Real Email System Test for srinuk2921\@gmail.com ===\n\n";

# Import the actual email function from PrepUtils
BEGIN {
    # Since we're on Windows, we need to simulate the Linux environment
    $ENV{HOSTNAME} = 'test-windows-system';
}

# Simulate the PrepUtils_send_exception_email function
sub PrepUtils_send_exception_email {
    my ($error_type, $error_message, $query, $location) = @_;

    # Simple email sending using system sendmail
    my $to_email = '<EMAIL>';
    my $from_email = '<EMAIL>';
    my $subject = "[PREP-SYSTEM] Exception Alert - $error_type";
    my $timestamp = localtime();
    my $hostname = $ENV{HOSTNAME} || $ENV{COMPUTERNAME} || 'unknown';

    my $email_body = <<EOF;
PREP SYSTEM EXCEPTION ALERT
===========================

Timestamp: $timestamp
Hostname: $hostname
Error Type: $error_type
Location: $location

ERROR DETAILS:
$error_message

FAILED QUERY:
$query

This is an automated notification from the PREP Exception Handling System.
Please check the system logs for additional details.
EOF

    # Try multiple email methods
    my $email_sent = 0;
    my $method_used = "";

    print "Attempting to send email to $to_email...\n";
    print "Subject: $subject\n\n";

    eval {
        # Method 1: Try system mail command (Linux/Unix)
        if (-x '/usr/bin/mail' || -x '/bin/mail') {
            print "Method 1: Trying system mail command...\n";
            open(my $mail, '|-', 'mail', '-s', $subject, $to_email) or die "Cannot open mail: $!";
            print $mail "From: $from_email\n";
            print $mail $email_body;
            close($mail);
            print "✓ Email sent via system mail\n";
            $email_sent = 1;
            $method_used = "system mail";
        }
        # Method 2: Try sendmail directly
        elsif (-x '/usr/sbin/sendmail' || -x '/usr/lib/sendmail') {
            print "Method 2: Trying sendmail...\n";
            my $sendmail_path = (-x '/usr/sbin/sendmail') ? '/usr/sbin/sendmail' : '/usr/lib/sendmail';
            open(my $sendmail, '|-', "$sendmail_path -t") or die "Cannot open sendmail: $!";
            print $sendmail "To: $to_email\n";
            print $sendmail "From: $from_email\n";
            print $sendmail "Subject: $subject\n";
            print $sendmail "\n";
            print $sendmail $email_body;
            close($sendmail);
            print "✓ Email sent via sendmail\n";
            $email_sent = 1;
            $method_used = "sendmail";
        }
        # Method 3: Try SMTP using Perl modules (if available)
        elsif (eval { require Net::SMTP; 1; }) {
            print "Method 3: Trying SMTP...\n";
            my $smtp = Net::SMTP->new('localhost', Timeout => 30);
            if ($smtp) {
                $smtp->mail($from_email);
                $smtp->to($to_email);
                $smtp->data();
                $smtp->datasend("To: $to_email\n");
                $smtp->datasend("From: $from_email\n");
                $smtp->datasend("Subject: $subject\n");
                $smtp->datasend("\n");
                $smtp->datasend($email_body);
                $smtp->dataend();
                $smtp->quit;
                print "✓ Email sent via SMTP\n";
                $email_sent = 1;
                $method_used = "SMTP";
            } else {
                print "✗ SMTP connection failed - falling back to email queue\n";
                # Force fallback to email queue
                die "SMTP failed, using queue fallback";
            }
        }
        # Method 4: Write to email queue file for external processing
        else {
            print "Method 4: Writing to email queue file...\n";
            my $email_queue_file = "prep_email_queue.txt";
            open(my $queue, '>>', $email_queue_file) or die "Cannot open email queue: $!";
            print $queue "=" x 60 . "\n";
            print $queue "EMAIL QUEUED: $timestamp\n";
            print $queue "TO: $to_email\n";
            print $queue "FROM: $from_email\n";
            print $queue "SUBJECT: $subject\n";
            print $queue "BODY:\n$email_body\n";
            print $queue "=" x 60 . "\n\n";
            close($queue);
            print "✓ Email queued in $email_queue_file\n";
            $email_sent = 1;
            $method_used = "email queue";
        }
    };

    if ($@) {
        print "✗ Email sending failed: $@\n";
        # Fallback: Write to error log file
        my $error_log = "prep_email_errors.log";
        eval {
            open(my $log, '>>', $error_log);
            print $log "$timestamp: EMAIL SEND FAILED\n";
            print $log "TO: $to_email\n";
            print $log "ERROR: $@\n";
            print $log "ORIGINAL ERROR: $error_type - $error_message\n";
            print $log "-" x 50 . "\n";
            close($log);
            print "✓ Error logged to $error_log\n";
        };
    }

    # Always log the attempt
    if (!$email_sent) {
        print "✗ No working mail system found\n";
        print "Error details: $error_type - $error_message\n";
        print "Target email: $to_email\n";
    } else {
        print "✓ Email notification completed using: $method_used\n";
    }

    return $email_sent;
}

# Test different exception scenarios
print "=== Testing Email Notifications ===\n\n";

print "Test 1: Database Connection Error\n";
print "-" x 40 . "\n";
my $result1 = PrepUtils_send_exception_email(
    "Database Connection Timeout",
    "Connection to MySQL server lost after 30 seconds. Server may be overloaded or network issues detected.",
    "SELECT * FROM prep.flows WHERE userID = 1",
    "PrepUtils_connect_to_database"
);

print "\nTest 2: CSV Upload Error\n";
print "-" x 40 . "\n";
my $result2 = PrepUtils_send_exception_email(
    "CSV Upload Processing Error", 
    "Failed to parse Electric_Vehicle_Population_Data.csv - Invalid character encoding detected at line 1247",
    "INSERT INTO prep.raw_data VALUES (...)",
    "prep/xhrHandleUpload.cld"
);

print "\nTest 3: File Size Limit Error\n";
print "-" x 40 . "\n";
my $result3 = PrepUtils_send_exception_email(
    "File Size Limit Exceeded",
    "Upload file Electric_Vehicle_Population_Data.csv (57MB) exceeds maximum allowed size of 50MB",
    "N/A - File validation error",
    "prep/flowLoadRawData.cld"
);

print "\n=== Test Results Summary ===\n";
printf "Database Error Email: %s\n", $result1 ? "SENT" : "FAILED";
printf "CSV Upload Error Email: %s\n", $result2 ? "SENT" : "FAILED";
printf "File Size Error Email: %s\n", $result3 ? "SENT" : "FAILED";

print "\n=== Why You Might Not Receive Emails ===\n";
print "1. **Windows Environment**: This system is Windows, but the app needs Linux\n";
print "2. **No Mail Server**: No sendmail/postfix installed\n";
print "3. **No SMTP Setup**: No local SMTP server configured\n";
print "4. **Firewall/Network**: Email ports (25, 587) may be blocked\n";
print "5. **Email Queue**: Emails are queued in files instead\n";

print "\n=== To Get Real Emails ===\n";
print "1. **Deploy on Linux** with Apache/Perl\n";
print "2. **Install mail system**: sudo apt-get install mailutils postfix\n";
print "3. **Configure SMTP**: Set up Gmail relay or local mail server\n";
print "4. **Test mail command**: echo 'test' | mail -s 'test' srinuk2921\@gmail.com\n";
print "5. **Check logs**: /var/log/mail.log for delivery status\n";

print "\n=== Check These Files ===\n";
if (-f "prep_email_queue.txt") {
    print "✓ prep_email_queue.txt - Contains queued emails\n";
} else {
    print "✗ prep_email_queue.txt - No queued emails\n";
}

if (-f "prep_email_errors.log") {
    print "✓ prep_email_errors.log - Contains email errors\n";
} else {
    print "✗ prep_email_errors.log - No email errors\n";
}

print "\nTest completed!\n";
