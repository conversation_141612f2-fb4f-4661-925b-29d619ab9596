#!/usr/bin/perl

################################################################################
#
# Rename all of the segments in the BRAND and BRANDS segmentations to remove
# a vendor name included in parantheses - works around an issue BAIR has with
# the way Nielsen is formatting the BRAND segmentation in Connect data.
#
################################################################################

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;



  #connect to the database
  $db = KAPutil_connect_to_database();

  #grab list of all data sources in the cloud
  %dsNameHash = ds_get_name_hash($db);

  #cycle through data source list looking for BRAND and BRANDS hash
  foreach $dsID (keys %dsNameHash)
  {

    print("\n--------------------------------------------------------------\n");
    print("$dsID: $dsNameHash{$dsID}\n");

    $dsSchema = "datasource_$dsID";

    #get list of all segmentations in data source
    %segHash = DSRsegmentation_get_segmentations_hash($db, $dsSchema, "p");
    %segIDHash = reverse(%segHash);

    $brandSegID = $segIDHash{'BRAND'};
    $brandsSegID = $segIDHash{'BRANDS'};
    $brandFamilySegID = $segIDHash{'BRAND FAMILY'};
    $brandLowSegID = $segIDHash{'BRAND LOW'};

    if (($brandSegID < 1) || ($brandsSegID < 1))
    {
      print("Didn't find BRAND and BRANDS, skipping\n");
      next;
    }

    #get segments in affected segmentations
    %brandSegNameHash = DSRseg_get_segments_hash($db, $dsSchema, 'p', $brandSegID);
    %brandsSegNameHash = DSRseg_get_segments_hash($db, $dsSchema, 'p', $brandsSegID);
    %brandFamilySegNameHash = DSRseg_get_segments_hash($db, $dsSchema, 'p', $brandFamilySegID);
    %brandLowSegNameHash = DSRseg_get_segments_hash($db, $dsSchema, 'p', $brandLowSegID);

    #foreach BRAND segment, update name
    foreach $segmentID (keys %brandSegNameHash)
    {
      if ($brandSegNameHash{$segmentID} =~ m/^NO BRAND LISTED \((.*)\)/)
      {
        $newName = $1;
      }
      elsif ($brandSegNameHash{$segmentID} =~ m/(.*) \(.*\)/)
      {
        $newName = $1;
      }
      else
      {
        $newName = $brandSegNameHash{$segmentID};
      }
      $q_newName = $db->quote($newName);
      $query = "UPDATE $dsSchema.product_segment SET name=$q_newName WHERE ID=$segmentID";
      print("$brandSegNameHash{$segmentID} -> $newName\n");
      $db->do($query);
    }


    #foreach BRANDS segment, update name
    foreach $segmentID (keys %brandsSegNameHash)
    {
      if ($brandsSegNameHash{$segmentID} =~ m/^NO BRAND LISTED \((.*)\)/)
      {
        $newName = $1;
      }
      elsif ($brandsSegNameHash{$segmentID} =~ m/(.*) \(.*\)/)
      {
        $newName = $1;
      }
      else
      {
        $newName = $brandsSegNameHash{$segmentID};
      }
      $q_newName = $db->quote($newName);
      $query = "UPDATE $dsSchema.product_segment SET name=$q_newName WHERE ID=$segmentID";
      print("$brandsSegNameHash{$segmentID} -> $newName\n");
      $db->do($query);
    }

    #foreach BRAND FAMILY segment, update name
    foreach $segmentID (keys %brandFamilySegNameHash)
    {
      if ($brandFamilySegNameHash{$segmentID} =~ m/^NO BRAND LISTED \((.*)\)/)
      {
        $newName = $1;
      }
      elsif ($brandFamilySegNameHash{$segmentID} =~ m/(.*) \(.*\)/)
      {
        $newName = $1;
      }
      else
      {
        $newName = $brandFamilySegNameHash{$segmentID};
      }
      $q_newName = $db->quote($newName);
      $query = "UPDATE $dsSchema.product_segment SET name=$q_newName WHERE ID=$segmentID";
      print("$brandFamilySegNameHash{$segmentID} -> $newName\n");
      $db->do($query);
    }

    #foreach BRAND LOW segment, update name
    foreach $segmentID (keys %brandLowSegNameHash)
    {
      if ($brandLowSegNameHash{$segmentID} =~ m/^NO BRAND LISTED \((.*)\)/)
      {
        $newName = $1;
      }
      elsif ($brandLowSegNameHash{$segmentID} =~ m/(.*) \(.*\)/)
      {
        $newName = $1;
      }
      else
      {
        $newName = $brandLowSegNameHash{$segmentID};
      }
      $q_newName = $db->quote($newName);
      $query = "UPDATE $dsSchema.product_segment SET name=$q_newName WHERE ID=$segmentID";
      print("$brandLowSegNameHash{$segmentID} -> $newName\n");
      $db->do($query);
    }

    $query = "UPDATE app.dataSources SET lastModified=NOW() WHERE ID=$dsID";
    $db->do($query);
  }
