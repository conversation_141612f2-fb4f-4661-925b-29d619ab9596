#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::Reports;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $rptID = $q->param('rptID');
  $visID = $q->param('v');
  $showValues = $q->param('showValues');
  $valueFontColor = $q->param('valueFontColor');
  $formatNumberScale = $q->param('formatNumberScale');
  $showPercentValues = $q->param('showPercentValues');
  $decimals = $q->param('decimals');
  $placeValuesInside = $q->param('placeValuesInside');
  $rotateValues = $q->param('rotateValues');
  $valueFontSize = $q->param('valueFontSize');
  $valueFont = $q->param('valueFont');
  $showValuesBg = $q->param('showValuesBg');
  $valueBgColor = $q->param('valueBgColor');
  $valueBgAlpha = $q->param('valueBgAlpha');

  #fix up the CGI parameters from the submitted form
  if (defined($showValues))
  {
    $showValues = ($showValues eq "false") ? "0" : "1";
  }

  $valueFontColor = "#" . $valueFontColor;

  if (defined($formatNumberScale))
  {
    $formatNumberScale = ($formatNumberScale eq "false") ? "0" : "1";
  }

  if (defined($showPercentValues))
  {
    if (($showPercentValues eq "false") || ($showPercentValues eq "undefined"))
    {
      $showPercentValues = "0";
    }
    else
    {
      $showPercentValues = "1";
    }
  }

  if (defined($rotateValues))
  {
    $rotateValues = ($rotateValues eq "false") ? "0" : "1";
  }

  if (defined($showValuesBg))
  {
    $showValuesBg = ($showValuesBg eq "false") ? "0" : "1";
  }

  $valueBgColor = "#" . $valueBgColor;

  $db = KAPutil_connect_to_database();

  #make sure we have edit privs for this report
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this report.");
  }

  #get the chart title details from the database
  $query = "SELECT design FROM visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($graphDesign) = $dbOutput->fetchrow_array;


  ########################################################################
  #
  # This code block is called on submit to save the changes
  #

  if (defined($showValues))
  {

    if ($showValues ne reports_chart_design_default("showValues"))
    {
      $graphDesign = reports_set_style($graphDesign, "showValues", $showValues);
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "showValues");
    }

    if (lc($valueFontColor) ne reports_chart_design_default("valueFontColor"))
    {
      $graphDesign = reports_set_style($graphDesign, "valueFontColor", $valueFontColor);
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "valueFontColor");
    }

    if ($formatNumberScale ne reports_chart_design_default("formatNumberScale"))
    {
      $graphDesign = reports_set_style($graphDesign, "formatNumberScale", $formatNumberScale);
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "formatNumberScale");
    }

    if ($showPercentValues ne reports_chart_design_default("showPercentValues"))
    {
      $graphDesign = reports_set_style($graphDesign, "showPercentValues", $showPercentValues);
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "showPercentValues");
    }

    if ($decimals ne reports_chart_design_default("decimals"))
    {
      $graphDesign = reports_set_style($graphDesign, "decimals", $decimals);
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "decimals");
    }

    if ($placeValuesInside ne reports_chart_design_default("placeValuesInside"))
    {
      $graphDesign = reports_set_style($graphDesign, "placeValuesInside", $placeValuesInside);
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "placeValuesInside");
    }

    if ($rotateValues ne reports_chart_design_default("rotateValues"))
    {
      $graphDesign = reports_set_style($graphDesign, "rotateValues", $rotateValues);
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "rotateValues");
    }

    if ($valueFontSize ne reports_chart_design_default("valueFontSize"))
    {
      $graphDesign = reports_set_style($graphDesign, "valueFontSize", $valueFontSize);
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "valueFontSize");
    }

    if ($valueFont eq "Helvetica")
    {
      $graphDesign = reports_remove_style($graphDesign, "valueFont");
    }
    else
    {
      $graphDesign = reports_set_style($graphDesign, "valueFont", $valueFont);
    }

    if ($showValuesBg ne reports_chart_design_default("showValuesBg"))
    {
      $graphDesign = reports_set_style($graphDesign, "showValuesBg", $showValuesBg);
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "showValuesBg");
    }

    if (lc($valueBgColor) ne reports_chart_design_default("valueBgColor"))
    {
      $graphDesign = reports_set_style($graphDesign, "valueBgColor", $valueBgColor);
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "valueBgColor");
    }

    if ($valueBgAlpha ne reports_chart_design_default("valueBgAlpha"))
    {
      $graphDesign = reports_set_style($graphDesign, "valueBgAlpha", $valueBgAlpha);
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "valueBgAlpha");
    }

    $q_graphDesign = $db->quote($graphDesign);
    $query = "UPDATE visuals SET design = $q_graphDesign WHERE ID=$visID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    $cubeName = cube_id_to_name($db, $rptID);
    $dsID = cube_get_ds_id($db, $rptID);
    $dsName = ds_id_to_name($db, $dsID);

    utils_audit($db, $userID, "Changed chart data value formatting", $dsID, $rptID, 0);
    $activity = "$first $last changed chart data value formatting for $cubeName in $dsName";
    utils_slack($activity);

    exit;
  }


  #########################################################################

  #extract graph settings from design string
  $graphType = reports_get_style($graphDesign, "type");
  $showValues = reports_get_style($graphDesign, "showValues");
  $valueFontColor = reports_get_style($graphDesign, "valueFontColor");
  $formatNumberScale = reports_get_style($graphDesign, "formatNumberScale");
  $showPercentValues = reports_get_style($graphDesign, "showPercentValues");
  $decimals = reports_get_style($graphDesign, "decimals");
  $placeValuesInside = reports_get_style($graphDesign, "placeValuesInside");
  $rotateValues = reports_get_style($graphDesign, "rotateValues");
  $valueFontSize = reports_get_style($graphDesign, "valueFontSize");
  $valueFont = reports_get_style($graphDesign, "valueFont");
  $showValuesBg = reports_get_style($graphDesign, "showValuesBg");
  $valueBgColor = reports_get_style($graphDesign, "valueBgColor");
  $valueBgAlpha = reports_get_style($graphDesign, "valueBgAlpha");

  #set appropriate defaults
  if (length($showValues) < 1)
  {
    $showValues = "CHECKED";
  }
  if (length($valueFontColor) < 7)
  {
    $valueFontColor = "#333333";
  }
  if (length($formatNumberScale) < 1)
  {
    $formatNumberScale = "CHECKED";
  }
  if (length($showPercentValues) < 1)
  {
    $showPercentValues = "";
  }
  if (length($decimals) < 1)
  {
    $decimals = 2;
  }
  if (length($placeValuesInside) < 1)
  {
    $placeValuesInside = 0;
  }
  if (length($rotateValues) < 1)
  {
    $rotateValues = 0;
  }
  if ($valueFontSize < 3)
  {
    $valueFontSize = "10";
  }
  if (length($valueFont) < 3)
  {
    $valueFont = "Helvetica";
  }
  if (length($showValuesBg) < 1)
  {
    $showValuesBg = "";
  }
  if (length($valueBgColor) < 7)
  {
    $valueBgColor = "#CCCCCC";
  }
  if (length($valueBgAlpha) < 1)
  {
    $valueBgAlpha = 90;
  }

  #set up things for HTML form display
  if ($showValues eq "1")
  {
    $showValues = "CHECKED";
  }
  if ($formatNumberScale eq "1")
  {
    $formatNumberScale = "CHECKED";
  }
  if ($showPercentValues eq "1")
  {
    $showPercentValues = "CHECKED";
  }
  if ($rotateValues eq "1")
  {
    $rotateValues = "CHECKED";
  }
  if ($showValuesBg eq "1")
  {
    $showValuesBg = "CHECKED";
  }

  #hide irrelevant options for pie/donut charts to avoid confusing analysts
  if (($graphType =~ m/Pie/) || ($graphType =~ m/Donut/))
  {
    $hideIfPie = "STYLE='display:none;'";
  }


  ########################################################################
  #
  # Everything after this point is called to display the chart layout dialog
  #

  print <<END_HTML;
<SCRIPT>
function submitForm()
{
  let showValues = \$("#showValues").prop("checked");
  let valueFontColor = document.getElementById('valueFontColor').value;
  let formatNumberScale = \$("#formatNumberScale").prop("checked");
  let showPercentValues = \$("#showPercentValues").prop("checked");
  let decimals = document.getElementById('decimals').value;
  let placeValuesInside = document.getElementById('placeValuesInside').value;
  let rotateValues = \$("#rotateValues").prop("checked");
  let valueFontSize = document.getElementById('valueFontSize').value;
  let valueFont = document.getElementById('valueFont').value;
  let showValuesBg = \$("#showValuesBg").prop("checked");
  let valueBgColor = document.getElementById('valueBgColor').value;
  let valueBgAlpha = document.getElementById('valueBgAlpha').value;

  //knock # off of color strings
  valueFontColor = valueFontColor.substr(1);
  valueBgColor = valueBgColor.substr(1);

  let url = "xhrChartDataValues?rptID=$rptID&v=$visID&showValues=" + showValues +
      "&valueFontColor=" + valueFontColor +
      "&formatNumberScale=" + formatNumberScale +
      "&showPercentValues=" + showPercentValues + "&decimals=" + decimals +
      "&placeValuesInside=" + placeValuesInside + "&rotateValues=" + rotateValues +
      "&valueFontSize=" + valueFontSize + "&valueFont=" + valueFont +
      "&showValuesBg=" + showValuesBg + "&valueBgColor=" + valueBgColor +
      "&valueBgAlpha=" + valueBgAlpha;

  \$.get(url, function(data, status)
  {
    location.href = "display.cld?rpt=$rptID&v=$visID";
  });
}



function showBgOptions()
{
  let state = \$("#showValuesBg").prop("checked");

  if (state == true)
  {
    document.getElementById('tr-valueBgColor').style.display = "";
    document.getElementById('tr-valueBgAlpha').style.display = "";
  }
  else
  {
    document.getElementById('tr-valueBgColor').style.display = "none";
    document.getElementById('tr-valueBgAlpha').style.display = "none";
  }
}



function revertDefaults()
{
  document.getElementById('showValues').checked = true;
  document.getElementById('valueFontColor').value = "#333333";
  document.getElementById('formatNumberScale').checked = true;
  try
  {
    document.getElementById('showPercentValues').checked = false;
  } catch {}
  document.getElementById('decimals').value = 2;
  try
  {
    document.getElementById('placeValuesInside').value = "0";
  } catch {}
  document.getElementById('rotateValues').checked = true;
  document.getElementById('valueFontSize').value = 10;
  document.getElementById('valueFont').value = "Helvetica";
  document.getElementById('valueFontColor').value = "#333333";
  document.getElementById('showValuesBg').checked = false;
  document.getElementById('valueBgColor').value = "#CCCCCC";
  document.getElementById('valueBgAlpha').value = 90;
}
</SCRIPT>

<DIV CLASS="modal-dialog" STYLE="overflow-y: initial !important;">
  <DIV CLASS="modal-content">
    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Data Values</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">
      <FORM>
      <TABLE>
        <TR>
          <TD STYLE="text-align:right;">
            Display data values:&nbsp;
          </TD>
          <TD>
            <DIV CLASS="form-check form-switch">
              <INPUT TYPE="checkbox" CLASS="form-check-input" NAME="showValues" ID="showValues" data-offstyle="secondary" $showValues>
              <LABEL CLASS="form-check-label" FOR="showValues">&nbsp;</LABEL>
            </DIV>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Color:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="color" NAME="valueFontColor" ID="valueFontColor" STYLE="width:50px;"VALUE="$valueFontColor">
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Autoscale display units:&nbsp;
          </TD>
          <TD>
            <DIV CLASS="form-check form-switch">
              <INPUT TYPE="checkbox" CLASS="form-check-input" NAME="formatNumberScale" ID="formatNumberScale" data-offstyle="secondary" $formatNumberScale>
              <LABEL CLASS="form-check-label" FOR="formatNumberScale">&nbsp;</LABEL>
            </DIV>
          </TD>
        </TR>
END_HTML

  if (($graphType =~ m/Pie/) || ($graphType =~ m/Donut/))
  {
    print <<END_HTML;
        <TR>
          <TD STYLE="text-align:right;">
            Show percent values:&nbsp;
          </TD>
          <TD>
            <DIV CLASS="form-check form-switch">
              <INPUT TYPE="checkbox" CLASS="form-check-input" NAME="showPercentValues" ID="showPercentValues" data-offstyle="secondary" $showPercentValues>
              <LABEL CLASS="form-check-label" FOR="showPercentValues">&nbsp;</LABEL>
            </DIV>
          </TD>
        </TR>
END_HTML
  }

  print <<END_HTML;
        <TR>
          <TD STYLE="text-align:right;">
            Value decimal places:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="number" NAME="decimals" ID="decimals" STYLE="width:75px;" VALUE="$decimals" min=0 max=6>
          </TD>
        </TR>

        <TR $hideIfPie>
          <TD STYLE="text-align:right;">
            Value position:&nbsp;
          </TD>
          <TD>
            <SELECT CLASS="form-select" NAME="placeValuesInside" ID="placeValuesInside">
              <OPTION VALUE=1>Inside</OPTION>
              <OPTION VALUE=0>Outside</OPTION>
            </SELECT>
            <SCRIPT>
              \$("select#placeValuesInside").val("$placeValuesInside");
            </SCRIPT>
          </TD>
        </TR>

        <TR $hideIfPie>
          <TD STYLE="text-align:right;">
            Rotate data values:&nbsp;
          </TD>
          <TD>
            <DIV CLASS="form-check form-switch">
              <INPUT TYPE="checkbox" CLASS="form-check-input" NAME="rotateValues" ID="rotateValues" data-offstyle="secondary" $rotateValues>
              <LABEL CLASS="form-check-label" FOR="rotateValues">&nbsp;</LABEL>
            </DIV>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Text Size:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="number" NAME="valueFontSize" ID="valueFontSize" STYLE="width:75px;" VALUE="$valueFontSize" min=3>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Font family:&nbsp;
          </TD>
          <TD>
            <SELECT CLASS="form-select" NAME="valueFont" ID="valueFont">
              <OPTION VALUE="Arial">Arial</OPTION>
              <OPTION VALUE="Arial Black">Arial Black</OPTION>
              <OPTION VALUE="Comic Sans MS">Comic Sans MS</OPTION>
              <OPTION VALUE="Courier New">Courier New</OPTION>
              <OPTION VALUE="Helvetica">Helvetica</OPTION>
              <OPTION VALUE="Impact">Impact</OPTION>
              <OPTION VALUE="Tahoma">Tahoma</OPTION>
              <OPTION VALUE="Times New Roman">Times New Roman</OPTION>
              <OPTION VALUE="Verdana">Verdana</OPTION>
            </SELECT>
            <SCRIPT>
              \$("select#valueFont").val("$valueFont");
            </SCRIPT>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Show background:&nbsp;
          </TD>
          <TD>
            <DIV CLASS="form-check form-switch">
              <INPUT TYPE="checkbox" CLASS="form-check-input" NAME="showValuesBg" ID="showValuesBg" onChange="showBgOptions()" data-offstyle="secondary" $showValuesBg>
              <LABEL CLASS="form-check-label" FOR="showValuesBg">&nbsp;</LABEL>
            </DIV>
          </TD>
        </TR>

        <TR ID="tr-valueBgColor">
          <TD STYLE="text-align:right;">
            Background color:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="color" NAME="valueBgColor" ID="valueBgColor" STYLE="width:50px;" VALUE="$valueBgColor">
          </TD>
        </TR>

        <TR ID="tr-valueBgAlpha">
          <TD STYLE="text-align:right;">
            Transparency:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="custom-range" TYPE="range" NAME="valueBgAlpha" ID="valueBgAlpha" STYLE="width:100px;" MIN=0 MAX=100 STEP=10 VALUE="$valueBgAlpha">
          </TD>
        </TR>

        <TR>
          <TD COLSPAN="2" STYLE="text-align:center;">
            <P></P>
            <A CLASS="text-decoration-none" HREF="#" onClick="revertDefaults()">Revert to default</A>
          </TD>
        </TR>
      </TABLE>

      <SCRIPT>
        showBgOptions();
      </SCRIPT>

      </FORM>
    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="submitForm()"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML

#EOF
