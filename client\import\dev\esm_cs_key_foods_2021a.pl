#!/usr/bin/perl

use Text::CSV;

#Import C&S Key Foods Spin data for ESM-Ferolie

  open(INPUT, "$ARGV[0]");
  open(OUTPUT, ">$ARGV[1]");

  #instantiate our CSV object
  $csv = Text::CSV->new( {binary => 1} );

  #first line is headers
  $line = <INPUT>;
  $csv->parse($line);
  @columns = $csv->fields();

  $idx = 0;
  foreach $header (@columns)
  {
    if ($header eq "PRINTED DATE")
    {
      $columns[$idx] = "tattr:PRINTED DATE";
    }
    elsif ($header eq "MONTH YEAR")
    {
      $columns[$idx] = "Time Period";
    }
    elsif ($header =~ /MANUFACTURER/)
    {
      $columns[$idx] = "pseg:Vendor Name";
    }
    elsif ($header =~ /ARTICLE #/)
    {
      $columns[$idx] = "pattr:WHOLESALE ARTICLE #";
    }
    elsif ($header eq "WHOLE SALE ARTICLE DESCRIPTION")
    {
      $columns[$idx] = "Product";
      $productCol = $idx;
    }
    elsif ($header =~ /UPC/)
    {
      $columns[$idx] = "UPC";
      $upcCol = $idx;
    }
    elsif ($header eq "CASE COST")
    {
      $columns[$idx] = "LIST";
    }

    $idx++;
  }

  #add our geo and vendoe # headers
  @tmp = ('Geography', 'pseg:VENDOR#');
  push(@tmp, @columns);

  #output the headers
  $csv->combine(@tmp);
  $line = $csv->string();
  print OUTPUT "$line\n";

  while ($line = <INPUT>)
  {
    $csv->parse($line);
    @columns = $csv->fields();

    #format the date column
    $columns[1] = "1 MONTH ENDING " . $columns[1];

    #split the vendor # out of the manufacturer column
    if ($columns[2] =~ m/^(\d+) (.*)/)
    {
      $vendorNum = $1;
      $columns[2] = $2;
    }

    #format the UPC (assumes 11 digit UPC)
    $columns[$upcCol] = sprintf("%011d", $columns[$upcCol]);
    $columns[$upcCol] =~ m/(\d)(\d\d\d\d\d)(\d\d\d\d\d)/;
    $upc = "0-$2-$3-$3";
    $columns[$upcCol] = $upc;

    #add the WHOLESALE ARTICLE# to the end of the product name
    $columns[$productCol] = "$columns[$productCol] $columns[3]";

    #strip the " EA" off the end of the pack size
    if ($columns[8] =~ m/^(\d+) EA/)
    {
      $columns[8] = $1;
    }

    #push our geography and end date onto the beginning of the line
    @tmp = ('Key Foods', "$vendorNum");
    push(@tmp, @columns);

    $csv->combine(@tmp);
    $line = $csv->string();

    print OUTPUT "$line\n";
  }
