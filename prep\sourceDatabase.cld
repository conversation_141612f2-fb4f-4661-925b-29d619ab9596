#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($kapDB);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Load Data from Database</TITLE>
<LINK REL="SHORTCUT ICON" HREF="$Lib::KoalaConfig::kapHostURL/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$("#btn-submit").prop('disabled', true);
  \$("#btn-submit").text("Please Wait...");
  return(true);
}
</SCRIPT>


</HEAD>

<BODY>
END_HTML

  print_html_navbar($kapDB, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="$Lib::KoalaConfig::kapHostURL/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">Data Prep</A></LI>
    <LI CLASS="breadcrumb-item">$flowName</LI>
    <LI CLASS="breadcrumb-item active">Import Data from Database</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  get_cgi_session_info();

  #get CGI parameters
  $flowID = $q->param('f');
  $action = $q->param('a');

  $dbType = "mysql";
  $dbHost = "";
  $dbTable = "";
  $dbUser = "";
  $dbPass = "";
  if ($action eq "e")
  {
    $query = "SELECT sourceInfo FROM prep.flows WHERE ID=$flowID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($sourceInfo) = $dbOutput->fetchrow_array;
    if ($sourceInfo =~ m/^TYPE=(.*?)\|HOST=(.*?)\|TABLE=(.*?)\|USER=(.*?)\|PASS=(.*)$/)
    {
      $dbType = $1;
      $dbHost = $2;
      $dbTable = $3;
      $dbUser = $4;
      $dbPass = $5;
    }
    $actionVal = "Edit";
  }
  else
  {
    $actionVal = "New";
  }

  $flowName = prep_flow_id_to_name($prepDB, $flowID);

  print_html_header();

  #make sure we have write privs for this data flow
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to work with jobs in this data flow.");
  }

  #come up with a random 5 digit integer, used to uniquely identify source
  #files for this run
  $key = int(rand(99999));

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-11 col-lg-8 col-xl-7"> <!-- content -->

      <FORM METHOD="post" ACTION="sourceDatabaseLoad.cld" onsubmit="return checkForm(this);">
      <INPUT TYPE="hidden" NAME="f" VALUE="$flowID">
      <INPUT TYPE="hidden" NAME="key" VALUE="$key">
      <INPUT TYPE="hidden" NAME="a" VALUE="$action">

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Upload Data From Database</DIV>
        <DIV CLASS="card-body">

          <P>
          <LABEL FOR="dbType">Database Type:</LABEL>
          <SELECT NAME="dbType" ID="dbType" CLASS="form-select">
            <OPTION VALUE="sqlserver">Microsoft SQL Server</OPTION>
            <OPTION VALUE="mysql">MySQL</OPTION>
            <OPTION VALUE="oracle">Oracle</OPTION>
          </SELECT>
          <SCRIPT>
            \$("select#dbType").val("$dbType");
          </SCRIPT>

          <P>
          <LABEL FOR="dbHost">Database Server:</LABEL>
          <INPUT TYPE="text" NAME="dbHost" CLASS="form-control" VALUE="$dbHost" required>

          <P>
          <LABEL FOR="dbTable">Database Table:</LABEL>
          <INPUT TYPE="text" NAME="dbTable" CLASS="form-control" VALUE="$dbTable" required>

          <P>
          <LABEL FOR="dbUser">Database User:</LABEL>
          <INPUT TYPE="text" NAME="dbUser" CLASS="form-control" VALUE="$dbUser" required>

          <P>
          <LABEL FOR="dbPass">Database Password:</LABEL>
          <INPUT TYPE="password" NAME="dbPass" CLASS="form-control" VALUE="$dbPass" autocomplete="new-password" required>

          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="location.href='main.cld'"><I CLASS="bi bi-x-lg"></I></SPAN> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-submit">Next <I CLASS="bi bi-arrow-right"></I></BUTTON>
          </DIV>

        </DIV>
      </DIV>

      </FORM>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
