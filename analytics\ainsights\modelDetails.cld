#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;
use File::Copy;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::DataSources;
use Lib::AInsights::AInsights;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: AInsights Model Details</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Please Wait...');
  return(true);
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">AInsights</A></LI>
    <LI CLASS="breadcrumb-item active">$actionHR</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $priceModelID = $q->param('pm');
  $action = $q->param('a');
  $dsID = $q->param('ds');
  $modelName = $q->param('name');
  $modelDesc = $q->param('desc');
  $brandSegID = $q->param('bseg');
  $ownBrandID = $q->param('ob');
  @productIDs = $q->param('p');
  @geographyIDs = $q->param('g');

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsSchema = "datasource_" . $dsID;
  $dsName = ds_id_to_name($db, $dsID);

  #if we're editing an existing model
  if ($priceModelID > 0)
  {
    $actionHR = "Editing Model $modelName";
  }
  else
  {
    $actionHR = "New Model";
  }

  print_html_header();

  #make sure we have write privs for this pricing model
  $privs = AInsights_rights($db, $userID, $priceModelID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this model.");
  }

  %measNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "m");

  $excludeWindowDuration = 2;
  $excludeWindowPeriod = "years";

  $outlierStdDev = 2;

  $avgPriceMeasID = AInsights_measure_by_name($db, $dsSchema, ('Avg Unit Price', 'Avg Retail Price', 'Average Retail', 'Unit Price'));

  $unitSalesMeasID = AInsights_measure_by_name($db, $dsSchema, ('Units', 'Unit Sales', 'Unit Volume'));

  $distMeasID = AInsights_measure_by_name($db, $dsSchema, ('%ACV Reach'));

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-11 col-lg-8 col-xl-7"> <!-- content -->

      <FORM METHOD="post" ACTION="modelRefresh.cld" onsubmit="return checkForm(this);">
      <INPUT TYPE="hidden" NAME="pm" VALUE="$priceModelID">
      <INPUT TYPE="hidden" NAME="a" VALUE="$action">
      <INPUT TYPE="hidden" NAME="ds" VALUE="$dsID">
      <INPUT TYPE="hidden" NAME="name" VALUE="$modelName">
      <INPUT TYPE="hidden" NAME="desc" VALUE="$modelDesc">
      <INPUT TYPE="hidden" NAME="p" VALUE="@productIDs">
      <INPUT TYPE="hidden" NAME="g" VALUE="@geographyIDs">
      <INPUT TYPE="hidden" NAME="bseg" VALUE="$brandSegID">
      <INPUT TYPE="hidden" NAME="ob" VALUE="$ownBrandID">

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">AInsights Model Details</DIV>
        <DIV CLASS="card-body">

          <DIV CLASS="row">
            <DIV CLASS="col-auto mt-1">
              Use average unit pricing information from:
            </DIV>
            <DIV CLASS="col-auto">
              <SELECT NAME="avgPriceID" ID="avgPriceID" CLASS="form-select">
END_HTML

  if ($avgPriceMeasID < 1)
  {
    $avgPriceMeasID = 0;
    print(" <OPTION VALUE=0>Avg Unit Price</OPTION>\n");
  }

  foreach $id (sort {$measNameHash{$a} cmp $measNameHash{$b}} keys %measNameHash)
  {
    print(" <OPTION VALUE=$id>$measNameHash{$id}</OPTION>\n");
  }

  print <<END_HTML;
              </SELECT>
              <SCRIPT>
                \$('select#avgPriceID').val('$avgPriceMeasID');
              </SCRIPT>
            </DIV>
          </DIV>

          <P>
          <DIV CLASS="row">
            <DIV CLASS="col-auto mt-1">
              Use unit sales information from:
            </DIV>
            <DIV CLASS="col-auto">
              <SELECT NAME="unitSalesID" ID="unitSalesID" CLASS="form-select">
END_HTML

  foreach $id (sort {$measNameHash{$a} cmp $measNameHash{$b}} keys %measNameHash)
  {
    print(" <OPTION VALUE=$id>$measNameHash{$id}</OPTION>\n");
  }

  print <<END_HTML;
              </SELECT>
              <SCRIPT>
                \$('select#unitSalesID').val('$unitSalesMeasID');
              </SCRIPT>
            </DIV>
          </DIV>

          <P>
          <DIV CLASS="row">
            <DIV CLASS="col-auto mt-1">
              Use distribution information from:
            </DIV>
            <DIV CLASS="col-auto">
              <SELECT NAME="distID" ID="distID" CLASS="form-select">
END_HTML

  foreach $id (sort {$measNameHash{$a} cmp $measNameHash{$b}} keys %measNameHash)
  {
    print(" <OPTION VALUE=$id>$measNameHash{$id}</OPTION>\n");
  }

  print <<END_HTML;
              </SELECT>
              <SCRIPT>
                \$('select#distID').val('$distMeasID');
              </SCRIPT>
            </DIV>
          </DIV>

          <P>&nbsp;</P>

          <DIV CLASS="row">
            <DIV CLASS="col-auto mt-1">
              Don't consider data more than
            </DIV>
            <DIV CLASS="col-auto">
              <INPUT NAME="exwindur" CLASS="form-control" TYPE="number" STYLE="width:4em;" DISABLED VALUE=$excludeWindowDuration required>
            </DIV>
            <DIV CLASS="col-auto">
              <SELECT NAME="exwinper" ID="exwinper" CLASS="form-select" DISABLED>
                <OPTION>years</OPTION>
                <OPTION>months</OPTION>
                <OPTION>weeks</OPTION>
              </SELECT>
              <SCRIPT>
                \$('select#exwinper').val('$excludeWindowPeriod');
              </SCRIPT>
            </DIV>
            <DIV CLASS="col-auto mt-1">
             old
            </DIV>
          </DIV>

          <P>
          <DIV CLASS="row">
            <DIV CLASS="col-auto mt-1">
              Exclude outlying data points that are more than
            </DIV>
            <DIV CLASS="col-auto">
              <INPUT NAME="outlierstddev" CLASS="form-control" TYPE="number" STYLE="width:4em;" DISABLED VALUE=$outlierStdDev required>
            </DIV>
            <DIV CLASS="col-auto mt-1">
              standard deviations from the mean.
            </DIV>
          </DIV>

          <P>&nbsp;</P>

          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onclick="location.href='main.cld'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="submit" ID="btn-submit">Next <I CLASS="bi bi-arrow-right"></I></BUTTON>
          </DIV>

        </DIV>
      </DIV>

      </FORM>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
