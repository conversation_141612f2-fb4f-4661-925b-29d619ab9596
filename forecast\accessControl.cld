#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Forecasts;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Elasticity Model Sharing</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Saving...');
  return(true);
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">KCast</A></LI>
    <LI CLASS="breadcrumb-item">$fcastName</LI>
    <LI CLASS="breadcrumb-item active">Sharing</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $fcID = $q->param('fcID');

  $db = KAPutil_connect_to_database();

  #get the name of the elasticity model
  $fcastName = forecast_id_to_name($db, $fcID);

  print_html_header();

  #make sure we have write privs for this data source
  $query = "SELECT userID FROM analytics.forecasts WHERE ID=$fcID";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($modelOwnerID) = $dbOutput->fetchrow_array;

  if (($modelOwnerID != $userID) && ($acctType < 5))
  {
    exit_error("You don't have privileges to change sharing settings for this forecast - you're not the forecast owner.");
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-12 col-lg-10 col-xl-8"> <!-- content -->

      <FORM METHOD="post" ACTION="accessControlSave.cld" onsubmit="return checkForm(this);">
      <INPUT TYPE="hidden" NAME="fcID" VALUE="$fcID">

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Forecast Sharing</DIV>
        <DIV CLASS="card-body">

          Choose the users you want to grant read or write access on the <B>$fcastName</B> forecast to:
          <P>

          <DIV CLASS="table-responsive">
            <TABLE CLASS="table table-hover table-striped table-bordered table-sm">
              <THEAD><TR>
                <TH>Read Only</TD>
                <TH>Modify</TD>
                <TH>User</TD>
                <TH>Organization</TD>
              </TR></THEAD>
END_HTML

  #build up a hash of all R and RW users for this forecast, keyed by user ID
  #NB: We're doing this here instead of calling forecast_rights to avoid
  #    making a bazillion database queries for sites with lots of users
  $query = "SELECT Rusers, RWusers FROM analytics.forecasts WHERE ID=$fcID";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($Rusers, $RWusers) = $dbOutput->fetchrow_array;

  @rusers = split(',', $Rusers);
  @rwusers = split(',', $RWusers);
  foreach $ruser (@rusers)
  {
    $userRights{$ruser} = 'R';
  }
  foreach $rwuser (@rwusers)
  {
    $userRights{$rwuser} = 'W';
  }

  #get a hash of all organization names for display purposes
  %orgNames = utils_get_org_hash($db);

  #get a list of all users in the private cloud, and display
  if ($Lib::KoalaConfig::cloudtype eq "multi")
  {
    $query = "SELECT ID, first, last, orgID FROM users \
        WHERE orgID=$orgID AND acctType > 0 ORDER BY last";
  }
  else
  {
    $query = "SELECT ID, first, last, orgID FROM users \
        WHERE acctType > 0 ORDER BY orgID, last";
  }
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;

  while (($id, $userFirst, $userLast, $userOrg) = $dbOutput->fetchrow_array)
  {
    if ($userRights{$id} eq "R")
    {
      $checked = "CHECKED";
    }
    else
    {
      $checked = "";
    }

    print <<END_HTML;
              <TR>
                <TD CLASS="text-center">
                  <DIV CLASS="form-check form-check-inline">
                    <INPUT CLASS="form-check-input" NAME="R $id" ID="R_$id" TYPE="checkbox" $checked>
                    <LABEL CLASS="form-check-label" FOR="R_$id">&nbsp;</LABEL>
                  </DIV>
                </TD>
END_HTML

    if ($userRights{$id} eq "W")
    {
      $checked = "CHECKED";
    }
    else
    {
      $checked = "";
    }

    print <<END_HTML;
                <TD CLASS="text-center">
                  <DIV CLASS="form-check form-check-inline">
                    <INPUT CLASS="form-check-input" NAME="W $id" ID="W_$id" TYPE="checkbox" $checked>
                    <LABEL CLASS="form-check-label" FOR="W_$id">&nbsp;</LABEL>
                  </DIV>
                </TD>
                <TD>$userFirst $userLast</TD>
                <TD>$orgNames{$userOrg}</TD>
              </TR>
END_HTML
  }

  print <<END_HTML;
            </TABLE>
          </DIV>

          <P>

          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="location.href='main.cld'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-submit"><I CLASS="bi bi-save"></I> Save</BUTTON>
          </DIV>

        </DIV>
      </DIV>

      </FORM>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
<P>
END_HTML

  print_html_footer();

#EOF
