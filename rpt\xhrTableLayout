#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSel;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::Reports;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $rptID = $q->param('rptID');
  $visID = $q->param('v');
  $rowDims = $q->param('r');
  $filterDims = $q->param('f');
  $colDims = $q->param('c');

  $db = KAPutil_connect_to_database();

  #make sure we have edit privs for this report
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this report.");
  }


  ########################################################################
  #
  # This code block is called on submit to save the changes
  #

  if (defined($rowDims))
  {
    $q_rowDims = $db->quote($rowDims);
    $q_filterDims = $db->quote($filterDims);
    $q_colDims = $db->quote($colDims);

    #update our dimension layout in the database
    $query = "UPDATE visuals \
        SET tableRowDims=$q_rowDims,tableFilterDims=$q_filterDims, tableColDims=$q_colDims \
        WHERE ID=$visID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    #update table selections to match changes to layout
    reports_table_default_selections($db, $rptID, $visID);

    $cubeName = cube_id_to_name($db, $rptID);
    $dsID = cube_get_ds_id($db, $rptID);
    $dsName = ds_id_to_name($db, $dsID);

    utils_audit($db, $userID, "Changed table layout", $dsID, $rptID, 0);

    $activity = "$first $last changed table layout for $cubeName in $dsName";
    utils_slack($activity);

    exit;
  }


  #########################################################################

  #get the chart layout details from the database
  $query = "SELECT tableRowDims, tableColDims, tableFilterDims FROM visuals \
      WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($rowDim, $colDim, $filterDims) = $dbOutput->fetchrow_array;

  #make sure every dimension is correctly represented (any dim not specifically
  #assigned elsewhere is assumed to be a filter dimension)
  $dimStr = "$rowDim,$filterDims,$colDim";
  if (!($dimStr =~ m/p/))
  {
    $filterDims .= ",p";
  }
  if (!($dimStr =~ m/g/))
  {
    $filterDims .= ",g";
  }
  if (!($dimStr =~ m/t/))
  {
    $filterDims .= ",t";
  }


  ##################################################################
  #
  # Everything after this point is called to display the table layout dialog
  #

  print <<END_HTML;
<SCRIPT>
function addRows()
{
  let i = 0;

  let filterObj = document.getElementById('filterDims');
  let rowObj = document.getElementById('rowDims');

  let isSelected = [];
  for (i=0; i < filterObj.options.length; i++)
  {
    isSelected[i] = filterObj.options[i].selected;
    if (isSelected[i])
    {
      let itemID = filterObj.options[i].value;
      let itemName = filterObj.options[i].label;

      let opt = document.createElement("option");
      opt.text = itemName;
      opt.value = itemID;
      rowObj.add(opt);
    }
  }

  i = filterObj.options.length;
  while (i--)
  {
    if (isSelected[i])
    {
      filterObj.remove(i);
    }
  }
}



function addFilters()
{
  let i = 0;

  let filterObj = document.getElementById('filterDims');
  let rowObj = document.getElementById('rowDims');

  let isSelected = [];
  for (i=0; i < rowObj.options.length; i++)
  {
    isSelected[i] = rowObj.options[i].selected;
    if (isSelected[i])
    {
      let itemID = rowObj.options[i].value;
      let itemName = rowObj.options[i].label;

      let opt = document.createElement("option");
      opt.text = itemName;
      opt.value = itemID;
      filterObj.add(opt);
    }
  }

  i = rowObj.options.length;
  while (i--)
  {
    if (isSelected[i])
    {
      rowObj.remove(i);
    }
  }
}



function addCols()
{
  let i = 0;

  let rowObj = document.getElementById('rowDims');
  let colObj = document.getElementById('colDims');

  if (colObj.options.length > 0)
  {
    return;
  }

  let isSelected = [];
  for (i=0; i < rowObj.options.length; i++)
  {
    isSelected[i] = rowObj.options[i].selected;
    if (isSelected[i])
    {
      let itemID = rowObj.options[i].value;
      let itemName = rowObj.options[i].label;

      let opt = document.createElement("option");
      opt.text = itemName;
      opt.value = itemID;
      colObj.add(opt);
    }
  }

  i = rowObj.options.length;
  while (i--)
  {
    if (isSelected[i])
    {
      rowObj.remove(i);
    }
  }
}



function delCols()
{
  let i = 0;

  let rowObj = document.getElementById('rowDims');
  let colObj = document.getElementById('colDims');

  let isSelected = [];
  for (i = 0; i < colObj.options.length; i++)
  {
    isSelected[i] = colObj.options[i].selected;
    if (isSelected[i])
    {
      let itemID = colObj.options[i].value;
      let itemName = colObj.options[i].label;

      let opt = document.createElement("option");
      opt.text = itemName;
      opt.value = itemID;
      rowObj.add(opt);
    }
  }

  i = colObj.options.length;
  while (i--)
  {
    if (isSelected[i])
    {
      colObj.remove(i);
    }
  }
}



function submitForm()
{
  let i = 0;
  let rowSel = document.getElementById("rowDims");
  let filterSel = document.getElementById("filterDims");
  let colSel = document.getElementById("colDims");
  let rowDims = "";
  let filterDims = "";
  let colDims = "";

  for (i = 0; i < rowSel.options.length; i++)
  {
    rowDims = rowDims + rowSel.options[i].value + ",";
  }

  if (rowDims.length < 2)
  {
    alert("You must define at least one row dimension");
    return;
  }

  for (i = 0; i < filterSel.options.length; i++)
  {
    filterDims = filterDims + filterSel.options[i].value + ",";
  }

  for (i = 0; i < colSel.options.length; i++)
  {
    colDims = colSel.options[i].value;
  }

  let url = "xhrTableLayout?rptID=$rptID&v=$visID&r=" + rowDims +
      "&f=" +filterDims + "&c=" + colDims;

  \$.get(url, function(data, status)
  {
    location.href = "display.cld?rpt=$rptID&v=$visID";
  });
}
</SCRIPT>

<DIV CLASS="modal-dialog modal-lg" STYLE="overflow-y: initial !important;">
  <DIV CLASS="modal-content">
    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Table Layout</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">

      <FORM NAME="table_layout">
      <TABLE CLASS="mx-auto">
        <TR>
          <TD>
            <B>Filters</B><BR>
            <SELECT CLASS="form-select" ID="filterDims" NAME="filterDims" STYLE="width:150px" SIZE="4">
END_HTML

  @filterDims = split(',', $filterDims);
  foreach $dim (@filterDims)
  {
    if ($dim eq "p")
    {
      print("<OPTION VALUE='p'>Products</OPTION>\n");
    }
    if ($dim eq "g")
    {
       print("<OPTION VALUE='g'>Geographies</OPTION>\n");
    }
    if ($dim eq "t")
    {
       print("<OPTION VALUE='t'>Time Periods</OPTION>\n");
    }
  }

  print <<END_HTML;
            </SELECT>
          </TD>
          <TD STYLE="vertical-align:bottom;">
            &nbsp;
            <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="addRows()" TITLE="Display the selected dimension as rows"><I CLASS="bi bi-arrow-right"></I></BUTTON><BR>
            &nbsp;
            <BR>
            &nbsp;
            <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="addFilters()" TITLE="Use the selected dimensions as filters"><I CLASS="bi bi-arrow-left"></I></BUTTON>
            &nbsp;
          </TD>
          <TD>
            <B>Rows</B><BR>
            <SELECT CLASS="form-select" ID="rowDims" NAME="rowDims" STYLE="width:150px" SIZE="4">
END_HTML

  @rowDims = split(',', $rowDim);
  foreach $dim (@rowDims)
  {
    if ($dim eq "p")
    {
      print("<OPTION VALUE='p'>Products</OPTION>\n");
    }
    if ($dim eq "g")
    {
       print("<OPTION VALUE='g'>Geographies</OPTION>\n");
    }
    if ($dim eq "t")
    {
       print("<OPTION VALUE='t'>Time Periods</OPTION>\n");
    }
  }

  print <<END_HTML;
            </SELECT>
          </TD>

          <TD STYLE="vertical-align:bottom;">
            &nbsp;
            <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="addCols()" TITLE="Display the selected dimension as rows"><I CLASS="bi bi-arrow-right"></I></BUTTON><BR>
            &nbsp;
            <BR>
            &nbsp;
            <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="delCols()" TITLE="Use the selected dimensions as filters"><I CLASS="bi bi-arrow-left"></I></BUTTON>
            &nbsp;
          </TD>

          <TD>
            <B>Columns</B><BR>
            <SELECT CLASS="form-select" ID="colDims" NAME="colDims" STYLE="width:150px" SIZE="4">
END_HTML

  if ($colDim eq "p")
  {
    print("<OPTION VALUE='p'>Products</OPTION>\n");
  }
  if ($colDim eq "g")
  {
     print("<OPTION VALUE='g'>Geographies</OPTION>\n");
  }
  if ($colDim eq "t")
  {
     print("<OPTION VALUE='t'>Time Periods</OPTION>\n");
  }

  print <<END_HTML;
            </SELECT>
          </TD>
        </TR>
      </TABLE>
      </FORM>

    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="submitForm()"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML


#EOF
