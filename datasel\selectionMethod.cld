#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE html>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName Data Selection</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  #output appropriate navigation header for the structure type we're editing
  if ($structType eq "a")
  {
    $dsName = ds_id_to_name($db, $dsID);
    $aggName = $structName;
    if (length($structName) < 1)
    {
      $aggName = DSRagg_id_to_name($db, $dsSchema, $dim, $structID);
    }
    print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/dsr/main.cld">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/dsr/display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Edit Aggregate $aggName</LI>
  </OL>
</NAV>

<P>
END_HTML
  }

  elsif ($structType eq "l")
  {
    $dsName = ds_id_to_name($db, $dsID);
    $listName = $structName;
    if (length($structName) < 1)
    {
      $listName = DSRlist_id_to_name($db, $dsSchema, $dim, $structID);
    }
    print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/dsr/main.cld">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/dsr/display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Edit List $listName</LI>
  </OL>
</NAV>
<P>
END_HTML
  }

  elsif ($structType eq "c")
  {
    $rptName = $structName;
    if (length($rptName) < 1)
    {
      $rptName = cube_id_to_name($db, $rptID);
    }
    print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/rpt/main">Reports</A></LI>
    <LI CLASS="breadcrumb-item">$rptName</LI>
    <LI CLASS="breadcrumb-item active">Data Selection Method</LI>
  </OL>
</NAV>
<P>
END_HTML
  }
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');
  $dim = $q->param('dim');
  $rptID = $q->param('rptID');
  $structType = $q->param('st');
  $structID = $q->param('sid');
  $structName = $q->param('name');
  $modifyItem = $q->param('modItem');

  #if we didn't get an explicit structure type, assume we're dealing with a cube
  if (length($structType) < 1)
  {
    $structType = "c";
  }

  #URI encode struct name
  $uriName = $structName;
  $uriName =~ s/&/\%26/g;
  $uriName =~ s/\'//g;

  #if we're modifying an existing selection
  if ($modifyItem =~ m/^M\:/)
  {
    $redirect = "selectManual.cld?ds=$dsID&rptID=$rptID&dim=$dim&st=$structType&sid=$structID&name=$uriName&modItem=$modifyItem";
  }
  elsif (($modifyItem =~ m/^H\:/) || ($modifyItem =~ m/^SH\:/))
  {
    $redirect = "selectHierarchy.cld?ds=$dsID&rptID=$rptID&dim=$dim&st=$structType&sid=$structID&name=$uriName&modItem=$modifyItem";
  }
  elsif ($modifyItem =~ m/^VAL\:/)
  {
    $redirect = "selectValue.cld?ds=$dsID&rptID=$rptID&dim=$dim&st=$structType&sid=$structID&name=$uriName&modItem=$modifyItem";
  }
  elsif ($modifyItem =~ m/^TB\:/)
  {
    $redirect = "selectTopBottom.cld?ds=$dsID&rptID=$rptID&dim=$dim&st=$structType&sid=$structID&name=$uriName&modItem=$modifyItem";
  }
  elsif (($modifyItem =~ m/^RANGE\:/) || ($modifyItem =~ m/^RECENT\:/))
  {
    $redirect = "selectRange.cld?ds=$dsID&rptID=$rptID&dim=$dim&st=$structType&sid=$structID&name=$uriName&modItem=$modifyItem";
  }
  elsif ($modifyItem =~ m/^MATCH\:/)
  {
    $redirect = "selectMatch.cld?ds=$dsID&rptID=$rptID&dim=$dim&st=$structType&sid=$structID&name=$uriName&modItem=$modifyItem";
  }

  #if no method supplied, we're manual method
  elsif (length($modifyItem) > 0)
  {
    $redirect = "selectManual.cld?ds=$dsID&rptID=$rptID&dim=$dim&st=$structType&sid=$structID&name=$uriName&modItem=$modifyItem";
  }

  #else we're not editing an existing item, so do no redirect
  else
  {
    undef($redirect);
  }

  if (length($redirect) > 5)
  {
    print("Content-type: text/html\n\n");
    print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META HTTP-EQUIV="refresh" CONTENT="0; URL=$redirect">
</HEAD>
<BODY>
</BODY>
</HTML>
END_HTML
    exit;
  }

  $db = KAPutil_connect_to_database();

  $dsSchema = "datasource_" . $dsID;

  print_html_header();

  #check our permissions
  if ($structType eq "c")
  {
    $privs = cube_rights($db, $userID, $rptID, $acctType);
    if ($privs ne "W")
    {
      exit_error("You don't have privileges to edit this report.");
    }
  }
  else
  {
    $privs = ds_rights($db, $userID, $dsID, $acctType);
    if ($privs ne "W")
    {
      exit_error("You don't have privileges to modify this data source.");
    }
  }

  #determine display names for our dimension
  if ($dim eq "p")
  {
    $dispItems = "products";
  }
  elsif ($dim eq "g")
  {
    $dispItems = "geographies";
  }
  elsif ($dim eq "t")
  {
    $dispItems = "time periods";
  }
  elsif ($dim eq "m")
  {
    $dispItems = "measures";
  }

  #determine if we're selecting data for a report, list, or aggregate
  if ($structType eq "l")
  {
    $dispStructType = "list";
  }
  elsif ($structType eq "a")
  {
    $dispStructType = "aggregate";
  }
  else
  {
    $dispStructType = "report";
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-11 col-lg-8 col-xl-7"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Selection Method</DIV>
        <DIV CLASS="card-body">

          <P>
          How do you want to add $dispItems to your $dispStructType?

          <P>
          <TABLE CLASS="table table-sm table-bordered">
            <TR>
              <TD>
                <BUTTON CLASS="btn btn-primary" STYLE="width:120px;" TYPE="button" onClick="location.href='selectManual.cld?ds=$dsID&rptID=$rptID&dim=$dim&st=$structType&sid=$structID&name=$uriName'"><IMG SRC="/icons/datasel_manual.png"><BR>Manual</BUTTON>
              </TD>
              <TD STYLE="vertical-align:middle;">
                I want to manually add specific $dispItems and related structures.
              </TD>
            </TR>
END_HTML

  #Hierarchy, Value, and Top/Bottom are valid for every dimension except measure
  if ($dim ne "m")
  {
    print <<END_HTML;
            <TR>
              <TD>
                <BUTTON CLASS="btn btn-primary" STYLE="width:120px;" TYPE="button" onClick="location.href='selectHierarchy.cld?ds=$dsID&rptID=$rptID&dim=$dim&st=$structType&sid=$structID&name=$uriName'"><IMG SRC="/icons/datasel_hierarchy.png"><BR>Hierarchy</BUTTON>
              </TD>
              <TD STYLE="vertical-align:middle;">
                I want to add groups of $dispItems based on their assigned aggregates, segmentations, or segmentation hierarchies.
              </TD>
            </TR>
END_HTML
  }

  if ($dim eq "t")
  {
    print <<END_HTML;
            <TR>
              <TD>
                <BUTTON CLASS="btn btn-primary" STYLE="width:120px;" TYPE="button" onClick="location.href='selectRange.cld?ds=$dsID&rptID=$rptID&dim=$dim&st=$structType&sid=$structID&name=$uriName'"><IMG SRC="/icons/datasel_range.png"><BR>Range</BUTTON>
              </TD>
              <TD STYLE="vertical-align:middle;">
                I want to add time periods that fall within a certain range to my report.
              </TD>
            </TR>
END_HTML
  }

  print <<END_HTML;
            <TR>
              <TD>
                <BUTTON CLASS="btn btn-primary" STYLE="width:120px;" TYPE="button" onClick="location.href='selectMatch.cld?ds=$dsID&rptID=$rptID&dim=$dim&st=$structType&sid=$structID&name=$uriName'"><IMG SRC="/icons/datasel_match.png"><BR>Match</BUTTON>
              </TD>
              <TD STYLE="vertical-align:middle;">
                I want to add $dispItems with names that match a particular string.
              </TD>
            </TR>
          </TABLE>

          <P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="history.go(-1);"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
<P>
END_HTML

  print_html_footer();

#EOF
