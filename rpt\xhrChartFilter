#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSel;
use Lib::DSRUtils;
use Lib::DataSources;
use Lib::Reports;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $rptID = $q->param('rptID');
  $visID = $q->param('v');
  $filterMeas1 = $q->param('f1');
  $filterOp1 = $q->param('o1');
  $filterNum1 = $q->param('n1');
  $allOthers = $q->param('allOthers');
  $action = $q->param('action');

  $db = KAPutil_connect_to_database();

  #make sure we have edit privs for this report
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this report.");
  }

  #get the chart filter details from the database
  $query = "SELECT design FROM visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($chartDesign) = $dbOutput->fetchrow_array;


  ########################################################################
  #
  # This code block is called on submit to save the changes
  #

  if ($action eq "e")
  {
    #only save if a measure to filter on was specified
    if (defined($filterMeas1))
    {
      $chartDesign = reports_set_style($chartDesign, "filterMeas1", "$filterMeas1");
      $chartDesign = reports_set_style($chartDesign, "filterOp1", "$filterOp1");
      $chartDesign = reports_set_style($chartDesign, "filterNum1", "$filterNum1");
    }
    else
    {
      $chartDesign = reports_remove_style($chartDesign, "filterMeas1");
      $chartDesign = reports_remove_style($chartDesign, "filterOp1");
      $chartDesign = reports_remove_style($chartDesign, "filterNum1");
    }

    if ($allOthers eq "true")
    {
      $chartDesign = reports_set_style($chartDesign, "allOthers", "$allOthers");
    }
    else
    {
      $chartDesign = reports_remove_style($chartDesign, "allOthers");
    }

    $q_chartDesign = $db->quote($chartDesign);

    $query = "UPDATE visuals SET design = $q_chartDesign WHERE ID=$visID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    $cubeName = cube_id_to_name($db, $rptID);
    $dsID = cube_get_ds_id($db, $rptID);
    $dsName = ds_id_to_name($db, $dsID);

    utils_audit($db, $userID, "Changed chart filtering", $dsID, $rptID, 0);

    $activity = "$first $last changed chart filters for $cubeName in $dsName";
    utils_slack($activity);

    exit;
  }


  #########################################################################
  #
  # Everything after this point is called to display the table filter dialog
  #

  #extract current filter settings from the table design string
  $filterMeas1 = reports_get_style($chartDesign, "filterMeas1");
  $filterOp1 = reports_get_style($chartDesign, "filterOp1");
  $filterNum1 = reports_get_style($chartDesign, "filterNum1");
  $allOthers = reports_get_style($chartDesign, "allOthers");

  if (!defined($filterNum1))
  {
    $filterNum1 = 10;
  }

  if ($allOthers eq "true")
  {
    $allOthers = "CHECKED";
  }

  print <<END_HTML;
<SCRIPT>
function submitForm()
{
  let filterMeas1 = document.getElementById('filterMeas1').value;
  let filterOp1 = document.getElementById('filterOp1').value;
  let filterNum1 = document.getElementById('filterNum1').value;
  let allOthers = document.getElementById('allOthers').checked;

  let url = "xhrChartFilter?rptID=$rptID&v=$visID&f1=" + filterMeas1 +
      "&o1=" + filterOp1 + "&n1=" + filterNum1 + "&allOthers=" + allOthers +
      "&action=e";

  \$.get(url, function(data, status)
  {
    location.href = "display.cld?rpt=$rptID&v=$visID";
  });
}
</SCRIPT>

<DIV CLASS="modal-dialog modal-lg" STYLE="overflow-y: initial !important;">
  <DIV CLASS="modal-content">
    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Chart Filtering</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">

      <FORM>
      <DIV CLASS="row">
        <DIV CLASS="col-auto mt-2">
          Display where
        </DIV>
        <DIV CLASS="col-auto">
          <SELECT CLASS="form-select" id='filterMeas1' STYLE="width:200px;">
            <OPTION VALUE="0">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</OPTION>
END_HTML

  #get the list of measures available in the report
  @measureIDs = datasel_get_dimension_items($db, $rptID, "m");

  #get hash of all measures and their names
  $dsID = cube_get_ds_id($db, $rptID);
  $dsSchema = "datasource_" . $dsID;
  %measureNameHash = dsr_get_item_name_hash($db, $dsSchema, "m");

  #output OPTION tags for all available measures
  foreach $id (@measureIDs)
  {
    print(" <OPTION VALUE=$id>$measureNameHash{$id}</OPTION>\n");
  }

  print <<END_HTML;
          </SELECT>
        </DIV>

        <DIV CLASS="col-auto mt-2">
          is
        </DIV>
        <DIV CLASS="col-auto">
          <SELECT CLASS="form-select" id='filterOp1' VALUE='$filterOp1'>
            <OPTION VALUE="gt">greater than</OPTION>
            <OPTION VALUE="lt">less than</OPTION>
            <OPTION VALUE="eq">equal to</OPTION>
            <OPTION VALUE="top">in the top n</OPTION>
            <OPTION VALUE="bottom">in the bottom n</OPTION>
          </SELECT>
          <SCRIPT>
            \$("select#filterMeas1").val("$filterMeas1");
            \$("select#filterOp1").val("$filterOp1");
          </SCRIPT>
        </DIV>

        <DIV CLASS="col-auto">
          <INPUT CLASS="form-control" TYPE="number" id="filterNum1" VALUE='$filterNum1' STYLE="width:75px;">
        </DIV>
      </DIV>
END_HTML

  $graphType = reports_get_style($chartDesign, "type");
  $script = reports_data_script($graphType);
  if (!($script =~ m/Multi/))
  {
    print <<END_HTML;
      <P>
      <DIV CLASS="form-check">
        <INPUT CLASS="form-check-input" name="allOthers" ID="allOthers" TYPE="checkbox" $allOthers>
        <LABEL CLASS="form-check-label" FOR="allOthers">Display All Others</LABEL>
      </DIV>
END_HTML
  }
  else
  {
    print <<END_HTML;
      <DIV CLASS="custom-control custom-checkbox" STYLE="display:none;">
        <INPUT name="allOthers" ID="allOthers" TYPE="checkbox" $allOthers>
        <LABEL FOR="allOthers">Display All Others</LABEL>
      </DIV>
END_HTML
  }

  print <<END_HTML;
      </FORM>

    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="submitForm()"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML

#EOF
