#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DataSel;
use Lib::DSRUtils;
use Lib::Reports;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $rptID = $q->param('rptID');
  $visID = $q->param('v');
  $graph_x = $q->param('graph_x');
  $graph_y = $q->param('graph_y');

  $meas_x = $q->param('meas_x');
  $meas_y = $q->param('meas_y');
  $meas_z = $q->param('meas_z');

  $showRegressionLine = $q->param('showRegressionLine');

  $comboSecondaryAxis = $q->param('comboSecondaryAxis');
  $comboGraphAsLine = $q->param('comboGraphAsLine');
  $comboGraphAsColumn = $q->param('comboGraphAsColumn');
  $comboGraphAsArea = $q->param('comboGraphAsArea');

  $showSumAtEnd = $q->param('showSumAtEnd');
  $cumeSumValue = $q->param('cumeSumValue');

  #fix up the CGI parameters from the submitted form
  if (defined($showRegressionLine))
  {
    $showRegressionLine = ($showRegressionLine eq "false") ? "0" : "1";
  }

  if (defined($showSumAtEnd))
  {
    $showSumAtEnd = ($showSumAtEnd eq "false") ? "0" : "1";
  }

  $db = KAPutil_connect_to_database();

  #make sure we have edit privs for this report
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this report.");
  }


  ########################################################################
  #
  # This code block is called on submit to save the changes
  #

  #if we're being called to save a new chart layout
  if (defined($graph_x))
  {
    $q_graph_x = $db->quote($graph_x);
    $q_graph_y = $db->quote($graph_y);

    $query = "UPDATE visuals SET graph_x = $q_graph_x, graph_y = $q_graph_y \
        WHERE ID=$visID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    $query = "SELECT design FROM visuals WHERE ID=$visID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($graphDesign) = $dbOutput->fetchrow_array;

    $graphDesign = reports_set_style($graphDesign, "measureX", $meas_x);
    $graphDesign = reports_set_style($graphDesign, "measureY", $meas_y);
    $graphDesign = reports_set_style($graphDesign, "measureZ", $meas_z);
    $graphDesign = reports_set_style($graphDesign, "showRegressionLine", $showRegressionLine);

    $graphDesign = reports_set_style($graphDesign, "comboSecondaryAxis", $comboSecondaryAxis);
    $graphDesign = reports_set_style($graphDesign, "comboGraphAsLine", $comboGraphAsLine);
    $graphDesign = reports_set_style($graphDesign, "comboGraphAsColumn", $comboGraphAsColumn);
    $graphDesign = reports_set_style($graphDesign, "comboGraphAsArea", $comboGraphAsArea);

    if ($showSumAtEnd ne reports_chart_design_default("showSumAtEnd"))
    {
      $graphDesign = reports_set_style($graphDesign, "showSumAtEnd", $showSumAtEnd);
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "showSumAtEnd");
    }

    if ($cumeSumValue > 0)
    {
      $graphDesign = reports_set_style($graphDesign, "cumeSumValue", $cumeSumValue);
    }
    else
    {
      $graphDesign = reports_remove_style($graphDesign, "cumeSumValue");
    }

    $q_graphDesign = $db->quote($graphDesign);
    $query = "UPDATE visuals SET design = $q_graphDesign WHERE ID=$visID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    reports_graph_default_selections($db, $rptID, $visID, 1);

    $cubeName = cube_id_to_name($db, $rptID);
    $dsID = cube_get_ds_id($db, $rptID);
    $dsName = ds_id_to_name($db, $dsID);

    utils_audit($db, $userID, "Changed chart layout", $dsID, $rptID, 0);
    $activity = "$first $last changed chart layout for $cubeName in $dsName";
    utils_slack($activity);

    exit;
  }


  #########################################################################

  #get the chart layout details from the database
  $query = "SELECT design, graph_x, graph_y FROM visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  ($graphDesign, $graph_x, $graph_y) = $dbOutput->fetchrow_array;

  $graphDesign =~ m/type:(.*?),/;
  $chart = $1;

  $showSumAtEnd = reports_get_style($graphDesign, "showSumAtEnd");
  $cumeSumValue = reports_get_style($graphDesign, "cumeSumValue");

  #set defaults
  if (!(defined($showSumAtEnd)))
  {
    $showSumAtEnd = reports_chart_design_default("showSumAtEnd");
  }
  if ($cumeSumValue < 1)
  {
    $cumeSumValue = 0;
  }

  $showSumAtEnd = ($showSumAtEnd eq "1") ? "CHECKED" : "";

  #
  # Everything after this point is called to display the chart layout dialog
  #

  #get the list of measures available in the report
  $dsID = cube_get_ds_id($db, $rptID);
  $dsSchema = "datasource_" . $dsID;
  @measureIDs = datasel_get_dimension_items($db, $rptID, "m");
  %measureNameHash = get_measure_name_hash($dsSchema, $db, 1);

  #filter down the list of base measures included in this report
  foreach $measureID (@measureIDs)
  {
    if ($measureID =~ m/^\d+$/)
    {
      $rptBaseMeasureHash{$measureID} = 1;
    }
  }

  #if we're a bubble or scatter, get the x, y, and maybe z axis measures
  if (($chart =~ m/Bubble/) || ($chart =~ m/Scatter/))
  {

    $meas_x = reports_get_style($graphDesign, "measureX");
    $meas_y = reports_get_style($graphDesign, "measureY");
    $meas_z = reports_get_style($graphDesign, "measureZ");
    $showRegressionLine = reports_get_style($graphDesign, "showRegressionLine");

    $showRegressionLine = ($showRegressionLine eq "1") ? "CHECKED" : "";

    if ($chart =~ m/Bubble/)
    {
      $objectName = "Bubbles: ";
    }
    else
    {
      $objectName = "Points: ";
    }

  print <<END_HTML;
<SCRIPT>
function submitForm()
{
  let graph_x = document.getElementById('graph_x').value;
  let meas_x = document.getElementById('meas_x').value;
  let meas_y = document.getElementById('meas_y').value;
  if ("$chart" == "Bubble")
  {
    let meas_z = document.getElementById('meas_z').value;
  }
  else
  {
    let meas_z = "";
  }
  let showRegressionLine = \$("#showRegressionLine").prop("checked");

  let url = "xhrChartLayout?rptID=$rptID&v=$visID&graph_x=" + graph_x +
      "&meas_x=" + meas_x + "&meas_y=" + meas_y + "&meas_z=" + meas_z +
      "&showRegressionLine=" + showRegressionLine;

  \$.get(url, function(data, status)
  {
    location.href = "display.cld?rpt=$rptID&v=$visID";
  });
}
</SCRIPT>

<DIV CLASS="modal-dialog" STYLE="overflow-y: initial !important;">
  <DIV CLASS="modal-content">
    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Chart Layout</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">

      <FORM>
      <TABLE>
        <TR>
          <TD STYLE="text-align:right;">$objectName &nbsp;</TD>
          <TD>
            <SELECT CLASS="form-select" ID="graph_x">
              <OPTION VALUE="p">Products</OPTION>
              <OPTION VALUE="g">Geographies</OPTION>
              <OPTION VALUE="t">Times</OPTION>
            </SELECT>
            <SCRIPT>
              \$("select#graph_x").val("$graph_x");
            </SCRIPT>
          </TD>
        </TR>

        <TR><TD>&nbsp;</TD><TD>&nbsp;</TD></TR>

        <TR>
          <TD>X-axis Measure:&nbsp;</TD>
          <TD>
            <SELECT CLASS="form-select" id='meas_x'>
END_HTML

    foreach $measureID (sort {$measureNameHash{$a} cmp $measureNameHash{$b}} keys %measureNameHash)
    {
      if ($rptBaseMeasureHash{$measureID} == 1)
      {
        print(" <OPTION VALUE=$measureID>$measureNameHash{$measureID}</OPTION>\n");
      }
    }
    print <<END_HTML;
            </SELECT>
            <SCRIPT>
              \$("select#meas_x").val("$meas_x");
            </SCRIPT>
          </TD>
        </TR>
        <TR>
          <TD>Y-axis Measure:&nbsp;</TD>
          <TD>
            <SELECT CLASS="form-select" id='meas_y'>
END_HTML

    foreach $measureID (sort {$measureNameHash{$a} cmp $measureNameHash{$b}} keys %measureNameHash)
    {
      if ($rptBaseMeasureHash{$measureID} == 1)
      {
        print(" <OPTION VALUE=$measureID>$measureNameHash{$measureID}</OPTION>\n");
      }
    }
    print <<END_HTML;
            </SELECT>
            <SCRIPT>
              \$("select#meas_y").val("$meas_y");
            </SCRIPT>
END_HTML

    #if we're a Bubble chart and need a Z axis
    if ($chart =~ m/Bubble/)
    {
      print <<END_HTML;
          </TD>
        </TR>

        <TR>
          <TD>Z-axis Measure:&nbsp;</TD>
          <TD>
            <SELECT CLASS="form-select" id='meas_z'>
END_HTML

      foreach $measureID (sort {$measureNameHash{$a} cmp $measureNameHash{$b}} keys %measureNameHash)
      {
        if ($rptBaseMeasureHash{$measureID} == 1)
        {
          print(" <OPTION VALUE=$measureID>$measureNameHash{$measureID}</OPTION>\n");
        }
      }
      print <<END_HTML;
            </SELECT>
            <SCRIPT>
              \$("select#meas_z").val("$meas_z");
            </SCRIPT>
END_HTML
    }

    print <<END_HTML;
          </TD>
        </TR>

        <TR><TD>&nbsp;</TD><TD>&nbsp;</TD></TR>

        <TR>
          <TD>
            <DIV CLASS="form-check" STYLE="margin-top:0px; margin-bottom:0px; text-align:right;">
              <INPUT CLASS="form-check-input" NAME="showRegressionLine" ID="showRegressionLine" TYPE="checkbox" $showRegressionLine>
              <LABEL CLASS="form-check-label" FOR="showRegressionLine">&nbsp;</LABEL>
            </DIV>
          </TD>
          <TD STYLE="text-align:left;">Show Regression Line &nbsp;</TD>
        </TR>
      </TABLE>
      </FORM>

    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="submitForm()"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML
  }

  #else we're a single/multi series chart
  else
  {
    print <<END_HTML;
<SCRIPT>
function submitForm()
{
  let comboPrimaryAxis="", comboSecondaryAxis="";
  let comboGraphAsLine="", comboGraphAsArea="", comboGraphAsColumn="";
  let graph_y = "";

  let graph_x = document.getElementById('graph_x').value;
  try
  {
    graph_y = document.getElementById('graph_y').value;
    console.log(graph_y);
  }
  catch(err)
  {
  }

  try
  {
    len = axisArray.length;
    for (i=0; i < len; i++)
    {
      val = document.getElementById(axisArray[i]).value;
      if (val == "p")
      {
        comboPrimaryAxis = comboPrimaryAxis + axisIdArray[i] + "-";
      }
      else
      {
        comboSecondaryAxis = comboSecondaryAxis + axisIdArray[i] + "-";
      }
    }

    len = graphAsArray.length;
    for (i=0; i < len; i++)
    {
      val = document.getElementById(graphAsArray[i]).value;
      if (val == "l")
      {
        comboGraphAsLine = comboGraphAsLine + graphAsIdArray[i] + "-";
      }
      else if (val == "a")
      {
        comboGraphAsArea = comboGraphAsArea + graphAsIdArray[i] + "-";
      }
      else if (val == "c")
      {
        comboGraphAsColumn = comboGraphAsColumn + graphAsIdArray[i] + "-";
      }
    }

  }
  catch(err)
  {
  }

  try
  {
    let showSumAtEnd = \$("#showSumAtEnd").prop("checked");
    let cumeSumValue = document.getElementById('cumeSumValue').value;
  }
  catch(err)
  {
    showSumAtEnd = "";
    cumeSumValue = "";
  }

  let url = "xhrChartLayout?rptID=$rptID&v=$visID&graph_x=" + graph_x +
      "&graph_y=" + graph_y + "&comboSecondaryAxis=" + comboSecondaryAxis +
      "&comboGraphAsLine=" + comboGraphAsLine +
      "&comboGraphAsColumn=" + comboGraphAsColumn +
      "&comboGraphAsArea=" + comboGraphAsArea +
      "&showSumAtEnd=" + showSumAtEnd + "&cumeSumValue=" + cumeSumValue;

  \$.get(url, function(data, status)
  {
    location.href = "display.cld?rpt=$rptID&v=$visID";
  });
}
</SCRIPT>

<DIV CLASS="modal-dialog" STYLE="overflow-y: initial !important;">
  <DIV CLASS="modal-content">
    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Chart Layout</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">
      <FORM>
      <TABLE>
        <TR>
END_HTML

  if (($chart =~ m/Lines/) || ($chart =~ m/Area/) || ($chart =~ m/Columns/) ||
      ($chart =~ m/Bars/) || ($chart =~ m/Radar/) || ($chart =~ m/Waterfall/) ||
      ($chart =~ m/ColumnLine/) || ($chart =~ m/DualY/))
  {
    print("   <TD STYLE='text-align:right;'>Category:&nbsp;</TD>\n");
  }

  if (($chart =~ m/Pie/i) || ($chart =~ m/Donut/i))
  {
    print("   <TD>Slices:&nbsp;</TD>\n");
  }

  print <<END_HTML;
          <TD>
            <SELECT CLASS="form-select" ID="graph_x">
              <OPTION VALUE="p">Products</OPTION>
              <OPTION VALUE="g">Geographies</OPTION>
              <OPTION VALUE="t">Times</OPTION>
              <OPTION VALUE="m">Measures</OPTION>
            </SELECT>
            <SCRIPT>
              \$("select#graph_x").val("$graph_x");
            </SCRIPT>
          </TD>
        </TR>
END_HTML

  #if we're a multi-series chart, output graph_y selector
  if (($chart =~ m/Lines/) || ($chart =~ m/Area/) || ($chart =~ m/Columns/) ||
      ($chart =~ m/Bars/) || ($chart =~ m/Radar/) ||
      ($chart =~ m/ColumnLine/) || ($chart =~ m/DualY/))
  {
  print <<END_HTML;
        <TR>
          <TD>Sets:&nbsp;</TD>
          <TD>
            <SELECT CLASS="form-select" ID="graph_y">
              <OPTION VALUE="p">Products</OPTION>
              <OPTION VALUE="g">Geographies</OPTION>
              <OPTION VALUE="t">Times</OPTION>
              <OPTION VALUE="m">Measures</OPTION>
            </SELECT>
            <SCRIPT>
              \$("select#graph_y").val("$graph_y");
            </SCRIPT>
          </TD>
        </TR>
END_HTML
  }

  if ($chart =~ m/Waterfall/)
  {
    print <<END_HTML;
          <TR>
            <TD STYLE="text-align:right;">Show a cumulative sum column:&nbsp;</TD>
            <TD>
            <DIV CLASS="form-check form-switch">
              <INPUT TYPE="checkbox" CLASS="form-check-input" NAME="showSumAtEnd" ID="showSumAtEnd" data-offstyle="secondary" $showSumAtEnd>
              <LABEL CLASS="form-check-label" FOR="showSumAtEnd">&nbsp;</LABEL>
            </DIV>
            </TD>
          </TR>
          <TR>
            <TD STYLE="text-align:right;">Custom cumulative sum value:&nbsp;</TD>
            <TD>
              <SELECT CLASS="form-select" id='cumeSumValue'>
                <OPTION ID=0>Automatic</OPTION>
END_HTML

    foreach $measureID (sort {$measureNameHash{$a} cmp $measureNameHash{$b}} keys %measureNameHash)
    {
      if ($rptBaseMeasureHash{$measureID} == 1)
      {
        print(" <OPTION VALUE=$measureID>$measureNameHash{$measureID}</OPTION>\n");
      }
    }

    print <<END_HTML;
              </SELECT>
              <SCRIPT>
                \$("select#cumeSumValue").val("$cumeSumValue");
              </SCRIPT>
            </TD>
          </TR>
END_HTML
  }

  #close out the basic layout table
  print("  </TABLE>\n");

  if ($chart =~ m/DualY/)
  {

    $comboSecondaryAxisStr = reports_get_style($graphDesign, "comboSecondaryAxis");
    @tmp = split('-', $comboSecondaryAxisStr);
    foreach $id (@tmp)
    {
      $comboSecondaryAxis{$id} = "s";
    }

    $comboGraphAsStr = reports_get_style($graphDesign, "comboGraphAsLine");
    @tmp = split('-', $comboGraphAsStr);
    foreach $id (@tmp)
    {
      $comboGraphAs{$id} = "l";
    }
    $comboGraphAsStr = reports_get_style($graphDesign, "comboGraphAsColumn");
    @tmp = split('-', $comboGraphAsStr);
    foreach $id (@tmp)
    {
      $comboGraphAs{$id} = "c";
    }
    $comboGraphAsStr = reports_get_style($graphDesign, "comboGraphAsArea");
    @tmp = split('-', $comboGraphAsStr);
    foreach $id (@tmp)
    {
      $comboGraphAs{$id} = "a";
    }

    print <<END_HTML;
      <P>&nbsp;</P>
      <STRONG>Graph Measures As:</STRONG>
      <TABLE>
END_HTML

  #output OPTION tags for all available measures
  $jsGraphAsArray = "[";
  $jsComboAxisArray = "[";
  $jsGraphAsIdArray = "[";
  $jsComboAxisIdArray = "[";
  foreach $id (@measureIDs)
  {
    if ($rptBaseMeasureHash{$id} != 1)
    {
      next;
    }

    $jsGraphAsArray .= "'comboGraphAs_$id',";
    $jsComboAxisArray .= "'comboAxis_$id',";
    $jsGraphAsIdArray .= "$id,";
    $jsComboAxisIdArray .= "$id,";

    print <<END_HTML;
        <TR>
          <TD STYLE="text-align:right;">$measureNameHash{$id}:</TD>
          <TD>
            <SELECT ID="comboGraphAs_$id" CLASS="form-select">
              <OPTION VALUE="l">Line</OPTION>
              <OPTION VALUE="c">Column</OPTION>
              <OPTION VALUE="a">Area</OPTION>
            </SELECT>
            <SCRIPT>
              \$("select#comboGraphAs_$id").val("$comboGraphAs{$id}");
            </SCRIPT>
          </TD>
          <TD>
            <SELECT ID="comboAxis_$id" CLASS="form-select">
              <OPTION VALUE="p">Primary Y-Axis</OPTION>
              <OPTION VALUE="s">Secondary Y-Axis</OPTION>
            </SELECT>
            <SCRIPT>
              if ("$comboSecondaryAxis{$id}" == "s")
              {
                \$("select#comboAxis_$id").val("$comboSecondaryAxis{$id}");
              }
            </SCRIPT>
          </TD>
        </TR>
END_HTML
      }

      chop($jsGraphAsArray);
      $jsGraphAsArray .= "]";
      chop($jsComboAxisArray);
      $jsComboAxisArray .= "]";
      chop($jsGraphAsIdArray);
      $jsGraphAsIdArray .= "]";
      chop($jsComboAxisIdArray);
      $jsComboAxisIdArray .= "]";

      print <<END_HTML;
      </TABLE>

<SCRIPT>
var graphAsArray = $jsGraphAsArray;
var axisArray = $jsComboAxisArray;
var graphAsIdArray = $jsGraphAsIdArray;
var axisIdArray = $jsComboAxisIdArray;
</SCRIPT>
END_HTML
    }

    print <<END_HTML;
      </FORM>

    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="submitForm()"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML
  }

#EOF
