#!/usr/bin/perl

use Text::CSV;

#Import shipping data from KeHE point of distribution database for CA Fortune

  #instantiate our CSV object
  $csv = Text::CSV->new( {binary => 1} );

  open(INPUT, "$ARGV[0]");
  open(OUTPUT, ">$ARGV[1]");

  #burn unneeded header info
  $line = <INPUT>;
  while (!($line =~ m/^Date Range/))
  {
    $line = <INPUT>;
  }

  #line is now the date line, so parse it to get our universal end date
  $csv->parse($line);
  @columns = $csv->fields();
  $endDate = $columns[1];
  $endDate =~ m/^.*? to (.*)/;
  $endDate = "1 Month Ending " . $1;

  #next line should be the header line, so parse it
  $line = <INPUT>;
  $line =~ s/\s+/ /g;
  $line =~ s/\\n/ /g;

  $csv->parse($line);
  @columns = $csv->fields();

  $idx = 0;
  foreach $header (@columns)
  {
    if ($header =~ m/Banner Name/)
    {
      $columns[$idx] = "GSEG:Banner Name";
    }
    elsif ($header =~ m/Customer Name/)
    {
      $columns[$idx] = "Geography";
    }
    elsif ($header =~ m/Address Book Number/)
    {
      $columns[$idx] = "GATTR:Address Book Number";
    }
    elsif ($header =~ m/Addressline1/)
    {
      $columns[$idx] = "GATTR:Addressline1";
    }
    elsif ($header =~ m/Address Line2/)
    {
      $columns[$idx] = "GATTR:Address Line2";
    }
    elsif ($header =~ m/Customer City/)
    {
      $columns[$idx] = "GATTR:Customer City";
    }
    elsif ($header =~ m/Customer State Code/)
    {
      $columns[$idx] = "GATTR:Customer State Code";
    }
    elsif ($header =~ m/Customer Postal Code/)
    {
      $columns[$idx] = "GATTR:Customer Postal Code";
    }
    elsif ($header =~ m/DC/)
    {
      $columns[$idx] = "GSEG:DC";
    }
    elsif ($header =~ m/UPC/)
    {
      $columns[$idx] = "Product";
    }
    elsif ($header =~ m/Brand Name/)
    {
      $columns[$idx] = "PSEG:Brand Name";
    }
    elsif ($header =~ m/Product Description/)
    {
      $columns[$idx] = "PATTR:Product Description";
    }
    elsif ($header =~ m/Product Size/)
    {
      $columns[$idx] = "PSEG:Product Size";
    }
    elsif ($header =~ m/UOM/)
    {
      $columns[$idx] = "PSEG:UOM";
    }
    elsif ($header =~ m/Product Division/)
    {
      $columns[$idx] = "PSEG:Product Division";
    }
    elsif ($header =~ m/Product Sub Division/)
    {
      $columns[$idx] = "PSEG:Product Sub Division";
    }
    elsif ($header =~ m/Product Category/)
    {
      $columns[$idx] = "PSEG:Product Category";
    }
    elsif ($header =~ m/Product Sub Category/)
    {
      $columns[$idx] = "PSEG:Product Sub Category";
    }
    elsif ($header =~ m/Product Class/)
    {
      $columns[$idx] = "PSEG:Product Class";
    }

    $idx++;
  }

  push(@columns, "Time");

  #output the headers
  $csv->combine(@columns);
  $line = $csv->string();
  print OUTPUT "$line\n";

  while ($line = <INPUT>)
  {
    $line =~ s/\s+/ /g;
    $line =~ s/\n/ /g;

    $csv->parse($line);
    @columns = $csv->fields();

    push(@columns, $endDate);

    $csv->combine(@columns);
    $line = $csv->string();

    print OUTPUT "$line\n";
  }
