#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Delete Report</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$("#btn-submit").prop('disabled', true);
  \$("#btn-submit").text("Please Wait...");
  return(true);
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/rpt/main">Reports</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Delete $objectTypeCaps</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------


  #initialize the CGI variable parser and get the forecast to be deleted
  $q = new CGI;
  $rptIDStr = $q->param('rpt');

  @rptIDs = split(',', $rptIDStr);
  $rptIDStr = join(',', @rptIDs);

  #temporarily restrict to 1 report at a time for deletion
  $rptID = $rptIDs[0];

  #set some human-readable UI text
  $objectType = "report";
  $objectTypeCaps = "Report";
  if (scalar(@rptIDs) > 1)
  {
    $objectType = "reports";
    $objectTypeCaps = "Reports";
  }

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  $db = KAPutil_connect_to_database();

  #make sure we're the owner of the reports or their backing DS
  $query = "SELECT userID, dsID FROM cubes WHERE ID=$rptID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($cubeOwnerID, $dsID) = $dbOutput->fetchrow_array;

  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  $query = "SELECT userID FROM dataSources WHERE ID=$dsID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($dsOwnerID) = $dbOutput->fetchrow_array;

  if (($cubeOwnerID != $userID) && ($dsOwnerID != $userID) && ($acctType < 5))
  {
    exit_error("You don't have privileges to delete these reports - you're not the owner of one or more reports.");
  }

  #make sure the reports aren't being updated
  $query = "SELECT PID FROM app.jobs WHERE cubeID IN ($rptIDStr)";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  if ($status > 0)
  {
    exit_error("One or more of the selected reports are being updated, and can't be deleted until the update completes.")
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-10 col-lg-7 col-xl-6"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Delete $objectTypeCaps</DIV>
        <DIV CLASS="card-body">

          <P>
          <FORM METHOD="post" ACTION="/app/rpt/deleteReportDo.cld" onsubmit="return checkForm(this);">
          <INPUT TYPE="hidden" NAME="rptID" VALUE="$rptIDStr">
          <INPUT TYPE="hidden" NAME="dsID" VALUE="$dsID">
          Are you sure you want to delete the $objectType:
          <P>
          <TABLE CLASS="table table-striped table-sm table-bordered mx-auto">
END_HTML

  #get the selected report names from the database
  $query = "SELECT name FROM app.cubes WHERE ID IN ($rptIDStr)";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  while (($rptName) = $dbOutput->fetchrow_array)
  {
    print("<TR><TD>$rptName</TD></TR>\n");
  }

  print <<END_HTML;
          </TABLE>

          <P>&nbsp;</P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="location.href='/app/rpt/main?ds=$dsID'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-danger" TYPE="SUBMIT" ID="btn-submit"><I CLASS="bi bi-trash"></I> Delete $objectTypeCaps</BUTTON>
          </DIV>
          </FORM>

        </DIV>
      </DIV>


    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
