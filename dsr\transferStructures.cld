#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSel;
use Lib::DataSources;
use Lib::DSRMeasures;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Transfer Data Source Structures</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Transfer Data Source Structures</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------
#
# Transfer any segmentation rules that might exist from the source
# segmentation to the destination segmentation
#

sub xfer_seg_rules
{
  my ($query, $dbOutput, $srcSegID, $destSegID, $dbName, $status);
  my (%srcSegHash, %destSegHash, %srcSegments, %destSegments);

  my ($db, $dim, $destSchema, $srcSchema) = @_;


  #get a hash of all segmentations in the source DS
  %srcSegHash = DSRsegmentation_get_segmentations_hash($db, $srcSchema, $dim);

  #get a hash of all segmentations in the destination DS, and reverse
  %destSegHash = DSRsegmentation_get_segmentations_hash($db, $destSchema, $dim);
  %destSegHash = reverse(%destSegHash);

  #run through each source segmentation
  foreach $srcSegID (keys %srcSegHash)
  {

    #make sure there's a matching segmentation in the destination DS
    $destSegID = $destSegHash{$srcSegHash{$srcSegID}};
    if ($destSegID < 1)
    {
      next;
    }

    #if we're not overwriting, find our new rule step value
    if ($overwrite != 1)
    {
      $dbName = $dbStub . "seg_rules";
      $query = "SELECT MAX(step) FROM $destSchema.$dbName \
          WHERE segmentationID=$destSegID";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      ($destStep) = $dbOutput->fetchrow_array;
      $destStep++;
    }
    else
    {

      #pull any existing rules since we're overwriting
      $dbName = $dbStub . "seg_rules";
      $query = "DELETE FROM $destSchema.$dbName WHERE segmentationID=$destSegID";
      $db->do($query);

      #set our starting point for renumbering the order of seg rules (some might
      #not transfer over)
      $destStep = 1;
    }

    #get every rule associated with the current source segmentation
    $dbName = $dbStub . "seg_rules";
    $query = "SELECT segmentationID, segmentID, rule, filter1, filter2 \
        FROM $srcSchema.$dbName WHERE segmentationID=$srcSegID ORDER BY step";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;

    #if there aren't any segmentation rules in this source seg, move on
    if ($status < 1)
    {
      next;
    }

    #get a hash of the segments in the source segmentation
    %srcSegments = DSRseg_get_segments_hash($db, $srcSchema, $dim, $srcSegID);

    #get a hash of the segments in dest segmentation and reverse for matching
    %destSegments = DSRseg_get_segments_hash($db, $destSchema, $dim, $destSegID);
    %destSegments = reverse(%destSegments);

    #run through the segmentation rules, and convert them over if possible
    while (($segmentationID, $segmentID, $rule, $filter1, $filter2) = $dbOutput->fetchrow_array)
    {

      #get the matching segment in the destination segment
      $destSegmentID = $destSegments{$srcSegments{$segmentID}};

      #if there isn't one, move on (we're not creating one since it should
      #have already been created by the main segmentation xfer code)
      if ($destSegmentID < 1)
      {
        next;
      }

      #handle a TEXT rule
      if ($rule =~ m/^TEXT /)
      {
        $q_rule = $db->quote($rule);
        $query = "INSERT INTO $destSchema.$dbName \
            (step, segmentationID, segmentID, rule) \
            VALUES ($destStep, $destSegID, $destSegmentID, $q_rule)";
        $db->do($query);
        $destStep++;
      }

      #handle an attribute match rule
      if ($rule =~ m/^ATTR /)
      {
        $q_rule = $db->quote($rule);
        $query = "INSERT INTO $destSchema.$dbName \
            (step, segmentationID, segmentID, rule) \
            VALUES ($destStep, $destSegID, $destSegmentID, $q_rule)";
        $db->do($query);
        $destStep++;
      }

      #handle a segment name match rule
      elsif ($rule =~ m/^SEGMATCH (.*?) (.*?) (.*?) (.*)$/)
      {
        $matchSide = $1;
        $matchSegID = $2;
        $matchType = $3;
        $matchStr = $4;

        #find a matching segmentation in the destination DS
        $destMatchSegID = $destSegHash{$srcSegHash{$matchSegID}};

        #move on to next rule if no matching segmentation
        if ($destMatchSegID < 1)
        {
          next;
        }

        $rule = "SEGMATCH $matchSide $destMatchSegID $matchType $matchStr";
        $q_rule = $db->quote($rule);
        $query = "INSERT INTO $destSchema.$dbName \
            (step, segmentationID, segmentID, rule) \
            VALUES ($destStep, $destSegID, $destSegmentID, $q_rule)";
        $db->do($query);
        $destStep++;
      }

      #handle a segment membership rule
      elsif ($rule =~ m/^SEG (.*?) (.*?) (.*)$/)
      {
        $matchSide = $1;
        $matchSeg = $2;
        $matchSegment = $3;

        #find a matching segmentation in the destination DS
        $destMatchSegID = $destSegHash{$srcSegHash{$matchSeg}};

        #move on to next rule if no matching segmentation
        if ($destMatchSegID < 1)
        {
          next;
        }

        #get the matching segments from the source DS
        %srcMatchSegments = DSRseg_get_segments_hash($db, $srcSchema, $dim, $matchSeg);

        #get the matching segments from the destination DS and reverse them
        %destMatchSegments = DSRseg_get_segments_hash($db, $destSchema, $dim, $destMatchSegID);
        %destMatchSegments = reverse(%destMatchSegments);

        #run through the segments, looking for matches
        $destSegmentMembers = "";
        @srcSegmentMembers = split(',', $matchSegment);
        foreach $srcSegmentMemberID (@srcSegmentMembers)
        {
          $tmpID = $destMatchSegments{$srcMatchSegments{$srcSegmentMemberID}};

          if ($tmpID > 0)
          {
            $destSegmentMembers .= "$tmpID,";
          }

        }
        chop($destSegmentMembers);

        $rule = "SEG $matchSide $destMatchSegID $destSegmentMembers";
        $q_rule = $db->quote($rule);
        $query = "INSERT INTO $destSchema.$dbName \
            (step, segmentationID, segmentID, rule) \
            VALUES ($destStep, $destSegID, $destSegmentID, $q_rule)";
        $db->do($query);
        $destStep++;
      }

      #handle a segment value rule
      elsif ($rule =~ m/^SEGVAL (.*?) (.*?) (.*?) (.*?) (.*)$/)
      {
        $matchSide = $1;
        $matchSeg = $2;
        $matchOp = $3;
        $matchNumVal = $4;
        $matchNumValUpper = $5;

        #find a matching segmentation in the destination DS
        $destMatchSegID = $destSegHash{$srcSegHash{$matchSeg}};

        #move on to next rule if no matching segmentation
        if ($destMatchSegID < 1)
        {
          next;
        }

        $rule = "SEGVAL $matchSide $destMatchSegID $matchOp $matchNumVal $matchNumValUpper";
        $q_rule = $db->quote($rule);
        $query = "INSERT INTO $destSchema.$dbName \
            (step, segmentationID, segmentID, rule) \
            VALUES ($destStep, $destSegID, $destSegmentID, $q_rule)";
        $db->do($query);
        $destStep++;
      }

      #handle a fill-down segmentation rule
      elsif ($rule =~ m/^SEGFILLDOWN (.*)$/)
      {
        $matchSeg = $1;

        #find a matching segmentation in the destination DS
        $destMatchSegID = $destSegHash{$srcSegHash{$matchSeg}};

        #move on to next rule if no matching segmentation
        if ($destMatchSegID < 1)
        {
          next;
        }

        $rule = "SEGFILLDOWN $destMatchSegID";
        $q_rule = $db->quote($rule);
        $query = "INSERT INTO $destSchema.$dbName \
            (step, segmentationID, segmentID, rule) \
            VALUES ($destStep, $destSegID, 0, $q_rule)";
        $db->do($query);
        $destStep++;
      }

      #handle a "segment all remaining items" rule
      elsif ($rule =~ m/^CATCHALL$/)
      {
        $q_rule = $db->quote($rule);
        $query = "INSERT INTO $destSchema.$dbName \
            (step, segmentationID, segmentID, rule) \
            VALUES ($destStep, $destSegID, $destSegmentID, $q_rule)";
        $db->do($query);
        $destStep++;
      }
    }

    #now that we're done xferring all of the rules, let's run them
    DSRsegmentation_apply_seg_rules($db, $destSchema, $dim, $destSegID);
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #connect to the database
  $db = KAPutil_connect_to_database();

  #get CGI parameters
  $dsID = $q->param('dsID');
  $sourceDS = $q->param('sourceDS');

  $overwrite = $q->param('overwrite');
  $matchUPC = $q->param('matchUPC');

  $xferprod = $q->param('xferprod');
  $xfergeo = $q->param('xfergeo');
  $xfertime = $q->param('xfertime');
  $xfermeas = $q->param('xfermeas');

  $aggs = $q->param('aggs');
  $lists = $q->param('lists');
  $segs = $q->param('segs');
  $seghiers = $q->param('seghiers');
  $aliases = $q->param('aliases');
  $attrs = $q->param('attrs');
  $calcmeas = $q->param('calcmeas');
  $aggrules = $q->param('aggrules');

  $overwrite = ($overwrite eq "on") ? 1 : 0;
  $matchUPC = ($matchUPC eq "on") ? 1 : 0;

  $xferprod = ($xferprod eq "on") ? 1 : 0;
  $xfergeo = ($xfergeo eq "on") ? 1 : 0;
  $xfertime = ($xfertime eq "on") ? 1 : 0;
  $xfermeas = ($xfermeas eq "on") ? 1 : 0;

  $aggs = ($aggs eq "on") ? 1 : 0;
  $lists = ($lists eq "on") ? 1 : 0;
  $segs = ($segs eq "on") ? 1 : 0;
  $seghiers = ($seghiers eq "on") ? 1 : 0;
  $aliases = ($aliases eq "on") ? 1 : 0;
  $attrs = ($attrs eq "on") ? 1 : 0;
  $calcmeas = ($calcmeas eq "on") ? 1 : 0;
  $aggrules = ($aggrules eq "on") ? 1 : 0;

  #create an array of dimension codes for every dim we're transfering
  undef(@dims);
  if ($xferprod == 1)
  {
    push(@dims, "p");
  }
  if ($xfergeo == 1)
  {
    push(@dims, "g");
  }
  if ($xfertime == 1)
  {
    push(@dims, "t");
  }
  if ($xfermeas == 1)
  {
    push(@dims, "m");
  }

  $dsSchema = "datasource_" . $dsID;
  $dsName = ds_id_to_name($db, $dsID);
  $sourceSchema = "datasource_" . $sourceDS;
  $srcName = ds_id_to_name($db, $sourceDS);

  print_html_header();

  #make sure we have read privs for the source data source
  $privs = ds_rights($db, $userID, $sourceDS, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to transfer structures from this data source.");
  }

  #make sure we have write privs for the data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to modify this data source.");
  }

  #make sure the data source isn't locked by another background process
  $ok = DSRutil_operation_ok($db, $dsID, "XFER-STRUCTS");

  if ($ok != 1)
  {
    exit_warning("Another job is currently using this data source - please try again later.")
  }

  #if the user is already using more than their fair share of a production cloud
  $loadAvg = KAPutil_get_load_avg($db);
  if (($loadAvg >= 0.75) && ($acctType < 10))
  {
    $jobCount = KAPutil_get_user_jobs($db, $userID);

    #if the user already has more than a couple jobs running
    if ($jobCount > 1)
    {
      exit_error("Your analytics cloud is heavily loaded, and you already have at least one large job running. Wait a bit for that job to finish, and then try again.");
    }
  }

  #set our initial state in the jobs table
  KAPutil_job_store_status($db, $userID, $dsID, 0, "XFER-STRUCTS", "Transferring structures");

  utils_audit($db, $userID, "Transferred structures from $srcName", $dsID, 0, 0);
  utils_audit($db, $userID, "Transferred structures to $dsName", $sourceDS, 0, 0);
  $activity = "$first $last transferred structures from $srcName to $dsName";

  #fork off a background process to do the actual measure calculation
  $SIG{CHLD} = "IGNORE";
  if ($pid = fork())
  {
    #parent process
  }
  else
  {
    #child process

    #let Apache know not to wait on the child process
    close(STDIN);
    close(STDOUT);

    #redirect STDERR to the Koala error log
    close(STDERR);
    open(STDERR, ">>/opt/apache/htdocs/tmp/koala_error.log");

    #reconnect to database in child process
    $db = KAPutil_connect_to_database();

    #set our initial state in the jobs table
    KAPutil_job_store_status($db, $userID, $dsID, 0, "XFER-MEASURES", "Transferring structures");
    $q_name = $db->quote($dsName);
    $query = "UPDATE app.jobs SET dsName=$q_name WHERE PID=$$ AND dsID=$dsID";
    $db->do($query);

    ################ Segmentations #################

    if ($segs == 1)
    {

      #for every dimension the user wants us to transfer aliases on
      foreach $dim (@dims)
      {
        if ($dim eq "m")
        {
          next;
        }

        $dbStub = KAPutil_get_dim_stub_name($dim);

        #get the base item name hash from the source DS
        %srcItemNameHash = dsr_get_base_item_name_hash($db, $sourceSchema, $dim);

        #get the base item name hash from the dest DS, reverse it for searching
        %itemNameHash = dsr_get_base_item_name_hash($db, $dsSchema, $dim);
        undef(%itemIDhash);
        foreach $key (keys %itemNameHash)
        {
          $name = $itemNameHash{$key};
          $itemIDhash{$name} = $key;
        }

        #grab a hash of segmentations from the source and destination schema
        %sourceSegHash = DSRsegmentation_get_segmentations_hash($db, $sourceSchema, $dim);
        %segHash = DSRsegmentation_get_segmentations_hash($db, $dsSchema, $dim);

        #reverse the segmentation hash in the destination DS so we can search
        #by name
        undef(%segIDhash);
        foreach $key (keys %segHash)
        {
          $name = $segHash{$key};
          $segIDhash{$name} = $key;
        }

        #if we're in the product dimension and we're matching based on UPCs...
        if (($dim eq "p") && ($matchUPC == 1))
        {
          undef(%destUPCHash);
          undef(%srcUPCHash);

          #get ID of UPC attribute in source DS
          $query = "SELECT ID FROM $sourceSchema.product_attributes \
              WHERE name='UPC'";
          $dbOutput = $db->prepare($query);
          $dbOutput->execute;
          ($srcUPCAttrID) = $dbOutput->fetchrow_array;

          #get ID of UPC attribute in destination DS
          $query = "SELECT ID FROM $dsSchema.product_attributes WHERE name='UPC'";
          $dbOutput = $db->prepare($query);
          $dbOutput->execute;
          ($destUPCAttrID) = $dbOutput->fetchrow_array;

          #get value hashes for UPC attributes in both source and dest DS
          %srcUPCHash = DSRattr_get_values_hash($db, $sourceSchema, "p", $srcUPCAttrID);
          %destUPCHash = DSRattr_get_values_hash($db, $dsSchema, "p", $destUPCAttrID);

          #reverse the destination hash so we can search for matching values
          %destUPCHash = reverse(%destUPCHash);
        }

        #for every segmentation in the source DS, try to replicate in dest DS
        foreach $srcSegID (keys %sourceSegHash)
        {
          undef(%destSegments);
          undef(%destSegmentNameHash);

          #if a segmentation with the same name already exists
          $srcSegName = $sourceSegHash{$srcSegID};
          $segID = $segIDhash{$srcSegName};
          if ($segID > 0)
          {

            #if we're not set to overwrite, move on to the next segmentation
            if ($overwrite == 0)
            {
              next;
            }

            #else remove all old segment membership info for this segmentation
            else
            {
              $segID = $segIDhash{$srcSegName};
              $dbName = $dbStub . "segment_item";
              $query = "DELETE FROM $dsSchema.$dbName WHERE segmentationID=$segID";
              $db->do($query);

              #get a hash of all segments that already exist in the destination
              %destSegmentIDs = DSRseg_get_segments_hash($db, $dsSchema, $dim, $segID);

              #reverse the segment hash in the dest DS so we can search by name
              foreach $key (keys %destSegmentIDs)
              {
                $name = $destSegmentIDs{$key};
                $destSegments{$name} = $key;
              }
            }
          }

          #add the segmentation to the destination DS if it's new
          if ($segID < 1)
          {
            $q_name = $db->quote($srcSegName);
            $dbName = $dbStub . "segmentation";
            $query = "INSERT INTO $dsSchema.$dbName (name) VALUES ($q_name)";
            $db->do($query);
            $segID = $db->{q{mysql_insertid}};
          }

          #get the segment membership of items in the source DS
          %srcSegMembership = DSRseg_get_segment_membership_hash($db, $sourceSchema, $dim, $srcSegID);

          #grab the segments from the source DS and insert into dest DS
          %srcSegments = DSRseg_get_segments_hash($db, $sourceSchema, $dim, $srcSegID);
          $dbName = $dbStub . "segment";
          foreach $segmentID (keys %srcSegments)
          {

            #create a matching segment in the destination DS
            $name = $srcSegments{$segmentID};
            if (!defined($destSegments{$name}))
            {
              $q_name = $db->quote($name);
              $query = "INSERT INTO $dsSchema.$dbName (segmentationID, name) \
                  VALUES ($segID, $q_name)";
              $db->do($query);
              $destSegmentID = $db->{q{mysql_insertid}};
              $destSegments{$name} = $destSegmentID;
            }
          }

          #set up the matching hashes (UPC vs name of item)
          %srcMatchHash = %srcItemNameHash;
          %destMatchHash = %itemIDhash;
          if (($dim eq "p") && ($matchUPC == 1))
          {
            %srcMatchHash = %srcUPCHash;
            %destMatchHash = %destUPCHash;
          }

          #look for any base items in common between the two data sources, and
          #set their segment membership in the destination DS
          $dbName = $dbStub . "segment_item";
          foreach $itemID (keys %srcMatchHash)
          {
            $name = $srcMatchHash{$itemID};
            $destItemID = $destMatchHash{$name};

            #if we found an item that's in both data sources
            if ($destItemID > 0)
            {
              $srcSegmentID = $srcSegMembership{$itemID};

              #if the item has an assigned segment in the current segmentation
              if ($srcSegmentID > 0)
              {
                $srcSegName = $srcSegments{$srcSegmentID};
                $destSegmentID = $destSegments{$srcSegName};
                $query = "INSERT INTO $dsSchema.$dbName (segmentationID, segmentID, itemID) VALUES ($segID, $destSegmentID, $destItemID) ON DUPLICATE KEY UPDATE segmentID=$destSegmentID";
                $db->do($query);
              }
            }
          }
        }

        #transfer rules associated with segmentations in this dimension
        xfer_seg_rules($db, $dim, $dsSchema, $sourceSchema);
      }
    }


    ######################## Segmentation Hierarchies ##########

    if ($seghiers == 1)
    {

      #for every dimension the user wants us to transfer seghiers on
      foreach $dim (@dims)
      {
        if ($dim eq "m")
        {
          next;
        }

        $dbStub = KAPutil_get_dim_stub_name($dim);

        #grab a hash of segmentations from the source and destination schema
        %sourceSegHash = DSRsegmentation_get_segmentations_hash($db, $sourceSchema, $dim);
        %segHash = DSRsegmentation_get_segmentations_hash($db, $dsSchema, $dim);

        #reverse the segmentation hash in the destination DS so we can search
        #by name
        undef(%segIDhash);
        foreach $key (keys %segHash)
        {
          $name = $segHash{$key};
          $segIDhash{$name} = $key;
        }

        #grab every segmentation hierarchy from the selected dimension
        $dbName = $dbStub . "seghierarchy";
        $query = "SELECT name, segmentations, namePattern \
            FROM $sourceSchema.$dbName";
        $dbOutput = $db->prepare($query);
        $dbOutput->execute;

        #cycle through each segmentation hierarchy from the source, transferring
        #if appropriate
        while (($segHierName, $segmentations, $namePattern) = $dbOutput->fetchrow_array)
        {

          #look for a hierarchy with the same name in the dest DS
          $q_segHierName = $db->quote($segHierName);
          $query = "SELECT ID FROM $dsSchema.$dbName WHERE name = $q_segHierName";
          $dbOutput2 = $db->prepare($query);
          $status = $dbOutput2->execute;
          ($segHierID) = $dbOutput2->fetchrow_array;

          #if a hierarchy with the same name already exists
          if ($segHierID > 0)
          {

            #if we're not set to overwrite, move on to next hierarchy
            if ($overwrite == 0)
            {
              next;
            }
          }

          #find matching segmentations in dest data source and build up the new
          #segmentations string
          @srcSegs = split(',', $segmentations);
          $destSegs = "";
          $transferOK = 1;
          foreach $srcSegID (@srcSegs)
          {
            $srcSegName = $sourceSegHash{$srcSegID};
            $destSegID = $segIDhash{$srcSegName};

            if ($destSegID < 1)
            {
              $transferOK = 0;
            }
            else
            {
              $destSegs .= "$destSegID,";
            }
          }
          chop($destSegs);

          #if we're OK to transfer this seg hierarchy, do it
          if ($transferOK == 1)
          {
            $q_name = $db->quote($segHierName);

            #if we're overwriting an existing hierarchy
            if ($segHierID > 0)
            {
              $query = "UPDATE $dsSchema.$dbName \
                  SET segmentations='$destSegs', namePattern='$namePattern'";
            }
            else
            {
              $query = "INSERT INTO $dsSchema.$dbName \
                  (name, segmentations, namePattern) \
                  VALUES ($q_name, '$destSegs', '$namePattern')";
            }
            $db->do($query);
          }
        }
      }
    }


    ################ Calculated Measures #################

    #only xfer calculated measures if both the "calculated measures" checkbox
    #and the "measures" checkbox are both checked
    if (($calcmeas == 1) && ($xfermeas == 0))
    {
      $calcmeas = 0;
    }

    if ($calcmeas == 1)
    {

      #NB: This is nasty, but at the moment it seems less kludgy than the
      #    alternatives. We're using a GOTO to run through this entire code
      #    path 3 times to deal with nested calculated measures (those that
      #    depend on other calculated measures). This way the first pass will
      #    create all measures that depend on no other calculated measures,
      #    the second pass will create calculated measures that depend on
      #    calculated measures from the first pass, and so on.
      #    If we ever need to do more than 3 passes, just change the conditional
      #    at the bottom of this giant code block
      $measurePassCount = 0;

      #we're storing up hashes of measures that need to be overwritten (all
      #values set to NULL) and that need columns created in the facts table.
      #We don't want to do these potentially very long-running operations in
      #front of the user, so we'll do them in the background process forked
      #at the bottom of this script
      undef(%measureColsCreate);
      undef(%measureColsOverwrite);

      undef(%measureOverwritten);

      START_OF_CALC_MEASURE:

      #get a list of every calculated measure in the source data source
      $query = "SELECT name, alias, calculation, format \
          FROM $sourceSchema.measures WHERE calculation IS NOT NULL";
      $dbOutput = $db->prepare($query);
      $dbOutput->execute;
      ($measName, $measAlias, $calculation, $format) = $dbOutput->fetchrow_array;

      #get a hash of measure names/IDs from the source schema - use
      #get_measure_name_hash instead of dsr_get_item_name_hash to give
      #preference to base names over aliases
      %measureNameHash = get_measure_name_hash($sourceSchema, $db);

      #for each calculated measure in the source DS
      while (defined($measName))
      {

        #if a measure with the same name already exists in the destination DS
        $q_measName = $db->quote($measName);
        $query = "SELECT ID FROM $dsSchema.measures WHERE name = $q_measName";
        $dbOutput2 = $db->prepare($query);
        $status = $dbOutput2->execute;
        ($newMeasureID) = $dbOutput2->fetchrow_array;
        if ($newMeasureID > 0)
        {
          #if we're not set to overwrite, move on to the next measure
          if ($overwrite == 0)
          {
            ($measName, $measAlias, $calculation, $format) = $dbOutput->fetchrow_array;
            next;
          }

          #else if we've already overwritten this measure
          elsif ($measureOverwritten{$newMeasureID} == 1)
          {
            ($measName, $measAlias, $calculation, $format) = $dbOutput->fetchrow_array;
            next;
          }

          #else we need to overwrite this measure one time
          else
          {
            $measureOverwritten{$newMeasureID} = 1;
          }
        }

        #handle measure alias
        if (length($measAlias) < 1)
        {
          $q_measAlias = "NULL";
        }
        else
        {
          $q_measAlias = $db->quote($measAlias);
        }

        #extract the type of calculated measure
        $calculation =~ m/(.*?)\|/;
        $measType= $1;

        #if this is 1 at the end of the xfer check, we know it's OK to xfer
        $measXferOK = 0;

        #if we're a Change measure
        if ($measType eq "change")
        {
          $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|/;
          $baseMeasure = $1;
          $period = $2;
          $periodType = $3;

          #get the name of the base measure from the source schema
          $baseMeasureName = $measureNameHash{$baseMeasure};

          #get the matching base measure name from the destination DS
          $newBaseMeasureID = DSRmeasures_id_by_name($db, $dsSchema, $baseMeasureName);

          #if we found all of our pre-req base measures
          if ($newBaseMeasureID > 0)
          {
            $newCalc = "change|$newBaseMeasureID|$period|$periodType|";

            $q_newCalc = $db->quote($newCalc);
            $q_measName = $db->quote($measName);

            $measXferOK = 1;
          }
        }

        #if we're a Percent Change measure
        elsif ($measType eq "pct_change")
        {
          $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|/;
          $baseMeasure = $1;
          $period = $2;
          $periodType = $3;

          #get the name of the base measure from the source schema
          $baseMeasureName = $measureNameHash{$baseMeasure};

          #get the matching base measure name from the destination DS
          $newBaseMeasureID = DSRmeasures_id_by_name($db, $dsSchema, $baseMeasureName);

          if ($newBaseMeasureID > 0)
          {
            $newCalc = "pct_change|$newBaseMeasureID|$period|$periodType|";

            $q_newCalc = $db->quote($newCalc);
            $q_measName = $db->quote($measName);

            $measXferOK = 1;
          }
        }

        #if we're a ratio measure
        elsif ($measType eq "ratio")
        {
          $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|/;
          $anteMeasure = $1;
          $consMeasure = $2;
          $convertPct = $3;

          #get the name of the base measures from the source schema
          $anteMeasureName = $measureNameHash{$anteMeasure};
          $consMeasureName = $measureNameHash{$consMeasure};

          #get the matching base measure names from the destination DS
          $newAnteMeasureID = DSRmeasures_id_by_name($db, $dsSchema, $anteMeasureName);
          $newConsMeasureID = DSRmeasures_id_by_name($db, $dsSchema, $consMeasureName);

          if (($newAnteMeasureID > 0) && ($newConsMeasureID> 0))
          {
            $newCalc = "ratio|$newAnteMeasureID|$newConsMeasureID|$convertPct|";

            $q_newCalc = $db->quote($newCalc);
            $q_measName = $db->quote($measName);

            $measXferOK = 1;
          }
        }

        #if we're a Multiplication Measure
        elsif ($measType eq "multiplication")
        {
          $calculation =~ m/.*?\|(.*?)\|(.*?)\|/;
          $measures = $1;
          $constant = $2;

          #get the name and ID of every measure we're supposed to multiply
          @measureIDs = split(/,/, $measures);
          $calcOK = 1;
          $newMeasureIDStr = "";
          foreach $measureID (@measureIDs)
          {
            $measureName = $measureNameHash{$measureID};

            #get the matching base measure names from the destination DS
            $newMeasureID1 = DSRmeasures_id_by_name($db, $dsSchema, $measureName);

            #if we found a matching measure, add it to the string
            if ($newMeasureID1 > 0)
            {
              $newMeasureIDStr .= "$newMeasureID1,";
            }

            #else if we didn't find a matching measure, mark this as not OK
            else
            {
              $calcOK = 0;
            }
          }

          #knock off the trailing comma
          chop($newMeasureIDStr);

          #if we found all of the matching measures in the destination DS
          if ($calcOK)
          {
            $newCalc = "multiplication|$newMeasureIDStr|$constant|";

            $q_newCalc = $db->quote($newCalc);
            $q_measName = $db->quote($measName);

            $measXferOK = 1;
          }
        }

        #if we're a Sum measure
        elsif ($measType eq "sum")
        {
          $calculation =~ m/.*?\|(.*?)\|/;
          $measures = $1;

          #get the name and ID of every measure we're supposed to sum
          @measureIDs = split(/,/, $measures);
          $calcOK = 1;
          $newMeasureIDStr = "";
          foreach $measureID (@measureIDs)
          {
            $measureName = $measureNameHash{$measureID};

            #get the matching base measure names from the destination DS
            $newMeasureID1 = DSRmeasures_id_by_name($db, $dsSchema, $measureName);

            #if we found a matching measure, add it to the string
            if ($newMeasureID1 > 0)
            {
              $newMeasureIDStr .= "$newMeasureID1,";
            }

            #else if we didn't find a matching measure, mark this as not OK
            else
            {
              $calcOK = 0;
            }
          }

          #knock off the trailing comma
          chop($newMeasureIDStr);

          #if we found all of the matching measures in the destination DS
          if ($calcOK)
          {
            $newCalc = "sum|$newMeasureIDStr||";

            $q_newCalc = $db->quote($newCalc);
            $q_measName = $db->quote($measName);

            $measXferOK = 1;
          }
        }

        #if we're a Difference measure
        elsif ($measType eq "difference")
        {
          $calculation =~ m/.*?\|(.*?)\|(.*?)\|/;
          $measure1 = $1;
          $measure2 = $2;

          #get the name of the base measures from the source schema
          $measure1Name = $measureNameHash{$measure1};
          $measure2Name = $measureNameHash{$measure2};

          #get the matching base measure name from the destination DS
          $newMeasure1ID = DSRmeasures_id_by_name($db, $dsSchema, $measure1Name);
          $newMeasure2ID = DSRmeasures_id_by_name($db, $dsSchema, $measure2Name);

          if (($newMeasure1ID > 0) && ($newMeasure2ID > 0))
          {

            $newCalc = "difference|$newMeasure1ID|$newMeasure2ID|";

            $q_newCalc = $db->quote($newCalc);
            $q_measName = $db->quote($measName);

            $measXferOK = 1;
          }
        }

        #if we're a Percent Change Between Measures measure
        elsif ($measType eq "pct_change_meas")
        {
          $calculation =~ m/.*?\|(.*?)\|(.*?)\|/;
          $measure1 = $1;
          $measure2 = $2;

          #get the name of the base measures from the source schema
          $measure1Name = $measureNameHash{$measure1};
          $measure2Name = $measureNameHash{$measure2};

          #get the matching base measure name from the destination DS
          $newMeasure1ID = DSRmeasures_id_by_name($db, $dsSchema, $measure1Name);
          $newMeasure2ID = DSRmeasures_id_by_name($db, $dsSchema, $measure2Name);

          if (($newMeasure1ID > 0) && ($newMeasure2ID > 0))
          {
            $newCalc = "pct_change_meas|$newMeasure1ID|$newMeasure2ID|";

            $q_newCalc = $db->quote($newCalc);
            $q_measName = $db->quote($measName);

            $measXferOK = 1;
          }
        }

        #if we're a Lag measure
        elsif ($measType eq "lag")
        {
          $calculation =~ m/.*?\|(.*?)\|/;
          $measure = $1;

          #get the name of the base measures from the source schema
          $measureName = $measureNameHash{$measure};

          #get the matching base measure name from the destination DS
          $newBaseMeasureID = DSRmeasures_id_by_name($db, $dsSchema, $measureName);

          if ($newBaseMeasureID > 0)
          {
            $newCalc = "lag|$newBaseMeasureID|";

            $q_newCalc = $db->quote($newCalc);
            $q_measName = $db->quote($measName);

            $measXferOK = 1;
          }
        }

        #if we're a Lead measure
        elsif ($measType eq "lead")
        {
          $calculation =~ m/.*?\|(.*?)\|/;
          $measure = $1;

          #get the name of the base measure from the source schema
          $measureName = $measureNameHash{$measure};

          #get the matching base measure name from the destination DS
          $newBaseMeasureID = DSRmeasures_id_by_name($db, $dsSchema, $measureName);

          if ($newBaseMeasureID > 0)
          {
            $newCalc = "lead|$newBaseMeasureID|";

            $q_newCalc = $db->quote($newCalc);
            $q_measName = $db->quote($measName);

            $measXferOK = 1;
          }
        }

        #if we're a Moving Average measure
        elsif ($measType eq "mov_avg")
        {
          $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|/;
          $baseMeasure = $1;
          $periods = $2;
          $timeDirection = $3;

          #get the name of the base measure from the source schema
          $baseMeasureName = $measureNameHash{$baseMeasure};

          #get the matching base measure name from the destination DS
          $newBaseMeasureID = DSRmeasures_id_by_name($db, $dsSchema, $baseMeasureName);

          if ($newBaseMeasureID > 0)
          {
            $newCalc = "mov_total|$newBaseMeasureID|$periods|$timeDirection|";

            $q_newCalc = $db->quote($newCalc);
            $q_measName = $db->quote($measName);

            $measXferOK = 1;
          }
        }

        #if we're a Moving Total measure
        elsif ($measType eq "mov_total")
        {
          $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|/;
          $baseMeasure = $1;
          $periods = $2;
          $timeDirection = $3;

          #get the name of the base measure from the source schema
          $baseMeasureName = $measureNameHash{$baseMeasure};

          #get the matching base measure name from the destination DS
          $newBaseMeasureID = DSRmeasures_id_by_name($db, $dsSchema, $baseMeasureName);

          if ($newBaseMeasureID > 0)
          {
            $newCalc = "mov_total|$newBaseMeasureID|$periods|$timeDirection|";

            $q_newCalc = $db->quote($newCalc);
            $q_measName = $db->quote($measName);

            $measXferOK = 1;
          }
        }

        #if we're a Year To Date measure
        elsif ($measType eq "ytd")
        {
          $calculation =~ m/.*?\|(.*?)\|/;
          $baseMeasure = $1;

          #get the name of the base measure from the source schema
          $baseMeasureName = $measureNameHash{$baseMeasure};

          #get the matching base measure name from the destination DS
          $newBaseMeasureID = DSRmeasures_id_by_name($db, $dsSchema, $baseMeasureName);

          if ($newBaseMeasureID > 0)
          {
            $newCalc = "ytd|$newBaseMeasureID|";

            $q_newCalc = $db->quote($newCalc);
            $q_measName = $db->quote($measName);

            $measXferOK = 1;
          }
        }

        #if we're a Count measure
        elsif ($measType eq "count")
        {
          $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|(.*?)\|(.*?)\|/;
          $dim = $1;
          $useCond = $2;
          $condMeasure = $3;
          $conditional = $4;
          $constant = $5;

          #get the name of the conditional measure from the source schema
          $condMeasureName = $measureNameHash{$condMeasure};

          #get the matching conditional measure name from the destination DS
          $newCondMeasureID = DSRmeasures_id_by_name($db, $dsSchema, $condMeasureName);

          #if the user specified a conditional, we have to have a matching measure
          $ok = 0;
          if (($useCond == 1) && ($newCondMeasureID > 0))
          {
            $ok = 1;
          }

          #if we're not using a conditional, it's cool if we didn't find a
          #matching measure
          elsif ($useCond == 0)
          {
            $newCondMeasureID = 0;
            $ok = 2;
          }

          #if we're OK base measure-wise, transfer the measure
          if ($ok > 0)
          {
            $newCalc = "count|$dim|$useCond|$newCondMeasureID|$conditional|$constant|";

            $q_newCalc = $db->quote($newCalc);
            $q_measName = $db->quote($measName);

            $measXferOK = 1;
          }
        }

        #if we're a Share measure
        elsif ($measType eq "share")
        {
          $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|(.*?)\|/;
          $dim = $1;
          $baseMeasure = $2;
          $structType = $3;
          $structID = $4;

          #get the name of the base measure from the source schema
          $baseMeasureName = $measureNameHash{$baseMeasure};

          #get the matching base measure name from the destination DS
          $newBaseMeasureID = DSRmeasures_id_by_name($db, $dsSchema, $baseMeasureName);

          #get the hash of all product item/structure names from the source DS
          %sourceProdNameHash = dsr_get_item_name_hash($db, $sourceSchema, "p");

          #get the name of the structure in our source DS, based on type
          if ($structType eq "agg")
          {
            $tmp = "AGG_" . $structID;
            $structName = $sourceProdNameHash{$tmp};
          }
          elsif ($structType eq "seg")
          {
            $tmp = "SEG_". $structID;
            $structName = $sourceProdNameHash{$tmp};
          }
          else  #it's a base item
          {
            $structName = $sourceProdNameHash{$structID};
          }

          #get a hash of product item/struct names in the destination DS
          %prodNameHash = dsr_get_item_name_hash($db, $dsSchema, "p");

          #reverse our DS's item hash so we can do lookups by name
          undef(%prodIDhash);
          foreach $key (keys %prodNameHash)
          {
            $name = $prodNameHash{$key};
            $prodIDhash{$name} = $key;
          }

          #determine if a matching named structure exists in destination DS
          $newStructID = $prodIDhash{$structName};
          if (length($newStructID) > 0)
          {
            if ($structType eq "agg")
            {
              if ($newStructID =~ m/AGG_(\d+)/)
              {
                $newStructID = $1;
              }
              else
              {
                $newStructID = 0;
              }
            }
            elsif ($structType eq "seg")
            {
              if ($newStructID =~ m/SEG_(\d+)/)
              {
                $newStructID = $1;
              }
              else
              {
                $newStructID = 0;
              }
            }
            else
            {
              if ($newStructID =~ m/^(\d+)$/)
              {
                $newStructID = $1;
              }
              else
              {
                $newStructID = 0;
              }
            }
          }

          if (($newBaseMeasureID > 0) && ($newStructID > 0))
          {
            $newCalc = "share|$dim|$newBaseMeasureID|$structType|$newStructID|";

            $q_newCalc = $db->quote($newCalc);
            $q_measName = $db->quote($measName);

            $measXferOK = 1;
          }
        }

        elsif ($measType eq "calc")
        {
          $calculation =~ m/.*?\|(.*)\|/;
          $formula = $1;

          #run through every measure in the formula, looking for matching
          #measures in the destination data source
          $calcOK = 1;
          $newFormula = "";

          #NB: this regex looks for the left-most measure in the remaining
          #    formula string
          while ($formula =~ m/(.*?)measure_(\d+)(.*)$/)
          {

            #everything to the left of the leftmost measure is something we
            #can just append straight to the new formula
            $newFormula = $newFormula . $1;

            #extract the ID of the measure from the original formula
            $origMeasID = $2;

            #save everything to the right of the measure for our next pass
            #through the loop
            $formula = $3;

            #get the name of the original base measure
            $origMeasName = $measureNameHash{$origMeasID};

            #get the matching measure name from the destination DS
            $newMeasID = DSRmeasures_id_by_name($db, $dsSchema, $origMeasName);

            #if we found a matching measure in the destination DS
            if ($newMeasID > 0)
            {
              $newFormula .= "measure_" . $newMeasID;
            }

            #if the measure doesn't exist in the destination DS, we're done
            else
            {
              $calcOK = 0;
            }
          }

          #if there was anything left in the original formula after the last
          #measure, append it to our new formula
          $newFormula .= $formula;

          #if we found all of our pre-req measures, do the transfer
          if ($calcOK > 0)
          {
            $newCalc = "calc|$newFormula|";

            $q_newCalc = $db->quote($newCalc);
            $q_measName = $db->quote($measName);

            $measXferOK = 1;
          }
        }

        #if we found all of the measure's pre-reqs, transfer it
        if ($measXferOK == 1)
        {
          #if we're overwriting an existing measure
          if ($newMeasureID > 0)
          {
            $query = "UPDATE $dsSchema.measures \
                SET alias=$q_measAlias, calculation=$q_newCalc, lastCalc=NULL, format='$format' \
                WHERE ID=$newMeasureID";
            $db->do($query);

            #NULL out the old measure column
            $colName = "measure_" . $newMeasureID;
            $measureColsOverwrite{$colName} = 1;
          }

          #else we're creating a new measure
          else
          {
            $query = "INSERT INTO $dsSchema.measures \
                (name, alias, calculation, calcBeforeAgg, format) \
                VALUES ($q_measName, $q_measAlias, $q_newCalc, 0, '$format')";
            $db->do($query);

            #get the ID of the new measure
            $newMeasureID = $db->{q{mysql_insertid}};

            #store the info that we need to add a column for the new measure to
            #the facts table
            $colName = "measure_" . $newMeasureID;
            $measureColsCreate{$colName} = 1;
          }
        }

        ($measName, $measAlias, $calculation) = $dbOutput->fetchrow_array;
      }

      if ($measurePassCount < 3)
      {
        $measurePassCount++;
        goto START_OF_CALC_MEASURE;
      }
    }


    ################ Aggregation Rules #################

    if ($aggrules == 1)
    {

      #get a list of every measure in the source data source with agg rules
      #defined
      $query = "SELECT name, prodAggRule, geoAggRule, timeAggRule, format \
          FROM $sourceSchema.measures";
      $dbOutput = $db->prepare($query);
      $dbOutput->execute;
      undef(%ruleHash);
      undef(%formatHash);
      while (($name, $prodAggRule, $geoAggRule, $timeAggRule, $format) = $dbOutput->fetchrow_array)
      {
        if ((length($prodAggRule) > 1) && (length($geoAggRule) > 1) &&
             (length($timeAggRule) > 1))
        {
          $ruleHash{$name} = "$prodAggRule,$geoAggRule,$timeAggRule";
        }

        $formatHash{$name} = $format;
      }

      #foreach source measure, see if there's a matching destination measure
      foreach $srcMeasureName (keys %ruleHash)
      {

        #see if a matching destination measure exists
        $q_name = $db->quote($srcMeasureName);
        $query = "SELECT ID, prodAggRule, geoAggRule, timeAggRule \
            FROM $dsSchema.measures WHERE name = $q_name";
        $dbOutput = $db->prepare($query);
        $dbOutput->execute;
        ($id, $prodAggRule, $geoAggRule, $timeAggRule) = $dbOutput->fetchrow_array;

        #update the agg rules and formatting of the destination measure
        if (defined($id))
        {
          #set the destination agg rules to the source agg rules
          $ruleHash{$srcMeasureName} =~ m/(.*?),(.*?),(.*?)$/;
          $newProdRule = $1;
          $q_newProdRule = $db->quote($newProdRule);
          $newGeoRule = $2;
          $q_newGeoRule = $db->quote($newGeoRule);
          $newTimeRule = $3;
          $q_newTimeRule = $db->quote($newTimeRule);
          $newFormat = $formatHash{$srcMeasureName};
          $query = "UPDATE $dsSchema.measures \
              SET prodAggRule=$q_newProdRule, geoAggRule=$q_newGeoRule, timeAggRule=$q_newTimeRule, format='$newFormat' \
              WHERE ID=$id";
          $db->do($query);
        }
      }
    }

    #NB: As with the calculated measures, the easiest-to-maintain solution for
    #    nested lists and aggregates is to make multiple passes through the
    #    transfer code using a goto. The added twist here is that we're going
    #    to maintain a hash of the lists and aggregates we're transferring, and
    #    do an implicit overwrite of them on each pass through to make sure we
    #    pick up any changes caused by transferring other lists/aggregates.
    $listAggPassCount = 0;
    undef(%xferredList);
    undef(%xferredAgg);

    START_OF_LIST_AGG:

    ################ Lists #################

    if ($lists == 1)
    {

      #for every dimension the user wants us to run a transfer on
      foreach $dim (@dims)
      {
        $dbStub = KAPutil_get_dim_stub_name($dim);
        $dbName = $dbStub . "list";

        #NB: this is a little hack-y, but we're going to handle an overwrite
        #    request by pre-loading up the xferredList hash so all of the lists
        #    get deleted/recreated
        if ($overwrite == 1)
        {
          $query = "SELECT ID, name FROM $dsSchema.$dbName";
          $dbOutput = $db->prepare($query);
          $dbOutput->execute;
          while (($structID, $name) = $dbOutput->fetchrow_array)
          {
            $key = $dim . "-" . $name;
            $xferredList{$key} = $structID;
          }
        }

        #grab the name and scripts for each list in the source DS
        $query = "SELECT name, script FROM $sourceSchema.$dbName";
        $dbOutput = $db->prepare($query);
        $dbOutput->execute;
        while (($name, $addScript) = $dbOutput->fetchrow_array)
        {

          #if this isn't the first attempt at transferring this list
          #NB: we're making the dimension part of the key so lists with the same
          #    name in different dimensions don't collide
          $key = $dim . "-" . $name;
          $structID = $xferredList{$key};

          if ($structID > 0)
          {
            $query = "DELETE FROM $dsSchema.$dbName WHERE ID=$structID";
            $db->do($query);
          }

          #if a list with the same name already exists in the dest DS, skip
          $q_name = $db->quote($name);
          $query = "SELECT ID FROM $dsSchema.$dbName WHERE name = $q_name";
          $dbOutput2 = $db->prepare($query);
          $status = $dbOutput2->execute;
          if ($status > 0)
          {
            next;
          }

          $addScript = datasel_transfer_script($db, $sourceSchema, $dsSchema, $dim, $addScript, $matchUPC);

          #add the reconstituted list to the current data source
          $q_name = $db->quote($name);
          $q_addScript = $db->quote($addScript);

          if ($structID > 0)
          {
            $query = "INSERT INTO $dsSchema.$dbName (ID, name, script) \
                VALUES ($structID, $q_name, $q_addScript)";
            $db->do($query);
          }
          else
          {
            $query = "INSERT INTO $dsSchema.$dbName (name, script) \
                VALUES ($q_name, $q_addScript)";
            $db->do($query);

            #get the unique ID for this list
            $structID = $db->{q{mysql_insertid}};

            $key = $dim . "-" . $name;
            $xferredList{$key} = $structID;
          }

          #expand the aggregate out to its members, and store them
          datasel_expand_script($db, $dsSchema, $structID, $dim, "l");
        }
      }
    }


    ####################### Aggregates ############################


    if ($aggs == 1)
    {

      #for every dimension the user wants us to run a transfer on
      foreach $dim (@dims)
      {
        if ($dim eq "m")
        {
          next;
        }

        $dbStub = KAPutil_get_dim_stub_name($dim);
        $dbName = $dbStub . "aggregate";

        #NB: this is a little hack-y, but we're going to handle an overwrite
        #    request by pre-loading up the xferredAgg hash so all of the
        #    aggregates get deleted/recreated
        if ($overwrite == 1)
        {
          $query = "SELECT ID, name FROM $dsSchema.$dbName";
          $dbOutput = $db->prepare($query);
          $dbOutput->execute;
          while (($structID, $name) = $dbOutput->fetchrow_array)
          {
            $key = $dim . "-" . $name;
            $xferredAgg{$key} = $structID;
          }
        }

        #grab the name and scripts for each list in the source DS
        $query = "SELECT ID, name, addScript FROM $sourceSchema.$dbName";
        $dbOutput = $db->prepare($query);
        $dbOutput->execute;
        while (($srcStructID, $name, $addScript) = $dbOutput->fetchrow_array)
        {

          #if this isn't the first attempt at transferring this aggregate
          #NB: we're making the dimension part of the key so aggs with the same
          #    name in different dimensions don't collide
          $key = $dim . "-" . $name;
          $structID = $xferredAgg{$key};

          if ($structID > 0)
          {
            $query = "DELETE FROM $dsSchema.$dbName WHERE ID=$structID";
            $db->do($query);
          }

          #if an aggregate with the same name already exists in the dest DS, skip
          $q_name = $db->quote($name);
          $query = "SELECT ID FROM $dsSchema.$dbName WHERE name = $q_name";
          $dbOutput2 = $db->prepare($query);
          $status = $dbOutput2->execute;
          if ($status > 0)
          {
            next;
          }

          $addScript = datasel_transfer_script($db, $sourceSchema, $dsSchema, $dim, $addScript, $matchUPC);

          #add the reconstituted aggregate to the current data source
          $q_name = $db->quote($name);
          $q_addScript = $db->quote($addScript);

          if ($structID > 0)
          {
            $query = "INSERT INTO $dsSchema.$dbName (ID, name, addScript) \
                VALUES ($structID, $q_name, $q_addScript)";
            $db->do($query);
          }
          else
          {
            $query = "INSERT INTO $dsSchema.$dbName (name, addScript) \
                VALUES ($q_name, $q_addScript)";
            $db->do($query);

            #get the unique ID for this list
            $structID = $db->{q{mysql_insertid}};

            $key = $dim . "-" . $name;
            $xferredAgg{$key} = $structID;
          }

          #if we're a time aggregate, handle the appendEndDate feature
          if ($dim eq "t")
          {
            $query = "SELECT appendEndDate FROM $sourceSchema.$dbName \
                WHERE ID=$srcStructID";
            $dbOutput2 = $db->prepare($query);
            $dbOutput2->execute;
            ($appendEndDate) = $dbOutput2->fetchrow_array;
            if ($appendEndDate == 1)
            {
              $query = "UPDATE $dsSchema.$dbName SET appendEndDate=1 \
                  WHERE ID=$structID";
              $db->do($query);
            }
          }

          #expand the aggregate out to its members, and store them
          datasel_expand_script($db, $dsSchema, $structID, $dim, "a");
          datasel_expand_script($db, $dsSchema, $structID, $dim, "as");
        }
      }
    }

    if ($listAggPassCount < 3)
    {
      $listAggPassCount++;
      goto START_OF_LIST_AGG;
    }


    ######################## Attributes ###############################

    if ($attrs == 1)
    {

      #for every dimension the user wants us to transfer attributes on
      foreach $dim (@dims)
      {
        if ($dim eq "m")
        {
          next;
        }

        $dbStub = KAPutil_get_dim_stub_name($dim);

        #get hashes of attributes for the selected dim from the source & dest DS
        %srcAttrHash = DSRattr_get_attributes_hash($db, $sourceSchema, $dim);
        %destAttrHash = DSRattr_get_attributes_hash($db, $dsSchema, $dim);

        #reverse the attribute hash in the destination DS so we can search by name
        undef(%attrIDhash);
        foreach $key (keys %destAttrHash)
        {
          $name = $destAttrHash{$key};
          $attrIDhash{$name} = $key;
        }

        #get the base item name hash from the source DS
        %srcItemNameHash = dsr_get_base_item_name_hash($db, $sourceSchema, $dim);

        #get the base item name hash from the dest DS, reverse it for searching
        %itemNameHash = dsr_get_base_item_name_hash($db, $dsSchema, $dim);
        undef(%itemIDhash);
        foreach $key (keys %itemNameHash)
        {
          $name = $itemNameHash{$key};
          $itemIDhash{$name} = $key;
        }

        #cycle through every attribute in the source DS
        foreach $attrID (keys %srcAttrHash)
        {

          #if the attribute is already in the destination DS, bail out
          $attrName = $srcAttrHash{$attrID};
          $id = $attrIDhash{$attrName};
          if ($id > 0)
          {
            next;
          }

          #add the attribute to the destination DS
          $q_name = $db->quote($attrName);
          $dbName = $dbStub . "attributes";
          $query = "INSERT INTO $dsSchema.$dbName (name) VALUES ($q_name)";
          $db->do($query);
          $newAttrID = $db->{q{mysql_insertid}};

          #get the value hash from the source DS
          %attrValuesHash = DSRattr_get_values_hash($db, $sourceSchema, $dim, $attrID);

          #for every item in the source DS, look for a match and update the dest
          $dbName = $dbStub . "attribute_values";
          foreach $srcItemID (keys %srcItemNameHash)
          {

            #look for a matching item in the destination DS
            $srcName = $srcItemNameHash{$srcItemID};
            $destItemID = $itemIDhash{$srcName};

            #if we found a matching item
            if ($destItemID > 0)
            {

              #get the attribute value for this item from the source DS
              $attrVal = $attrValuesHash{$destItemID};

              #set the attribute value for the item in the destination DS
              $q_val = $db->quote($attrVal);
              $query = "INSERT INTO $dsSchema.$dbName \
                  (attributeID, itemID, value) \
                  VALUES ($newAttrID, $destItemID, $q_val)";
              $db->do($query);
            }
          }
        }
      }
    }


    ######################## Aliases ###############################

    if ($aliases == 1)
    {

      #for every dimension the user wants us to transfer aliases on
      foreach $dim (@dims)
      {

        #get the name of the dimension's database table
        $itemDB = KAPutil_get_dim_db_name($dim);

        #get a hash of aliases for the selected dimension from the source DS
        %aliasHash = dsr_get_aliases_hash_by_name($db, $sourceSchema, $dim);

        #since our alias hash is keyed by name, all we need to do is run through
        #the list of base item names in the destination DS and see what matches
        $query = "SELECT ID, name FROM $dsSchema.$itemDB";
        $dbOutput = $db->prepare($query);
        $dbOutput->execute;

        while (($id, $name) = $dbOutput->fetchrow_array)
        {

          #if we found a matching base item name, update its alias
          if (length($aliasHash{$name}) > 0)
          {
            $q_alias = $db->quote($aliasHash{$name});
            $query = "UPDATE $dsSchema.$itemDB SET alias=$q_alias WHERE ID=$id";
            $db->do($query);
          }
        }
      }
    }

    if ($calcmeas == 1)
    {
      KAPutil_job_update_status($db, "Recalculating measures");

      #create any new measure columns that need to be added to the facts table
      $query = "ALTER TABLE $dsSchema.facts ";
      foreach $colName (keys %measureColsCreate)
      {
        $query .= "ADD COLUMN $colName DOUBLE, ";
      }
      chop($query);  chop($query);
      $db->do($query);

      #NULL out any existing measure columns that are being overwritten
      foreach $colName (keys %measureColsOverwrite)
      {
        $query = "UPDATE $dsSchema.facts SET $colName = NULL";
        $db->do($query);
      }

      #recalculate all measures in the destination DS
      DSRmeasures_recalculate_all_measures($db, $dsSchema);
    }

    #remove this task from the jobs table
    DSRutil_clear_status($db);
    $query = "UPDATE dataSources SET lastModified=NOW() WHERE ID=$dsID";
    $db->do($query);

    exit;
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-11 col-md-8 col-lg-6 col-xl-5"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Transfer Data Source Structures</DIV>
        <DIV CLASS="card-body">

          The requested data source structures are being transferred.

          <P>&nbsp;<P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='display.cld?ds=$dsID'"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
          </DIV>

        </DIV>
      </DIV>
    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

  utils_slack($activity);
  DSRutil_clear_status($db);


#EOF
