# CSV Upload Functionality Test Plan

## Overview
This document outlines the testing approach for the CSV upload functionality in the Koala Data Analytics application, including verification of recent improvements to exception handling, database retry logic, and email notifications.

## Recent Improvements Made
Based on code analysis, the following improvements have been implemented:

### 1. Database Connection Retry Logic
- **File**: `Lib/PrepUtils.pm`
- **Enhancement**: Added robust retry mechanisms with configurable parameters
- **Features**:
  - Maximum 3 retry attempts with 5-second delays
  - Automatic reconnection for "MySQL server has gone away" errors
  - Detailed logging of connection attempts and failures

### 2. Exception Handling with Email Notifications
- **File**: `Lib/PrepUtils.pm`
- **Enhancement**: Added comprehensive exception handling system
- **Features**:
  - Email notifications <NAME_EMAIL> for all exceptions
  - Multiple email delivery methods (system mail, sendmail, SMTP, file queue)
  - Detailed error logging with timestamps and context
  - Fallback mechanisms when email delivery fails

### 3. SQL Execution Retry Logic
- **File**: `Lib/PrepUtils.pm`
- **Enhancement**: Added `PrepUtils_execute_with_retry` function
- **Features**:
  - Automatic retry for deadlock, timeout, and lock wait timeout errors
  - Configurable retry parameters (max_retries, retry_delay)
  - Email notifications for SQL execution failures

## Test Data Created

### Sample CSV File: `test_data.csv`
```csv
Product,UPC,Geography,Time,Units Sold,Sales Revenue,Market Share
"Test Product A 12345","0-12345-67890-1","Test Market","1 MONTH ENDING 12/31/2023",150,2250.50,15.5
"Test Product B 23456","0-23456-78901-2","Test Market","1 MONTH ENDING 12/31/2023",200,3500.75,22.3
"Test Product C 34567","0-34567-89012-3","Test Market","1 MONTH ENDING 12/31/2023",175,2875.25,18.7
"Test Product D 45678","0-45678-90123-4","Test Market","1 MONTH ENDING 12/31/2023",125,1950.00,12.8
"Test Product E 56789","0-56789-01234-5","Test Market","1 MONTH ENDING 12/31/2023",300,4200.00,30.7
```

This test data follows the expected format with:
- Product names with item numbers
- Properly formatted UPC codes
- Geography and time period information
- Numerical measures (Units Sold, Sales Revenue, Market Share)

## Upload Endpoints Analysis

### 1. DSR Upload Handler: `dsr/xhrHandleUpload.cld`
- **Purpose**: Handles Data Source Repository uploads
- **File Naming**: `$userID.$key.$file`
- **Location**: `/opt/apache/app/tmp/`
- **Features**:
  - File sanitization (removes special characters)
  - Database connection with retry logic
  - JSON response format

### 2. Prep Upload Handler: `prep/xhrHandleUpload.cld`
- **Purpose**: Handles Data Preparation uploads
- **File Naming**: `prep.$userID.$key.$file`
- **Location**: `/opt/apache/app/tmp/`
- **Features**:
  - Similar sanitization and processing
  - Integrated with prep workflow system

### 3. Report Upload Handler: `rpt/xhrHandleUpload.cld`
- **Purpose**: Handles report background image uploads
- **Storage**: Database BLOB storage
- **Features**:
  - Binary file handling
  - Database insertion with error handling

## Testing Approach

### Phase 1: Code Verification ✅
- [x] Analyzed upload handler logic
- [x] Verified file sanitization processes
- [x] Confirmed database integration points
- [x] Reviewed exception handling implementation

### Phase 2: Configuration Verification
Since this is a production system, the following should be verified in a proper environment:

#### Database Configuration
- [ ] Verify database connection strings in `Lib/KoalaConfig.pm`
- [ ] Test database connectivity with retry logic
- [ ] Confirm exception handling triggers email notifications

#### File System Permissions
- [ ] Verify `/opt/apache/app/tmp/` directory exists and is writable
- [ ] Test file upload and storage processes
- [ ] Confirm file cleanup processes work correctly

#### Email System Configuration
- [ ] Verify email system is configured (postfix/sendmail)
- [ ] Test email <NAME_EMAIL>
- [ ] Confirm email queue fallback works when SMTP is unavailable

### Phase 3: Functional Testing
The following tests should be performed in a proper Linux/Apache environment:

#### Upload Process Testing
1. **Basic CSV Upload**
   - Upload the test CSV file through the web interface
   - Verify file is stored with correct naming convention
   - Confirm JSON response is returned correctly

2. **File Processing**
   - Verify CSV parsing works correctly
   - Test column detection and type inference
   - Confirm data validation processes

3. **Error Handling**
   - Test with malformed CSV files
   - Verify exception emails are sent
   - Confirm retry logic works for database errors

4. **Large File Testing**
   - Test with files approaching size limits
   - Verify timeout handling
   - Confirm memory usage is reasonable

### Phase 4: Integration Testing
1. **End-to-End Workflow**
   - Upload CSV → Process → Create Data Source
   - Verify all steps complete successfully
   - Confirm data is accessible in reports

2. **Concurrent Upload Testing**
   - Test multiple simultaneous uploads
   - Verify file naming prevents conflicts
   - Confirm system stability under load

## Expected Results

### Successful Upload Indicators
1. **File Storage**: File appears in `/opt/apache/app/tmp/` with correct naming
2. **JSON Response**: Proper JSON response with file name
3. **Database Logging**: Upload activity logged in audit tables
4. **No Exceptions**: No error emails <NAME_EMAIL>

### Error Scenarios to Test
1. **Database Connection Failure**
   - Should trigger retry logic
   - Should send exception email if retries fail
   - Should log detailed error information

2. **File System Issues**
   - Should handle permission errors gracefully
   - Should send appropriate error responses
   - Should not crash the application

3. **Invalid File Formats**
   - Should reject non-CSV files appropriately
   - Should provide clear error messages
   - Should not process malformed data

## Monitoring and Validation

### Log Files to Monitor
- `/opt/apache/htdocs/tmp/koala_error.log` - General application errors
- `/var/log/mail.log` - Email delivery status
- `/tmp/prep_email_queue.txt` - Email queue fallback
- `/tmp/prep_email_errors.log` - Email delivery failures

### Database Tables to Check
- `prep.audit` - Upload activity logging
- `prep.jobs` - Processing job status
- `prep.telemetry` - Detailed operation logs

## Conclusion

The CSV upload functionality has been significantly improved with robust error handling, retry logic, and email notifications. The test data and plan provided here will help verify that all improvements work correctly in a production environment.

**Next Steps:**
1. Deploy to a Linux/Apache environment with Perl support
2. Execute the functional testing phases
3. Verify email notifications work correctly
4. Monitor system performance under various load conditions
