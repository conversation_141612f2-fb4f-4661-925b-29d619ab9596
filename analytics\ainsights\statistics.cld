#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::AInsights::AInsights;
use Lib::AInsights::Utils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: AInsights Model Statistics</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">AInsights</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="insights.cld?pm=$priceModelID">$pricingName</A></LI>
    <LI CLASS="breadcrumb-item active">Statistics</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

sub commify
{
  my $text = reverse $_[0];
  $text =~ s/(\d\d\d)(?=\d)(?!\d*\.)/$1,/g;
  return scalar reverse $text;
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $priceModelID = $q->param('pm');

  AInsights_Utils_initialize_constants($priceModelID);

  #connect to the database
  $db = KAPutil_connect_to_database();

  #get the model's backing data source
  $query = "SELECT dsID, lastRun FROM analytics.pricing WHERE ID=$priceModelID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($dsID, $lastRun) = $dbOutput->fetchrow_array;

  $dsSchema = "datasource_" . $dsID;

  $pricingName = AInsights_ID_to_name($db, $priceModelID);

  print_html_header();

  #make sure we have privs to at least view this model
  $privs = AInsights_rights($db, $userID, $priceModelID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to view statistics for this AInsights model.");
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-11 col-lg-8 col-xl-7"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">AInsights Model Statistics</DIV>
        <DIV CLASS="card-body">
END_HTML

  #get the size of the model table
  $query = "SELECT data_length, index_length \
      FROM information_schema.TABLES \
      WHERE information_schema.TABLES.table_schema = '$dsSchema' AND information_schema.TABLES.table_name = '$AInsightsItemTable'";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($sizeData, $sizeIndex) = $dbOutput->fetchrow_array;
  $modelSize = $sizeData + $sizeIndex;

  #turn the size into MB
  $modelSize = $modelSize / 1024;
  $modelSize = $modelSize / 1024;

  if ($modelSize < 0.1)
  {
    $modelSize = 0.1;
  }

  #if we're into GB range, convert to GB
  if ($modelSize > 1024)
  {
    $modelSize = $modelSize / 1024;
    $modelSize = sprintf("%0.2f GB", $modelSize);
  }
  else
  {
    $modelSize = sprintf("%0.2f MB", $modelSize);
  }

  #get the size of the model details table
  $query = "SELECT data_length, index_length FROM information_schema.TABLES \
      WHERE information_schema.TABLES.table_schema = '$dsSchema' AND information_schema.TABLES.table_name = '$AInsightsItemCube'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($sizeData, $sizeIndex) = $dbOutput->fetchrow_array;
  $modelDetailsSize = $sizeData + $sizeIndex;

  #turn the size into MB
  $modelDetailsSize = $modelDetailsSize / 1024;
  $modelDetailsSize = $modelDetailsSize / 1024;

  if ($modelDetailsSize < 0.1)
  {
    $modelDetailsSize = 0.1;
  }

  #if we're into GB range, convert to GB
  if ($modelDetailsSize > 1024)
  {
    $modelDetailsSize = $modelDetailsSize / 1024;
    $modelDetailsSize = sprintf("%0.2f GB", $modelDetailsSize);
  }
  else
  {
    $modelDetailsSize = sprintf("%0.2f MB", $modelDetailsSize);
  }

  $query = "SELECT COUNT(productID) FROM $dsSchema.$AInsightsItemTable \
      WHERE modelConfidence > 0";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($successCount) = $dbOutput->fetchrow_array;

  $query = "SELECT COUNT(productID) FROM $dsSchema.$AInsightsItemTable \
      WHERE status='No sales'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($noSalesCount) = $dbOutput->fetchrow_array;

  $query = "SELECT COUNT(productID) FROM $dsSchema.$AInsightsItemTable \
      WHERE status LIKE 'Not enough data points %'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($lowDataCount) = $dbOutput->fetchrow_array;

  $query = "SELECT COUNT(productID) FROM $dsSchema.$AInsightsItemTable \
      WHERE status='Invalid model'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($invalidModelCount) = $dbOutput->fetchrow_array;

  print <<END_HTML;
          <DIV CLASS="table-responsive">
            <TABLE CLASS="table table-sm table-borderless">
              <TR>
                <TD CLASS="text-end"><B>AInsights model size:</B></TD>
                <TD>$modelSize</TD>
              </TR>
              <TR>
                <TD CLASS="text-end"><B>AInsights details size:</B></TD>
                <TD>$modelDetailsSize</TD>
              </TR>
              <TR>
                <TD>&nbsp;</TD><TD>&nbsp;</TD>
              </TR>
              <TR>
                <TD CLASS="text-end"><B>AInsights Model Calculations:</B></TD>
                <TD>&nbsp;</TD>
              </TR>
              <TR>
                <TD>&nbsp;</TD>
                <TD>$successCount successful</TD>
              </TR>
              <TR>
                <TD>&nbsp;</TD>
                <TD>$noSalesCount no sales data</TD>
              </TR>
              <TR>
                <TD>&nbsp;</TD>
                <TD>$lowDataCount insufficient sales data</TD>
              </TR>
              <TR>
                <TD>&nbsp;</TD>
                <TD>$invalidModelCount couldn't find a valid model</TD>
              </TR>
              <TR>
                <TD>&nbsp;</TD><TD>&nbsp;</TD>
              </TR>
              <TR>
                <TD CLASS="text-end"><B>Last model refresh:</B></TD>
                <TD>$lastRun</TD>
              </TR>
            </TABLE>
          </DIV>

          <P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" onClick="location.href='main.cld'"><I CLASS="bi bi-check-lg"></I> Done</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
<P>
END_HTML

  print_html_footer();

  $activity = "$first $last viewed statistics for $pricingName";
  utils_slack($activity);


#EOF
