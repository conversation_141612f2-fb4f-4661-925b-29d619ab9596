#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Segmentation</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/jquery-ui/jquery-ui.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<SCRIPT SRC="/fancytree-2.31.0/dist/jquery.fancytree-all-deps.min.js"></SCRIPT>
<SCRIPT SRC="/fancytree-2.31.0/dist/modules/jquery.fancytree.multi.js"></SCRIPT>
<SCRIPT SRC="/fancytree-2.31.0/dist/modules/jquery.fancytree.dnd.js"></SCRIPT>
<SCRIPT SRC="/fancytree-2.31.0/dist/modules/jquery.fancytree.edit.js"></SCRIPT>
<LINK REL="stylesheet" HREF="/jquery-ui/jquery-ui.css">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/fancytree-2.31.0/dist/skin-win8/ui.fancytree.min.css" rel="stylesheet">
<SCRIPT>
END_HTML

  #editing an existing segmentation, so fill out the tree with current data
  $editingExisting = 0;
  $newTree = 0;
  if ($segID > 0)
  {
    $editingExisting = 1;

    #init JSON string
    $json = "";

    #get hash of names of items in current dimension
    %itemNames = dsr_get_base_item_name_hash($db, $dsSchema, $dim);

    #get list of segments
    %segmentsHash = DSRseg_get_segments_hash($db, $dsSchema, $dim, $segID);

    #get the segment membership for every item in the data source
    %segmentMembership = DSRseg_get_segment_item_hash($db, $dsSchema, $dim, $segID);

    #run through each segment in the segmentation, sorted in alpha order
    foreach $segmentID (sort {$segmentsHash{$a} cmp $segmentsHash{$b}} keys %segmentsHash)
    {

      #get the items that belong to this segment
      #NB: We're doing this by repeatedly running through the hash of item
      #    segment membership, which at first glance seems weird and
      #    inefficient. But it keeps us from making a database query for
      #    every segment, which is ridiculously and very noticeably slow for
      #    segmentations with tons of segments. Maybe in the future
      #    implement a DSRstructures library function to return a hash of
      #    item arrays indexed by segment ID to save the CPU cycles.

      @segmentItems = split(/,/, $segmentMembership{$segmentID});

      $segItemCount = scalar(@segmentItems);

      #add the segment to the tree
      $segmentName = $segmentsHash{$segmentID};
      $segmentName =~ s/\\//g;
      $segmentName =~ s/\"/\\"/g;
      $segmentName =~ s/\'/\\'/g;
      $id = "SEG_" . $segmentName;

      #if the segment doesn't have any items in it
      if ($segItemCount < 1)
      {
        $json .= "{key: '$id', title: '$segmentName', type: 'segment', folder: true},\n";
      }

      #else output the segment and all of its children
      else
      {
        $json .= "{key: '$id', title: '$segmentName', type: 'segment', folder: true, 'children': [\n";

        foreach $itemID (@segmentItems)
        {
          $name = $itemNames{$itemID};

          $name =~ s/\\//g;
          $name =~ s/\"/\\"/g;
          $name =~ s/\'/\\'/g;
          $name =~ s/&LT/& LT/g;

          $json .= "{key: '$itemID', title: '$name'},\n";
        }

        chop($json);  chop($json);

        $json .= "]},\n";
      }
    }

    #chop the trailing comma and new line off the tree data
    chop($json); chop($json);
  }

  #creating a new segmentation, initialize an empty tree
  else
  {
    $newTree = 1;
  }

  print <<END_HTML;
globalNewTree = $newTree;

\$(function()
{
  \$('#segtree').fancytree(
  {
    extensions: ['dnd', 'edit', 'multi'],
    selectMode: 2,
    quicksearch: true,
    autoScroll: true,
    multi: {mode: 'sameParent'},
    source: [$json],
    edit:
    {
      adjustWidthOfs: 4,
      inputCss: {minWidth: '3em'},
      triggerStart: ['f2', 'dblclick'],
      beforeEdit: function(event, data)
      {
        //make sure only segment names are getting edited
        if (data.node.type != 'segment')
        {
          return(false);
        }
      },
      beforeClose: function(event, data)
      {
        let str = data.input;
        if (str.length < 1)
        {
          return(false);
        }
      }
    },
    dnd:
    {
      autoExpandMS: 400,
      draggable:
      {
        // modify default jQuery draggable options
        zIndex: 1000,
        scroll: true,
        containment: 'parent',
        revert: 'invalid'
      },
      preventRecursiveMoves: true, // Prevent dropping nodes on own descendants
      preventVoidMoves: true, // Prevent dropping nodes 'before self', etc.

      dragStart: function(node, data)
      {
        //don't allow dragging of parent folders
        if (node.isFolder())
        {
          return(false);
        }
        return(true);
      },
      dragEnter: function(node, data)
      {
        // Prevent dropping a parent below another parent (only sort
        // nodes under the same parent):
/*
        if (node.parent !== data.otherNode.parent)
        {
          return(false);
        }

      //don't allow dropping items outside of a segment folder
      if (node.parent.isRootNode())
      {
        return(false);
      }
*/

      //allow dropping on folder
      if (node.isFolder() == true)
      {
        return(true);
      }

      //don't allow dropping over another item node (don't create item child)
      if (node.isFolder() != 1)
      {
        return(['before', 'after']);
      }

      //fall-through: accept everything
      return(true);
    },
    dragExpand: function(node, data)
    {
      // return false to prevent auto-expanding data.node on hover
    },
    dragOver: function(node, data)
    {
    },
    dragLeave: function(node, data)
    {
    },
    dragStop: function(node, data)
    {
    },
    dragDrop: function(node, data)
    {
      // This function MUST be defined to enable dropping of items on the tree.
      // data.hitMode is 'before', 'after', or 'over'.
      // We could for example move the source to the new target:
      let mode = data.hitMode;

      //if we're dropping onto a folder, make it a child of that folder
      if (node.isFolder() == true)
      {
        mode = 'child';
      }
      data.otherNode.moveTo(node, mode);
    }
  }
  });
});



function addSegment()
{
  let newSegmentName = document.getElementById('newSegment').value;
  let segID = 'SEG_' + newSegmentName;
  let obj = [{key: segID, title: newSegmentName, type: 'segment', folder: true}];

  if (newSegmentName.length == 0)
  {
    return;
  }

  \$('#segtree').fancytree('getRootNode').addChildren(obj);
  \$('#segtree').fancytree('getRootNode').sortChildren();

  document.getElementById('newSegment').value = '';
  globalNewTree = 0;
}



function addItem()
{
  let i;

  if (globalNewTree == 1)
  {
    return;
  }

  let baseObj = document.getElementById('baseItems');
  let tree = \$('#segtree').fancytree('getTree');

  let curNode = tree.getActiveNode();
  if (curNode == null)
  {
    return;
  }

  if (curNode.type != 'segment')
  {
    curNode = curNode.getParent();
  }

  let isSelected = [];
  for (i = 0; i < baseObj.options.length; i++)
  {
    isSelected[i] = baseObj.options[i].selected;
    if (isSelected[i])
    {
      let itemID = baseObj.options[i].value;
      let itemName = baseObj.options[i].label;
      curNode.addChildren({key: itemID, title: itemName});
    }
  }

  i = baseObj.options.length;
  while (i--)
  {
    if (isSelected[i])
    {
      baseObj.remove(i);
    }
  }
}



function sortSelect(selElem)
{
  let tmpAry = new Array();

  for (let i = 0; i < selElem.options.length; i++)
  {
    tmpAry[i] = new Array();
    tmpAry[i][0] = selElem.options[i].text;
    tmpAry[i][1] = selElem.options[i].value;
  }
  tmpAry.sort();
  /*
  while (selElem.options.length > 0)
  {
    selElem.options[0] = null;
  }
  */
  for (let i = 0; i < tmpAry.length; i++)
  {
    //var op = new Option(tmpAry[i][0], tmpAry[i][1]);
    //selElem.options[i] = op;
    selElem.options[i].text = tmpAry[i][0];
    selElem.options[i].value = tmpAry[i][1];
  }
  return;
}



function removeItem()
{
  let baseObj = document.getElementById('baseItems');
  let tree = \$('#segtree').fancytree('getTree');

  if (globalNewTree == 1)
  {
    return;
  }

  let nodes = tree.getSelectedNodes();
  let nodeCount = nodes.length;
  for (let i = 0; i < nodeCount; i++)
  {

    //if it's an entire segment
    if (nodes[i].folder == true)
    {
      let childNodeCount;
      let childNodes = nodes[i].getChildren();
      if (childNodes == null)
      {
        childNodeCount = 0;
      }
      else
      {
        childNodeCount = childNodes.length;
      }
      for (let j = 0; j < childNodeCount; j++)
      {
        let opt = document.createElement('option');
        opt.text = childNodes[j].title;
        opt.value = childNodes[j].key;
        baseObj.add(opt);
      }
      for (let j = 0; j < childNodeCount; j++)
      {
        childNodes[0].remove();
      }
    }

    //else it's a single item
    else
    {
      let opt = document.createElement('option');
      opt.text = nodes[i].title;
      opt.value = nodes[i].key;
      baseObj.add(opt);
    }
  }

  for (let i = 0; i < nodeCount; i++)
  {
    nodes[i].remove();
  }

  sortSelect(baseObj);
}



function submitForm()
{
  \$('#btn-next').prop('disabled', true);
  \$('#btn-next').text('Saving...');

  let form = document.getElementById('segForm');
  let tree = \$('#segtree').fancytree('getTree');

  let segments = tree.findAll(function(node)
  {
    if (node.type == 'segment')
    {
      return(true);
    }
    else
    {
      return(false);
    }
  });

  for (let i = 0; i < segments.length; i++)
  {
    let items = segments[i].getChildren();
    if (items != null)
    {
      numItems = items.length;
    }
    else
    {
      numItems = 0;
    }

    let val = '';
    for (let j = 0; j < numItems; j++)
    {
      val = val + items[j].key + ',';
    }

    let hiddenField = document.createElement('input');
    hiddenField.setAttribute('type', 'hidden');
    hiddenField.setAttribute('name', 'SEG ' + segments[i].title);
    hiddenField.setAttribute('value', val);
    form.appendChild(hiddenField);
  }

  form.submit();
}



function doMatch()
{
  let pattern, modifiers;
  let matchCount = 0;
  let baseObj = document.getElementById('baseItems');
  let matchType = document.getElementById('matchType').value;
  let matchChars = document.getElementById('matchChars').value;
  let matchCase = document.getElementById('matchCase').checked;
  let matchSide = document.getElementById('matchSide').value;

  let i = 0;
  while (i < baseObj.options.length)
  {
    baseObj.options[i].selected = 0;
    i++;
  }

  \$('#segtree').fancytree('getTree').visit(function(node)
  {
    node.setSelected(false);
  });

  if (matchType == 'start')
  {
    pattern = '^' + matchChars;
  }
  else if (matchType == 'end')
  {
    pattern = matchChars + '\$';
  }
  else
  {
    pattern = matchChars;
  }

  let regex;
  if (matchCase)
  {
    regex = new RegExp(pattern);
  }
  else
  {
    regex = new RegExp(pattern, 'i');
  }

  if ((matchSide == 'base') || (matchSide == 'both'))
  {
    i = 0;
    while (i < baseObj.options.length)
    {
      let itemName = baseObj.options[i].text;
      if (regex.test(itemName))
      {
        baseObj.options[i].selected = 1;
        matchCount++;
        i++;
      }
      else
      {
        i++;
      }
    }
  }

  if ((matchSide == 'seg') || (matchSide == 'both'))
  {
    let tree = \$('#segtree').fancytree('getTree');

    let matches = tree.findAll(function(node)
    {
      if (regex.test(node.title))
      {
        return(true);
      }
      else
      {
        return(false);
      }
    });

    matchCount = matchCount + matches.length;

    for (i = 0; i < matches.length; i++)
    {
      matches[i].parent.setExpanded()
      matches[i].setSelected();
    }
  }

  \$('#modal-match').modal('hide');

  if (matchCount == 0)
  {
    \$('#modal-nomatch').modal('show');
  }

  return(1);
}


function closeWarning()
{
  document.getElementById('warn-select-items').style.display = 'none';
}


function dispItemInfo()
{
  let i, isSelected;
  let itemID, itemName;
  let itemStr;

  let baseObj = document.getElementById('baseItems');
  let tree = \$('#segtree').fancytree('getTree');
  let curNodes = tree.getSelectedNodes();;

  itemStr = '';
  for (i = 0; i < baseObj.options.length; i++)
  {
    isSelected = baseObj.options[i].selected;
    if (isSelected)
    {
      itemID = baseObj.options[i].value;
      itemStr = itemStr + itemID + ',';
    }
  }

  for (i = 0; i < curNodes.length; i++)
  {
    curNode = curNodes[i];

    if (curNode.folder != true)
    {
      itemStr = itemStr + curNode.key + ',';
    }
  }

  if (itemStr.length < 1)
  {
    document.getElementById('warn-select-items').style.display = 'block';
    return;
  }

  document.getElementById('warn-select-items').style.display = 'none';

  let urlStr = 'segmentItemInfo.cld?ds=$dsID&d=$dim&s=$segID&i=' + itemStr;

  window.open(urlStr, '_blank', 'menubar=0,status=0,toolbar=0,width=600,height=600');
}


function editRules()
{
  location.href='segmentRules.cld?ds=$dsID&dim=$dim&seg=$segID&segName=$segName';
}

</SCRIPT>

<STYLE>
ul.fancytree-container
{
  height:445px;
  width: 100%;
  overflow: auto;
  border:1px solid darkgray;
  font-size: 14px;
}

span.fancytree-selected span.fancytree-title
{
  background-color: rgb(211,211,211) !important;
}

.accordion-button.collapsed {
  background: blue
}

.accordion-button.collapsed::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}
</STYLE>

</HEAD>

<BODY>

<DIV ID="warn-select-items" CLASS="alert alert-warning alert-dismissible fade show" role="alert" STYLE="display:none; width:50%; position:absolute; margin-left:25%;">
  <DIV CLASS="text-center"><STRONG>Please select one or more items.</STRONG></DIV>
  <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="alert"></BUTTON>
</DIV>


END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">$action Segmentation $segName</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');
  $dim = $q->param('dim');
  $segName = $q->param('segName');
  $segID = $q->param('seg');

  if ($segID =~ m/^SEG_(\d+)$/)
  {
    $segID = $1;
  }

  #set human-readable action
  if ($segID > 0)
  {
    $action = "Edit";
  }
  else
  {
    $action = "New";
  }

  #connect to the database
  $db = KAPutil_connect_to_database();

  #make sure we have write privs for the data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    print_html_header();
    exit_error("You don't have privileges to modify the data source.");
  }

  $dsSchema = "datasource_" . $dsID;

  #get our dimension name for display purposes/db table name for SELECT
  $dimName = KAPutil_get_dim_name_singular($dim, 1);
  $itemDB = KAPutil_get_dim_db_name($dim);
  $dimDB = KAPutil_get_dim_stub_name($dim);

  #if we're editing an existing segmentation and came straight here without
  #passing through the "Segmentation Name" screen first, get the name
  if (($segID > 0) && (length($segName) < 1))
  {
    $dbName = $dimDB . "segmentation";
    $query = "SELECT name FROM $dsSchema.$dbName WHERE ID=$segID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute();
    ($segName) = $dbOutput->fetchrow_array;
  }

  #if the name has (maybe) been changed on an already existing segmentation
  elsif (($segID > 0) && (length($segName) > 0))
  {
    $q_segName = $db->quote($segName);
    $dbName = $dimDB . "segmentation";
    $query = "UPDATE $dsSchema.$dbName SET name=$q_segName WHERE ID=$segID";
    $db->do($query);
  }

  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  print <<END_HTML;
<DIV CLASS="container-fluid gx-3">

  <DIV CLASS="row">

    <DIV CLASS="col"> <!-- content -->

      <FORM METHOD="post" ID="segForm" ACTION="segmentSave.cld" onkeypress="return event.keyCode != 13;" onkeydown="return event.keyCode != 13;">
      <INPUT TYPE="hidden" NAME="dsID" VALUE="$dsID">
      <INPUT TYPE="hidden" NAME="dim" VALUE="$dim">
      <INPUT TYPE="hidden" NAME="segName" VALUE="$segName">
      <INPUT TYPE="hidden" NAME="seg" VALUE="$segID">

      <DIV CLASS="accordion mx-auto" ID="accordion">
        <DIV CLASS="accordion-item border-primary">
          <H2 CLASS="accordion-header">
            <BUTTON CLASS="accordion-button bg-primary text-white icon-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
              $action $dimName Segmentation
            </BUTTON>
          </H2>
          <DIV ID="collapse1" CLASS="collapse show" data-bs-parent="#accordion">
            <DIV CLASS="accordion-body">

              <!-- TABLE -->
                <DIV CLASS="row">
                  <DIV CLASS="col-auto gx-0">

                    <DIV CLASS="row">
                      <DIV CLASS="col-auto">
                        <INPUT CLASS="form-control mb-1" TYPE="text" NAME="newSegment" ID="newSegment" onkeyup="if (event.keyCode == 13) addSegment();">
                      </DIV>
                      <DIV CLASS="col auto gx-0">
                        <BUTTON CLASS="btn btn-primary btn-sm mt-1" TYPE="button" onClick="addSegment()"><I CLASS="bi bi-plus-lg"></I> Add Segment</BUTTON>
                      </DIV>
                    </DIV>

                  </DIV>
                </DIV>
                <DIV CLASS="row">
                  <DIV CLASS="col-5 col-sm-5 col-md-5 col-lg-5 col-xl-5 col-xxl gx-0" STYLE="vertical-align:top;">
                    <DIV ID="segtree" CLASS="mx-auto" STYLE="fontsize:16px;"></DIV>
                    <DIV CLASS="form-check">
                      <INPUT CLASS="form-check-input" TYPE="checkbox" NAME="allothers" ID="allothers">
                      <LABEL CLASS="form-check-label" FOR="allothers">Create "All Others" Segment</LABEL>
                    </DIV>
                  </DIV>
                  <DIV CLASS="col-2 col-sm-2 col-md-2 col-lg-2 col-xl-2 col-xxl-auto">
                    <P>&nbsp;&nbsp;</P>
                    <DIV CLASS="text-center">
                      <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="addItem()" TITLE="Segment the selected items"><I CLASS="bi bi-chevron-left" STYLE="font-size:48px;"></I></BUTTON>
                    </DIV>
                    <P>&nbsp;</P>
                    <DIV CLASS="text-center">
                      <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="removeItem()" TITLE="Unsegment the selected items"><I CLASS="bi bi-chevron-right" STYLE="font-size:48px;"></I></BUTTON>
                    </DIV>
                    <P>&nbsp;<BR></P>
                    <P>&nbsp;<BR></P>
                    <DIV CLASS="text-center">
                      <BUTTON CLASS="btn btn-primary" TYPE="button" data-bs-toggle="modal" data-bs-target="#modal-match"><I CLASS="bi bi-search"></I> Match</BUTTON><BR>
                    </DIV>
                  </DIV>
                  <DIV CLASS="col-5 col-sm-5 col-md-5 col-lg-5 col-xl-5 col-xxl gx-0">
                    <SELECT CLASS="form-select" ID="baseItems" NAME="baseItems" STYLE="height:445px; overflow:auto;" MULTIPLE onDblClick="addItem()">
END_HTML

  #if we're editing an existing segmentation
  if ($segID > 0)
  {
    @unsegmentedIDs = DSRseg_get_unsegmented_items($db, $dsSchema, $dim, $segID);

    foreach $itemID (@unsegmentedIDs)
    {
      $itemName = $itemNames{$itemID};
      print("<OPTION VALUE=\"$itemID\">$itemName</OPTION>\n");
    }
  }

  #we're creating a new segmentation, so display all available base items
  else
  {
    $query = "SELECT ID, name FROM $dsSchema.$itemDB ORDER BY name";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;

    while (($ID, $name) = $dbOutput->fetchrow_array)
    {
      if (length($name) > 0)
      {
        print("<OPTION VALUE=\"$ID\">$name</OPTION>\n");
      }
    }
  }

  print <<END_HTML;
                    </SELECT>
                    <BUTTON CLASS="btn btn-primary mt-1" TYPE="button" onClick="dispItemInfo()"><I CLASS="bi bi-eye"></I> Display Item Info</BUTTON>
                  </DIV>
                </DIV>
              </DIV>
            <!-- /TABLE -->
            <P>
          </DIV>
        </DIV>
END_HTML

#get the ID of our parent segmentation, if we have one
if ($segID > 0)
{
  $dbName = $dimDB . "segmentation";
  $query = "SELECT parentID FROM $dsSchema.$dbName WHERE ID=$segID";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($parentSeg) = $dbOutput->fetchrow_array;

  #get the ID of any child segmentations we might have
  $childSegs = "";
  $query = "SELECT ID FROM $dsSchema.$dbName WHERE parentID=$segID";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  while (($tmp) = $dbOutput->fetchrow_array)
  {
    $childSegs .= "$tmp,";
  }
}

  print <<END_HTML;
      <DIV CLASS="accordion-item border-primary">
        <H2 CLASS="accordion-header">
          <BUTTON CLASS="accordion-button collapsed bg-primary text-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse3" onClick="editRules()">
            Segmentation Rules
          </BUTTON>
        </H2>
        <DIV ID="collapse3" CLASS="accordion-collapse collapse" data-bs-parent="#accordion">
          <DIV CLASS="accordion-body">

          </DIV>
        </DIV>
      </DIV>
END_HTML

  #display info about linked segmentations (if we have any)
  if (($parentSeg > 0) || (length($childSegs) > 0))
  {

    print <<END_HTML;
      <DIV CLASS="accordion-item border-primary">
        <H2 CLASS="accordion-header">
          <BUTTON CLASS="accordion-button collapsed bg-primary text-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse4">
            Linked Segmentations
          </BUTTON>
        </H2>
        <DIV ID="collapse4" CLASS="accordion-collapse collapse" data-bs-parent="#accordion">
          <DIV CLASS="accordion-body">
            <TABLE CLASS="table">
END_HTML

    %segNameHash = DSRsegmentation_get_segmentations_hash($db, $dsSchema, $dim);

    if ($parentSeg > 0)
    {
      print <<END_HTML;
              <TR>
                <TD>
                  <A CLASS="btn btn-primary" TARGET="_blank" HREF="segmentAssign.cld?ds=$dsID&dim=$dim&segName=$segNameHash{$parentSeg}&seg=$parentSeg"><I CLASS="bi bi-folder2-open"></I> Open Linked Segmentation</A>
                  <P></P>
                  <A CLASS="btn btn-primary" HREF="segmentBreakLink.cld?ds=$dsID&dim=$dim&pseg=$parentSeg&cseg=$segID"><I CLASS="bi bi-scissors"></I> Cut Link</A>
                </TD>
                <TD>
                  This segmentation is linked to the <STRONG>$segNameHash{$parentSeg}</STRONG> segmentation - changes made to unsegmented items in $segNameHash{$parentSeg} will automatically be applied to this segmentation.
                </TD>
              </TR>
END_HTML
    }

    if (length($childSegs) > 0)
    {
      @tmp = split(',', $childSegs);
      foreach $id (@tmp)
      {
        print <<END_HTML;
              <TR>
                <TD>
                  <A CLASS="btn btn-primary text-nowrap" TARGET="_blank" HREF="segmentAssign.cld?ds=$dsID&dim=$dim&segName=$segNameHash{$id}&seg=$id"><I CLASS="bi bi-folder2-open"></I> Open Linked Segmentation</A>
                  <P></P>
                  <A CLASS="btn btn-primary" TARGET="_blank" HREF="segmentBreakLink.cld?ds=$dsID&dim=$dim&pseg=$segID&cseg=$id"><I CLASS="bi bi-scissors"></I> Cut Link</A>
                </TD>
                <TD>
                  <STRONG>$segNameHash{$id}</STRONG> is linked to this segmentation. Changes made to this segmentation will automatically be applied to the <STRONG>$segNameHash{$id}</STRONG> segmentation.
                </TD>
              </TR>
END_HTML
      }
    }

    print <<END_HTML;
            </TABLE>
          </DIV>
        </DIV>
      </DIV>
END_HTML
  }


  #if we're creating a new segmentation, give the user the option to create
  #it based on an attribute
  if ($editingExisting == 0)
  {
    print <<END_HTML;
      <DIV CLASS="accordion-item border-primary">
        <H2 CLASS="accordion-header">
          <BUTTON CLASS="accordion-button collapsed bg-primary text-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
            Segmentation by Attribute
          </BUTTON>
        </H2>
        <DIV ID="collapse2" CLASS="accordion-collapse collapse" data-bs-parent="#accordion">
          <DIV CLASS="accordion-body">

            Choose the $dimension attribute you'd like to use as the basis for a segmentation.

            <P>&nbsp;</P>
            Attribute:
            <SELECT CLASS="form-select" ID="attr" NAME="attr" STYLE="width:300px">
              <OPTION VALUE="0"></OPTION>
END_HTML

    $dbName = $dimDB . "attributes";
    $query = "SELECT ID, name FROM $dsSchema.$dbName";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;

    while (($id, $name) = $dbOutput->fetchrow_array)
    {
      print("<OPTION VALUE=\"$id\">$name</OPTION>\n");
    }

    print <<END_HTML;
            </SELECT>
          </DIV>
        </DIV>
      </DIV>
END_HTML
  }

  print <<END_HTML;
      </DIV>

      <P>
      <DIV CLASS="text-center">
        <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="location.href='/app/dsr/display.cld?ds=$dsID&dim=$dim'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
        <BUTTON CLASS="btn btn-primary" TYPE="button" ID="btn-next" onClick="submitForm()"><I CLASS="bi bi-save"></I> Save</BUTTON>
      </DIV>
    </FORM>

    <DIV ID="modal-match" class="modal" role="dialog">
      <DIV CLASS="modal-dialog" STYLE="overflow-y: initial !important;">
        <DIV CLASS="modal-content">

          <DIV CLASS="modal-header">
            <H5 CLASS="modal-title">Match</H5>
            <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
          </DIV>

          <DIV CLASS="modal-body">

            <DIV CLASS="row">
              <DIV CLASS="col-auto gx-0 ms-2 mt-1">
                <LABEL FOR="matchType">Select items that:</LABEL>
              </DIV>
              <DIV CLASS="col-auto">
                <SELECT CLASS="form-select" NAME="matchType" ID="matchType">
                  <OPTION VALUE="contain">Contain</OPTION>
                  <OPTION VALUE="start">Begin With</OPTION>
                  <OPTION VALUE="end">End With</OPTION>
                </SELECT>
              </DIV>
            </DIV>

            <P>
            <DIV CLASS="row">
              <DIV CLASS="col-auto gx-0 ms-2 mt-1">
                <LABEL FOR="matchChars">The characters:</LABEL>
              </DIV>
              <DIV CLASS="col-auto">
                <INPUT CLASS="form-control" NAME="matchChars" ID="matchChars">
              </DIV>
            </DIV>

            <P>
            <DIV CLASS="form-check">
              <INPUT CLASS="form-check-input" ID="matchCase" TYPE="checkbox">
              <LABEL CLASS="form-check-label" FOR="matchCase">Case Sensitive</LABEL>
            </DIV>

            <P></P>
            <SELECT CLASS="form-select" NAME="matchSide" ID="matchSide" STYLE="width:auto;">
              <OPTION VALUE="base">Match unsegmented items</OPTION>
              <OPTION VALUE="seg">Match segmented items</OPTION>
              <OPTION VALUE="both">Match both</OPTION>
            </SELECT>

          </DIV>

          <DIV CLASS="modal-footer">

            <DIV CLASS="text-center">
              <BUTTON CLASS="btn btn-primary" TYPE="submit" onClick="return doMatch();">Match</BUTTON>
              <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="\$('#modal-match').modal('hide')">Cancel</BUTTON>
            </DIV>
          </DIV>

        </DIV>
      </DIV>
    </DIV>


    <DIV id="modal-nomatch" class="modal" role="dialog">
      <DIV CLASS="modal-dialog" STYLE="overflow-y: initial !important;">
        <DIV CLASS="modal-content">

          <DIV CLASS="modal-header">
            <H5>No Matches</H5>
            <BUTTON TYPE="button" CLASS="close" DATA-DISMISS="modal"><SPAN>&times;</SPAN></BUTTON>
          </DIV>

          <DIV CLASS="modal-body">

            No matching items found.

          </DIV>

          <DIV CLASS="modal-footer">

            <DIV CLASS="text-center">
              <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="\$('#modal-nomatch').modal('hide')">OK</BUTTON>
            </DIV>

          </DIV>

        </DIV>
      </DIV>
    </DIV>

    </DIV>  <!-- content -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
<P>
END_HTML


  print_html_footer();

#EOF
