#!/usr/bin/perl

use Text::CSV;

#Import weekly shipping data from ArchPoint's internal 852 shipping DB

  #instantiate our CSV object
  $csv = Text::CSV->new( {binary => 1} );

  open(INPUT, "$ARGV[0]");
  open(OUTPUT, ">$ARGV[1]");

  #parse header line
  $line = <INPUT>;
  $csv->parse($line);
  @columns = $csv->fields();

  #transform the headers into something Ko<PERSON> can work with
  $idx = 0;
  undef($timeIdx);
  foreach $header (@columns)
  {

    if ($header =~ m/^PRODUCT DESC/)
    {
      $columns[$idx] = "PRODUCT";
    }
    elsif ($header =~ m/^PRODUCT/)
    {
      $columns[$idx] = "UPC";
    }
    elsif ($header =~ m/^CLIENT NAME/)
    {
      $columns[$idx] = "PSEG:CLIENT NAME";
    }
    elsif ($header =~ m/^CUSTOMER CODE/)
    {
      $columns[$idx] = "PSEG:CUSTOMER CODE";
    }
    elsif ($header =~ m/^DUNS NUMBER/)
    {
      $columns[$idx] = "GATTR:DUNS NUMBER";
    }
    elsif ($header =~ m/^CUSTOMER NAME/)
    {
      $columns[$idx] = "Geography";
    }
    elsif ($header =~ m/^START DATE/)
    {
      $columns[$idx] = "Time";
      $timeIdx = $idx;
    }
    elsif ($header =~ m/^END DATE/)
    {
      $columns[$idx] = "TATTR:END DATE";
    }
    elsif ($header =~ m/^CLIENT CODE/)
    {
      $columns[$idx] = "PSEG:CLIENT CODE";
    }
    elsif ($header =~ m/^START PO/)
    {
      $columns[$idx] = "PATTR:START PO";
    }
    elsif ($header =~ m/^END PO/)
    {
      $columns[$idx] = "PATTR:END PO";
    }

    $idx++;
  }

  #output the headers
  $csv->combine(@columns);
  $line = $csv->string();
  print OUTPUT "$line\n";

  while ($line = <INPUT>)
  {
    $csv->parse($line);
    @columns = $csv->fields();

    $time = $columns[$timeIdx];
    $time =~ m/(\d+)\/(\d+)\/(\d+)/;
    $time = $3 . "/" . $1 . "/" . $2;
    $columns[$timeIdx] = $time;
    $columns[$timeIdx] = "1 WK $columns[$timeIdx]";

    $csv->combine(@columns);
    $line = $csv->string();

    print OUTPUT "$line\n";
  }

  close(INPUT);
  close(OUTPUT);
