#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::BuildCube;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Refresh All Reports</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/rpt/main">Reports</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/rpt/main">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Refresh All Reports</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser and get the forecast to be deleted
  $q = new CGI;
  $dsID = $q->param('ds');

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  $db = KAPutil_connect_to_database();

  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  $dsSchema = "datasource_" . $dsID;

  #get the last modified time of the backing data source
  $query = "SELECT UNIX_TIMESTAMP(lastModified) FROM app.dataSources \
      WHERE ID=$dsID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($lastModified) = $dbOutput->fetchrow_array;

  #get the list of all reports in this data source that need an update
  undef(@rptIDs);
  $query = "SELECT ID, UNIX_TIMESTAMP(lastUpdate) FROM cubes WHERE dsID=$dsID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  while (($id, $lastUpdate) = $dbOutput->fetchrow_array)
  {
    if ($lastUpdate < $lastModified)
    {
      push(@rptIDs, $id);
    }
  }

  #make sure we have write privs on at least one report
  if (scalar(@rptIDs) > 0)
  {
    $privs = cube_rights($db, $userID, $rptIDs[0], $acctType);
    if ($privs ne "W")
    {
      exit_error("You don't have privileges to refresh these reports.");
    }
  }

  #make sure the data source isn't currently being used by another process
  $ok = DSRutil_operation_ok($db, $dsID, 0, "CUBE-UPDATE");
  if ($ok != 1)
  {
    exit_warning("This data source is currently being updated by another job - please wait until it finishes to refresh reports in the data source.");
  }

  #if the user is already using more than their fair share of a production cloud
  $loadAvg = KAPutil_get_load_avg($db);
  if (($loadAvg >= 0.75) && ($acctType < 10))
  {
    $jobCount = KAPutil_get_user_jobs($db, $userID);

    #if the user already has more than a couple jobs running
    if ($jobCount > 1)
    {
      exit_error("Your analytics cloud is heavily loaded, and you already have at least one large job running. Wait a bit for that job to finish, and then try again.");
    }
  }

  #make sure the reports in this data source aren't currently being updated
  $ok = cube_refresh_ok($db, $dsID);
  if ($ok != 1)
  {
    exit_warning("At least one report in $dsName is already being refreshed - please wait for that refresh to finish before refreshing all of the reports in the data source.");

    print_html_footer();
    exit;
  }

  #fork a new process to do the actual refreshes in the background
  $SIG{CHLD} = "IGNORE";
  if ($pid = fork)
  {
    #parent process

    print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-11 col-md-8 col-lg-6 col-xl-5"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Refresh All Reports</DIV>
        <DIV CLASS="card-body">

          <P>
          All of the reports in the $dsName data source are now being refreshed.

          <P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='main'"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

    print_html_footer();

    $activity = "$first $last refreshed all reports in $dsName";
    utils_slack($activity);
  }

  #child process
  else
  {

    #let Apache know not to wait on the child process
    close(STDIN);
    close(STDOUT);
    close(STDERR);

    #put our error messages & output in a user-specific background job log
    open(STDERR, ">>/opt/apache/htdocs/tmp/koala_error.log") or die("Unable to open STDERR, $!");

    #reconnect to database
    $db = KAPutil_connect_to_database();

    #run the cube update process against each report
    foreach $id (@rptIDs)
    {
      cube_build($db, $dsSchema, $id, $userID);
    }
  }

#EOF
