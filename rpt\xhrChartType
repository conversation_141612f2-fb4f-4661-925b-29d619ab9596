#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::Reports;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $rptID = $q->param('rptID');
  $visID = $q->param('v');
  $chartType = $q->param('chartType');

  $db = KAPutil_connect_to_database();

  #make sure we have edit privs for this report
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this report.");
  }

  #get the main chart display details from the database
  $query = "SELECT design FROM visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($graphDesign) = $dbOutput->fetchrow_array;

#########################################################################
#
#if we're being called to save the user's selected chart type
#

  if (defined($chartType))
  {

    #save the new chart type
    $graphDesign =~ m/^(.*),type:.*?,(.*)$/;
    $graphDesign = $1 . ",type:$chartType," . $2;
    $q_graphDesign = $db->quote($graphDesign);

    $query = "UPDATE visuals \
        SET design = $q_graphDesign, graph_x = NULL, graph_y = NULL, graph_z = NULL \
        WHERE ID=$visID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    reports_graph_default_selections($db, $rptID, $visID, 1);

    $cubeName = cube_id_to_name($db, $rptID);
    $dsID = cube_get_ds_id($db, $rptID);
    $dsName = ds_id_to_name($db, $dsID);

    utils_audit($db, $userID, "Changed chart type", $dsID, $rptID, 0);

    $activity = "$first $last changed chart type for $cubeName in $dsName";
    utils_slack($activity);
  }


########################################################################

  #
  # Everything after this point is called to display the chart selection dialog
  #

  #NB: It's really annoying to have to manually build up a giant pile of
  #    variables to determine which chart type should be checked, but after
  #    2 days of frustration I can't find a way to do it inside an xhr'd Dojo
  #    dialog box.
  #extract the current chart type (default is Lines)
  undef($chkLines);
  undef($chkZoomLines);
  undef($chkScatter);
  undef($chkArea);
  undef($chkBubble);
  undef($chk2DColumns);
  undef($chkStacked2DColumns);
  undef($chk3DColumns);
  undef($chkStacked3DColumns);
  undef($chk2DBars);
  undef($chkStacked2DBars);
  undef($chk3DBars);
  undef($chkStacked3DBars);
  undef($chkWaterfall);
  undef($chk2DPie);
  undef($chk3DPie);
  undef($chk2DDonut);
  undef($chk3DDonut);
  undef($chkRadar);
  undef($chkTreeMap);
  undef($chkFunnel);
  $graphDesign =~ m/,type:(.*?),/;
  if ($1 eq "Lines")
  {
    $chkLines = "CHECKED";
  }
  elsif ($1 eq "ZoomLines")
  {
    $chkZoomLines = "CHECKED";
  }
  elsif ($1 eq "Scatter")
  {
    $chkScatter = "CHECKED";
  }
  elsif ($1 eq "Area")
  {
    $chkArea = "CHECKED";
  }
  elsif ($1 eq "Bubble")
  {
    $chkBubble = "CHECKED";
  }
  elsif ($1 eq "2DColumns")
  {
    $chk2DColumns = "CHECKED";
  }
  elsif ($1 eq "Stacked2DColumns")
  {
    $chkStacked2DColumns = "CHECKED";
  }
  elsif ($1 eq "3DColumns")
  {
    $chk3DColumns = "CHECKED";
  }
  elsif ($1 eq "Stacked3DColumns")
  {
    $chkStacked3DColumns = "CHECKED";
  }
  elsif ($1 eq "2DBars")
  {
    $chk2DBars = "CHECKED";
  }
  elsif ($1 eq "Stacked2DBars")
  {
    $chkStacked2DBars = "CHECKED";
  }
  elsif ($1 eq "3DBars")
  {
    $chk3DBars = "CHECKED";
  }
  elsif ($1 eq "Stacked3DBars")
  {
    $chkStacked3DBars = "CHECKED";
  }
  elsif ($1 eq "Waterfall")
  {
    $chkWaterfall = "CHECKED";
  }
  elsif ($1 eq "2DPie")
  {
    $chk2DPie = "CHECKED";
  }
  elsif ($1 eq "3DPie")
  {
    $chk3DPie = "CHECKED";
  }
  elsif ($1 eq "2DDonut")
  {
    $chk2DDonut = "CHECKED";
  }
  elsif ($1 eq "3DDonut")
  {
    $chk3DDonut = "CHECKED";
  }
  elsif ($1 eq "TreeMap")
  {
    $chkTreeMap = "CHECKED";
  }
  elsif ($1 eq "Radar")
  {
    $chkRadar = "CHECKED";
  }
  elsif ($1 eq "Funnel")
  {
    $chkFunnel = "CHECKED";
  }
  elsif ($1 eq "ColumnLine")
  {
    $chkColumnLine = "CHECKED";
  }
  elsif ($1 eq "DualY")
  {
    $chkDualY = "CHECKED";
  }
  else
  {
    $chkLines = "CHECKED";
  }

  print <<END_HTML;
<SCRIPT>
function submitForm()
{
  for (i = 0; i < document.options.chartType.length; i++)
  {
    if (document.options.chartType[i].checked)
    {
      chosen = document.options.chartType[i].value;
    }
  }

  let url = "xhrChartType?rptID=$rptID&v=$visID&chartType=" + chosen;

  \$.get(url, function(data, status)
  {
    location.href = "display.cld?rpt=$rptID&v=$visID";
  });
}
</SCRIPT>

<STYLE>
.input-hidden
{
  position: absolute;
  left: -9999px;
}

input[type=radio]:checked + label>img
{
  border: 1px solid #337ab7;
  box-shadow: 0 0 3px 3px #337ab7;
}
</STYLE>

<DIV CLASS="modal-dialog" STYLE="overflow-y: initial !important;">
  <DIV CLASS="modal-content">
    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Chart Type</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">
      <FORM NAME="options">
      <TABLE CELLPADDING="15" STYLE="width:95%;">
        <TR>
          <TD ALIGN="center" VALIGN="top" WIDTH="25%" HEIGHT="75px">
            <INPUT TYPE="radio" NAME="chartType" ID="lines" VALUE="Lines" CLASS="input-hidden" $chkLines></INPUT>
            <LABEL FOR="lines">
              <IMG SRC="/icons/chart_lines.png" TITLE="">
            </LABEL>
          </TD>
          <TD ALIGN="center" VALIGN="top" WIDTH="25%" HEIGHT="75px">
            <INPUT TYPE="radio" NAME="chartType" ID="zoomlines" VALUE="ZoomLines" CLASS="input-hidden" $chkZoomLines></INPUT>
            <LABEL FOR="zoomlines">
              <IMG SRC="/icons/chart_lines.png" TITLE="Zoom Lines">
            </LABEL>
          </TD>
          <TD ALIGN="center" VALIGN="top" WIDTH="25%" HEIGHT="75px">
            <INPUT TYPE="radio" NAME="chartType" ID="scatter" VALUE="Scatter" CLASS="input-hidden" $chkScatter></INPUT>
            <LABEL FOR="scatter">
              <IMG SRC="/icons/chart_scatter.png" TITLE="Scatter">
            </LABEL>
          </TD>
          <TD ALIGN="center" VALIGN="top" WIDTH="25%" HEIGHT="75px">
            <INPUT TYPE="radio" NAME="chartType" ID="area" VALUE="Area" CLASS="input-hidden" $chkArea></INPUT>
            <LABEL FOR="area">
              <IMG SRC="/icons/chart_area.png" TITLE="Area">
            </LABEL>
          </TD>
        </TR>
        <TR>
          <TD ALIGN="center" VALIGN="top" WIDTH="25%" HEIGHT="75px">
            <INPUT TYPE="radio" NAME="chartType" ID="2dcolumns" VALUE="2DColumns" CLASS="input-hidden" $chk2DColumns></INPUT>
            <LABEL FOR="2dcolumns">
              <IMG SRC="/icons/chart_2Dcolumn.png" TITLE="2D Columns">
            </LABEL>
          </TD>
          <TD ALIGN="center" VALIGN="top" WIDTH="25%" HEIGHT="75px">
            <INPUT TYPE="radio" NAME="chartType" ID="stacked2dcolumns" VALUE="Stacked2DColumns" CLASS="input-hidden" $chkStacked2DColumns></INPUT>
            <LABEL FOR="stacked2dcolumns">
              <IMG SRC="/icons/chart_2Dstackcolumn.png" TITLE="Stacked 2D Columns">
            </LABEL>
          </TD>
          <TD ALIGN="center" VALIGN="top" WIDTH="25%" HEIGHT="75px">
            <INPUT TYPE="radio" NAME="chartType" ID="3dcolumns" VALUE="3DColumns" CLASS="input-hidden" $chk3DColumns></INPUT>
            <LABEL FOR="3dcolumns">
              <IMG SRC="/icons/chart_3Dcolumn.png" TITLE="3D Columns">
            </LABEL>
          </TD>
          <TD ALIGN="center" VALIGN="top" WIDTH="25%" HEIGHT="75px">
            <INPUT TYPE="radio" NAME="chartType" ID="stacked3dcolumns" VALUE="Stacked3DColumns" CLASS="input-hidden" $chkStacked3DColumns></INPUT>
            <LABEL FOR="stacked3dcolumns">
              <IMG SRC="/icons/chart_3Dstackcolumn.png" TITLE="Stacked 3D Columns">
            </LABEL>
          </TD>
        </TR>
        <TR>
          <TD ALIGN="center" VALIGN="top" WIDTH="25%" HEIGHT="75px">
            <INPUT TYPE="radio" NAME="chartType" ID="2dbars" VALUE="2DBars" CLASS="input-hidden" $chk2DBars></INPUT>
            <LABEL FOR="2dbars">
              <IMG SRC="/icons/chart_2Dbar.png" TITLE="2D Bars">
            </LABEL>
          </TD>
          <TD ALIGN="center" VALIGN="top" WIDTH="25%" HEIGHT="75px">
            <INPUT TYPE="radio" NAME="chartType" ID="stacked2dbars" VALUE="Stacked2DBars" CLASS="input-hidden" $chkStacked2DBars></INPUT>
            <LABEL FOR="stacked2dbars">
              <IMG SRC="/icons/chart_2Dstackbar.png" TITLE="Stacked 2D Bars">
            </LABEL>
          </TD>
          <TD ALIGN="center" VALIGN="top" WIDTH="25%" HEIGHT="75px">
            <INPUT TYPE="radio" NAME="chartType" ID="3dbars" VALUE="3DBars" CLASS="input-hidden" $chk3DBars></INPUT>
            <LABEL FOR="3dbars">
              <IMG SRC="/icons/chart_3Dbar.png" TITLE="3D Bars">
            </LABEL>
          </TD>
          <TD ALIGN="center" VALIGN="top" WIDTH="25%" HEIGHT="75px">
            <INPUT TYPE="radio" NAME="chartType" ID="stacked3dbars" VALUE="Stacked3DBars" CLASS="input-hidden" $chkStacked3DBars></INPUT>
            <LABEL FOR="stacked3dbars">
              <IMG SRC="/icons/chart_3Dstackbar.png" TITLE="Stacked 3D Bars">
            </LABEL>
          </TD>
        </TR>
        <TR>
          <TD ALIGN="center" VALIGN="top" WIDTH="25%" HEIGHT="75px">
            <INPUT TYPE="radio" NAME="chartType" ID="2dpie" VALUE="2DPie" CLASS="input-hidden" $chk2DPie></INPUT>
            <LABEL FOR="2dpie">
              <IMG SRC="/icons/chart_2Dpie.png" TITLE="2D Pie">
            </LABEL>
          </TD>
          <TD ALIGN="center" VALIGN="top" WIDTH="25%" HEIGHT="75px">
            <INPUT TYPE="radio" NAME="chartType" ID="3dpie" VALUE="3DPie" CLASS="input-hidden" $chk3DPie></INPUT>
            <LABEL FOR="3dpie">
              <IMG SRC="/icons/chart_3Dpie.png" TITLE="3D Pie">
            </LABEL>
          </TD>
          <TD ALIGN="center" VALIGN="top" WIDTH="25%" HEIGHT="75px">
            <INPUT TYPE="radio" NAME="chartType" ID="2ddonut" VALUE="2DDonut" CLASS="input-hidden" $chk2DDonut></INPUT>
            <LABEL FOR="2ddonut">
              <IMG SRC="/icons/chart_2Ddonut.png" TITLE="2D Donut">
            </LABEL>
          </TD>
          <TD ALIGN="center" VALIGN="top" WIDTH="25%" HEIGHT="75px">
            <INPUT TYPE="radio" NAME="chartType" ID="3ddonut" VALUE="3DDonut" CLASS="input-hidden" $chk3DDonut></INPUT>
            <LABEL FOR="3ddonut">
              <IMG SRC="/icons/chart_3Ddonut.png" TITLE="3D Donut">
            </LABEL>
          </TD>
        </TR>
        <TR>
          <TD ALIGN="center" VALIGN="top" WIDTH="25%" HEIGHT="75px">
            <INPUT TYPE="radio" NAME="chartType" ID="radar" VALUE="Radar" CLASS="input-hidden" $chkRadar></INPUT>
            <LABEL FOR="radar">
              <IMG SRC="/icons/chart_radar.png" TITLE="Radar">
            </LABEL>
          </TD>
          <TD ALIGN="center" VALIGN="top" WIDTH="25%" HEIGHT="75px">
            <INPUT TYPE="radio" NAME="chartType" ID="treemap" VALUE="TreeMap" CLASS="input-hidden" $chkTreeMap></INPUT>
            <LABEL FOR="treemap">
              <IMG SRC="/icons/chart_heatmap.png" TITLE="Tree Map">
            </LABEL>
          </TD>
          <TD ALIGN="center" VALIGN="top" WIDTH="25%" HEIGHT="75px">
            <INPUT TYPE="radio" NAME="chartType" ID="waterfall" VALUE="Waterfall" CLASS="input-hidden" $chkWaterfall></INPUT>
            <LABEL FOR="waterfall">
              <IMG SRC="/icons/chart_waterfall.png" TITLE="Waterfall">
            </LABEL>
          </TD>
          <TD ALIGN="center" VALIGN="top" WIDTH="25%" HEIGHT="75px">
            <INPUT TYPE="radio" NAME="chartType" ID="bubble" VALUE="Bubble" CLASS="input-hidden" $chkBubble></INPUT>
            <LABEL FOR="bubble">
              <IMG SRC="/icons/chart_bubble.png" TITLE="Bubble">
            </LABEL>
          </TD>
        </TR>
        <TR>
          <TD ALIGN="center" VALIGN="top" WIDTH="25%" HEIGHT="75px">
            <INPUT TYPE="radio" NAME="chartType" ID="funnel" VALUE="Funnel" CLASS="input-hidden" $chkFunnel></INPUT>
            <LABEL FOR="funnel">
              <IMG SRC="/icons/chart_funnel.png" TITLE="Funnel">
            </LABEL>
          </TD>
          <TD ALIGN="center" VALIGN="top" WIDTH="25%" HEIGHT="75px">
            <INPUT TYPE="radio" NAME="chartType" ID="columnline" VALUE="ColumnLine" CLASS="input-hidden" $chkColumnLine></INPUT>
            <LABEL FOR="columnline">
              <IMG SRC="/icons/chart_columnline.png" TITLE="Combined">
            </LABEL>
          </TD>
          <TD ALIGN="center" VALIGN="top" WIDTH="25%" HEIGHT="75px">
            <INPUT TYPE="radio" NAME="chartType" ID="dualy" VALUE="DualY" CLASS="input-hidden" $chkDualY></INPUT>
            <LABEL FOR="dualy">
              <IMG SRC="/icons/chart_dualy.png" TITLE="Dual Y">
            </LABEL>
          </TD>
        </TR>
      </TABLE>
      </FORM>

    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="submitForm()"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML

#EOF
