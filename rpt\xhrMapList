#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::Reports;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $rptID = $q->param('rptID');
  $visID = $q->param('v');
  $mapType = $q->param('m');

  $db = KAPutil_connect_to_database();

  #make sure we have edit privs for this report
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this report.");
  }

  #get the main map display details from the database
  $query = "SELECT design FROM visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($design) = $dbOutput->fetchrow_array;


  #########################################################################
  #
  #if we're being called to save the user's selected map type
  #

  if (defined($mapType))
  {

    #save the new map type
    $design =~ m/^(.*),type:.*?,(.*)$/;
    $design = $1 . ",type:$mapType," . $2;
    $q_design = $db->quote($design);

    $query = "UPDATE visuals SET design = $q_design WHERE ID=$visID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    $cubeName = cube_id_to_name($db, $rptID);
    $dsID = cube_get_ds_id($db, $rptID);
    $dsName = ds_id_to_name($db, $dsID);

    utils_audit($db, $userID, "Changed map type", $dsID, $rptID, 0);

    $activity = "$first $last changed map type for $cubeName in $dsName";
    utils_slack($activity);
  }


  ########################################################################
  #
  # Everything after this point is called to display the map selection dialog
  #

  $design =~ m/,type:(.*?),/;
  $map = $1;

  print <<END_HTML;
<SCRIPT>
function submitForm()
{
  let map = document.getElementById('map').value;

  let url = "xhrMapList?rptID=$rptID&v=$visID&m=" + map;

  \$.get(url, function(data, status)
  {
    location.href = "display.cld?rpt=$rptID&v=$visID";
  });
}
</SCRIPT>

<DIV CLASS="modal-dialog" STYLE="overflow-y: initial !important;">
  <DIV CLASS="modal-content">
    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Map Type</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">

      <FORM>
      <TABLE CLASS="mx-auto">
        <TR>
          <TD>
            <SELECT ID="cont" CLASS="form-select" MULTIPLE SIZE=10>
              <OPTION SELECTED VALUE="na">North America</OPTION>
            </SELECT>
          </TD>

          <TD>&nbsp;&nbsp;</TD>

          <TD>
            <SELECT ID="map" CLASS="form-select" MULTIPLE SIZE=10>
              <OPTION VALUE="northamericawocentral">North America</OPTION>
              <OPTION SELECTED VALUE="usa">USA (States)</OPTION>
              <OPTION VALUE="usaregion">USA (All Regions)</OPTION>
              <OPTION VALUE="usacentralregion">USA Central Region</OPTION>
              <OPTION VALUE="usanortheastregion">USA Northeast Region</OPTION>
              <OPTION VALUE="usasoutheastregion">USA Southeast Region</OPTION>
              <OPTION VALUE="usanorthwestregion">USA Northwest Region</OPTION>
              <OPTION VALUE="usasouthwestregion">USA Southwest Region</OPTION>
              <OPTION VALUE="usadma">USA (Nielsen DMA)</OPTION>
              <OPTION VALUE="eastnorthcentraldma">East North Central DMA</OPTION>
              <OPTION VALUE="eastsouthcentraldma">East South Central DMA</OPTION>
              <OPTION VALUE="middleatlanticdma">Middle Atlantic DMA</OPTION>
              <OPTION VALUE="mountaindma">Mountain DMA</OPTION>
              <OPTION VALUE="newenglanddma">New England DMA</OPTION>
              <OPTION VALUE="pacificdma">Pacific DMA</OPTION>
              <OPTION VALUE="southatlanticdma">South Atlantic DMA</OPTION>
              <OPTION VALUE="westnorthcentraldma">West North Central DMA</OPTION>
              <OPTION VALUE="westsouthcentraldma">West South Central DMA</OPTION>
            </SELECT>
            <SCRIPT>
              \$("select#map").val("$map");
            </SCRIPT>
          </TD>
        </TR>
      </TABLE>
      </FORM>

    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="submitForm()"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML

#EOF
