#!/usr/bin/perl

use Text::CSV;

#Import user access information for Beacon United analysts

  #instantiate our CSV object
  $csv = Text::CSV->new( {binary => 1} );

  open(INPUT, "$ARGV[0]");
  open(OUTPUT, ">$ARGV[1]");

  #parse header line
  $line = <INPUT>;
  $csv->parse($line);
  @columns = $csv->fields();

  #transform the headers into something Ko<PERSON> can work with
  $columns[0] = "PSEG:UserName";
  $columns[1] = "PSEG:Broker";
  $columns[2] = "PSEG:Category_Alias";
  $columns[3] = "Geography";
  $columns[4] = "PSEG:Manufacturer";
  $columns[5] = "PSEG:UserType";
  $columns[6] = "Product";
  $columns[7] = "Time";
  $columns[8] = "Dummy Measure";

  $date = `date "+%m/%d/%Y"`;
  chomp($date);

  #output the headers
  $csv->combine(@columns);
  $line = $csv->string();
  print OUTPUT "$line\n";

  while ($line = <INPUT>)
  {
    $csv->parse($line);
    @columns = $csv->fields();

    $columns[6] = $columns[1] . "-" . $columns[2] . "-" . $columns[3];
    $columns[7] = "1 WE $date";
    $columns[8] = "0";

    $csv->combine(@columns);
    $line = $csv->string();

    print OUTPUT "$line\n";
  }

  close(INPUT);
  close(OUTPUT);
