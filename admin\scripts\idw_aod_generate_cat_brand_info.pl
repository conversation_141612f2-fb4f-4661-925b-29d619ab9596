#!/usr/bin/perl

use lib "/opt/apache/app/";

use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



  open(INPUT, "/opt/apache/htdocs/prep/Beacon_Product_Ref_prdc_ref_1611158.txt");

  $line = <INPUT>;

  while ($line = <INPUT>)
  {
    @cols = split(/\|/, $line);

    $superCat = $cols[2];
    $cat = $cols[3];
    $manufacturer = $cols[11];
    $brand = $cols[9];

    $key = "$superCat|$cat|$manufacturer|$brand";
    $results{$key}++;
  }


  print("Super Category,Category,Manufacturer,Brand High,SKU Count\n");

  foreach $key (keys %results)
  {
    @cols = split(/\|/, $key);

    print("\"$cols[0]\",\"$cols[1]\",\"$cols[2]\",\"$cols[3]\",$results{$key}\n");
  }

#EOF
