
package Lib::DataSel;

use lib "/opt/apache/app/";

use Exporter;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::KAPSegmentation;


our @ISA = ('Exporter');

our @EXPORT = qw(
    &datasel_move_items_top
    &datasel_move_items_bottom
    &datasel_move_items_up
    &datasel_move_items_down
    &datasel_replace_item
    &datasel_delete_items
    &datasel_uniquify_items
    &datasel_add_item
    &datasel_get_items_csv
    &datasel_get_script_lines
    &datasel_get_items_json
    &datasel_get_script_json
    &datasel_expand_script
    &datasel_transfer_script
    &datasel_get_selected_base_items
    &datasel_get_dimension_items
    &datasel_get_tgpm_value);




#-------------------------------------------------------------------------
#
# Handle a database error of some kind during a datasel function call
#

sub datasel_db_err
{
  my ($date);

  my ($db, $status, $text) = @_;

  if (!defined($status))
  {
    $date = localtime();
    print STDERR "$date: $text\n";
    if ($db->errstr =~ m/^MySQL server has gone away/)
    {
      die("Lost connection to database, terminating");
    }
  }
}



#-------------------------------------------------------------------------
#
# Internal function used to unique-ify an array of data selection elements
#

sub uniq
{
  my (%seen);

  return grep { !$seen{$_}++ } @_;
}



#-------------------------------------------------------------------------
#
# Calculate the set intersection of two arrays (the list of items that are
# present in both arrays), and return an array of the intersected set
#

sub datasel_intersect_arrays
{
  my ($itemID, $match, $levelID);
  my (@array1, @array2, @intersection, @union, @preserve);
  my (%isect, %union);

  my ($array1ref, $array2ref) = @_;

  #dereference the arrays
  @array1 = @{$array1ref};
  @array2 = @{$array2ref};

  #if the intersecting array contains SHS levels, then we want to intersect
  #them with other SHS levels
  if ($array1[0] =~ m/^SHS_/)
  {
    undef(@intersection);

    #we're going to cycle through each item in the selection list, and if its
    #a hierarchy level (SHS), we're then going to cycle through each SHS tag
    #in the intersection selection. If there's a match, we're going to keep
    #the SHS level from the original list, otherwise we're going to discard it.
    #NB: we're doing matching against higher levels, e.g. if an existing
    #    selection is SHS_1_3_4, and SHS_1_3 is in the intersecting array,
    #    we're going to keep SHS_1_3_4. This matches functionality in legacy
    #    tools.
    foreach $itemID (@array2)
    {

      #if the item isn't a hierarchy level, keep it
      if (!($itemID =~ m/^SHS_/))
      {
        push(@intersection, $itemID);
        next;
      }

      #run through every hierarchy level in the intersecting list, and see if
      #there's a match
      $match = 0;
      foreach $levelID (@array1)
      {
        if ($itemID =~ m/^$levelID/)
        {
          $match = 1;
        }
      }

      #if there was a match, keep the item
      if ($match == 1)
      {
        push(@intersection, $itemID);
      }
    }

    return(@intersection);
  }

  #we don't want to do anything to segs and aggs (which will be at the top of
  #the array), so go ahead and store them separately
  undef(@preserve);
  foreach $itemID (@array2)
  {
    if ($itemID =~ m/^SMT_/)
    {
      push(@preserve, $itemID);
    }
    if ($itemID =~ m/^AGG_/)
    {
      push(@preserve, $itemID);
    }
    if ($itemID =~ m/^SHS_/)
    {
      push(@preserve, $itemID);
    }
  }

  undef(%isect);
  undef(%union);
  foreach $itemID (@array1, @array2)
  {
    $union{$itemID}++ && $isect{$itemID}++;
  }
  @intersection = keys %isect;

  #put back the segs and aggs we preserved before the intersection
  push(@preserve, @intersection);
  @intersection = @preserve;

  return(@intersection);
}



#-------------------------------------------------------------------------
#
# Move the specified items to the top of the supplied list of items
#

sub datasel_move_items_top
{
  my (@lines, @items, @newItems);
  my ($item, $index, $line, $newItemStr, $idx);

  my ($linesStr, $itemStr) = @_;

  #split the list into individual items
  @lines = split(',', $linesStr);

  #split the items to be moved up into an array
  @items = split(',', $itemStr);

  #assume unspecified method is manual
  $idx = 0;
  foreach $item (@items)
  {
    if (!($item =~ m/:/))
    {
      $items[$idx] = "M:" . $item;
    }
    $idx++;
  }

  #build up a new array that starts with the items being moved to the top
  undef(@newItems);
  push(@newItems, @items);

  #run through the old list, and remove any items that were moved up
  foreach $item (@items)
  {
    $index = 0;
    foreach $line (@lines)
    {
      #we found our match
      if ($item eq $line)
      {
        splice(@lines, $index, 1);
      }
      $index++;
    }
  }

  #append the old list, now missing the items moved to the top, to the new
  push(@newItems, @lines);

  #rejoin the array into a CSV string
  $newItemStr = join(',', @newItems);

  return($newItemStr);
}



#-------------------------------------------------------------------------
#
# Move the specified items to the bottom of the supplied list of items
#

sub datasel_move_items_bottom
{
  my (@lines, @items, @newItems);
  my ($item, $index, $line, $newItemStr, $idx);

  my ($linesStr, $itemStr) = @_;

  #split the list into individual items
  @lines = split(',', $linesStr);

  #split the items to be moved down into an array
  @items = split(',', $itemStr);

  #assume unspecified method is manual
  $idx = 0;
  foreach $item (@items)
  {
    if (!($item =~ m/:/))
    {
      $items[$idx] = "M:" . $item;
    }
    $idx++;
  }

  #create array containing all items except those being moved down
  undef(@newItems);
  push(@newItems, @lines);
  foreach $item (@items)
  {
    $index = 0;
    foreach $line (@newItems)
    {
      #we found our match
      if ($item eq $line)
      {
        splice(@newItems, $index, 1);
      }
      $index++;
    }
  }

  #append the bottom items to the new list
  push(@newItems, @items);

  #rejoin the array into a CSV string
  $newItemStr = join(',', @newItems);

  return($newItemStr);
}



#-------------------------------------------------------------------------
#
# Move the specified items up 1 slot in the supplied list of items
#

sub datasel_move_items_up
{
  my (@lines, @items, @newItems);
  my ($item, $index, $count, $line, $newItemStr, $upItem, $downItem, $idx);

  my ($linesStr, $itemStr) = @_;

  #split the list into individual items
  @lines = split(',', $linesStr);

  #split the items to be moved up into an array
  @items = split(',', $itemStr);

  #assume unspecified method is manual
  $idx = 0;
  foreach $item (@items)
  {
    if (!($item =~ m/:/))
    {
      $items[$idx] = "M:" . $item;
    }
    $idx++;
  }

  #foreach item we're moving up (blocks of items "bubble" up to their
  #correct locations through this iterative process)
  foreach $item (@items)
  {

    #cycle through the item array until we find the item we're moving up
    $count = 0;
    foreach $line (@lines)
    {
      if ($line eq $item)
      {
        $index = $count;
      }

      $count++;
    }

    #if the item isn't already at the top of the list, move it up
    if ($index > 0)
    {
      $upItem = $lines[$index];
      $downItem = $lines[$index-1];

      $lines[$index] = $downItem;
      $lines[$index-1] = $upItem;
    }
  }

  #rejoin the array into a CSV string
  $newItemStr = join(',', @lines);

  return($newItemStr);
}



#-------------------------------------------------------------------------
#
# Move the specified items down 1 slot in the supplied list of items
#

sub datasel_move_items_down
{
  my (@lines, @items, @newItems);
  my ($item, $index, $count, $line, $newItemStr, $upItem, $downItem, $idx);
  my ($arrayLength);

  my ($linesStr, $itemStr) = @_;

  #split the list into individual items
  @lines = split(',', $linesStr);

  #split the items to be moved down into an array, and reverse their order
  @items = split(',', $itemStr);
  @items = reverse(@items);

  #assume unspecified method is manual
  $idx = 0;
  foreach $item (@items)
  {
    if (!($item =~ m/:/))
    {
      $items[$idx] = "M:" . $item;
    }
    $idx++;
  }

  #foreach item we're moving down (blocks of items "bubble" down to their
  #correct locations through this iterative process)
  foreach $item (@items)
  {

    #cycle through the item array until we find the item we're moving down
    $count = 0;
    foreach $line (@lines)
    {
      if ($line eq $item)
      {
        $index = $count;
      }

      $count++;
    }

    #if the item isn't already at the bottom of the list, move it down
    $arrayLength = @lines;
    if ($index < $arrayLength)
    {
      $upItem = $lines[$index+1];
      $downItem = $lines[$index];

      $lines[$index] = $upItem;
      $lines[$index+1] = $downItem;
    }
  }

  #rejoin the array into a CSV string
  $newItemStr = join(',', @lines);

  return($newItemStr);
}



#-------------------------------------------------------------------------
#
# Replace the specified item with a new one (used by "modify" operation)
#

sub datasel_replace_item
{
  my (@lines, @items, @newItems, @oldLines, @newLines);
  my ($item, $index, $count, $line, $newItemStr);

  my ($linesStr, $itemStr, $newItem) = @_;

  #split the list into individual items
  @oldLines = split(',', $linesStr);

  #assume unspecified method is manual
  if (!($newItem =~ m/:/))
  {
    $newItem = "M:" . $newItem;
  }

  #run through the list of selected items, replacing the specified line
  foreach $line (@oldLines)
  {
    if ($line eq $itemStr)
    {
      push(@newLines, $newItem);
    }
    else
    {
      push(@newLines, $line);
    }
  }

  #rejoin the array into a CSV string
  $newItemStr = join(',', @newLines);

  return($newItemStr);
}



#-------------------------------------------------------------------------
#
# Remove the specified items from a data selection list
#

sub datasel_delete_items
{
  my (@lines, @items, @newItems, @oldLines, @delItems, @newLines);
  my ($item, $index, $count, $line, $newItemStr, $idx, $keep, $delItem);

  my ($linesStr, $itemStr) = @_;

  #split the list into individual items
  @oldLines = split(',', $linesStr);
  @delItems = split(',', $itemStr);

  #assume unspecified method is manual
  $idx = 0;
  foreach $item (@delItems)
  {
    if (!($item =~ m/:/))
    {
      $delItems[$idx] = "M:" . $item;
    }
    $idx++;
  }

  #run through the list of selected items, keeping ones that aren't in the
  #list of items to be deleted
  foreach $line (@oldLines)
  {
    $keep = 1;

    #see if the item ID is in the list to be deleted
    foreach $delItem (@delItems)
    {
      if ($delItem eq $line)
      {
        $keep = 0;
      }
    }

    if ($keep)
    {
      push(@newLines, $line);
    }
  }

  #rejoin the array into a CSV string
  $newItemStr = join(',', @newLines);

  return($newItemStr);
}



#-------------------------------------------------------------------------
#
# Make sure every step in the supplied data selection script is unique. On
# occasion, users have been known to do something crazy like make the same
# selection multiple times. They don't mean to, and it messes up both their
# numbers and things like the grid control where we use steps as unique IDs
#

sub datasel_uniquify_items
{
  my ($script);
  my (@steps);
  my (%seen);

  my ($srcScript) = @_;

  @steps = split(',', $srcScript);

  #strip any duplicate selections out of the array of selection steps
  @steps = grep { !$seen{$_}++ } @steps;

  $script = join(',', @steps);

  return($script);
}



#-------------------------------------------------------------------------
#
# Add the specified item to the specified data selection script
#

sub datasel_add_item
{
  my ($query, $dbOutput, $q_selection, $status);
  my ($segID, $scriptColName, $oldScript, $newScript, $q_newScript);

  #grab our arguments - we're passing them in a hash since there's so many of
  #them that they make traditional parameters a little unwieldy
  my ($dataSelOpts, @segmentIDs) = @_;
  my $db = $dataSelOpts->{'db'};
  my $dim = $dataSelOpts->{'dim'};
  my $dbName = $dataSelOpts->{'dbName'};
  my $structType= $dataSelOpts->{'structType'};
  my $structID = $dataSelOpts->{'structID'};
  my $selMethod = $dataSelOpts->{'selMethod'};
  my $selection = $dataSelOpts->{'selection'};
  my $modifyItem = $dataSelOpts->{'modifyItem'};
  my $segHierLevel = $dataSelOpts->{'segHierLevel'};
  my $segmentationID = $dataSelOpts->{'segmentationID'};
  my $selOp = $dataSelOpts->{'selOp'};
  my $selDuration = $dataSelOpts->{'selDuration'};
  my $selType = $dataSelOpts->{'selType'};
  my $selEnding = $dataSelOpts->{'selEnding'};
  my $selStarting = $dataSelOpts->{'selStarting'};
  my $matchType = $dataSelOpts->{'matchType'};


  #set our script column name depending on dimension and structure type
  if ($structType eq "a")
  {
    $scriptColName = "addScript";
  }
  elsif ($structType eq "l")
  {
    $scriptColName = "script";
  }
  elsif ($structType eq "c")
  {
    if ($dim eq "p")
    {
      $scriptColName = "scriptProducts";
    }
    elsif ($dim eq "g")
    {
      $scriptColName = "scriptGeographies";
    }
    elsif ($dim eq "t")
    {
      $scriptColName = "scriptTimeperiods";
    }
    elsif ($dim eq "m")
    {
      $scriptColName = "scriptMeasures";
    }
  }

  #if the user selected a hierarchy, append it to script
  if ($selMethod eq "H")
  {

    #append the selected segmentations to the selection string
    foreach $segID (@segmentIDs)
    {
      $selection .= " $segID";
    }

    $query = "SELECT $scriptColName FROM $dbName WHERE ID=$structID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    datasel_db_err($db, $status, $query);
    ($oldScript) = $dbOutput->fetchrow_array;

    if (length($modifyItem) > 1)
    {
      $newScript = datasel_replace_item($oldScript, $modifyItem, $selection);
    }
    elsif (length($oldScript) > 1)
    {
      $newScript = "$oldScript,$selection";
    }
    else
    {
      $newScript = $selection;
    }

    $newScript = datasel_uniquify_items($newScript);
    $q_newScript = $db->quote($newScript);
    $query = "UPDATE $dbName SET $scriptColName=$q_newScript WHERE ID=$structID";
    $status = $db->do($query);
    datasel_db_err($db, $status, $query);
  }

  elsif ($selMethod eq "SH")
  {

    #append the selected seg hier level to the selection string
    $selection .= " $segHierLevel";

    #append the selected segmentation to the selection string
    $selection .= " $segmentationID";

    #append the selected segmentations to the selection string
    foreach $segID (@segmentIDs)
    {
      $selection .= " $segID";
    }

    if ($selOp eq "intersect")
    {
      $selection .= " INTERSECT";
    }

    #add the script line to the script
    $query = "SELECT $scriptColName FROM $dbName WHERE ID=$structID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    datasel_db_err($db, $status, $query);
    ($oldScript) = $dbOutput->fetchrow_array;

    if (length($modifyItem) > 1)
    {
      $newScript = datasel_replace_item($oldScript, $modifyItem, $selection);
    }
    elsif (length($oldScript) > 2)
    {
      $newScript = "$oldScript,$selection";
    }
    else
    {
      $newScript = $selection;
    }

    $newScript = datasel_uniquify_items($newScript);
    $q_newScript = $db->quote($newScript);
    $query = "UPDATE $dbName SET $scriptColName=$q_newScript WHERE ID=$structID";
    $status = $db->do($query);
    datasel_db_err($db, $status, $query);
  }

  #else if the user wants a "most recent" time period selection
  elsif ($selMethod eq "RECENT")
  {

    #append the selected top or bottom parameters to the selection string
    $selection = "RECENT:$selection $selDuration $selType $selEnding";
    $selection =~ s/ +/ /;
    $query = "SELECT $scriptColName FROM $dbName WHERE ID=$structID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    datasel_db_err($db, $status, $query);
    ($oldScript) = $dbOutput->fetchrow_array;

    if (length($modifyItem) > 1)
    {
      $newScript = datasel_replace_item($oldScript, $modifyItem, $selection);
    }
    elsif (length($oldScript) > 1)
    {
      $newScript = "$oldScript,$selection";
    }
    else
    {
      $newScript = $selection;
    }

    $newScript = datasel_uniquify_items($newScript);
    $q_newScript = $db->quote($newScript);
    $query = "UPDATE $dbName SET $scriptColName=$q_newScript WHERE ID=$structID";
    $status = $db->do($query);
    datasel_db_err($db, $status, $query);
  }

  #else if the user wants a range time period selection
  elsif ($selMethod eq "RANGE")
  {

    #append the selected top or bottom parameters to the selection string
    $selection = "RANGE:$selDuration $selType $selStarting $selEnding";
    $selection =~ s/ +/ /;
    $query = "SELECT $scriptColName FROM $dbName WHERE ID=$structID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    datasel_db_err($db, $status, $query);
    ($oldScript) = $dbOutput->fetchrow_array;

    if (length($modifyItem) > 1)
    {
      $newScript = datasel_replace_item($oldScript, $modifyItem, $selection);
    }
    elsif (length($oldScript) > 1)
    {
      $newScript = "$oldScript,$selection";
    }
    else
    {
      $newScript = $selection;
    }

    $newScript = datasel_uniquify_items($newScript);
    $q_newScript = $db->quote($newScript);
    $query = "UPDATE $dbName SET $scriptColName=$q_newScript WHERE ID=$structID";
    $status = $db->do($query);
    datasel_db_err($db, $status, $query);
  }

  #else if the user wants a match
  elsif ($selMethod eq "MATCH")
  {

    #append the selected match parameter to the selection string
    $selection = "MATCH:$matchType $selection";
    $query = "SELECT $scriptColName FROM $dbName WHERE ID=$structID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    datasel_db_err($db, $status, $query);
    ($oldScript) = $dbOutput->fetchrow_array;

    if (length($modifyItem) > 1)
    {
      $newScript = datasel_replace_item($oldScript, $modifyItem, $selection);
    }
    elsif (length($oldScript) > 2)
    {
      $newScript = "$oldScript,$selection";
    }
    else
    {
      $newScript = $selection;
    }

    $newScript = datasel_uniquify_items($newScript);
    $q_newScript = $db->quote($newScript);
    $query = "UPDATE $dbName SET $scriptColName=$q_newScript WHERE ID=$structID";
    $status = $db->do($query);
    datasel_db_err($db, $status, $query);
  }

  #else we got a full set of data from Manual method
  else
  {
    $q_selection = $db->quote($selection);
    $query = "UPDATE $dbName SET $scriptColName=$q_selection WHERE ID=$structID";
    $status = $db->do($query);
    datasel_db_err($db, $status, $query);
  }
}



#-------------------------------------------------------------------------
#
# Get the list of all data selection script lines, in human-readable form,
# for the specified dimension of the specified data cube. Return in array
# form.
#

sub datasel_get_script_lines
{
  my ($colName, $query, $dbOutput, $scriptString, $dsSchema, $ending, $line);
  my ($starting, $selDuration, $selType, $idx, $matchType, $matchChars);
  my ($measureID, $operator, $status, $dsID, $segmentationID, $segmentID);
  my ($segmentationName, $segmentName, $segHierID, $segHierLevelID);
  my ($segHierLevelName, $segHierName, $segmentStr, $value, $measureName);
  my ($secondaryID, $secondaryName, $tertiaryID, $tertiaryName);
  my ($secondaryDim, $secondaryDimName, $tertiaryDim, $tertiaryDimName);
  my ($selLimit, $selPeriods, $segmentIDStr, $tmp, $selOp);
  my (%itemNameHash, %dateTypeName);
  my (@scriptLines, @segmentIDs);

  my ($db, $cubeID, $dim) = @_;

  #get our dimension's cube definition column name
  if ($dim eq "p")
  {
    $colName = "scriptProducts";
  }
  elsif ($dim eq "g")
  {
    $colName = "scriptGeographies";
  }
  elsif ($dim eq "t")
  {
    $colName = "scriptTimeperiods";
  }
  elsif ($dim eq "m")
  {
    $colName = "scriptMeasures";
  }

  #fetch the data source ID and script string from the cubes table
  $query = "SELECT dsID, $colName FROM cubes WHERE ID=$cubeID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  datasel_db_err($db, $status, $query);
  ($dsID, $scriptString) = $dbOutput->fetchrow_array;

  #hash that we use to convert numerical date types to human-readable string
  undef(%dateTypeName);
  %dateTypeName = (
    10 => "Year",
    20 => "Month",
    30 => "Week",
    40 => "Day"
  );

  #build schema name
  $dsSchema = "datasource_" . $dsID;

  #split the script string into individual steps
  @scriptLines = split(/,/, $scriptString);

  #get hash of item dimension names and IDs
  %itemNameHash = dsr_get_item_name_hash($db, $dsSchema, $dim);

  #transform each line of the script into something human-readable
  $idx = 0;
  while (defined($scriptLines[$idx]))
  {

    #
    #if it's a Manual add
    #
    if ($scriptLines[$idx] =~ m/^M:(.*)/)
    {
      if ($1 eq "ALL")
      {
        $line = "All Items";
      }
      else
      {
        $line = $itemNameHash{$1};
      }

      $scriptLines[$idx] = $line;
    }

    #
    #handle a hierarchy selection
    #
    elsif ($scriptLines[$idx] =~ m/^H:(.*?) (.*?)$/)
    {
      $segmentationID = $1;
      $segmentIDStr = $2;

      @segmentIDs = split(' ', $segmentIDStr);

      $line = "Items from the ";
      foreach $tmp (@segmentIDs)
      {
        $tmp = "SMT_" . $tmp;
        $line .= $itemNameHash{$tmp} . ", ";
      }
      chop($line);  chop($line);

      $segmentationName = KAPsegmentation_seg_ID_to_name($db, $dsSchema, $dim, $segmentationID);
      $line .= " segments of $segmentationName";
      $scriptLines[$idx] = $line;
    }

    #
    #handle a segmentation hierarchy selection
    #
    elsif (($scriptLines[$idx] =~ m/^SH:(.*?) (.*?) (.*?) (.*) (INTERSECT)/) ||
           ($scriptLines[$idx] =~ m/^SH:(.*?) (.*?) (.*?) (.*)/))
    {
      $segHierID = $1;
      $segHierLevelID = $2;
      $segmentationID = $3;
      $segmentStr = $4;
      $selOp = $5;

      @segmentIDs = split(' ', $segmentStr);

      $segHierName = KAPsegmentation_hierarchy_ID_to_name($db, $dsSchema, $dim, $segHierID);
      if (length($segHierName) < 1)
      {
        $scriptLines[$idx] = "";
        $idx++;
        next;
      }
      $segHierLevelName = KAPsegmentation_seg_ID_to_name($db, $dsSchema, $dim, $segHierLevelID);
      $segmentationName = KAPsegmentation_seg_ID_to_name($db, $dsSchema, $dim, $segmentationID);
      if ($selOp eq "INTERSECT")
      {
        $line = "Intersected with elements from the $segHierName hierarchy at the $segHierLevelName level where $segmentationName is ";
      }
      else
      {
        $line = "Elements from the $segHierName hierarchy at the $segHierLevelName level where $segmentationName is ";
      }

      foreach $segmentID (@segmentIDs)
      {
        $segmentName = KAPsegmentation_segment_id_to_name($db, $dsSchema, $dim, $segmentID);
        $line .= "$segmentName, ";
      }
      chop($line); chop($line);

      $scriptLines[$idx] = $line;
    }

    #
    #handle a "most recent" selection
    #
    elsif ($scriptLines[$idx] =~ m/^RECENT:(.*?) (.*?) (.*?) (.*?)$/)
    {
      $selPeriods = $1;
      $selDuration = $2;
      $selType = $3;
      $ending = $4;

      #convert ending to something (more) human-readable
      if ($ending eq "1period")
      {
        $ending = "1 period ago";
      }
      elsif ($ending eq "1year")
      {
        $ending = "1 year ago";
      }

      $line = "The $selPeriods most recent $selDuration $dateTypeName{$selType} time periods ending $ending";
      $scriptLines[$idx] = $line;
    }

    #
    #handle a range selection
    #
    elsif ($scriptLines[$idx] =~ m/^RANGE:(.*?) (.*?) (.*?) (.*?)$/)
    {
      $selDuration = $1;
      $selType = $2;
      $starting = $3;
      $ending = $4;

      #convert time IDs to something human-readable
      $starting = KAPutil_get_item_ID_name($db, $dsSchema, "t", $starting);

      if ($ending ne "now")
      {
        $ending = KAPutil_get_item_ID_name($db, $dsSchema, "t", $ending);
      }

      $line = "The range of $selDuration $dateTypeName{$selType} time periods between $starting and $ending";
      $scriptLines[$idx] = $line;
    }

    #
    #handle a match selection
    #
    elsif ($scriptLines[$idx] =~ m/^MATCH:(.*?) (.*)$/)
    {
      $matchType = $1;
      $matchChars = $2;

      #convert match type to something human readable
      if ($matchType eq "start")
      {
        $matchType = "beginning with";
      }
      elsif ($matchType eq "contain")
      {
        $matchType = "containing";
      }
      elsif ($matchType eq "end")
      {
        $matchType = "ending with";
      }
      elsif ($matchType eq "exact")
      {
        $matchType = "exactly matching";
      }

      $line = "Items $matchType $matchChars";
      $scriptLines[$idx] = $line;
    }

    $idx++;
  }

  return(@scriptLines);
}



#-------------------------------------------------------------------------
#
# Get the list of all selected items for the specified component. "Items"
# can refer to a single base item product, segment, list, aggregate, etc.
# These get pulled from the appropriate column in the "cubes" table. The
# values in that column are generated by executing the script stored in the
# "scriptProducts" column.
# Return JSON data block containing ID, name, and type
#

sub datasel_get_items_json
{
  my ($JSONblock, $query, $dbOutput, $status, $itemString, $dsSchema);
  my ($itemID, $colName, $id, $product, $type, $dsID, $itemType);
  my (%itemNameHash);
  my (@items);

  my ($db, $rptID, $dim) = @_;

  undef($JSONblock);

  #get our dimension's DB name
  if ($dim eq "p")
  {
    $colName = "products";
  }
  elsif ($dim eq "g")
  {
    $colName = "geographies";
  }
  elsif ($dim eq "t")
  {
    $colName = "timeperiods";
  }
  elsif ($dim eq "m")
  {
    $colName = "measures";
  }

  #fetch the product string from the cubes table
  $query = "SELECT dsID, $colName FROM cubes WHERE ID=$rptID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  datasel_db_err($db, $status, $query);
  ($dsID, $itemString) = $dbOutput->fetchrow_array;

  #build schema name to contain cube
  $dsSchema = "datasource_" . $dsID;

  #split the product string into individual items
  @items = split(/,/, $itemString);

  #get hash of product dimension names and IDs
  %itemNameHash = dsr_get_item_name_hash($db, $dsSchema, $dim);

  #build up one line of the JSON data block for each selected product
  foreach $itemID (@items)
  {
    #handle base items
    $product = $itemNameHash{$itemID};
    $product =~ s/\"/\\"/g;
    $product =~ s/\'/\\'/g;
    $itemType = KAPutil_get_item_type($itemID);
    if (defined($product))
    {
      $JSONblock .= "{id: '$itemID', product: '$product', type: '$itemType'},\n";
      next;
    }
  }

  #remove unneeded last comma and newline
  chop($JSONblock); chop($JSONblock);

  return($JSONblock);
}



#-------------------------------------------------------------------------
#
# Return a JSON data block of the human-readable datasel script lines for
# the selected dimension of the selected data cube, list, or aggregate.
#

sub datasel_get_script_json
{
  my ($JSONblock, $query, $dbOutput, $status, $scriptString, $dsSchema);
  my ($step, $colName, $id, $product, $type, $ending, $itemName, $selPeriods);
  my ($selDuration, $selType, $measureID, $measureName, $selLimit, $starting);
  my ($secondaryDim, $tertiaryDim, $secondaryDimName, $tertiaryDimName);
  my ($dimPrefix, $dbName, $itemID, $itemType, $segmentationID, $segmentID);
  my ($segmentationName, $segmentName, $segHierID, $segHierLevelID);
  my ($segmentStr, $segHierName, $operator, $value, $segHierLevelName);
  my ($secondaryID, $tertiaryID, $secondaryName, $tertiaryName, $tmp);
  my ($matchType, $matchChars, $idx, $segmentIDStr, $selOp);
  my (%itemNameHash, %dateTypeName);
  my (@scriptSteps, @segmentIDs, @scriptLines);

  my ($db, $structID, $structType, $dim, $dsID) = @_;

  undef($JSONblock);

  #get our dimension's script column name (if we're dealing with a cube), and
  #DSR structure prefix (if we're dealing with a list or aggregate)
  if ($dim eq "p")
  {
    $colName = "scriptProducts";
    $dimPrefix = "product_";
  }
  elsif ($dim eq "g")
  {
    $colName = "scriptGeographies";
    $dimPrefix = "geography_";
  }
  elsif ($dim eq "t")
  {
    $colName = "scriptTimeperiods";
    $dimPrefix = "time_";
  }
  elsif ($dim eq "m")
  {
    $colName = "scriptMeasures";
    $dimPrefix = "measure_";
  }

  #set structure-specific database access information
  if ($structType eq "c")
  {
    $dbName = "app.cubes";
  }

  #NB: This is kind of hack-y, but it makes the code dramatically more
  #    readable. If we're not dealing with a cube, set the correct colName
  elsif ($structType eq "l")
  {
    $colName = "script";
    $dbName = "datasource_$dsID.$dimPrefix" . "list";
  }
  elsif ($structType eq "a")
  {
    $colName = "addScript";
    $dbName = "datasource_$dsID.$dimPrefix" . "aggregate";
  }
  elsif ($structType eq "as")
  {
    $colName = "subtractScript";
    $dbName = "datasource_$dsID.$dimPrefix" . "aggregate";
  }

  #hash that we use to convert numerical date types to human-readable string
  undef(%dateTypeName);
  %dateTypeName = (
    10 => "Year",
    20 => "Month",
    30 => "Week",
    40 => "Day"
  );

  #if we're editing a structure that already exists
  if ($structID > 0)
  {

    #fetch the dimension's script string from the master cube table
    if ($structType eq "c")
    {
      $query = "SELECT dsID, $colName FROM app.cubes WHERE ID=$structID";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      datasel_db_err($db, $status, $query);
      ($dsID, $scriptString) = $dbOutput->fetchrow_array;
    }

    #else fetch the list's/aggregate's script string
    elsif (($structType eq "l") || ($structType eq "a") || ($structType eq "as"))
    {
      $query = "SELECT $colName FROM $dbName WHERE ID=$structID";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      datasel_db_err($db, $status, $query);
      ($scriptString) = $dbOutput->fetchrow_array;
    }
  }

  #build up schema name that contains the cube
  $dsSchema = "datasource_" . $dsID;

  #split the script string into individual script steps
  @scriptSteps = split(/,/, $scriptString);

  #get hash of dimension item/structure names and IDs
  %itemNameHash = dsr_get_item_name_hash($db, $dsSchema, $dim);

  #build up one line of the JSON data block for each step in the script
  foreach $step (@scriptSteps)
  {

    #handle manual addition
    if ($step =~ m/^M:(.*)/)
    {

      #the "step" is just the name of the dimension item/structure
      $itemID = $1;
      $itemName = $itemNameHash{$itemID};

      if ($itemID eq "ALL")
      {
        $itemName = "All Items";
      }

      #if the manually selected item doesn't actually exist, skip it
      if (length($itemName) < 1)
      {
        $idx++;
        next;
      }

      #if it's a manually added hierarchy level
      if ($itemID =~ m/^(SHS_\d+)_/)
      {
        $segHierID = $1;
        $segHierName = $itemNameHash{$segHierID};
        $itemName = "$itemName from the $segHierName hierarchy";
      }

      #filter off issue-causing characters
      $itemName =~ s/\"/\\"/g;
      $itemName =~ s/\'/\\'/g;
      $itemName =~ s/\\\\/\\/g;
      $itemType = KAPutil_get_item_type($itemID);
      $JSONblock .= "{id: '$itemID', product: '$itemName', type: '$itemType'},\n";
    }

    #handle a hierarchy selection
    elsif ($step =~ m/^H:(.*?) (.*?)$/)
    {
      $segmentationID = $1;
      $segmentIDStr = $2;

      @segmentIDs = split(' ', $segmentIDStr);

      $itemName = "Items from the ";
      foreach $tmp (@segmentIDs)
      {
        $tmp = "SMT_" . $tmp;
        $itemName .= $itemNameHash{$tmp} . ", ";
      }
      chop($itemName);  chop($itemName);

      $segmentationName = $itemNameHash{$segmentationID};

      $itemName .= " segments of $segmentationName";
      $itemName =~ s/\'/\\'/g;
      $JSONblock .= "{id: '$step', product: '$itemName', type: 'Hierarchy'},\n";
    }

    #handle a segmentation hierarchy selection
    elsif (($step =~ m/^SH:(.*?) (.*?) (.*?) (.*) (INTERSECT)$/) ||
           ($step =~ m/^SH:(.*?) (.*?) (.*?) (.*)$/))
    {
      $segHierID = $1;
      $segHierLevelID = $2;
      $segmentationID = $3;
      $segmentStr = $4;
      $selOp = $5;

      @segmentIDs = split(' ', $segmentStr);

      $segHierName = $itemNameHash{$segHierID};
      if (length($segHierName) < 1)
      {
        $scriptLines[$idx] = "";
        $idx++;
        next;
      }

      if ($segHierLevelID eq "ITEM")
      {
        $segHierLevelName = "item";
      }
      else
      {
        $segHierLevelName = $itemNameHash{"SEG_$segHierLevelID"};
      }

      $segmentationName = $itemNameHash{"SEG_$segmentationID"};
      if ($selOp eq "INTERSECT")
      {
        $itemName = "Intersected with elements from the $segHierName hierarchy at the $segHierLevelName level where $segmentationName is ";
      }
      else
      {
        $itemName = "Elements from the $segHierName hierarchy at the $segHierLevelName level where $segmentationName is ";
      }

      foreach $segmentID (@segmentIDs)
      {
        $segmentName = $itemNameHash{"SMT_$segmentID"};
        $itemName .= "$segmentName, ";
      }

      chop($itemName); chop($itemName);
      $itemName =~ s/\'/\\'/g;

      $JSONblock .= "{id: '$step', product: '$itemName', type: 'Hierarchy'},\n";
    }

    #
    #handle a "most recent" selection
    #
    elsif ($step =~ m/^RECENT:(.*?) (.*?) (.*?) (.*?)$/)
    {
      $selPeriods = $1;
      $selDuration = $2;
      $selType = $3;
      $ending = $4;

      #convert ending to something (more) human-readable
      if ($ending eq "1period")
      {
        $ending = "1 period ago";
      }
      elsif ($ending eq "1year")
      {
        $ending = "1 year ago";
      }

      $itemName = "The $selPeriods most recent $selDuration $dateTypeName{$selType} time periods ending $ending";
      $JSONblock .= "{id: '$step', product: '$itemName', type: 'Range'},\n";
    }

    #
    #handle a range selection
    #
    elsif ($step =~ m/^RANGE:(.*?) (.*?) (.*?) (.*?)$/)
    {
      $selDuration = $1;
      $selType = $2;
      $starting = $3;
      $ending = $4;

      #convert time IDs to something human-readable
      $starting = KAPutil_get_item_ID_name($db, $dsSchema, "t", $starting);

      if ($ending ne "now")
      {
        $ending = KAPutil_get_item_ID_name($db, $dsSchema, "t", $ending);
      }

      $itemName = "The range of $selDuration $dateTypeName{$selType} time periods between $starting and $ending";
      $JSONblock .= "{id: '$step', product: '$itemName', type: 'Range'},\n";
    }

    #handle a match selection
    elsif ($step =~ m/^MATCH:(.*?) (.*)$/)
    {
      $matchType = $1;
      $matchChars = $2;

      #convert match type to something human readable
      if ($matchType eq "start")
      {
        $matchType = "beginning with";
      }
      elsif ($matchType eq "contain")
      {
        $matchType = "containing";
      }
      elsif ($matchType eq "end")
      {
        $matchType = "ending with";
      }
      elsif ($matchType eq "exact")
      {
        $matchType = "exactly matching";
      }

      $itemName = "Items $matchType $matchChars";
      $JSONblock .= "{id: '$step', product: '$itemName', type: 'Match'},\n";
    }
  }

  #remove unneeded last comma and newline
  chop($JSONblock); chop($JSONblock);

  return($JSONblock);
}



#-------------------------------------------------------------------------
#
# Reads the data selection "script" for the specified dimension in the
# specified cube or list/aggregate, parses it, runs any required queries, and
# puts a list of selected item IDs in the dimension column for the specified
# structure or cube.
# Should be called whenever a cube or structure is updated.
#

sub datasel_expand_script
{
  my ($query, $dbOutput, $scriptString, $itemString, $q_itemString, $idx);
  my ($dsSchema, $dbName, $itemColName, $scriptColName, $measureID, $id);
  my ($operator, $matchChars, $matchType, $filterQuery, $itemsDB, $name);
  my ($starting, $ending, $selDuration, $selType, $selPeriods, $selLimit);
  my ($dimCol, $measureCol, $order, $value, $appendEndDate);
  my ($secondaryID, $secondaryDimCol, $tertiaryID, $tertiaryDimCol, $endDate);
  my ($recentEndDate, $itemID, $segmentID, $listID, $itemStr, $segHierID);
  my ($segHierLevelID, $segmentationID, $segmentStr, $listIDs, $q_name);
  my ($segmentIDStr, $selOp, $segHierName, $segmentIDs, $status);
  my (@script, @items, @listMembers, @segHierSegments, @segments, @segmentIDs);
  my (@tmp, @itemArray, @intersect, @finalItemList, @listItems);
  my (%itemNameHash, %baseItems);

  my ($db, $dsSchema, $structID, $dim, $structType, $dontUpdate) = @_;

  #figure out the script and item column names for our dimension
  if ($dim eq "p")
  {
    $itemsDB = "products";
  }
  elsif ($dim eq "g")
  {
    $itemsDB = "geographies";
  }
  elsif ($dim eq "t")
  {
    $itemsDB = "timeperiods";
  }
  elsif ($dim eq "m")
  {
    $itemsDB = "measures";
  }

  #if we're dealing with a list
  if ($structType eq "l")
  {
    $itemColName = "members";
    $scriptColName = "script";

    if ($dim eq "p")
    {
      $dbName = "$dsSchema.product_list";
    }
    elsif ($dim eq "g")
    {
      $dbName = "$dsSchema.geography_list";
    }
    elsif ($dim eq "t")
    {
      $dbName = "$dsSchema.time_list";
    }
    elsif ($dim eq "m")
    {
      $dbName = "$dsSchema.measure_list";
    }
  }

  #else if we're dealing with an aggregate
  if ($structType eq "a")
  {
    $itemColName = "addMembers";
    $scriptColName = "addScript";

    if ($dim eq "p")
    {
      $dbName = "$dsSchema.product_aggregate";
    }
    elsif ($dim eq "g")
    {
      $dbName = "$dsSchema.geography_aggregate";
    }
    elsif ($dim eq "t")
    {
      $dbName = "$dsSchema.time_aggregate";
    }
  }

  #else if we're dealing with an aggregate's "subtract" list
  if ($structType eq "as")
  {
    $itemColName = "subtractMembers";
    $scriptColName = "subtractScript";

    if ($dim eq "p")
    {
      $dbName = "$dsSchema.product_aggregate";
    }
    elsif ($dim eq "g")
    {
      $dbName = "$dsSchema.geography_aggregate";
    }
    elsif ($dim eq "t")
    {
      $dbName = "$dsSchema.time_aggregate";
    }
  }

  #else if we're dealing with a cube
  elsif ($structType eq "c")
  {
    $dbName = "app.cubes";

    if ($dim eq "p")
    {
      $itemColName = "products";
      $scriptColName = "scriptProducts";
    }
    elsif ($dim eq "g")
    {
      $itemColName = "geographies";
      $scriptColName = "scriptGeographies";
    }
    elsif ($dim eq "t")
    {
      $itemColName = "timeperiods";
      $scriptColName = "scriptTimeperiods";
    }
    elsif ($dim eq "m")
    {
      $itemColName = "measures";
      $scriptColName = "scriptMeasures";
    }
  }

  #get the specified script from the database
  $query = "SELECT $scriptColName FROM $dbName WHERE ID=$structID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  datasel_db_err($db, $status, $query);
  ($scriptString) = $dbOutput->fetchrow_array;

  #the script's steps are comma-separated
  @script = split(',', $scriptString);

  #get the hash of all items in the data source (we use this to see if a
  #previously selected item has been deleted, and remove it)
  %itemNameHash = dsr_get_item_name_hash($db, $dsSchema, $dim);

  #step through each script step, adding selected items to the item string
  $itemString = "";
  $idx = 0;
  while (defined($script[$idx]))
  {

    #if the script step is a manual add
    if ($script[$idx] =~ m/^M:(.*)/)
    {
      $itemID = $1;

      #if the manually selected item doesn't actually exist, skip it
      if ((length($itemNameHash{$itemID}) < 1) && ($itemID ne "ALL"))
      {
        $idx++;
        next;
      }

      #if the user is manually selecting a hierarchy level, mark it as
      #manually selected to keep any possible intersection operations
      #further down in the script from interfering with it
      if ($itemID =~ m/SHS_/)
      {
        $itemID = "M" . $itemID;
      }

      #if the item is a DSR list, expand it then add the member items to list
      #NB: effectively implements nested list functionality 1 level deep
      #NB: Since the nested list already exists, we're assuming it's already
      #    been correctly expanded so we can just grab the "members" field
      #    from the database
      if ($itemID =~ m/LIS_(\d+)/)
      {
        @listMembers = DSRlist_expand_items_array($db, $dsSchema, $dim, $itemID);
        $listIDs = "";
        foreach $listID (@listMembers)
        {
          if (length($itemNameHash{$listID}) > 0)
          {
            $listIDs .= "$listID,";
          }
        }
        $itemString .= $listIDs;
      }

      #else if we're a segmentation that needs to be expanded into segments
      elsif ($itemID =~ m/^SEG_(\d+)/)
      {
        @segments = DSRsegmentation_expand_segments_array($db, $dsSchema, $dim, $itemID);
        $segmentIDs = join(',', @segments);
        $itemString .= $segmentIDs . ",";
      }

      #else we're the special "ALL" tag that means all base items
      elsif ($itemID eq "ALL")
      {

        #grab every ID, sorted by name from dimension table (if product dim,
        #don't get child merged items)
        if ($dim eq "p")
        {
          $query = "SELECT ID FROM $dsSchema.$itemsDB WHERE merged < 2 ORDER BY name";
        }
        elsif ($dim eq "t")
        {
          $query = "SELECT ID FROM $dsSchema.$itemsDB ORDER BY endDate DESC";
        }
        else
        {
          $query = "SELECT ID FROM $dsSchema.$itemsDB ORDER BY name";
        }

        $dbOutput = $db->prepare($query);
        $status = $dbOutput->execute();
        datasel_db_err($db, $status, $query);

        while (($id) = $dbOutput->fetchrow_array)
        {
          $itemString .= "$id,";
        }
      }

      #else no additional processing needed, just append item to list
      else
      {
        $itemString .= $itemID . ",";
      }
    }

    #else if the script is a hierarchy selection
    elsif ($script[$idx] =~ m/^H:(.*?) (.*)$/)
    {

      $segmentIDStr = $2;
      @segmentIDs = split(' ', $segmentIDStr);

      #expand each segment into its member items
      undef(@items);
      foreach $segmentID (@segmentIDs)
      {
        @tmp = DSRsegment_expand_items($db, $dsSchema, $dim, $segmentID);
        push(@items, @tmp);
      }

      $itemStr = join(',', @items);

      #no additional processing needed, so just append the item to the list
      $itemString .= $itemStr . ",";
    }

    #else if a segmentation hierarchy selection
    elsif (($script[$idx] =~ m/^SH:(.*?) (.*?) (.*?) (.*) (INTERSECT)$/) ||
           ($script[$idx] =~ m/^SH:(.*?) (.*?) (.*?) (.*)$/))
    {
      $segHierID = $1;
      $segHierLevelID = $2;
      $segmentationID = $3;
      $segmentStr = $4;
      $selOp = $5;

      #if the hierarchy has been deleted, skip it
      $segHierName = KAPsegmentation_hierarchy_ID_to_name($db, $dsSchema, $dim, $segHierID);
      if (length($segHierName) < 1)
      {
        $idx++;
        next;
      }

      #if we've been asked to give hierarchy results at the item level
      if ($segHierLevelID eq "ITEM")
      {
        @segments = split(' ', $segmentStr);
        undef(@itemArray);
        foreach $segmentID (@segments)
        {
          $itemStr = DSRsegment_expand_items($db, $dsSchema, $dim, $segmentID);
          @items = split(',', $itemStr);
          push(@itemArray, @items);
        }
      }

      #else we're creating hierarchy level subsegments
      else
      {
        @itemArray = DSRsegHier_expand_segments($db, $dsSchema, $dim,
            $segHierID, $segHierLevelID, $segmentationID, $segmentStr);
      }

      #handle a modification caused by a selection operation
      if ($selOp eq "INTERSECT")
      {
        @items = split(',', $itemString);
        @intersect = datasel_intersect_arrays(\@itemArray, \@items);
        $itemString = join(',', @intersect);
        if (length($itemString) > 0)
        {
          $itemString .= ",";
        }
      }

      #default selection operation is ADD
      else
      {
        $itemStr = join(',', @itemArray);
        $itemString .= $itemStr . ",";
      }
    }

    #else if the script is a "most recent" selection
    elsif ($script[$idx] =~ m/^RECENT:(.*?) (.*?) (.*?) (.*?)$/)
    {
      $selPeriods = $1;
      $selDuration = $2;
      $selType = $3;
      $ending = $4;

      #build the query string to retrieve matching time periods
      if ($ending eq "now")
      {
        $query = "SELECT ID FROM $dsSchema.timeperiods \
            WHERE duration = $selDuration AND type = $selType \
            ORDER BY endDate DESC LIMIT $selPeriods";
      }
      elsif ($ending eq "1period")
      {
        $query = "SELECT ID FROM $dsSchema.timeperiods \
            WHERE duration = $selDuration AND type = $selType \
            ORDER BY endDate DESC LIMIT 1,$selPeriods";
      }
      elsif ($ending eq "1year")
      {

        #we want this to be 1 year ago from the most recent matching time
        #period in the database (not 1 year from current calendar date NOW())
        $query = "SELECT endDate FROM $dsSchema.timeperiods \
            WHERE duration = $selDuration AND type = $selType \
            ORDER BY endDate DESC LIMIT 1";
        $dbOutput = $db->prepare($query);
        $status = $dbOutput->execute;
        datasel_db_err($db, $status, $query);
        ($recentEndDate) = $dbOutput->fetchrow_array;
        $query = "SELECT ID FROM $dsSchema.timeperiods \
            WHERE duration = $selDuration AND type = $selType AND endDate <= DATE_SUB('$recentEndDate', INTERVAL 52 WEEK) \
            ORDER BY endDate DESC LIMIT $selPeriods";
      }

      #run the query, and append every returned item to the list
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      datasel_db_err($db, $status, $query);
      while (($id) = $dbOutput->fetchrow_array)
      {
        $itemString .= $id . ",";
      }
    }

    #else if the script is a range of time periods selection
    elsif ($script[$idx] =~ m/^RANGE:(.*?) (.*?) (.*?) (.*?)$/)
    {
      $selDuration = $1;
      $selType = $2;
      $starting = $3;
      $ending = $4;

      #get the actual datestamps that correspond to our start and end dates
      $query = "SELECT endDate FROM $dsSchema.timeperiods WHERE ID=$starting";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      datasel_db_err($db, $status, $query);
      ($starting) = $dbOutput->fetchrow_array;

      #if the starting date is bad (undefined), skip this script line
      if (!defined($starting))
      {
        $idx++;
        next;
      }

      #if we're dynamically determining the "now" date
      if ($ending eq "now")
      {
        $query = "SELECT endDate FROM $dsSchema.timeperiods \
            WHERE duration = $selDuration AND type = $selType \
            ORDER BY endDate DESC LIMIT 1";
        $dbOutput = $db->prepare($query);
        $status = $dbOutput->execute;
        datasel_db_err($db, $status, $query);
        ($ending) = $dbOutput->fetchrow_array;
      }

      #else it's a fixed end date
      else
      {
        $query = "SELECT endDate FROM $dsSchema.timeperiods WHERE ID=$ending";
        $dbOutput = $db->prepare($query);
        $status = $dbOutput->execute;
        datasel_db_err($db, $status, $query);
        ($ending) = $dbOutput->fetchrow_array;
      }

      #build the query string to retrieve matching time periods
      $query = "SELECT ID FROM $dsSchema.timeperiods \
          WHERE duration = $selDuration AND type = $selType AND endDate >= '$starting' AND endDate <= '$ending' \
          ORDER BY endDate DESC";

      #run the query, and append every returned item to the list
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      datasel_db_err($db, $status, $query);
      while (($id) = $dbOutput->fetchrow_array)
      {
        $itemString .= $id . ",";
      }
    }

    #else if the script is a match selection
    elsif ($script[$idx] =~ m/^MATCH:(.*?) (.*)$/)
    {
      $matchType = $1;
      $matchChars = $2;

      #construct the filter for the query based on match parameters
      if ($matchType eq "start")
      {
        $filterQuery = "name LIKE '$matchChars%'";
      }
      elsif ($matchType eq "contain")
      {
        $filterQuery = "name LIKE '\%$matchChars%'";
      }
      elsif ($matchType eq "end")
      {
        $filterQuery = "name LIKE '\%$matchChars'";
      }
      elsif ($matchType eq "exact")
      {
        $filterQuery = "name = '$matchChars'";
      }

      #build up the query string to retrieve matching products
      $query = "SELECT ID FROM $dsSchema.$itemsDB WHERE $filterQuery";

      #run the query, and append every returned item to the list
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      datasel_db_err($db, $status, $query);
      while (($id) = $dbOutput->fetchrow_array)
      {
        $itemString .= $id . ",";
      }
    }

    $idx++;
  }

  #chop the final trailing comma from the item list
  chop($itemString);

  #expand any list structures in the item list
  @items = split(/,/, $itemString);
  undef(@finalItemList);
  foreach $itemID (@items)
  {

    #if we're a list, expand ourselves and add to item array
    if ($itemID =~ m/^LIS_(\d+)/)
    {
      @listItems = DSRlist_expand_items_array($db, $dsSchema, $dim, $1);
      push(@finalItemList, @listItems);
    }

    #if we're a manual hierarchy selection, get rid of the manual tag
    elsif ($itemID =~ m/^M(SHS_.*)/)
    {
      push(@finalItemList, $1);
    }

    #if we're anything else, just add to item array
    else
    {
      push(@finalItemList, $itemID);
    }
  }

  #remove any duplicate items
  @finalItemList = uniq(@finalItemList);

  $itemString = join(',', @finalItemList);

  #store the list of items
  if ($dontUpdate < 1)
  {
    $q_itemString = $db->quote($itemString);
    $query = "UPDATE $dbName SET $itemColName = $q_itemString WHERE ID=$structID";
    $status = $db->do($query);
    datasel_db_err($db, $status, $query);
  }

  #if we're a time aggregate, update our name if we need to
  if (($structType eq "a") && ($dim eq "t"))
  {
    $query = "SELECT name, appendEndDate FROM $dsSchema.time_aggregate \
        WHERE ID=$structID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    datasel_db_err($db, $status, $query);
    ($name, $appendEndDate) = $dbOutput->fetchrow_array;
    if ($appendEndDate == 1)
    {
      $name =~ m/^(.*) .*?$/;
      $name = $1;

      $endDate = DSRagg_get_recent_date($db, $dsSchema, $structID);
      $name = "$name $endDate";

      $q_name = $db->quote($name);
      $query = "UPDATE $dsSchema.time_aggregate SET name=$q_name WHERE ID=$structID";
      $status = $db->do($query);
      datasel_db_err($db, $status, $query);
    }
  }

  return($itemString);
}



#-------------------------------------------------------------------------
#
# "Transfers" a data selection script from one data source to another by
# looking for matching items and changing all ID references in the script
# to match. Primarily used to swap reports between data sources and to
# transfer data source structures like lists/aggregates from one data source
# to another. The updated script is returned as a CSV string suitable for
# insertion into a database table.
#

sub datasel_transfer_script
{
  my ($query, $dbOutput, $destScript, $key, $name, $scriptLine, $srcItemID);
  my ($srcItemName, $destItemID, $srcSegID, $srcSegmentID, $segDB);
  my ($segmentDB, $q_srcSegName, $q_srcSegmentName, $srcSegmentName);
  my ($srcSegName, $destSegID, $destSegmentID, $newScriptLine);
  my ($measureID, $operator, $value, $secondaryID, $tertiaryID);
  my ($srcMeasureName, $destMeasureID, $secondaryDim, $tertiaryDim);
  my ($secondaryName, $tertiaryName, $newSecondaryID, $newTertiaryID);
  my ($selLimit, $measureName, $newMeasureID, $selDuration);
  my ($selType, $starting, $ending, $status, $startingDate, $endingDate);
  my ($srcSegHierID, $srcIntersect, $segHierDB, $srcSegHierLevelID);
  my ($srcSegHierFilterID, $srcSegments, $srcSegHierName, $ok);
  my ($q_srcSegHierName, $destSegHierID, $srcSegHierLevelName);
  my ($q_srcSegHierLevelName, $destSegHierLevelID, $srcSegHierFilterName);
  my ($q_srcSegHierFilterName, $destSegHierFilterID, $destSegments);
  my ($srcUPCAttrID, $destUPCAttrID, $srcUPC);
  my (@scriptLines, @destScriptLines, @srcSegmentIDs, @destSegmentIDs);
  my (%destItemNames, %srcItemNames, %destItemIDhash);
  my (%srcUPCHash, %destUPCHash);

  my ($db, $srcSchema, $destSchema, $dim, $srcScript, $matchUPC) = @_;

  #grab hashes of item names indexed by ID for source and dest DS
  %destItemNames = dsr_get_item_name_hash($db, $destSchema, $dim, 1);
  %srcItemNames = dsr_get_item_name_hash($db, $srcSchema, $dim, 1);

  #if we're going to do a match by UPC, build up the matching hashes
  if (($dim eq "p") && ($matchUPC > 0))
  {
    #get ID of UPC attribute in source DS
    $query = "SELECT ID FROM $srcSchema.product_attributes WHERE name='UPC'";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    datasel_db_err($db, $status, $query);
    ($srcUPCAttrID) = $dbOutput->fetchrow_array;

    #get ID of UPC attribute in destination DS
    $query = "SELECT ID FROM $destSchema.product_attributes WHERE name='UPC'";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    datasel_db_err($db, $status, $query);
    ($destUPCAttrID) = $dbOutput->fetchrow_array;

    #get value hashes for UPC attributes in both source and dest DS
    %srcUPCHash = DSRattr_get_values_hash($db, $srcSchema, "p", $srcUPCAttrID);
    %destUPCHash = DSRattr_get_values_hash($db, $destSchema, "p", $destUPCAttrID);

    #reverse the destination hash so we can search for matching values
    %destUPCHash = reverse(%destUPCHash);
  }

  #reverse the destination ID hash so we can easily search it by name
  #NB: we're lower-casing the names to make searches case-insensitive
  undef(%destItemIDhash);
  foreach $key (keys %destItemNames)
  {
    $name = lc($destItemNames{$key});

    #if the name is a structure, prepend the struct type to it
    #NB: do this to avoid selecting a list with the same name as an agg, etc.
    if (($key =~ m/^(SEG)_/) || ($key =~ m/^(SMT)_/) || ($key =~ m/^(SHS)_/) ||
        ($key =~ m/^(LIS)_/) || ($key =~ m/^(AGG)_/))
    {
      $name = $1 . $name;
    }

    $destItemIDhash{$name} = $key;
  }

  #split the source script into an array of its lines
  @scriptLines = split(',', $srcScript);

  #run through each line of the script, transferring what we can
  foreach $scriptLine (@scriptLines)
  {

    #if we're a manual item selection, look for a matching item and add it
    if ($scriptLine =~ m/^M:(.*)/)
    {
      $srcItemID = $1;
      $srcItemName = lc($srcItemNames{$srcItemID});

      #prepend struct type to name, if appropriate, for searching
      if (($srcItemID =~ m/^(SEG)_/) || ($srcItemID =~ m/^(SMT)_/) ||
          ($srcItemID =~ m/^(SHS)_/) || ($srcItemID =~ m/^(LIS)_/) ||
          ($srcItemID =~ m/^(AGG)_/))
      {
        $srcItemName = $1 . $srcItemName;
      }

      $destItemID = $destItemIDhash{$srcItemName};

      #if we're the magic "ALL" tag
      if ($srcItemID eq "ALL")
      {
        $destItemID = "ALL";
      }

      #if we're a base item and matching by UPC, ignore the above and do a UPC
      #match
      if (($matchUPC > 0) && ($srcItemID =~ m/^\d+$/))
      {
        $srcUPC = $srcUPCHash{$srcItemID};
        $destItemID = $destUPCHash{$srcUPC};
      }

      if (defined($destItemID))
      {
        $scriptLine = "M:$destItemID";
        push(@destScriptLines, $scriptLine);
      }
    }

    #else if we're a hierarchy selection, look for a matching segment
    elsif ($scriptLine =~ m/^H:(.*?) (.*)$/)
    {
      $srcSegID = $1;
      $srcSegmentID = $2;

      #make sure we have the segmentation and segment IDs fully qualified
      if ($srcSegID =~ m/^(\d+)/)
      {
        $srcSegID = "SEG_$1";
      }
      if ($srcSegmentID =~ m/^(\d+)/)
      {
        $srcSegmentID = "SMT_$1";
      }

      #get names of segmentation and segment from source DS
      $srcSegName = $srcItemNames{$srcSegID};
      $srcSegmentName = $srcItemNames{$srcSegmentID};

      #figure out which databases we're going to search for matching names
      if ($dim eq "p")
      {
        $segDB = "product_segmentation";
        $segmentDB = "product_segment";
      }
      elsif ($dim eq "g")
      {
        $segDB = "geography_segmentation";
        $segmentDB = "geography_segment";
      }
      elsif ($dim eq "t")
      {
        $segDB = "time_segmentation";
        $segmentDB = "time_segment";
      }

      #look for a matching segmentation in the destination DS
      $q_srcSegName = $db->quote($srcSegName);
      $query = "SELECT ID FROM $destSchema.$segDB WHERE name LIKE $q_srcSegName";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      datasel_db_err($db, $status, $query);
      ($destSegID) = $dbOutput->fetchrow_array;
      if (!defined($destSegID))
      {
        next;
      }

      #look for a matching segment in that segmentation
      $q_srcSegmentName = $db->quote($srcSegmentName);
      $query = "SELECT ID FROM $destSchema.$segmentDB \
          WHERE name LIKE $q_srcSegmentName AND segmentationID=$destSegID";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      datasel_db_err($db, $status, $query);
      ($destSegmentID) = $dbOutput->fetchrow_array;
      if (!defined($destSegmentID))
      {
        next;
      }

      #if we made it this far, we're good to transfer
      $newScriptLine = "H:SEG_$destSegID $destSegmentID";
      push(@destScriptLines, $newScriptLine);
    }

    #else if we're a seghierarchy selection, look for a matching segment
    elsif ($scriptLine =~ m/^SH:(SHS_\d+) (\d+) (\d+) ([\d\s]+) (.*)$/)
    {
      $srcSegHierID = $1;
      $srcSegHierLevelID = $2;
      $srcSegHierFilterID = $3;
      $srcSegments = $4;
      $srcIntersect = $5;

      #figure out which databases we're going to search for matching names
      if ($dim eq "p")
      {
        $segHierDB = "product_seghierarchy";
        $segDB = "product_segmentation";
        $segmentDB = "product_segment";
      }
      elsif ($dim eq "g")
      {
        $segHierDB = "geography_seghierarchy";
        $segDB = "geography_segmentation";
        $segmentDB = "geography_segment";
      }
      elsif ($dim eq "t")
      {
        $segHierDB = "time_seghierarchy";
        $segDB = "time_segmentation";
        $segmentDB = "time_segment";
      }

      #get the name of our source seg hierarchy
      $srcSegHierName = $srcItemNames{$srcSegHierID};

      #look for a matching seg hierarchy in the destination DS
      $q_srcSegHierName = $db->quote($srcSegHierName);
      $query = "SELECT ID FROM $destSchema.$segHierDB WHERE name LIKE $q_srcSegHierName";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      datasel_db_err($db, $status, $query);
      ($destSegHierID) = $dbOutput->fetchrow_array;
      if (!defined($destSegHierID))
      {
        next;
      }
      $destSegHierID = "SHS_" . $destSegHierID;

      #get the name of our level segmentation
      $srcSegHierLevelID = "SEG_" . $srcSegHierLevelID;
      $srcSegHierLevelName = $srcItemNames{$srcSegHierLevelID};

      #look for a matching level segmentation in the destination DS
      $q_srcSegHierLevelName = $db->quote($srcSegHierLevelName);
      $query = "SELECT ID FROM $destSchema.$segDB WHERE name LIKE $q_srcSegHierLevelName";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      datasel_db_err($db, $status, $query);
      ($destSegHierLevelID) = $dbOutput->fetchrow_array;
      if (!defined($destSegHierLevelID))
      {
        next;
      }

      #get the name of our filter level segmentation
      $srcSegHierFilterID = "SEG_" . $srcSegHierFilterID;
      $srcSegHierFilterName = $srcItemNames{$srcSegHierFilterID};

      #look for a matching filter level segmentation in the destination DS
      $q_srcSegHierFilterName = $db->quote($srcSegHierFilterName);
      $query = "SELECT ID FROM $destSchema.$segDB WHERE name LIKE $q_srcSegHierFilterName";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      datasel_db_err($db, $status, $query);
      ($destSegHierFilterID) = $dbOutput->fetchrow_array;
      if (!defined($destSegHierFilterID))
      {
        next;
      }

      #turn our list of segments into an array
      @srcSegmentIDs = split(' ', $srcSegments);

      #look for a matching segment in the destination
      $ok = 1;
      undef(@destSegmentIDs);
      foreach $srcSegmentID (@srcSegmentIDs)
      {
        $srcSegmentID = "SMT_" . $srcSegmentID;
        $srcSegmentName = $srcItemNames{$srcSegmentID};

        $q_srcSegmentName = $db->quote($srcSegmentName);
        $query = "SELECT ID FROM $destSchema.$segmentDB WHERE name LIKE $q_srcSegmentName";
        $dbOutput = $db->prepare($query);
        $status = $dbOutput->execute;
        datasel_db_err($db, $status, $query);
        ($destSegmentID) = $dbOutput->fetchrow_array;
        if (!defined($destSegmentID))
        {
          $ok = 0;
        }
        else
        {
          push(@destSegmentIDs, $destSegmentID);
        }
      }

      if ($ok == 0)
      {
        next;
      }
      else
      {
        $destSegments = join(' ', @destSegmentIDs);
      }

      #if we made it this far, we're good to transfer
      $newScriptLine = "SH:$destSegHierID $destSegHierLevelID $destSegHierFilterID $destSegments $srcIntersect";
      push(@destScriptLines, $newScriptLine);
    }

    #if we're a "most recent" selection, we can add as-is and let the
    #DataSel script expander figure out the rest
    elsif ($scriptLine =~ m/^RECENT:/)
    {
      push(@destScriptLines, $scriptLine);
    }

    #if we're a time range selection, make sure we have matching start
    #and ending time periods
    elsif ($scriptLine =~ m/^RANGE:(.*?) (.*?) (.*?) (.*?)$/)
    {
      $selDuration = $1;
      $selType = $2;
      $starting = $3;
      $ending = $4;

      #get the endDate for the starting and ending time periods from the
      #source schema
      $query = "SELECT endDate FROM $srcSchema.timeperiods WHERE ID=$starting";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      datasel_db_err($db, $status, $query);
      ($startingDate) = $dbOutput->fetchrow_array;

      if ($ending ne "now")
      {
        $query = "SELECT endDate FROM $srcSchema.timeperiods WHERE ID=$ending";
        $dbOutput = $db->prepare($query);
        $status = $dbOutput->execute;
        datasel_db_err($db, $status, $query);
        ($endingDate) = $dbOutput->fetchrow_array;
      }

      #look for a matching starting date in the destination schema
      $query = "SELECT ID FROM $destSchema.timeperiods \
          WHERE type=$selType AND duration=$selDuration AND endDate='$startingDate'";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      datasel_db_err($db, $status, $query);
      ($starting) = $dbOutput->fetchrow_array;

      #if we didn't find a matching starting date, we can't transfer it
      if ($starting < 1)
      {
        next;
      }

      #look for a ending starting date in the destination schema
      if ($ending ne "now")
      {
        $query = "SELECT ID FROM $destSchema.timeperiods \
            WHERE type=$selType AND duration=$selDuration AND endDate='$endingDate'";
        $dbOutput = $db->prepare($query);
        $status = $dbOutput->execute;
        datasel_db_err($db, $status, $query);
        ($ending) = $dbOutput->fetchrow_array;
      }

      #if we didn't find a matching ending date, we can't transfer it
      if (($ending < 1) && ($ending ne "now"))
      {
        next;
      }

      #if we made it this far, we're good to transfer
      $newScriptLine = "RANGE:$selDuration $selType $starting $ending";
      push(@destScriptLines, $newScriptLine);
    }

    #if we're a match selection, we can add as-is and let the DataSel
    #script expander figure out the rest
    elsif ($scriptLine =~ m/^MATCH:/)
    {
      push(@destScriptLines, $scriptLine);
    }
  }

  $destScript = join(',', @destScriptLines);
  return($destScript);
}



#-------------------------------------------------------------------------
#
# Returns the list of selected base items in array format for the specified
# dimension and specified component. Used by the cube building process.
# We're also going to get any lists defined for the dimension and expand them,
# since in the cube list items are treated just like base items.
#

sub datasel_get_selected_base_items
{
  my ($query, $dbOutput, $status);
  my ($itemString, $colName, $itemID, $dsID, $dsSchema, $listItem);
  my (@items, @listItems, @baseItems);

  my ($db, $rptID, $dim) = @_;

  #get our dimension's DB name
  if ($dim eq "p")
  {
    $colName = "products";
  }
  elsif ($dim eq "g")
  {
    $colName = "geographies";
  }
  elsif ($dim eq "t")
  {
    $colName = "timeperiods";
  }
  elsif ($dim eq "m")
  {
    $colName = "measures";
  }

  #fetch the selected items for our dimension from the cubes table
  $query = "SELECT dsID, $colName FROM app.cubes WHERE ID=$rptID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  datasel_db_err($db, $status, $query);
  ($dsID, $itemString) = $dbOutput->fetchrow_array;

  #build up schema name
  $dsSchema = "datasource_" . $dsID;

  #split the product string into individual items
  @items = split(/,/, $itemString);

  #add any items that are base items to the array we're going to pass back
  undef(@baseItems);
  foreach $itemID (@items)
  {

    #base items have IDs that are just numbers, no characters
    if ($itemID =~ m/^\d+/)
    {
      push(@baseItems, $itemID);
    }

    #lists have IDs that begin LIS_
    if ($itemID =~ m/^LIS_(\d+)/)
    {
      @listItems = DSRlist_expand_items_array($db, $dsSchema, $dim, $1);

      #make sure we're only grabbing the base items from the list, not aggs and
      #segs as well
      foreach $listItem (@listItems)
      {
        if ($listItem =~ m/^\d+/)
        {
          push(@baseItems, $listItem);
        }
      }

    }
  }

  return(@baseItems);
}



#-------------------------------------------------------------------------
#
# Returns the in-order list of dimension items in a particular report.
# The returned array contains the items that should be displayed to the
# user to choose from in a report's dimension selector.
#

sub datasel_get_dimension_items
{
  my ($query, $dbOutput, $status);
  my ($itemString, $colName, $itemID, $dsID, $dsSchema, $name);
  my (@items, @baseItems, @listItems);

  my ($db, $rptID, $dim) = @_;

  #get our dimension's DB name
  if ($dim eq "p")
  {
    $colName = "products";
  }
  elsif ($dim eq "g")
  {
    $colName = "geographies";
  }
  elsif ($dim eq "t")
  {
    $colName = "timeperiods";
  }
  elsif ($dim eq "m")
  {
    $colName = "measures";
  }

  #fetch the selected items for our dimension from the cubes table
  $query = "SELECT dsID, $colName FROM cubes WHERE ID=$rptID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  datasel_db_err($db, $status, $query);
  ($dsID, $itemString) = $dbOutput->fetchrow_array;

  #build up schema name
  $dsSchema = "datasource_" . $dsID;

  #split the product string into individual items
  @items = split(/,/, $itemString);

  #add any items that are base items to the array we're going to pass back
  undef(@baseItems);
  foreach $itemID (@items)
  {

    #base items have IDs that are just numbers, no characters
    if ($itemID =~ m/^\d+/)
    {
      push(@baseItems, $itemID);
    }

    #lists have IDs that begin LIS_
    #expand each list out into its base items and add to array
    if ($itemID =~ m/^LIS_(\d+)/)
    {
      @listItems = DSRlist_expand_items_array($db, $dsSchema, $dim, $1);
      push(@baseItems, @listItems);
    }

    #aggregates have IDs that begin AGG_
    if ($itemID =~ m/^AGG_(\d+)/)
    {
      push(@baseItems, $itemID);
    }

    #segmentations have IDs that begin SEG_
    #expand each segmentation out into its segments and add to array
    if ($itemID =~ m/^SEG_(\d+)/)
    {
      @listItems = DSRsegmentation_expand_segments_array($db, $dsSchema, $dim, $1);
      push(@baseItems, @listItems);
    }

    #segments have IDs that begin SMT_
    if ($itemID =~ m/^SMT_(\d+)/)
    {
      push(@baseItems, $itemID);
    }

    #segment levels in segment hierarchies
    if ($itemID =~ m/^SHS_(\d+)/)
    {
      push(@baseItems, $itemID);
    }

    #if we're the measure dim, we might have "special" references to attributes
    #and segmentations that mean print their name rather than their numerical
    #value. We're going to handle that here
    if ($itemID =~ m/^[PGT]ATT_/)
    {
      push(@baseItems, $itemID);
    }
    if ($itemID =~ m/^[PGT]SEG_/)
    {
      push(@baseItems, $itemID);
    }
  }

  return(@baseItems);
}



#-------------------------------------------------------------------------
#
# Get and return the numerical value of the specified measure for the
# specified TGP tuple. Mostly useful for custom reports.
#

sub datasel_get_tgpm_value
{
  my ($query, $dbOutput, $value, $colName, $rptCube, $status);

  my ($db, $dsSchema, $rptID, $productID, $geographyID, $timeID, $measureID) = @_;

  $colName = "measure_" . $measureID;
  $rptCube = "_rptcube_" . $rptID;

  #fetch the specified measure value from the report cube
  $query = "SELECT $colName FROM $dsSchema.$rptCube \
      WHERE product=$productID AND geography=$geographyID AND time=$timeID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  datasel_db_err($db, $status, $query);
  ($value) = $dbOutput->fetchrow_array;

  return($value);
}


#-------------------------------------------------------------------------



1;
