#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: New Data Source</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<SCRIPT SRC="/jquery-editable-select-2.2.5/dist/jquery-editable-select.min.js"></SCRIPT>
<LINK HREF="/jquery-editable-select-2.2.5/dist/jquery-editable-select.min.css" REL="stylesheet">


<SCRIPT>
function checkForm(form)
{
  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Please Wait...');
  return(true);
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item active">New Data Source</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  $layout = $q->param('layout');
  $key = $q->param('key');
  $tabs = $q->param('tabs');
  $appendUPC = $q->param('appendUPC');
  $compressWS = $q->param('compressWS');
  $dontOverwriteNames = $q->param('dontOverwriteNames');
  $scriptID = $q->param('scriptID');

  $options = "";
  if (defined($appendUPC))
  {
    $options .= "appendUPC,";
  }
  if (defined($compressWS))
  {
    $options .= "compressWS,";
  }
  if (defined($dontOverwriteNames))
  {
    $dontOverwriteNames = 1;
  }

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #connect to the database
  $db = KAPutil_connect_to_database();

  print_html_header();

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-11 col-md-8 col-lg-6 col-xl-5"> <!-- content -->

      <FORM METHOD="post" ACTION="/app/dsr/newDSmatching.cld" onsubmit="return checkForm(this);">
      <INPUT TYPE="hidden" NAME="layout" VALUE="$layout">
      <INPUT TYPE="hidden" NAME="key" VALUE="$key">
      <INPUT TYPE="hidden" NAME="tabs" VALUE="$tabs">
      <INPUT TYPE="hidden" NAME="options" VALUE="$options">
      <INPUT TYPE="hidden" NAME="dontOverwriteNames" VALUE="$dontOverwriteNames">
      <INPUT TYPE="hidden" NAME="scriptID" VALUE="$scriptID">

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Data Source Name</DIV>
        <DIV CLASS="card-body">

          <LABEL FOR="dsName">Name:</LABEL>
          <INPUT CLASS="form-control" TYPE="text" NAME="dsName" ID="dsName" MAXLENGTH="127" required>

          <P>&nbsp;</P>
          <DIV CLASS="row form-group">
            <DIV CLASS="col">
              <LABEL FOR="dsType">Type:</LABEL>
              <SELECT CLASS="form-select" NAME="dsType" ID="dsType" required>
END_HTML

  #initialize our data source type hash with the old standbys
  %dsTypes = (
	"IRI" => 1,
	"Nielsen" => 1,
	"POS" => 1,
	"RetailLink" => 1,
	"Shipping" => 1);

  #grab all of the data source types from the customer's cloud
  $query = "SELECT type FROM dataSources";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  while (($type) = $dbOutput->fetchrow_array)
  {
    $dsTypes{$type} = 1;
  }

  foreach $type (sort keys %dsTypes)
  {
    print("<OPTION>$type</OPTION>\n");
  }

  print <<END_HTML;
              </SELECT>
              <SCRIPT>
                \$('#dsType').editableSelect({filter:false});
                \$("#dsType").val('IRI');
              </SCRIPT>
            </DIV>
          </DIV>

          <P>&nbsp;</P>
          <LABEL FOR="dsDescription">Description:</LABEL>
          <INPUT CLASS="form-control" TYPE="text" NAME="dsDescription" ID="dsDescription" MAXLENGTH="1023">

          <P>&nbsp;<P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="location.href='/app/dsr/main.cld'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-submit">Next <I CLASS="bi bi-arrow-right"></I></BUTTON>
          </DIV>

        </DIV>
      </DIV>

      </FORM>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
