#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::Social;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: $action Segmentation</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">$action Segmentation</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('dsID');
  $dim = $q->param('dim');
  $segName = $q->param('segName');
  $allothers = $q->param('allothers');
  $attribute = $q->param('attr');
  $segID = $q->param('seg');
  $editType = $q->param('etype');

  #if we didn't get a segmentation ID, we know we're creating a new segment
  if ($segID > 0)
  {
    $newSegmentation = 0;
    $action = "Edit";
    $message = "The changes to segmentation $segName have been saved.";
  }
  else
  {
    $newSegmentation = 1;
    $action = "New";
    $message = "The segmentation $segName was created successfully.";
  }

  #run through the list of CGI parameters, extracting segments (prepended with
  #"SEG " to identify them
  undef(%segHash);
  @names = $q->param;
  foreach $name (@names)
  {
    if ($name =~ m/^SEG (.*)/)
    {
      $segment = $1;
      $items = $q->param($name);
      $segHash{$segment} = $items;
    }
  }

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  #make sure we have write privs for the data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to modify the data source.");
  }

  $dsSchema = "datasource_" . $dsID;

  #clear any feed items related to old versions of this segmentation
  Social_clear_seg_items($db, $dsID, $segName);

  $dimDB = KAPutil_get_dim_stub_name($dim);
  $segTable = $dimDB . "segmentation";
  $segmentTable = $dimDB . "segment";
  $itemTable = $dimDB . "segment_item";

  #if we're saving (and applying) segmentation rules
  if ($editType eq "rules")
  {

    #apply any segmentation rules that might exist
    DSRsegmentation_apply_seg_rules($db, $dsSchema, $dim, $segID);

    #update the last modified timestamp for the data source
    $query = "UPDATE dataSources SET lastModified=NOW() WHERE ID=$dsID";
    $db->do($query);

    #update any linked segmentations that might exist
    DSRsegmentation_update_linked_segs($db, $dsSchema, $dim, $segID);

    print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-11 col-md-8 col-lg-6 col-xl-5"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">$action Segmentation</DIV>
        <DIV CLASS="card-body">

          $message

          <P>&nbsp;<P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='display.cld?ds=$dsID&dim=$dim'"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
          </DIV>

        </DIV>
      </DIV>
    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

    print_html_footer();

    $activity = "$first $last saved/applied segmentation rules for $segName in $dsName";
    utils_audit($db, $userID, "Saved/applied segmentation rules for $segName in $dsName", $dsID, 0, 0);
    utils_slack($activity);

    exit;
  }


  #everything after this point handles a new/edited segmentation


  #if we're creating a new segmentation, add entry for it and get its
  #auto-assigned ID
  if ($newSegmentation)
  {
    $q_segName = $db->quote($segName);
    $query = "INSERT INTO $dsSchema.$segTable (name) VALUES ($q_segName)";
    $db->do($query);
    $segID = $db->{q{mysql_insertid}};

    $dsName = ds_id_to_name($db, $dsID);
    $activity = "$first $last created new segmentation $segName in $dsName";
    utils_audit($db, $userID, "Created new segmentation $segName", $dsID, 0, 0);
  }

  #else update the segmentation for a potential name change
  else
  {
    $q_segName = $db->quote($segName);
    $query = "UPDATE $dsSchema.$segTable SET name=$q_segName WHERE id=$segID";
    $db->do($query);

    $dsName = ds_id_to_name($db, $dsID);
    $activity = "$first $last modified segmentation $segName in $dsName";
    utils_audit($db, $userID, "Modified segmentation $segName", $dsID, 0, 0);
  }

  #if we're creating a segment based on an attribute
  if ($attribute > 0)
  {

    #get unique list of possible values for the selected attribute
    $dbName = $dimDB . "_attribute_values";
    $query = "SELECT DISTINCT(value) FROM $dsSchema.$dbName \
        WHERE attributeID = $attribute";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;

    #treat the list of unique attribute values as segments, and add them to DB
    while (($segment) = $dbOutput->fetchrow_array)
    {
      #if the segment name is empty, ignore it
      if (length($segment) < 1)
      {
        next;
      }

      #add segment to DB
      $q_segment = $db->quote($segment);
      $query = "INSERT INTO $dsSchema.$segmentTable (segmentationID, name) \
          VALUES ($segID, $q_segment)";
      $db->do($query);

      #get the newly inserted segment's ID
      $segmentID = $db->{q{mysql_insertid}};

      #grab the ID of every item that has the current attribute value, and add
      #them as members of the segment
      $query = "SELECT itemID FROM $dsSchema.$dbName \
          WHERE attributeID = $attribute AND value = $q_segment";
      $dbOutput1 = $db->prepare($query);
      $status = $dbOutput1->execute;

      while (($item) = $dbOutput1->fetchrow_array)
      {
        $query = "INSERT INTO $dsSchema.$itemTable \
            (segmentationID, segmentID, itemID) \
            VALUES ($segID, $segmentID, $item)";
        $db->do($query);
      }
    }
  }

  #else if we're editing an existing segmentation
  elsif ($newSegmentation == 0)
  {

    #get list of existing segments
    %existingSegments = DSRseg_get_segments_hash($db, $dsSchema, $dim, $segID);

    #if an existing segment isn't in the list of user-selected segments,
    #delete it
    foreach $existingSegID (keys %existingSegments)
    {
      $existingSegName = $existingSegments{$existingSegID};
      if (!(defined($segHash{$existingSegName})))
      {

        #if the segment no longer exists, delete it from the list of segments
        $query = "DELETE FROM $dsSchema.$segmentTable \
            WHERE ID=$existingSegID AND segmentationID=$segID";
        $db->do($query);

        #and delete all of its item membership info
        $query = "DELETE FROM $dsSchema.$itemTable \
            WHERE segmentationID=$segID AND segmentID=$existingSegID";
        $db->do($query);
      }
    }

    #invert the hash of existing segments so we can search by name
    %existingSegNames = reverse(%existingSegments);

    #if we're editing an existing segmentation, get everything's current
    #membership and use it later on when we're updating memberships to
    #optimize our use of the database and only make updates that really
    #need to be made
    %segmentMembership = DSRseg_get_segment_membership_hash($db, $dsSchema, $dim, $segID);

    #add new segments and update existing items from user selections
    foreach $segment (keys %segHash)
    {
      #if it's a new segment, add it and its items
      if (!(defined($existingSegNames{$segment})))
      {
        $q_segment = $db->quote($segment);
        $query = "INSERT INTO $dsSchema.$segmentTable (segmentationID, name) \
            VALUES ($segID, $q_segment)";
        $db->do($query);

        #get the newly inserted segment's ID
        $segmentID = $db->{q{mysql_insertid}};

        #split the CSV of the segment's items into an array
        @items = split(',', $segHash{$segment});

        foreach $item (@items)
        {
          $query = "INSERT INTO $dsSchema.$itemTable \
              (segmentationID, segmentID, itemID) \
              VALUES ($segID, $segmentID, $item) \
              ON DUPLICATE KEY UPDATE segmentationID=$segID, segmentID=$segmentID";
          $db->do($query);
        }
      }

      #else just update the existing segment's item list
      else
      {
        $segmentID = $existingSegNames{$segment};

        #pull an extraneous comma off of the membership string if we need to
        if ($segHash{$segment} =~ m/^(.*),$/)
        {
          $segHash{$segment} = $1;
        }

        #delete the stale info about the segment's membership
        if (length($segHash{$segment}) > 0)
        {
          $query = "DELETE FROM $dsSchema.$itemTable \
              WHERE segmentationID=$segID AND segmentID=$segmentID AND itemID NOT IN ($segHash{$segment})";
        }
        else
        {
          $query = "DELETE FROM $dsSchema.$itemTable \
              WHERE segmentationID=$segID AND segmentID=$segmentID";
        }
        $db->do($query);

        #split the CSV of the segment's items into an array
        @items = split(',', $segHash{$segment});

        foreach $item (@items)
        {

          #if the segment membership of the item has been changed
          if ($segmentMembership{$item} != $segmentID)
          {
            $query = "INSERT INTO $dsSchema.$itemTable \
                (segmentationID, segmentID, itemID) \
                VALUES ($segID, $segmentID, $item) \
                ON DUPLICATE KEY UPDATE segmentationID=$segID, segmentID=$segmentID";
            $db->do($query);
          }
        }
      }
    }

    #if the user wants us to create an "All Others" segment
    if (defined($allothers))
    {

      #get array of all items in dimension that aren't segmented
      @unsegmentedItems = DSRseg_get_unsegmented_items($db, $dsSchema, $dim, $segID);

      #if the "All Others" segment already exists, get its ID
      $query = "SELECT ID from $dsSchema.$segmentTable \
          WHERE segmentationID=$segID AND name='All Others'";
      $dbOutput = $db->prepare($query);
      $dbOutput->execute;
      ($allOthersID) = $dbOutput->fetchrow_array;

      #if the "All Others" segment doesn't exist, create it and get ID
      if ($allOthersID < 1)
      {
        $query = "INSERT INTO $dsSchema.$segmentTable \
            (segmentationID, name) VALUES ($segID, 'All Others')";
        $db->do($query);
        $allOthersID = $db->{q{mysql_insertid}};
      }

      #add any unsegmented items to the "All Others" segment
      foreach $itemID (@unsegmentedItems)
      {
        $query = "INSERT INTO $dsSchema.$itemTable \
            (segmentationID, segmentID, itemID) \
            VALUES ($segID, $allOthersID, $itemID)";
        $db->do($query);
      }
    }
  }

  #else we're creating a new segment based on manual picks
  else
  {

    #insert entries for each of the segments and the items they contain
    foreach $segment (keys %segHash)
    {

      #insert the segment
      $q_segment = $db->quote($segment);
      $query = "INSERT INTO $dsSchema.$segmentTable (segmentationID, name) \
          VALUES ($segID, $q_segment)";
      $db->do($query);

      #get the newly inserted segment's ID
      $segmentID = $db->{q{mysql_insertid}};

      #split the CSV of the segment's items into an array
      @items = split(',', $segHash{$segment});

      foreach $item (@items)
      {
        $query = "INSERT INTO $dsSchema.$itemTable \
            (segmentationID, segmentID, itemID) \
            VALUES ($segID, $segmentID, $item)";
        $db->do($query);
      }
    }

    #if the user wants us to create an "All Others" segment
    if (defined($allothers))
    {

      #get array of all items in dimension that aren't segmented
      @unsegmentedItems = DSRseg_get_unsegmented_items($db, $dsSchema, $dim, $segID);

      #create the "All Others" segment, and get ID
      $query = "INSERT INTO $dsSchema.$segmentTable (segmentationID, name) \
          VALUES ($segID, 'All Others')";
      $db->do($query);
      $segmentID = $db->{q{mysql_insertid}};

      #add any unsegmented items to the "All Others" segment
      foreach $itemID (@unsegmentedItems)
      {
        $query = "INSERT INTO $dsSchema.$itemTable \
            (segmentationID, segmentID, itemID) \
            VALUES ($segID, $segmentID, $itemID)";
        $db->do($query);
      }
    }
  }

  #apply any segmentation rules that might exist
  DSRsegmentation_apply_seg_rules($db, $dsSchema, $dim, $segID);

  #update the last modified timestamp for the data source
  $query = "UPDATE dataSources SET lastModified=NOW() WHERE ID=$dsID";
  $db->do($query);

  #update any linked segmentations that might exist
  DSRsegmentation_update_linked_segs($db, $dsSchema, $dim, $segID);

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-11 col-md-8 col-lg-6 col-xl-5"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">$action Segmentation</DIV>
        <DIV CLASS="card-body">

          $message

          <P>&nbsp;<P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='display.cld?ds=$dsID&dim=$dim'"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

  utils_slack($activity);

#EOF
