#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::WebUtils;


#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $flowID = $q->param('f');
  $jobID = $q->param('j');

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #make sure we have read privs for this data flow
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to analyze this data flow.");
  }

  $flowName = prep_flow_id_to_name($prepDB, $flowID);

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterTableName = $jobID . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  print <<END_HTML;
<DIV CLASS="modal-dialog">
  <DIV CLASS="modal-content">

    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Row Profile</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">
      <DIV CLASS="table-responsive" STYLE="height:400px; overflow:auto;">
        <TABLE CLASS="table table-sm table-hover">
END_HTML

  #get basic row stats from information schema
  $query = "SELECT table_rows, avg_row_length FROM information_schema.TABLES \
      WHERE TABLE_SCHEMA='prep_data' AND TABLE_NAME='$masterTableName'";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($tableRows, $avgRowSize) = $dbOutput->fetchrow_array;

  #get count of invalid rows
  $query = "SELECT COUNT(*) FROM $masterTable WHERE valid > 0";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($invalidRows) = $dbOutput->fetchrow_array;

  #get counts of missing product, geography, times, and UPCs
  $query = "SELECT ID FROM $masterColTable WHERE type='product'";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($colIdx) = $dbOutput->fetchrow_array;

  $colName = "column_" . $colIdx;
  $query = "SELECT COUNT(*) FROM $masterTable WHERE ISNULL($colName)";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($prodMissing) = $dbOutput->fetchrow_array;

  $query = "SELECT ID FROM $masterColTable WHERE type='geography'";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($colIdx) = $dbOutput->fetchrow_array;

  $colName = "column_" . $colIdx;
  $query = "SELECT COUNT(*) FROM $masterTable WHERE ISNULL($colName)";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($geoMissing) = $dbOutput->fetchrow_array;

  $query = "SELECT ID FROM $masterColTable WHERE type='time'";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($colIdx) = $dbOutput->fetchrow_array;

  $colName = "column_" . $colIdx;
  $query = "SELECT COUNT(*) FROM $masterTable WHERE ISNULL($colName)";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($timeMissing) = $dbOutput->fetchrow_array;

  $query = "SELECT ID FROM $masterColTable WHERE type='upc'";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($colIdx) = $dbOutput->fetchrow_array;

  $colName = "column_" . $colIdx;
  $query = "SELECT COUNT(*) FROM $masterTable WHERE ISNULL($colName)";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($upcMissing) = $dbOutput->fetchrow_array;

  #output profile table
  print <<END_HTML;
          <TR>
            <TD STYLE="text-align:right; font-weight:bold; width:200px;">Rows:</TD>
            <TD>$tableRows</TD>
          </TR>
          <TR>
            <TD STYLE="text-align:right; font-weight:bold;">Average Row Size:</TD>
            <TD>$avgRowSize characters</TD>
          </TR>
          <TR>
            <TD STYLE="text-align:right; font-weight:bold;">Invalid Rows:</TD>
            <TD>$invalidRows</TD>
          </TR>
          <TR>
            <TD STYLE="text-align:right; font-weight:bold;">Missing Products:</TD>
            <TD>$prodMissing</TD>
          </TR>
          <TR>
            <TD STYLE="text-align:right; font-weight:bold;">Missing Geographies:</TD>
            <TD>$geoMissing</TD>
          </TR>
          <TR>
            <TD STYLE="text-align:right; font-weight:bold;">Missing Time Periods:</TD>
            <TD>$timeMissing</TD>
          </TR>
          <TR>
            <TD STYLE="text-align:right; font-weight:bold;">Missing UPCs:</TD>
            <TD>$upcMissing</TD>
          </TR>
END_HTML

  print <<END_HTML;
        </TABLE>
      </DIV>
    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-primary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-check-lg"></I> Done</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML

  $query = "UPDATE prep.jobs SET lastAction=NOW() WHERE ID=$jobID";
  $prepDB->do($query);

  prep_audit($prepDB, $userID, "Profiled rows", $flowID);
  utils_slack("PREP: $first $last profiled rows in $flowName");

#EOF
