#!/usr/bin/perl

###########################################################################
#
# This script looks for new data files that have been uploaded to an IDW
# node by Nielsen. It uses a flat file to keep track of what's already
# been detected in the past.
#
# When new data is detected, the script alerts the #devops channel in Koala's
# Slack and outputs a state file that lets future runs know an upload is in
# progress.
#
# After 2 hours of not detecting any new files being uploaded, the script
# decides that the data push is complete and sends a follow-up message to
# the #devops Slack channel to announce that. Then, it kicks off the
# monthly split process.
#
#########################################################################

use lib "/opt/apache/app/";

use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;



  #

  #add new files to our list of detected files

exit;

  $userID = $ARGV[0];

#  $dsID = $ARGV[0];

#  $dsSchema = "datasource_" . $dsID;


  #connect to the database
  $db = KAPutil_connect_to_database();

  #get all of the specified user's data sources
  $query = "SELECT ID FROM dataSources WHERE userID=$userID";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  while (($dsID) = $dbOutput->fetchrow_array)
  {
    push(@dataSources, $dsID);
  }

  foreach $dsID (@dataSources)
  {

    $dsName = ds_id_to_name($db, $dsID);
    $dsSchema = "datasource_" . $dsID;

    #get the product names
    %prodNameHash = dsr_get_item_name_hash($db, $dsSchema, "p");

    #get the UPC hash (we're cheating and assuming its attribute ID 1)
    %attrValHash = DSRattr_get_values_hash($db, $dsSchema, "p", 1);
    %attrValHash = reverse(%attrValHash);

    #build up our filename
    $filename = $dsName;
    $filename =~ s/\"//g;
    $filename =~ s/\'//g;
    $filename =~ s/\s//g;
    $filename =~ s/&//g;
    $filename =~ s/\(//g;
    $filename =~ s/\)//g;
    $filename = "$userID" . "_" . $filename . ".csv";

    open(DATA, "/data/nielsen/Beacon_Product_Ref_prdc_ref_281084.txt");
    open(OUTPUT, ">/data/beacon/$filename");

    $count = 0;

    while ($line = <DATA>)
    {
      @columns = split('\|', $line);
      if (defined($attrValHash{$columns[0]}))
      {
        $nitroName = $prodNameHash{$attrValHash{$columns[0]}};

        $category = $columns[3];
        $name = $columns[85];

        print OUTPUT "$columns[0],$nitroName,$name,$category\n";
      }

      $count++;
      if (($count % 5000) == 0)
      {
        $pct = $count / 4694374;
        $pct = $pct * 100;
        $pct = sprintf("%.1f", $pct);
        print("$pct% ($count / 4694374)\n");
      }
    }


    close(OUTPUT);

  }


#EOF
