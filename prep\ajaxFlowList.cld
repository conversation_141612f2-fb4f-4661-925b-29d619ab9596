#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $userOnly = $q->param('user');

  print("Expires: -1\n");
  print("Cache-Control: private, max-age=0");
  print("Content-type: application/json\n\n");

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #get the list of the user's data flows and output them
  @userFlows = prep_flow_list($prepDB, $kapDB, $userID, $acctType);
  $flows = join(',', @userFlows);

  #get the list of data flows that have one or more jobs in a error state
  $query = "SELECT flowID FROM prep.jobs WHERE validation='ERROR' OR state='ERROR'";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  while (($flowID) = $dbOutput->fetchrow_array)
  {
    $errorFlows{$flowID} = 1;
  }

  #get the list of data flows that are scheduled
  $query = "SELECT flowID FROM prep.schedule WHERE sched != 'never'";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  while (($flowID) = $dbOutput->fetchrow_array)
  {
    $scheduledFlows{$flowID} = 1;
  }

  #get the job count for data flows
  $query = "SELECT flowID, COUNT(ID) FROM prep.jobs GROUP BY flowID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  while (($flowID, $jobCount) = $dbOutput->fetchrow_array)
  {
    $flowJobCount{$flowID} = $jobCount;
  }

  #get any running job statuses for color block display
  $query = "SELECT flowID, state, exportedKoala FROM prep.jobs";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  while (($flowID, $jobState, $exportedKoala) = $dbOutput->fetchrow_array)
  {
    if (($jobState eq "LOADED") && ($exportedKoala == 0))
    {
      $flowJobState{$flowID} = "LOADED";
    }
    elsif (($jobState eq "LOADED") && ($exportedKoala > 0))
    {
      $flowJobState{$flowID} = "EXPORTED";
    }
    else
    {
      $flowJobState{$flowID} = $jobState;
    }
  }

  %userNames = utils_get_user_hash($kapDB);

  if ($userOnly > 0)
  {
    $query = "SELECT ID, name, source, lastRun, userID, description, dsID \
        FROM prep.flows WHERE userID=$userID ORDER BY name";
  }
  else
  {
    $query = "SELECT ID, name, source, lastRun, userID, description, dsID \
        FROM prep.flows WHERE ID IN ($flows) ORDER BY name";
  }

  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);

  print <<JSON_LABEL;
[
JSON_LABEL

  $count = 1;
  while (($flowID, $name, $type, $lastRun, $chanUserID, $description, $dsID) = $dbOutput->fetchrow_array)
  {
    $active = "";
    $jobState = $flowJobState{$flowID};

    if (($jobState eq "PARSE-WAIT") || ($jobState eq "DATATYPE-WAIT"))
    {
      $active = "waiting";
    }
    elsif ($errorFlows{$flowID} > 0)
    {
      $active = "error";
    }
    elsif ($jobState eq "LOADED")
    {
      $active = "loaded";
    }
    elsif ((length($jobState) > 0) && ($jobState ne "EXPORTED"))
    {
      $active = "running";
    }

    $jobs = $flowJobCount{$flowID};
    if ($dsID > 0)
    {
      $jobs = "$jobs <I STYLE='color:#007bff;' CLASS='bi bi-cloud-arrow-up' TITLE='Exporting to Koala Analytics'></I>";
    }

    if ($scheduledFlows{$flowID} > 0)
    {
      $jobs = "$jobs <I STYLE='color:#28a745;' CLASS='bi bi-calendar3' TITLE='Scheduled'></I>";
    }

    print <<JSON_LABEL;
    {
      "ID": $flowID,
      "active": "$active",
      "Flow Name": "$name",
      "Type": "$type",
      "Jobs": "$jobs",
      "Last Run": "$lastRun",
      "Owner": "$userNames{$chanUserID}",
      "Description": "$description"
    }
JSON_LABEL
    if ($count < $status)
    {
      print(",");
    }

    $count++;
  }

    print <<JSON_LABEL;
]
JSON_LABEL

#EOF
