#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use File::Listing qw (parse_dir);
use LWP::UserAgent;

use Lib::KoalaConfig;
use Lib::PrepSources;
use Lib::PrepUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $ftpserver = $q->param('ftpserver');
  $ftpuser = $q->param('ftpuser');
  $ftppass = $q->param('ftppass');
  $ftppath = $q->param('ftppath');

  #handle any special tag the user might be using for the FTP server
  $ftpserver = prep_source_expand_custom($ftpserver);

  #make sure the path is a directory
  $ftppath =~ m/(.*)\//;
  $ftppath = $1;

  #build up FTP URI
  $uri = "ftp://$ftpuser:$ftppass\@$ftpserver/$ftppath";

  #get directory listing
  $ua = LWP::UserAgent->new();
  $resp = $ua->get($uri);

  $rawListing = $resp->code . " " . $resp->message . "\n" . $resp->content;

  print <<END_HTML;
<DIV CLASS="modal-dialog modal-xl">
  <DIV CLASS="modal-content">

    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">FTP File Selection</H5>
    </DIV>

    <DIV CLASS="modal-body">
END_HTML

  #if an error happened...
  if ($resp->code != 200)
  {
    $error = $resp->message;
    print("ERROR: $error\n");
  }

  #else we got a valid directory listing, so we need to parse & display options
  else
  {
    print <<END_HTML;
      <DIV CLASS="table-responsive" STYLE='height:400px; overflow:auto;'>
        <TABLE CLASS='table table-sm table-striped'>
          <THEAD><TR>
            <TH>File</TH>
            <TH>Size</TH>
            <TH>Date</TH>
          </TR></THEAD>
END_HTML

    for (parse_dir($rawListing))
    {
      ($name, $type, $size, $mtime, $mode) = @$_;

      #format down size
      $size = $size / 1024;
      if ($size < 1024)
      {
        $size = sprintf("%.1f KB", $size);
      }
      else
      {
        $size = $size / 1024;
        if ($size < 1024)
        {
          $size = sprintf("%.1f MB", $size);
        }
        else
        {
          $size = $size / 1024;
          $size = sprintf("%.2f GB", $size);
        }
      }

      @timeArray = localtime($mtime);
      $timeArray[5] += 1900;
      $timeArray[4]++;
      $timeArray[4] = sprintf("%02d", $timeArray[4]);
      $timeArray[3] = sprintf("%02d", $timeArray[3]);
      $timeArray[2] = sprintf("%02d", $timeArray[2]);
      $timeArray[1] = sprintf("%02d", $timeArray[1]);
      $timeArray[0] = sprintf("%02d", $timeArray[0]);

      $timeStr = "$timeArray[5]-$timeArray[4]-$timeArray[3] $timeArray[2]:$timeArray[1]:$timeArray[0]";

      print <<END_HTML;
          <TR>
            <TD>
              <A CLASS="text-decoration-none" HREF="#" onClick="updateFile('$name')" data-bs-dismiss="modal">$name</A>
            </TD>
            <TD>$size</TD>
            <TD>$timeStr</TD>
          </TR>
END_HTML
    }

    print <<END_HTML;
        </TABLE>
      </DIV>
END_HTML
  }

  print <<END_HTML;
    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML

#EOF
