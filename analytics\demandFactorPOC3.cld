#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;
use Statistics::R;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#format currency for pretty HTML output
sub html_format_currency
{
  ($value) = @_;


  #go down to 2 decimal places
  $value = sprintf("%.2f", $value);

  if ($value < 0)
  {
    $value = abs($value);
    $formatStr = "<SPAN STYLE='color:red;'>(\$$value)</SPAN>";
  }
  else
  {
    $formatStr = "\$$value";
  }

  return($formatStr);
}



#-------------------------------------------------------------------------

#format number for pretty HTML output
sub html_format_number
{
  ($value, $decimals) = @_;


  #go down to 2 decimal places
  $formatStr = "%." . $decimals . "f";
  $value = sprintf($formatStr, $value);

  if ($value < 0)
  {
    $value = abs($value);
    $formatStr = "<SPAN STYLE='color:red;'>($value)</SPAN>";
  }
  else
  {
    $formatStr = "$value";
  }

  return($formatStr);
}



#-------------------------------------------------------------------------

#format number for pretty HTML output
sub analytics_lm_is_significant
{
  ($variable, @lines) = @_;


  foreach $line (@lines)
  {

    #if we have at least * on the end of the variable stats output line
    if ($line =~ m/^$variable\s+.*\s+.*\s+.*\s+.*\s+(\*)/)
    {
      return(1);
    }
  }

  return(0);
}



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Demand Factor POC</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<SCRIPT SRC="/fusioncharts-3.18.0/js/fusioncharts.js"></SCRIPT>
<SCRIPT SRC="/fusioncharts-3.18.0/js/themes/fusioncharts.theme.zune.js"></SCRIPT>


<SCRIPT>
function closeWarning()
{
  document.getElementById('warn-confidence').style.display = 'none';
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">Retail Analytics</A></LI>
    <LI CLASS="breadcrumb-item active">Demand Factor POC</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');
  $prodID = $q->param('p');
  $geoID = $q->param('g');

  #connect to the database
  $db = KAPutil_connect_to_database();

  $R = Statistics::R->new();
  $R->startR or die("$$\n");

  $dsSchema = "datasource_" . $dsID;

  %prodNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "p");
  %geoNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "g");

  print_html_header();

  #make sure we have reaad privs for this data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to view analyses in this data source.");
  }

  #get IDs for as many weeks of data as we have
  $query = "SELECT ID FROM $dsSchema.timeperiods \
      WHERE duration=1 AND type=30 ORDER BY endDate";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  if ($status < 5)
  {
    exit_error("Not enough 1-week time periods: you only have $status");
  }
  while (($timeID) = $dbOutput->fetchrow_array)
  {
    $timeIDstring .= "$timeID,";
  }
  chop($timeIDstring);

  #determine measures containing Avg Unit Price
  $query = "SELECT ID FROM $dsSchema.measures \
      WHERE name LIKE 'Avg Retail Price' OR name LIKE 'Avg Unit Price'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  if ($status < 1)
  {
    exit_error("Can't find an Avg Retail Price/Average Retail measure in this data source");
  }
  ($priceMeasID) = $dbOutput->fetchrow_array;

  $query = "SELECT ID FROM $dsSchema.measures WHERE name LIKE '\%ACV Reach'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  if ($status < 1)
  {
    exit_error("Can't find \%ACV Reach measure in this data source");
  }
  ($ACVMeasID) = $dbOutput->fetchrow_array;

  $query = "SELECT ID FROM $dsSchema.measures \
      WHERE name LIKE 'Disp w/o Feat Units'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  if ($status < 1)
  {
    exit_error("Can't find Disp w/o Feat Units measure in this data source");
  }
  ($dispPromoMeasID) = $dbOutput->fetchrow_array;

  $query = "SELECT ID FROM $dsSchema.measures \
      WHERE name LIKE 'Feat & Disp Units'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  if ($status < 1)
  {
    exit_error("Can't find Feat & Disp Units measure in this data source");
  }
  ($featDispPromoMeasID) = $dbOutput->fetchrow_array;

  $query = "SELECT ID FROM $dsSchema.measures \
      WHERE name LIKE 'Feat w/o Disp Units'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  if ($status < 1)
  {
    exit_error("Can't find Feat w/o Disp Units measure in this data source");
  }
  ($featPromoMeasID) = $dbOutput->fetchrow_array;

  $query = "SELECT ID FROM $dsSchema.measures WHERE name LIKE 'Price Decr Units'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  if ($status < 1)
  {
    exit_error("Can't find Price Decr Units measure in this data source");
  }
  ($pricePromoMeasID) = $dbOutput->fetchrow_array;

  $query = "SELECT ID FROM $dsSchema.measures WHERE name LIKE 'Units'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  if ($status < 1)
  {
    exit_error("Can't find a Units measure in this data source");
  }
  ($unitMeasID) = $dbOutput->fetchrow_array;

  #retrieve data for selected product and geography
  $unitColName = "measure_" . $unitMeasID;
  $query = "SELECT timeID, measure_$priceMeasID, measure_$ACVMeasID, measure_$dispPromoMeasID, measure_$featDispPromoMeasID, measure_$featPromoMeasID, measure_$pricePromoMeasID, $unitColName \
      FROM $dsSchema.facts \
      WHERE productID=$prodID AND geographyID=$geoID AND timeID IN ($timeIDstring) AND $unitColName > 0 \
      ORDER BY FIELD(timeID, $timeIDstring)";
  $db_dataFrame = $db->prepare($query);
  $status = $db_dataFrame->execute;

  if ($status < 5)
  {
    if ($status < 1)
    {
      $status = "none";
    }
    exit_error("Not enough data points for this product/geo combination: you have $status");
  }

  #build up independent (price, promos, ACV, etc) and dependent (y, units) vectors
  $vectorPrice = "price = c(";
  $vectorACV = "acv = c(";
  $vectorDispPromo = "disppromo = c(";
  $vectorDispFeatPromo = "dispfeatpromo = c(";
  $vectorFeatPromo = "featpromo = c(";
  $vectorPricePromo = "pricepromo = c(";
  $vectorUnits = "y = c(";
  while (($timeID, $price, $acvReach, $dispPromo, $featDispPromo, $featPromo, $pricePromo, $units) = $db_dataFrame->fetchrow_array)
  {
    if (defined($price))
    {
      $vectorPrice .= "$price,";
    }
    else
    {
      $vectorPrice .= "NA,";
    }

    if (defined($acvReach))
    {
      $vectorACV .= "$acvReach,";
    }
    else
    {
      $vectorACV .= "NA,";
    }

    if ($dispPromo > 0)
    {
      $vectorDispPromo .= "$dispPromo,";
    }
    else
    {
      $vectorDispPromo .= "0,";
    }

    if ($featDispPromo > 0)
    {
      $vectorDispFeatPromo .= "$featDispPromo,";
    }
    else
    {
      $vectorDispFeatPromo .= "0,";
    }

    if ($featPromo > 0)
    {
      $vectorFeatPromo .= "$featPromo,";
    }
    else
    {
      $vectorFeatPromo .= "0,";
    }

    if ($pricePromo > 0)
    {
      $vectorPricePromo .= "$pricePromo,";
    }
    else
    {
      $vectorPricePromo .= "0,";
    }

    if (defined($units))
    {
      $vectorUnits .= "$units,";
    }
    else
    {
      $vectorUnits .= "NA,";
    }
  }

  chop($vectorPrice);   chop($vectorUnits);   chop($vectorACV);
  chop($vectorDispPromo);  chop($vectorDispFeatPromo);
  chop($vectorFeatPromo);  chop($vectorPricePromo);
  $vectorPrice .= ")";
  $vectorACV .= ")";
  $vectorDispPromo .= ")";
  $vectorDispFeatPromo .= ")";
  $vectorFeatPromo .= ")";
  $vectorPricePromo .= ")";
  $vectorUnits .= ")";

  #send data vectors to R
  $R->send($vectorPrice);
  $R->send($vectorACV);
  $R->send($vectorDispPromo);
  $R->send($vectorDispFeatPromo);
  $R->send($vectorFeatPromo);
  $R->send($vectorPricePromo);
  $R->send($vectorUnits);

  #run linear regression model in R
  $cmd = "m <- lm(y ~ price + acv + disppromo + dispfeatpromo + featpromo + pricepromo)";
  #print STDERR "$cmd\n";
  $R->send($cmd);
  $R->send("summary(m)");
  $output = $R->read();
  #print STDERR "$output\n";

  #parse the data returned by R to get our results
  @lines = split(/\n/, $output);

  #peel off the top 4 lines about the call that we don't use
  splice(@lines, 0, 4);

  #run through the results, and determine which variables are significant
  $significantModelElements = "";
  if (analytics_lm_is_significant("price", @lines))
  {
    $significantModelElements .= "price +";
  }
  if (analytics_lm_is_significant("acv", @lines))
  {
    $significantModelElements .= " acv +";
  }
  if (analytics_lm_is_significant("disppromo", @lines))
  {
    $significantModelElements .= " disppromo +";
  }
  if (analytics_lm_is_significant("dispfeatpromo", @lines))
  {
    $significantModelElements .= " dispfeatpromo +";
  }
  if (analytics_lm_is_significant("featpromo", @lines))
  {
    $significantModelElements .= " featpromo +";
  }
  if (analytics_lm_is_significant("pricepromo", @lines))
  {
    $significantModelElements .= " pricepromo +";
  }
  chop($significantModelElements);

  if (length($significantModelElements) < 1)
  {
    exit_error("No statistically significant model can be constructed for this item");
  }


  #re-construct the model with only significant variables
  $R->send("m <- lm(y ~ $significantModelElements)");
  $R->send("summary(m)");
  $output = $R->read();

  #parse the data returned by R to get our results
  @modelLines = split(/\n/, $output);

  #peel off the top 4 lines about the call that we don't use
  splice(@modelLines, 0, 4);

  #get the R-square value for the model
  foreach $line (@modelLines)
  {
    if ($line =~ m/Adjusted R-squared:\s+(.*)/)
    {
      $R2 = $1;
      $R2 = $R2 * 100;
    }
  }

  #get the price impact coefficient
  $priceCoefficient = 0;
  foreach $line (@modelLines)
  {
    if ($line =~ m/^price\s+(.*?)\s+/)
    {
      $priceCoefficient = $1;
    }
  }
  $priceCoefficient = abs($priceCoefficient);

  #get the distribution impact coefficient
  $acvCoefficient = 0;
  foreach $line (@modelLines)
  {
    if ($line =~ m/^acv\s+(.*?)\s+/)
    {
      $acvCoefficient = $1;
    }
  }

  #get the display promo impact coefficient
  $dispCoefficient = 0;
  foreach $line (@modelLines)
  {
    if ($line =~ m/^disppromo\s+(.*?)\s+/)
    {
      $dispCoefficient = $1;
    }
  }

  #get the display & featured promo impact coefficient
  $dispfeatCoefficient = 0;
  foreach $line (@modelLines)
  {
    if ($line =~ m/^dispfeatpromo\s+(.*?)\s+/)
    {
      $dispfeatCoefficient = $1;
    }
  }

  #get the feature promo impact coefficient
  $featCoefficient = 0;
  foreach $line (@modelLines)
  {
    if ($line =~ m/^featpromo\s+(.*?)\s+/)
    {
      $featCoefficient = $1;
    }
  }

  #get the price reduction promo impact coefficient
  $pricePromoCoefficient = 0;
  foreach $line (@modelLines)
  {
    if ($line =~ m/^pricepromo\s+(.*?)\s+/)
    {
      $pricePromoCoefficient = $1;
    }
  }

  #figure out what % of R^2 each coefficient represents
  $coefTotal = $priceCoefficient + $acvCoefficient + $dispCoefficient +
      $dispfeatCoefficient + $featCoefficient + $pricePromoCoefficient;

  if ($priceCoefficient > 0)
  {
    $pricePct = $priceCoefficient / $coefTotal;
    $pricePct = $pricePct * $R2;
  }

  if ($acvCoefficient > 0)
  {
    $acvPct = $acvCoefficient / $coefTotal;
    $acvPct = $acvPct * $R2;
  }

  if ($dispCoefficient > 0)
  {
    $dispPct = $dispCoefficient / $coefTotal;
    $dispPct = $dispPct * $R2;
  }

  if ($dispfeatCoefficient > 0)
  {
    $dispFeatPct = $dispfeatCoefficient / $coefTotal;
    $dispFeatPct = $dispFeatPct * $R2;
  }

  if ($featCoefficient > 0)
  {
    $featPct = $featCoefficient / $coefTotal;
    $featPct = $featPct * $R2;
  }

  if ($pricePromoCoefficient > 0)
  {
    $pricePromoPct = $pricePromoCoefficient / $coefTotal;
    $pricePromoPct = $pricePromoPct * $R2;
  }

  #the value for "other" is what the model doesn't explain
  $other = 100 - $R2;

  #get the p-value for the model
  foreach $line (@modelLines)
  {
    if ($line =~ m/\s+p-value:\s+(.*)/)
    {
      $pValue = $1;
    }
  }

  print <<END_HTML;
<DIV CLASS="container-fluid">
END_HTML

  #see if we need to display a model accuracy warning based on the p-value
  if ($pValue > 0.05)
  {
    print <<END_HTML;
  <DIV ID="warn-confidence" CLASS="alert alert-warning">
  <DIV CLASS="text-center"><STRONG>WARNING: </STRONG>This is an extremely low-confidence model, and should be used with caution.</DIV>
  </DIV>
END_HTML
  }

  print <<END_HTML;
  <DIV CLASS="row">
    <DIV CLASS="col text-center">

      <P>&nbsp;</P>
      <DIV CLASS="card">
        <DIV CLASS="card-body">

          <DIV ID="div-chart-demand-factors" STYLE="height:602px;"></DIV>
<SCRIPT>
FusionCharts.ready(function()
{
  let factorsChart = new FusionCharts(
  {
    type: 'waterfall2d',
      renderAt: 'div-chart-demand-factors',
      width: '95%', height: '600',
      dataFormat: 'json',
      dataSource:
      {
        'chart':
        {
          'caption': 'Impact of Demand Factors on Sales',
          'subcaption': '$prodNameHash{$prodID}, $geoNameHash{$geoID}',
          'captionFontSize': '24',
          'baseFontSize': '14',
          'valueFontSize': '14',
          'decimals': '1',
          'numbersuffix': '%',
          'yAxisName': '% Impact',
          'showSumAtEnd' : '0',
          'theme': 'zune'
        },
        'data': [
        {
          label: 'Display Promotion',
          value: '$dispPct'
        },
        {
          label: 'Display & Feature Promotion',
          value: '$dispFeatPct'
        },
        {
          label: 'Feature Promotion',
          value: '$featPct'
        },
        {
          label: 'Price Promotion',
          value: '$pricePromoPct'
        },
        {
          label: 'Price',
          value: '$pricePct'
        },
        {
          label: 'Distribution',
          value: '$acvPct'
        },
        {
          label: 'Other',
          value: '$other'
        }
       ]
     }
   });
   factorsChart.render();
 });
 </SCRIPT>
        </DIV>
      </DIV>

    </DIV>
  </DIV>

  <DIV CLASS="row">
    <DIV CLASS="col">
      <P>&nbsp;</P>

      <DIV ID="details-collapse">
        <DIV CLASS="card border-primary mx-auto">
          <DIV CLASS="card-header bg-primary text-white">
            <A CLASS="card-link text-white text-decoration-none" data-bs-toggle="collapse" HREF="#collapse-details-table">Model Details</A>
            <I CLASS="bi bi-chevron-down"></I>
          </DIV>
          <DIV ID="collapse-details-table" CLASS="collapse" data-parent="#details-collapse">
            <DIV CLASS="card-body">
              <PRE>
END_HTML

  $idx = 0;
  foreach $line (@lines)
  {
    print("$idx: $line\n");
    $idx++;
  }

  $idx = 0;
  foreach $line (@modelLines)
  {
    print("$idx: $line\n");
    $idx++;
  }

  print <<END_HTML;
              </PRE>
            </DIV>
          </DIV>
        </DIV>
      </DIV>
      <P>

    </DIV>
  </DIV>

</DIV>
END_HTML

  print_html_footer();

#EOF
