#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepRecipes;
use Lib::PrepSources;
use Lib::PrepUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($kapDB);

  print <<END_HTML;
<!DOCTYPE html>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Run Data Prep Flow</TITLE>
<LINK REL="SHORTCUT ICON" HREF="$Lib::KoalaConfig::kapHostURL/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
let statusTimer = setInterval(function(){displayStatus()}, 5000);

function displayStatus()
{
  let statElements;
  let url = 'ajaxAPI.cld?svc=job_op_status&f=$flowID&j=$jobID&as=1';

  \$.get(url, function(data, status)
  {
    let statusText = data;
    let opTitle;

    statElements = statusText.split('|');

    opTitle = statElements[0];
    document.getElementById('div-op-title').innerHTML = opTitle;
  });

  //----------------------------------------------

  let pct;
  url = 'xhrExtractStatus.cld?j=$jobID';

  \$.get(url, function(data, status)
  {
    statusText = data;

    if (statusText.length < 2)
    {
      statusText = '0% Running Data Flow';
    }

    if (statusText.includes('0|Loading Koala data source'))
    {
      location.href='main.cld';
    }

    if (statusText.search('ERR') == 0)
    {
      clearInterval(statusTimer);
      \$('#progress-bar').css('width', '100%');
      \$('#progress-bar').addClass('bg-danger');
      document.getElementById('progress-bar').innerHTML = 'ERROR';
      statElements = statusText.split('|');
      document.getElementById('progressDiv').innerHTML = statElements[1];
      return;
    }

    if (statusText.includes('LOAD-MANUAL')) //manual source load redirect
    {
      \$('#detailedDiv').hide();
      \$('#progressDiv').hide();
      clearInterval(statusTimer);
      location.href='sourceManual.cld?f=$flowID&j=$jobID&a=r';
    }

    if (statusText.includes('LOAD-PASTE')) //copy/paste source load redirect
    {
      \$('#detailedDiv').hide();
      \$('#progressDiv').hide();
      clearInterval(statusTimer);
      location.href='sourcePaste.cld?f=$flowID&j=$jobID&a=r';
    }

    if (statusText.includes('DONE-RUN'))
    {
      \$('#progress-bar').css('width', '100%');
      document.getElementById('progress-bar').innerHTML = 'DONE';
      \$('#progress-bar').removeClass('progress-bar-striped');
      \$('#detailedDiv').hide();
      \$('#progressDiv').hide();
      clearInterval(statusTimer);
      location.href='flowViewData.cld?f=$flowID&j=$jobID&a=$action';
    }
    else if (statusText.includes('DONE-KL-EXPORT-RUN'))
    {
      \$('#progress-bar').css('width', '100%');
      document.getElementById('progress-bar').innerHTML = 'DONE';
      \$('#progress-bar').removeClass('progress-bar-striped');
      \$('#detailedDiv').hide();
      \$('#progressDiv').hide();
      clearInterval(statusTimer);
      location.href='main.cld';
    }
    else
    {
      statElements = statusText.split('|');

      pct = statElements[0];
      if (pct.includes('DONE'))
      {
        pct = 100;
      }

      if (statElements.length == 2)
      {
        detailText = '';
        statusText = statElements[1];
      }
      else if (statElements.length == 3)
      {
        detailText = statElements[1];
        statusText = statElements[2];
      }

      \$('#progress-bar').css('width', pct+'%');

      document.getElementById('progress-bar').innerHTML = pct + '%';
      document.getElementById('detailedDiv').innerHTML = detailText;
      document.getElementById('progressDiv').innerHTML = statusText;
    }
  });


  url = 'xhrExtractStatus.cld?j=$jobID&o=1';
  \$.get(url, function(data, status)
  {
    statusText = data;

    if (statusText.includes('LOADED'))
    {
      \$('#overall-progress').hide();
    }
    else
    {
      statElements = statusText.split('|');

      pct = statElements[0];
      statusText = statElements[1];

      if (pct > 5)
      {
        \$('#overall-progress-bar').css('width', pct+'%');
      }

      document.getElementById('overall-progress-bar').innerHTML = pct + '% Complete';
    }
  });
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($kapDB, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="$Lib::KoalaConfig::kapHostURL/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">Data Prep</A></LI>
    <LI CLASS="breadcrumb-item">$flowName</LI>
    <LI CLASS="breadcrumb-item active">Run Data Flow</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------
#
# Output the HTML for the web page that displays the status dialog
#

sub print_status_html
{
  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-10 col-lg-7 col-xl-6"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Running Data Flow</DIV>
        <DIV CLASS="card-body">

          <DIV CLASS="progress">
            <DIV ID="overall-progress-bar" CLASS="progress-bar bg-success" role="progressbar" STYLE="width:5%;">
              0%
            </DIV>
          </DIV>

          <P>
          <DIV ID="div-op-title"></DIV>
          <DIV CLASS="progress">
            <DIV ID="progress-bar" CLASS="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" STYLE="width:5%;">
              0%
            </DIV>
          </DIV>

          <DIV CLASS="text-center">
            <DIV ID="progressDiv">Running Data Flow</DIV>
            <DIV ID="detailedDiv"></DIV>
          </DIV>

          <P>&nbsp;</P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" ID="btnAsync" onClick="location.href='main.cld'"><I CLASS="bi bi-speedometer"></I> Be More Productive</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get the CGI input variables
  $flowID = $q->param('f');
  $jobID = $q->param('j');
  $clean = $q->param('c');

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #if we're being called after an interactive op, clean the status
  if ($clean == 1)
  {
    $query = "UPDATE prep.jobs SET opInfo = '' WHERE ID=$jobID";
    $prepDB->do($query);
  }

  $flowName = prep_flow_id_to_name($prepDB, $flowID);

  #if we're re-joining an already-running job
  if ($jobID > 0)
  {
    print_html_header();
    print_status_html();

    exit;
  }

  #make sure we have read privs for this data source
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs eq "N")
  {
    print_html_header();
    exit_error("You don't have privileges to run this data flow.");
  }

  #make sure the analytics node isn't out of storage
  $usagePct = KAPutil_get_org_quota_used($kapDB, $userID);
  if ($usagePct > 99)
  {
    print_html_header();
    exit_error("Your Koala Analytics cloud is out of storage - please contact your Koala account manager to order more.")
  }

  #make sure the prep cloud isn't out of storage
  $usagePct = prep_flow_storage($acctType);
  if ($usagePct > 99)
  {
    print_html_header();
    exit_error("Your Koala Data Prep cloud is out of storage - please contact your Koala account manager to order more.")
  }

  #if the system is overloaded, have the user try again later
  $activeJobs = PrepUtils_active_job_count($prepDB);
  if (($activeJobs >= ($Lib::KoalaConfig::prepCores * 1.25)) && ($acctType < 10))
  {
    print_html_header();
    exit_warning("Whoa! It looks like your Data Prep cloud is being overused. Wait a little bit, and then try again.")
  }

  #don't let a single user help themselves to all resources
  if (($activeJobs >= ($Lib::KoalaConfig::prepCores * 0.75)) && ($acctType < 10))
  {
    $query = "SELECT COUNT(*) FROM prep.jobs \
        WHERE state NOT IN ('LOADED', 'ERROR') AND userID=$userID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($runningJobs) = $dbOutput->fetchrow_array;
    if ($runningJobs > 0)
    {
      print_html_header();
      exit_warning("Whoa! It looks like your Data Prep cloud is heavily loaded, and you already have at least one active job. Wait for that job to finish, and then try again.")
    }
  }

  #don't let the user run a job if there's already another job updating the
  #same Koala data source
  $query = "SELECT dsID FROM prep.flows WHERE ID=$flowID";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($dsID) = $dbOutput->fetchrow_array;

  if ($dsID > 0)
  {

    #get a list of all flows that update our data source
    #NB: we're not doing this as a JOIN query in anticipation of a day when
    #    a data flow updates multiple data sources
    $query = "SELECT ID FROM prep.flows WHERE dsID=$dsID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    $siblingFlows = "";
    while (($siblingFlowID) = $dbOutput->fetchrow_array)
    {
      $siblingFlows .= "$siblingFlowID,";
    }
    chop($siblingFlows);

    #see if there's any currently running job in one of those flows
    $query = "SELECT flowID FROM prep.jobs \
        WHERE (state != 'LOADED' AND state NOT LIKE 'ERROR%') AND flowID IN ($siblingFlows)";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($siblingFlowID) = $dbOutput->fetchrow_array;

    if ($siblingFlowID > 0)
    {
      $siblingFlowName = prep_flow_id_to_name($prepDB, $siblingFlowID);
      $dsName = ds_id_to_name($kapDB, $dsID);
      print_html_header();
      exit_warning("Another job that updates the <STRONG>$dsName</STRONG> data source is already running in $siblingFlowName. Please wait for it to finish before running a new job in this flow.")
    }

    #if we're updating a data source that already has something running in it,
    #error out
    $ok = DSRutil_operation_ok($kapDB, $dsID, 0, "DS-UPDATE");
    if ($ok != 1)
    {
      $dsName = ds_id_to_name($kapDB, $dsID);
      print_html_header();
      exit_warning("Another job is already using the <STRONG>$dsName</STRONG> data source - please wait until it finishes and try again later.")
    }
  }

  $jobID = prep_flow_create_job($prepDB, $flowID, $userID);

  print_html_header();

  utils_slack("PREP: $first $last ran data flow $flowName");

  #fork a new process to do the actual work in the background
  $SIG{CHLD} = "IGNORE";
  if ($pid = fork())
  {
    #parent process

    print_status_html();
  }
  else
  {
    #child process

    #let Apache know not to wait on the child process
    close(STDIN);
    close(STDOUT);

    #redirect STDERR to the Koala error log
    close(STDERR);
    open(STDERR, ">>/opt/apache/htdocs/tmp/koala_error.log");
    select(STDERR);
    $| = 1;

    #reconnect to the database
    $kapDB = KAPutil_connect_to_database();
    $prepDB = PrepUtils_connect_to_database();

    #grab the initial info we need to start running the flow
    $query = "SELECT source FROM prep.flows WHERE ID=$flowID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($type) = $dbOutput->fetchrow_array;


    #---------------------------------------------------------------------------
    #
    # Handle sources that require manual intervention
    #

    #get the raw source data using the selected method
    if ($type eq "Manual")
    {
      $query = "UPDATE prep.jobs \
          SET opInfo='LOAD-MANUAL', lastAction=NOW(), state='LOAD-MANUAL' \
          WHERE ID=$jobID";
      $prepDB->do($query);

      #now we wait for the manual data load to finish
      $opInfo = "LOAD-MANUAL";
      while ($opInfo eq "LOAD-MANUAL")
      {
        $query = "SELECT opInfo FROM prep.jobs WHERE ID=$jobID";
        $dbOutput = $prepDB->prepare($query);
        $dbOutput->execute;
        ($opInfo) = $dbOutput->fetchrow_array;

        sleep(10);
      }
    }
    elsif ($type eq "Paste")
    {
      $query = "UPDATE prep.jobs \
          SET opInfo='LOAD-PASTE', lastAction=NOW(), state='LOAD-PASTE' \
          WHERE ID=$jobID";
      $prepDB->do($query);

      #now we wait for the manual data load to finish
      $opInfo = "LOAD-PASTE";
      while ($opInfo eq "LOAD-PASTE")
      {
        $query = "SELECT opInfo FROM prep.jobs WHERE ID=$jobID";
        $dbOutput = $prepDB->prepare($query);
        $dbOutput->execute;
        ($opInfo) = $dbOutput->fetchrow_array;

        sleep(10);
      }
    }

    #run the data flow
    prep_run_flow($prepDB, $kapDB, $userID, $flowID, $jobID);

    #since we just did a run, we want to update the data used by the
    #scheduler to determine when new data is available
    prep_flow_update_schedule_status($prepDB, $flowID);
  }

#EOF
