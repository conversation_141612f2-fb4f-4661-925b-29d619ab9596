#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::AInsights::AInsights;
use Lib::AInsights::Utils;



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get the CGI input variables
  $priceModelID = $q->param('pm');
  $dsID = $q->param('ds');
  $prodID = $q->param('p');
  $geoID = $q->param('g');
  $schema = $q->param('s');
  $AI_zeroedElasticity = $q->param('ze');
  $AI_noElasticityGraph = $q->param('noe');

  $dsSchema = "datasource_" . $dsID;

  AInsights_Utils_initialize_constants($priceModelID);

  #connect to the database
  $db = KAPutil_connect_to_database();

  print("Content-type: text/plain\n\n");

  #make sure we have privs to at least view this model
  $privs = AInsights_rights($db, $userID, $priceModelID, $acctType);
  if ($privs eq "N")
  {
    print("ERROR: User doesn't have view privileges on this model.");
    exit;
  }

  if ($schema == 1)
  {
    print <<JSON_LABEL;
[{
    "name": "Date",
    "type": "date",
    "format": "%Y-%m-%d"
},
JSON_LABEL

    if (!$AI_noElasticityGraph)
    {
      print <<JSON_LABEL;
{
    "name": "Elasticity",
    "type": "number"
},
JSON_LABEL
    }

  print <<JSON_LABEL;
{
    "name": "Units Sold",
    "type": "number"
},
{
    "name": "Avg Retail Price",
    "type": "number"
},
{
    "name": "Distribution",
    "type": "number"
},
{
    "name": "Promotion",
    "type": "number"
}]
JSON_LABEL
  }

  #else we're writing out the multi-variate data to be graphed
  else
  {

    #figure out the chrono-order of the time periods
    $query = "SELECT ID, endDate FROM $dsSchema.timeperiods \
        WHERE duration=1 AND type=30 ORDER BY endDate ASC";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($timeID, $endDate) = $dbOutput->fetchrow_array)
    {
      $orderedTimeIDStr .= "$timeID,";

      $endDate =~ m/^(.*?) /;
      $endDateHash{$timeID} = $1;
    }
    chop($orderedTimeIDStr);

    #pull the data from the stored model info
    $query = "SELECT timeID, units, avgPrice, dist, promo, elasticity \
        FROM $dsSchema.$AInsightsItemCube \
        WHERE productID='$prodID' AND geographyID=$geoID AND outlier = 0 \
        ORDER BY FIELD(timeID, '$orderedTimeIDStr')";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);

    #run through cube data and output to user
    while (($timeID, $units, $price, $dist, $promo, $elasticity) = $dbOutput->fetchrow_array)
    {
      $units = sprintf("%.1f", $units);
      $price = sprintf("%.2f", $price);
      $dist = sprintf("%.1f", $dist);
      $promo = sprintf("%.1f", $promo);
      $elasticity = sprintf("%.2f", $elasticity);

      if ($AI_zeroedElasticity)
      {
        $elasticity = 0;
      }

      if ($AI_noElasticityGraph)
      {
        $json .= "[\"$endDateHash{$timeID}\", $units, $price, $dist, $promo],\n";
      }
      else
      {
        $json .= "[\"$endDateHash{$timeID}\", $elasticity, $units, $price, $dist, $promo],\n";
      }

    }
    chop($json);  chop($json);
    print("[$json]\n");
  }

#EOF
