#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::DataSources;
use Lib::Reports;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $telemetryType = $q->param('t');
  $runID = $q->param('r');

  $runID = utils_sanitize_integer($runID);

  #connect to the database
  $db = KAPutil_connect_to_database();

  #set up the title and SQL query depending on the type of telemetry
  if ($telemetryType eq "update")
  {
    $query = "SELECT telemetry FROM audit.telemetry_data WHERE ID=$runID";
    $title = "Data Source Telemetry";
  }
  elsif ($telemetryType eq "odbc")
  {
    $query = "SELECT telemetry FROM audit.telemetry_odbc WHERE ID=$runID";
    $title = "ODBC Telemetry";
  }

  #get the telemetry data from the database
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($telemetry) = $dbOutput->fetchrow_array;

  print <<END_HTML;
<DIV CLASS="modal-header">
  <H5 CLASS="modal-title">$title</H5>
  <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
</DIV>

<DIV CLASS="modal-body">
  <DIV CLASS="card" STYLE="height:50vh; overflow-y:auto;">
    <DIV CLASS="card-body">
      <PRE STYLE="font-size:12px; background-color:white; border:0px; overflow:visible;">
$telemetry
      </PRE>
    </DIV>
  </DIV>
</DIV>

<DIV CLASS="modal-footer">
  <DIV CLASS="text-center">
    <BUTTON CLASS="btn btn-primary" data-bs-dismiss="modal"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
  </DIV>
</DIV>
END_HTML


#EOF
