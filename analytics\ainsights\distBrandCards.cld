#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::DataSources;
use Lib::AInsights::AInsights;
use Lib::AInsights::Utils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if ((length($email) < 1) && (length($credentials) < 1))
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #get CGI parameters
  $priceModelID = $q->param('m');
  $block = $q->param('b');
  $geoID = $q->param('g');
  $credentials = $q->param('auth');

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print($session->header());

  #connect to the database
  $db = KAPutil_connect_to_database();

  #TODO: check credentials

  $modelName = AInsights_ID_to_name($db, $priceModelID);
  $dsName = ds_id_to_name($db, $dsID);
  $dsID = AInsights_get_dsID($db, $priceModelID);

  $dsSchema = "datasource_" . $dsID;

  AInsights_Utils_initialize_constants($priceModelID);

  $ownBrandID = AInsights_Utils_get_own_brand_id($db, $priceModelID);
  $brandSegID = AInsights_Utils_get_brand_seg_id($db, $priceModelID);



  #------------------- Distribution Trends -------------------------


  if ($block eq 'trends')
  {
    $insight = AInsights_Utils_get_insight_html($db, $dsSchema,
        "dist", "brand", "trends", $geoID);

    print <<END_HTML;
          <H4>Distribution Trends</H4>
          <P>
          $insight

          <DIV ID="chart-line-trends"></DIV>
          <SCRIPT>
            let trendsChart = new FusionCharts({'type': 'MSLine', 'width': '99%', 'height': '450', 'dataFormat': 'json'});
            trendsChart.setJSONUrl('ajaxBrandDistCharts.cld?pm=$priceModelID&c=trends&g=$geoID&auth=$credentials');
            trendsChart.render('chart-line-trends');
          </SCRIPT>
END_HTML
  }


#------------------- Distribution of all brands in geo -----------------------

  elsif ($block eq "brands_dist")
  {
    %geoNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "g");
    print <<END_HTML;
        <H4>Distribution of Brands in $geoNameHash{$geoID}</H4>
        <P>
END_HTML

    #get number of bars we're going to graph, and size chart appropriately
    $query = "SELECT COUNT(avgDist52) FROM $dsSchema.$AInsightsBrandTable \
        WHERE avgDist52 > 0 AND geographyID=$geoID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($barsToGraph) = $dbOutput->fetchrow_array;
    $chartHeight = $barsToGraph * 20 + 75;

    $insight = AInsights_Utils_get_insight_html($db, $dsSchema,
        "dist", "brand", "brands", $geoID);

    print <<END_HTML;
        $insight
        <DIV ID="chart-bar-brands"></DIV>
        <SCRIPT>
          let brandDistChart = new FusionCharts({'type': 'bar2d', 'width': '100%', 'height': '$chartHeight', 'dataFormat': 'json'});
          brandDistChart.setJSONUrl('ajaxBrandDistCharts.cld?pm=$priceModelID&c=brands_dist&g=$geoID&auth=$credentials');
          brandDistChart.render('chart-bar-brands');
        </SCRIPT>
END_HTML
  }


#------------------- Own Brand Geographical Distribution -----------------------

  elsif ($block eq "brand_geos")
  {
    print <<END_HTML;
        <H4>$ownBrandName Distribution Across Geographies</H4>
        <P>
END_HTML

    #get number of bars we're going to graph, and size chart appropriately
    $query = "SELECT COUNT(DISTINCT geographyID) FROM $dsSchema.$AInsightsBrandTable \
        WHERE avgDist52 > 0 AND brandID = $ownBrandID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($barsToGraph) = $dbOutput->fetchrow_array;
    $chartHeight = $barsToGraph * 20 + 75;

    $insight = AInsights_Utils_get_insight_html($db, $dsSchema,
        "dist", "brand", "geos", 0);

    print <<END_HTML;
        $insight
        <DIV ID="chart-bar-geographies"></DIV>
        <SCRIPT>
          let ownBrandGeoChart = new FusionCharts({'type': 'bar2d', 'width': '100%', 'height': '$chartHeight', 'dataFormat': 'json'});
          ownBrandGeoChart.setJSONUrl('ajaxBrandDistCharts.cld?pm=$priceModelID&c=brand_geos&g=$geoID&auth=$credentials');
          ownBrandGeoChart.render('chart-bar-geographies');
        </SCRIPT>
END_HTML
  }


#------------------- Own Brand Item Distribution -----------------------

  if ($block eq "brand_items")
  {
    print <<END_HTML;
        <H4>$ownBrandName Item Distribution</H4>
        <P>
END_HTML

    #get number of bars we're going to graph, and size chart appropriately
    $query = "SELECT COUNT(productID) FROM $dsSchema.$AInsightsItemTable \
        WHERE avgDist52 > 0 AND brandID = $ownBrandID AND geographyID=$geoID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($barsToGraph) = $dbOutput->fetchrow_array;
    $chartHeight = $barsToGraph * 20 + 85;

    $insight = AInsights_Utils_get_insight_html($db, $dsSchema,
        "dist", "brand", "own_items", $geoID);

    print <<END_HTML;
        $insight
        <DIV ID="chart-bar-items"></DIV>
        <SCRIPT>
          let ownBrandItemChart = new FusionCharts({'type': 'bar2d', 'width': '100%', 'height': '$chartHeight', 'dataFormat': 'json'});
          ownBrandItemChart.setJSONUrl('ajaxBrandDistCharts.cld?pm=$priceModelID&c=brand_items&g=$geoID&auth=$credentials');
          ownBrandItemChart.render('chart-bar-items');
        </SCRIPT>
END_HTML

  }


#------------------- Velocity of all brands in geo -----------------------

  elsif ($block eq "brands_velocity")
  {
    %geoNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "g");
    print <<END_HTML;
        <H4>Velocity of Brands in $geoNameHash{$geoID}</H4>
        <P>
END_HTML

    #get number of bars we're going to graph, and size chart appropriately
    $query = "SELECT COUNT(velocity52) FROM $dsSchema.$AInsightsBrandTable \
        WHERE velocity52 > 0 AND geographyID=$geoID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($barsToGraph) = $dbOutput->fetchrow_array;
    $chartHeight = $barsToGraph * 20 + 75;

    $insight = AInsights_Utils_get_insight_html($db, $dsSchema,
        "dist", "brand", "velocities", $geoID);

    print <<END_HTML;
        $insight
        <DIV ID="chart-bar-velocities"></DIV>
        <SCRIPT>
          let brandVelocityChart = new FusionCharts({'type': 'bar2d', 'width': '100%', 'height': '$chartHeight', 'dataFormat': 'json'});
          brandVelocityChart.setJSONUrl('ajaxBrandDistCharts.cld?pm=$priceModelID&c=brands_velocity&g=$geoID&auth=$credentials');
          brandVelocityChart.render('chart-bar-velocities');
        </SCRIPT>
END_HTML
  }



#------------------ Own Brand Velocity Across Geographies ----------------------

  elsif ($block eq "velocity_geos")
  {
    print <<END_HTML;
        <H4>$ownBrandName Velocity Across Geographies</H4>
        <P>
END_HTML

    #get number of bars we're going to graph, and size chart appropriately
    $query = "SELECT COUNT(DISTINCT geographyID) FROM $dsSchema.$AInsightsBrandTable \
        WHERE velocity52 > 0 AND brandID = $ownBrandID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($barsToGraph) = $dbOutput->fetchrow_array;
    $chartHeight = $barsToGraph * 20 + 75;

    $insight = AInsights_Utils_get_insight_html($db, $dsSchema,
        "dist", "brand", "velocity_geos", 0);

    print <<END_HTML;
        $insight
        <DIV ID="chart-bar-velocity-geos"></DIV>
        <SCRIPT>
          let ownBrandVelocityGeoChart = new FusionCharts({'type': 'bar2d', 'width': '100%', 'height': '$chartHeight', 'dataFormat': 'json'});
          ownBrandVelocityGeoChart.setJSONUrl('ajaxBrandDistCharts.cld?pm=$priceModelID&c=velocity_geos&g=$geoID&auth=$credentials');
          ownBrandVelocityGeoChart.render('chart-bar-velocity-geos');
        </SCRIPT>
END_HTML
  }



#------------------- Own Brand Item Velocity -----------------------

  if ($block eq "velocity_items")
  {
    print <<END_HTML;
        <H4>$ownBrandName Item Velocity</H4>
        <P>
END_HTML

    #get number of bars we're going to graph, and size chart appropriately
    $query = "SELECT COUNT(productID) FROM $dsSchema.$AInsightsItemTable \
        WHERE velocity52 > 0 AND brandID = $ownBrandID AND geographyID=$geoID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($barsToGraph) = $dbOutput->fetchrow_array;
    $chartHeight = $barsToGraph * 20 + 85;

    $insight = AInsights_Utils_get_insight_html($db, $dsSchema,
        "dist", "brand", "velocity_own_items", $geoID);

    print <<END_HTML;
        $insight
        <DIV ID="chart-bar-velocity-items"></DIV>
        <SCRIPT>
          let ownBrandVelocityItemChart = new FusionCharts({'type': 'bar2d', 'width': '100%', 'height': '$chartHeight', 'dataFormat': 'json'});
          ownBrandVelocityItemChart.setJSONUrl('ajaxBrandDistCharts.cld?pm=$priceModelID&c=velocity_items&g=$geoID&auth=$credentials');
          ownBrandVelocityItemChart.render('chart-bar-velocity-items');
        </SCRIPT>
END_HTML

  }



#------------------------- Distribution Shifts -------------------------------

  elsif ($block eq "comp_flow")
  {
    $insight = AInsights_Utils_get_insight_html($db, $dsSchema,
        "dist", "brand", "shifts", $geoID);

    print <<END_HTML;
        <H4>Distribution Shifts</H4>
        <P>
        $insight

        <P>
        <DIV ID="chart-sankey-compflow"></DIV>
        <SCRIPT>
          let compFlowChart = new FusionCharts({'type': 'sankey', 'width': '99%', 'height': '450', 'dataFormat': 'json'});
          compFlowChart.setJSONUrl('ajaxBrandDistCharts.cld?pm=$priceModelID&c=comp_flow&g=$geoID&auth=$credentials');
          compFlowChart.render('chart-sankey-compflow');
        </SCRIPT>
END_HTML
  }


#------------------ Market Competitors (Brand Data Table) ----------------------

  elsif ($block eq "market_comps")
  {

    #get the last 52 weeks' worth of time period IDs from the data source
    @recent52WeekIDs = AInsights_Utils_get_time_period_ids($db, $dsSchema, 52);
    $recent52wksTimeIDStr = join(',', @recent52WeekIDs);

    %brandNameHash = DSRseg_get_segments_hash($db, $dsSchema, "p", $brandSegID);

    $insight = AInsights_Utils_get_insight_html($db, $dsSchema,
        "dist", "brand", "shifts", $geoID);

    $velocityInsight = AInsights_Utils_get_insight_html($db, $dsSchema,
        "dist", "brand", "market_velocity", $geoID);

    $countInsight = AInsights_Utils_get_insight_html($db, $dsSchema,
        "dist", "brand", "market_brand_count", $geoID);

    print <<END_HTML;
      <P>&nbsp;</P>

      <DIV CLASS="card">
        <DIV CLASS="card-body">

          <H4>Market Competitors</H4>
          <P>
          $velocityInsight

          <P>
          $countInsight
END_HTML

    #get first and last dist date for brands
    $query = "SELECT brandID, DATE_FORMAT(firstDistDate, '%c/%e/%Y'), DATE_FORMAT(lastDistDate, '%c/%e/%Y') FROM $dsSchema.$AInsightsBrandTable \
        WHERE geographyID=$geoID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($brandID, $firstDistDate, $lastDistDate) = $dbOutput->fetchrow_array)
    {
      $firstDistHash{$brandID} = $firstDistDate;
      $lastDistHash{$brandID} = $lastDistDate;
    }

    #output info about brands that were introduced in the market, if any
    $introBrandStr = AInsights_Utils_get_brand_calc_value($db, $dsSchema, 'dist_brand_intro', 0, $geoID);

    if (length($introBrandStr) > 0)
    {
      $insight = AInsights_Utils_get_insight_html($db, $dsSchema,
          "dist", "brand", "market_comp_intro", $geoID);

      print <<END_HTML;
          <P></P>
          $insight
          <P>
          <TABLE CLASS="table table-bordered table-striped">
            <TR>
              <TH>Brand</TH>
              <TH>Introduced</TH>
              <TH>Distribution Level</TH>
              <TH>Dollar Sales</TH>
              <TH>Unit Sales</TH>
            </TR>
END_HTML

      $query = "SELECT brandID, AVG(maxDist) AS avgDist, SUM(dollars), SUM(units) FROM $dsSchema.$AInsightsBrandCube \
          WHERE geographyID=$geoID AND timeID IN ($recent52wksTimeIDStr) AND brandID IN ($introBrandStr) \
          GROUP BY brandID ORDER BY avgDist DESC";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      KAPutil_handle_db_err($db, $status, $query);
      while (($brandID, $maxDist, $dollars, $units) = $dbOutput->fetchrow_array)
      {

        #format numerical values
        $dispMaxDist = AInsights_Utils_html_format_number($maxDist, 1);
        $dispDollars = AInsights_Utils_html_format_currency($dollars);
        $dispUnits = AInsights_Utils_html_format_number($units, 0);

        print <<END_HTML;
            <TR>
              <TD>$brandNameHash{$brandID}</TD>
              <TD>$firstDistHash{$brandID}</TD>
              <TD>$dispMaxDist</TD>
              <TD>$dispDollars</TD>
              <TD>$dispUnits</TD>
            </TR>
END_HTML
      }

    print("</TABLE>\n");
    }

    #output info about brands that were discontinued in the market, if any
    $discoBrandStr = AInsights_Utils_get_brand_calc_value($db, $dsSchema, 'dist_brand_disco', 0, $geoID);

    if (length($discoBrandStr) > 0)
    {
      $insight = AInsights_Utils_get_insight_html($db, $dsSchema,
          "dist", "brand", "market_comp_disco", $geoID);

      print <<END_HTML;
          <P></P>
          $insight
          <P>
          <TABLE CLASS="table table-bordered table-striped">
            <TR>
              <TH>Brand</TH>
              <TH>Discontinued</TH>
              <TH>Distribution Level</TH>
              <TH>Dollar Sales</TH>
              <TH>Unit Sales</TH>
            </TR>
END_HTML

      $query = "SELECT brandID, AVG(maxDist) AS avgDist, SUM(dollars), SUM(units) FROM $dsSchema.$AInsightsBrandCube \
          WHERE geographyID=$geoID AND timeID IN ($recent52wksTimeIDStr) AND brandID IN ($discoBrandStr) \
          GROUP BY brandID ORDER BY avgDist DESC";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      KAPutil_handle_db_err($db, $status, $query);
      while (($brandID, $maxDist, $dollars, $units) = $dbOutput->fetchrow_array)
      {

        #format numerical values
        $dispMaxDist = AInsights_Utils_html_format_number($maxDist, 1);
        $dispDollars = AInsights_Utils_html_format_currency($dollars);
        $dispUnits = AInsights_Utils_html_format_number($units, 0);

        print <<END_HTML;
            <TR>
              <TD>$brandNameHash{$brandID}</TD>
              <TD>$lastDistHash{$brandID}</TD>
              <TD>$dispMaxDist</TD>
              <TD>$dispDollars</TD>
              <TD>$dispUnits</TD>
            </TR>
END_HTML
      }

    print("</TABLE>\n");
    }


    #output info about brands that cycled through the market, if any
    $cycledBrandStr = AInsights_Utils_get_brand_calc_value($db, $dsSchema, 'dist_brand_cycled', 0, $geoID);

    if (length($cycledBrandStr) > 0)
    {
      $insight = AInsights_Utils_get_insight_html($db, $dsSchema,
          "dist", "brand", "market_comp_cycled", $geoID);

      print <<END_HTML;
          <P></P>
          $insight
          <P>
          <TABLE CLASS="table table-bordered table-striped">
            <TR>
              <TH>Brand</TH>
              <TH>Introduced</TH>
              <TH>Discontinued</TH>
              <TH>Distribution Level</TH>
              <TH>Dollar Sales</TH>
              <TH>Unit Sales</TH>
            </TR>
END_HTML

      $query = "SELECT brandID, AVG(maxDist) AS avgDist, SUM(dollars), SUM(units) FROM $dsSchema.$AInsightsBrandCube \
          WHERE geographyID=$geoID AND timeID IN ($recent52wksTimeIDStr) AND brandID IN ($cycledBrandStr) \
          GROUP BY brandID ORDER BY avgDist DESC";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      KAPutil_handle_db_err($db, $status, $query);
      while (($brandID, $maxDist, $dollars, $units) = $dbOutput->fetchrow_array)
      {

        #format numerical values
        $dispMaxDist = AInsights_Utils_html_format_number($maxDist, 1);
        $dispDollars = AInsights_Utils_html_format_currency($dollars);
        $dispUnits = AInsights_Utils_html_format_number($units, 0);

        print <<END_HTML;
            <TR>
              <TD>$brandNameHash{$brandID}</TD>
              <TD>$firstDistHash{$brandID}</TD>
              <TD>$lastDistHash{$brandID}</TD>
              <TD>$dispMaxDist</TD>
              <TD>$dispDollars</TD>
              <TD>$dispUnits</TD>
            </TR>
END_HTML
      }

    print("</TABLE>\n");
    }

    print <<END_HTML;
          <P>
          <DIV ID="data-collapse-brand">
            <DIV CLASS="card border-primary mx-auto">
              <DIV CLASS="card-header bg-primary text-white">
                <A CLASS="card-link text-white text-decoration-none" data-bs-toggle="collapse" HREF="#collapse-brand-data-table">Brand Distribution Data Table</A>
                <I CLASS="fas fa-caret-down"></I>
              </DIV>
              <DIV ID="collapse-brand-data-table" CLASS="collapse" data-bs-parent="#data-collapse-brand">
                <DIV CLASS="card-body">
                  <TABLE CLASS="table table-bordered table-striped">
                    <TR>
                      <TH>Brand</TH>
                      <TH>Distribution Level</TH>
                      <TH>Dollar Sales</TH>
                      <TH>Unit Sales</TH>
                    </TR>
END_HTML

    $query = "SELECT brandID, AVG(maxDist) AS avgDist, SUM(dollars), SUM(units) FROM $dsSchema.$AInsightsBrandCube \
        WHERE geographyID=$geoID AND timeID IN ($recent52wksTimeIDStr) AND brandID != 0 \
        GROUP BY brandID ORDER BY avgDist DESC";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($brandID, $maxDist, $dollars, $units) = $dbOutput->fetchrow_array)
    {

      #skip anything that wasn't actually sold
      if ($units < 5)
      {
        next;
      }

      #format numerical values
      $dispMaxDist = AInsights_Utils_html_format_number($maxDist, 1);
      $dispDollars = AInsights_Utils_html_format_currency($dollars);
      $dispUnits = AInsights_Utils_html_format_number($units, 0);

      if ($brandID == $ownBrandID)
      {
        $htmlTRColor = "table-success";
      }
      else
      {
        $htmlTRColor = "";
      }

      print <<END_HTML;
                    <TR CLASS="$htmlTRColor">
                      <TD>$brandNameHash{$brandID}</TD>
                      <TD CLASS="text-end">$dispMaxDist</TD>
                      <TD CLASS="text-end">$dispDollars</TD>
                      <TD CLASS="text-end">$dispUnits</TD>
                    </TR>
END_HTML
    }

    print <<END_HTML;
                  </TABLE>
                </DIV>
              </DIV>
            </DIV>
          </DIV>
        </DIV>
      </DIV>
END_HTML
  }



#EOF
