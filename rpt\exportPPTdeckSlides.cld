#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSel;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Export PowerPoint Slide Deck</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$("#btn-submit").prop('disabled', true);
  \$("#btn-submit").text("Please Wait...");
  return(true);
}



function toggleCheckboxes(source)
{
  let state = source.checked;

  \$('.cbAll').prop('checked', state);
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  $dsName = ds_id_to_name($db, $dsID);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/rpt/main">Reports</A></LI>
    <LI CLASS="breadcrumb-item active">Export Report to PowerPoint Deck</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print_html_header();

  #get CGI parameters
  $dsID = $q->param('ds');
  $srcRptID = $q->param('rpt');

  $db = KAPutil_connect_to_database();

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-11 col-lg-8 col-xl-7"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Slides to Export</DIV>
        <DIV CLASS="card-body">

          <FORM METHOD="post" ACTION="exportPPTdeck.cld" onsubmit="return checkForm(this);">
          <INPUT TYPE="hidden" NAME="ds" VALUE="$dsID">

          <TABLE CLASS="table table-hover table-bordered table-sm">
            <THEAD><TR>
              <TH>&nbsp;</TH>
              <TH>Report Name</TH>
            </TR></THEAD>

            <TR>
              <TD STYLE="width:10%;">
                <DIV CLASS="form-check" STYLE="margin-top:0px; margin-bottom:0px; text-align:center;">
                  <INPUT CLASS="form-check-input" TYPE='checkbox' ID="checkAll" onclick="toggleCheckboxes(this)" CHECKED>
                  <LABEL CLASS="form-check-label" FOR="checkAll">&nbsp;</LABEL>
                </DIV>
              </TD>
              <TD><STRONG>All</STRONG></TD>
            </TR>
END_HTML

  #get the list of reports the user is allowed to view in this data source
  %reports = cube_list($db, $userID, $acctType, $dsID);

  foreach $rptID (sort {$reports{$a} cmp $reports{$b}} keys %reports)
  {
    print <<END_HTML;
            <TR>
              <TD STYLE="width:10%;">
                <DIV CLASS="form-check" STYLE="margin-top:0px; margin-bottom:0px; text-align:center;">
                  <INPUT CLASS="cbAll form-check-input" NAME="R $rptID" ID="R_$rptID" TYPE="checkbox" CHECKED>
                  <LABEL CLASS="form-check-label" FOR="R_$rptID">&nbsp;</LABEL>
                </DIV>
              </TD>
              <TD>$reports{$rptID}</TD>
            </TR>
END_HTML
  }

  print <<END_HTML;
          </TABLE>

          <P>&nbsp;</P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="location.href='display.cld?rpt=$srcRptID'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-submit">Next <I CLASS="bi bi-arrow-right"></I></BUTTON>
          </DIV>

          </FORM>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
