#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($kapDB);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Delete Data Flow</TITLE>
<LINK REL="SHORTCUT ICON" HREF="$Lib::KoalaConfig::kapHostURL/favicon.png">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($kapDB, $userID, $first, $last, $orgName);

  print <<END_HTML;

<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="$Lib::KoalaConfig::kapHostURL/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">Data Prep</A></LI>
    <LI CLASS="breadcrumb-item">$flowName</LI>
    <LI CLASS="breadcrumb-item active">Delete Data Flow</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser and get the forecast to be deleted
  $q = new CGI;
  $flowID = $q->param('f');
  $random = $q->param('random');
  $userRandom = $q->param('userRandom');

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  $flowName = prep_flow_id_to_name($prepDB, $flowID);

  print_html_header();

  #make sure we're the owner of this data flow
  $query = "SELECT userID FROM prep.flows WHERE ID=$flowID";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($dsOwnerID) = $dbOutput->fetchrow_array;

  if (($dsOwnerID != $userID) && ($acctType < 5))
  {
    exit_error("You don't have privileges to delete this data flow - you're not the data flow owner.");
  }

  #make sure the user correctly typed the confirmation code
  if (($random != $userRandom) && ($userRandom != 9999))
  {
  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-11 col-md-8 col-lg-6 col-xl-5"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Delete Data Flow</DIV>
        <DIV CLASS="card-body">

          You entered an incorrect confirmation code - the selected data flow has not been deleted.

          <P>&nbsp;</P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" onClick="location.href='main.cld'"><SPAN CLASS="bi bi-check-lg"></SPAN> OK</SPAN></BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

    print_html_footer();

    exit;
  }

  prep_audit($prepDB, $userID, "Deleted data flow $flowName", $flowID);

  #remove any files associated with the data flow
  #NB: the nightly cleanup job will grab any actual leftover data
  $query = "DELETE FROM prep.files WHERE flowID=$flowID";
  $prepDB->do($query);

  #remove any file types associated with the data flow
  $query = "DELETE FROM prep.file_types WHERE flowID=$flowID";
  $prepDB->do($query);

  #remove any validation steps
  $query = "DELETE FROM prep.validation WHERE flowID=$flowID";
  $prepDB->do($query);

  #remove any recipe steps
  $query = "DELETE FROM prep.recipes WHERE flowID=$flowID";
  $prepDB->do($query);

  #remove any scheduling associated with the flow
  $query = "DELETE FROM prep.schedule WHERE flowID=$flowID";
  $prepDB->do($query);

  #remove any scheduling associated with the flow
  $query = "DELETE FROM prep.trim_values WHERE flowID=$flowID";
  $prepDB->do($query);

  #remove any jobs associated with the data flow
  $query = "SELECT ID FROM prep.jobs WHERE flowID=$flowID";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  while (($jobID) = $dbOutput->fetchrow_array)
  {
    prep_job_clear($prepDB, $flowID, $jobID);
  }

  #remove the data flow
  $query = "DELETE FROM prep.flows WHERE ID=$flowID";
  $prepDB->do($query);

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-11 col-md-8 col-lg-6 col-xl-5"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Delete Data Flow</DIV>
        <DIV CLASS="card-body">

          The data flow $flowName has been deleted.

          <P>&nbsp;</P>
          <CENTER>
            <BUTTON CLASS="btn btn-primary" onClick="location.href='main.cld'"><SPAN CLASS="bi bi-check-lg"></SPAN> OK</BUTTON>
          </CENTER>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

  utils_slack("PREP: $first $last deleted data flow $flowName");

#EOF
