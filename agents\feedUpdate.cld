#!/usr/bin/perl

use lib "/opt/apache/app/";

use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::Social;


my $debug;



#-------------------------------------------------------------------------
#
# Output debug data, if enabled
#

sub DBG
{
  my ($str) = @_;


  if ($debug == 1)
  {
    print STDERR "$str\n";
  }
}



#-------------------------------------------------------------------------

  #determine if we're being run manually
  $debug = 0;
  if ($ARGV[0] eq "debug")
  {
    $debug = 1;
  }

  #redirect STDERR to the Koala error log
  if ($debug == 0)
  {
    close(STDOUT);
    close(STDERR);
    open(STDERR, ">>/opt/apache/htdocs/tmp/koala_error.log");
  }

  #connect to the master database
  $db = KAPutil_connect_to_database();

  #every hour, check for a variety of possible issues to let analysts know
  #about via their home feed

  #NB: we're depending on Social_feed_add_item to keep us from adding
  #    duplicate items

  #remove all feed items over two months old
  $query = "DELETE FROM app.feed \
      WHERE DATE_SUB(NOW(), INTERVAL 2 MONTH) > insertDate";
  $db->do($query);

  #get data sources that currently have a job running in them for exclusion
  #NB: we want to ignore anything potentially being altered because it can look
  #    like it's in an inconsistent state even though it really isn't (e.g., lots
  #    of "unsegmented items" that are really just items that haven't been
  #    segmented yet)
  $query = "SELECT dsID FROM app.jobs";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  while (($dsID) = $dbOutput->fetchrow_array)
  {
    $dsInUseHash{$dsID} = 1;
  }

  #get ID-keyed hash of data sources
  %dsNameHash = ds_get_name_hash($db);

  #look for reports that are too large
  $query = "SELECT ID, userID, dsID FROM app.cubes \
      WHERE status LIKE 'ERROR:Report is too large%'";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  while (($cubeID, $userID, $dsID) = $dbOutput->fetchrow_array)
  {
    Social_feed_add_item($db, $userID, $dsID, $cubeID, 0, "danger", "rpt_size");
  }

  #look for reports with empty dimensions
  $query = "SELECT ID, userID, dsID, status FROM app.cubes \
      WHERE status LIKE 'ERROR:No % are selected'";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  while (($cubeID, $userID, $dsID, $statusText) = $dbOutput->fetchrow_array)
  {
    Social_feed_add_item($db, $userID, $dsID, $cubeID, 0, "danger", "rpt_missing_dim", $statusText);
  }

  #look for reports that need a manual refresh
  $query = "SELECT ID, lastModified FROM app.dataSources WHERE autoUpdateCubes=0";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  undef(%dsModifiedHash);
  while (($dsID, $dsLastModified) = $dbOutput->fetchrow_array)
  {
    $dsModifiedHash{$dsID} = $dsLastModified;
  }

  foreach $dsID (keys %dsModifiedHash)
  {
    $query = "SELECT ID, userID FROM app.cubes \
        WHERE dsID=$dsID AND lastUpdate < '$dsModifiedHash{$dsID}'";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    while (($cubeID, $userID) = $dbOutput->fetchrow_array)
    {
      Social_feed_add_item($db, $userID, $dsID, $cubeID, 0, "warn", "rpt_manual_refresh");
    }
  }

  #look for data sources that haven't been updated in at least 3 months
  $query = "SELECT ID, name, userID FROM dataSources \
      WHERE deleted = 0 AND DATE_SUB(NOW(), INTERVAL 3 MONTH) > lastUpdate";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  while (($dsID, $name, $userID) = $dbOutput->fetchrow_array)
  {
    Social_feed_add_item($db, $userID, $dsID, 0, 0, "info", "ds_no_updates3");
  }

  #look for manual ODBC exports that need to be performed
  $query = "SELECT ID, name, userID FROM dataSources \
      WHERE ODBCexport > 0 AND ODBCmanual = 1 AND ODBCexported < lastModified";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  while (($dsID, $name, $userID) = $dbOutput->fetchrow_array)
  {
    Social_feed_add_item($db, $userID, $dsID, 0, 0, "warn", "odbc_needs_update");
  }

  #look for large tabular ODBC exports & suggest conversion to star schema
  $query = "SELECT ID, userID FROM app.dataSources WHERE ODBCexport = 1";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  while (($dsID, $userID) = $dbOutput->fetchrow_array)
  {

    #get the size of the data source's ODBC export table and index on disk
    $dsSchema = "datasource_" . $dsID;
    $query = "SELECT table_rows FROM information_schema.TABLES \
        WHERE information_schema.TABLES.table_schema = '$dsSchema' AND information_schema.TABLES.table_name = 'export'";
    $dbOutput1 = $db->prepare($query);
    $dbOutput1->execute;
    ($ODBCrows) = $dbOutput1->fetchrow_array;
    if ($ODBCrows > 1_000_000)
    {
      Social_feed_add_item($db, $userID, $dsID, 0, 0, "warn", "odbc_large_tabular");
    }
  }

  #find every data source modified in the past 2 hours
  $query = "SELECT ID, name FROM app.dataSources \
      WHERE lastModified > DATE_SUB(NOW(), INTERVAL 2 HOUR)";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  while (($dsID, $dsname) = $dbOutput->fetchrow_array)
  {
    $dsModifiedHash{$dsID} = $dsName;
  }

  #cycle through every data source looking for DS-specific issues
  foreach $dsID (keys %dsModifiedHash)
  {

    #skip the data source if it's in use
    if ($dsInUseHash{$dsID} == 1)
    {
      next;
    }

    $dsSchema = "datasource_" . $dsID;

    #--------------------------------------

    #look for unsegmented items
    %segHash = DSRsegmentation_get_segmentations_hash($db, $dsSchema, "p");
    foreach $segID (keys %segHash)
    {
      @unsegmented = DSRseg_get_unsegmented_items($db, $dsSchema, "p", $segID);
      $count = @unsegmented;

      if ($count > 0)
      {
        $userID = ds_get_owner($db, $dsID);
        $content = "The segmentation <a CLASS='text-decoration-none' href='/app/dsr/segmentAssign.cld?ds=$dsID&dim=p&segName=$segHash{$segID}&seg=$segID'>$segHash{$segID}</a> in <a CLASS='text-decoration-none' href='dsr/display.cld?ds=$dsID'>$dsNameHash{$dsID}</a> has $count unsegmented items.";
        Social_feed_add_item($db, $userID, $dsID, 0, 0, "warn", "ds_unsegmented", $content);
      }
    }

    #---------------------------------

    #look for empty lists in all 4 dimensions
    $query = "SELECT ID, name FROM $dsSchema.product_list \
        WHERE LENGTH(members) < 1";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    while (($listID, $name) = $dbOutput->fetchrow_array)
    {
      $userID = ds_get_owner($db, $dsID);
      $content = "Your product list <b>$name</b> in <a CLASS='text-decoration-none' href='dsr/display.cld?ds=$dsID'>$dsNameHash{$dsID}</a> is empty.";
      Social_feed_add_item($db, $userID, $dsID, 0, 0, "warn", "ds_empty_list", $content);
    }

    $query = "SELECT ID, name FROM $dsSchema.geography_list \
        WHERE LENGTH(members) < 1";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    while (($listID, $name) = $dbOutput->fetchrow_array)
    {
      $userID = ds_get_owner($db, $dsID);
      $content = "Your geography list <b>$name</b> in <a CLASS='text-decoration-none' href='dsr/display.cld?ds=$dsID&dim=g'>$dsNameHash{$dsID}</a> is empty.";
      Social_feed_add_item($db, $userID, $dsID, 0, 0, "warn", "ds_empty_list", $content);
    }

    $query = "SELECT ID, name FROM $dsSchema.time_list \
        WHERE LENGTH(members) < 1";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    while (($listID, $name) = $dbOutput->fetchrow_array)
    {
      $userID = ds_get_owner($db, $dsID);
      $content = "Your time period list <b>$name</b> in <a CLASS='text-decoration-none' href='dsr/display.cld?ds=$dsID&dim=t'>$dsNameHash{$dsID}</a> is empty.";
      Social_feed_add_item($db, $userID, $dsID, 0, 0, "warn", "ds_empty_list", $content);
    }

    $query = "SELECT ID, name FROM $dsSchema.measure_list \
        WHERE LENGTH(members) < 1";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    while (($listID, $name) = $dbOutput->fetchrow_array)
    {
      $userID = ds_get_owner($db, $dsID);
      $content = "Your measure list <b>$name</b> in <a CLASS='text-decoration-none' href='dsr/display.cld?ds=$dsID&dim=m'>$dsNameHash{$dsID}</a> is empty.";
      Social_feed_add_item($db, $userID, $dsID, 0, 0, "warn", "ds_empty_list", $content);
    }

    #---------------------------------

    #look for empty aggregates in products, geos, and times
    $query = "SELECT ID, name FROM $dsSchema.product_aggregate \
        WHERE LENGTH(addMembers) < 1";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    while (($aggID, $name) = $dbOutput->fetchrow_array)
    {
      $userID = ds_get_owner($db, $dsID);
      $content = "Your product aggregate <b>$name</b> in <a CLASS='text-decoration-none' href='dsr/display.cld?ds=$dsID'>$dsNameHash{$dsID}</a> is empty.";
      Social_feed_add_item($db, $userID, $dsID, 0, 0, "warn", "ds_empty_agg", $content);
    }

    $query = "SELECT ID, name FROM $dsSchema.geography_aggregate \
        WHERE LENGTH(addMembers) < 1";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    while (($aggID, $name) = $dbOutput->fetchrow_array)
    {
      $userID = ds_get_owner($db, $dsID);
      $content = "Your geography aggregate <b>$name</b> in <a CLASS='text-decoration-none' href='dsr/display.cld?ds=$dsID&dim=g'>$dsNameHash{$dsID}</a> is empty.";
      Social_feed_add_item($db, $userID, $dsID, 0, 0, "warn", "ds_empty_agg", $content);
    }

    $query = "SELECT ID, name FROM $dsSchema.time_aggregate \
        WHERE LENGTH(addMembers) < 1";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    while (($aggID, $name) = $dbOutput->fetchrow_array)
    {
      $userID = ds_get_owner($db, $dsID);
      $content = "Your time period aggregate <b>$name</b> in <a CLASS='text-decoration-none' href='dsr/display.cld?ds=$dsID&dim=t'>$dsNameHash{$dsID}</a> is empty.";
      Social_feed_add_item($db, $userID, $dsID, 0, 0, "warn", "ds_empty_agg", $content);
    }

    #---------------------------------

    #look for non-calculated measures that are missing aggregation rules
    $query = "SELECT ID, name, prodAggRule, geoAggRule, timeAggRule \
        FROM $dsSchema.measures WHERE calculation IS NULL";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    while (($id, $name, $prodAggRule, $geoAggRule, $timeAggRule) = $dbOutput->fetchrow_array)
    {
      if ((length($prodAggRule) < 1) || (length($geoAggRule) < 1) ||
          (length($timeAggRule) < 1))
      {
        $userID = ds_get_owner($db, $dsID);
        $content = "Your measure <b>$name</b> in <a CLASS='text-decoration-none' href='dsr/display.cld?ds=$dsID&dim=t'>$dsNameHash{$dsID}</a> doesn't have its aggregation rules defined.";
        Social_feed_add_item($db, $userID, $dsID, 0, 0, "danger", "ds_undef_meas_agg", $content);
      }
    }
  }


  ############################################################################
  #
  # Populate user's feed with info/warn/danger for Data Prep flows
  #

  $prepDB = PrepUtils_connect_to_database();

  #NB: commenting this out for now since it's super specific to the BAIR extract
=pod
  #warn on Nielsen AOD data warehouse flows that aren't trimming extra BC CATEGORY
  $query = "SELECT flowID FROM prep.recipes \
      WHERE action='TRANS-COL-DISCARD|COL=BC CATEGORY'";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  $flowStr = "";
  while (($flowID) = $dbOutput->fetchrow_array)
  {
    $flowStr .= "$flowID,";
  }
  chop($flowStr);

  $query = "SELECT ID, userID, name FROM prep.flows WHERE sourceInfo LIKE 'FTP=nielsen|%' AND ID NOT IN ($flowStr)";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  while (($flowID, $userID, $name) = $dbOutput->fetchrow_array)
  {
    $content = "Your Data Prep flow <a href='$Lib::KoalaConfig::prepHostURL/app/prep/main.cld?f=$flowID'>$name</a> is extracting data from your Nielsen AOD warehouse, but doesn't have a recipe step to trim the duplicate BC CATEGORY column.";
    Social_feed_add_item($db, $userID, 0, 0, $flowID, "warn", "prep_flow_idw_trim_bccat", $content);
  }
=cut

  #info on active jobs that have been waiting for user-supplied parsing info
  #for at least an hour
  $query = "SELECT flowID, userID FROM prep.jobs \
      WHERE state = 'PARSE-WAIT' AND DATE_SUB(NOW(), INTERVAL 1 HOUR) > lastAction";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  while (($flowID, $userID) = $dbOutput->fetchrow_array)
  {
    $flowName = prep_flow_id_to_name($prepDB, $flowID);
    $content = "A job in your <a CLASS='text-decoration-none' href='$Lib::KoalaConfig::prepHostURL/app/prep/flowOpen.cld?f=$flowID'>$flowName</a> Data Prep flow is waiting for data parsing input.";
    Social_feed_add_item($db, $userID, 0, 0, $flowID, "info", "prep_flow_parse_wait", $content);
  }

  $query = "SELECT flowID, userID FROM prep.jobs \
      WHERE state = 'DATATYPE-WAIT' AND DATE_SUB(NOW(), INTERVAL 1 HOUR) > lastAction";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  while (($flowID, $userID) = $dbOutput->fetchrow_array)
  {
    $flowName = prep_flow_id_to_name($prepDB, $flowID);
    $content = "A job in your <a CLASS='text-decoration-none' href='$Lib::KoalaConfig::prepHostURL/app/prep/flowOpen.cld?f=$flowID'>$flowName</a> Data Prep flow is waiting for data type input.";
    Social_feed_add_item($db, $userID, 0, 0, $flowID, "info", "prep_flow_datatype_wait", $content);
  }

  $query = "SELECT flowID, userID FROM prep.jobs \
      WHERE state = 'LOADED' AND DATE_SUB(NOW(), INTERVAL 1 HOUR) > lastAction AND exportedKoala = 0";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  while (($flowID, $userID) = $dbOutput->fetchrow_array)
  {
    $flowName = prep_flow_id_to_name($prepDB, $flowID);
    $content = "A job in your <a CLASS='text-decoration-none' href='$Lib::KoalaConfig::prepHostURL/app/prep/flowOpen.cld?f=$flowID'>$flowName</a> Data Prep flow is loaded and ready to be exported to Koala Analytics.";
    Social_feed_add_item($db, $userID, 0, 0, $flowID, "info", "prep_flow_export_ready", $content);
  }

  #info on Nielsen AOD data warehouse flows that aren't set to daily for updates
  $query = "SELECT prep.flows.ID, prep.flows.userID FROM prep.flows \
      INNER JOIN prep.schedule ON prep.flows.ID = prep.schedule.flowID \
      WHERE prep.flows.sourceInfo LIKE 'FTP=nielsen|%-update.zip%' AND prep.schedule.sched IN ('weekly', 'monthly')";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  while (($flowID, $userID) = $dbOutput->fetchrow_array)
  {
    $flowName = prep_flow_id_to_name($prepDB, $flowID);
    $content = "Your Data Prep flow <a CLASS='text-decoration-none' href='$Lib::KoalaConfig::prepHostURL/app/prep/scheduleFlow.cld?f=$flowID'>$flowName</a> is extracting data from your Nielsen AOD warehouse, but isn't scheduled to check for new data every day.";
    Social_feed_add_item($db, $userID, 0, 0, $flowID, "info", "prep_flow_idw_schedule", $content);
  }

#EOF
