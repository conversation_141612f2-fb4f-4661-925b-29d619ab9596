#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::DataSources;
use Lib::AInsights::AInsights;
use Lib::AInsights::Utils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if ((length($email) < 1) && (length($credentials) < 1))
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #get CGI parameters
  $priceModelID = $q->param('m');
  $block = $q->param('b');
  $geoID = $q->param('g');
  $credentials = $q->param('auth');

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print($session->header());

  #connect to the database
  $db = KAPutil_connect_to_database();

  #TODO: check credentials

  $modelName = AInsights_ID_to_name($db, $priceModelID);
  $dsName = ds_id_to_name($db, $dsID);
  $dsID = AInsights_get_dsID($db, $priceModelID);

  $dsSchema = "datasource_" . $dsID;

  AInsights_Utils_initialize_constants($priceModelID);

  $ownBrandID = AInsights_Utils_get_own_brand_id($db, $priceModelID);
  $brandSegID = AInsights_Utils_get_brand_seg_id($db, $priceModelID);


  #-----------------------------------------------------------------------------

  if ($block eq 'insights')
  {

  #get values and display info for key KPIs
  $unitsPctChgOwnBrand = AInsights_Utils_get_brand_calc_value($db, $dsSchema, 'units_pct_chg_year', $ownBrandID, $geoID);
  $unitsChgOwnCardBG = AInsights_Utils_get_kpi_html_bgcolor($unitsPctChgOwnBrand);
  $unitsIconHTML = AInsights_Utils_get_kpi_html_icon($unitsPctChgOwnBrand);
  $unitsPctChgOwnBrand = sprintf("%.1f", $unitsPctChgOwnBrand);

  $dollarsPctChgOwnBrand = AInsights_Utils_get_brand_calc_value($db, $dsSchema, 'dollars_pct_chg_year', $ownBrandID, $geoID);
  $dollarsChgOwnCardBG = AInsights_Utils_get_kpi_html_bgcolor($dollarsPctChgOwnBrand);
  $dollarsIconHTML = AInsights_Utils_get_kpi_html_icon($dollarsPctChgOwnBrand);
  $dollarsPctChgOwnBrand = sprintf("%.1f", $dollarsPctChgOwnBrand);

  $shareChgOwnBrand = AInsights_Utils_get_brand_calc_value($db, $dsSchema, 'unit_share_pct_chg_year', $ownBrandID, $geoID);
  $shareChgOwnCardBG = AInsights_Utils_get_kpi_html_bgcolor($shareChgOwnBrand);
  $shareIconHTML = AInsights_Utils_get_kpi_html_icon($shareChgOwnBrand);
  $shareChgOwnBrand = sprintf("%.1f", $shareChgOwnBrand);

  $velocityChgOwnBrand = AInsights_Utils_get_brand_calc_value($db, $dsSchema, 'velocity_pct_chg_year', $ownBrandID, $geoID);
  $velocityChgOwnCardBG = AInsights_Utils_get_kpi_html_bgcolor($velocityChgOwnBrand);
  $velocityIconHTML = AInsights_Utils_get_kpi_html_icon($velocityChgOwnBrand);
  $velocityChgOwnBrand = sprintf("%.1f", $velocityChgOwnBrand);

  print <<END_HTML;
          <DIV CLASS="row">
            <DIV CLASS="col">
              <DIV CLASS="card $unitsChgOwnCardBG text-white mx-auto" STYLE="width:12em;">
                <DIV CLASS="card-body">
                  <H2 CLASS="card-title text-center">$unitsIconHTML $unitsPctChgOwnBrand%</H2>
                  <H6 CLASS="card-subtitle text-center text-white">Unit Sales</H6>
                  <P>
                </DIV>
              </DIV>
            </DIV>
            <DIV CLASS="col">
              <DIV CLASS="card $dollarsChgOwnCardBG text-white mx-auto" STYLE="width:12em;">
                <DIV CLASS="card-body">
                  <H2 CLASS="card-title text-center">$dollarsIconHTML $dollarsPctChgOwnBrand%</H2>
                  <H6 CLASS="card-subtitle text-center text-white">Dollar Sales</H6>
                  <P>
                </DIV>
              </DIV>
            </DIV>
            <DIV CLASS="col">
              <DIV CLASS="card $shareChgOwnCardBG text-white mx-auto" STYLE="width:12em;">
                <DIV CLASS="card-body">
                  <H2 CLASS="card-title text-center">$shareIconHTML $shareChgOwnBrand%</H2>
                  <H6 CLASS="card-subtitle text-center text-white">% Share Change</H6>
                  <P>
                </DIV>
              </DIV>
            </DIV>
            <DIV CLASS="col">
              <DIV CLASS="card $velocityChgOwnCardBG text-white mx-auto" STYLE="width:12em;">
                <DIV CLASS="card-body">
                  <H3 CLASS="card-title text-center">$velocityIconHTML $velocityChgOwnBrand%</H3>
                  <H6 CLASS="card-subtitle text-center text-white">Velocity</H6>
                  <P>
                </DIV>
              </DIV>
            </DIV>
          </DIV>
END_HTML

  $summaryInsight = AInsights_Utils_get_insight_html($db, $dsSchema,
      "top", "brand", "summary", $geoID);

  $salesInsight = AInsights_Utils_get_insight_html($db, $dsSchema,
      "top", "brand", "sales", $geoID);

  $priceChangeInsight = AInsights_Utils_get_insight_html($db, $dsSchema,
      "top", "brand", "price_change", $geoID);

  $promoInsight = AInsights_Utils_get_insight_html($db, $dsSchema,
      "top", "brand", "promo", $geoID);

  $distInsight = AInsights_Utils_get_insight_html($db, $dsSchema,
      "top", "brand", "dist", $geoID);

  $AIrecommendations = AInsights_Utils_get_insight_html($db, $dsSchema,
      "top", "brand", "recommend", $geoID);

  print <<END_HTML;
          <DIV CLASS="row mt-3">
            <DIV CLASS="col">
              <P>
              $summaryInsight

              <P>
              $salesInsight
              $priceChangeInsight
              $promoInsight
              $distInsight

              <P>
              $AIrecommendations

            </DIV>
          </DIV>

END_HTML

    exit;
  }


#EOF
