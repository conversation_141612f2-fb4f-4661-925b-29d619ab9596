#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  #output Content-type header
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  #output HTML
  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Delete Data Source</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Please Wait...');
  return(true);
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Delete Data Source</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser and get the forecast to be deleted
  $q = new CGI;
  $dsID = $q->param('ds');

  #load the CGI session
  $session = CGI::Session->new();

  #sanitize parameters
  $dsID = utils_sanitize_integer($dsID);

  get_cgi_session_info();

  #connect to the database
  $db = KAPutil_connect_to_database();

  #get the name of the data source
  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  #make sure we're the owner of this data source and it isn't ODBC exported
  $query = "SELECT userID, ODBCexport FROM dataSources WHERE ID=$dsID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($dsOwnerID, $ODBCexport) = $dbOutput->fetchrow_array;

  if (($dsOwnerID != $userID) && ($acctType < 5))
  {
    exit_error("You don't have privileges to delete this data source - you're not the data source owner.");
  }

  if ($ODBCexport > 0)
  {
    exit_error("This is an ODBC-exported data source, and can't be deleted until the export option is removed.");
  }

  #generate the random number we're going to make the user type to delete the DS
  $random = 999 + int(rand(8999));

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-11 col-lg-8 col-xl-7"> <!-- content -->

      <FORM METHOD="post" ACTION="/app/dsr/deleteDS.cld" onsubmit="return checkForm(this);">
      <INPUT TYPE="hidden" NAME="dsID" VALUE="$dsID">
      <INPUT TYPE="hidden" NAME="random" VALUE="$random">

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Delete Data Source</DIV>
        <DIV CLASS="card-body">

          Are you sure you want to delete the data source <STRONG>$dsName</STRONG>, along with all of the reporting and analytics that depend on it?

          <P>&nbsp;</P>
          To continue, please enter the confirmation number displayed below:

          <H3 CLASS="text-primary text-center">$random</H3>

          <P>&nbsp;</P>
          <DIV CLASS="row">
            <DIV CLASS="col-auto mt-1">
              <LABEL FOR="userRandom">Confirmation number:</LABEL>
            </DIV>
            <DIV CLASS="col-auto gx-1">
              <INPUT CLASS="form-control" NAME="userRandom" ID="userRandom" STYLE="width:5em;" pattern="[0-9]{4}" required autocomplete="false">
            </DIV>
          </DIV>
          <P>&nbsp;</P>

          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onclick="location.href='main.cld?ds=$dsID'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-danger" TYPE="SUBMIT" ID="btn-submit"><I CLASS="bi bi-trash"></I> Delete Data Source</BUTTON>
          </DIV>

        </DIV>
      </DIV>

      </FORM>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
