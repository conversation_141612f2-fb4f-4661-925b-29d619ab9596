#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  #output Content-type header
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  #output HTML
  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: $action Calculated Measure</TITLE>
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Please Wait...');
  return(true);
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">$action Calculated Measure $measName</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');
  $measName = $q->param('measureName');
  $measType = $q->param('measType');
  $calcBeforeAgg = $q->param('calcBeforeAgg');
  $measureID = $q->param('measID');

  #sanitize parameters
  $dsID = utils_sanitize_integer($dsID);
  if (!defined($dsID))
  {
    exit_early_error($session, "Invalid data source");
  }
  $measureID = utils_sanitize_integer($measureID);

  #connect to the database
  $db = KAPutil_connect_to_database();

  if ($measureID > 0)
  {
    $action = "Edit";
  }
  else
  {
    $action = "New";
  }

  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  #make sure we have write privs for the data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to modify this data source.");
  }

  $dsSchema = "datasource_" . $dsID;

  #if we're editing an existing measure, grab the info we need
  if ($measureID > 0)
  {
    $query = "SELECT calculation FROM $dsSchema.measures WHERE ID=$measureID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($calculation) = $dbOutput->fetchrow_array;

    $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|(.*?)\|/;
    $dim = $1;
    $measure = $2;
    $structType = $3;
    $structID = $4;
  }

  #set up the text depending on what type of measure the user is defining
  if ($measType eq "share")
  {
    $UImeasType = "Share";
    $UItext = "I want to calculate the share of products based on";
  }
  elsif ($measType eq "index")
  {
    $UImeasType = "Index";
    $UItext = "I want to calculate an index based on";
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-12 col-lg-10 col-xl-8"> <!-- content -->

      <FORM METHOD="post" ACTION="calcMeasureIdxShrVar2.cld" onsubmit="return checkForm(this);">
      <INPUT TYPE="hidden" NAME="ds" VALUE="$dsID">
      <INPUT TYPE="hidden" NAME="measName" VALUE="$measName">
      <INPUT TYPE="hidden" NAME="measType" VALUE="$measType">
      <INPUT TYPE="hidden" NAME="calcBeforeAgg" VALUE="$calcBeforeAgg">
      <INPUT TYPE="hidden" NAME="measID" VALUE="$measureID">
      <INPUT TYPE="hidden" NAME="calculation" VALUE="$calculation">

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Calculated $UImeasType Measure</DIV>
        <DIV CLASS="card-body">

          <DIV CLASS="row">
            <DIV CLASS="col-auto mt-1 gx-1 ms-2">
              $UItext
            </DIV>
            <DIV CLASS="col-auto gx-1">
              <SELECT CLASS="form-select" name="measure" ID="measure" VALUE="$measure" STYLE="width: auto;" required>
END_HTML

  #get hash of measure names and IDs
  %measureNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "m");

  foreach $id (sort {$measureNameHash{$a} cmp $measureNameHash{$b}} keys %measureNameHash)
  {
    if ($id != $measureID)
    {
      print(" <OPTION VALUE=$id>$measureNameHash{$id}</OPTION>\n");
    }
  }

  print <<END_HTML;
              </SELECT>
              <SCRIPT>
                \$('select#measure').val('$measure');
              </SCRIPT>
            </DIV>
END_HTML

  if ($measType eq "index")
  {
    print <<END_HTML;
            <DIV CLASS="col-auto gx-1 mt-1">
              for the
            </DIV>
            <DIV CLASS="col-auto gx-1">
              <SELECT CLASS="form-select" NAME="dim" ID="dim" VALUE="$dim" STYLE="width: auto;" required>
                <OPTION VALUE="p">product</OPTION>
                <OPTION VALUE="g">geography</OPTION>
                <OPTION VALUE="t">time period</OPTION>
              </SELECT>
            </DIV>
            <DIV CLASS="col-auto gx-1 mt-1">
              dimension.
            </DIV>
END_HTML
  }
  else
  {
    print <<END_HTML;
            <INPUT TYPE="hidden" NAME="dim" VALUE="p">
END_HTML
  }

  print <<END_HTML;
          </DIV>

          <P>&nbsp;<P>

          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onclick="location.href='display.cld?ds=$dsID&dim=m'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-submit">Next <I CLASS="bi bi-arrow-right"></I></BUTTON>
          </DIV>

        </DIV>
      </DIV>

      </FORM>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
