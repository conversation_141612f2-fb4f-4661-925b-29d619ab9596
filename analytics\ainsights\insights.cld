#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::DataSources;
use Lib::AInsights::AInsights;
use Lib::AInsights::Utils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  #output Content-type header
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  #output HTML
  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: AInsights</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<SCRIPT SRC="/fusioncharts-3.18.0/js/fusioncharts.js"></SCRIPT>
<SCRIPT SRC="/fusioncharts-3.18.0/js/themes/fusioncharts.theme.zune.js"></SCRIPT>

<SCRIPT>
let vpHeight = window.innerHeight - 50;
if (vpHeight < 400)
{
  vpHeight = 400;
}
</SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;

<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">AInsights</A></LI>
    <LI CLASS="breadcrumb-item active">$modelName</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $priceModelID = $q->param('pm');
  $geoID = $q->param('g');

  #connect to the database
  $db = KAPutil_connect_to_database();

  #if the model is currently being rebuilt
  $query = "SELECT PID FROM app.jobs \
      WHERE analyticsID=$priceModelID AND operation='ANALYTICS-PRICE'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($pid) = $dbOutput->fetchrow_array;
  if ($pid > 0)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: /app/analytics/ainsights/modelRefresh.cld?ds=$dsID&pm=$priceModelID\n\n");
    exit;
  }

  #if we weren't passed a geography to display
  if ($geoID < 1)
  {

    #see if we have one to try in the analyst's cookie
    $geoID = $session->param("priceModelGeoSelection.$priceModelID");

    #if still nothing, redirect to overview page
    if ($geoID < 1)
    {
      print("Status: 302 Moved temporarily\n");
      print("Location: /app/analytics/ainsights/overview.cld?ds=$dsID&pm=$priceModelID\n\n");
      exit;
    }
  }
  else
  {
    $session->param("priceModelGeoSelection.$priceModelID", "$geoID");
  }


  $modelName = AInsights_ID_to_name($db, $priceModelID);
  $dsName = ds_id_to_name($db, $dsID);
  $dsID = AInsights_get_dsID($db, $priceModelID);

  $dsSchema = "datasource_" . $dsID;

  print_html_header();

  #make sure we have at least read privs for this pricing model
  $privs = AInsights_rights($db, $userID, $priceModelID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to view this pricing model.");
  }

  %geoNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "g");

  AInsights_Utils_initialize_constants($priceModelID);

  $ownBrandID = AInsights_Utils_get_own_brand_id($db, $priceModelID);
  $brandSegID = AInsights_Utils_get_brand_seg_id($db, $priceModelID);
  @geoIDs = AInsights_Utils_get_model_geo_ids($db, $priceModelID);
  $HR_endDate = AInsights_Utils_get_most_recent_human_readable_end_date($db, $dsSchema);

  %brandNameHash = DSRseg_get_segments_hash($db, $dsSchema, "p", $brandSegID);
  $ownBrandName = $brandNameHash{$ownBrandID};

  #output off-canvas data selector
  print <<END_HTML;
  <DIV CLASS="offcanvas offcanvas-start" TABINDEX="-1" ID="offcanvas-data-selector">
  <DIV CLASS="offcanvas-header">
    <H5 CLASS="offcanvas-title" ID="offcanvas-label-data-selector"></H5>
    <BUTTON TYPE="button" CLASS="btn-close text-reset" data-bs-dismiss="offcanvas"></BUTTON>
  </DIV>
  <DIV CLASS="offcanvas-body">
    <DIV>
      <DIV CLASS="card">
        <DIV CLASS="card-header">Focus Geography</DIV>
        <DIV CLASS="card-body">
         <DIV CLASS="list-group">
           <A HREF="overview.cld?pm=$priceModelID&g=$availableGeoID" CLASS="list-group-item list-group-item-action"><B>Overview</B></A>
END_HTML

  #determine which geographies didn't distribute our brand in the past 52 wks
  $query = "SELECT geographyID FROM $dsSchema.$AInsightsBrandTable \
      WHERE brandID=$ownBrandID AND ISNULL(avgDist52)";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  while (($id) = $dbOutput->fetchrow_array)
  {
    $noDistGeosHash{$id} = 1;
  }

  foreach $availableGeoID (@geoIDs)
  {
    $htmlListClass = "";
    if ($availableGeoID == $geoID)
    {
      $htmlListClass = "active";
    }
    elsif ($noDistGeosHash{$availableGeoID} > 0)
    {
      $htmlListClass = "list-group-item-secondary";
    }

    print <<END_HTML;
           <A HREF="?pm=$priceModelID&g=$availableGeoID" CLASS="list-group-item list-group-item-action $htmlListClass">$geoNameHash{$availableGeoID}</A>
END_HTML
  }

  print <<END_HTML;
          </DIV>
        </DIV>
      </DIV>

      <P>
      <FORM METHOD="post" ACTION="modelRefresh.cld">
      <INPUT TYPE="hidden" NAME="pm" VALUE="$priceModelID">
      <INPUT TYPE="hidden" NAME="a" VALUE="c">
      <INPUT TYPE="hidden" NAME="compGeo" VALUE="$geoID">
      <DIV CLASS="card">
        <DIV CLASS="card-header">Key Competitors</DIV>
        <DIV CLASS="card-body">
          <SELECT CLASS="form-select" NAME="comp1" ID="comp1"">
            <OPTION VALUE="auto">Automatically Detect</OPTION>
END_HTML

  ($compID1, $compID2) = AInsights_Utils_get_brand_comp_ids($db, $dsSchema, $geoID);

  foreach $brandID (sort {$brandNameHash{$a} cmp $brandNameHash{$b}} keys %brandNameHash)
  {
    print("  <OPTION VALUE='$brandID'>$brandNameHash{$brandID}\n");
  }

  print <<END_HTML;
          </SELECT>
          <SCRIPT>
          \$(document).ready(function()
          {
            \$('#comp1').val('$compID1');
          });
          </SCRIPT>

          <SELECT CLASS="form-select mt-2" NAME="comp2" ID="comp2">
          <OPTION VALUE="auto">Automatically Detect</OPTION>
END_HTML

  foreach $brandID (sort {$brandNameHash{$a} cmp $brandNameHash{$b}} keys %brandNameHash)
  {
    print("  <OPTION VALUE='$brandID'>$brandNameHash{$brandID}\n");
  }

  print <<END_HTML;
          </SELECT>
          <SCRIPT>
          \$(document).ready(function()
          {
            \$('#comp2').val('$compID2');
          });
          </SCRIPT>

          <DIV CLASS="form-check form-check-inline">
            <INPUT CLASS="form-check-input" NAME="comp-all" ID="comp-all" TYPE="checkbox">
            <LABEL CLASS="form-check-label" FOR="comp-all">Apply change to all geographies</LABEL>
          </DIV>

          <DIV CLASS="text-center mt-2">
            <BUTTON CLASS="btn btn-primary" TYPE="submit" ID="btn-submit-comp">Apply <I CLASS="bi bi-pencil-square""></I></BUTTON>
          </DIV>
        </DIV>
      </DIV>
      </FORM>
    </DIV>
  </DIV>
</DIV>
END_HTML

  print <<END_HTML;
<DIV CLASS="container-fluid">

  <DIV CLASS="row">
    <DIV CLASS="col">

      <H3>Insights for $ownBrandName in
      <A HREF="#" CLASS="text-decoration-none" data-bs-toggle="offcanvas" data-bs-target="#offcanvas-data-selector">$geoNameHash{$geoID}</A></H3>
      <H6>52 Weeks Ending $HR_endDate</H6>
      <P>&nbsp;</P>

    </DIV>
  </DIV>

  <DIV CLASS="row">
    <DIV CLASS="col">
END_HTML

  #if our brand isn't carried in this market, state so and exit out
  $query = "SELECT units52 FROM $dsSchema.$AInsightsBrandTable \
      WHERE brandID=$ownBrandID AND geographyID=$geoID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($unitsOwnBrand) = $dbOutput->fetchrow_array;
  if ($unitsOwnBrand < 2)
  {
    print <<END_HTML;
      <DIV CLASS="card">
        <DIV CLASS="card-body">
          $ownBrandName isn't sold in this market.
        </DIV>
    </DIV>
  </DIV>
</DIV>
<P>
END_HTML
    exit;
  }

  print <<END_HTML;
      <DIV CLASS="card">
        <DIV ID="card-insights" CLASS="card-body">

          <P CLASS="placeholder-glow">
            <SPAN CLASS="placeholder placeholder-lg bg-success w-100 my-2"></SPAN>
            <SPAN CLASS="placeholder placeholder-lg bg-success w-100 my-2"></SPAN>
            <SPAN CLASS="placeholder placeholder-lg bg-success w-100 my-2"></SPAN>
            <SPAN CLASS="placeholder placeholder-lg bg-success w-100 my-2"></SPAN>
            <SPAN CLASS="placeholder placeholder-lg bg-success w-100 my-2"></SPAN>
          </P>

        </DIV>
      </DIV>
      <SCRIPT>
        \$('#card-insights').load('insightsCards.cld?m=$priceModelID&b=insights&g=$geoID&auth=jkdshjfdsjh7wt-sdfsdf');
      </SCRIPT>


    </DIV> <!-- col -->
  </DIV>  <!-- row -->
END_HTML

  #get actual dollars and units
  $query = "SELECT dollars52, units52 FROM $dsSchema.$AInsightsBrandTable \
      WHERE brandID=$ownBrandID AND geographyID=$geoID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($ownBrandDollars, $ownBrandUnits) = $dbOutput->fetchrow_array;
  $ownBrandDollars = AInsights_Utils_autoscale_number($ownBrandDollars);
  $ownBrandUnits = AInsights_Utils_autoscale_number($ownBrandUnits);

  #grab the first sentence of the sales overview insight to display here
  $insight = AInsights_Utils_get_insight_html($db, $dsSchema,
      "sales", "brand", "overview", $geoID);
  $insight =~ m/^(.*?)\<P\>/;
  $insight = $1;

  $query = "SELECT ID, endDate FROM $dsSchema.timeperiods \
      WHERE duration=1 AND type=30 ORDER BY endDate DESC LIMIT 52";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  while (($timeID, $endDate) = $dbOutput->fetchrow_array)
  {
    $timeIDStr .= "'$timeID',";
    push(@orderedTimeIDs, $timeID);

    $endDate =~ m/^(.*?) /;
    $endDateHash{$timeID} = $1;
  }
  chop($timeIDStr);
  @orderedTimeIDs = reverse(@orderedTimeIDs);

  $query = "SELECT timeID, units FROM $dsSchema.$AInsightsBrandCube \
      WHERE brandID=$ownBrandID AND geographyID=$geoID AND timeID IN ($timeIDStr)";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  while (($timeID, $unitsVal) = $dbOutput->fetchrow_array)
  {
    $unitsValueHash{$timeID} = $unitsVal;
  }

  $jsonData = "";
  foreach $timeID (@orderedTimeIDs)
  {
    $unitsVal = $unitsValueHash{$timeID};
    $jsonData .= "{value: $unitsVal},\n";
  }
  chop($jsonData);  chop($jsonData);


  print <<END_HTML;
      <P>
      <DIV CLASS="row">

        <DIV CLASS="col-6">
          <DIV CLASS="card border-primary h-100">
            <DIV CLASS="card-header bg-primary text-white">Sales</DIV>
            <DIV CLASS="card-body">

              <DIV CLASS="row">

                <DIV CLASS="col">
                  <DIV CLASS="card bg-info text-white mx-auto" STYLE="width:8em;">
                    <DIV CLASS="card-body text-center text-white">
                      <B>\$$ownBrandDollars</B><BR>
                      Dollars
                    </DIV>
                  </DIV>
                </DIV>

                <DIV CLASS="col">
                  <DIV CLASS="card bg-info text-white mx-auto" STYLE="width:9em;">
                    <DIV CLASS="card-body text-center text-white">
                      <B>$ownBrandUnits</B><BR>
                      Units
                    </DIV>
                  </DIV>
                </DIV>

              </DIV> <!-- row -->

              <P>
              <DIV ID="sparkchart-line-sales"></DIV>

<SCRIPT>
const salesSparkData = {
  chart: {
    theme: "fusion",
    showclosevalue: "1",
    showopenvalue: "1",
    setadaptiveymin: "1",
    showHighLowValue: "0",
    decimals: "0"
  },
  dataset: [
    {
      data: [ $jsonData ]
    }
  ]
};

FusionCharts.ready(function() {
  var chartSparkSales = new FusionCharts({
    type: "sparkline",
    renderAt: "sparkchart-line-sales",
    width: "100%",
    height: "50",
    dataFormat: "json",
    dataSource: salesSparkData
  }).render();
});
</SCRIPT>

              $insight
            </DIV>
            <DIV CLASS="card-footer border-white bg-white text-end">
              <BUTTON CLASS="btn btn-primary" TYPE="button" onclick="location.href='salesBrand.cld?pm=$priceModelID'"><I CLASS="bi bi-building"></I> Brands</BUTTON>
<!---
              <BUTTON CLASS="btn btn-primary disabled" TYPE="button" onclick="location.href='salesPPG.cld?pm=$priceModelID'"><I CLASS="bi bi-cart4"></I> Promo Groups</BUTTON>
              <BUTTON CLASS="btn btn-primary disabled" TYPE="button" onclick="location.href='salesItem.cld?pm=$priceModelID'"><I CLASS="bi bi-upc"></I> Items</BUTTON>
--->
            </DIV>
          </DIV>
        </DIV>
END_HTML

  #get % Chg Avg Price for display in KPI tile
  $ownBrandAvgPricePctChg = AInsights_Utils_get_brand_calc_value($db, $dsSchema, 'avg_price_pct_chg_year', $ownBrandID, $geoID);
  $ownBrandAvgPriceKPIColor = "bg-info";
  $icon = AInsights_Utils_get_kpi_html_icon($ownBrandAvgPricePctChg);
  $ownBrandAvgPricePctChg = sprintf("%.1f", $ownBrandAvgPricePctChg);
  $ownBrandAvgPricePctChg = "$icon $ownBrandAvgPricePctChg%";

  #get actual average price
  $query = "SELECT avgPrice52 FROM $dsSchema.$AInsightsBrandTable \
      WHERE brandID=$ownBrandID AND geographyID=$geoID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($ownBrandAvgPrice) = $dbOutput->fetchrow_array;
  $ownBrandAvgPriceColor = "bg-info";
  $ownBrandAvgPrice = AInsights_Utils_html_format_currency($ownBrandAvgPrice);

  #grab the first sentence of the pricing overview insight to display here
  $insight = AInsights_Utils_get_insight_html($db, $dsSchema,
      "price", "brand", "overview", $geoID);
  $insight =~ m/^(.*?)\<P\>/;
  $insight = $1;

  #build up a JSON data set for display in spark chart
  $query = "SELECT timeID, avgPrice FROM $dsSchema.$AInsightsBrandCube \
      WHERE brandID=$ownBrandID AND geographyID=$geoID AND timeID IN ($timeIDStr)";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  while (($timeID, $distVal) = $dbOutput->fetchrow_array)
  {
    $distValueHash{$timeID} = $distVal;
  }

  $jsonData = "";
  foreach $timeID (@orderedTimeIDs)
  {
    $distVal = $distValueHash{$timeID};
    $jsonData .= "{value: $distVal},\n";
  }
  chop($jsonData);  chop($jsonData);

  print <<END_HTML;
        <DIV CLASS="col-6">
          <DIV CLASS="card border-primary">
            <DIV CLASS="card-header bg-primary text-white">Pricing</DIV>
            <DIV CLASS="card-body">
              <DIV CLASS="row">

                <DIV CLASS="col">
                  <DIV CLASS="card $ownBrandAvgPriceKPIColor text-white mx-auto" STYLE="width:8em;">
                    <DIV CLASS="card-body text-center text-white">
                      <B>$ownBrandAvgPricePctChg</B><BR>
                      % Change
                    </DIV>
                  </DIV>
                </DIV>

                <DIV CLASS="col">
                  <DIV CLASS="card $ownBrandAvgPriceColor text-white mx-auto" STYLE="width:9em;">
                    <DIV CLASS="card-body text-center text-white">
                      <B>$ownBrandAvgPrice</B><BR>
                      Average Price
                    </DIV>
                  </DIV>
                </DIV>

              </DIV> <!-- row -->

              <P>
              <DIV ID="sparkchart-line-price"></DIV>

<SCRIPT>
const priceSparkData = {
  chart: {
    theme: "fusion",
    showclosevalue: "1",
    showopenvalue: "1",
    setadaptiveymin: "1",
    showHighLowValue: "0",
    decimals: "2",
    numberPrefix: "\$"

  },
  dataset: [
    {
      data: [ $jsonData ]
    }
  ]
};

FusionCharts.ready(function() {
  var chartSparkPrice = new FusionCharts({
    type: "sparkline",
    renderAt: "sparkchart-line-price",
    width: "100%",
    height: "50",
    dataFormat: "json",
    dataSource: priceSparkData
  }).render();
});
</SCRIPT>

              $insight

            </DIV>
            <DIV CLASS="card-footer border-white bg-white text-end">
              <BUTTON CLASS="btn btn-primary" TYPE="button" onclick="location.href='priceBrand.cld?pm=$priceModelID'"><I CLASS="bi bi-building"></I> Brands</BUTTON>
              <BUTTON CLASS="btn btn-primary" TYPE="button" onclick="location.href='elasticity.cld?pm=$priceModelID&dm=ppg'"><I CLASS="bi bi-cart4"></I> Promo Groups</BUTTON>
              <BUTTON CLASS="btn btn-primary" TYPE="button" onclick="location.href='elasticity.cld?pm=$priceModelID&dm=sku'"><I CLASS="bi bi-upc"></I> Items</BUTTON>
            </DIV>
          </DIV>
        </DIV>

      </DIV>  <!-- card row -->

      <P>&nbsp;</P>
END_HTML

  #get % change in promotion for our brand
  $ownBrandPromoUnitsPctChg = AInsights_Utils_get_brand_calc_value($db, $dsSchema, 'promo_units_pct_chg_year', $ownBrandID, $geoID);
  $ownBrandPromoUnitsPctChgKPIColor = "bg-info";
  $ownBrandPromoUnitsPctChg = AInsights_Utils_html_format_number($ownBrandPromoUnitsPctChg, 1, 0);
  $ownBrandPromoUnitsPctChg = "$ownBrandPromoUnitsPctChg";

  #get % of units sold on promotion for our brand
  $ownBrandPromoUnitsPct = AInsights_Utils_get_brand_calc_value($db, $dsSchema, 'promo_units_pct_year', $ownBrandID, $geoID);
  $ownBrandPromoUnitsPctKPIColor = "bg-info";
  $ownBrandPromoUnitsPct = AInsights_Utils_html_format_number($ownBrandPromoUnitsPct, 1, 0);
  $ownBrandPromoUnitsPct = "$ownBrandPromoUnitsPct";

  #grab the first sentence of the pricing overview insight to display here
  $insight = AInsights_Utils_get_insight_html($db, $dsSchema,
      "promo", "brand", "overview", $geoID);
  $insight =~ m/^(.*?)\<P\>/;
  $insight = $1;

  $query = "SELECT timeID, promoUnits FROM $dsSchema.$AInsightsBrandCube \
      WHERE brandID=$ownBrandID AND geographyID=$geoID AND timeID IN ($timeIDStr)";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  while (($timeID, $promoVal) = $dbOutput->fetchrow_array)
  {
    $promoValueHash{$timeID} = $promoVal;
  }

  $jsonData = "";
  foreach $timeID (@orderedTimeIDs)
  {
    $promoVal = $promoValueHash{$timeID};
    $jsonData .= "{value: $promoVal},\n";
  }
  chop($jsonData);  chop($jsonData);

  print <<END_HTML;
      <DIV CLASS="row">

        <DIV CLASS="col-6">
          <DIV CLASS="card border-primary h-100">
            <DIV CLASS="card-header bg-primary text-white">Promotion</DIV>
            <DIV CLASS="card-body">

              <DIV CLASS="row">

                <DIV CLASS="col">
                  <DIV CLASS="card $ownBrandAvgPriceKPIColor text-white mx-auto" STYLE="width:8em;">
                    <DIV CLASS="card-body text-center text-white">
                      <B>$ownBrandAvgPricePctChg</B><BR>
                      % Change
                    </DIV>
                  </DIV>
                </DIV>

                <DIV CLASS="col">
                  <DIV CLASS="card $ownBrandPromoUnitsPctKPIColor text-white mx-auto" STYLE="width:9em;">
                    <DIV CLASS="card-body text-center text-white">
                      <B>$ownBrandPromoUnitsPct%</B><BR>
                      % Promo Units
                    </DIV>
                  </DIV>
                </DIV>

              </DIV> <!-- row -->

              <P>
              <DIV ID="sparkchart-line-promo"></DIV>

<SCRIPT>
const promoSparkData = {
  chart: {
    theme: "fusion",
    showclosevalue: "1",
    showopenvalue: "1",
    setadaptiveymin: "1",
    showHighLowValue: "0",
    decimals: "0"
  },
  dataset: [
    {
      data: [ $jsonData ]
    }
  ]
};

FusionCharts.ready(function() {
  var chartSparkPromo = new FusionCharts({
    type: "sparkline",
    renderAt: "sparkchart-line-promo",
    width: "100%",
    height: "50",
    dataFormat: "json",
    dataSource: promoSparkData
  }).render();
});
</SCRIPT>

              $insight
            </DIV>
            <DIV CLASS="card-footer border-white bg-white text-end">
              <BUTTON CLASS="btn btn-primary" TYPE="button" onclick="location.href='promoBrand.cld?pm=$priceModelID'"><I CLASS="bi bi-building"></I> Brands</BUTTON>
<!---
              <BUTTON CLASS="btn btn-primary disabled" TYPE="button" onclick="location.href='promoPPG.cld?pm=$priceModelID'"><I CLASS="bi bi-cart4"></I> Promo Groups</BUTTON>
              <BUTTON CLASS="btn btn-primary disabled" TYPE="button" onclick="location.href='promoItem.cld?pm=$priceModelID'"><I CLASS="bi bi-upc"></I> Items</BUTTON>
--->
            </DIV>
          </DIV>
        </DIV>
END_HTML

  #get % Chg of distribution for display in KPI tile
  $ownBrandDistPctChg = AInsights_Utils_get_brand_calc_value($db, $dsSchema, 'dist_year_over_year', $ownBrandID, $geoID);
  $ownBrandDistKPIColor = AInsights_Utils_get_kpi_html_bgcolor($ownBrandDistPctChg);
  $icon = AInsights_Utils_get_kpi_html_icon($ownBrandDistPctChg);
  $ownBrandDistPctChg = sprintf("%.1f", $ownBrandDistPctChg);
  $ownBrandDistPctChg = "$icon $ownBrandDistPctChg";

  #get actual distribution level, and color-code against category average
  $query = "SELECT avgDist52 FROM $dsSchema.$AInsightsBrandTable \
      WHERE brandID=$ownBrandID AND geographyID=$geoID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($ownBrandDist) = $dbOutput->fetchrow_array;

  $query = "SELECT avgDist52 FROM $dsSchema.$AInsightsBrandTable \
      WHERE brandID=0 AND geographyID=$geoID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($categoryDist) = $dbOutput->fetchrow_array;

  if ($ownBrandDist >= $categoryDist)
  {
    $ownBrandDistColor = "bg-success";
  }
  elsif ((($categoryDist - $ownBrandDist) / $categoryDist) < 0.25)
  {
    $ownBrandDistColor = "bg-warning";
  }
  else
  {
    $ownBrandDistColor = "bg-danger";
  }
  $ownBrandDist = sprintf("%.1f", $ownBrandDist);


  #grab the first sentence of the distribution overview insight to display here
  $insight = AInsights_Utils_get_insight_html($db, $dsSchema,
      "dist", "brand", "overview", $geoID);
  $insight =~ m/^(.*?)\<P\>/;
  $insight = $1;

  $query = "SELECT timeID, avgDist FROM $dsSchema.$AInsightsBrandCube \
      WHERE brandID=$ownBrandID AND geographyID=$geoID AND timeID IN ($timeIDStr)";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  while (($timeID, $distVal) = $dbOutput->fetchrow_array)
  {
    $distValueHash{$timeID} = $distVal;
  }

  $jsonData = "";
  foreach $timeID (@orderedTimeIDs)
  {
    $distVal = $distValueHash{$timeID};
    $jsonData .= "{value: $distVal},\n";
  }
  chop($jsonData);  chop($jsonData);


  print <<END_HTML;
        <DIV CLASS="col-6">
          <DIV CLASS="card border-primary h-100">
            <DIV CLASS="card-header bg-primary text-white">Distribution</DIV>
            <DIV CLASS="card-body">
              <DIV CLASS="row">

                <DIV CLASS="col">
                  <DIV CLASS="card $ownBrandDistKPIColor text-white mx-auto" STYLE="width:8em;">
                    <DIV CLASS="card-body text-center text-white">
                      <B>$ownBrandDistPctChg</B><BR>
                      Change
                    </DIV>
                  </DIV>
                </DIV>

                <DIV CLASS="col">
                  <DIV CLASS="card $ownBrandDistColor text-white mx-auto" STYLE="width:8em;">
                    <DIV CLASS="card-body text-center text-white">
                      <B>$ownBrandDist</B><BR>
                      Distribution
                    </DIV>
                  </DIV>
                </DIV>

              </DIV> <!-- row -->

              <P>
              <DIV ID="sparkchart-line-dist"></DIV>

<SCRIPT>
const distSparkData = {
  chart: {
    theme: "fusion",
    showclosevalue: "1",
    showopenvalue: "1",
    setadaptiveymin: "1",
    showHighLowValue: "0"
  },
  dataset: [
    {
      data: [ $jsonData ]
    }
  ]
};

FusionCharts.ready(function() {
  var myChart = new FusionCharts({
    type: "sparkline",
    renderAt: "sparkchart-line-dist",
    width: "100%",
    height: "50",
    dataFormat: "json",
    dataSource: distSparkData
  }).render();
});
</SCRIPT>



              $insight

            </DIV>
            <DIV CLASS="card-footer border-white bg-white text-end">
              <BUTTON CLASS="btn btn-primary" TYPE="button" onclick="location.href='distBrand.cld?pm=$priceModelID'"><I CLASS="bi bi-building"></I> Brands</BUTTON>
<!---
              <BUTTON CLASS="btn btn-primary disabled" TYPE="button" onclick="location.href='distPPG.cld?pm=$priceModelID'"><I CLASS="bi bi-cart4"></I> Promo Groups</BUTTON>
              <BUTTON CLASS="btn btn-primary disabled" TYPE="button" onclick="location.href='distItem.cld?pm=$priceModelID'"><I CLASS="bi bi-upc"></I> Items</BUTTON>
--->
            </DIV>
          </DIV>
        </DIV>

      </DIV>  <!-- card row -->
END_HTML


  print <<END_HTML;
    </DIV>
  </DIV>
</DIV>
<P>
END_HTML

  print_html_footer();

  AInsights_audit($db, $userID, $priceModelID, "Viewed details for $prodNameHash{$ppgID} in $geoNameHash{$geoID}");
  $activity = "PRICING: $first $last viewed model details for $prodNameHash{$ppgID} / $geoNameHash{$geoID} in pricing model $modelName in $dsName";
  utils_slack($activity);

#EOF
