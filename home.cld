#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;
use Email::Valid;
use MIME::Base64;
use Digest::SHA qw(sha256_hex);

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<SCRIPT SRC="/infinite-scroll/infinite-scroll.pkgd.min.js"></SCRIPT>

<SCRIPT>
let heartbeat = setInterval(function(){detectNewItems()}, 60000);

function closeFeedItem(itemID)
{
  let url = '/app/dsr/ajaxAPI.cld?svc=feed_del_item&item=' + itemID;
  let key = '#feed_' + itemID;

  \$.get(url);
  \$(key).hide();
}



function detectNewItems(itemID)
{
  let url = '/app/dsr/ajaxAPI.cld?svc=feed_new_items&item=' + $newestItemID;

  \$.get(url, function(data)
  {
    if (data > 0)
    {
      let alertTxt = `<a href=''>See ` + data + 'recent items</a>';
      \$('#div-new-items-alert').html(alertTxt);
      \$('#div-new-items-alert').show();
      clearInterval(heartbeat);
      heartbeat = setInterval(function(){detectNewItems()}, 300000);
    }
  });
}
</SCRIPT>
</HEAD>

<BODY>
END_HTML

  $usagePct = KAPutil_get_org_quota_used($db, $userID);

  if ($usagePct > 99)
  {
    print <<END_HTML;
<DIV CLASS="alert alert-danger" STYLE="width:60%; position:absolute; margin-left:20%; z-index:9999;">
  <CENTER><STRONG>WARNING: </STRONG>Your Koala Analytics instance has exceeded its storage limit ($usagePct%), and is now in read-only mode. Delete unneeded data sources or contact your Koala account manager to order more storage.</CENTER>
</DIV>
END_HTML
  }
  elsif ($usagePct > 90)
  {
    print <<END_HTML;
<DIV CLASS="alert alert-warning" STYLE="width:50%; position:absolute; margin-left:25%; z-index:9999;">
  <CENTER><STRONG>WARNING: </STRONG>Your Koala Analytics instance is approaching its storage limit ($usagePct%). Contact your Koala account manager to order more storage.</CENTER>
</DIV>
END_HTML
  }

  print_html_navbar($db, $userID, $first, $last, $orgName);

}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #create the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #grab a clickThrough status update, if there is one
  $q = new CGI;
  $clickThrough = $q->param('ct');
  $action = $q->param('a');
  $feedItemID = $q->param('i');

  #let super admins view another user's feed
  $feedUserID = $q->param('u');
  if (($acctType < 10) || ($feedUserID < 1))
  {
    $feedUserID = $userID;
  }

  #connect to user login database
  $db = KAPutil_connect_to_database();

  #get the user's info from the database
  $query = "SELECT password, orgID, first, last, acctType, disabled, licensePrep, licenseAInsights, licenseForecast, betaTester \
      FROM users WHERE ID=$userID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  #extract the user's info
  ($password, $orgID, $first, $last, $acctType, $disabled, $licensePrep, $licenseAInsights, $licenseForecast, $betaTester) = $dbOutput->fetchrow_array;

  #if the user accepted a clickThrough agreement, update the users table
  if ($clickThrough == 1)
  {
    $query = "UPDATE app.users SET clickThrough=1 WHERE ID=$userID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    #post a click-through accepted notification to Slack
    $activity = "$first $last ($email) from $orgName accepted the click-through license agreement";
    utils_slack($activity);
  }

  #if the user is a viewer, redirect to the reports
  if ($acctType == 0)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: /app/rpt/main\n");
    print($session->header());
    exit;
  }

  #get the ID of the most recent feed item for "you have new items" display
  #purposes
  $query = "SELECT ID FROM app.feed WHERE userID=$feedUserID ORDER BY ID DESC \
      LIMIT 1";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($newestItemID) = $dbOutput->fetchrow_array;
  if ($newestItemID < 1)
  {
    $newestItemID = 1;
  }

  print_html_header();

  #if the account is disabled, toss an error
  if ($disabled == 1)
  {
    exit_error("This account has been disabled.");
  }

  #if we've been asked to clear all of the user's feed items of a specific type
  if ($action eq "c")
  {
    $query = "SELECT type FROM app.feed WHERE ID=$feedItemID AND userID=$feedUserID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($feedItemType) = $dbOutput->fetchrow_array;

    $query = "DELETE FROM app.feed where userID=$feedUserID AND type='$feedItemType'";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);
  }

  print <<END_HTML;
<DIV CLASS="container-fluid bg-light">

  <DIV CLASS="row">

    <DIV CLASS="col-3"> <!-- nav -->
    <DIV STYLE="position:sticky; top:0;">

      <UL CLASS="nav flex-column" >
END_HTML

  if (($Lib::KoalaConfig::cloudtype eq "dev") || ($licensePrep > 0))
  {
    $b64Email = encode_base64($email, "");
    $b64Redirect = encode_base64("/app/prep/main.cld", "");
    $credentialCode = sha256_hex("$password");

    print <<END_HTML;
        <LI CLASS="nav-item">
          <a class="nav-link" HREF="$Lib::KoalaConfig::prepHostURL/app/sso.cld?e=$b64Email&c=$credentialCode&r=$b64Redirect">
            <img height="25px" src="/icons/ios_prep_blue.png">
            Data Prep
          </a>
        </LI>
END_HTML
  }

  if ($acctType > 0)
  {
    print <<END_HTML;
        <LI CLASS="nav-item">
          <a class="nav-link" HREF="/app/dsr/main.cld">
            <img height="25px" src="/icons/ios_db_blue.png">
            Data Sources
          </a>
        </LI>
END_HTML
  }

  print <<END_HTML;
        <LI CLASS="nav-item">
          <a class="nav-link" HREF="/app/rpt/main">
            <img height="25px" src="/icons/ios_reports_blue.png">
            Reporting
          </a>
        </LI>
END_HTML

if (($Lib::KoalaConfig::cloudtype eq "dev") || (($betaTester == 1) && ($licenseForecast == 1)))
{
  print <<END_HTML;
        <LI CLASS="nav-item">
          <a class="nav-link" HREF="/app/forecast/main.cld">
            <img height="25px" src="/icons/ios_kcast_blue.png">
            KCast
          </a>
        </LI>
END_HTML
  }

  if ($Lib::KoalaConfig::cloudtype eq "dev")
  {
    print <<END_HTML;
        <LI CLASS="nav-item">
          <a class="nav-link" HREF="/app/analytics/ainsights/main.cld">
            <img height="25px" src="/icons/ios_ai_blue.png">
            AInsights
          </a>
        </LI>
END_HTML
  }


  if ($Lib::KoalaConfig::cloudtype eq "dev")
  {
    print <<END_HTML;
        <LI CLASS="nav-item">
          <a class="nav-link" HREF="/app/analytics/main.cld">
            <img height="25px" src="/icons/ios_retail_blue.png">
            Retail Analytics
          </a>
        </LI>
END_HTML
  }

  print <<END_HTML;
        <LI CLASS="nav-item">
          <a class="nav-link" HREF="/app/admin/userSettings.cld">
            <img height="25px" src="/icons/ios_settings_blue.png">
            User Settings
          </a>
        </LI>
END_HTML

  #if we're an administrator, output admin icons
  if ($acctType > 4)
  {
    print <<END_HTML;
        <LI CLASS="nav-item">
          <a class="nav-link" HREF="/app/admin/home.cld">
          <img height="25px" src="/icons/ios_admin_blue.png">
            Administration
          </a>
        </LI>
END_HTML
  }

  print <<END_HTML;
</ul>
</DIV>

    </DIV>

    <DIV CLASS="col-9">

    <p>
    <div id="div-new-items-alert" class="alert alert-warning" style="display:none; text-align:center;">
    </div>

    <div class="article-feed">
    </div>

    <!-- status elements -->
    <div class="scroller-status">
      <div class="infinite-scroll-request loader-ellips text-center">
        ...
      </div>
    </div>

    <!-- pagination has path -->
    <p class="pagination">
      <a class="pagination__next" href="homeFeed.cld?u=$feedUserID&id=0">Next page</a>
    </p>
    <SCRIPT>
    \$('.article-feed').infiniteScroll(
    {
      path: '.pagination__next',
      append: '.article',
      status: '.scroller-status',
      hideNav: '.pagination',
      prefill: true,
      history: false
    });
    </SCRIPT>

    </DIV>

  </DIV>
</DIV>
<P>&nbsp;</P>
END_HTML

  $session->param('feedID', 0);
  $session->flush();

  print_html_footer();

#EOF
