#!/usr/bin/perl

use lib "/opt/apache/app/";

use File::Copy;
use Text::CSV_XS;
use DateTime::Duration;

use Lib::KoalaConfig;



#---------------------------------------------------------------------------
#
# Output log info to console and log file
#


sub DBG
{
  my ($date);

  my ($str) = @_;

  $date = localtime();
  print STDERR "$date: $str\n";
}



#---------------------------------------------------------------------------
#
# Determine if the upload for the specified data set is complete:
#   * If the file has a last modified date of at least an hour ago
#

sub upload_complete
{
  my ($fileEpochTime, $timeDiff);

  my ($filename) = @_;


  $fileEpochTime = (stat("/data/mtolive-iri/$filename"))[9];
  $timeDiff = time() - $fileEpochTime;

  #if the data file has been modified in the past hour, we're going
  #to wait to process the data set
  if ($timeDiff < 1800)
  {
    return(0);
  }
  else
  {
    return(1);
  }
}



#---------------------------------------------------------------------------

  DBG("Starting update load run");

  chdir("/data/mtolive-iri");

  $csv = Text::CSV_XS->new( {binary => 1, eol => $/} );

  #see if we have new data dictionary info
  $characteristicsFile = "/data/mtolive-iri/Mt. Olive PPR Characteristics_Data Template_Rule Creation.csv";
  $dataDictionaryFile = "/data/mtolive-iri/NEW MT. OLIVE DATA DICTIONARY.xlsx";
  $lookupFile = "/data2/mtolive-work/product-lookup.csv";
  $characteristicsUpdate = (stat($characteristicsFile))[9];
  $dataDictionaryUpdate = (stat($dataDictionaryFile))[9];
  $lookupUpdate = (stat($lookupFile))[9];

  if (($lookupUpdate < $dataDictionaryUpdate) ||
      ($lookupUpdate < $characteristicsFile))
  {
    DBG("Updating master product lookup file");

    #convert the data dictionary file to a CSV file
    `/usr/bin/python /opt/xlsx2csv/xlsx2csv.py -e -s 1 -i "/data/mtolive-iri/NEW MT. OLIVE DATA DICTIONARY.xlsx" /data2/mtolive-work/data_dictionary_work.csv`;

    open(INPUT, "/data2/mtolive-work/data_dictionary_work.csv") or
        die("Unable to open /data2/mtolive-work/data_dictionary_work.csv, $!");
    open(OUTPUT, ">/data2/mtolive-work/data_dictionary_out.csv") or
        die("Unable to open /data2/mtolive-work/data_dictionary_out.csv, $!");

    #grab header line, remove Products field
    $line = <INPUT>;
    $csv->parse($line);
    @dataDictionaryHeaders = $csv->fields();
    splice(@dataDictionaryHeaders, 0, 1);

    $csv->combine(@dataDictionaryHeaders);
    $csvString = $csv->string();
    print OUTPUT "$csvString";

    #parse data dictionary, fixing UPC formatting and removing product column
    while ($line = <INPUT>)
    {
      $csv->parse($line);
      @columns = $csv->fields();

      #remove first (Product) column
      splice(@columns, 0, 1);

      #zero-pad the UPC field to get a consistent 10 digits
      $columns[0] = sprintf("%010s", $columns[0]);

      #strip unprintable characters from text
      $idx = 0;
      foreach $tmp (@columns)
      {
        $columns[$idx] =~ s/[^\x09\x0A\x0D\x20-\x7E]//g;
        $idx++;
      }

      #write data line out to file
      $csv->combine(@columns);
      $csvString = $csv->string();
      print OUTPUT "$csvString";

      #create data dictionary hash, indexed by UPC
#      $upc = $columns[0];
#      $dataDictionaryHash{$upc} = \@columns;
    }

    close(INPUT);
    close(OUTPUT);

    move("/data2/mtolive-work/data_dictionary_out.csv", "/data2/mtolive-work/data_dictionary.csv")
        or DBG("ERROR: Unable to rename /data2/mtolive-work/data_dictionary_out.csv, $!");
    unlink("/data2/mtolive-work/data_dictionary_work.csv");



    #handle IRI characteristics file
    open(INPUT, "/data/mtolive-iri/Mt. Olive PPR Characteristics_Data Template_Rule Creation.csv") or
        die("Unable to open /data/mtolive-iri/Mt. Olive PPR Characteristics_Data Template_Rule Creation.csv, $!");
    open(OUTPUT, ">/data2/mtolive-work/product-lookup_out.csv") or
        die("Unable to open /data2/mtolive-work/product-lookup_out.csv, $!");

    #grab header line, remove unneeded Geography,Time,Product fields
    $line = <INPUT>;
    $csv->parse($line);
    @iriCharacteristicsHeaders = $csv->fields();
    splice(@iriCharacteristicsHeaders, 0, 3);

    $csv->combine(@iriCharacteristicsHeaders);
    $csvString = $csv->string();
    print OUTPUT "$csvString";

    #parse data dictionary, fixing UPC formatting and removing unneeded columns
    while ($line = <INPUT>)
    {
      $csv->parse($line);
      @columns = $csv->fields();

      #remove first 3 columns
      splice(@columns, 0, 3);

      #zero-pad the UPC field to get a consistent 10 digits
      $columns[0] = sprintf("%010s", $columns[0]);

      #strip unprintable characters from text
      $idx = 0;
      foreach $tmp (@columns)
      {
        $columns[$idx] =~ s/[^\x09\x0A\x0D\x20-\x7E]//g;
        $idx++;
      }

      #write data line out to file
      $csv->combine(@columns);
      $csvString = $csv->string();
      print OUTPUT "$csvString";

      #create data dictionary hash, indexed by UPC
#      $upc = $columns[0];
#      $dataDictionaryHash{$upc} = \@columns;
    }

    close(INPUT);
    close(OUTPUT);

    move("/data2/mtolive-work/product-lookup_out.csv", "/data2/mtolive-work/product-lookup.csv")
        or DBG("ERROR: Unable to rename /data2/mtolive-work/product-lookup_out.csv, $!");
  }



  #run through all CSV data files in upload area
  opendir(DIRHANDLE, "/data/mtolive-iri") or die("$!, unable to open /data/mtolive-iri");
  while (defined($filename = readdir(DIRHANDLE)))
  {

    if (!($filename =~ m/(.*Week.*)\.csv$/i))
    {
      DBG("Skipping non-data file $filename");
      next;
    }
    $dataSetName = $1;
    $factsFileName = $filename;

    #make sure the data set is completely uploaded
    if (upload_complete($filename) < 1)
    {
      DBG("Skipping $filename, still potentially being uploaded");
      next;
    }

    DBG("Processing $factsFileName");

    #run through data file, fixing UPC and converting to valid CSV
    open(INPUT, "/data/mtolive-iri/$factsFileName") or
        die("Unable to open /data/mtolive-iri/$factsFileName, $!");
    open(OUTPUT, ">/data2/mtolive-work/facts.csv") or
        die("Unable to open /data2/mtolive-work/facts.csv, $!");

    #grab header line
    $line = <INPUT>;
    $csv->parse($line);
    @factsHeaders = $csv->fields();

    $csv->combine(@factsHeaders);
    $csvString = $csv->string();
    print OUTPUT "$csvString";

    #parse facts data, fixing UPC formatting and converting to valid CSV
    while ($line = <INPUT>)
    {
      $csv->parse($line);
      @columns = $csv->fields();

      #zero-pad the UPC field to get a consistent 10 digits
      $columns[3] = sprintf("%010s", $columns[3]);

      #fix issues with customer-supplied numerical values from Excel
      $idx = 0;
      foreach $tmp (@columns)
      {

        #skip first 3 columns containing text data
        if ($idx <= 3)
        {
          $idx++;
          next;
        }

        #trim whitespace off front and end of values
        $columns[$idx] =~ s/^\s+//;
        $columns[$idx] =~ s/\s+$//;

        #remove currency symbol if it's present
        if ($columns[$idx] =~ m/^\$([\d,\.]+)/)
        {
          $columns[$idx] = $1;
        }

        #remove commas if present
        $columns[$idx] =~ s/\,//g;

        $idx++;
      }

      #write data line out to file
      $csv->combine(@columns);
      $csvString = $csv->string();
      print OUTPUT "$csvString";
    }

    close(INPUT);
    close(OUTPUT);


    #compress the data and lookup files together
    chdir("/data2/mtolive-work");
    $zipFileName = $dataSetName . ".zip";
    DBG("dataSetName Creating output zip file $zipFileName");
    `/usr/bin/zip \"$zipFileName\" facts.csv data_dictionary.csv product-lookup.csv 2>&1`;

    #move final zip file to Data Prep-accessible directory
    DBG("Renaming /data2/mtolive-work/$zipFileName to /data2/mtolive/$zipFileName");
    unlink("/data2/mtolive/$zipFileName");
    move("/data2/mtolive-work/$zipFileName", "/data2/mtolive/$zipFileName")
        or DBG("ERROR: Unable to rename working zip file, $!");

    unlink("/data/mtolive-iri/$factsFileName");
  }


#EOF
