
package Lib::BuildCube;

use lib "/opt/apache/app/";

use Exporter;
use Lib::Cubes;
use Lib::DataSel;
use Lib::DataSources;
use Lib::DSRMeasures;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::Reports;
use Lib::Social;
use Lib::WebUtils;

our @ISA = ('Exporter');

our @EXPORT = qw(
    &cube_create_table
    &cube_telemetry
    &cube_base_items
    &cube_rollups
    &cube_needs_updating
    &cube_set_status
    &cube_build);

our $cubeUpdateID;


#Variables global to this module (used for hashing memberships and values to
#cut down on database queries)
my $_cacheDSID;

my %_cachedExpandedProdStructs;
my %_cachedExpandedGeoStructs;
my %_cachedExpandedTimeStructs;



#-------------------------------------------------------------------------------
#
# Handle a database error of some kind during cube build
#

sub cube_db_err
{
  my ($date);

  my ($db, $status, $text) = @_;

  if (!defined($status))
  {
    $date = localtime();
    print STDERR "$date: $text\n";
    if ($db->errstr =~ m/^MySQL server has gone away/)
    {
      die("Lost connection to database, terminating");
    }
  }
}



#-------------------------------------------------------------------------------
#
# Log telemetry information about the cube build process
#

sub cube_telemetry
{
  my ($query, $q_text);

  my ($db, $text) = @_;

  $text = ": $text\n";
  $q_text = $db->quote($text);
  $query = "UPDATE audit.telemetry_cubes \
      SET telemetry = CONCAT(telemetry, NOW(), $q_text) \
      WHERE ID=$cubeUpdateID";
  $db->do($query);
}



#-------------------------------------------------------------------------------
#
# Set the data cube creation/update status that'll be displayed to users.
# Call once with a blank status when the process is finished to unlock the
# cube.
#

sub cube_set_status
{
  my ($query, $q_text);

  my ($db, $rptID, $text) = @_;

  if (length($text) > 1)
  {
    $q_text = $db->quote($text);
  }
  else
  {
    $q_text = "NULL";
  }

  $query = "UPDATE cubes SET status=$q_text WHERE ID=$rptID";
  $db->do($query);
  $query = "UPDATE app.jobs SET lastAction=NOW(), status=$q_text \
      WHERE PID=$$ AND node='$Lib::KoalaConfig::nodeName'";
  $db->do($query);
}



#-------------------------------------------------------------------------------
#
# Create the table that'll hold the cube inside the data source schema. This
# should be called as the first step of creating a new cube.
#

sub cube_create_table
{
  my ($measure, $query, $rptCube, $status, $ownerID, $orgID, $dataDir, $dsID);
  my ($dbOutput);
  my (@measureIDs);

  my ($db, $dsSchema, $rptID) = @_;

  cube_telemetry($db, "Creating new report cube schema");

  #determine if we're using an external table space for capacity/IO reasons
  $dsSchema =~ m/datasource_(\d+)/;
  $dsID = $1;
  $query = "SELECT userID FROM app.dataSources WHERE ID=$dsID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  cube_db_err($db, $status, $query);
  ($ownerID) = $dbOutput->fetchrow_array;

  $orgID = KAPutil_get_user_org_id($db, $ownerID);

  $query = "SELECT dataStorage FROM app.orgs WHERE ID=$orgID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  cube_db_err($db, $status, $query);
  ($dataDir) = $dbOutput->fetchrow_array;

  if (length($dataDir) > 2)
  {
    $dataDir = $dataDir . "/mysql/";
    $dataDir = "DATA DIRECTORY='$dataDir'";
  }
  else
  {
    $dataDir = "";
  }

  #get the measureIDs to include in the cube
  @measureIDs = datasel_get_dimension_items($db, $rptID, "m");

  #build up name of cube table
  $rptCube = "__rptcube_" . $rptID;

  $query = "CREATE TABLE $dsSchema.$rptCube \
      ( \
      product VARCHAR(48), \
      geography VARCHAR(16), \
      time VARCHAR(16), \
      sortTime VARCHAR(24),";

  #create columns for each selected measure, attribute, and segmentation
  foreach $measure (@measureIDs)
  {
    if (($measure =~ m/ATT_/) || ($measure =~ m/SEG_/))
    {
      $query .= $measure . " VARCHAR(128) DEFAULT NULL,";
    }
    else
    {
      $query .= "measure_" . $measure . " DOUBLE DEFAULT NULL,";
    }
  }

  $query .= "PRIMARY KEY (product, geography, time), \
      INDEX product (product), \
      INDEX geography (geography), \
      INDEX time (time)) $dataDir";

  $status = $db->do($query);
  cube_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------------
#
# Bring the cube's base items (and lists of base items) up to date with the
# cube definition.
#

sub cube_base_items
{
  my ($rptCube, $query, $dbOutput, $status, $measureCount, $i, $key);
  my ($prodString, $geoString, $timeString, $measureString, $measureVal);
  my ($geoSelString, $timeSelString, $geoID, $timeID, $measureVals, $valSet);
  my ($product, $geography, $time, $measure, $measurelist, $count, $pct);
  my ($measurevals, @measurevalues, $totalBase);
  my (@productIDs, @geographyIDs, @timeIDs, @measureIDs, @valuesArray);
  my (@emptyValuesArray);
  my (%tmpSeenHash);

  my ($db, $dsSchema, $rptID) = @_;

  #build up name of cube table
  $rptCube = "__rptcube_" . $rptID;

  #get the cube's base items
  #NB: These functions also expand lists into base items
  cube_telemetry($db, "Getting report base items");
  @productIDs = datasel_get_selected_base_items($db, $rptID, "p");
  @geographyIDs = datasel_get_selected_base_items($db, $rptID, "g");
  @timeIDs = datasel_get_selected_base_items($db, $rptID, "t");
  @measureIDs = datasel_get_selected_base_items($db, $rptID, "m");

  $totalBase = scalar(@productIDs) * scalar(@geographyIDs) * scalar(@timeIDs);
  cube_telemetry($db, "Report cube has $totalBase TGP tuples");

  #if there aren't any base items in the cube, we're done here
  if ($totalBase < 1)
  {
    return;
  }

  #if there aren't any measures in the cube, we're done here
  if (scalar(@measureIDs) < 1)
  {
    return;
  }

  #recalculate any base measures that depend on structures that have changed
  #since the last time they were calculated (ex: shares of aggregates)
#  DSRmeasures_recalculate_outofdate_measures($db, $dsSchema, @measureIDs);

  #build up list of measures to select from master facts table
  $measurelist = "";
  $measureCount = 0;
  foreach $measure (@measureIDs)
  {
    $measureCount++;
    $measurelist .= "measure_" . $measure . ",";
  }
  chop($measurelist);

  cube_telemetry($db, "Report cube has $measureCount measures explicitly included");

  $count = 0;

  cube_telemetry($db, "Starting population of base items");
  cube_set_status($db, $rptID, "Populating base items 0% (0 of $totalBase)");

  #build up SQL selection strings for geographies and time periods
  $geoSelString = "";
  $timeSelString = "";
  foreach $geoID (@geographyIDs)
  {
    $geoSelString .= "'$geoID',";
  }
  foreach $timeID (@timeIDs)
  {
    $timeSelString .= "'$timeID',";
  }
  chop($geoSelString);
  chop($timeSelString);

  #run through each product in the cube, selecting all of the geo/time data slices
  #associated with it
  foreach $product (@productIDs)
  {

    #NB: we're creating "full" cubes (as opposed to "sparse" cubes), so we need to
    #    add entries for TGP tuples that don't exist in the underlying data source.
    #    We handle that by keeping track of every geography and time we see for the
    #    current product, and building up an SQL INSERT for all of the unseen entries
    #    at the end of this foreach block
    undef(%tmpSeenHash);
    foreach $geography (@geographyIDs)
    {
      foreach $time (@timeIDs)
      {
        $tmpSeenHash{"$geography.$time"} = 1;
      }
    }

    #get the values for the selected measures for the current product slice
    $query = "SELECT geographyID, timeID, $measurelist FROM $dsSchema.facts \
        WHERE productID = $product AND geographyID IN ($geoSelString) AND timeID IN ($timeSelString)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    cube_db_err($db, $status, $query);

    while (@measurevalues = $dbOutput->fetchrow_array)
    {

      #extract the geography and time ID's from the results hash
      $geography = shift(@measurevalues);
      $time = shift(@measurevalues);

      #mark the geo and time combination as "seen"
      delete($tmpSeenHash{"$geography.$time"});

      #every 500 rows, update the user
      $count++;
      if (($count % 500) == 0)
      {
        $pct = ($count / $totalBase) * 100;
        $pct = int($pct);
        cube_set_status($db, $rptID, "Populating base items $pct\% ($count of $totalBase)");
      }

      #if we didn't get any data back, set everything to NA (NULL)
      if (scalar(@measurevalues) == 0)
      {
        for ($i = 0; $i < $measureCount; $i++)
        {
          $measurevalues[$i] = "NULL";
        }
      }

      #set empty measure values to NULL
      foreach $measureVal (@measurevalues)
      {
        if (length($measureVal) < 1)
        {
          $measureVal = "NULL";
        }
      }

      #turn the measure value array into a CSV string
      $measureVals = join(',', @measurevalues);

      #add this tuple's values to the array for bulk insertion
      push(@valuesArray, "('$product', '$geography', '$time', $measureVals), ");

      #if we have 50 records ready for bulk insertion, build & run the SQL
      if (scalar(@valuesArray) > 49)
      {
        $query = "INSERT INTO $dsSchema.$rptCube (product, geography, time, $measurelist) VALUES ";
        foreach $valSet (@valuesArray)
        {
          $query .= "$valSet";
        }
        chop($query);  chop($query);

        $status = $db->do($query);
        cube_db_err($db, $status, $query);

        undef(@valuesArray);
      }
    }

    #insert empty rows for geo-time combos with no data for this product
    if (scalar(%tmpSeenHash) > 0)
    {
      undef(@emptyValuesArray);
      foreach $key (keys %tmpSeenHash)
      {
        $key =~ m/^(.*?)\.(.*)$/;
        $geography = $1;
        $time = $2;
        push(@emptyValuesArray, "('$product', '$geography', '$time'), ");

        $count++;
      }
      $query = "INSERT INTO $dsSchema.$rptCube (product, geography, time) VALUES ";
      foreach $valSet (@emptyValuesArray)
      {
        $query .= "$valSet";
      }
      chop($query);  chop($query);

      $status = $db->do($query);
      cube_db_err($db, $status, $query);
    }
  }

  #bulk insert any remaining records in the valueArray to finish out the file
  if (scalar(@valuesArray) > 0)
  {
    $query = "INSERT INTO $dsSchema.$rptCube (product, geography, time, $measurelist) VALUES ";
    foreach $valSet (@valuesArray)
    {
      $query .= "$valSet";
    }
    chop($query);  chop($query);

    $status = $db->do($query);
    cube_db_err($db, $status, $query);

    undef(@valuesArray);
  }

  cube_telemetry($db, "Done populating base items");
}



#-------------------------------------------------------------------------------
#
# Fill in segmentation and attribute values selected to be part of the cube.
#

sub cube_seg_attr_strings
{
  my ($rptCube, $query, $dbOutput, $status, $id, $measureID, $dim, $attrID);
  my ($fqAttrID, $colName, $attrName, $value, $q_value, $segID, $fqSegID);
  my ($segName, $segmentID, $q_segmentName, $todo, $count, $pct, $productID);
  my ($geographyID, $timeID, $name, $hierLevel, $q_name, $idx, $hierChain);
  my ($segmentationIDs, $dbOutput1, $segHierID, $curSegHierID, $key);
  my (@tmp, @measureIDs, @segmentations, @hierLevels);
  my (%cubeProductIDs, %productNameHash, %geographyNameHash, %timeNameHash);
  my (%prodUpdates, %geoUpdates, %timeUpdates, %attrValues, %segMembershipHash);
  my (%segmentsHash, %segInCube, %hierSegNameCache);

  my ($db, $dsSchema, $rptID) = @_;

  #build up name of cube table
  $rptCube = "__rptcube_" . $rptID;

  cube_set_status($db, $rptID, "Filling in segment/attribute names");

  #get list of base items included in cube, and convert to hash
  #NB: We use these to only create update statements for items that are
  #    actually part of the cube, rather than all items in the whole DS
  @tmp = datasel_get_selected_base_items($db, $rptID, "p");
  undef(%cubeProductIDs);
  foreach $id (@tmp)
  {
    $cubeProductIDs{$id} = 1;
  }

  @measureIDs = datasel_get_dimension_items($db, $rptID, "m");
  %productNameHash = dsr_get_item_name_hash($db, $dsSchema, "p");
  %geographyNameHash = dsr_get_item_name_hash($db, $dsSchema, "g");
  %timeNameHash = dsr_get_item_name_hash($db, $dsSchema, "t");

  #NB: For performance reasons, we're going to build up a hash of UPDATE
  #   statements for each dimension, hashed by the product ID. This way instead
  #   of performing potentially millions of UPDATE statements (which get
  #   slower as the table grows), we just do one for each dim item in the
  #   underlying data source

  cube_telemetry($db, "Collating segment/attribute membership information");

  undef(%prodUpdates);
  undef(%geoUpdates);
  undef(%timeUpdates);

  foreach $measureID (@measureIDs)
  {

    #handle an attribute field
    if ($measureID =~ m/^([PGT])ATT_(\d+)/)
    {

      #get the attribute's ID and dimension from its column name
      $dim = lc($1);
      $attrID = $2;
      $fqAttrID = "ATT_" . $attrID;

      #figure out our dimension's ID column name in the facts table
      if ($dim eq "p")
      {
        $colName = "product";
        $attrName = $productNameHash{$fqAttrID};
      }
      elsif ($dim eq "g")
      {
        $colName = "geography";
        $attrName = $geographyNameHash{$fqAttrID};
      }
      elsif ($dim eq "t")
      {
        $colName = "time";
        $attrName = $timeNameHash{$fqAttrID};
      }

      #get a hash of values for this attribute
      %attrValues = DSRattr_get_values_hash($db, $dsSchema, $dim, $attrID);

      cube_set_status($db, $rptID, "Populating $colName attribute $attrName");

      #insert the attribute values into the report cube
      foreach $id (keys %attrValues)
      {
        $value = $attrValues{$id};
        $q_value = $db->quote($value);

        #add the update subquery to the appropriate hash
        if (($dim eq "p") && ($cubeProductIDs{$id} > 0))
        {
          $prodUpdates{$id} .= " $measureID=$q_value,";
        }
        elsif ($dim eq "g")
        {
          $geoUpdates{$id} .= " $measureID=$q_value,";
        }
        elsif ($dim eq "t")
        {
          $timeUpdates{$id} .= " $measureID=$q_value,";
        }
      }
    }

    #handle a segmentation field
    if ($measureID =~ m/^([PGT])SEG_(\d+)/)
    {

      #get the segment's ID and dimension from its column name
      $dim = lc($1);
      $segID = $2;
      $fqSegID = "SEG_" . $segID;

      #figure out our dimension's ID column name in the facts table
      if ($dim eq "p")
      {
        $colName = "product";
        $segName = $productNameHash{$fqSegID};
      }
      elsif ($dim eq "g")
      {
        $colName = "geography";
        $segName = $geographyNameHash{$fqSegID};
      }
      elsif ($dim eq "t")
      {
        $colName = "time";
        $segName = $timeNameHash{$fqSegID};
      }

      #get the segment each item is a member of for the current segmentation
      %segMembershipHash = DSRseg_get_segment_membership_hash($db, $dsSchema, $dim, $segID);

      #get the name of the segments in this segmentation, hashed by ID
      %segmentsHash = DSRseg_get_segments_hash($db, $dsSchema, $dim, $segID);

      #run through each product in the segmentation, and add the membership
      #to the product's UPDATE statement
      foreach $id (keys %segMembershipHash)
      {
        $segmentID = $segMembershipHash{$id};
        $q_segmentName = $db->quote($segmentsHash{$segmentID});

        #add the update subquery to the appropriate hash
        if (($dim eq "p") && ($cubeProductIDs{$id} > 0))
        {
          $prodUpdates{$id} .= " $measureID=$q_segmentName,";
        }
        elsif ($dim eq "g")
        {
          $geoUpdates{$id} .= " $measureID=$q_segmentName,";
        }
        elsif ($dim eq "t")
        {
          $timeUpdates{$id} .= " $measureID=$q_segmentName,";
        }
      }
    }
  }

  #run through each product UPDATE statement, and apply it to the export table
  cube_telemetry($db, "Adding product segment/attribute info to report cube");

  $todo = keys %prodUpdates;
  $count = 0;
  cube_set_status($db, $rptID, "Adding product fields, 0% ($count of $todo)");
  foreach $productID (keys %prodUpdates)
  {

    if (($count % 100) == 0)
    {
      $pct = ($count / $todo) * 100;
      $pct = int($pct);
      cube_set_status($db, $rptID, "Adding product fields, $pct\% ($count of $todo)");
    }

    chop($prodUpdates{$productID});
    $query = "UPDATE $dsSchema.$rptCube SET $prodUpdates{$productID} \
        WHERE product='$productID'";
    $status = $db->do($query);
    cube_db_err($db, $status, $query);

    $count++;
  }


  #run through each geography UPDATE statement, and apply it to the export table
  cube_telemetry($db, "Adding geography segment/attribute info to report cube");

  $todo = keys %geoUpdates;
  $count = 0;
  cube_set_status($db, $rptID, "Adding geography fields, 0% ($count of $todo)");
  foreach $geographyID (keys %geoUpdates)
  {

    if ((($count % 25) == 0) && ($todo > 0))
    {
      $pct = ($count / $todo) * 100;
      $pct = int($pct);
      cube_set_status($db, $rptID, "Adding geography fields, $pct\% ($count of $todo)");
    }

    chop($geoUpdates{$geographyID});
    $query = "UPDATE $dsSchema.$rptCube SET $geoUpdates{$geographyID} WHERE \
        geography='$geographyID'";
    $status = $db->do($query);
    cube_db_err($db, $status, $query);
  }

  #run through each time UPDATE statement, and apply it to the export table
  cube_telemetry($db, "Adding time segment/attribute info to report cube");

  $todo = keys %timeUpdates;
  $count = 0;
  cube_set_status($db, $rptID, "Adding time fields, 0% ($count of $todo)");
  foreach $timeID (keys %timeUpdates)
  {

    if (($count % 25) == 0)
    {
      $pct = ($count / $todo) * 100;
      $pct = int($pct);
      cube_set_status($db, $rptID, "Adding time fields, $pct\% ($count of $todo)");
    }

    chop($timeUpdates{$timeID});
    $query = "UPDATE $dsSchema.$rptCube SET $timeUpdates{$timeID} \
        WHERE time='$timeID'";
    $status = $db->do($query);
    cube_db_err($db, $status, $query);
  }

  #populate segmentation information for any product seg hierarchy levels
  cube_telemetry($db, "Adding hierarchy level segment info to report cube");

  %segInCube = map { $_ => 1 } @measureIDs;

  #cycle the hierarchy levels included in the report
  $query = "SELECT product FROM $dsSchema.$rptCube \
      WHERE product LIKE 'SHS_%' ORDER BY product";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  cube_db_err($db, $status, $query);
  $curSegHierID = 0;
  while (($productID) = $dbOutput->fetchrow_array)
  {

    #extract the ID of the current segmentation hierarchy
    $productID =~ m/^SHS_(\d+?)_(.*)$/;
    $segHierID = $1;
    $hierChain = $2;

    #if we're entering a new hierarchy, pull its information
    if ($segHierID != $curSegHierID)
    {
      $query = "SELECT segmentations FROM $dsSchema.product_seghierarchy \
          WHERE ID=$segHierID";
      $dbOutput1 = $db->prepare($query);
      $status = $dbOutput1->execute;
      cube_db_err($db, $status, $query);
      ($segmentationIDs) = $dbOutput1->fetchrow_array;
      @segmentations = split(',', $segmentationIDs);
      $curSegHierID = $segHierID;
    }

    #split the hierarchy chain into individual segments
    @hierLevels = split('_', $hierChain);

    #now, run through each level of the hierarchy and see if there's a matching
    #measure column in the cube we need to populate
    $idx = 0;
    foreach $hierLevel (@hierLevels)
    {
      $segID = $segmentations[$idx];
      $segID = "PSEG_$segID";
      if ($segInCube{$segID} == 1)
      {
        $segmentID = "SMT_$hierLevel";
        $name = $productNameHash{$segmentID};
        $q_name = $db->quote($name);

        #build up the UPDATE query in a cache so we only run once per segment in
        #the hierarchy for performance reasons
        $key = "$segID-$name";
        if (!$hierSegNameCache{$key})
        {
          $hierSegNameCache{$key} = "UPDATE $dsSchema.$rptCube \
              SET $segID=$q_name WHERE product IN ('$productID',";
        }
        else
        {
          $hierSegNameCache{$key} .= "'$productID',";
        }
      }

      $idx++;
    }
  }

  #execute each of the batched up hierarchy level name queries
  foreach $key (keys %hierSegNameCache)
  {
    $query = $hierSegNameCache{$key};
    chop($query);
    $query .= ")";
    $status = $db->do($query);
    cube_db_err($db, $status, $query);
  }
}



#-------------------------------------------------------------------------------
#
# Expand an item ID for the specified dimension into its component members.
# For example, calling this on an aggregate returns all of the items that
# make up that aggregate. Calling it on a segment returns all of the items
# contained in that segments, and etc.
# The ID supplied to this function needs to have its prepended string in place
# if it's a structure so the function knows how to handle it
#
# NB: We're using 3 caches (one for each dimension) to avoid constantly going
#     back to the database to expand structures that are almost certainly used
#     a bunch of times for the reports in a data source.
#

sub cube_expand_item
{
  my ($expandedString);

  my ($db, $dsSchema, $dim, $itemID) = @_;

  #if we were passed a base item ID, no need to do anything further
  if ($itemID =~ m/^\d+/)
  {
    return($itemID);
  }

  #make sure our caches of expanded structs are still valid
  $dsSchema =~ m/datasource_(\d+)/;
  if ($_cacheDSID != $1)
  {
    $_cacheDSID = $1;
    undef(%_cachedExpandedProdStructs);
    undef(%_cachedExpandedGeoStructs);
    undef(%_cachedExpandedTimeStructs);
  }

  #look for the expanded structure value in our caches
  if (($dim eq "p") && (defined($_cachedExpandedProdStructs{$itemID})))
  {
    return($_cachedExpandedProdStructs{$itemID});
  }
  elsif (($dim eq "g") && (defined($_cachedExpandedGeoStructs{$itemID})))
  {
    return($_cachedExpandedGeoStructs{$itemID});
  }
  elsif (($dim eq "t") && (defined($_cachedExpandedTimeStructs{$itemID})))
  {
    return($_cachedExpandedTimeStructs{$itemID});
  }

  #if we were passed a list
  if ($itemID =~ m/^LIS_\d+/)
  {
    $expandedString = DSRlist_expand_items($db, $dsSchema, $dim, $itemID);
  }

  #if we were passed an aggregate
  elsif ($itemID =~ m/^AGG_\d+/)
  {
    $expandedString = DSRagg_expand_items($db, $dsSchema, $dim, $itemID);
  }

  #if we were passed a segmentation
  elsif ($itemID =~ m/^SEG_\d+/)
  {
    $expandedString = DSRsegmentation_expand_segments($db, $dsSchema, $dim, $itemID);
  }

  #if we were passed a segment
  elsif ($itemID =~ m/SMT_\d+/)
  {
    $expandedString = DSRsegment_expand_items($db, $dsSchema, $dim, $itemID);
  }

  #if we were passed a segment hierarchy level segment
  elsif ($itemID =~ m/SHS_\d+/)
  {
    $expandedString = DSRseghier_expand_items($db, $dsSchema, $dim, $itemID);
  }

  #save the expanded structure value in our caches
  if ($dim eq "p")
  {
    $_cachedExpandedProdStructs{$itemID} = $expandedString;
  }
  elsif ($dim eq "g")
  {
    $_cachedExpandedGeoStructs{$itemID} = $expandedString;
  }
  elsif ($dim eq "t")
  {
    $_cachedExpandedTimeStructs{$itemID} = $expandedString;
  }

  return($expandedString);
}



#-------------------------------------------------------------------------------
#
# Expand lists/segmentations "in place" in the supplied string containing the
# IDs of dimension items. Need this when we're building cube rows that contain
# both a list/smntn and an aggregated structure of some kind (seg, agg, etc).
#

sub cube_expand_lists_in_place
{
  my ($itemID);
  my (@baseItems, @items, @listItems);

  my ($db, $dsSchema, $dim, $itemString) = @_;

  #split the list of dimension items into an array
  @items = split(/,/, $itemString);

  undef(@baseItems);
  foreach $itemID (@items)
  {

    #if we're a list, expand ourselves and add to item array
    if ($itemID =~ m/^LIS_(\d+)/)
    {
      @listItems = DSRlist_expand_items_array($db, $dsSchema, $dim, $1);
      push(@baseItems, @listItems);
    }

    #else if we're a segmentation, add our segments to the item array
    elsif ($itemID =~ m/^SEG_(\d+)/)
    {
      @listItems = DSRsegmentation_expand_segments_array($db, $dsSchema, $dim, $1);
      push(@baseItems, @listItems);
    }

    #if we're anything else, just add to item array
    else
    {
      push(@baseItems, $itemID);
    }
  }

  return(@baseItems);
}



#-------------------------------------------------------------------------------
#
# Calculate the value of any segments selected by the user to be part of
# the cube, and then add them to the cube. Any measures that don't have
# aggregation rules defined are going to be set as "NULL" values, which
# implies NA.
#

sub cube_rollups
{
  my ($query, $dbOutput, $status, $aggRule, $segmentCount, $id);
  my ($aggID, $productID, $q_productID, $geography, $q_geography, $time);
  my ($rptCube, $measureQuery, $measureNameString, $measure, $measureName);
  my ($prodMembers, $geoMembers, $timeMembers, $smtQuery, $smtVal, $count);
  my ($segmentCount, $prodString, $geoString, $timeString, $measureString);
  my ($q_time, $pct, $geoID, $timeID, $measureID);
  my (@timeIDs, @productIDs, @geographyIDs, @measureIDs, @aggValues);
  my (@smtValues, @tmp, @insertArray, @prodSegIDs, @geoSegIDs, @timeSegIDs);
  my (@measDependencies);
  my (%measDependencyHash);

  my ($db, $dsSchema, $rptID) = @_;

  #get the selected items for this cube
  $query = "SELECT products, geographies, timeperiods, measures FROM app.cubes \
      WHERE ID=$rptID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  cube_db_err($db, $status, $query);
  ($prodString, $geoString, $timeString, $measureString) = $dbOutput->fetchrow_array;

  cube_telemetry($db, "Rolling up product aggregate/segment/hierarchy level values");

  #build up name of cube table
  $rptCube = "__rptcube_" . $rptID;

  #convert strings to item arrays
  @timeIDs = cube_expand_lists_in_place($db, $dsSchema, "t", $timeString);
  @productIDs = cube_expand_lists_in_place($db, $dsSchema, "p", $prodString);
  @geographyIDs = cube_expand_lists_in_place($db, $dsSchema, "g", $geoString);
  @measureIDs = datasel_get_selected_base_items($db, $rptID, "m");

  #grab all of the base measures used by the calculated measures in this cube
  #NB: this is a performance thing - it's faster to roll them all up here
  #    instead of later on when we're doing the actual measure calculations.
  #    We're only going two levels deep on this at the moment - anything
  #    deeper than that is going to get rolled up by the calculation code
  #    via regression later on.
  undef(@measDependencies);
  foreach $measureID (@measureIDs)
  {
    @tmp = DSRmeasures_get_calc_measure_dependencies($db, $dsSchema, $measureID);
    push(@measDependencies, @tmp);
  }

  #turn the dependency array into a hash, and clear everything we already have
  %measDependencyHash = map {$_ => 1} @measDependencies;
  foreach $id (@measureIDs)
  {
    delete($measDependencyHash{$id});
  }

  #add columns for the dependency measures to the working cube
  foreach $id (keys %measDependencyHash)
  {
    $query = "ALTER TABLE $dsSchema.$rptCube \
        ADD COLUMN measure_$id DOUBLE DEFAULT NULL";
    $status = $db->do($query);
  }

  #add the dependency measures to the list of measures we're rolling up
  foreach $id (keys %measDependencyHash)
  {
    push(@measureIDs, $id);
  }

  #build up the measure portion of the query strings
  $measureQuery = "";
  $measureNameString = "";
  foreach $measure (@measureIDs)
  {

    #build up measure's column name
    $measureName = "measure_" . $measure;

    #get the measure's aggregation rule
    $aggRule = DSRmeasures_get_agg_rule($db, $dsSchema, $measure, "p");

    #if an aggregate rule isn't defined, force the return to "NA"
    if ($aggRule eq "None")
    {
      $measureQuery .= "NULL,";
      $measureNameString .= $measureName . ",";
    }
    else
    {

      #add the measure's calc string to the query string
      $measureQuery .= $aggRule . "($measureName),";
      $measureNameString .= $measureName . ",";
    }
  }

  #trim extra trailing comma
  chop($measureQuery);
  chop($measureNameString);

  #now we're going to figure out which prods/geos/times we need to perform
  #some kind of aggregation on. We know that no matter what, we need to do
  #an aggregation for any segment, aggregate, or hierarchy level present in
  #the cube's definition, so we start by finding just those for each
  #dimension
  undef(@prodSegIDs);
  undef(@geoSegIDs);
  undef(@timeSegIDs);

  foreach $productID (@productIDs)
  {
    if ($productID =~ m/SMT|SHS|AGG/)
    {
      push(@prodSegIDs, $productID);
    }
  }
  foreach $geoID (@geographyIDs)
  {
    if ($geoID =~ m/SMT|SHS|AGG/)
    {
      push(@geoSegIDs, $geoID);
    }
  }
  foreach $timeID (@timeIDs)
  {
    if ($timeID =~ m/SMT|SHS|AGG/)
    {
      push(@timeSegIDs, $timeID);
    }
  }

  #we now have an array for each dimension that contains all of the IDs of
  #structures that need to be aggregated for the cube. If there's any
  #structure that needs to be aggregated in a dimension, then we need to
  #aggregate it for each possible value in the other two dimensions (e.g., if
  #we have a bunch of product segments in the cube we need to aggregate them
  #for all possible geography and time combinations)
  #NB: I'm running regex matches against the original ID strings instead of
  #    checking scalar values of arrays so I don't have to use a bunch of
  #    conditional storage variables to line everything up at the end
  if ($prodString =~ m/SMT|SHS|AGG/)
  {
    @geoSegIDs = @geographyIDs;
    @timeSegIDs = @timeIDs;
  }
  if ($geoString =~ m/SMT|SHS|AGG/)
  {
    @prodSegIDs = @productIDs;
    @timeSegIDs = @timeIDs;
  }
  if ($timeString =~ m/SMT|SHS|AGG/)
  {
    @prodSegIDs = @productIDs;
    @geoSegIDs = @geographyIDs;
  }

  #get the number of structures we're rolling up for user display purposes
  $count = 0;
  $segmentCount = scalar(@prodSegIDs) * scalar(@geoSegIDs) * scalar(@timeSegIDs);

  #run through each structure we need to roll up, and do it
  foreach $productID (@prodSegIDs)
  {
    $q_productID = $db->quote($productID);

    #expand segment into members
    $prodMembers = cube_expand_item($db, $dsSchema, "p", $productID);

    foreach $geography (@geoSegIDs)
    {
      $q_geography = $db->quote($geography);

      #if geography is a structure, get its members
      $geoMembers = cube_expand_item($db, $dsSchema, "g", $geography);

      foreach $time (@timeSegIDs)
      {

        #every 500 segments, update the user
        if (($count % 500) == 0)
        {
          $pct = ($count / $segmentCount) * 100;
          $pct = int($pct);
          cube_set_status($db, $rptID, "Calculating roll-ups $pct\% ($count of $segmentCount)");

          #execute all of the queued up INSERT statements
          foreach $query (@insertArray)
          {
            $status = $db->do($query);
            cube_db_err($db, $status, $query);
          }
          undef(@insertArray);
        }

        $q_time = $db->quote($time);

        #if time is a structure, get its members
        $timeMembers = cube_expand_item($db, $dsSchema, "t", $time);

        #if we're dealing with an exception (like a product segment with no
        #members), just insert an empty line and do the next TGP combo
        if ((length($prodMembers) < 1) || (length($geoMembers) < 1) ||
            (length($timeMembers) < 1) || (length($measureNameString) < 1))
        {
          $query = "INSERT INTO $dsSchema.$rptCube (product, geography, time) \
              VALUES ($q_productID, $q_geography, $q_time) \
              ON DUPLICATE KEY UPDATE product=$q_productID";
          $status = $db->do($query);
          cube_db_err($db, $status, $query);
          next;
        }

        #get the aggregated values from the facts table, and insert them
        #into the cube
        $query = "INSERT IGNORE INTO $dsSchema.$rptCube \
            (product, geography, time, $measureNameString) \
                SELECT $q_productID, $q_geography, $q_time, $measureQuery \
                FROM $dsSchema.facts \
                WHERE geographyID IN ($geoMembers) AND timeID IN($timeMembers) AND productID IN ($prodMembers)";
        push(@insertArray, $query);

        $count++;
      } #time
    } #geography
  } #product

  #execute all remaining queued up INSERT statements
  foreach $query (@insertArray)
  {
    $status = $db->do($query);
    cube_db_err($db, $status, $query);
  }
  undef(@insertArray);
}



#-------------------------------------------------------------------------------
#
# Analysts can specify elements to be subtracted from aggregate totals. We want
# to look for any aggregates included in the cube where that's the case, and
# come up with correct values for them.
# NB: note that this is overwriting any values created in cube_rollups that
#     (incorrectly) only include the additive values from the aggregate.
#

sub cube_aggregate_subtractions
{
  my ($query, $dbOutput, $status, $aggRule, $segmentCount, $id);
  my ($aggID, $productID, $q_productID, $geography, $q_geography, $time);
  my ($rptCube, $measureQuery, $measureNameString, $measure, $measureName);
  my ($prodMembers, $geoMembers, $timeMembers, $smtQuery, $smtVal, $count);
  my ($segmentCount, $prodString, $geoString, $timeString, $measureString);
  my ($q_time, $pct, $prodID, $geoID, $timeID, $measureID, $subtractMembers);
  my ($prodAggsPresent, $geoAggsPresent, $timeAggsPresent, $measureUpdateStr);
  my ($val, $idx, $aggKey);
  my (@timeIDs, @productIDs, @geographyIDs, @measureIDs, @aggValues);
  my (@smtValues, @tmp, @insertArray, @prodSegIDs, @geoSegIDs, @timeSegIDs);
  my (@measDependencies, @aggSubMeasureNames, @measureVals);
  my (%measDependencyHash, %aggSubCacheHash);

  my ($db, $dsSchema, $rptID) = @_;

  #get the selected items for this cube
  $query = "SELECT products, geographies, timeperiods, measures \
      FROM app.cubes WHERE ID=$rptID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  cube_db_err($db, $status, $query);
  ($prodString, $geoString, $timeString, $measureString) = $dbOutput->fetchrow_array;

  cube_telemetry($db, "Calculating subtractive aggregates");

  #build up name of cube table
  $rptCube = "__rptcube_" . $rptID;

  #convert strings to item arrays
  @timeIDs = cube_expand_lists_in_place($db, $dsSchema, "t", $timeString);
  @productIDs = cube_expand_lists_in_place($db, $dsSchema, "p", $prodString);
  @geographyIDs = cube_expand_lists_in_place($db, $dsSchema, "g", $geoString);

  #find every aggregate in the cube, and see if any require subtractions
  foreach $prodID (@productIDs)
  {
    if ($prodID =~ m/^AGG_(\d+)/)
    {
      $aggID = $1;
      $query = "SELECT subtractMembers FROM $dsSchema.product_aggregate \
          WHERE ID=$aggID";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      cube_db_err($db, $status, $query);
      ($subtractMembers) = $dbOutput->fetchrow_array;
      if (length($subtractMembers) > 0)
      {
        $aggSubCacheHash{"p-$prodID"} = $subtractMembers;
      }
    }
  }
  foreach $geoID (@geographyIDs)
  {
    if ($geoID =~ m/^AGG_(\d+)/)
    {
      $aggID = $1;
      $query = "SELECT subtractMembers FROM $dsSchema.geography_aggregate \
          WHERE ID=$aggID";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      cube_db_err($db, $status, $query);
      ($subtractMembers) = $dbOutput->fetchrow_array;
      if (length($subtractMembers) > 0)
      {
        $aggSubCacheHash{"g-$geoID"} = $subtractMembers;
      }
    }
  }
  foreach $timeID (@timeIDs)
  {
    if ($timeID =~ m/AGG_(\d+)/)
    {
      $aggID = $1;
      $query = "SELECT subtractMembers FROM $dsSchema.time_aggregate \
          WHERE ID=$aggID";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      cube_db_err($db, $status, $query);
      ($subtractMembers) = $dbOutput->fetchrow_array;
      if (length($subtractMembers) > 0)
      {
        $aggSubCacheHash{"t-$timeID"} = $subtractMembers;
      }
    }
  }

  #if there aren't any aggregates in the cube with subtraction required
  if (scalar(%aggSubCacheHash) < 1)
  {
    return;
  }

  #get the measures in the cube we may need to subtract from
  @measureIDs = datasel_get_selected_base_items($db, $rptID, "m");

  #grab all of the base measures used by the calculated measures in this cube
  undef(@measDependencies);
  foreach $measureID (@measureIDs)
  {
    @tmp = DSRmeasures_get_calc_measure_dependencies($db, $dsSchema, $measureID);
    push(@measDependencies, @tmp);
  }

  #turn the dependency array into a hash, and clear everything we already have
  %measDependencyHash = map {$_ => 1} @measDependencies;
  foreach $id (@measureIDs)
  {
    delete($measDependencyHash{$id});
  }

  #add the dependency measures to the list of measures we're rolling up
  foreach $id (keys %measDependencyHash)
  {
    push(@measureIDs, $id);
  }

  #build up the measure portion of our aggregate query string, and the array of
  #measure ID's we're going to use to subtract from existing values in cube
  $measureQuery = "";
  foreach $measure (@measureIDs)
  {

    #build up measure's column name
    $measureName = "measure_" . $measure;

    #get the measure's aggregation rule
    $aggRule = DSRmeasures_get_agg_rule($db, $dsSchema, $measure, "p");

    #XXX for now, we're only doing subtraction on additive measures
    if ($aggRule ne "SUM")
    {
      next;
    }

    #add the measure's calc string to the query string
    $measureQuery .= $aggRule . "($measureName),";
    push(@aggSubMeasureNames, $measureName)
  }

  #trim extra trailing comma
  chop($measureQuery);

  #if there aren't any measures in the cube for us to subtract on
  if (length($measureQuery) < 1)
  {
    return;
  }

  #now we're going to figure out which prods/geos/times we need to perform
  #an aggregate subtraction on. We know that we need to do this for any
  # aggregate that has subtractMembers, no matter what, so we find those
  #based on what's in the subtractMembers hash
  undef(@prodSegIDs);
  undef(@geoSegIDs);
  undef(@timeSegIDs);
  $prodAggsPresent = $geoAggsPresent = $timeAggsPresent = 0;

  foreach $aggKey (keys %aggSubCacheHash)
  {
    if ($aggKey =~ m/p-(.*)/)
    {
      push(@prodSegIDs, $1);
      $prodAggsPresent = 1;
    }
    elsif ($aggKey =~ m/g-(.*)/)
    {
      push(@geoSegIDs, $1);
      $geoAggsPresent = 1;
    }
    elsif ($aggKey =~ m/t-(.*)/)
    {
      push(@timeSegIDs, $1);
      $timeAggsPresent = 1;
    }
  }

  #we now have an array for each dimension that contains all of the IDs of
  #aggregates that require a subtraction operation. If there's any aggregate
  #needing subtraction in a particular dimension, then we need to
  #do that for each possible value in the other two dimensions (e.g., if
  #we have a subtraction geography aggregate, we need to do the subtraction op
  #for all possible geography and time combinations)
  if ($prodAggsPresent > 0)
  {
    @geoSegIDs = @geographyIDs;
    @timeSegIDs = @timeIDs;
  }
  if ($geoAggsPresent > 0)
  {
    @prodSegIDs = @productIDs;
    @timeSegIDs = @timeIDs;
  }
  if ($timeAggsPresent > 0)
  {
    @prodSegIDs = @productIDs;
    @geoSegIDs = @geographyIDs;
  }

  #get the number of structures we're going to run the subtraction on
  $count = 0;
  $segmentCount = scalar(@prodSegIDs) * scalar(@geoSegIDs) * scalar(@timeSegIDs);

  #run through each structure we need to roll up, and do it
  foreach $productID (@prodSegIDs)
  {
    $q_productID = $db->quote($productID);

    #expand product into members
    if (length($aggSubCacheHash{"p-$productID"}) > 0)
    {
      $prodMembers = $aggSubCacheHash{"p-$productID"};
    }
    else
    {
      $prodMembers = cube_expand_item($db, $dsSchema, "p", $productID);
    }

    foreach $geography (@geoSegIDs)
    {
      $q_geography = $db->quote($geography);

      #if geography is a structure, get its members
      if (length($aggSubCacheHash{"g-$geography"}) > 0)
      {
        $geoMembers = $aggSubCacheHash{"g-$geography"};
      }
      else
      {
        $geoMembers = cube_expand_item($db, $dsSchema, "g", $geography);
      }

      foreach $time (@timeSegIDs)
      {

        #every 500 segments, update the user
        $count++;
        if (($count % 500) == 0)
        {
          $pct = ($count / $segmentCount) * 100;
          $pct = int($pct);
          cube_set_status($db, $rptID, "Calculating aggregate removals $pct\% ($count of $segmentCount)");
        }

        $q_time = $db->quote($time);

        #if time is a structure, get its members
        if (length($aggSubCacheHash{"t-$time"}) > 0)
        {
          $timeMembers = $aggSubCacheHash{"t-$time"};
        }
        else
        {
          $timeMembers = cube_expand_item($db, $dsSchema, "t", $time);
        }

        #if we're dealing with an exception (like a product segment with no
        #members), skip it and move on
        if ((length($prodMembers) < 1) || (length($geoMembers) < 1) || (length($timeMembers) < 1))
        {
          next;
        }

        #get the aggregated values to be subtracted
        $query = "SELECT $measureQuery FROM $dsSchema.facts \
            WHERE geographyID IN ($geoMembers) AND timeID IN($timeMembers) AND productID IN ($prodMembers)";
        $dbOutput = $db->prepare($query);
        $status = $dbOutput->execute;
        cube_db_err($db, $status, $query);
        @measureVals = $dbOutput->fetchrow_array;

        #build & run the subtraction query
        $idx = 0;
        $measureUpdateStr = "";
        foreach $val (@measureVals)
        {
          if (!defined($val))
          {
            $val = 0;
          }
          $measureUpdateStr .= "$aggSubMeasureNames[$idx]=$aggSubMeasureNames[$idx] - $val, ";
          $idx++;
        }
        chop($measureUpdateStr);  chop($measureUpdateStr);
        $query = "UPDATE $dsSchema.$rptCube SET $measureUpdateStr \
            WHERE product=$q_productID AND geography=$q_geography AND time=$q_time";
        $status = $db->do($query);
        cube_db_err($db, $status, $query);

      } #time
    } #geography
  } #product
}



#-------------------------------------------------------------------------------
#
# Determine if the specified cube needs to be updated (in other words, the
# underlying data source has been updated more recently than the cube has).
# Return 1 if cube needs updating, 0 if it doesn't
#

sub cube_needs_updating
{
  my ($query, $dbOutput, $dsDate, $timeDiff, $status);

  my ($db, $dsID, $rptID) = @_;

  #get the date of the last data source update
  $query = "SELECT lastUpdate FROM app.dataSources WHERE ID=$dsID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  cube_db_err($db, $status, $query);
  ($dsDate) = $dbOutput->fetchrow_array;

  #get the date difference between the cube update and data source update
  $query = "SELECT TIMEDIFF(lastUpdate, '$dsDate') FROM app.cubes WHERE ID=$rptID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  cube_db_err($db, $status, $query);
  ($timeDiff) = $dbOutput->fetchrow_array;

  #if MySQL couldn't calc a difference, assume we need to update
  if (!defined($timeDiff))
  {
    return(1);
  }

  #if data source is newer than cube
  if ($timeDiff < 0)
  {
    return(1);
  }
  else
  {
    return(0);
  }
}



#-------------------------------------------------------------------------------
#
# Perform the post-aggregation calculation of any calculated measures in
# the cube.
#

sub cube_agg_calc_measures
{
  my ($query, $dbOutput, $count, $pct, $totalStructures, $status);
  my ($calcMeasureID, $prodID, $geoID, $timeID);
  my (@calcMeasureIDs);
  my (%measureNameHash);

  my ($db, $dsSchema, $cubeID) = @_;

  cube_telemetry($db, "Updating calculated measures for structures included in cube");

  #get the IDs of all the calculated measures in our cube
  @calcMeasureIDs = DSRmeasures_get_cube_calc_measures_array($db, $dsSchema, $cubeID);

  #get the names of all measures (for status display purposes)
  %measureNameHash = dsr_get_item_name_hash($db, $dsSchema, "m", 0);

  #for each calculated measure in our cube
  foreach $calcMeasureID (@calcMeasureIDs)
  {
    cube_telemetry($db, "Calculating measure $measureNameHash{$calcMeasureID}");

    DSRmeasures_recalculate_cube_measure($db, $dsSchema, $calcMeasureID, $cubeID);
  }

  cube_telemetry($db, "Done updating calculated measures for structures in cube");
}



#-------------------------------------------------------------------------------
#
# Populate a sortable column of time stamps (duration, type, end date) for
# all base time items.
#

sub cube_sort_times
{
  my ($query, $dbOutput, $rptCube, $status);

  my ($db, $dsSchema, $rptID, @timeIDs) = @_;

  #build up name of cube table
  $rptCube = "__rptcube_" . $rptID;

  cube_telemetry($db, "Adding sortable time info to report cube");

  $query = "UPDATE $dsSchema.$rptCube AS `cube`, \
          (SELECT ID, CONCAT(DATE(endDate), '-', type, '-', duration) AS dateStr \
          FROM $dsSchema.timeperiods) AS times \
      SET `cube`.sortTime=times.dateStr WHERE `cube`.time = times.ID";
  $status = $db->do($query);
  cube_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------------
#
# Build the specified data cube, using the user's data selections
#

sub cube_build
{
  my ($query, $dbOutput, $rptCube, $workCube, $itemID, $visID, $visType);
  my ($dsID, $opInfo, $numMeasures, $refreshTime, $dsName, $rptName);
  my ($numProds, $numGeos, $numTimes, $cubeRows, $updateCount, $q_dsName);
  my ($needsRecalc, $chart, $graphDesign, $tableRowDim, $tableColDim, $ok);
  my ($graph_x, $graph_y, $graphType, $script, $status, $dsName, $q_rptName);
  my (@productIDs, @geographyIDs, @timeIDs, @measureIDs);
  my (@validProds, @validGeos, @validTimes, @validMeasures);
  my (%isect, %union);

  my ($db, $dsSchema, $rptID, $userID) = @_;

  #extract our dsID from the schema name
  $dsSchema =~ m/datasource_(\d+)/;
  $dsID = $1;

  if ($dsID < 1)
  {
    print STDERR "ERROR: cube_build passed invalid schema $dsSchema\n";
    return;
  }

  #if this cube is already queued up to be updated, skip it
  $query = "SELECT state FROM app.jobs WHERE cubeID=$rptID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  cube_db_err($db, $status, $query);
  if ($status > 0)
  {
    return;
  }

  #clear out any old (and potentially no longer valid) feed items related to
  #this report
  Social_clear_report_items($db, $rptID);

  KAPutil_job_store_status($db, $userID, $dsID, $rptID, "CUBE-UPDATE", "Refreshing report");

  $dsName = ds_id_to_name($db, $dsID);
  $rptName = cube_id_to_name($db, $rptID);
  $q_dsName = $db->quote($dsName);
  $q_rptName = $db->quote($rptName);
  $query = "UPDATE app.jobs SET dsName=$q_dsName, rptName=$q_rptName \
      WHERE PID=$$ AND cubeID=$rptID";
  $status = $db->do($query);
  cube_db_err($db, $status, $query);

  #if our DS is being used for a blocking operation, set the cube build to wait
  $ok = DSRutil_operation_ok($db, $dsID, $rptID, "CUBE-UPDATE");
  while ($ok != 1)
  {
    $query = "UPDATE app.jobs SET opInfo='Wait' WHERE PID=$$ AND cubeID=$rptID";
    $status = $db->do($query);
    cube_db_err($db, $status, $query);

    cube_set_status($db, $rptID, "Waiting for another job in this data source to finish");

    sleep(60);

    $ok = DSRutil_operation_ok($db, $dsID, $rptID, "CUBE-UPDATE");
  }

  $query = "UPDATE app.jobs SET opInfo='Update' WHERE PID=$$ AND cubeID=$rptID";
  $status = $db->do($query);
  cube_db_err($db, $status, $query);

  #get our update ID that we're going to use for telemetry logging
  $query = "INSERT INTO audit.telemetry_cubes (dsID, cubeID, startTime) \
      VALUES ($dsID, $rptID, NOW())";
  $status = $db->do($query);
  cube_db_err($db, $status, $query);
  $cubeUpdateID = $db->{q{mysql_insertid}};

  #get the "last report refresh" timestamp
  #NB: We do this at the top of the refresh process so any changes made
  #    by users during the refresh will trigger yet another refresh
  #NB: We put the timestamp in place at the end of the refresh process, just
  #    in case something keeps the update process from completing
  $query = "SELECT NOW() FROM dataSources LIMIT 1";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  cube_db_err($db, $status, $query);
  ($refreshTime) = $dbOutput->fetchrow_array;

  #add our startup telemetry
  $query = "UPDATE audit.telemetry_cubes \
      SET telemetry = CONCAT(NOW(), ': Starting report build\n') \
      WHERE ID=$cubeUpdateID";
  $status = $db->do($query);
  cube_db_err($db, $status, $query);

  $query = "INSERT INTO audit.stats_cubes (cubeID, year, month, refreshes) \
      VALUES ($rptID, YEAR(NOW()), MONTH(NOW()), 1) \
      ON DUPLICATE KEY UPDATE refreshes=refreshes+1";
  $status = $db->do($query);
  cube_db_err($db, $status, $query);

  utils_audit($db, $userID, "Refreshed report|$cubeUpdateID", $dsID, $rptID, 0);

  #see if the calculated measures in the backing DS need to be recalculated
  cube_telemetry($db, "Determining if calculated measures need to be refreshed");
  $query = "SELECT needsRecalc FROM dataSources WHERE ID=$dsID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  cube_db_err($db, $status, $query);
  ($needsRecalc) = $dbOutput->fetchrow_array;

  #if we need to do a recalc
  if ($needsRecalc == 1)
  {
    cube_telemetry($db, "Refreshing all calculated measures in data source");

    $query = "UPDATE app.dataSources SET needsRecalc=2 WHERE ID=$dsID";
    $status = $db->do($query);
    cube_db_err($db, $status, $query);

    $query = "UPDATE $dsSchema.measures SET lastCalc=NULL";
    $status = $db->do($query);
    cube_db_err($db, $status, $query);
    DSRmeasures_recalculate_all_measures($db, $dsSchema);

    $query = "UPDATE app.dataSources SET needsRecalc=0 WHERE ID=$dsID";
    $status = $db->do($query);
    cube_db_err($db, $status, $query);
  }

  #elsif a recalc is already being run by another process
  elsif ($needsRecalc > 1)
  {
    cube_set_status($db, $rptID, "Waiting for measure calculation to finish");
    cube_telemetry($db, "Waiting for another process to finish refreshing calculated measures");

    while ($needsRecalc > 1)
    {
      sleep(60);

      $query = "SELECT needsRecalc FROM app.dataSources WHERE ID=$dsID";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      cube_db_err($db, $status, $query);
      ($needsRecalc) = $dbOutput->fetchrow_array;
    }
  }

  $rptCube = "_rptcube_" . $rptID;
  $workCube = "__rptcube_" . $rptID;

  cube_set_status($db, $rptID, "Starting Update");

  #if there's an old working cube, drop it
  cube_telemetry($db, "Cleaning up old working cubes");
  KAPutil_db_delete_table($db, $dsSchema, $workCube);

  #run the scripts to generate the cube's dimension items from dataSel scripts
  $query = "UPDATE app.cubes SET status='Determining cube members' WHERE ID=$rptID";
  $status = $db->do($query);
  cube_db_err($db, $status, $query);
  cube_telemetry($db, "Expanding product selection script");
  datasel_expand_script($db, $dsSchema, $rptID, "p", "c");
  cube_telemetry($db, "Expanding geography selection script");
  datasel_expand_script($db, $dsSchema, $rptID, "g", "c");
  cube_telemetry($db, "Expanding time selection script");
  datasel_expand_script($db, $dsSchema, $rptID, "t", "c");
  cube_telemetry($db, "Expanding measure selection script");
  datasel_expand_script($db, $dsSchema, $rptID, "m", "c");

  #make sure the cube isn't ridiculously large
  @productIDs = datasel_get_dimension_items($db, $rptID, "p");
  @geographyIDs = datasel_get_dimension_items($db, $rptID, "g");
  @timeIDs = datasel_get_dimension_items($db, $rptID, "t");
  @measureIDs = datasel_get_dimension_items($db, $rptID, "m");

  $numProds = scalar @productIDs;
  $numGeos = scalar @geographyIDs;
  $numTimes = scalar @timeIDs;
  $numMeasures = scalar @measureIDs;
  $cubeRows = $numProds * $numGeos * $numTimes;

  if ($cubeRows > 5_000_000)
  {
    $db->do("UPDATE cubes SET lastUpdate=NOW(), status='ERROR:Report is too large' WHERE ID=$rptID");
    cube_telemetry($db, "Report is too large for your version of Koala - it contains $cubeRows rows. Please contact Koala about expanding your cloud capacity.");
    DSRutil_clear_status($db, "CUBE-UPDATE");
    return;
  }

  #make sure we have data for each dimension
  if ($numProds < 1)
  {
    $db->do("UPDATE cubes SET lastUpdate=NOW(), status='ERROR:No products are selected' WHERE ID=$rptID");
    cube_telemetry($db, "This report doesn't contain any products - please add some using the data selector.");
    DSRutil_clear_status($db, "CUBE-UPDATE");
    return;
  }
  if ($numGeos < 1)
  {
    $db->do("UPDATE cubes SET lastUpdate=NOW(), status='ERROR:No geographies are selected' WHERE ID=$rptID");
    cube_telemetry($db, "This report doesn't contain any geographies - please add some using the data selector.");
    DSRutil_clear_status($db, "CUBE-UPDATE");
    return;
  }
  if ($numTimes < 1)
  {
    $db->do("UPDATE cubes SET lastUpdate=NOW(), status='ERROR:No time periods are selected' WHERE ID=$rptID");
    cube_telemetry($db, "This report doesn't contain any time periods - please add some using the data selector.");
    DSRutil_clear_status($db, "CUBE-UPDATE");
    return;
  }
  if ($numMeasures < 1)
  {
    $db->do("UPDATE cubes SET lastUpdate=NOW(), status='ERROR:No measures are selected' WHERE ID=$rptID");
    cube_telemetry($db, "This report doesn't contain any measures - please add some using the data selector.");
    DSRutil_clear_status($db, "CUBE-UPDATE");
    return;
  }

  #create the table that'll hold the cube
  cube_set_status($db, $rptID, "Creating cube schema");
  cube_create_table($db, $dsSchema, $rptID);

  #turn off the transaction isolation guarantee for the same reasons as above
  $query = "SET SESSION transaction_isolation='READ-UNCOMMITTED'";
  $status = $db->do($query);
  cube_db_err($db, $status, $query);

  #populate the report cube's fact data
  cube_set_status($db, $rptID, "Populating base items");
  cube_base_items($db, $dsSchema, $rptID);

  #populate the report cube's segments, if any were selected
  cube_set_status($db, $rptID, "Calculating roll-ups");
  cube_rollups($db, $dsSchema, $rptID);
  cube_aggregate_subtractions($db, $dsSchema, $rptID);

  #do the calculations for any calculated measures that involve aggregates or
  #segments
  cube_set_status($db, $rptID, "Calculating measure values");
  cube_agg_calc_measures($db, $dsSchema, $rptID);

  #populate the names of segmentations and attributes
  cube_seg_attr_strings($db, $dsSchema, $rptID);

  #populate the sort time column (used to sort by time dimension data)
  cube_set_status($db, $rptID, "Populating time sorting information");
  cube_sort_times($db, $dsSchema, $rptID, @timeIDs);

  #if a previous version of the cube exists, drop it
  KAPutil_db_delete_table($db, $dsSchema, $rptCube);

  #turn the working cube into the production cube
  cube_telemetry($db, "Putting new report data cube in place");
  $query = "RENAME TABLE $dsSchema.$workCube TO $dsSchema.$rptCube";
  $status = $db->do($query);
  cube_db_err($db, $status, $query);

  #update the cube's lastUpdate timestamp
  $query = "UPDATE cubes SET lastUpdate='$refreshTime' WHERE ID=$rptID";
  $status = $db->do($query);
  cube_db_err($db, $status, $query);

  #trim out any selections that are now bad because the underlying cube
  #definition changed, and make sure at least one item in each dimension of the
  #cube is selected
  cube_telemetry($db, "Adjusting report display selections");

  #grab a list of every visualization in the report
  $query = "SELECT ID, type FROM visuals WHERE cubeID=$rptID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  cube_db_err($db, $status, $query);
  while (($visID, $visType) = $dbOutput->fetchrow_array)
  {
    if ($visType eq "map")
    {
      reports_map_default_selections($db, $rptID, $visID);
    }
    elsif ($visType eq "table")
    {
      reports_table_default_selections($db, $rptID, $visID);
    }
    elsif ($visType eq "chart")
    {
      reports_graph_default_selections($db, $rptID, $visID, 1);
    }
  }

  $query = "UPDATE app.cubes SET status='DONE' WHERE ID=$rptID";
  $status = $db->do($query);
  cube_db_err($db, $status, $query);

  DSRutil_clear_status($db, "CUBE-UPDATE");

  $query = "UPDATE audit.telemetry_cubes \
      SET endTime=NOW(), telemetry = CONCAT(telemetry, NOW(), ': Finished report build\n') \
      WHERE ID=$cubeUpdateID";
  $status = $db->do($query);
  cube_db_err($db, $status, $query);

  #if the refresh was initiated by a user, let them know it's finished
  if ($userID > 0)
  {
    Social_feed_add_item($db, $userID, $dsID, $rptID, 0, "success", "rpt_update");
  }
}


#-------------------------------------------------------------------------------

1;
