#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::DataSources;
use Lib::AInsights::AInsights;
use Lib::AInsights::Utils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#format currency for pretty HTML output
sub html_format_currency
{
  ($value) = @_;

  #go down to 2 decimal places
  $value = sprintf("%.2f", $value);

  if ($value < 0)
  {
    $value = abs($value);
    $formatStr = "<SPAN CLASS='text-danger'>(\$$value)</SPAN>";
  }
  else
  {
    $formatStr = "\$$value";
  }

  return($formatStr);
}



#-------------------------------------------------------------------------

#format number for pretty HTML output
sub html_format_number
{
  ($value, $decimals, $redNegative) = @_;

  if (!defined($value))
  {
    return;
  }

  #go down to 2 decimal places
  $formatStr = "%." . $decimals . "f";
  $value = sprintf($formatStr, $value);

  if (($redNegative == 1) && ($value < 0))
  {
    $value = abs($value);
    $formatStr = "<SPAN CLASS='text-danger'>($value)</SPAN>";
  }
  else
  {
    $formatStr = "$value";
  }

  return($formatStr);
}



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  #output Content-type header
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  #output HTML
  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Elasticity Calculation Details</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/fontawesome-5.10.2/css/all.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;

<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">AInsights</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="elasticity.cld?pm=$priceModelID">$modelName</A></LI>
    <LI CLASS="breadcrumb-item active">Elasticity Model Details</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');
  $priceModelID = $q->param('pm');
  $ppgID = $q->param('p');
  $geoID = $q->param('g');

  AInsights_Utils_initialize_constants($priceModelID);

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsSchema = "datasource_" . $dsID;

  $modelName = AInsights_ID_to_name($db, $priceModelID);
  $dsName = ds_id_to_name($db, $dsID);

  %prodNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "p");
  %geoNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "g");
  %timeNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "t");

  print_html_header();

  #make sure we have at least read privs for this model
  $privs = AInsights_rights($db, $userID, $priceModelID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to view this model.");
  }

  #grab summary elasticity info for this prod/geo combination
  $query = "SELECT name, elasticity \
      FROM $dsSchema.$AinsightsPPGTable \
      WHERE ppgID='$ppgID' AND geographyID=$geoID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($ppgName, $avgElasticity) = $dbOutput->fetchrow_array;


  ####################### AI Conclusions #############################


  $avgElasticity = html_format_number($avgElasticity, 2);
  $AI_elasticityStatement = "The average elasticity for this promo group in this market is <STRONG>$avgElasticity</STRONG>.";

  #determine a human-readable level of elasticity
  if (abs($avgElasticity) <= 0.75)
  {
    $AI_elasticityLevel = "It appears the products in this promo group are inelastic in this market.";
  }
  elsif (abs($avgElasticity) <= 1.25)
  {
    $AI_elasticityLevel = "It appears the products in this promo group are moderately inelastic in this market.";
  }
  elsif (abs($avgElasticity) <= 1.75)
  {
    $AI_elasticityLevel = "It appears the products in this promo group are moderately elastic in this market.";
  }
  elsif (abs($avgElasticity) <= 2.25)
  {
    $AI_elasticityLevel = "It appears the products in this promo group are elastic in this market.";
  }
  else
  {
    $AI_elasticityLevel = "It appears the products in this promo group are very elastic in this market.";
  }

  $AI_elasticityStatement = <<END_HTML;
  <P>
    $AI_elasticityStatement
    $AI_elasticityLevel
  </P>
END_HTML


  ###################### END AI Conclusions ##########################


  $prodName = $ppgName;

  print <<END_HTML;
<DIV CLASS="container-fluid">

  <DIV CLASS="row">
    <DIV CLASS="col">

      $AI_confidenceWarning

      <H3>Product Promo Group: $prodName</H3>
      <H3>Geography: $geoNameHash{$geoID}</H3>

      <P>&nbsp;</P>
      <H4>Conclusions</H4>

      $AI_elasticityStatement

    </DIV>
  </DIV>

  <DIV CLASS="row">
    <DIV CLASS="col">

      <P>&nbsp;</P>
      <DIV CLASS="card">
        <DIV CLASS="card-body">

          <H4>Products in Promo Group</H4>

          <TABLE CLASS="table table-bordered table-striped table-sm">
END_HTML

  $query = "SELECT memberIDs FROM $dsSchema.$AinsightsPPGTable \
      WHERE ppgID='$ppgID'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($ppgMemberIDstr) = $dbOutput->fetchrow_array;
  @ppgMemberIDs = split(',', $ppgMemberIDstr);
  foreach $productID (@ppgMemberIDs)
  {
    $ppgProducts{$productID} = $prodNameHash{$productID};
  }

  foreach $productID (keys %ppgProducts)
  {

    #get the individual product's calculated elasticity
    $query = "SELECT elasticity FROM $dsSchema.$AInsightsItemTable \
        WHERE productID=$productID AND geographyID=$geoID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($elasticity) = $dbOutput->fetchrow_array;

    #handle an NA elasticity value
    if (!defined($elasticity))
    {
      print <<END_HTML;
            <TR>
              <TD>$ppgProducts{$productID}</TD>
              <TD CLASS="text-right">NA</TD>
            </TR>
END_HTML
      next;
    }

    $dispElasticity = html_format_number($elasticity, 2);
    $colorCode = AInsights_Utils_get_elasticity_html_bgcolor($elasticity);
    $linkColorClass = "";
    if (length($colorCode) > 0)
    {
      $linkColorClass = "text-white";
    }

    print <<END_HTML;
            <TR>
              <TD>$ppgProducts{$productID}</TD>
              <TD STYLE='background-color:$colorCode;' CLASS="text-right"><A CLASS='$linkColorClass text-decoration-none' HREF='elasticityItem.cld?ds=$dsID&pm=$priceModelID&p=$productID&g=$geoID'>$dispElasticity</TD>
            </TR>

END_HTML
  }

  print <<END_HTML;
          </TABLE>
        </DIV>
      </DIV>

    </DIV>
  </DIV>
END_HTML


  print <<END_HTML;
    </DIV>
  </DIV>

</DIV>
END_HTML

  print_html_footer();

  AInsights_audit($db, $userID, $priceModelID, "Viewed details for $prodNameHash{$ppgID} in $geoNameHash{$geoID}");
  $activity = "PRICING: $first $last viewed model details for $prodNameHash{$ppgID} / $geoNameHash{$geoID} in pricing model $modelName in $dsName";
  utils_slack($activity);

#EOF
