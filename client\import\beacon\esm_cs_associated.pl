#!/usr/bin/perl

use Text::CSV;

#Import C&S Key Foods Spin data for ESM-Ferolie

  open(INPUT, "$ARGV[0]");
  open(OUTPUT, ">$ARGV[1]");


  #instantiate our CSV object
  $csv = Text::CSV->new( {binary => 1} );

  #NB: the first thing we want to do is find all of the date columns and
  #    unwind them into rows

  #parse the header line
  $line = <INPUT>;
  $csv->parse($line);
  @columns = $csv->fields();

  $idx = 0;
  $dateCount = 0;
  $dateStart = 0;
  foreach $header (@columns)
  {
    if ($header =~ m/^(\d+)\-(\d+)\-(\d+)/)
    {
      $month = $1;
      if ($month =~ m/^0(\d)/)
      {
        $month = $1;
      }

      #build up a hash of related date columns, indexed by month
      $dateCols{$month} = $dateCols{$1} . "$idx,";

      #since the dates are ordered left to right, the right-most one is the
      #one we want - this will just auto-overwrite until we get that
      $dateStrings{$month} = "$header";

      #keep track of how many date columns we're working with
      $dateCount++;

      #keep track of the first column a date appears in
      if ($dateStart == 0)
      {
        $dateStart = $idx;
      }
    }

    elsif ($header eq "customer")
    {
      $columns[$idx] = "pattr:customer";
    }
    elsif ($header eq "fac")
    {
      $columns[$idx] = "pattr:fac";
    }
    elsif ($header eq "itemno")
    {
      $columns[$idx] = "pattr:itemno";
      $itemNumCol = $idx;
    }
    elsif ($header eq "itemupc")
    {
      $columns[$idx] = "UPC";
      $upcCol = $idx;
    }
    elsif ($header eq "vendno")
    {
      $columns[$idx] = "pseg:vendno";
    }
    elsif ($header eq "vendor")
    {
      $columns[$idx] = "pseg:vendor";
    }
    elsif ($header eq "itemdesc")
    {
      $columns[$idx] = "Product";
      $prodCol = $idx;
    }
    elsif ($header eq "linkcode")
    {
      $columns[$idx] = "pattr:linkcode";
    }
    elsif ($header eq "total")
    {
      $columns[$idx] = "garbage";
    }


    $idx++;
  }


  #in case we've got a spurious couple days from a leading/trailing month,
  #fold it in to an existing time index
  foreach $month (keys %dateCols)
  {
    @weeks = split(',', $dateCols{$month});

    #if there's only one week in the month
    if (scalar(@weeks) == 1)
    {

      #if the month isn't the last month
      if (length($dateCols{$month+1}) > 0)
      {

        #add it to the first month
        $dateCols{$month+1} = $dateCols{$month+1} . $dateCols{$month};
        delete($dateCols{$month});
      }

      #else if the month isn't the first month
      elsif (length($dateCols{$month-1}) > 0)
      {

        #add it to the last month
        $dateCols{$month-1} = $dateCols{$month-1} . $dateCols{$month};
        delete($dateCols{$month});
      }


    }

  }


  #cut the date column headers out
  splice(@columns, $dateStart, $dateCount);

  #push the new shipped measure and date columns on the front of the headers
  @tmp = ('Time Period', 'Geography', 'total cases');
  push(@tmp, @columns);
  @columns = @tmp;

  #output headers
  $csv->combine(@columns);
  $line = $csv->string();
  print OUTPUT "$line\n";


  while ($line = <INPUT>)
  {
    $csv->parse($line);
    @columns = $csv->fields();

    if (length($columns[$prodCol]) < 2)
    {
      next;
    }

    #add the ITEM# to the end of the product name
    $columns[$prodCol] = "$columns[$prodCol] $columns[$itemNumCol]";

    #format the UPC (assumes 10 digit UPC)
    $columns[$upcCol] = sprintf("%010d", $columns[$upcCol]);
    $columns[$upcCol] =~ m/(\d\d\d\d\d)(\d\d\d\d\d)/;
    $upc = "0-$1-$2-$2";
    $columns[$upcCol] = $upc;

    #run through each month grouping contained in the line of data
    foreach $dateIdx (keys %dateCols)
    {

      #initialize the line of data to match the source file fields
      @dataColumns = @columns;

      #aggregate each month's data contained in the date columns
      @cols = split(',', $dateCols{$dateIdx});
      $measureVal = 0;
      foreach $val (@cols)
      {
        $measureVal = $measureVal + $columns[$val];
      }

      #strip the date column data out of the data columns
      splice(@dataColumns, $dateStart, $dateCount);

      #push the newly calculated measure and dims on front of the line of data
      @tmp = ("1 Month Ending $dateStrings{$dateIdx}", "Associated", $measureVal);
      push(@tmp, @dataColumns);
      @dataColumns = @tmp;

      #output the line to the temp file
      $csv->combine(@dataColumns);
      $line = $csv->string();
      print OUTPUT "$line\n";

    }
  }

  close(OUTPUT);
  close(INPUT);
