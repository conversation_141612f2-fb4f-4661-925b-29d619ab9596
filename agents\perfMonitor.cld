#!/usr/bin/perl

use lib "/opt/apache/app/";

use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;


my $debug;

#-------------------------------------------------------------------------
#
# Output debug data, if enabled

sub DBG
{
  my ($str) = @_;

  if ($debug eq 1)
  {
    print STDERR "$str\n";
  }
}



#-------------------------------------------------------------------------

  #determine if we're being run manually
  $debug = 0;
  if ($ARGV[0] eq "debug")
  {
    $debug = 1;
  }

  #redirect STDERR to the Koala error log
  if ($debug == 0)
  {
    close(STDOUT);
    close(STDERR);
    open(STDERR, ">>/opt/apache/htdocs/tmp/koala_error.log");
  }

  #connect to the databases
  $db = KAPutil_connect_to_database();

  #every minute, gather performance stats and update the KAP
  #performance database
  $output = `iostat -xy 5 1`;

  #convert iostat output into an array of lines
  @lines = split('\n', $output);
  $idx = 0;

  #burn system info line and blank line
  $idx = 1;
  while (length($lines[$idx]) < 5)
  {
    $idx++;
  }
  $idx++;

  #last value on CPU state line is total idle, we can sub from 100 to get active
  $activeCPUPct = 0;
  if ($lines[$idx] =~ m/.*\s([0-9\.]+)$/)
  {
    $activeCPUPct = $1;
    $activeCPUPct = 100 - $activeCPUPct;
    $activeCPUPct = sprintf('%.0f', $activeCPUPct);
  }

  #burn blank and header lines
  $idx += 3;

  #run through all remaining lines, taking max last value (% util)
  $storageLoad = 0;
  while ($idx <= @lines)
  {
    if ($lines[$idx] =~ m/.*\s([0-9\.]+)$/)
    {
      $tmp = $1;
      $tmp = sprintf('%.0f', $tmp);
      if ($tmp > $storageLoad)
      {
        $storageLoad = $tmp;
      }
    }
    $idx++;
  }

  #get a snapshot of memory usage
  $output = `free`;

  #convert memory usage output into an array of lines
  @lines = split('\n', $output);
  $idx = 0;

  #burn header line
  $idx++;

  #extract memory usage level
  if ($lines[$idx] =~ m/^Mem:\s+([0-9\.]+)\s+([0-9\.]+)\s+/)
  {
    $totalMem = $1;
    $usedMem = $2;

    $usedMemPct = ($usedMem / $totalMem) * 100;
    $usedMemPct = sprintf('%.0f', $usedMemPct);
  }

  #get number of active jobs
  $query = "SELECT COUNT(*) FROM app.jobs";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($activeJobs) = $dbOutput->fetchrow_array;

  #get storage usage
  #NB: we're goint to use the max value of all storage volumes
  #determine if we're using an external table space for capacity/IO reasons
  $query = "SELECT MAX(dataQuotaUsage) FROM app.orgs";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ($usagePct) = $dbOutput->fetchrow_array;

  #see if the main storage volume utilization is higher
  $query = "SELECT value FROM app.config WHERE name='analytics_storage_usage'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ($mainUsagePct) = $dbOutput->fetchrow_array;
  if ($mainUsagePct > $usagePct)
  {
    $usagePct = $mainUsagePct;
  }

  #update values in performance database
  $query = "INSERT INTO app.performance (instance, jobs, cpu, storage, storageThroughput, memory) \
      VALUES ('kap', $activeJobs, $activeCPUPct, $usagePct, $storageLoad, $usedMemPct) \
      ON DUPLICATE KEY UPDATE jobs=$activeJobs, cpu=$activeCPUPct, storage=$usagePct, storageThroughput=$storageLoad, memory=$usedMemPct";
  $db->do($query);

#EOF
