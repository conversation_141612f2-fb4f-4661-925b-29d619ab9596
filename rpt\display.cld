#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::BuildCube;
use Lib::Cubes;
use Lib::DataSel;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::Reports;
use Lib::WebUtils;
use Lib::DSRUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if ((length($email) < 1) && ($local != 1))
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------
#
# Output the HTML for the data selector checkbox tree (we don't display it
# if we're doing a "clean" display for expanded reports, PPT export, etc).
#
# Additional CSS located at /opt/apache/htdocs/css/display.css
# Additional JavaScript located at /opt/apache/htdocs/js/display.js

sub print_data_selector
{
  if ($clean == 1)
  {
    print <<END_HTML;
 <TD>
 </TD>
END_HTML
  }
  else
  {
    print <<END_HTML;
  <TD ID="dimensions_panel" CLASS="dimensions_panel_collapsed" STYLE="background-color:#f8f8f8;">
<!--split_here_dimensions_top2-->
    <DIV ID="dimensions_panel_inner">
      <DIV ID="dimensions_panel_tab">
        <DIV ID="dimensions_panel_tab_inner" CLASS="dimensions_panel_tab_inner_collapsed">
          <DIV ID="dimensions_panel_tab_icon" STYLE="font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif;">&#9664;</DIV>
          </DIV>
        </DIV>
      </DIV>
<!--split_here_dimensions_top-->
      <div id="refresh_dimensions">
        <STYLE>
          .navbar-nav > li > a {padding-top:5px !important; padding-bottom:5px !important;}
          .navbar {min-height:32px !important; margin-bottom:0px !important;}
          .fancytree-container {outline:none !important; background-color:#f8f8f8 !important;}
        </STYLE>
        <DIV ID="datasel-header-div">
          <STRONG>Data Selector</STRONG>
        </DIV>
        <DIV ID="dataSelDiv" STYLE='width:250px; height:550px !important; overflow:auto;'>
          <DIV ID='ProdCheckboxTree'></DIV>
          <DIV ID='GeoCheckboxTree'></DIV>
          <DIV ID='TimeCheckboxTree'></DIV>
          <DIV ID='MeasureCheckboxTree'></DIV>

          <P>&nbsp;</P>
END_HTML

  #output slicer drop-downs if there are any for this report
  if (length($slicersStr) > 1)
  {
    %segNameHash = DSRsegmentation_get_segmentations_hash($db, $dsSchema, "p");
    @slicers = split(',', $slicersStr);
    foreach $slicer (@slicers)
    {
      $slicer =~ m/PSEG_(\d+):(\d+)/;
      $segID = $1;
      $segmentID = $2;

      #build a hash of segments that are present in this report to display as
      #slicer options
      #NB: we're doing this to satisfy some brokers who don't want one set of
      #   Viewers to know all of the brands that the broker reps via a Brand
      #   segmentation in a warehouse report from AFS. I don't feel like this
      #   is efficient - revisit if performance becomes a problem.
      undef(%availableSegmentNames);
      $query = "SELECT DISTINCT PSEG_$segID FROM $dsSchema.$rptCube
          ORDER BY PSEG_$segID";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      KAPutil_handle_db_err($db, $status, $query);
      while (($segmentName) = $dbOutput->fetchrow_array)
      {
        $availableSegmentNames{$segmentName} = 1;
      }

      #output the slicer header
      print <<END_HTML;
          $segNameHash{$segID}:<BR>
          <SELECT CLASS="form-control" ID="slicer-$segID" onchange="changeSlicer($segID);">
            <OPTION VALUE=0>(All)</OPTION>
END_HTML

      %segmentNameHash = DSRseg_get_segments_hash($db, $dsSchema, "p", $segID);
      foreach $id (sort {$segmentNameHash{$a} cmp $segmentNameHash{$b}} keys %segmentNameHash)
      {

        #skip segment names that aren't present in the report
        if ($availableSegmentNames{$segmentNameHash{$id}} < 1)
        {
          next;
        }

        $checked = "";
        if ($id == $segmentID)
        {
          $checked = "SELECTED";
        }

        print(" <OPTION VALUE=$id $checked>$segmentNameHash{$id}</OPTION>\n");
      }

      print <<END_HTML;
          </SELECT>

          <P>&nbsp;</P>
END_HTML
      }
    }

    print <<END_HTML;
</DIV>
<SCRIPT>
  \$("#dataSelDiv").height(viewportHeight+10);
</SCRIPT>
</div>
<!--split_here_dimensions_bottom-->
  </TD>
END_HTML
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #get the CGI input variables
  $rptID = $q->param('rpt');
  $action = $q->param('a');
  $insertType = $q->param('type');
  $visID = $q->param('v');
  $slide = $q->param('s');
  if ($slide != 1)
  {
    $slide = 0;
  }

  if (length($action) < 1)
  {
    $selectedVisID = $visID;
  }

  $clean = $q->param('c');
  if ($clean != 1)
  {
    $clean = 0;
  }

  $local = $q->param('l');
  if ($local != 1)
  {
    $local = 0;
  }

  if ($userID < 1)
  {
    $userID = $q->param('u');
  }

  if ($rptID =~ m/^(\d+)\,.*/)
  {
    $rptID = $1;
  }

  #get "fixed" dimension items (used for expanded reports)
  $fProd = $q->param('p');
  $fGeo = $q->param('g');
  $fTime = $q->param('t');

  $fDim = "";
  if (length($fProd) > 0)
  {
    $fDim = "p=$fProd";
  }
  elsif (length($fGeo) > 0)
  {
    $fDim = "g=$fGeo";
  }
  elsif (length($fTime) > 0)
  {
    $fDim = "t=$fTime";
  }

  #get our target resolution if we're being given one for PPT export purposes
  $resolutionX = $q->param('rx');
  $resolutionY = $q->param('ry');
  if (length($resolutionX) < 1)
  {
    $resolutionX = 0;
  }
  if (length($resolutionY) < 1)
  {
    $resolutionY = 0;
  }

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #output Content-type header
  print($session->header());

  $db = KAPutil_connect_to_database();

  #see what sort of rights we have to the cube underlying this report
  #NB: This'll only get called if somebody is being cute and tries mucking
  #    with the CGI parameters
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($local == 0)
  {
    if ($privs eq "N")
    {
      exit_error("You don't have privileges on this report.");
    }
  }

  #update the view time
  $query = "UPDATE cubes SET lastViewed=NOW() WHERE ID=$rptID";
  $db->do($query);

  #get info about the report from the main database
  $query = "SELECT name, dsID, slicers, lockVisuals, background FROM cubes
      WHERE ID=$rptID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($rptName, $dsID, $slicersStr, $lockVisuals, $rptBackgroundID) = $dbOutput->fetchrow_array;

  if ($dsID < 1)
  {
    exit_error("The requested report doesn't exist.");
  }

  #if we're running "clean" mode, lockVisuals is implied to be 1
  if ($clean > 0)
  {
    $lockVisuals = 1;
  }

  #if we're on a mobile device, lockVisual is 1 so user can drag around screen
  $mobileMode = 0;
  if (($ENV{'HTTP_USER_AGENT'} =~ m/Mobile Safari/) ||
      ($ENV{'HTTP_USER_AGENT'} =~ m/Android/))
  {
    $lockVisuals = 1;
    $mobileMode = 1;
  }

  #if we're being asked to insert a new table
  if ($action eq "it")
  {
    $query = "INSERT INTO app.visuals (dsID, cubeID, type)
        VALUES ($dsID, $rptID, 'table')";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);
    $visID = $db->{q{mysql_insertid}};

    #do an initial format on the table
    reports_table_default_selections($db, $rptID, $visID);
  }

  #if we're being asked to insert a new chart
  elsif ($action eq "ic")
  {
    if (length($insertType) < 3)
    {
      $insertType = "2DPie";
    }
    $query = "INSERT INTO app.visuals (dsID, cubeID, type, design)
        VALUES ($dsID, $rptID, 'chart', ',type:$insertType,')";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);
    $visID = $db->{q{mysql_insertid}};

    #do an initial format on the graph
    reports_graph_default_selections($db, $rptID, $visID);
  }

  #if we're being asked to insert a new map
  elsif ($action eq "im")
  {
    $query = "INSERT INTO app.visuals (dsID, cubeID, type, design)
        VALUES ($dsID, $rptID, 'map', ',type:usa,width:0.48,height:0.48,xpct:0,ypct:0,')";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);
    $visID = $db->{q{mysql_insertid}};

    #do an initial format on the map
    reports_map_default_selections($db, $rptID, $visID);
  }

  #if we're being asked to insert a rectangle shape
  elsif ($action eq "irect")
  {
    $query = "INSERT INTO app.visuals (dsID, cubeID, type, design)
        VALUES ($dsID, $rptID, 'rectangle', ',width:0.48,height:0.48,xpct:0,ypct:0,title:Title,fontColor:#000000,titleBgColor:#ffffff,')";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);
  }

  #if we're being asked to insert a text box
  elsif ($action eq "text")
  {
    $query = "INSERT INTO app.visuals (dsID, cubeID, type, design)
        VALUES ($dsID, $rptID, 'text', ',width:0.48,height:0.48,xpct:0,ypct:0,content:|||<I>Enter text</I>|||,')";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);
  }

  #if we're being asked to insert a vertical line
  elsif ($action eq "ilnv")
  {
    $query = "INSERT INTO app.visuals (dsID, cubeID, type, design)
        VALUES ($dsID, $rptID, 'line_vertical', ',width:0.48,height:0.48,xpct:0,ypct:0,')";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);
  }

  #if we're being asked to insert a horizontal line
  elsif ($action eq "ilnh")
  {
    $query = "INSERT INTO app.visuals (dsID, cubeID, type, design)
        VALUES ($dsID, $rptID, 'line_horizontal', ',width:0.48,height:0.48,xpct:0,ypct:0,')";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);
  }

  #if we're being asked to remove a visualization
  elsif ($action eq "d")
  {
    $query = "DELETE FROM visuals WHERE ID=$visID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);
  }

  #assemble datasource and cube names
  $dsSchema = "datasource_" . $dsID;
  $rptCube = "_rptcube_" . $rptID;

  #if the report is already being updated, redirect to cube updating status
  $query = "SELECT opInfo FROM app.jobs \
      WHERE cubeID=$rptID AND operation='CUBE-UPDATE'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($opInfo) = $dbOutput->fetchrow_array;
  if (($opInfo =~ m/^Update/) || ($opInfo =~ m/^Wait/))
  {
    print("<HTML><HEAD><META HTTP-EQUIV='refresh' CONTENT='0; URL=cubeUpdate.cld?ds=$dsID&rptID=$rptID'></HEAD></HTML>\n");
    exit;
  }

  #if the report is being created by auto reports, redirect to autorpt status
  $query = "SELECT operation FROM app.jobs WHERE cubeID=$rptID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($operation) = $dbOutput->fetchrow_array;
  if ($operation eq "AUTO-RPTS")
  {
    print("<HTML><HEAD><META HTTP-EQUIV='refresh' CONTENT='0; URL=autoRptBuild.cld?ds=$dsID'></HEAD></HTML>\n");
    exit;
  }

  #start by grabbing the global multi-selection states for the entire report
  @multiSelections = reports_multiselect($db, $rptID, 0);
  $multiProduct = $multiSelections[0];
  $multiGeography = $multiSelections[1];
  $multiTime = $multiSelections[2];
  $multiMeasure = $multiSelections[3];

  #get info about all of the visualizations to be displayed in this report
  $query = "SELECT ID, type, design, tableRowDims, tableFilterDims, tableColDims, graph_x, graph_y, graph_z
      FROM app.visuals WHERE cubeID=$rptID ORDER BY ID";
  $dbOutput = $db->prepare($query);
  $visCount = $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  #build arrays of the visualization display info, indexed by vis ID
  $idx = 0;
  while (($visID, $visType, $visDesign, $tableRowDims, $tableFilterDims, $tableColDims, $graph_x, $graph_y, $graph_z) = $dbOutput->fetchrow_array)
  {

    #store name data (used to output JS array for data selector panel header)
    if ($visDesign =~ m/,caption:\"(.*?)\",/)
    {
      $jsVisNameHash{$visID} = $1;
    }
    elsif ($visType eq "table")
    {
      $jsVisNameHash{$visID} = "Table";
    }
    elsif ($visType eq "map")
    {
      $jsVisNameHash{$visID} = "Map";
    }
    elsif ($visType eq "chart")
    {
      $jsVisNameHash{$visID} = "Graph";
    }

    #store the type of the visual that's selected by default
    if ($visID eq $selectedVisID)
    {
      $selectedVisType = $visType;
    }

    $visIDArray[$idx] = $visID;
    $visTypeArray[$idx] = $visType;
    $visDesignArray[$idx] = $visDesign;

    #grab the multi-selection rules for this visualization
    @multiSelections = reports_multiselect($db, $rptID, $visID);
    $jsmultiProdHash{$visID} = $multiSelections[0];
    $jsmultiGeoHash{$visID} = $multiSelections[1];
    $jsmultiTimeHash{$visID} = $multiSelections[2];
    $jsmultiMeasHash{$visID} = $multiSelections[3];

    $idx++;
  }

  #if we were passed a visual ID to keep selected, set the remove DIV to display
  $selectedRemoveVisID = "#ignore";
  if ($selectedVisID > 0)
  {
    $selectedRemoveVisID = "#remove-$selectedVisID";
  }

  #determine which visual is selected by default if one wasn't specified
  if ($selectedVisID < 1)
  {

    #if there's more than 1 visual in the report, don't select anything
    if ($visCount > 1)
    {
      $selectedTable = 0;
      $selectedChart = 0;
      $selectedRect = 0;
      $selectedText = 0;
      $styleButtonShow = "";
      $selectedVisType = "";
    }

    #if there's only 1 visual in the report, select it by default
    else
    {
    #  $selectedVisID = $visIDArray[0];
    #  $selectedVisType = $visTypeArray[0];
    }
  }

  $globalSelVisual = 0;
  if ($selectedVisType eq "table")
  {
    $selectedTable = $selectedVisID;
    $globalSelVisual = $selectedVisID;
    $selectedChart = 0;
    $selectedRect = 0;
    $selectedText = 0;
    $styleButtonShow = "table-design-drop";
  }
  elsif ($selectedVisType eq "chart")
  {
    $selectedChart = $selectedVisID;
    $globalSelVisual = $selectedVisID;
    $selectedTable = 0;
    $selectedRect = 0;
    $selectedText = 0;
    $styleButtonShow = "chart-design-drop";
  }
  elsif ($selectedVisType eq "map")
  {
    $selectedChart = $selectedVisID;
    $globalSelVisual = $selectedVisID;
    $selectedTable = 0;
    $selectedRect = 0;
    $selectedText = 0;
    $styleButtonShow = "map-design-drop";
  }
  elsif ($selectedVisType eq "rectangle")
  {
    $selectedChart = 0;
    $selectedTable = 0;
    $selectedRect = $selectedVisID;
    $selectedText = 0;
    $styleButtonShow = "shape-design-drop";
  }
  elsif ($selectedVisType eq "text")
  {
    $selectedChart = 0;
    $selectedTable = 0;
    $selectedRect = 0;
    $selectedText = $selectedVisID;
    $styleButtonShow = "text-design-drop";
  }
  else
  {
    $selectedChart = 0;
    $selectedTable = 0;
    $selectedRect = 0;
    $selectedText = 0;
    $styleButtonShow = "";
  }

  #if we're a viewer, we need to (potentially) set up our default selections
  if ($acctType == 0)
  {
    $query = "SELECT ID, selProducts, selGeographies, selTimeperiods, selMeasures
        FROM app.visuals WHERE cubeID=$rptID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);

    while (($id, $selProducts, $selGeographies, $selTimeperiods, $selMeasures) = $dbOutput->fetchrow_array)
    {
      $query = "INSERT IGNORE INTO app.visuals_viewers
          (ID, userID, cubeID, dsID, selProducts, selGeographies, selTimeperiods, selMeasures)
          VALUES ($id, $userID, $rptID, $dsID, '$selProducts', '$selGeographies', '$selTimeperiods', '$selMeasures')";
      $status = $db->do($query);
      KAPutil_handle_db_err($db, $status, $query);
    }
  }

  $appName = WebUtils_get_app_name($db);

  #print page header
  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName Report</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/jquery-ui/jquery-ui.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<SCRIPT SRC="/jquery-bootstrap-scrolling-tabs-2.6.1/jquery.scrolling-tabs.min.js"></SCRIPT>
<SCRIPT SRC="/fancytree-2.31.0/dist/jquery.fancytree-all-deps.min.js"></SCRIPT>
<SCRIPT SRC="/fancytree-2.31.0/dist/modules/jquery.fancytree.multi.js"></SCRIPT>
<SCRIPT SRC="/summernote-0.8.19/summernote-lite.min.js"></SCRIPT>

<SCRIPT SRC="/fusioncharts-3.15.2/js/fusioncharts.js"></SCRIPT>
<SCRIPT SRC="/fusioncharts-3.15.2/js/themes/fusioncharts.theme.zune.js"></SCRIPT>

<LINK HREF="/css/glyphicons.css" REL="stylesheet">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK REL="stylesheet" HREF="/jquery-ui/jquery-ui.css">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/fontawesome-5.10.2/css/all.min.css" REL="stylesheet">
<LINK HREF="/jquery-bootstrap-scrolling-tabs-2.6.1/jquery.scrolling-tabs.min.css" REL="stylesheet">
<LINK HREF="/fancytree-2.31.0/dist/skin-win8/ui.fancytree.min.css" rel="stylesheet">
<LINK HREF="/summernote-0.8.19/summernote-lite.css" REL="stylesheet">


<LINK REL="stylesheet" HREF="/css/display.css?10" type="text/css">
<SCRIPT SRC="/js/display.js?4"></SCRIPT>
<LINK HREF="/css/material-icons.css" REL="stylesheet">


<!--split_here_script_top-->
<SCRIPT ID="main_javascript">
viewportHeight = window.innerHeight;
viewportWidth = window.innerWidth;

if ($resolutionX > 10)
{
  viewportWidth = $resolutionX;
}
if ($resolutionY > 10)
{
  viewportHeight = $resolutionY;
}

if ($clean != 1)
{
  viewportHeight = viewportHeight - 120;
  viewportWidth = viewportWidth - 65;

  xAdjustment = 53;
  yAdjustment = 80;
}
else
{
  xAdjustment = 0;
  yAdjustment = 0;
}



function koalaStoreSize(element, widthPct, heightPct)
{
  let url = "xhrRptSizePos?rptID=$rptID&e=" + element + "&w=" + widthPct + "&h=" + heightPct;
  \$.get(url);
}



function koalaStorePos(element, xPct, yPct, width, height)
{
  let url = "xhrRptSizePos?rptID=$rptID&e=" + element + "&x=" + xPct + "&y=" + yPct;
  \$.get(url);
}



function koalaStoreZIndex(element, zindex)
{
  let url = "xhrRptSizePos?rptID=$rptID&e=" + element + "&z=" + zindex;
  \$.get(url);
}



function koalaClearSelected()
{
END_HTML

  $idx = 0;
  foreach $visType (@visTypeArray)
  {
    $visID = $visIDArray[$idx];

    if ($visType eq "table")
    {
      print("  \$('#table-$visID').removeClass('ui-selected');\n");
    }
    elsif ($visType eq "chart")
    {
      print("  \$('#chart-$visID').removeClass('ui-selected');\n");
    }
    elsif ($visType eq "map")
    {
      print("  \$('#map-$visID').removeClass('ui-selected');\n");
    }
    elsif ($visType eq "rectangle")
    {
      print("  \$('#rectangle-$visID').removeClass('ui-selected');\n");
    }
    elsif ($visType eq "text")
    {
      print("  \$('#text-$visID').removeClass('ui-selected');\n");
    }

    print("  \$('#remove-$visID').hide();\n");

    $idx++;
  }

  print <<END_HTML;
}


globalSelChartID = $selectedChart;
globalSelTableID = $selectedTable;
globalSelRectangleID = $selectedRect;
globalSelTextID = $selectedText;
globalSelVisual = $globalSelVisual;


function koalaReloadDimSel()
{
  let source, html;
  let visNames = new Object();
  let multiProd = new Object();
  let multiGeo = new Object();
  let multiTime = new Object();
  let multiMeas = new Object();
END_HTML

  foreach $visID (keys %jsVisNameHash)
  {
    print("  visNames[$visID] = \"$jsVisNameHash{$visID}\"\n");
    print("  multiProd[$visID] = $jsmultiProdHash{$visID}\n");
    print("  multiGeo[$visID] = $jsmultiGeoHash{$visID}\n");
    print("  multiTime[$visID] = $jsmultiTimeHash{$visID}\n");
    print("  multiMeas[$visID] = $jsmultiMeasHash{$visID}\n");
  }

  print <<END_HTML;
  if (globalSelVisual < 1)
  {
    html = "<STRONG>Data Selector</STRONG>";
  }
  else
  {
    html = "<A onclick='koalaDimSelReport();'>Change report-level selections</A><BR>";
    html = html + "<STRONG>" + visNames[globalSelVisual] + " Data Selection</STRONG>";
  }

  document.getElementById('datasel-header-div').innerHTML = html;

  const prodTree = \$("#ProdCheckboxTree").fancytree("getTree");
  const geoTree = \$("#GeoCheckboxTree").fancytree("getTree");
  const timeTree = \$("#TimeCheckboxTree").fancytree("getTree");
  const measTree = \$("#MeasureCheckboxTree").fancytree("getTree");

  source = "ajaxDimSelector.cld?ds=$dsID&r=$rptID&d=p&v=" + globalSelVisual + "&m=" + multiProd[globalSelVisual];
  prodTree.options.source = {url: source};
  prodTree.options.selectMode = multiProd[globalSelVisual];
  source = "ajaxDimSelector.cld?ds=$dsID&r=$rptID&d=g&v=" + globalSelVisual + "&m=" + multiGeo[globalSelVisual];
  geoTree.options.source = {url: source};
  geoTree.options.selectMode = multiGeo[globalSelVisual];
  source = "ajaxDimSelector.cld?ds=$dsID&r=$rptID&d=t&v=" + globalSelVisual + "&m=" + multiTime[globalSelVisual];
  timeTree.options.source = {url: source};
  timeTree.options.selectMode = multiTime[globalSelVisual];
  source = "ajaxDimSelector.cld?ds=$dsID&r=$rptID&d=m&v=" + globalSelVisual + "&m=" + multiMeas[globalSelVisual];
  measTree.options.source = {url: source};
  measTree.options.selectMode = multiMeas[globalSelVisual];

  if (globalSelVisual < 1)
  {
    prodTree.options.selectMode = $multiProduct;
    geoTree.options.selectMode = $multiGeography;
    timeTree.options.selectMode = $multiTime;
    measTree.options.selectMode = $multiMeasure;
  }

  prodTree.reload();
  geoTree.reload();
  timeTree.reload();
  measTree.reload();
}



function koalaDimSelReport()
{
  globalSelVisual = 0;

  koalaClearSelected();
  koalaReloadDimSel();
}



FusionCharts.ready(function()
{

  FusionCharts.addEventListener("chartClick", function (eventObject, argsObj)
  {
    let selChart = argsObj.container.id;
    let tmp = selChart.split("-");
    let removeSelCharDiv = "#remove-" + tmp[1];

    if (tmp[0] == "map")
    {
      selChartID = "#map-" + tmp[1];
    }
    else
    {
      selChartID = "#chart-" + tmp[1];
    }

    globalSelTableID = 0;
    koalaClearSelected();

    if (tmp[1] == globalSelChartID)
    {
      globalSelChartID = 0;
      globalSelVisual = 0;
      \$("#chart-design-drop").hide();
      \$("#map-design-drop").hide();
      koalaReloadDimSel();
      return;
    }

    \$(selChartID).addClass("ui-selected");
END_HTML

  if ($mobileMode == 0)
  {
    print("    \$(removeSelCharDiv).show();\n");
  }

  print <<END_HTML;
    globalSelChartID = tmp[1];
    globalSelVisual = globalSelChartID;

    \$("#table-design-drop").hide();
    \$("#map-design-drop").hide();
    \$("#chart-design-drop").hide();
    \$("#shape-design-drop").hide();
    \$("#text-design-drop").hide();
    if (tmp[0] == "map")
    {
      \$("#map-design-drop").show();
    }
    else
    {
      \$("#chart-design-drop").show();
    }

    koalaReloadDimSel();
  });
});



function koalaSelTable(eventObject)
{
  let i = 0;
  let selTableID, removeTableDiv, oldSelTableID;

  koalaClearSelected();

  globalSelChartID = 0;
  oldSelTableID = globalSelTableID;
  globalSelTableID = 0;
  globalSelVisual = 0;

  let pathArray = eventObject.composedPath();
  while ((i < 20) && (globalSelTableID < 1))
  {
    let tmpStr = pathArray[i].id;
    let tmp = tmpStr.split('-');

    if ((tmp[0] == "table") && (tmp[1] == "data"))
    {
      if (tmp[2] == oldSelTableID)
      {
        \$("#table-design-drop").hide();
        koalaReloadDimSel();
        return;
      }

      globalSelTableID = tmp[2];
      globalSelVisual = globalSelTableID;
      selTableID = "#table-" + globalSelTableID;
      removeTableDiv = "#remove-" + globalSelTableID;
    }

    i++;
  }

  \$(selTableID).addClass("ui-selected");
END_HTML

  if ($mobileMode == 0)
  {
    print("  \$(removeTableDiv).show();\n");
  }

  print <<END_HTML;
  \$("#table-design-drop").show();
  \$("#chart-design-drop").hide();
  \$("#map-design-drop").hide();
  \$("#text-design-drop").hide();
  \$("#shape-design-drop").hide();

  koalaReloadDimSel();
}



function koalaSelRectangle(eventObject)
{
  let i = 0;
  let selRectangleID, removeRectangleDiv, oldSelRectangleID;

  koalaClearSelected();

  globalSelChartID = 0;
  globalSelTableID = 0;
  oldSelRectangleID = globalSelRectangleID;
  globalSelRectangleID = 0;
  globalSelVisual = 0;

  let pathArray = eventObject.composedPath();
  let tmpStr = pathArray[i].id;
  let tmp = tmpStr.split('-');

  if (tmp[1] == oldSelRectangleID)
  {
    \$("#shape-design-drop").hide();
    return;
  }

  globalSelRectangleID = tmp[1];
  globalSelVisual = globalSelRectangleID;
  selRectangleID = "#rectangle-" + globalSelRectangleID;
  removeRectangleDiv = "#remove-" + globalSelRectangleID;

  \$(selRectangleID).addClass("ui-selected");
  \$(removeRectangleDiv).show();

  \$("#shape-design-drop").show();
  \$("#table-design-drop").hide();
  \$("#text-design-drop").hide();
  \$("#chart-design-drop").hide();
  \$("#map-design-drop").hide();
}



function koalaSelText(eventObject)
{
  let i = 0;
  let selTextID, removeTextDiv, oldSelTextID;

  koalaClearSelected();

  globalSelChartID = 0;
  globalSelTableID = 0;
  oldSelTextID = globalSelTextID;
  globalSelTextID = 0;
  globalSelRectangleID = 0;
  globalSelVisual = 0;

  let pathArray = eventObject.composedPath();
  while ((i < 20) && (globalSelTextID < 1))
  {
    let tmpStr = pathArray[i].id;
    let tmp = tmpStr.split('-');

    if ((tmp[0] == "text") && (!(isNaN(tmp[1]))))
    {
      if (tmp[1] == oldSelTextID)
      {
        \$("#text-design-drop").hide();
        return;
      }
      globalSelTextID = tmp[1];
      globalSelVisual = globalSelTextID;
      selTextID = "#text-" + globalSelTextID;
      removeTextDiv = "#remove-" + globalSelTextID;
    }
    i++;
  }

  \$(selTextID).addClass("ui-selected");
  \$(removeTextDiv).show();

  \$("#text-design-drop").show();
  \$("#shape-design-drop").hide();
  \$("#table-design-drop").hide();
  \$("#chart-design-drop").hide();
  \$("#map-design-drop").hide();
}
END_HTML

  #construct the JS we need to run when a checkbox changes to redraw graphs
  $onChangeJS = "";
  $idx = 0;
  foreach $visID (@visIDArray)
  {
    $visType = $visTypeArray[$idx];
    $visDesign = $visDesignArray[$idx];

    if ($visType eq "chart")
    {
      $tmp = "chart_" . $visID;
      $chartType = reports_get_style($visDesign, "type");
      $chartAjax = reports_data_script($chartType);
      $onChangeJS .= "$tmp.setXMLUrl('$chartAjax?rpt=$rptID&v=$visID&n=$idx&$fDim');\n";
    }

    elsif ($visType eq "map")
    {
      $tmp = "map_" . $visID;
      $onChangeJS .= "$tmp.setXMLUrl('ajaxReportMap.cld?rpt=$rptID&v=$visID&$fDim');\n";
    }

    elsif ($visType eq "table")
    {
      $tmp = "#table-data-" . $visID;
      $onChangeJS .= "\$('#wait-spinner-$visID').css('display', 'block');
      \$('$tmp').load('displayTable.cld?rpt=$rptID&v=$visID&$fDim&l=$local', function (response, status, xhr)
      {
        if (status == 'success')
          \$('#wait-spinner-$visID').css('display', 'none');
      });";
    }

    $idx++;
  }

  print <<END_HTML;


function changeSlicer(segID)
{
  let id = "slicer-" + segID;
  let segmentID = document.getElementById(id).value;

  const url = "xhrdimslicer?rptID=$rptID&d=p&seg=" + segID + "&segment=" + segmentID;
  \$.get(url, function(data, status)
  {
    $onChangeJS;
  });
}



\$(function()
{
  \$("#ProdCheckboxTree").fancytree(
  {
    checkbox: true,
    selectMode: $multiProduct,
    quicksearch: true,
    autoScroll: true,
//    extensions: ["multi"],
    source: {url: "ajaxDimSelector.cld?ds=$dsID&r=$rptID&d=p&m=$multiProduct"},
    select: function(event, data)
    {
      let clickedItem = data.node.key;
      let itemState = data.node.isSelected();

      let url = "xhrdimsel?rptID=$rptID&d=p&item=" + clickedItem +
          "&state=" + itemState + "&v=" + globalSelVisual;
      \$.get(url, function(data, status)
      {
        $onChangeJS;
      });
    }
  });

  \$("#GeoCheckboxTree").fancytree(
  {
    checkbox: true,
    selectMode: $multiGeography,
    quicksearch: true,
    autoScroll: true,
//    extensions: ["multi"],
    source: {url: "ajaxDimSelector.cld?ds=$dsID&r=$rptID&d=g&m=$multiGeography"},
    select: function(event, data)
    {
      let clickedItem = data.node.key;
      let itemState = data.node.isSelected();

      let url = "xhrdimsel?rptID=$rptID&d=g&item=" + clickedItem +
          "&state=" + itemState + "&v=" + globalSelVisual;
      \$.get(url, function(data, status)
      {
        $onChangeJS;
      });
    }
  });

  \$("#TimeCheckboxTree").fancytree(
  {
    checkbox: true,
    selectMode: $multiTime,
    quicksearch: true,
    autoScroll: true,
//    extensions: ["multi"],
    source: {url: "ajaxDimSelector.cld?ds=$dsID&r=$rptID&d=t&m=$multiTime"},
    select: function(event, data)
    {
      let clickedItem = data.node.key;
      let itemState = data.node.isSelected();

      let url = "xhrdimsel?rptID=$rptID&d=t&item=" + clickedItem +
          "&state=" + itemState + "&v=" + globalSelVisual;
      \$.get(url, function(data, status)
      {
        $onChangeJS;
      });
    }
  });

  \$("#MeasureCheckboxTree").fancytree(
  {
    checkbox: true,
    selectMode: $multiMeasure,
    quicksearch: true,
    autoScroll: true,
//    extensions: ["multi"],
    source: {url: "ajaxDimSelector.cld?ds=$dsID&r=$rptID&d=m&m=$multiMeasure"},
    select: function(event, data)
    {
      let clickedItem = data.node.key;
      let itemState = data.node.isSelected();

      let url = "xhrdimsel?rptID=$rptID&d=m&item=" + clickedItem + "&state=" + itemState + "&v=" + globalSelVisual;
      \$.get(url, function(data, status)
      {
        $onChangeJS;
      });
    }
  });

  if (globalSelVisual > 0)
  {
    koalaReloadDimSel();
  }
});
</SCRIPT>
<!--split_here_script_bottom-->
<SCRIPT>


function reloadPage()
{
  location.href = "/app/rpt/display.cld?rpt=$rptID";
}



function showVisDesignDlg(styleGroup, selectedVis)
{
  const url = "/app/rpt/" + styleGroup + "?rptID=$rptID&v=" + selectedVis;

  \$("#wait-spinner").css("display", "block");
  \$('#modal-visual-design').load(url, function (response, status, xhr)
  {
    if (status == "success")
    {
      let myModal = new bootstrap.Modal(document.getElementById('modal-visual-design'));
      myModal.show();
      \$("#wait-spinner").css("display", "none");
    }
  });
}



function showExportDlg(exportType)
{
  const url = "/app/rpt/" + exportType + "?rptID=$rptID";

  \$("#wait-spinner").css("display", "block");
  \$('#modal-export').load(url, function (response, status, xhr)
  {
    if (status == "success")
    {
      let myModal = new bootstrap.Modal(document.getElementById('modal-export'));
      myModal.show();
      \$("#wait-spinner").css("display", "none");
    }
  });
}



function showExcelDataLinkDlg()
{
  \$("#wait-spinner").css("display", "block");
  \$('#modal-live-link').load('/app/rpt/xhrExcelLink?rptID=$rptID', function (response, status, xhr)
  {
    if (status == "success")
    {
      let myModal = new bootstrap.Modal(document.getElementById('modal-live-link'));
      myModal.show();
      \$("#wait-spinner").css("display", "none");
    }
  });
}



\$(document).ready(function()
{
  \$('.nav-tabs').scrollingTabs({enableSwiping: true});

  \$('$selectedRemoveVisID').show();

  \$('modal-visual-design').on('hide.bs.modal', function ()
  {
    \$('#modal-visual-design').removeData('bs.modal');
    \$('#modal-visual-design .modal-content').html('');
  });

  \$('modal-export').on('hide.bs.modal', function ()
  {
    \$('#modal-export').removeData('bs.modal');
    \$('#modal-export .modal-content').html('');
  });

  \$('#modal-live-link').on('hide.bs.modal', function ()
  {
    \$('#modal-live-link').removeData('bs.modal');
    \$('#modal-live-link .modal-content').html('');
  });
});
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  if ($clean != 1)
  {
    $dsName = ds_id_to_name($db, $dsID);
    print <<END_HTML;
<DIV ID="page_overlay"></DIV>
<DIV ID="present_controls_wrapper">
 <DIV ID="present_nav_previous" CLASS="present_nav"><I CLASS="material-icons present_icon">navigate_before</I></DIV>
 <DIV ID="present_nav_next" CLASS="present_nav"><I CLASS="material-icons present_icon">navigate_next</I></DIV>
 <DIV ID="present_nav_exit" CLASS="present_nav"><I CLASS="material-icons present_icon_x">clear</I></DIV>
</DIV>

<NAV CLASS="navbar navbar-expand navbar-light bg-light border" STYLE="height:32px;">
  <UL CLASS="nav navbar-nav" STYLE="width:100%;">
  <TABLE STYLE="width:100%; height:32px;">
   <TR>
    <TD STYLE="vertical-align:middle; text-align:left;><A HREF="#"></A>
    <A CLASS="text-decoration-none" HREF="/app/home.cld"><IMG SRC="/images/smlogo.png?1" ALT="Koala Home"></A> &gt;
    <A CLASS="text-decoration-none" HREF="/app/rpt/main?ds=$dsID">Reports</A> &gt;
    <A CLASS="text-decoration-none" HREF="/app/rpt/main?ds=$dsID">$dsName</A> &gt;
    $rptName
    </TD>
    <TD STYLE="text-align:right;">
    <A CLASS="text-decoration-none" HREF="/"><IMG SRC="/icons/icon_logout.png" TITLE="Logout" ALT="Logout"></A>

    </TD>
   </TR>
  </TABLE>
  </UL>
</NAV>
END_HTML
  }

  #output placeholders for modal dialogs
  #NB: We need to do this at the top of the page to keep Dojo from messing
  #    with it after the checkbox tree DIVs
  print <<END_HTML;
<DIV ID="wait-spinner" STYLE="display:none; z-index:1001; width:100%; height:100%; position:absolute; top:0; left:0; background-color: rgba(0,0,0,0.15);">
 <DIV STYLE="position:absolute; top:50%; left:50%;">
  <I CLASS="fas fa-circle-notch fa-spin" STYLE="font-size:100px; color:blue;"></I>
 </DIV>
</DIV>

<DIV ID="modal-visual-design" CLASS="modal">
</DIV>

<DIV ID="modal-export" CLASS="modal">
</DIV>

<DIV ID="modal-live-link" CLASS="modal">
</DIV>
END_HTML


#-------------------------------------------------------------------------------
#
# Output drop-down combobuttons to format chart or export report
#

  if ($clean != 1)
  {
    print <<END_HTML;
<NAV CLASS="navbar navbar-expand navbar-light bg-light border" STYLE="height:32px;">
  <BUTTON CLASS="navbar-toggler" TYPE="button" data-bs-toggle="collapse" data-bs-target="#navbarNavDropdown">
    <SPAN CLASS="navbar-toggler-icon"></SPAN>
  </BUTTON>
  <DIV CLASS="collapse navbar-collapse" ID="navbarNavDropdown">
    <UL CLASS="navbar-nav">
END_HTML

  if ($privs eq "W")
  {
    print <<END_HTML;
      <LI CLASS="nav-item dropdown">
        <A CLASS="nav-link dropdown-toggle" data-bs-toggle="dropdown" HREF="#"><I CLASS="bi bi-plus-lg"></I> Insert</A>
        <UL CLASS="dropdown-menu">
          <LI>
            <IMG WIDTH="35" SRC="/icons/table.png" TITLE="Table" onClick="location.href='?rpt=$rptID&a=it'">
            <IMG WIDTH="35" SRC="/icons/map.png" TITLE="Map" onClick="location.href='?rpt=$rptID&a=im'">
            <IMG WIDTH="35" SRC="/icons/rectangle.png" TITLE="Rectangle" onClick="location.href='?rpt=$rptID&a=irect'">
            <IMG WIDTH="35" SRC="/icons/textbox.png" TITLE="Text Box" onClick="location.href='?rpt=$rptID&a=text'">
          </LI>
          <LI>
            <IMG WIDTH="35" SRC="/icons/chart_lines.png" TITLE="Lines" onClick="location.href='?rpt=$rptID&a=ic&type=Lines'">
            <IMG WIDTH="35" SRC="/icons/chart_lines.png" TITLE="Zoom Lines" onClick="location.href='?rpt=$rptID&a=ic&type=ZoomLines'">
            <IMG WIDTH="35" SRC="/icons/chart_scatter.png" TITLE="Scatter" onClick="location.href='?rpt=$rptID&a=ic&type=Scatter'">
            <IMG WIDTH="35" SRC="/icons/chart_area.png" TITLE="Area" onClick="location.href='?rpt=$rptID&a=ic&type=Area'">
          </LI>
          <LI>
            <IMG WIDTH="35" SRC="/icons/chart_2Dcolumn.png" TITLE="2D Columns" onClick="location.href='?rpt=$rptID&a=ic&type=2DColumns'">
            <IMG WIDTH="35" SRC="/icons/chart_2Dstackcolumn.png" TITLE="Stacked 2D Columns" onClick="location.href='?rpt=$rptID&a=ic&type=Stacked2DColumns'">
            <IMG WIDTH="35" SRC="/icons/chart_3Dcolumn.png" TITLE="3D Columns" onClick="location.href='?rpt=$rptID&a=ic&type=3DColumns'">
            <IMG WIDTH="35" SRC="/icons/chart_3Dstackcolumn.png" TITLE="Stacked 3D Columns" onClick="location.href='?rpt=$rptID&a=ic&type=Stacked3DColumns'">
          </LI>
          <LI>
            <IMG WIDTH="35" SRC="/icons/chart_2Dbar.png" TITLE="2D Bars" onClick="location.href='?rpt=$rptID&a=ic&type=2DBars'">
            <IMG WIDTH="35" SRC="/icons/chart_2Dstackbar.png" TITLE="Stacked 2D Bars" onClick="location.href='?rpt=$rptID&a=ic&type=Stacked2DBars'">
            <IMG WIDTH="35" SRC="/icons/chart_3Dbar.png" TITLE="3D Bars" onClick="location.href='?rpt=$rptID&a=ic&type=3DBars'">
            <IMG WIDTH="35" SRC="/icons/chart_3Dstackbar.png" TITLE="Stacked 3D Bars" onClick="location.href='?rpt=$rptID&a=ic&type=Stacked3DBars'">
          </LI>
          <LI>
            <IMG WIDTH="35" SRC="/icons/chart_2Dpie.png" TITLE="2D Pie" onClick="location.href='?rpt=$rptID&a=ic&type=2DPie'">
            <IMG WIDTH="35" SRC="/icons/chart_3Dpie.png" TITLE="3D Pie" onClick="location.href='?rpt=$rptID&a=ic&type=3DPie'">
            <IMG WIDTH="35" SRC="/icons/chart_2Ddonut.png" TITLE="2D Donut" onClick="location.href='?rpt=$rptID&a=ic&type=2DDonut'">
            <IMG WIDTH="35" SRC="/icons/chart_3Ddonut.png" TITLE="3D Donut" onClick="location.href='?rpt=$rptID&a=ic&type=3DDonut'">
          </LI>
          <LI>
            <IMG WIDTH="35" SRC="/icons/chart_radar.png" TITLE="Radar" onClick="location.href='?rpt=$rptID&a=ic&type=Radar'">
            <IMG WIDTH="35" SRC="/icons/chart_heatmap.png" TITLE="Tree Map" onClick="location.href='?rpt=$rptID&a=ic&type=TreeMap'">
            <IMG WIDTH="35" SRC="/icons/chart_waterfall.png" TITLE="Waterfall" onClick="location.href='?rpt=$rptID&a=ic&type=Waterfall'">
            <IMG WIDTH="35" SRC="/icons/chart_bubble.png" TITLE="Bubble" onClick="location.href='?rpt=$rptID&a=ic&type=Bubble'">
          </LI>
          <LI>
            <IMG WIDTH="35" SRC="/icons/chart_funnel.png" TITLE="Funnel" onClick="location.href='?rpt=$rptID&a=ic&type=Funnel'">
            <IMG WIDTH="35" SRC="/icons/chart_pareto.png" TITLE="Pareto" onClick="location.href='?rpt=$rptID&a=ic&type=Pareto'">
            <IMG WIDTH="35" SRC="/icons/chart_columnline.png" TITLE="Combined" onClick="location.href='?rpt=$rptID&a=ic&type=ColumnLine'">
            <IMG WIDTH="35" SRC="/icons/chart_dualy.png" TITLE="Dual Y" onClick="location.href='?rpt=$rptID&a=ic&type=DualY'">
          </LI>
        </UL>
      </LI>

      <LI CLASS="nav-item dropdown" ID="table-design-drop">
        <A CLASS="nav-link dropdown-toggle" data-bs-toggle="dropdown" HREF="#"><I CLASS="bi bi-brush"></I> Table Design</A>
        <UL CLASS="dropdown-menu">
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showVisDesignDlg('xhrTableGeneral', globalSelTableID)">General</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showVisDesignDlg('xhrTableStyle.cld', globalSelTableID)">Style</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showVisDesignDlg('xhrTableGrid', globalSelTableID)">Grid</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showVisDesignDlg('xhrTableHeaders', globalSelTableID)">Column Headers</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showVisDesignDlg('xhrTableValues.cld', globalSelTableID)">Values</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showVisDesignDlg('xhrTableConditionalFormatting', globalSelTableID)">Conditional Formatting</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showVisDesignDlg('xhrTableTitle.cld', globalSelTableID)">Titles</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showVisDesignDlg('xhrTableLayout', globalSelTableID)">Table Layout</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showVisDesignDlg('xhrTableBorder', globalSelTableID)">Border</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showVisDesignDlg('xhrTableSort.cld', globalSelTableID)">Sorting</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showVisDesignDlg('xhrTableFilter', globalSelTableID)">Filtering</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showVisDesignDlg('xhrChartLockDataSel', globalSelTableID)">Lock Data Selection</A></LI>
        </UL>
      </LI>

      <LI CLASS="nav-item dropdown" ID="map-design-drop">
        <A CLASS="nav-link dropdown-toggle" data-bs-toggle="dropdown" HREF="#"><I CLASS="bi bi-brush"></I> Map Design</A>
        <UL CLASS="dropdown-menu">
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showVisDesignDlg('xhrMapList', globalSelChartID)">Map Type</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showVisDesignDlg('xhrMapGeneral', globalSelChartID)">General</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showVisDesignDlg('xhrMapLabels', globalSelChartID)">Labels</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showVisDesignDlg('xhrMapColors', globalSelChartID)">Data Range Coloring</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showVisDesignDlg('xhrMapTitle', globalSelChartID)">Titles</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showVisDesignDlg('xhrMapLockMeasure', globalSelChartID)">Lock Displayed Measure</A></LI>
        </UL>
      </LI>

      <LI CLASS="nav-item dropdown" ID="chart-design-drop">
        <A CLASS="nav-link dropdown-toggle" data-bs-toggle="dropdown" HREF="#"><I CLASS="bi bi-brush"></I> Chart Design</A>
        <UL CLASS="dropdown-menu">
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showVisDesignDlg('xhrChartType', globalSelChartID)">Chart Type</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showVisDesignDlg('xhrChartGeneral', globalSelChartID)">General</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showVisDesignDlg('xhrChartLegend', globalSelChartID)">Legend</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showVisDesignDlg('xhrChartSeriesColor', globalSelChartID)">Data Colors</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showVisDesignDlg('xhrChartDataLabels', globalSelChartID)">Data Labels</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showVisDesignDlg('xhrChartDataValues', globalSelChartID)">Data Values</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showVisDesignDlg('xhrChartBorderBack', globalSelChartID)">Border & Background</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showVisDesignDlg('xhrChartTitles', globalSelChartID)">Title</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showVisDesignDlg('xhrChartFilter', globalSelChartID)">Filtering</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showVisDesignDlg('xhrChartLayout', globalSelChartID)">Chart Layout</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showVisDesignDlg('xhrChartLockDataSel', globalSelChartID)">Lock Data Selection</A></LI>
        </UL>
      </LI>

      <LI CLASS="nav-item dropdown" ID="shape-design-drop">
        <A CLASS="nav-link dropdown-toggle" data-bs-toggle="dropdown" HREF="#"><I CLASS="bi bi-brush"></I> Shape Design</A>
        <UL CLASS="dropdown-menu">
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showVisDesignDlg('xhrShapeTitle', globalSelRectangleID)">Title</A></LI>
        </UL>
      </LI>

      <LI CLASS="nav-item dropdown" ID="text-design-drop">
        <A CLASS="nav-link dropdown-toggle" data-bs-toggle="dropdown" HREF="#"><I CLASS="bi bi-card-text"></I> Text Box Design</A>
        <UL CLASS="dropdown-menu">
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showVisDesignDlg('xhrTextContent.cld', globalSelTextID)">Edit Contents</A></LI>
        </UL>
      </LI>

      <SCRIPT>
        \$('#table-design-drop').hide();
        \$('#chart-design-drop').hide();
        \$('#map-design-drop').hide();
        \$('#shape-design-drop').hide();
        \$('#text-design-drop').hide();
        let selVisStyleMenuID = "$styleButtonShow";
        if (selVisStyleMenuID.length > 0)
        {
          \$('#$styleButtonShow').show();
        }
      </SCRIPT>

      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="/app/datasel/datasel.cld?ds=$dsID&rptID=$rptID"><I CLASS="bi bi-pencil"></I> Modify</A></LI>

      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="cubeUpdate.cld?ds=$dsID&rptID=$rptID"><I CLASS="bi bi-arrow-clockwise"></I> Refresh</A></LI>

      <LI CLASS="nav-item dropdown">
        <A CLASS="nav-link dropdown-toggle" data-bs-toggle="dropdown" HREF="#"><I CLASS="bi bi-gear"></I> Report Options</A>
        <UL CLASS="dropdown-menu">
        <LI><A CLASS="dropdown-item" HREF="rptBackground.cld?ds=$dsID&rpt=$rptID">Report Background</A></LI>
        <LI CLASS="dropdown-divider"></LI>
        <LI><A CLASS="dropdown-item" HREF="#" onclick="showVisDesignDlg('xhrSlicer')">Slicer Configuration</A></LI>
        <LI><A CLASS="dropdown-item" HREF="#" onclick="showVisDesignDlg('xhrLockVisuals')">Lock Visuals</A></LI>
        </UL>
      </LI>
END_HTML
  }

  print <<END_HTML;
      <LI CLASS="nav-item dropdown">
        <A CLASS="nav-link dropdown-toggle" data-bs-toggle="dropdown" HREF="#"><I CLASS="bi bi-cloud-download"></I> Export</A>
        <UL CLASS="dropdown-menu">
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showExportDlg('xhrExcelReport')">Export Report to Excel</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showExportDlg('xhrPPTslide')">Export PowerPoint Slide</A></LI>
          <LI><A CLASS="dropdown-item" HREF="exportPPTdeckSlides.cld?ds=$dsID&rpt=$rptID">Export Reports to PowerPoint Deck</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showExportDlg('xhrExcelCube')">Export Cube to Excel</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showExcelDataLinkDlg()">Excel Live Data Link</A></LI>
          <LI><A CLASS="dropdown-item" HREF="exportExpandedDim.cld?rpt=$rptID">Export with Expanded Dimensions</A></LI>
        </UL>
      </LI>
END_HTML

  if ($privs eq "W")
  {
    print <<END_HTML;

      <LI CLASS="nav-item dropdown">
        <A CLASS="nav-link dropdown-toggle" data-bs-toggle="dropdown" HREF="#"><I CLASS="bi bi-three-dots-vertical"></I> More</A>
        <UL CLASS="dropdown-menu">
          <LI><A CLASS="dropdown-item" HREF="history.cld?rptID=$rptID">History</A></LI>
          <LI><A CLASS="dropdown-item" HREF="statistics.cld?rptID=$rptID">Statistics</A></LI>
          <LI CLASS="dropdown-divider"></LI>
          <LI><A CLASS="dropdown-item" HREF="accessControl.cld?c=$rptID">Sharing</A></LI>
          <LI><A CLASS="dropdown-item" HREF="properties.cld?c=$rptID">Properties</A></LI>
        </UL>
      </LI>
END_HTML
  }

  print <<END_HTML;
      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" ID="presentation_mode_button"><I CLASS="bi bi-arrows-fullscreen"></I> Full Screen</A></LI>
    </UL>
  </DIV>
</NAV>
END_HTML
  }


  # --------------- Visualization Canvas -----------------------

  if ($clean == 1)
  {
    $visContainerStyle = "STYLE='top:0px; left:0px; bottom:0px;'";
  }
  else
  {
    $visContainerStyle = "";
  }

  print <<END_HTML;
<DIV ID="vis-container-wrapper" $visContainerStyle>

<TABLE STYLE="width:100%; height: 100%;">
 <TR>
END_HTML

  print_data_selector();

  if ($rptBackgroundID > 0)
  {
    $rptBackgroundStyle = "background-image: url('/app/rpt/imageRetrieve.cld?t=b&id=$rptBackgroundID'); background-repeat: no-repeat;";
  }

  print <<END_HTML;
  <TD ID="vis-container" VALIGN="top" STYLE="$rptBackgroundStyle">
END_HTML

  #set sizing of visualization if we're in "clean" mode
  if ($clean == 1)
  {
    print <<END_HTML
<SCRIPT>
\$("td#vis-container").height(viewportHeight);
\$("td#vis-container").width(viewportWidth);
</SCRIPT>
END_HTML
  }

  #output HTML & JS for each visualization element
  $idx = 0;
  foreach $visType (@visTypeArray)
  {

    $visDesign = $visDesignArray[$idx];
    $visID = $visIDArray[$idx];

    #get size and position info
    $visWidthPct = reports_get_style($visDesign, "width");
    $visHeightPct = reports_get_style($visDesign, "height");
    $visXPct = reports_get_style($visDesign, "xpct");
    $visYPct = reports_get_style($visDesign, "ypct");
    $visZIndex = reports_get_style($visDesign, "zindex");

    if (!defined($visWidthPct))
    {
      $visWidthPct = 0.98;
    }
    if (!defined($visHeightPct))
    {
      $visHeightPct = 0.98;
    }
    if (length($visXPct) < 1)
    {
      $visXPct = 0;
    }
    if (length($visYPct) < 1)
    {
      $visYPct = 0;
    }
    if (length($visZIndex) < 1)
    {
      $visZIndex = 1;
    }

    #set the action URL to delete a visualization
    if ($acctType > 0)
    {
      $visRemoveURL = "location.href='?rpt=$rptID&v=$visID&a=d'";
    }
    else
    {
      $visRemoveURL = "";
    }

    #output draggable/resizeable DIV for visualization
    if ($visType eq "chart")
    {
      $chartType = reports_get_style($visDesign, "type");
      $chartAlias = reports_graph_alias($chartType);
      $chartAjax = reports_data_script($chartType);
      $visName = "chart-" . $visID;

      print <<END_HTML;
<DIV ID="$visName" STYLE="position:absolute;" CLASS="ui-widget-content">
  <DIV ID="$visName-fc" STYLE="height:100%; width:100%"></DIV>
  <DIV ID="remove-$visID" CLASS="remove_icon" onClick="$visRemoveURL" STYLE="background-color:red; color:white; height:25px; width:25px; position:absolute; top:0px; right:0px; text-align:center;">X</DIV>
</DIV>

<SCRIPT>
var visWidthPct = $visWidthPct * 100 + "%";
var height = $visHeightPct * 100 + "%";
var x = $visXPct * 100 + "%";
var y = $visYPct * 100 + "%";
var zindex = $visZIndex;
END_HTML

    #don't make visualizations draggable/resizeable for PPT output
    if (($lockVisuals != 1) && ($acctType > 0))
    {
      print <<END_HTML;
\$(function()
{
  \$("#$visName").resizable(
  {
    autoHide: true,
    handles: "all",
    containment: "parent",
    stop: function(event, ui)
    {
      let widthPct = parseInt(\$(this).css("width")) / \$("#vis-container").innerWidth();
      let heightPct = parseInt(\$(this).css("height")) / \$("#vis-container").innerHeight();
      \$(this).css({width: widthPct * 100 + "%", height: heightPct * 100 + "%"});
      koalaStoreSize("$visID", widthPct, heightPct);
    }
  });

  \$("#$visName").draggable(
  {
    containment: "parent",
    stop: function(event, ui)
    {
      let leftRelPos = parseInt(\$(this).css("left")) / \$("#vis-container").innerWidth();
      let topRelPos = parseInt(\$(this).css("top")) / \$("#vis-container").innerHeight();
      \$(this).css({left: leftRelPos * 100 + "%", top: topRelPos * 100 + "%"});
      koalaStorePos("$visID", leftRelPos, topRelPos, \$("td#vis-container").width(), \$("td#vis-container").height());
    }
  });

  \$("#$visName").click(function()
  {
    if(\$(".ui-widget-content").length > 1)
    {
      let that = this;
      let highestZIdx = 0;
      \$(".ui-widget-content").each(function()
      {
        if (that != this)
        {
          let visZIdx = parseInt(\$(this).css("z-index"));
          if (Number.isInteger(visZIdx))
          {
            if (visZIdx > highestZIdx)
            {
              highestZIdx = visZIdx;
            }
          }
        }
      });

      \$(this).css("z-index", highestZIdx + 1);
      koalaStoreZIndex("$visID", highestZIdx + 1);
    }
  });
});
END_HTML
    }

    print <<END_HTML
\$("#$visName").css({top:y, left:x, width:visWidthPct, height:height, zIndex:zindex});
\$("#remove-$visID").hide();

\$(document).ready(function()
{
  chart_$visID = new FusionCharts({"type": "$chartAlias", "width": "99%", "height": "99%", "dataFormat": "xml"});
  chart_$visID.setXMLUrl("/app/rpt/$chartAjax?rpt=$rptID&v=$visID&n=1&$fDim&u=$userID");
  chart_$visID.render("$visName-fc");
});
</SCRIPT>
END_HTML
    }

    elsif ($visType eq "map")
    {
      $mapType = reports_get_style($visDesign, "type");
      $visName = "map-" . $visID;

      print <<END_HTML;
<DIV ID="$visName" STYLE="position:absolute;" CLASS="ui-widget-content">
  <DIV ID="$visName-fm" STYLE="height:100%; width:100%"></DIV>
  <DIV ID="remove-$visID" CLASS="remove_icon" onClick="$visRemoveURL" STYLE="background-color:red; color:white; height:25px; width:25px; position:absolute; top:0px; right:0px; text-align:center;">X</DIV>
</DIV>

<SCRIPT>
var visWidthPct = $visWidthPct * 100 + "%";
var height = $visHeightPct * 100 + "%";
var x = $visXPct * 100 + "%";
var y = $visYPct * 100 + "%";
var zindex = $visZIndex;
END_HTML

      #don't make visualizations draggable/resizeable for PPT output or viewers
      if (($lockVisuals != 1) && ($acctType > 0))
      {
        print <<END_HTML;
\$(function()
{
  \$("#$visName").resizable(
  {
    autoHide: true,
    handles: "all",
    containment: "parent",
    stop: function(event, ui)
    {
      let widthPct = parseInt(\$(this).css("width")) / \$("#vis-container").innerWidth();
      let heightPct = parseInt(\$(this).css("height")) / \$("#vis-container").innerHeight();
      \$(this).css({ width: widthPct * 100 + "%", height: heightPct * 100 + "%"});
      koalaStoreSize("$visID", widthPct, heightPct);
    }
  });


  \$("#$visName").draggable(
  {
    containment: "parent",
    stop: function(event, ui)
    {
      let leftRelPos = parseInt(\$(this).css("left")) / \$("#vis-container").innerWidth();
      let topRelPos = parseInt(\$(this).css("top")) / \$("#vis-container").innerHeight();
      \$(this).css({ left: leftRelPos * 100 + "%", top: topRelPos * 100 + "%"});
      koalaStorePos("$visID", leftRelPos, topRelPos, \$("td#vis-container").width(), \$("td#vis-container").height());
    }
  });


  \$("#$visName").click(function()
  {
    if(\$(".ui-widget-content").length > 1)
    {
      let that = this;
      let highestZIdx = 0;

      \$(".ui-widget-content").each(function()
      {
        if (that != this)
        {
          let visZIdx = parseInt(\$(this).css("z-index"));
          if (Number.isInteger(visZIdx))
          {
            if (visZIdx > highestZIdx)
            {
              highestZIdx = visZIdx;
            }
          }
        }
      });

      \$(this).css("z-index", highestZIdx + 1);
      koalaStoreZIndex("$visID", highestZIdx + 1);
    }
  });
});
END_HTML
      }

      print <<END_HTML
  \$("#$visName").css({top:y, left:x, width:visWidthPct, height:height, zIndex:zindex});
  \$("#remove-$visID").hide();

  map_$visID = new FusionCharts("maps/$mapType", "MapId_$visID", "99%", "99%", "0", "1");
  map_$visID.setXMLUrl("/app/rpt/ajaxReportMap.cld?rpt=$rptID&v=$visID&n=1&$fDim&u=$userID");
  map_$visID.render("$visName-fm");
</SCRIPT>
END_HTML
    }

    elsif ($visType eq "table")
    {
      $visName = "table-" . $visID;

      print <<END_HTML;
<DIV ID="$visName" STYLE="position:absolute;" CLASS="ui-widget-content">
  <DIV ID="wait-spinner-$visID" STYLE="z-index:1001; position:absolute; top:50%; left:50%;">
    <I CLASS="fas fa-spinner fa-spin" STYLE="font-size:100px; color:lightblue;"></I>
  </DIV>
  <DIV ID="table-data-$visID" STYLE="height:100%; overflow:auto;">
  </DIV>
  <DIV ID="remove-$visID" CLASS="remove_icon" onClick="$visRemoveURL" STYLE="background-color:red; color:white; height:25px; width:25px; position:absolute; top:0px; right:0px; text-align:center;">X</DIV>
</DIV>

<SCRIPT>
var visWidthPct = $visWidthPct * 100 + "%";
var height = $visHeightPct * 100 + "%";
var x = $visXPct * 100 + "%";
var y = $visYPct * 100 + "%";
var zindex = $visZIndex;
END_HTML

      #don't make visualizations draggable/resizeable for PPT output
      if (($lockVisuals != 1) && ($acctType > 0))
      {
        print <<END_HTML;

\$(function()
{
  \$("#$visName").resizable(
  {
    autoHide: true,
    handles: "all",
    containment: "parent",
    stop: function(event, ui)
    {
      let widthPct = parseInt(\$(this).css("width")) / \$("#vis-container").innerWidth();
      let heightPct = parseInt(\$(this).css("height")) / \$("#vis-container").innerHeight();
      \$(this).css({ width: widthPct * 100 + "%", height: heightPct * 100 + "%"});
      koalaStoreSize("$visID", widthPct, heightPct);
    }
  });

  \$("#$visName").draggable(
  {
    containment: "parent",
    stop: function(event, ui)
    {
      let leftRelPos = parseInt(\$(this).css("left")) / \$("#vis-container").innerWidth();
      let topRelPos = parseInt(\$(this).css("top")) / \$("#vis-container").innerHeight();
      \$(this).css({ left: leftRelPos * 100 + "%", top: topRelPos * 100 + "%"});
      koalaStorePos("$visID", leftRelPos, topRelPos, \$("td#vis-container").width(), \$("td#vis-container").height());
    }
  });

  \$("#$visName").click(function()
  {
    if(\$(".ui-widget-content").length > 1)
    {
      let that = this;
      let highestZIdx = 0;
      \$(".ui-widget-content").each(function()
      {
        if (that != this)
        {
          let visZIdx = parseInt(\$(this).css("z-index"));
          if (Number.isInteger(visZIdx))
          {
            if (visZIdx > highestZIdx)
            {
              highestZIdx = visZIdx;
            }
          }
        }
      });
      \$(this).css("z-index", highestZIdx + 1);
      koalaStoreZIndex("$visID", highestZIdx + 1);
    }
  });
});
END_HTML
      }

      print <<END_HTML;
  \$("#$visName").css({top:y, left:x, width:visWidthPct, height:height, zIndex:zindex});
  \$("#remove-$visID").hide();

  \$("#table-data-$visID").load("displayTable.cld?rpt=$rptID&v=$visID&$fDim&l=$local&u=$userID", function (response, status, xhr)
  {
    if (status == "success")
    {
      \$("#wait-spinner-$visID").css("display", "none");
    }
  });

  document.getElementById("$visName").addEventListener("click", koalaSelTable, false);
</SCRIPT>
END_HTML
    }

    elsif ($visType eq "rectangle")
    {
      $visName = "rectangle-" . $visID;
      $shapeTitle = reports_get_style($visDesign, "title");
      #$shapeTitle = substr $shapeTitle, 1, -1;
      $fontColor = reports_get_style($visDesign, "fontColor");
      $titleFontSize = reports_get_style($visDesign, "titleFontSize");
      if ($titleFontSize < 3)
      {
        $titleFontSize = "18px";
      }
      else
      {
        $titleFontSize = $titleFontSize . "px";
      }
      $bgColor = reports_get_style($visDesign, "titleBgColor");
      $titleTextAlign = reports_get_style($visDesign, "titleTextAlign");
      if (length($titleTextAlign) < 3)
      {
        $titleTextAlign = "left";
      }
      $titleTextFont = reports_get_style($visDesign, "titleTextFont");
      if (length($titleTextFont) < 3)
      {
        $titleTextFont = "";
      }
      else
      {
        $titleTextFont = "font-family:'$titleTextFont';";
      }

      print <<END_HTML;
<DIV ID="$visName" STYLE="position:absolute;" CLASS="ui-widget-content a_rectangle_wrapper">
  <div class='a_rectangle_header' style='background-color: $bgColor'>
    <div class='a_rectangle_text' style="color:$fontColor; font-size:$titleFontSize; text-align:$titleTextAlign; $titleTextFont">$shapeTitle</div>
  </div>
  <!-- <div class='a_rectangle_border'></div> -->
  <DIV ID="remove-$visID" CLASS="remove_icon" onClick="$visRemoveURL" STYLE="background-color:red; color:white; height:25px; width:25px; position:absolute; top:0px; right:0px; text-align:center;">X</DIV>
</DIV>

<SCRIPT>
var visWidthPct = $visWidthPct * 100 + "%";
var height = $visHeightPct * 100 + "%";
var x = $visXPct * 100 + "%";
var y = $visYPct * 100 + "%";
var zindex = $visZIndex;
END_HTML

      #don't make visualizations draggable/resizeable for PPT output
      if (($lockVisuals != 1) && ($acctType > 0))
      {
        print <<END_HTML;
\$(function()
{
  \$("#$visName").resizable(
  {
    autoHide: true,
    handles: "all",
    containment: "parent",
    stop: function(event, ui)
    {
      let widthPct = parseInt(\$(this).css("width")) / \$("#vis-container").innerWidth();
      let heightPct = parseInt(\$(this).css("height")) / \$("#vis-container").innerHeight();
      \$(this).css({ width: widthPct * 100 + "%", height: heightPct * 100 + "%"});
      koalaStoreSize("$visID", widthPct, heightPct);
    }
  });

  \$("#$visName").draggable(
  {
    containment: "parent",
    stop: function(event, ui)
    {
      let leftRelPos = parseInt(\$(this).css("left")) / \$("#vis-container").innerWidth();
      let topRelPos = parseInt(\$(this).css("top")) / \$("#vis-container").innerHeight();
      \$(this).css({left: leftRelPos * 100 + "%", top: topRelPos * 100 + "%"});
      koalaStorePos("$visID", leftRelPos, topRelPos, \$("td#vis-container").width(), \$("td#vis-container").height());
    }
  });

  \$("#$visName").click(function()
  {
    if(!\$(this).hasClass("a_rectangle_wrapper"))
    {
      if (\$(".ui-widget-content").length > 1)
      {
        let that = this;
        let highestZIdx = 0;
        \$(".ui-widget-content").each(function()
        {
          if (that != this)
          {
            let visZIdx = parseInt(\$(this).css("z-index"));
            if (Number.isInteger(visZIdx))
            {
              if (visZIdx > highestZIdx)
              {
                highestZIdx = visZIdx;
              }
            }
          }
        });
        \$(this).css("z-index", highestZIdx + 1);
        koalaStoreZIndex("$visID", highestZIdx + 1);
      }
    }
  });
});
END_HTML
      }

      print <<END_HTML
  \$("#$visName").css({top:y, left:x, width:visWidthPct, height:height, zIndex:zindex});
  \$("#remove-$visID").hide();

  document.getElementById("$visName").addEventListener("click", koalaSelRectangle, false);
</SCRIPT>
END_HTML
    }

    elsif ($visType eq "text")
    {
      $visName = "text-" . $visID;
      $visDesign =~ m/,content:\|\|\|(.*)\|\|\|,/;
      $textContent = $1;

      print <<END_HTML;
<DIV ID="$visName" STYLE="position:absolute;" CLASS="ui-widget-content">
  <DIV ID="text-data-$visID" STYLE="height:100%; overflow:auto;">
    $textContent
  </DIV>
  <DIV ID="remove-$visID" CLASS="remove_icon" onClick="$visRemoveURL" STYLE="background-color:red; color:white; height:25px; width:25px; position:absolute; top:0px; right:0px; text-align:center;">X</DIV>
</DIV>

<SCRIPT>
var visWidthPct = $visWidthPct * 100 + "%";
var height = $visHeightPct * 100 + "%";
var x = $visXPct * 100 + "%";
var y = $visYPct * 100 + "%";
var zindex = $visZIndex;
END_HTML

      #don't make visualizations draggable/resizeable for PPT output
      if (($lockVisuals != 1) && ($acctType > 0))
      {
        print <<END_HTML;
\$(function()
{
  \$("#$visName").resizable(
  {
    autoHide: true,
    handles: "all",
    containment: "parent",
    stop: function(event, ui)
    {
      let widthPct = parseInt(\$(this).css("width")) / \$("#vis-container").innerWidth();
      let heightPct = parseInt(\$(this).css("height")) / \$("#vis-container").innerHeight();
      \$(this).css({ width: widthPct * 100 + "%", height: heightPct * 100 + "%"});
      koalaStoreSize("$visID", widthPct, heightPct);
    }
  });

  \$("#$visName").draggable(
  {
    containment: "parent",
    stop: function(event, ui)
    {
      let leftRelPos = parseInt(\$(this).css("left")) / \$("#vis-container").innerWidth();
      let topRelPos = parseInt(\$(this).css("top")) / \$("#vis-container").innerHeight();
      \$(this).css({left: leftRelPos * 100 + "%", top: topRelPos * 100 + "%"});
      koalaStorePos("$visID", leftRelPos, topRelPos, \$("td#vis-container").width(), \$("td#vis-container").height());
    }
  });

  \$("#$visName").click(function()
  {
    if(\$(".ui-widget-content").length > 1)
    {
      let that = this;
      let highestZIdx = 0;
      \$(".ui-widget-content").each(function()
      {
        if (that != this)
        {
          let visZIdx = parseInt(\$(this).css("z-index"));
          if (Number.isInteger(visZIdx))
          {
            if (visZIdx > highestZIdx)
            {
              highestZIdx = visZIdx;
            }
          }
        }
      });
      \$(this).css("z-index", highestZIdx + 1);
      koalaStoreZIndex("$visID", highestZIdx + 1);
    }
  });
});
END_HTML
      }

      print <<END_HTML
  \$("#$visName").css({top:y, left:x, width:visWidthPct, height:height, zIndex:zindex});
  \$("#remove-$visID").hide();

  document.getElementById("$visName").addEventListener("click", koalaSelText, false);
</SCRIPT>
END_HTML
    }

    $idx++;
  }

  #if there aren't any visualizations yet for this report, output a how-to
  if (scalar(@visIDArray) == 0)
  {
    print <<END_HTML;
<DIV CLASS="alert alert-info alert-dismissible fade show" role="alert" STYLE="margin: 0 auto; margin-top:100px; width:50%;">
  Welcome to the Koala visualization interface! It looks like you haven't added any visualizations to this report yet - just click the Insert button above to create some.
  <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="alert"></BUTTON>
</DIV>

END_HTML
  }

  #place tabfooter outside of vis-container
  print <<END_HTML;
<!--splithere-->
  </TD>
 </TR>
</TABLE>
</DIV>
END_HTML

  #tab navigation footer
  if ($clean != 1)
  {
    print <<END_HTML;
<DIV ID="tabfooter">
 <UL CLASS="nav nav-tabs" role="tablist">
END_HTML

    %reports = cube_list($db, $userID, $acctType, $dsID);

    foreach $rptTabID (sort {$reports{$a} cmp $reports{$b}} keys %reports)
    {
      if ($rptTabID == $rptID)
      {
        $active = "CLASS='active'";
      }
      else
      {
        $active = "";
      }

      print("<LI role='presentation' $active><A id='display.cld?rpt=$rptTabID' class='tabfooter_item' role='tab'>$reports{$rptTabID}</A></LI>\n");
    }

    #let non-viewers create a copy of the current report to work with
    if ($acctType > 0)
    {
      print <<END_HTML;
  <LI role='presentation'><A HREF='newRptCopy.cld?ds=$dsID&rptID=$rptID' role='tab'><I CLASS="bi bi-plus-lg"></I></A></LI>
END_HTML
    }

    print <<END_HTML;
 </UL>
</DIV>
END_HTML
  }

  print_html_footer();

  $query = "INSERT INTO audit.stats_cubes (cubeID, year, month, viewsKoala) VALUES ($rptID, YEAR(NOW()), MONTH(NOW()), 1) ON DUPLICATE KEY UPDATE viewsKoala=viewsKoala+1";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);

  $dsName = ds_id_to_name($db, $dsID);
  $activity = "$first $last viewed report $rptName in $dsName";
  if ($local == 0)
  {
    utils_slack($activity);
  }


#EOF
