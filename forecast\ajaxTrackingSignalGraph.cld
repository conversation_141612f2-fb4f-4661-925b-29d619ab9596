#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;


#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/plain\n\n");

  #get the CGI input variables
  $fcID = $q->param('fcID');
  $product = $q->param('prod');
  $geography = $q->param('geo');

  $db = KAPutil_connect_to_database();

  #get the list of forecast time periods from the database
  $query = "SELECT dsID, timeperiods FROM analytics.forecasts WHERE ID=$fcID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;

  ($dsID, $timeIDstring) = $dbOutput->fetchrow_array;

  #assemble forecast cube names
  $dsSchema = "datasource_" . $dsID;
  $fcCube = "_fcastcube_" . $fcID;
  $fcMeta = "_fcast_" . $fcID;

  #convert the time ID strings into arrays
  @timeIDs = split(/,/, $timeIDstring);

  #get displayable names for our DSR dimensions
  %timeperiodNames = dsr_get_item_name_hash($db, $dsSchema, "t");

  #trim down the time names to maximize the graph area
  while(($key, $value) = each(%timeperiodNames))
  {
    if ($value =~ m/(.*?) Weeks Ending (.*)/i)
    {
      $timeperiodNames{$key} = "$1 WE $2";
    }
  }

  if (scalar(@timeIDs) > 52)
  {
    $labelStep = 8
  }
  elsif (scalar(@timeIDs) > 12)
  {
    $labelStep = 4;
  }
  else
  {
    $labelStep = 1;
  }

  #print out the JSON chart info
  $jsonData = <<JSON_LABEL;
{
  "chart":
  {
    "theme": "zune",
    "animation": "0",
    "caption": "Tracking Signals",
    "captionfontcolor": "#333333",
    "captionfontsize": 24,
    "yaxisname": "Tracking Signal",
    "yaxisnamefontsize": "14",
    "numvdivlines": "10",
    "divlinealpha": "30",
    "labelpadding": "10",
    "labelstep": "$labelStep",
    "labelfontsize": "14",
    "labeldisplay": "rotate",
    "slantlabel": "1",
    "useEllipsesWhenOverflow": "0",
    "yaxisvaluespadding": "10",
    "showlegend": "0",
    "showvalues": "0"
  },
JSON_LABEL

  #output categories (X axis values)
  $jsonData .= <<JSON_LABEL;
  "categories": [
    {
      "category": [
JSON_LABEL

  foreach $time (@timeIDs)
  {
    $jsonData .= <<JSON_LABEL;
        {
          "label": "$timeperiodNames{$time}"
        },
JSON_LABEL
  }
  chop($jsonData);  chop($jsonData);

  $jsonData .= <<JSON_LABEL;
      ]
    }
  ],
JSON_LABEL

  #output the tracking signals data set
  $jsonData .= <<JSON_LABEL;
  "dataset": [
  {
    "seriesname": "Tracking Signal",
    "color": "A66EDD",
    "data": [
JSON_LABEL

  foreach $time (@timeIDs)
  {
    $query = "SELECT trackingsignal FROM $dsSchema.$fcCube \
        WHERE time=$time AND geography=$geography AND product=$product";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    ($value) = $dbOutput->fetchrow_array;
    $jsonData .= "{ \"value\": \"$value\"},\n";
  }
  chop($jsonData);  chop($jsonData);

  $jsonData .= <<JSON_LABEL;
    ]
  }]
}
JSON_LABEL

  print $jsonData;


#EOF
