#!/usr/bin/perl

###############################################################################
#
# Searches for and coalesces any items with duplicate UPCs in the specified
# data source.
#
###############################################################################

use lib "/opt/apache/app/";

use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;



$DSID = 1234;

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsSchema = "datasource_" . $DSID;

  #get hash of UPCs
  %upcHash = DSRattr_get_values_hash($db, $dsSchema, "p", 1);

  #find products with duplicate UPCs
  foreach $itemID (keys %upcHash)
  {
    $upc = $upcHash{$itemID};
    $dupeHash{$upc}++;
  }

  #for every duplicate UPC
  foreach $upc (keys %dupeHash)
  {

    #only work with UPCs that have multiple entries
    if ($dupeHash{$upc} < 2)
    {
      next;
    }

    #get the products that match this UPC
    $query = "SELECT itemID FROM $dsSchema.product_attribute_values \
        WHERE value='$upc' ORDER BY itemID ASC";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;

    #get the IDs of the parent and child items
    $parentItem = $dbOutput->fetchrow_array;
    $childItem = $dbOutput->fetchrow_array;

    print "$childItem -> $parentItem\n";

    $query = "UPDATE $dsSchema.facts SET productID=$parentItem \
        WHERE productID=$childItem";
    $db->do($query);

    $query = "DELETE FROM $dsSchema.facts WHERE productID=$childItem";
    $db->do($query);

    $query = "DELETE FROM $dsSchema.products WHERE ID=$childItem";
    $db->do($query);

    $query = "DELETE FROM $dsSchema.product_segment_item WHERE itemID=$childItem";
    $db->do($query);

    $query = "DELETE FROM $dsSchema.product_attribute_values WHERE itemID=$childItem";
    $db->do($query);
  }

  $query = "UPDATE app.dataSources SET lastModified=NOW() WHERE ID=$DSID";
  $db->do($query);
