#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  #output Content-type header
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  #output HTML
  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Check Data Source</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<STYLE>
.accordion-button.collapsed {
  background: blue
}

.accordion-button.collapsed::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}
</STYLE>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Check Data Source</LI>
  </OL>
</NAV>

<P>

END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');

  #sanitize parameters
  $dsID = utils_sanitize_integer($dsID);

  $dsSchema = "datasource_" . $dsID;

  #connect to the database
  $db = KAPutil_connect_to_database();

  #get the name of the data source
  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  #make sure we have reaad privs for this data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to check this data source.");
  }

  print <<END_HTML;
  <DIV CLASS="container">

    <DIV CLASS="row">

      <DIV CLASS="col"></DIV>  <!-- spacing -->

      <DIV CLASS="col-xs-12 col-sm-12 col-md-12 col-lg-10 col-xl-8"> <!-- content -->

        <DIV CLASS="accordion mx-auto" ID="accordion">
          <DIV CLASS="accordion-item border-primary">
            <H2 CLASS="accordion-header">
              <BUTTON CLASS="accordion-button bg-primary text-white icon-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
                Data Holes
              </BUTTON>
            </H2>
            <DIV ID="collapse1" CLASS="accordion-collapse collapse show" data-bs-parent="#accordion">
              <DIV CLASS="accordion-body">

                Below is a list of all Geography and Time Period combinations that contain no measure data in the $dsName data source.
                <P>
                <DIV CLASS="table-responsive">
                  <TABLE CLASS="table table-bordered table-sm table-striped">
                    <THEAD><TR>
                      <TH>Geographies</TH>
                      <TH>Time Periods</TH>
                    </TR></THEAD>
END_HTML

  #grab the hashes containing every possible geo and time in the DS
  %prodHash = dsr_get_base_item_name_hash($db, $dsSchema, "p");
  %geoHash = dsr_get_base_item_name_hash($db, $dsSchema, "g");
  %timeHash = dsr_get_base_item_name_hash($db, $dsSchema, "t");

  #make a hash of every geo/time combo in the data source's facts table
  $query = "SELECT DISTINCT geographyID, timeID FROM $dsSchema.facts";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  undef(%factsHash);
  while (($geoID, $timeID) = $dbOutput->fetchrow_array)
  {
    $key = "$geoID-$timeID";
    $factsHash{$key} = 1;
  }

  #get an in-order array of our time periods
  #NB: The only reason we're doing this is so we can have the database sort
  #    the dates for us for human-friendly display purposes. Otherwise, we
  #    could just use the timeHash
  undef(@timeArr);
  $query = "SELECT ID FROM $dsSchema.timeperiods ORDER BY type, duration, endDate";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  while (($id) = $dbOutput->fetchrow_array)
  {
    push(@timeArr, $id);
  }

  #run through every possible geo/time combo, and output the combos that don't
  #have matching entries in the facts table
  foreach $geoID (sort {$geoHash{$a} cmp $geoHash{$b}} keys %geoHash)
  {
    foreach $timeID (@timeArr)
    {
      $key = "$geoID-$timeID";
      if ($factsHash{$key} != 1)
      {
        print <<END_HTML;
                    <TR>
                      <TD>$geoHash{$geoID}</TD>
                      <TD>$timeHash{$timeID}</TD>
                    </TR>
END_HTML
      }
    }
  }

  print <<END_HTML;
                  </TABLE>
                </DIV>
              </DIV>
            </DIV>
          </DIV>

          <DIV CLASS="accordion-item border-primary">
            <H2 CLASS="accordion-header">
              <BUTTON CLASS="accordion-button collapsed bg-primary text-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
                Undefined Aggregation Rules
              </BUTTON>
            </H2>
            <DIV ID="collapse2" CLASS="accordion-collapse collapse" data-bs-parent="#accordion">
              <DIV CLASS="accordion-body">

                <P>
                The following measures need to have aggregation rules defined before they can be used in structures like aggregates and segmentations. Click on the measure name to edit its properties and set its aggregation rules.
                </P>
END_HTML

  #grab a list of all measures without fully defined agg rules from the DS
  $query = "SELECT ID, name, prodAggRule, geoAggRule, timeAggRule \
      FROM $dsSchema.measures WHERE calculation IS NULL ORDER BY name";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  #output an "edit item" link for every measure with undefined agg rules
  while (($id, $name, $prodAggRule, $geoAggRule, $timeAggRule) = $dbOutput->fetchrow_array)
  {
    if ((length($prodAggRule) < 1) || (length($geoAggRule) < 1) ||
        (length($timeAggRule) < 1))
    {
      print("<A CLASS='text-decoration-none' TARGET='_blank' HREF='editItem.cld?ds=$dsID&dim=m&item=$id'>$name <I CLASS='bi bi-box-arrow-up-right'></I></A><BR>\n");
    }
  }

  print <<END_HTML;
              </DIV>
            </DIV>
          </DIV>

          <DIV CLASS="accordion-item border-primary">
            <H2 CLASS="accordion-header">
              <BUTTON CLASS="accordion-button collapsed bg-primary text-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse3">
                Unsegmented Items
              </BUTTON>
            </H2>
            <DIV ID="collapse3" CLASS="accordion-collapse collapse" data-bs-parent="#accordion">
              <DIV CLASS="accordion-body">

                <P>
                The following segmentations contain unsegmented items. Click on the segmentation name to assign items to segments.
                </P>
END_HTML

  #grab a hash of every product segmentation in the data source
  %segHash = DSRsegmentation_get_segmentations_hash($db, $dsSchema, "p");

  #run through the segmentation hash, writing out every segmentation with
  #unsegmented items
  foreach $segID (sort {$segHash{$a} cmp $segHash{$b}} keys %segHash)
  {
    @unsegmented = DSRseg_get_unsegmented_items($db, $dsSchema, "p", $segID);
    $count = @unsegmented;

    if ($count > 0)
    {
      print("<A CLASS='text-decoration-none' TARGET='_blank' HREF='segmentAssign.cld?ds=$dsID&dim=p&segName=$segHash{$segID}&seg=$segID'>$segHash{$segID} <I CLASS='bi bi-box-arrow-up-right'></I></A> ($count unsegmented items)<BR>\n");
    }
  }

  print <<END_HTML;
              </DIV>
            </DIV>
          </DIV>

          <DIV CLASS="accordion-item border-primary">
            <H2 CLASS="accordion-header">
              <BUTTON CLASS="accordion-button collapsed bg-primary text-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse4">
                Data Consistency
              </BUTTON>
            </H2>
            <DIV ID="collapse4" CLASS="accordion-collapse collapse" data-bs-parent="#accordion">
              <DIV CLASS="accordion-body">

                <P>
                Products with duplicate names:
                </P>

                <TABLE CLASS="table table-sm table-striped">
END_HTML

  #hunt for products with duplicate names
  $firstDupe = 1;
  undef(%seen);
  foreach $prodID (keys %prodHash)
  {
    $name = $prodHash{$prodID};
    $seen{$name} = $seen{$name} + 1;
  }
  foreach $tmp (keys %seen)
  {
    if ($seen{$tmp} < 2)
    {
      delete($seen{$tmp});
    }
  }

  #if we found products with duplicate names, make sure they're really dupes
  #and not just merged item child/parent sets
  if ((keys %seen) > 0)
  {
    $query = "SELECT name FROM $dsSchema.products WHERE merged > 0";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($name) = $dbOutput->fetchrow_array)
    {
      delete($seen{$name});
    }
  }

  #output the duplicate products, if we found any
  foreach $tmp (keys %seen)
  {
    if ($firstDupe)
    {
      print("<TR><TD>$tmp</TD>\n");
      print("<TD><BUTTON CLASS='btn btn-warning' onClick=\"location.href='fixDS.cld?ds=$dsID&dim=p&a=dedupeprod'\"><I CLASS='bi bi-wrench'></I> Fix All</BUTTON></TD></TR>\n");
      $firstDupe = 0;
    }
    else
    {
      print("<TR><TD>$tmp</TD><TD></TD></TR>\n");
    }
  }

  if (scalar(%seen) < 1)
  {
    print("<TR><TD><EM>None</EM></TD></TR>\n");
  }

  print <<END_HTML;
                </TABLE>

                <P>
                Geographies with duplicate names:
                </P>

                <TABLE CLASS="table table-sm table-striped">
END_HTML

  #hunt for geographies with duplicate names
  undef(%seen);
  foreach $geoID (keys %geoHash)
  {
    $name = $geoHash{$geoID};
    $seen{$name} = $seen{$name} + 1;
  }
  foreach $tmp (keys %seen)
  {
    if ($seen{$tmp} < 2)
    {
      delete($seen{$tmp});
    }
  }

  foreach $tmp (keys %seen)
  {
    print("<TR><TD>$tmp</TD></TR>\n");
  }

  if (scalar(%seen) < 1)
  {
    print("<TR><TD><EM>None</EM></TD></TR>\n");
  }

  print <<END_HTML;
                </TABLE>

                <P>
                Time periods with duplicate names:
                </P>

                <TABLE CLASS="table table-sm table-striped">
END_HTML

  #hunt for time periods with duplicate names
  undef(%seen);
  foreach $timeID (keys %timeHash)
  {
    $name = $timeHash{$timeID};
    $seen{$name}++;
  }
  foreach $tmp (keys %seen)
  {
    if ($seen{$tmp} < 2)
    {
      delete($seen{$tmp});
    }
  }

  foreach $tmp (keys %seen)
  {
    print("<TR><TD>$tmp</TD></TR>\n");
  }

  if (scalar(%seen) < 1)
  {
    print("<TR><TD><EM>None</EM></TD></TR>\n");
  }

  print <<END_HTML;
                </TABLE>

              </DIV>
            </DIV>
          </DIV>

        </DIV>

        <P>

        <DIV CLASS="text-center">
          <BUTTON CLASS="btn btn-primary" onClick="location.href='display.cld?ds=$dsID'"><I CLASS="bi bi-check-lg"></I> Done</BUTTON>
        </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

  $activity = "$first $last running data source check against $dsName";
  utils_audit($db, $userID, "Ran data source check", $dsID, 0, 0);
  utils_slack($activity);

#EOF
