#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  #output Content-type header
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  #output HTML
  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Copy Data Source</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Copy Data Source</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');
  $name = $q->param('name');
  $copyReports = $q->param('copyReports');
  $refLinkCode = $q->param('r');

  if ($refLinkCode eq "m")
  {
    $refLink = "main.cld?ds=$dsID";
  }
  else
  {
    $refLink = "display.cld?ds=$dsID";
  }

  #sanitize parameters
  $dsID = utils_sanitize_integer($dsID);
  $name = utils_sanitize_string($name);

  $dsSchema = "datasource_" . $dsID;

  #connect to the database
  $db = KAPutil_connect_to_database();

  #get the name of the data source
  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  #make sure we have read privs for this data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to copy this data source.");
  }

  utils_audit($db, $userID, "Copied data source to $name", $dsID, 0, 0);
  $activity = "$first $last created a copy of data source $dsName";
  utils_slack($activity);

  #split the copy process off into a background process
  $SIG{CHLD} = "IGNORE";
  if ($pid = fork())
  {
    #parent process - we're just going to finish up our display script
    print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-10 col-lg-6 col-xl-6"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Copy Data Source</DIV>
        <DIV CLASS="card-body">

          The data source $name is being created in the background for you.

          <P>&nbsp;<P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" onClick="location.href='$refLink'"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

    print_html_footer();
  }

  else
  {
    #child process - do the actual calculation

    #we're in a new process, so we need a new connection to the database
    $db = KAPutil_connect_to_database();

    #redirect STDERR to the Koala error log
    close(STDERR);
    open(STDERR, ">>/opt/apache/htdocs/tmp/koala_error.log");

    #let Apache know not to wait on the child process
    close(STDIN);
    close(STDOUT);

    #start by creating a new entry in the dataSources table
    $q_name = $db->quote($name);
    $query = "INSERT INTO app.dataSources (userID, name) VALUES ($userID, $q_name)";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    #get our new data source's unique ID
    $newDSID = $db->{q{mysql_insertid}};
    $newSchema = "datasource_" . $newDSID;

    #set our initial state in the jobs table
    KAPutil_job_store_status($db, $userID, $newDSID, 0, "COPY-DS",
        "Copying into this data source");

    #fill out the rest of new data source's table entry
    $query = "SELECT type, description, timeAlias FROM dataSources WHERE ID=$dsID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($type, $description, $timeAlias) = $dbOutput->fetchrow_array;

    $q_type = $db->quote($type);
    $q_description = $db->quote($description);
    $q_timeAlias = $db->quote($timeAlias);
    $query = "UPDATE dataSources \
        SET type=$q_type, description=$q_description, timeAlias=$q_timeAlias, lastUpdate=NOW(), lastModified=NOW() \
        WHERE ID=$newDSID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    #next, dump out the data source contents to disk
    KAPutil_job_update_status($db, "Extracting source data");
    `/usr/bin/mysqldump -h$Lib::KoalaConfig::dbServerName -u app -p$Lib::KoalaConfig::password -e $dsSchema --ignore-table=$dbName.export > /opt/apache/app/logs/copy_$userID.$dsSchema.sql`;

    #create the new database schema
    $query = "CREATE DATABASE $newSchema";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    #import the source data into the new data source
    KAPutil_job_update_status($db, "Loading data into new data source");
    `/usr/bin/mysql -h$Lib::KoalaConfig::dbServerName -u app -p$Lib::KoalaConfig::password $newSchema < /opt/apache/app/logs/copy_$userID.$dsSchema.sql`;

    #delete the data source dump file from disk
    unlink("/opt/apache/app/logs/copy_$userID.$dsSchema.sql");

    #if the user wants us to copy the DS's reports as well
    if ($copyReports eq "on")
    {
      KAPutil_job_update_status($db, "Copying reports");

      #get the list of cube table column names for use in INSERT statement
      $query = "SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS \
          WHERE TABLE_SCHEMA='app' AND TABLE_NAME='cubes'";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      KAPutil_handle_db_err($db, $status, $query);
      ($colName) = $dbOutput->fetchrow_array;
      $cubeColNames = "";
      while (defined($colName))
      {
        $cubeColNames .= "$colName,";
        ($colName) = $dbOutput->fetchrow_array;
      }
      chop($cubeColNames);

      #get the list of report table column names for use in INSERT statement
      $query = "SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS \
          WHERE TABLE_SCHEMA='app' AND TABLE_NAME='visuals'";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      KAPutil_handle_db_err($db, $status, $query);
      ($colName) = $dbOutput->fetchrow_array;
      $rptColNames = "";
      while (defined($colName))
      {
        $rptColNames .= "$colName,";
        ($colName) = $dbOutput->fetchrow_array;
      }
      chop($rptColNames);

      $query = "SELECT * FROM cubes WHERE dsID = $dsID";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      KAPutil_handle_db_err($db, $status, $query);
      @cubeRow = $dbOutput->fetchrow_array;

      #for each cube in the data source
      while (defined($cubeRow[0]))
      {

        #set unique ID to NULL so DB will fill it in
        $oldCubeID = $cubeRow[0];
        undef($cubeRow[0]);

        #use our new data source ID
        $cubeRow[1] = $newDSID;

        #use our current user ID
        $cubeRow[2] = $userID;

        #quote each value to prep it for DB insert
        $cubeValues = "";
        foreach $str (@cubeRow)
        {
          $str = $db->quote($str);
          $cubeValues = $cubeValues . "$str,";
        }
        chop($cubeValues);

        #insert the new cube's row and get its unique ID
        $query = "INSERT INTO app.cubes ($cubeColNames) VALUES ($cubeValues)";
        $status = $db->do($query);
        KAPutil_handle_db_err($db, $status, $query);
        $newCubeID = $db->{q{mysql_insertid}};

        #rename the cube we copied so it uses its new ID
        $query = "ALTER TABLE $newSchema._rptcube_$oldCubeID \
            RENAME $newSchema._rptcube_$newCubeID";
        $status = $db->do($query);
        KAPutil_handle_db_err($db, $status, $query);

        #grab all of the report rows that depend on this cube
        $query = "SELECT * FROM visuals WHERE cubeID = $oldCubeID";
        $dbOutput1 = $db->prepare($query);
        $status = $dbOutput1->execute;
        KAPutil_handle_db_err($db, $status, $query);
        @rptRow = $dbOutput1->fetchrow_array;

        while (defined($rptRow[0]))
        {

          #set unique ID to NULL so DB will fill it in
          undef($rptRow[0]);

          #use our new cube ID
          $rptRow[1] = $newCubeID;

          #use our new DS ID
          $rptRow[2] = $newDSID;

          #quote each value to prep it for DB insert
          $rptValues = "";
          foreach $str (@rptRow)
          {
            $str = $db->quote($str);
            $rptValues = $rptValues . "$str,";
          }
          chop($rptValues);

          #insert the new report row
          $query = "INSERT INTO app.visuals ($rptColNames) VALUES ($rptValues)";
          $status = $db->do($query);
          KAPutil_handle_db_err($db, $status, $query);

          @rptRow = $dbOutput1->fetchrow_array;
        }
        @cubeRow = $dbOutput->fetchrow_array;
      }
    }

    #else remove the dumped cubes
    else
    {
      $query = "SELECT ID FROM cubes WHERE dsID = $dsID";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      KAPutil_handle_db_err($db, $status, $query);
      ($cubeID) = $dbOutput->fetchrow_array;

      while (($cubeID) = $dbOutput->fetchrow_array)
      {
        $cubeName = "_rptcube_" . $cubeID;
        KAPutil_db_delete_table($db, $newSchema, $cubeName);
      }
    }

    #remove this task from the jobs table
    DSRutil_clear_status($db);

    utils_audit($db, $userID, "Copied data source from $dsName", $newDSID, 0, 0);
  }


#EOF
