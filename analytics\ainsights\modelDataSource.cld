#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::AInsights::AInsights;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: AInsights Model Name & Data Source</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Please Wait...');
  \$('#ds').removeAttr('disabled');
  return(true);
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;

<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">AInsights</A></LI>
    <LI CLASS="breadcrumb-item active">$actionHR</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $priceModelID = $q->param('pm');
  $action = $q->param('a');

  #connect to the database
  $db = KAPutil_connect_to_database();

  #if we're editing an existing model
  if ($priceModelID > 0)
  {
    $query = "SELECT name, description, dsID FROM analytics.pricing WHERE ID=$priceModelID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($modelName, $modelDesc, $dsID) = $dbOutput->fetchrow_array;

    $actionHR = "Editing Model $modelName";
    $dsSelectEdit = "DISABLED";
  }
  else
  {
    $actionHR = "New Model";
  }

  print_html_header();

  #make sure we have write privs for this pricing model
  $privs = AInsights_rights($db, $userID, $priceModelID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this pricing model.");
  }

  @userSources = ds_list($db, $userID, $acctType, "R");

  %dsNames = ds_get_name_hash($db);

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-11 col-lg-8 col-xl-7"> <!-- content -->

      <FORM METHOD="post" ACTION="modelItems.cld" onsubmit="return checkForm(this);">
      <INPUT TYPE="hidden" NAME="pm" VALUE="$priceModelID">
      <INPUT TYPE="hidden" NAME="a" VALUE="$action">

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Model Name & Data Source</DIV>
        <DIV CLASS="card-body">

          <P>
          AInsights Model Name:
          <INPUT CLASS="form-control" TYPE="text" NAME="name" ID="name" VALUE="$modelName" required>

          <P>
          Description:
          <INPUT CLASS="form-control" TYPE="text" NAME="desc" ID="desc" VALUE="$modelDesc">

          <P>&nbsp;</P>
          Data Source:
          <P>
          <SELECT required CLASS="form-select" NAME="ds" ID="ds" data-bs-width="auto" $dsSelectEdit required>
END_HTML

  foreach $dsID (@userSources)
  {
    print("<OPTION VALUE=\"$dsID\">$dsNames{$dsID}</OPTION>\n");
  }

  print <<END_HTML;
          </SELECT>
          <SCRIPT>
            \$('select#ds').val('$dsID');
          </SCRIPT>

          <P>&nbsp;</P>

          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onclick="location.href='main.cld'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="submit" ID="btn-submit">Next <I CLASS="bi bi-arrow-right"></I></BUTTON>
          </DIV>

        </DIV>
      </DIV>

      </FORM>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
