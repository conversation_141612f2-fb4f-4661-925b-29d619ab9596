#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName Data Sources</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jsgrid/jsgrid.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<LINK HREF="/jsgrid/jsgrid.min.css" REL="stylesheet">
<LINK HREF="/jsgrid/jsgrid-theme.min.css" REL="stylesheet">

<STYLE>
div.grid th
{
  background: #e2e3e5 !important;
}

.selected-row > td
{
  background: #c4e2ff !important;
  border-color: #c4e2ff;
}
</STYLE>

<SCRIPT>
//global
selectedDS = 0;

let gridHeight = window.innerHeight - 275;


\$(document).ready(function()
{

  \$("#dsGrid").jsGrid(
  {
    width: "900px",
    height: gridHeight,
    sorting: true,
    autoload: true,
    loadIndication: true,

    controller:
    {
      loadData: function (filter)
      {
        let data = \$.Deferred();
        \$.ajax(
        {
          type: "GET",
          contentType: "application/json; charset=utf-8",
          url: "ajaxDataSourceList.cld?r=$dispRpt&a=$admin",
          dataType: "json"
        }).done(function(response)
        {
         data.resolve(response);
        });
        return data.promise();
      }
    },

    rowClick: function(args)
    {
      selectedDS = args.item.ID;

      \$("#dsGrid tr").removeClass("selected-row");

      \$selectedRow = \$(args.event.target).closest("tr");
      \$selectedRow.addClass("selected-row");
    },

    rowDoubleClick: function(args)
    {
      selectedDS = args.item.ID;

      location.href='/app/dsr/display.cld?ds=' + selectedDS;
    },

    onDataLoaded: function(args)
    {
      \$("#dsGrid").jsGrid("sort", {field: "$gridSortField", order: "$gridSortOrder"});
    },

    $gridFields
  });
});


function change_report()
{
  let newReport = document.getElementById('rpt').value;
  let newURL = "?rpt=" + newReport + "&a=" + $admin;

  location.href = newURL;
}

</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item">
END_HTML

  #if we came in via user settings rather than administration
  if ($admin < 1)
  {
    $returnLink = "/app/admin/userSettings.cld";
    print("<A CLASS='text-decoration-none' HREF='/app/admin/userSettings.cld'>User Settings</A>");
  }
  else
  {
    $returnLink = "/app/admin/home.cld";
    print("<A CLASS='text-decoration-none' HREF='/app/admin/home.cld'>Administration</A>");
  }

  print <<END_HTML;
    </LI>
    <LI CLASS="breadcrumb-item active">Data Source Management</LI>
  </OL>
</NAV>

  <P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dispRpt = $q->param('rpt');
  $admin = $q->param('a');

  #by default, display no update in 3 months report
  if (length($dispRpt) < 1)
  {
    $dispRpt = "noupdate3";
  }

  #determine if we came in via user settings or admin page
  if ($admin < 1)
  {
    if ($ENV{HTTP_REFERER} =~ m/admin\/home.cld/)
    {
      $admin = 1;
    }
    else
    {
      $admin = 0;
    }
  }

  #set up our grid display fields depending on the mgmt rpt being shown
  if ($dispRpt eq "size")
  {
    $gridSortField = "Total Size";
    $gridSortOrder = "desc";
    $gridFields = "  fields: [ \
      {name: 'ID', type: 'number', visible: false}, \
      {name: 'Name', type: 'text', width: 275}, \
      {name: 'Owner', type: 'text', width:125}, \
      {name: 'Total Size (MB)', type: 'number', width:100}, \
      {name: 'Data Size (MB)', type: 'number', width:100}, \
      {name: 'Reports Size (MB)', type: 'number', width:100}, \
      {name: 'ODBC Size (MB)', type: 'number', width:100}, \
    ]\n";
  }

  elsif ($dispRpt eq "unused")
  {
    $gridSortField = "Name";
    $gridSortOrder = "asc";
    $gridFields = "  fields: [ \
      {name: 'ID', type: 'number', visible: false}, \
      {name: 'Name', type: 'text', width: 275}, \
      {name: 'Type', type: 'text', width: 75}, \
      {name: 'Owner', type: 'text', width:125}, \
      {name: 'Last Update', type: 'text', width:125}, \
      {name: 'Reports', type: 'text', width:75}, \
      {name: 'Exported', type: 'text', width:50}, \
      {name: 'Size (MB)', type: 'number', width:100}
    ]\n";
  }

  elsif ($dispRpt eq "noupdate3")
  {
    $gridSortField = "Last Update";
    $gridSortOrder = "asc";
    $gridFields = "  fields: [ \
      {name: 'ID', type: 'number', visible: false}, \
      {name: 'Name', type: 'text', width: 275}, \
      {name: 'Type', type: 'text', width: 75}, \
      {name: 'Owner', type: 'text', width:125}, \
      {name: 'Last Update', type: 'text', width:125}, \
      {name: 'Last Modified', type: 'text', width:125}, \
      {name: 'Size (MB)', type: 'number', width:100}
    ]\n";
  }

  #connect to user login database
  $db = KAPutil_connect_to_database();

  print_html_header();

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col">

      <SELECT CLASS="form-select mx-auto w-50" ID="rpt" onChange="change_report()">
        <OPTION VALUE="noupdate3">Data Sources That Haven't Been Updated in 3 Months</OPTION>
        <OPTION VALUE="unused">Unused Data Sources</OPTION>
        <OPTION VALUE="size">Data Source Size</OPTION>
      </SELECT>
      <SCRIPT>
        \$("select#rpt").val("$dispRpt");
      </SCRIPT>

      <P>
      <DIV ID="dsGrid" CLASS="grid" STYLE="margin:auto; font-size:13px;"></DIV>

      <P>
      <DIV CLASS="text-center">
        <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='$returnLink'"><I CLASS="bi bi-check-lg"></I> Done</BUTTON>
      </DIV>

    </DIV>  <!-- content -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
