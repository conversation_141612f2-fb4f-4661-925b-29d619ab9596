#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Edit Item</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Edit $dimName</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('dsID');
  $dim = $q->param('dim');
  $itemID = $q->param('item');
  $name = $q->param('name');
  $alias = $q->param('alias');

  $type = $q->param('type');
  $duration = $q->param('duration');
  $endDate = $q->param('endDate');

  $prodAggRule = $q->param('prodAggRule');
  $geoAggRule = $q->param('geoAggRule');
  $timeAggRule = $q->param('timeAggRule');
  $mergeAggRule = $q->param('mergeAggRule');

  $formatCategory = $q->param('fcat');
  $formatDecimal = $q->param('fdec');
  $formatSeparator = $q->param('fsep');
  $formatNegative = $q->param('fneg');

  #sanitize parameters
  $dsID = utils_sanitize_integer($dsID);
  $dim = utils_sanitize_dim($dim);
  $itemID = utils_sanitize_string($itemID);
  $name = utils_sanitize_string($name);
  $alias = utils_sanitize_string($alias);

  $formatSeparator = ($formatSeparator eq "on") ? "1" : "0";
  if ($formatCategory eq "number")
  {
    $formatCategory = "0";
  }
  elsif ($formatCategory eq "currency")
  {
    $formatCategory = "1";
  }
  elsif ($formatCategory eq "percentage")
  {
    $formatCategory = "2";
  }

  #run through the list of CGI parameters, extracting attributes (prepended with
  #"ATTR " to identify them
  undef(%attrHash);
  @names = $q->param;
  foreach $name (@names)
  {
    if ($name =~ m/^ATTR (.*)/)
    {
      $attributeID = $1;
      $value = $q->param($name);
      $attrHash{$attributeID} = $value;
    }
  }

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsSchema = "datasource_" . $dsID;
  $dsName = ds_id_to_name($db, $dsID);

  $structDB = KAPutil_get_dim_stub_name($dim);
  $dimDB = KAPutil_get_dim_db_name($dim);
  $dimName = KAPutil_get_dim_name_singular($dim, 1);

  $q_name = $db->quote($name);

  if (length($alias) < 1)
  {
    $q_alias = "NULL";
  }
  else
  {
    $q_alias = $db->quote($alias);
  }

  print_html_header();

  #make sure we have write privs for the data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to modify the data source.");
  }

  %itemNameHash = dsr_get_item_name_hash($db, $dsSchema, $dim);

  utils_audit($db, $userID, "Edited item $itemNameHash{$itemID}", $dsID, 0, 0);
  $activity = "$first $last edited item $itemNameHash{$itemID} in $dsName";

  #update name and alias
  $query = "UPDATE $dsSchema.$dimDB \
      SET name=$q_name, alias=$q_alias WHERE ID=$itemID";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);

  #if we're dealing with a time period, update time-specific values
  if (defined($duration))
  {
    $q_endDate = $db->quote($endDate);
    $query = "UPDATE $dsSchema.$dimDB \
        SET type=$type, duration=$duration, endDate='$endDate' WHERE ID=$itemID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);
  }

  #if we're dealing with a measure, update measure-specific values
  if (defined($prodAggRule))
  {
    $q_prodAggRule = $db->quote($prodAggRule);
    $q_geoAggRule = $db->quote($geoAggRule);
    $q_timeAggRule = $db->quote($timeAggRule);

    $query = "UPDATE $dsSchema.measures \
        SET prodAggRule=$q_prodAggRule, geoAggRule=$q_geoAggRule, timeAggRule=$q_timeAggRule \
        WHERE ID=$itemID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);
  }

  if (defined($mergeAggRule))
  {
    $q_mergeAggRule = $db->quote($mergeAggRule);

    $query = "UPDATE $dsSchema.measures \
        SET mergeAggRule=$q_mergeAggRule WHERE ID=$itemID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);
  }

  if (defined($formatDecimal))
  {
    $formatStr = "$formatDecimal,$formatSeparator,$formatCategory,$formatNegative";
    $q_formatStr = $db->quote($formatStr);
    $query = "UPDATE $dsSchema.measures SET format=$q_formatStr WHERE ID=$itemID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);
  }

  #update attributes
  $dbName = $structDB . "attribute_values";
  foreach $attribute (keys %attrHash)
  {
    $q_value = $db->quote($attrHash{$attribute});
    $query = "INSERT INTO $dsSchema.$dbName (attributeID, itemID, value) \
        VALUES ($attribute, $itemID, $q_value) ON DUPLICATE KEY UPDATE value=$q_value";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);
  }

  #update segmentation memberships
  $dbName = $structDB . "segment_item";
  @names = $q->param;
  foreach $name (@names)
  {
    if ($name =~ m/SEG_(\d+)/)
    {
      $segID = $1;

      $segmentID = $q->param($name);
      if ($segmentID =~ m/SMT_(\d+)/)
      {
        $segmentID = $1;
      }

      #if the item is assigned a segment in this segmentation
      if ($segmentID > 0)
      {
        $query = "INSERT INTO $dsSchema.$dbName \
            (segmentationID, segmentID, itemID) \
            VALUES ($segID, $segmentID, $itemID) \
            ON DUPLICATE KEY UPDATE segmentationID=$segID, segmentID=$segmentID";
        $status = $db->do($query);
        KAPutil_handle_db_err($db, $status, $query);
      }
    }
  }

  #update data source's lastModified datestamp
  $query = "UPDATE dataSources SET lastModified=NOW() WHERE ID=$dsID";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-10 col-lg-7 col-xl-6"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Edit $dimName</DIV>
        <DIV CLASS="card-body">

          Changes to $name have been saved.

          <P>&nbsp;<P>

          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='display.cld?ds=$dsID&dim=$dim&path=$itemID'"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

  utils_slack($activity);


#EOF
