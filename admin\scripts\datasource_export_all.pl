#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;



  chdir("/opt/apache/htdocs/tmp/");

  #connect to the database
  $db = KAPutil_connect_to_database();

  #get hash of all users on the system
  %userNames = utils_get_user_hash($db);

  #get a hash of all data sources on the system
  %dataSources = ds_get_name_hash($db);

  #export each data source
  foreach $dsID (keys %dataSources)
  {
    $dsName = ds_id_to_name($db, $dsID);
    $dsSchema = "datasource_" . $dsID;

    print STDERR "Exporting $dsName ($dsID)\n";

    #get the data source's owner
    $query = "SELECT userID FROM dataSources WHERE ID=$dsID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($userID) = $dbOutput->fetchrow_array;

    #get the item names for all dimensions
    %prodNameHash = dsr_get_item_name_hash($db, $dsSchema, "p");
    %geoNameHash = dsr_get_item_name_hash($db, $dsSchema, "g");
    %timeNameHash = dsr_get_item_name_hash($db, $dsSchema, "t");
    %measNameHash = dsr_get_item_name_hash($db, $dsSchema, "m");

    #build up our filename
    $userName = $userNames{$userID};
    $filename = $userName . "_" . $dsName;
    $filename =~ s/\"//g;
    $filename =~ s/\'//g;
    $filename =~ s/\s//g;
    $filename =~ s/&//g;
    $filename =~ s/\(//g;
    $filename =~ s/\)//g;
    $zipFile = "$userID" . "_" . $dsID . "_" . $filename . ".zip";
    $filename = "$userID" . "_" . $dsID . "_" . $filename . ".csv";

    #open the file for output
    open(OUTPUT, ">$filename");

    #if there's a UPC attribute in this data source, find its ID and get val hash
    $query = "SELECT ID FROM $dsSchema.product_attributes WHERE name = 'UPC'";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($upcID) = $dbOutput->fetchrow_array;
    undef(%upcHash);
    if (defined($upcID))
    {
      %upcHash = DSRattr_get_values_hash($db, $dsSchema, "p", $upcID);
    }

    #get the ID of every segmentation in our DS
    @prodSegIDs = DSRsegmentation_get_segmentations_array($db, $dsSchema, "p");
    @geoSegIDs = DSRsegmentation_get_segmentations_array($db, $dsSchema, "g");
    @timeSegIDs = DSRsegmentation_get_segmentations_array($db, $dsSchema, "t");

    #build up an array of segment membership hashes for each segmentation
    undef(@prodSegs);
    undef(@geoSegs);
    undef(@timeSegs);
    foreach $segID (@prodSegIDs)
    {
      my %memHash = DSRseg_get_segment_membership_hash($db, $dsSchema, "p", $segID);
      push(@prodSegs, \%memHash);
    }
    foreach $segID (@geoSegIDs)
    {
      my %memHash = DSRseg_get_segment_membership_hash($db, $dsSchema, "g", $segID);
      push(@geoSegs, \%memHash);
    }
    foreach $segID (@timeSegIDs)
    {
      my %memHash = DSRseg_get_segment_membership_hash($db, $dsSchema, "t", $segID);
      push(@timeSegs, \%memHash);
    }

    #output the CSV file's header line
    print OUTPUT "Product,Geography,Time Period,";
    foreach $measureID (sort {$a<=>$b} keys %measNameHash)
    {
      if ($measureID =~ m/^\d+$/)
      {
        print OUTPUT "\"$measNameHash{$measureID}\",";
      }
    }
    foreach $segID (@prodSegIDs)
    {
      $segID = "SEG_" . $segID;
      print OUTPUT "\"PSEG:$prodNameHash{$segID}\",";
    }
    foreach $segID (@geoSegIDs)
    {
      $segID = "SEG_" . $segID;
      print OUTPUT "\"GSEG:$geoNameHash{$segID}\",";
    }
    foreach $segID (@timeSegIDs)
    {
      $segID = "SEG_" . $segID;
      print OUTPUT "\"TSEG:$timeNameHash{$segID}\",";
    }
    if (defined($upcID))
    {
      print OUTPUT "PATTR:UPC,";
    }

    print OUTPUT "\n";

    #grab every row from the data source's facts table
    $query = "SELECT * FROM $dsSchema.facts";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    @rowData = $dbOutput->fetchrow_array;

    #run through every line from the facts table, and output as CSV
    while (defined(@rowData[0]))
    {

      #convert dimension IDs to human-readable text
      $prodID = $rowData[0];
      $geoID = $rowData[1];
      $timeID = $rowData[2];
      $rowData[0] = "\"$prodNameHash{$rowData[0]}\"";
      $rowData[1] = "\"$geoNameHash{$rowData[1]}\"";
      $rowData[2] = "\"$timeNameHash{$rowData[2]}\"";

      #write out the measure data
      foreach $data (@rowData)
      {
        print OUTPUT "$data,";
      }

      #output segment name for each segmentation
      $idx = 0;
      foreach $segID (@prodSegIDs)
      {
        $segmentID = $prodSegs[$idx]->{$prodID};
        $segmentID = "SMT_" . $segmentID;
        print OUTPUT "\"$prodNameHash{$segmentID}\",";
        $idx++;
      }

      $idx = 0;
      foreach $segID (@geoSegIDs)
      {
        $segmentID = $geoSegs[$idx]->{$geoID};
        $segmentID = "SMT_" . $segmentID;
        print OUTPUT "\"$geoNameHash{$segmentID}\",";
        $idx++;
      }

      $idx = 0;
      foreach $segID (@timeSegIDs)
      {
        $segmentID = $timeSegs[$idx]->{$timeID};
        $segmentID = "SMT_" . $segmentID;
        print OUTPUT "\"$timeNameHash{$segmentID}\",";
        $idx++;
      }

      if (defined($upcID))
      {
        print OUTPUT "$upcHash{$prodID},";
      }

      print OUTPUT "\n";

      @rowData = $dbOutput->fetchrow_array;
    }

    close(OUTPUT);

    #compress the output file for download
    unlink("$zipFile");
    `/usr/bin/zip $zipFile $filename`;
    unlink("$filename");
  }

#EOF
