#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;



  $dsID = $ARGV[0];

  chdir("/opt/apache/app/tmp/");

  #connect to the database
  $db = KAPutil_connect_to_database();

  #open the file we're going to dump table data into
  open(OUTPUT, ">/opt/apache/app/tmp/archive_$dsID.txt");

  $dsSchema = "datasource_" . $dsID;

  #grab all of the datasource's table setting fields
  $query = "SELECT * from app.dataSources WHERE ID=$dsID";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  @vals = $dbOutput->fetchrow_array;
  $dsString = join(',', @vals);
  print OUTPUT "$dsString\n";

  #grab all of the datasource's cube setting fields
  $query = "SELECT * from app.cubes WHERE dsID=$dsID";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  print OUTPUT "\nCUBES:\n------\n";
  while (@vals = $dbOutput->fetchrow_array)
  {
    $str = join(',', @vals);
    print OUTPUT "$str\n";
  }

  #grab all of the datasource's visual setting fields
  $query = "SELECT * from app.visuals WHERE dsID=$dsID";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  print OUTPUT "\nVISUALS:\n--------\n";
  while (@vals = $dbOutput->fetchrow_array)
  {
    $str = join(',', @vals);
    print OUTPUT "$str\n";
  }

  close(OUTPUT);

  #next, dump out the data source contents to disk
  `/usr/bin/mysqldump -h$Lib::KoalaConfig::dbServerName -u app -p$Lib::KoalaConfig::password -e $dsSchema --ignore-table=$dbName.export > /opt/apache/app/tmp/archive_$dsID.sql`;

  `/usr/bin/zip archive_$dsID.zip archive_$dsID.txt archive_$dsID.sql`;
