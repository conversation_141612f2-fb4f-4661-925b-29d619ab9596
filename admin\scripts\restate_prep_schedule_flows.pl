#!/usr/bin/perl

#
# Schedule all 108 week data flows that are pulling from the Nielsen IDW for
# a monthly run.
#

use lib "/opt/apache/app/";

use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::PrepUtils;


  #connect to the database
  $db = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();


  #get the ID of every Nielsen IDW data flow on the system
  $query = "SELECT ID, name, sourceInfo FROM prep.flows WHERE sourceInfo LIKE 'FTP=nielsen|%.zip' AND dsID > 0";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  while (($flowID, $name, $sourceInfo) = $dbOutput->fetchrow_array)
  {

    #skip update data flows
    if (($sourceInfo =~ m/update\.zip/))
    {
      next;
    }

    print("Scheduling $name ($flowID)\n");

    #schedule the flow to run monthly, this month
#    $query = "UPDATE prep.schedule SET lastRun='2021-01-01 00:00:00', lastFileSize=0 WHERE flowID=$flowID";
    $query = "INSERT INTO prep.schedule (flowID, sched, details, lastFileSize, lastFileDate, needsRun) VALUES ($flowID, 'monthly', '1', 0, '2020-09-01 00:00:00', 1)";
    $prepDB->do($query);

  }

exit;



#EOF
