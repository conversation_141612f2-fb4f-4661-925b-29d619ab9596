#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::AInsights::AInsights;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $priceModelID = $q->param('pm');
  $dsID = $q->param('ds');
  $naFilter = $q->param('na');
  $confidenceFilter = $q->param('confidence');
  $segID = $q->param('seg');
  $segmentIDstr = $q->param('segments');
  $action = $q->param('action');

  if ($naFilter eq "true")
  {
    $naFilter = 1;
  }
  else
  {
    $naFilter = 0;
  }

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsSchema = "datasource_" . $dsID;

  #make sure we have read privs for this pricing model
  $privs = AInsights_rights($db, $userID, $priceModelID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to view this elasticity model.");
  }


  ########################################################################
  #
  # This code block is called on submit to save the changes
  #

  if ($action eq "save")
  {
    if (($segID > 0) && (length($segmentIDstr) > 0))
    {
      $filter = "$segID:$segmentIDstr";
    }
    else
    {
      $filter = "";
    }

    $query = "UPDATE analytics.pricing \
        SET filter='$filter', filterNA=$naFilter, filterConfidence=$confidenceFilter \
        WHERE ID=$priceModelID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    $elasticName = AInsights_ID_to_name($db, $priceModelID);
    $dsName = ds_id_to_name($db, $dsID);

    AInsights_audit($db, $userID, $priceModelID, "Changed filtering settings");
    $activity = "ELASTICITY: $first $last changed filter settings in elasticity $elasticName in $dsName";
    utils_slack($activity);

    exit;
  }

  #########################################################################
  #
  # Everything after this point is called to display the filtering dialog
  #

  #get the filtering details from the database
  $query = "SELECT filterNA, filterConfidence, filter FROM analytics.pricing \
      WHERE ID=$priceModelID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($filterNA, $filterConfidence, $filter) = $dbOutput->fetchrow_array;

  if ($filterNA == 1)
  {
    $filterNA = "CHECKED";
  }
  else
  {
    $filterNA = "";
  }

  if ($filter =~ m/(.*?)\:(.*)/)
  {
    $matchSegVal = $1;
    $matchSegmentVal = $2;
  }

  print <<END_HTML;
<SCRIPT>
function submitForm()
{
  let na = \$('#na').prop('checked');
  let confidence = document.getElementById('confidence').value;
  let segmentMatch = \$('#seg-segmentMatch').val();
  let segMatch = document.getElementById('seg-segMatch').value;
  let url = 'xhrFilter.cld?action=save&pm=$priceModelID&v=$visID&na=' + na + '&confidence=' + confidence + '&seg=' + segMatch + '&segments=' + segmentMatch;

  \$.get(url, function(data, status)
  {
    location.href = 'elasticity.cld?pm=$priceModelID';
  });
}


function segUpdate(segSel, segmentSel, firstRun, matchSegmentVal)
{
  let segID = document.getElementById(segSel).value;

  let urlStr = '/app/dsr/ajaxAPI.cld?svc=segments&ds=$dsID&dim=p&seg=' + segID;

  \$(segmentSel).empty();
  \$.ajax(
  {
    url: urlStr,
    dataType: 'json',
    type: 'GET',
    success: function(response)
    {
      if (response != '')
      {
        for (i in response)
        {
          \$(segmentSel).append('<OPTION VALUE=' + response[i].id + '>'+response[i].name+'</OPTION>');
        }

        if ((firstRun == 1) && (matchSegmentVal.length > 0))
	      {
          let valStr = matchSegmentVal;
          let vals = valStr.split(',');
          \$(segmentSel).val(vals);
	      }
      }
      \$(segmentSel).selectpicker('refresh');
    },
    error: function(x, e) {console.log(e)}
  });
}
</SCRIPT>

<DIV CLASS="modal-dialog modal-lg">
  <DIV CLASS="modal-content">
    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Filtering</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">

      <DIV CLASS="row">
        <DIV CLASS="col-auto mt-1">
          Only show products that in the
        </DIV>
        <DIV CLASS="col-auto">
          <SELECT CLASS="form-select" ID="seg-segMatch" onChange="segUpdate('seg-segMatch', '#seg-segmentMatch', 0);">
END_HTML

  %segHash = DSRsegmentation_get_segmentations_hash($db, $dsSchema, "p");
  foreach $segID (sort {$segHash{$a} cmp $segHash{$b}} keys %segHash)
  {
    if ($matchSegVal < 1)
    {
      $matchSegVal = $segID;
    }
    print(" <OPTION VALUE='$segID'>$segHash{$segID}</OPTION>\n");
  }

print <<END_HTML;
          </SELECT>
        </DIV>

        <DIV CLASS="row my-1">
          <DIV CLASS="col-auto mt-1">
            segmentation are members of the
          </DIV>
          <DIV CLASS="col-auto">
            <SELECT CLASS="selectpicker" ID="seg-segmentMatch" multiple>
            </SELECT>
          </DIV>
          <DIV CLASS="col-auto mt-1">
            segment(s).
          </DIV>

        <SCRIPT>
          \$('.selectpicker').selectpicker('render');

          \$(document).ready(function()
          {
            \$('#seg-segMatch').val('$matchSegVal');
            segUpdate('seg-segMatch', '#seg-segmentMatch', 1, '$matchSegmentVal');
          });
        </SCRIPT>

      <P>
      <HR>
      <P>

      <DIV CLASS="row">
        <DIV CLASS="col-auto mt-1">
          Only show elasticities from models with a
        </DIV>
        <DIV CLASS="col-auto">
          <SELECT CLASS="form-select" NAME="confidence" ID="confidence">
            <OPTION VALUE=0>Non-existant</OPTION>
            <OPTION VALUE=1>Low</OPTION>
            <OPTION VALUE=2>Medium</OPTION>
            <OPTION VALUE=3>High</OPTION>
          </SELECT>
          <SCRIPT>
            \$('select#confidence').val('$filterConfidence');
          </SCRIPT>
        </DIV>
        <DIV CLASS="col-auto mt-1">
          or better confidence level.
        </DIV>
      </DIV>

      <P>
      <HR>
      <P>

      <DIV CLASS="row">
        <DIV CLASS="col-auto">
          <DIV CLASS="form-check">
            <INPUT CLASS="form-check-input" TYPE="checkbox" NAME="na" id="na" $filterNA>
            <LABEL CLASS="form-check-label" FOR="na">Filter products with no elasticity values</LABEL>
          </DIV>
        </DIV>
      </DIV>

      <P>
    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="submitForm()"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML


#EOF
