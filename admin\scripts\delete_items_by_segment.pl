#!/usr/bin/perl

use lib "/opt/apache/app/";

use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;



$DSID = 4387;
$SEGMENTID = 6062;

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsSchema = "datasource_" . $DSID;

  #cycle through all spurious cheese items, and remove them
  $query = "SELECT itemID FROM $dsSchema.product_segment_item \
      WHERE segmentID=$SEGMENTID";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  while (($itemID) = $dbOutput->fetchrow_array)
  {
    print("Deleting item $itemID\n");
    $query = "DELETE FROM $dsSchema.products WHERE ID=$itemID";
    $db->do($query);
    $query = "DELETE FROM $dsSchema.facts WHERE productID=$itemID";
    $db->do($query);
  }
