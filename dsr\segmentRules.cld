#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Segmentation</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<SCRIPT SRC="/jsgrid/jsgrid.min.js"></SCRIPT>

<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/jsgrid/jsgrid.min.css" REL="stylesheet">
<LINK HREF="/jsgrid/jsgrid-theme.min.css" REL="stylesheet">
<LINK HREF="/fontawesome-5.10.2/css/all.min.css" REL="stylesheet">

<SCRIPT>
function submitForm()
{
  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Please Wait...');
  return(true);
}



function editSegmentation()
{
  location.href='segmentAssign.cld?ds=$dsID&dim=$dim&seg=$segID';
}



function getSelectionStr()
{
  let grid = \$('#dsGrid').jsGrid('option', 'data');
  let selStr = '';

  for (let i = 0; i < grid.length; i++)
  {
    if (grid[i].selected == 1)
    {
      selStr = selStr + grid[i].id + ',';
    }
  }

  return(selStr);
}



function getSelectionIdx()
{
  let grid = \$('#dsGrid').jsGrid('option', 'data');
  let selStr = '';

  for (let i = 0; i < grid.length; i++)
  {
    if (grid[i].selected == 1)
    {
      selStr = selStr + i + ',';
    }
  }

  return(selStr);
}



function topRule()
{
  let url = 'xhrSegRuleDel.cld?ds=$dsID&dim=$dim&a=top&r=' + selectedRule;

  \$.get(url, function(data, status)
  {
    \$('#dsGrid').jsGrid('render');
  });

}



function upRule()
{
  let url = 'xhrSegRuleDel.cld?ds=$dsID&dim=$dim&a=up&r=' + selectedRule;

  \$.get(url, function(data, status)
  {
    \$('#dsGrid').jsGrid('render');
  });

}



function downRule()
{
  let url = 'xhrSegRuleDel.cld?ds=$dsID&dim=$dim&a=down&r=' + selectedRule;

  \$.get(url, function(data, status)
  {
    \$('#dsGrid').jsGrid('render');
  });

}



function bottomRule()
{
  let url = 'xhrSegRuleDel.cld?ds=$dsID&dim=$dim&a=bottom&r=' + selectedRule;

  \$.get(url, function(data, status)
  {
    \$('#dsGrid').jsGrid('render');
  });
}
</SCRIPT>

<STYLE>
div.grid th
{
  background: #e2e3e5 !important;
}

.selected-row > td
{
  background: #c4e2ff !important;
  border-color: #c4e2ff;
}
</STYLE>

<SCRIPT>
  //global
  selectedRule = 0;

\$(document).ready(function()
{

  \$('#dsGrid').jsGrid(
  {
    width: '100pct',
    height: '400px',
    sorting: false,
    autoload: true,
    loadIndication: true,

    controller:
    {
      loadData: function (filter)
      {
        let data = \$.Deferred();
        \$.ajax(
        {
          type: 'GET',
          contentType: 'application/json; charset=utf-8',
          url: 'ajaxSegmentationRules.cld?ds=$dsID&dim=$dim&s=$segID',
          dataType: 'json'
        }).done(function(response)
        {
          data.resolve(response);
        });
        return data.promise();
      }
    },

    rowClick: function(args)
    {
      selectedRule = args.item.ID;

      \$('#dsGrid tr').removeClass('selected-row');

      \$selectedRow = \$(args.event.target).closest('tr');
      \$selectedRow.addClass('selected-row');
    },

    fields: [
      {name: 'ID', type: 'number', visible: false},
      {name: 'Segment', type: 'text', width: 150},
      {name: 'Rule', type: 'text', width:350}
    ]
  });

  \$('modal-segrule-add').on('hide.bs.modal', function ()
  {
    \$('#modal-segrule-add').removeData('bs.modal');
    \$('#modal-segrule-add .modal-content').html('');
  });

});



function addSegRuleDlg()
{
  location.href='segmentRulesDefine.cld?ds=$dsID&dim=$dim&s=$segID';
}



function editSegRuleDlg()
{
  location.href='segmentRulesDefine.cld?ds=$dsID&dim=$dim&s=$segID&r=' + selectedRule;
}



function delSegRuleDlg()
{
  \$('#modal-segrule-add').load('/app/dsr/xhrSegRuleDel.cld?ds=$dsID&dim=$dim&s=$segID&r=' + selectedRule, function (response, status, xhr)
  {
    if (status == 'success')
    {
      let myModal = new bootstrap.Modal(document.getElementById('modal-segrule-add'));
      myModal.show();
    }
  });
}
</SCRIPT>

<STYLE>
.accordion-button.collapsed {
  background: blue
}

.accordion-button.collapsed::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}
</STYLE>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  $dsName = ds_id_to_name($db, $dsID);

  print <<END_HTML;

<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">$action Segmentation $segName</LI>
  </OL>
</NAV>

<DIV id="modal-segrule-add" class="modal" role="dialog">
</DIV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');
  $dim = $q->param('dim');
  $segName = $q->param('segName');
  $segID = $q->param('seg');

  if ($segID =~ m/^SEG_(\d+)$/)
  {
    $segID = $1;
  }

  #set human-readable action
  if ($segID > 0)
  {
    $action = "Edit";
  }
  else
  {
    $action = "New";
  }

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsSchema = "datasource_" . $dsID;

  #get our dimension name for display purposes/db table name for SELECT
  $dimDB = KAPutil_get_dim_stub_name($dim);
  $itemDB = KAPutil_get_dim_db_name($dim);
  $dimName = KAPutil_get_dim_name_singular($dim, 1);

  #make sure we have write privs for the data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    exit_early_error($session, "You don't have privileges to modify this data source.");
  }

  #if the segmentation hasn't been created yet, do it
  if ($segID < 1)
  {
    $q_segName = $db->quote($segName);
    $dbName = $dimDB . "segmentation";
    $query = "INSERT INTO $dsSchema.$dbName (name) VALUES ($q_segName)";
    $db->do($query);
    $segID = $db->{q{mysql_insertid}};
  }

  if (($segID > 0) && (length($segName) < 1))
  {
    $query = "SELECT name FROM $dsSchema.product_segmentation WHERE ID=$segID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute();
    ($segName) = $dbOutput->fetchrow_array;
  }

  print_html_header();

  print <<END_HTML;
<DIV CLASS="container-fluid gx-3">

  <DIV CLASS="row">

    <DIV CLASS="col"> <!-- content -->

      <FORM METHOD="post" ID="segForm" ACTION="segmentSave.cld" onsubmit="return submitForm(this);">
      <INPUT TYPE="hidden" NAME="dsID" VALUE="$dsID">
      <INPUT TYPE="hidden" NAME="dim" VALUE="$dim">
      <INPUT TYPE="hidden" NAME="segName" VALUE="$segName">
      <INPUT TYPE="hidden" NAME="seg" VALUE="$segID">
      <INPUT TYPE="hidden" NAME="etype" VALUE="rules">

      <DIV CLASS="accordion mx-auto" ID="accordion">

        <DIV CLASS="accordion-item border-primary">
          <H2 CLASS="accordion-header">
            <BUTTON CLASS="accordion-button bg-primary text-white icon-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse1" onClick="editSegmentation()">
              $action $dimName Segmentation
            </BUTTON>
          </H2>
          <DIV ID="collapse1" CLASS="accordion-collapse collapse" data-bs-parent="#accordion">
            <DIV CLASS="accordion-body">

            </DIV>
          </DIV>
        </DIV>

        <DIV CLASS="accordion-item border-primary">
          <H2 CLASS="accordion-header">
            <BUTTON CLASS="accordion-button collapsed bg-primary text-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse3">
              Segmentation Rules
            </BUTTON>
          </H2>
          <DIV ID="collapse3" CLASS="accordion-collapse collapse show" data-bs-parent="#accordion">
            <DIV CLASS="accordion-body">

              <DIV CLASS="container-fluid gx-3">
                <DIV CLASS="row">
                  <DIV CLASS="col-11">
                    <DIV ID="dsGrid" CLASS="grid mx-auto" STYLE="font-size:14px;"></DIV>

                    <P>
                    <DIV CLASS="text-center">
                      <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="addSegRuleDlg()"><I CLASS="bi bi-plus-lg"></I> Add</BUTTON>
                      <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="editSegRuleDlg()"><I CLASS="bi bi-pencil"></I> Modify</BUTTON>
                      <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="delSegRuleDlg()"><I CLASS="bi bi-trash"></I> Remove</BUTTON>
                    </DIV>
                  </DIV>
                  <DIV CLASS="col-1">
                    <DIV CLASS="position-relative top-50 start-50 translate-middle">
                      <A HREF="#" CLASS="btn btn-primary" onClick="topRule()" TITLE="Move selected rule to the top of the list"><I CLASS="bi bi-chevron-bar-up"></I></A>
                      <P></P>
                      <A HREF="#" CLASS="btn btn-primary" onClick="upRule()" TITLE="Move selected rule up"><I CLASS="bi bi-chevron-up"></I></A>
                      <P></P>
                      <A HREF="#" CLASS="btn btn-primary" onClick="downRule()" TITLE="Move selected rule down"><I CLASS="bi bi-chevron-down"></I></A>
                      <P></P>
                      <A HREF="#" CLASS="btn btn-primary" onClick="bottomRule()" TITLE="Move selected rule to the bottom of the list"><I CLASS="bi bi-chevron-bar-down"></I></A>
                    </DIV>
                  </DIV>
                </DIV>  <!-- row -->
              </DIV>  <!-- container -->

            </DIV>
          </DIV>
        </DIV>
END_HTML

  #get the ID of our parent segmentation, if we have one
  $query = "SELECT parentID FROM $dsSchema.product_segmentation WHERE ID=$segID";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($parentSeg) = $dbOutput->fetchrow_array;

  #get the ID of any child segmentations we might have
  $childSegs = "";
  $query = "SELECT ID FROM $dsSchema.product_segmentation WHERE parentID=$segID";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  while (($tmp) = $dbOutput->fetchrow_array)
  {
    $childSegs .= "$tmp,";
  }

  print <<END_HTML;
      </DIV>

      <P>
      <DIV CLASS="text-center">
        <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="location.href='/app/dsr/display.cld?ds=$dsID&dim=$dim'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
        <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-submit"><I CLASS="bi bi-save"></I> Save</BUTTON>
      </DIV>

      </FORM>
    </DIV>  <!-- content -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
<P>
END_HTML

  print_html_footer();

#EOF
