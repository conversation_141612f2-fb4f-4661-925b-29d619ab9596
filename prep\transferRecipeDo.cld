#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepRecipes;
use Lib::PrepUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($kapDB);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Transfer Transform Recipe Steps</TITLE>
<LINK REL="SHORTCUT ICON" HREF="$Lib::KoalaConfig::kapHostURL/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($kapDB, $userID, $first, $last, $orgName);

  $flowName = prep_flow_id_to_name($prepDB, $flowID);
  $destFlowName = prep_flow_id_to_name($prepDB, $destFlowID);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="$Lib::KoalaConfig::kapHostURL/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?f=$flowID">Data Prep</A></LI>
    <LI CLASS="breadcrumb-item">$flowName</LI>
    <LI CLASS="breadcrumb-item active">Transfer Recipe Steps</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #get CGI parameters
  $flowID = $q->param('f');
  $destFlowID = $q->param('destFlowID');

  print_html_header();

  #make sure we have read privs on the source data flow
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to transfer recipes steps from this data flow.");
  }

  #make sure we have write privs for the destination data flow
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $destFlowID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit the destination data flow.");
  }

  #run through the list of CGI parameters, extracting step xfer information
  @names = $q->param;
  foreach $name (@names)
  {
    if ($name =~ m/^S (\d+)/)
    {
      $stepID = $q->param($name);
      if ($stepID eq "on")
      {
        $stepID = $1;
      }
      else
      {
        next;
      }

      $query = "SELECT action FROM prep.recipes WHERE flowID=$flowID AND step=$stepID";
      $dbOutput = $prepDB->prepare($query);
      $dbOutput->execute;
      ($action) = $dbOutput->fetchrow_array;

      prep_recipe_add_step($prepDB, $destFlowID, $action);
    }
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-10 col-lg-7 col-xl-6"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Recipe Steps Transferred</DIV>
        <DIV CLASS="card-body">

          <P>
          The selected steps have been transferred.

          <P>&nbsp;<P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" onClick="location.href='main.cld?f=$flowID'"><I CLASS="bi bi-check-lg"></I> Done</BUTTON>
            <BUTTON CLASS="btn btn-primary" onClick="location.href='recipeEdit.cld?f=$destFlowID'"><I CLASS="bi bi-pencil"></I> Edit Destination Recipe</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

  prep_audit($prepDB, $userID, "Transferred recipe steps to $destFlowName", $flowID);
  prep_audit($prepDB, $userID, "Transferred recipe steps from $flowName", $destFlowID);
  utils_slack("PREP: $first $last transferred recipe steps from $flowName");


#EOF
