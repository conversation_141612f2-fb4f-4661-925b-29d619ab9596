#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get CGI parameters
  $flowID = $q->param('f');
  $name = $q->param('name');
  $desc = $q->param('desc');
  $type = $q->param('type');

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  $q_name = $prepDB->quote($name);
  $q_desc = $prepDB->quote($desc);
  $q_source = $prepDB->quote($type);

  #if we're creating a new data flow, add it to the database
  if ($flowID < 1)
  {
    $action = "n";

    $query = "INSERT INTO prep.flows (userID, name, description, source) \
        VALUES ($userID, $q_name, $q_desc, $q_source)";
    $prepDB->do($query);

    #get the ID for the new data flow
    $flowID = $prepDB->{q{mysql_insertid}};

    prep_audit($prepDB, $userID, "Created new $type data flow", $flowID);
    utils_slack("PREP: $first $last created new $type data flow: $name");
  }

  #else we're updating an existing data flow
  else
  {

    $action = "e";

    #update our source type
    $query = "UPDATE prep.flows \
        SET name=$q_name, description=$q_desc, source=$q_source \
        WHERE ID=$flowID";
    $prepDB->do($query);
  }

  #redirect to the appropriate source script
  if ($type eq "Manual")
  {
    $redirect = "sourceManual.cld?f=$flowID&a=$action";
  }
  elsif ($type eq "Web")
  {
    $redirect = "sourceWeb.cld?f=$flowID&a=$action";
  }
  elsif ($type eq "FTP")
  {
    $redirect = "sourceFTP.cld?f=$flowID&a=$action";
  }
  elsif ($type eq "Paste")
  {
    $redirect = "sourcePaste.cld?f=$flowID&a=$action";
  }
  elsif ($type eq "Database")
  {
    $redirect = "sourceDatabase.cld?f=$flowID&a=$action";
  }
  elsif ($type eq "AmazonS3")
  {
    $redirect = "sourceAmazon.cld?f=$flowID&a=$action";
  }
  elsif ($type eq "Koala")
  {
    $redirect = "sourceKoala.cld?f=$flowID&a=$action";
  }

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META HTTP-EQUIV="refresh" CONTENT="0; URL=$redirect">
<META NAME="google" CONTENT="notranslate">
</HEAD>
<BODY>
</BODY>
</HTML>

END_HTML


#EOF
