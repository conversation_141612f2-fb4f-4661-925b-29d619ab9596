#!/usr/bin/perl

use lib "/opt/apache/app/";

use DBI;
use Email::Valid;

use Lib::KoalaConfig;
use Lib::BuildCube;
use Lib::DataSources;
use Lib::DSRMeasures;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::WebUtils;


my $debug;


#-------------------------------------------------------------------------
#
# Output debug data, if enabled
#

sub DBG
{
  my ($str) = @_;


  if ($debug == 1)
  {
    print STDERR "$str\n";
  }
}



#-------------------------------------------------------------------------

  #determine if we're being run manually
  $debug = 0;
  if ($ARGV[0] eq "debug")
  {
    $debug = 1;
  }

  #redirect STDERR to the Koala error log
  if ($debug == 0)
  {
    close(STDOUT);
    close(STDERR);
    open(STDERR, ">>/opt/apache/htdocs/tmp/koala_error.log");
  }

  #connect to the databases
  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #remove any data files from the Koala tmp folder that are more than 7 days old
  DBG("Removing source data files from app/tmp that are older than 7 days");
  chdir("/opt/apache/app/tmp");
  @files = glob("*.work *.csv *.xls *.xlsx");
  foreach $file (@files)
  {
    if (-C $file > 7)
    {
      unlink($file);
      DBG("***** Unlinked $file");
    }
  }

  #remove any DS update logs that are more than a week old
  DBG("Removing DS update telemetry logs more than a week old");
  chdir("/opt/apache/htdocs/tmp");
  @files = glob("dsimport*.log");
  foreach $file (@files)
  {
    if (-M $file > 7)
    {
      unlink($file);
      DBG("***** Unlinked $file");
    }
  }

  #remove any user export files that are more than a day old
  DBG("Removing user export files more than a day old");
  chdir("/opt/apache/htdocs/tmp");
  @files = glob("*.zip *.pptx *.xlsx");
  foreach $file (@files)
  {
    if (-M $file > 1)
    {
      unlink($file);
      DBG("***** Unlinked $file");
    }
  }

  #remove Apache log files that are more than a week old
  DBG("Removing Apache logs files more than a week old");
  chdir("/opt/apache/logs");
  @files = glob("access_log* error_log*");
  foreach $file (@files)
  {
    if (-M $file > 7)
    {
      unlink($file);
      DBG("***** Unlinked $file");
    }
  }

  #remove user audit records older than 3 years
  DBG("Removing audit records over 3 years old");
  $query = "DELETE FROM prep.audit \
      WHERE DATE_SUB(NOW(), INTERVAL 156 WEEK) > timestamp";
  $prepDB->do($query);

  $query = "DELETE FROM prep.telemetry \
      WHERE DATE_SUB(NOW(), INTERVAL 156 WEEK) > startTime";
  $prepDB->do($query);

  #grab every job ID that hasn't been used in a calendar week
  $query = "SELECT ID, flowID FROM prep.jobs \
      WHERE DATE_SUB(NOW(), INTERVAL 7 DAY) > lastAction AND ISNULL(PID)";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;

  while (($jobID, $flowID) = $dbOutput->fetchrow_array)
  {
    DBG("Dropping stale prep job $jobID");

    prep_job_clear($prepDB, $flowID, $jobID);
    flow_telemetry($prepDB, $jobID, "Job automatically cleared");
  }

  #remove any targeted data sources that no longer exist
  %dsNames = ds_get_name_hash($kapDB);
  $query = "SELECT ID, name, dsID FROM prep.flows WHERE dsID > 0";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  while (($flowID, $flowName, $dsID) = $dbOutput->fetchrow_array)
  {
    if (length($dsNames{$dsID}) < 1)
    {
      DBG("Removing non-existent DS target from $flowName");
      $query = "UPDATE prep.flows SET dsID = NULL WHERE ID=$flowID";
      $prepDB->do($query);
    }
  }


#EOF
