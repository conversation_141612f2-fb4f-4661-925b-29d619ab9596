#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepRecipes;
use Lib::PrepUtils;
use Lib::WebUtils;


#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $flowID = $q->param('f');
  $jobID = $q->param('j');
  $colID = $q->param('col');

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #make sure we have read privs for this data flow
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to analyze this data flow.");
  }

  $flowName = prep_flow_id_to_name($prepDB, $flowID);

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #build name of column containing the dates
  $column = "column_" . $colID;

  #get the name of the column we're analyzing
  $query = "SELECT name FROM $masterColTable WHERE ID=$colID";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($colName) = $dbOutput->fetchrow_array;

  print <<END_HTML;
<DIV CLASS="modal-dialog modal-lg">
  <DIV CLASS="modal-content">

    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Date Analysis</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">
      <DIV CLASS="table-responsive" STYLE="height:400px; overflow:auto;">
        <TABLE CLASS="table table-striped table-sm table-hover">
          <THEAD><TR>
            <TH>Date</TH>
            <TH>Count</TH>
            <TH>Type</TH>
            <TH>Duration</TH>
            <TH>End Date</TH>
          </TR></THEAD>
END_HTML

  #grab each date field entry and its count from the data
  undef(@dateArray);
  undef(@countArray);
  undef(%dateDetailHash);
  $idx = 0;
  $query = "SELECT $column, COUNT($column) AS count FROM $masterTable \
      GROUP BY $column ORDER BY count DESC";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;

  while (($val, $count) = $dbOutput->fetchrow_array)
  {
    $dateArray[$idx] = $val;
    $countArray[$idx] = $count;

    ($duration, $type, $endDate) = prep_recipe_parse_date($prepDB, $val);
    $endDate =~ m/^(\d+)-(\d+)-(\d+) /;
    $year = $1;
    $month = $2;
    $day = $3;
    if (length($month) < 2)
    {
      $month = "0" . $month;
    }
    if (length($day) < 2)
    {
      $day = "0" . $day;
    }
    $endDate = "$year-$month-$day";
    $dateDetailHash{$idx} = "$endDate $type $duration";

    $idx++;
  }

  #display text fields and counts
  foreach $idx (sort {$dateDetailHash{$a} cmp $dateDetailHash{$b}} keys %dateDetailHash)
  {
    $dateDetailHash{$idx} =~ m/^(.*?) (.*?) (\d+)$/;
    $endDate = $1;
    $type = $2;
    $duration = $3;
    print <<END_HTML;
          <TR>
            <TD>$dateArray[$idx]</TD>
            <TD>$countArray[$idx]</TD>
            <TD>$type</TD>
            <TD>$duration</TD>
            <TD>$endDate</TD>
          </TR>
END_HTML
  }

  print <<END_HTML;
        </TABLE>
      </DIV>
    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-primary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-check-lg"></I> Done</BUTTON>
    </DIV>

  </DIV>
</DIV>

END_HTML

  $query = "UPDATE prep.jobs SET lastAction=NOW() WHERE ID=$jobID";
  $prepDB->do($query);

  prep_audit($prepDB, $userID, "Analyzed dates in $colName", $flowID);
  utils_slack("PREP: $first $last analyzed dates in $colName in $flowName");

#EOF
