#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSel;
use Lib::DSRUtils;
use Lib::DataSources;
use Lib::Reports;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $rptID = $q->param('rptID');
  $visID = $q->param('v');
  $filterMeas1 = $q->param('f1');
  $filterMeas2 = $q->param('f2');
  $filterMeas3 = $q->param('f3');
  $filterOp1 = $q->param('o1');
  $filterOp2 = $q->param('o2');
  $filterOp3 = $q->param('o3');
  $filterNum1 = $q->param('n1');
  $filterNum2 = $q->param('n2');
  $filterNum3 = $q->param('n3');
  $excludeNA = $q->param('ex');

  if ($excludeNA eq "true")
  {
    $excludeNA = 1;
  }
  else
  {
    $excludeNA = 0;
  }

  $db = KAPutil_connect_to_database();

  #make sure we have edit privs for this report
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this report.");
  }

  #get the table filter details from the database
  $query = "SELECT design FROM visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($tableDesign) = $dbOutput->fetchrow_array;


  ########################################################################
  #
  # This code block is called on submit to save the changes
  #

  if (defined($filterMeas1))
  {
    $tableDesign = reports_set_style($tableDesign, "filterMeas1", "$filterMeas1");
    $tableDesign = reports_set_style($tableDesign, "filterMeas2", "$filterMeas2");
    $tableDesign = reports_set_style($tableDesign, "filterMeas3", "$filterMeas3");

    $tableDesign = reports_set_style($tableDesign, "filterOp1", "$filterOp1");
    $tableDesign = reports_set_style($tableDesign, "filterOp2", "$filterOp2");
    $tableDesign = reports_set_style($tableDesign, "filterOp3", "$filterOp3");

    $tableDesign = reports_set_style($tableDesign, "filterNum1", "$filterNum1");
    $tableDesign = reports_set_style($tableDesign, "filterNum2", "$filterNum2");
    $tableDesign = reports_set_style($tableDesign, "filterNum3", "$filterNum3");

    $tableDesign = reports_set_style($tableDesign, "excludeNA", "$excludeNA");

    $q_tableDesign = $db->quote($tableDesign);

    $query = "UPDATE visuals SET design = $q_tableDesign WHERE ID=$visID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    $cubeName = cube_id_to_name($db, $rptID);
    $dsID = cube_get_ds_id($db, $rptID);
    $dsName = ds_id_to_name($db, $dsID);

    utils_audit($db, $userID, "Changed table filtering", $dsID, $rptID, 0);

    $activity = "$first $last changed table filters for $cubeName in $dsName";
    utils_slack($activity);

    exit;
  }


  #########################################################################
  #
  # Everything after this point is called to display the table filter dialog
  #

  $filterMeas1 = reports_get_style($tableDesign, "filterMeas1");
  $filterMeas2 = reports_get_style($tableDesign, "filterMeas2");
  $filterMeas3 = reports_get_style($tableDesign, "filterMeas3");
  $filterOp1 = reports_get_style($tableDesign, "filterOp1");
  $filterOp2 = reports_get_style($tableDesign, "filterOp2");
  $filterOp3 = reports_get_style($tableDesign, "filterOp3");
  $filterNum1 = reports_get_style($tableDesign, "filterNum1");
  $filterNum2 = reports_get_style($tableDesign, "filterNum2");
  $filterNum3 = reports_get_style($tableDesign, "filterNum3");
  $excludeNA = reports_get_style($tableDesign, "excludeNA");

  if (!defined($filterNum1))
  {
    $filterNum1 = 10;
  }
  if (!defined($filterNum2))
  {
    $filterNum2 = 10;
  }
  if (!defined($filterNum3))
  {
    $filterNum3 = 10;
  }

  $excludeNA = ($excludeNA == 1) ? "CHECKED" : "";

  print <<END_HTML;
<SCRIPT>
function submitForm()
{
  let filterMeas1 = document.getElementById('filterMeas1').value;
  let filterOp1 = document.getElementById('filterOp1').value;
  let filterNum1 = document.getElementById('filterNum1').value;
  let filterMeas2 = document.getElementById('filterMeas2').value;
  let filterOp2 = document.getElementById('filterOp2').value;
  let filterNum2 = document.getElementById('filterNum2').value;
  let filterMeas3 = document.getElementById('filterMeas3').value;
  let filterOp3 = document.getElementById('filterOp3').value;
  let filterNum3 = document.getElementById('filterNum3').value;
  let excludeNA = \$("#excludeNA").prop("checked");

  let url = "xhrTableFilter?rptID=$rptID&v=$visID&f1=" + filterMeas1 +
      "&f2=" + filterMeas2 + "&f3=" + filterMeas3 + "&o1=" + filterOp1 +
      "&o2=" + filterOp2 + "&o3=" + filterOp3 + "&n1=" + filterNum1 +
      "&n2=" + filterNum2 + "&n3=" + filterNum3 + "&ex=" + excludeNA;

  \$.get(url, function(data, status)
  {
    location.href = "display.cld?rpt=$rptID&v=$visID";
  });
}
</SCRIPT>

<DIV CLASS="modal-dialog modal-lg" STYLE="overflow-y: initial !important;">
  <DIV CLASS="modal-content">
    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Table Filtering</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">

      <FORM>
      <DIV CLASS="row">
        <DIV CLASS="col-auto mt-2">
          Display where
        </DIV>
        <DIV CLASS="col-auto">
          <SELECT CLASS="form-select" id='filterMeas1'>
            <OPTION VALUE="0">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</OPTION>
END_HTML

  #get the list of measures available in the report
  @measureIDs = datasel_get_dimension_items($db, $rptID, "m");

  #get hash of all measures and their names
  $dsID = cube_get_ds_id($db, $rptID);
  $dsSchema = "datasource_" . $dsID;
  %measureNameHash = dsr_get_item_name_hash($db, $dsSchema, "m");

  #output OPTION tags for all available measures
  foreach $id (@measureIDs)
  {
    print(" <OPTION VALUE=$id>$measureNameHash{$id}</OPTION>\n");
  }

  print <<END_HTML;
          </SELECT>
        </DIV>

        <DIV CLASS="col-auto mt-2">
          is
        </DIV>
        <DIV CLASS="col-auto">
          <SELECT CLASS="form-select" id='filterOp1' VALUE='$filterOp1'>
            <OPTION VALUE="gt">greater than</OPTION>
            <OPTION VALUE="lt">less than</OPTION>
            <OPTION VALUE="eq">equal to</OPTION>
            <OPTION VALUE="top">in the top n</OPTION>
            <OPTION VALUE="bottom">in the bottom n</OPTION>
            <OPTION VALUE="toppct">in the top n%</OPTION>
            <OPTION VALUE="bottompct">in the bottom n%</OPTION>
          </SELECT>
          <SCRIPT>
            \$("select#filterMeas1").val("$filterMeas1");
            \$("select#filterOp1").val("$filterOp1");
          </SCRIPT>
        </DIV>

        <DIV CLASS="col-auto">
          <INPUT CLASS="form-control" TYPE="number" id="filterNum1" VALUE='$filterNum1' STYLE="width:75px;">
        </DIV>

      </DIV>

      <P>&nbsp;</P>
      <DIV CLASS="row">

        <DIV CLASS="col-auto mt-2">
          And where
        </DIV>

        <DIV CLASS="col-auto">
          <SELECT CLASS="form-select" id='filterMeas2'>
            <OPTION VALUE="0">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</OPTION>
END_HTML

  #output OPTION tags for all available measures
  foreach $id (@measureIDs)
  {
    print(" <OPTION VALUE=$id>$measureNameHash{$id}</OPTION>\n");
  }

  print <<END_HTML;
          </SELECT>
        </DIV>

        <DIV CLASS="col-auto mt-2">
          is
        </DIV>

        <DIV CLASS="col-auto">
          <SELECT CLASS="form-select" id='filterOp2'>
            <OPTION VALUE="gt">greater than</OPTION>
            <OPTION VALUE="lt">less than</OPTION>
            <OPTION VALUE="eq">equal to</OPTION>
            <OPTION VALUE="top">in the top n</OPTION>
            <OPTION VALUE="bottom">in the bottom n</OPTION>
            <OPTION VALUE="toppct">in the top n%</OPTION>
            <OPTION VALUE="bottompct">in the bottom n%</OPTION>
          </SELECT>
          <SCRIPT>
            \$("select#filterMeas2").val("$filterMeas2");
            \$("select#filterOp2").val("$filterOp2");
          </SCRIPT>
        </DIV>

        <DIV CLASS="col-auto">
          <INPUT CLASS="form-control" TYPE="number" id="filterNum2" VALUE='$filterNum2' STYLE="width:75px;">
        </DIV>

      </DIV>

      <P>&nbsp;</P>
      <DIV CLASS="row">

        <DIV CLASS="col-auto mt-2">
          And where
        </DIV>

        <DIV CLASS="col-auto">
          <SELECT CLASS="form-select" id='filterMeas3'>
            <OPTION VALUE="0">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</OPTION>
END_HTML

  #output OPTION tags for all available measures
  foreach $id (@measureIDs)
  {
    print(" <OPTION VALUE=$id>$measureNameHash{$id}</OPTION>\n");
  }

  print <<END_HTML;
          </SELECT>
        </DIV>

        <DIV CLASS="col-auto mt-2">
          is
        </DIV>

        <DIV CLASS="col-auto">
          <SELECT CLASS="form-select" id='filterOp3'>
            <OPTION VALUE="gt">greater than</OPTION>
            <OPTION VALUE="lt">less than</OPTION>
            <OPTION VALUE="eq">equal to</OPTION>
            <OPTION VALUE="top">in the top n</OPTION>
            <OPTION VALUE="bottom">in the bottom n</OPTION>
            <OPTION VALUE="toppct">in the top n%</OPTION>
            <OPTION VALUE="bottompct">in the bottom n%</OPTION>
          </SELECT>
          <SCRIPT>
            \$("select#filterMeas3").val("$filterMeas3");
            \$("select#filterOp3").val("$filterOp3");
          </SCRIPT>
        </DIV>

        <DIV CLASS="col-auto">
          <INPUT CLASS="form-control" TYPE="number" id="filterNum3" VALUE='$filterNum3' STYLE="width:75px;">
        </DIV>

      </DIV>

      <P>
      <DIV CLASS="form-check">
        <INPUT CLASS="form-check-input" TYPE="checkbox" NAME="excludeNA" id="excludeNA" $excludeNA>
        <LABEL CLASS="form-check-label" FOR="excludeNA">Exclude NA and Zero</LABEL>
      </DIV>

      </FORM>

    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="submitForm()"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML


#EOF
