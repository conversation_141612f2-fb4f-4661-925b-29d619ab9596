#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  #output Content-type header
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  #output HTML
  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Export Data Source</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Export Data Source</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');

  #sanitize parameters
  $dsID = utils_sanitize_integer($dsID);

  $dsSchema = "datasource_" . $dsID;

  #connect to the database
  $db = KAPutil_connect_to_database();

  #get the name of the data source
  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  #make sure we have read privs for this data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to export this data source.");
  }

  $activity = "$first $last exported data source $dsName as a tabular file";
  utils_audit($db, $userID, "Exported data source as a tabular file", $dsID, 0, 0);
  utils_slack($activity);

  #make sure this DS isn't too big to export
  $query = "SELECT TABLE_ROWS FROM information_schema.TABLES \
      WHERE TABLE_SCHEMA='$dsSchema' AND TABLE_NAME='facts'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($rowCount) = $dbOutput->fetchrow_array;

  if ($rowCount > 1_000_000)
  {
    exit_warning("This data source is too large to be exported through the web user interface ($rowCount rows) - please contact Koala Support for assistance.")
  }

  #get the item names for all dimensions
  %prodNameHash = dsr_get_item_name_hash($db, $dsSchema, "p");
  %geoNameHash = dsr_get_item_name_hash($db, $dsSchema, "g");
  %timeNameHash = dsr_get_item_name_hash($db, $dsSchema, "t");
  %measNameHash = dsr_get_item_name_hash($db, $dsSchema, "m");

  #build a hash of child merged items (if any) - we want to exclude them from
  #the output
  $query = "SELECT ID FROM $dsSchema.products WHERE merged=2";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  while (($mergedProdID) = $dbOutput->fetchrow_array)
  {
    $mergedProdChildrenHash{$mergedProdID} = 1;
  }

  chdir("/opt/apache/htdocs/tmp/");

  #build up our filename
  $filename = $dsName;
  $filename =~ s/\"//g;
  $filename =~ s/\'//g;
  $filename =~ s/\s//g;
  $filename =~ s/&//g;
  $filename =~ s/\(//g;
  $filename =~ s/\)//g;
  $zipFile = "$userID" . "_" . $filename . ".zip";
  $filename = "$userID" . "_" . $filename . ".csv";

  #open the file for output
  open(OUTPUT, ">$filename");

  #if there's a UPC attribute in this data source, find its ID and get val hash
  $query = "SELECT ID FROM $dsSchema.product_attributes WHERE name = 'UPC'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($upcID) = $dbOutput->fetchrow_array;
  undef(%upcHash);
  if (defined($upcID))
  {
    %upcHash = DSRattr_get_values_hash($db, $dsSchema, "p", $upcID);
  }

  #get the ID of every segmentation in our DS
  @prodSegIDs = DSRsegmentation_get_segmentations_array($db, $dsSchema, "p");
  @geoSegIDs = DSRsegmentation_get_segmentations_array($db, $dsSchema, "g");
  @timeSegIDs = DSRsegmentation_get_segmentations_array($db, $dsSchema, "t");

  #build up an array of segment membership hashes for each segmentation
  undef(@prodSegs);
  undef(@geoSegs);
  undef(@timeSegs);
  foreach $segID (@prodSegIDs)
  {
    my %memHash = DSRseg_get_segment_membership_hash($db, $dsSchema, "p", $segID);
    push(@prodSegs, \%memHash);
  }
  foreach $segID (@geoSegIDs)
  {
    my %memHash = DSRseg_get_segment_membership_hash($db, $dsSchema, "g", $segID);
    push(@geoSegs, \%memHash);
  }
  foreach $segID (@timeSegIDs)
  {
    my %memHash = DSRseg_get_segment_membership_hash($db, $dsSchema, "t", $segID);
    push(@timeSegs, \%memHash);
  }

  #output the CSV file's header line
  print OUTPUT "Product,Geography,Time Period,";
  foreach $measureID (sort {$a<=>$b} keys %measNameHash)
  {
    if ($measureID =~ m/^\d+$/)
    {
      print OUTPUT "\"$measNameHash{$measureID}\",";
    }
  }
  foreach $segID (@prodSegIDs)
  {
    $segID = "SEG_" . $segID;
    print OUTPUT "\"PSEG:$prodNameHash{$segID}\",";
  }
  foreach $segID (@geoSegIDs)
  {
    $segID = "SEG_" . $segID;
    print OUTPUT "\"GSEG:$geoNameHash{$segID}\",";
  }
  foreach $segID (@timeSegIDs)
  {
    $segID = "SEG_" . $segID;
    print OUTPUT "\"TSEG:$timeNameHash{$segID}\",";
  }
  if (defined($upcID))
  {
    print OUTPUT "PATTR:UPC,";
  }

  print OUTPUT "\n";

  #get the list of measures that are in the table
  $measSubQ = "";
  $query = "SELECT ID FROM $dsSchema.measures ORDER BY ID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  while (($measID) = $dbOutput->fetchrow_array)
  {
    $measSubQ .= "measure_" . $measID . ",";
  }
  chop($measSubQ);

  #grab every row from the data source's facts table
  $dbh = KAPutil_connect_to_database();
  $query = "SELECT productID, geographyID, timeID, $measSubQ \
      FROM $dsSchema.facts LIMIT 1000000";
  $dbOutput = $dbh->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  #run through every line from the facts table, and output as CSV
  while (@rowData = $dbOutput->fetchrow_array)
  {

    #convert dimension IDs to human-readable text
    $prodID = $rowData[0];
    $geoID = $rowData[1];
    $timeID = $rowData[2];
    $rowData[0] = "\"$prodNameHash{$rowData[0]}\"";
    $rowData[1] = "\"$geoNameHash{$rowData[1]}\"";
    $rowData[2] = "\"$timeNameHash{$rowData[2]}\"";

    #skip over children of merged products
    if ($mergedProdChildrenHash{$prodID} > 0)
    {
      next;
    }

    #write out the measure data
    foreach $data (@rowData)
    {
      print OUTPUT "$data,";
    }

    #output segment name for each segmentation
    $idx = 0;
    foreach $segID (@prodSegIDs)
    {
      $segmentID = $prodSegs[$idx]->{$prodID};
      $segmentID = "SMT_" . $segmentID;
      print OUTPUT "\"$prodNameHash{$segmentID}\",";
      $idx++;
    }

    $idx = 0;
    foreach $segID (@geoSegIDs)
    {
      $segmentID = $geoSegs[$idx]->{$geoID};
      $segmentID = "SMT_" . $segmentID;
      print OUTPUT "\"$geoNameHash{$segmentID}\",";
      $idx++;
    }

    $idx = 0;
    foreach $segID (@timeSegIDs)
    {
      $segmentID = $timeSegs[$idx]->{$timeID};
      $segmentID = "SMT_" . $segmentID;
      print OUTPUT "\"$timeNameHash{$segmentID}\",";
      $idx++;
    }

    if (defined($upcID))
    {
      print OUTPUT "$upcHash{$prodID},";
    }

    print OUTPUT "\n";
  }

  close(OUTPUT);

  #compress the output file for download
  unlink("$zipFile");
  `/usr/bin/zip $zipFile $filename`;
  unlink("$filename");

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-10 col-lg-7 col-xl-6"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Export Data Source</DIV>
        <DIV CLASS="card-body">

          The data source has been exported as a compressed CSV file.

          <P>&nbsp;</P>

          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-success" TYPE="button" onClick="location.href='/tmp/$zipFile'"><I CLASS="bi bi-download"></I> Download</BUTTON>
          </DIV>

          <P>&nbsp;</P>

          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" TYPE="buton" onClick="location.href='display.cld?ds=$dsID&dim=$dim'"><I CLASS="bi bi-check-lg"></I> Done</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
