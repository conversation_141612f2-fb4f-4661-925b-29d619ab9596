#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;
use Socket;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::PrepUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Analytics Platform Job Monitor</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<SCRIPT SRC="/fusioncharts-3.18.0/js/fusioncharts.js"></SCRIPT>
<SCRIPT SRC="/fusioncharts-3.18.0/js/themes/fusioncharts.theme.fusion.js"></SCRIPT>

</HEAD>

<BODY>

END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">Analytics Platform</A></LI>
    <LI CLASS="breadcrumb-item active">Active Job & Cloud Resource Monitor</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;
  $dsID = $q->param('ds');

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #connect to the database
  $db = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  print_html_header();

  #get performance info and output gauge HTML/JS
  $query = "SELECT jobs, cpu, storage, storageThroughput, memory \
      FROM app.performance WHERE instance='kap'";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($activeJobs, $cpu, $storage, $storageThroughput, $memory) = $dbOutput->fetchrow_array;
  $jobWarnThreshold = $Lib::KoalaConfig::cores * 0.75;
  $overuseThreshold = $Lib::KoalaConfig::cores;

  if ($storage >= 90)
  {
    $storageColor = "#f2726f";
  }
  elsif ($storage >= 85)
  {
    $storageColor = "#FFC533";
  }
  else
  {
    $storageColor = "#62B58F";
  }

  print <<END_HTML;
  <SCRIPT>
  FusionCharts.ready(function()
  {
    let jobsBulb = new FusionCharts(
    {
      type: "bulb",
      id: "gauge-jobs-bulb",
      renderAt: "div-gauge-jobs-bulb",
      width: "150",
      height: "175",
      dataFormat: "json",
      dataSource:
      {
        chart:
        {
          caption: "Jobs",
          upperlimit: "$Lib::KoalaConfig::prepCores",
          lowerlimit: "0",
          numberSuffix: " jobs",
          useColorNameAsValue: "1",
          placeValuesInside: "1",
          plottooltext: "Active Jobs: <b>\$dataValue</b>",
          theme: "fusion"
        },
        colorrange:
        {
          color: [
          {
            minvalue: "0",
            maxvalue: "$jobWarnThreshold",
            label: "Normal",
            code: "#62B58F"
          },
          {
            minvalue: "$jobWarnThreshold",
            maxvalue: "$Lib::KoalaConfig::prepCores",
            label: "Heavy Usage",
            code: "#FFC533"
          },
          {
            minvalue: "$overuseThreshold",
            maxvalue: "100",
            label: "Over Used",
            code: "#F2726F"
          }]
        },

        value: "$activeJobs"
      },
      events:
      {
        rendered: function(evtObj, argObj)
        {
          let chartRef = evtObj.sender;
          chartRef.intervalUpdateId = setInterval(function()
          {
            \$.get('/app/dsr/ajaxAPI.cld?svc=perf_jobs', function(data, status)
            {
              chartRef.feedData(data);
            });
          }, 60000);
        },
        disposed: function(evt, args)
        {
          clearInterval(evt.sender.intervalUpdateId);
        }
      }
    }).render();

    let cpuAngular = new FusionCharts(
    {
      type: "angulargauge",
      id: "gauge-cpu-angular",
      renderAt: "div-gauge-cpu-angular",
      width: "150",
      height: "175",
      dataFormat: "json",
      dataSource:
      {
        chart:
        {
          caption: "CPU",
          upperlimit: "100",
          lowerlimit: "0",
          numberSuffix: "%",
          showValue: "1",
          showTickValues: "0",
          plottooltext: "\$dataValue",
          theme: "fusion"
        },
        colorrange:
        {
          color: [
          {
            minvalue: "0",
            maxvalue: "75",
            code: "#62B58F"
          },
          {
            minvalue: "75",
            maxvalue: "90",
            code: "#FFC533"
          },
          {
            minvalue: "90",
            maxvalue: "100",
            code: "#F2726F"
          }]
        },
        "dials":
        {
          "dial": [
          {
            "value": "$cpu"
          }]
        }
      },
      events:
      {
        rendered: function(evtObj, argObj)
        {
          let chartRef = evtObj.sender;
          chartRef.intervalUpdateId = setInterval(function()
          {
            \$.get('/app/dsr/ajaxAPI.cld?svc=perf_cpu', function(data, status)
            {
              chartRef.feedData(data);
            });
          }, 60000);
        },
        disposed: function(evt, args)
        {
          clearInterval(evt.sender.intervalUpdateId);
        }
      }
    }).render();

    let memoryAngular = new FusionCharts(
    {
      type: "angulargauge",
      id: "gauge-memory-angular",
      renderAt: "div-gauge-memory-angular",
      width: "150",
      height: "175",
      dataFormat: "json",
      dataSource:
      {
        chart:
        {
          caption: "Memory",
          upperlimit: "100",
          lowerlimit: "0",
          numberSuffix: "%",
          showValue: "1",
          showTickValues: "0",
          plottooltext: "\$dataValue",
          theme: "fusion"
        },
        colorrange:
        {
          color: [
          {
            minvalue: "0",
            maxvalue: "90",
            code: "#62B58F"
          },
          {
            minvalue: "95",
            maxvalue: "100",
            code: "#F2726F"
          }]
        },
        "dials":
        {
          "dial": [
          {
            "value": "$memory"
          }]
        }
      },
      events:
      {
        rendered: function(evtObj, argObj)
        {
          let chartRef = evtObj.sender;
          chartRef.intervalUpdateId = setInterval(function()
          {
            \$.get('/app/dsr/ajaxAPI.cld?svc=perf_memory', function(data, status)
            {
              chartRef.feedData(data);
            });
          }, 60000);
        },
        disposed: function(evt, args)
        {
          clearInterval(evt.sender.intervalUpdateId);
        }
      }
    }).render();

    let throughputAngular = new FusionCharts(
    {
      type: "angulargauge",
      id: "gauge-throughput-angular",
      renderAt: "div-gauge-throughput-angular",
      width: "150",
      height: "175",
      dataFormat: "json",
      dataSource:
      {
        chart:
        {
          caption: "Throughput",
          upperlimit: "100",
          lowerlimit: "0",
          numberSuffix: "%",
          showValue: "1",
          showTickValues: "0",
          plottooltext: "\$dataValue",
          theme: "fusion"
        },
        colorrange:
        {
          color: [
          {
            minvalue: "0",
            maxvalue: "75",
            code: "#62B58F"
          },
          {
            minvalue: "75",
            maxvalue: "90",
            code: "#FFC533"
          },
          {
            minvalue: "90",
            maxvalue: "100",
            code: "#F2726F"
          }]
        },
        "dials":
        {
          "dial": [
          {
            "value": "$storageThroughput"
          }]
        }
      },
      events:
      {
        rendered: function(evtObj, argObj)
        {
          let chartRef = evtObj.sender;
          chartRef.intervalUpdateId = setInterval(function()
          {
            \$.get('/app/dsr/ajaxAPI.cld?svc=perf_throughput', function(data, status)
            {
              chartRef.feedData(data);
            });
          }, 60000);
        },
        disposed: function(evt, args)
        {
          clearInterval(evt.sender.intervalUpdateId);
        }
      }
    }).render();

    let storageCylinder = new FusionCharts(
    {
      type: "cylinder",
      id: "gauge-storage-cylinder",
      renderAt: "div-gauge-storage-cylinder",
      width: "150",
      height: "175",
      dataFormat: "json",
      dataSource:
      {
        chart:
        {
          caption: "Storage",
          upperlimit: "100",
          lowerlimit: "0",
          numberSuffix: "%",
          showTickMarks: "0",
          showValue: "1",
          theme: "fusion",
          cylFillColor: "$storageColor",
        },
        value: "$storage"
      },
      events:
      {
        rendered: function(evtObj, argObj)
        {
          let chartRef = evtObj.sender;
          chartRef.intervalUpdateId = setInterval(function()
          {
            \$.get('/app/dsr/ajaxAPI.cld?svc=perf_storage', function(data, status)
            {
              chartRef.feedData(data);
            });
          }, 60000);
        },
        disposed: function(evt, args)
        {
          clearInterval(evt.sender.intervalUpdateId);
        }
      }
    }).render();

  });
  </SCRIPT>
END_HTML

    #get list of jobs being run inside Data Prep
    $query = "SELECT PID FROM prep.jobs WHERE STATE NOT IN ('LOADED', 'ERROR')";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    while (($pid) = $dbOutput->fetchrow_array)
    {
      $prepJobPIDHash{$pid} = 1;
    }

  #see if there's any current jobs
  $query = "SELECT PID, userID, dsID, cubeID, analyticsID, lastAction, operation, state, opInfo, status, dsName, rptName \
      FROM app.jobs ORDER BY operation";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;

  #output the panel header HTML
  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-auto"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Koala Analytics Job & Cloud Resource Monitor</DIV>
        <DIV CLASS="card-body">

        <div class="container">
          <div class="row no-gutters">

            <div class="col">
              <div id="div-gauge-jobs-bulb"></div>
            </div>
            <div class="col">
              <div id="div-gauge-cpu-angular"></div>
            </div>
            <div class="col">
              <div id="div-gauge-memory-angular"></div>
            </div>
            <div class="col">
              <div id="div-gauge-throughput-angular"></div>
            </div>
            <div class="col">
              <div id="div-gauge-storage-cylinder"></div>
            </div>

          </div>
        </div>
END_HTML


  #if there aren't any jobs, let the user know and finish
  if ($status < 1)
  {
    print <<END_HTML;
          There aren't any active jobs right now.
END_HTML
  }

  #else there's at least one job
  else
  {
    print <<END_HTML;
          <DIV CLASS="table-responsive">
          <TABLE CLASS="table table-condensed table-striped table-sm">
            <THEAD><TR>
              <TH>User</TH>
              <TH>Job</TH>
              <TH>Status</TH>
            </TR></THEAD>
END_HTML

  while (($jobPID, $jobUserID, $dsID, $cubeID, $analyticsID, $lastAction, $operation, $state, $opInfo, $jobStatus, $dsName, $rptName) = $dbOutput->fetchrow_array)
  {

    #set defaults for color/user/etc
    $rowColor = "table-secondary";
    if ($jobUserID == 0)
    {
      $jobUserID = "Koala Automation";
      if ($prepJobPIDHash{$jobPID} == 1)
      {
        $jobUserID = "Data Prep Scheduler";
      }
    }
    else
    {
      $jobUserID = utils_userID_to_name($db, $jobUserID);
    }

    if ($operation eq "ADD-MEASURE")
    {
      $operation = "Adding a calculated measure to <A CLASS='text-decoration-none' HREF='/app/dsr/display.cld?ds=$dsID'>$dsName</A>";
    }
    elsif ($operation eq "ANALYTICS-PRICE")
    {
      $operation = "Updating pricing model in <A CLASS='text-decoration-none' HREF='/app/dsr/display.cld?ds=$dsID'>$dsName</A>";
    }
    elsif ($operation eq "AUTO-RPTS")
    {
      $operation = "Creating automated reports in <A CLASS='text-decoration-none' HREF='/app/dsr/display.cld?ds=$dsID'>$dsName</A>";
      $rowColor = "table-primary";
    }
    elsif ($operation eq "BACKUP")
    {
      $operation = "Backing up data source <A CLASS='text-decoration-none' HREF='/app/dsr/display.cld?ds=$dsID'>$dsName</A>";
      $jobUserID = "Backup Agent";
    }
    elsif ($operation eq "COPY-DS")
    {
      $operation = "Copying a data source";
    }
    elsif ($operation eq "CUBE-UPDATE")
    {
      $operation = "Refreshing report <A CLASS='text-decoration-none' HREF='/app/rpt/display.cld?rpt=$cubeID'>$rptName</A> in <A CLASS='text-decoration-none' HREF='/app/dsr/display.cld?ds=$dsID'>$dsName</A>";
      $rowColor = "table-primary";
    }
    elsif ($operation eq "REPORT-EXPORT")
    {
      $operation = "Exporting reports";
      $rowColor = "table-primary";
    }
    elsif ($operation eq "DS-UPDATE")
    {
      $operation = "Updating data source <A CLASS='text-decoration-none' HREF='/app/dsr/display.cld?ds=$dsID'>$dsName</A>";
      $rowColor = "table-success";
    }
    elsif ($operation eq "FORCE-REFRESH")
    {
      $operation = "Forcing refresh of data source <A CLASS='text-decoration-none' HREF='/app/dsr/display.cld?ds=$dsID'>$dsName</A>";
    }
    elsif ($operation eq "FORECAST")
    {
      $operation = "Updating forecast in <A CLASS='text-decoration-none' HREF='/app/dsr/display.cld?ds=$dsID'>$dsName</A>";
    }
    elsif ($operation eq "MERGE-ITEMS")
    {
      $operation = "Merging items in data source <A CLASS='text-decoration-none' HREF='/app/dsr/display.cld?ds=$dsID'>$dsName</A>";
    }
    elsif ($operation eq "ODBC")
    {
      $operation = "Refreshing ODBC export for <A CLASS='text-decoration-none' HREF='/app/dsr/display.cld?ds=$dsID'>$dsName</A>";
      $rowColor = "table-warning";
      $jobUserID = "ODBC Agent";
    }
    elsif ($operation eq "OPTIMIZE")
    {
      $operation = "Optimizing data source <A CLASS='text-decoration-none' HREF='/app/dsr/display.cld?ds=$dsID'>$dsName</A>";
    }
    elsif ($operation eq "ROLLBACK")
    {
      $operation = "Rolling back update from <A CLASS='text-decoration-none' HREF='/app/dsr/display.cld?ds=$dsID'>$dsName</A>";
    }
    elsif (($operation eq "XFER-STRUCTS") || ($operation eq "XFER-MEASURES"))
    {
      $operation = "Transferring structures into <A CLASS='text-decoration-none' HREF='/app/dsr/display.cld?ds=$dsID'>$dsName</A>";
    }

    if ($waitState == 1)
    {
      $rowColor = "table-secondary";
    }

    print <<END_HTML;
            <TR CLASS="$rowColor">
              <TD NOWRAP>$jobUserID</TD>
              <TD>$operation</TD>
              <TD>$jobStatus</TD>
            </TR>
END_HTML
    }

    print <<END_HTML;
          </TABLE>
          </DIV>
END_HTML
  }

  #output list of ODBC connections
  $query = "show processlist";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;

  while (@processDetails = $dbOutput->fetchrow_array)
  {

    #skip everything being used by the Koala system
    if ($processDetails[1] eq "app")
    {
      next;
    }

    if ($processDetails[3] =~ m/^datasource_(\d+)$/i)
    {
      $dsID = $1;
      $processDetails[3] = ds_id_to_name($db, $dsID);

      if ($processDetails[2] =~ m/^([\d\.]+)\:(\d+)$/)
      {
        $processDetails[2] = $1;
        $nsData = `/usr/bin/nslookup $processDetails[2]`;

        if ($nsData =~ m/\.res\.spectrum\.com/)
        {
          $processDetails[2] = "Spectrum Residential";
        }
      }

      elsif ($processDetails[2] =~ m/\.amazonaws.com\:/i)
      {
        $processDetails[2] = "Amazon AWS";
      }
      elsif ($processDetails[2] =~ m/\.res.spectrum.com\:/i)
      {
        $processDetails[2] = "Spectrum Residential";
      }
      else
      {
        $processDetails[2] = "Unknown ($processDetails[2])";
      }

      $HTMLline = "<TD>$processDetails[2]</TD>";
      $HTMLline .= "<TD><A CLASS='text-decoration-none' HREF='/app/dsr/display.cld?ds=$dsID'>$processDetails[3]</A></TD>\n";
      push(@ODBCconnections, $HTMLline);
    }
  }

  if (@ODBCconnections > 0)
  {
    print <<END_HTML;
          <P>&nbsp;</P>
          <H4>Active External Data Connections</H4>
          <TABLE CLASS="table table-striped table-sm">
END_HTML

    foreach $HTMLline (@ODBCconnections)
    {
      print <<END_HTML;
            <TR>$HTMLline</TR>
END_HTML
    }

    print <<END_HTML;
          </TABLE>
END_HTML
  }

  print <<END_HTML;
<SCRIPT>
\$(function()
{
  \$('[data-toggle="popover"]').popover()
})
</SCRIPT>

          <P>
          <CENTER>
            <BUTTON CLASS="btn btn-primary" TYPE="button" onclick="location.href='main.cld'"><I CLASS="bi bi-check-lg"></I> Done</BUTTON>
          </CENTER>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
<P>
END_HTML

  print_html_footer();


#EOF
