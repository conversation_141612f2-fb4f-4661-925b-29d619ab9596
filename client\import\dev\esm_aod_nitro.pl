#!/usr/bin/perl

use Text::CSV;

#Convert AOD measure and geography names to Nitro format


  #define our hash of AOD to Nitro geography names
  %geoMap = (
"Ahold USA Corp ex Rchmd Total TA" => "AHOLD USA TOTAL CORP CTA EX RCH",
"Ahold USA Corp ex Rchmd Total Rem" => "AHOLD USA TOTAL CORP REM MKT EX RCH",
"ALB Denver Div Cluster" => "ALB DENVER DIV CENSUS CLUSTER",
"ALB Houston Div Cluster" => "ALB HOUSTON DIV CENSUS CLUSTER",
"ALB Intermountain Div Cluster" => "ALB INTERMOUNTAIN DIV CENSUS CLUSTER",
"ALB Portland Div Cluster" => "ALB PORTLAND DIV CENSUS CLUSTER",
"ALB Seattle Div Cluster" => "ALB SEATTLE DIV CENSUS CLUSTER",
"ALB So Cal Div Cluster" => "ALB SOCAL DIV CENSUS CLUSTER",
"ALB Southern Div Cluster" => "ALB SOUTHERN DIV CENSUS CLUSTER",
"ALB Southwest Div Cluster" => "ALB SOUTHWEST DIV CENSUS CLUSTER",
"ALB Total Div Cluster" => "ALB TOTAL CENSUS CLUSTER",
"ALB/SFY Acme Div TA" => "ALB/SFY ACME DIV CEN TA",
"ALB/SFY Acme Div Rem" => "ALB/SFY ACME DIV REM FOOD",
"ALB/SFY Acme Philadelphia TA" => "ALB/SFY ACME PHILADELPHIA CEN TA",
"ALB/SFY Acme Philadelphia Rem" => "ALB/SFY ACME PHILADELPHIA REM FOOD",
"ALB/SFY Alaska Cluster" => "ALB/SFY ALASKA CENSUS CLUSTER",
"ALB/SFY All Oth Denver Div TA" => "ALB/SFY ALL OTH DENVER DIV CEN TA",
"ALB/SFY All Oth Denver Div Rem" => "ALB/SFY ALL OTH DENVER DIV REM FOOD",
"ALB/SFY All Oth Nor Cal Div TA" => "ALB/SFY ALL OTH NOR CAL DIV CEN TA",
"ALB/SFY All Oth Nor Cal Div Rem" => "ALB/SFY ALL OTH NOR CAL DIV REM FOOD",
"ALB/SFY All Oth Portland Div TA" => "ALB/SFY ALL OTH PORTLND DIV CEN TA",
"ALB/SFY All Oth Portland Div Rem" => "ALB/SFY ALL OTH PORTLND DIV REM FOOD",
"ALB/SFY Bay Area TA" => "ALB/SFY BAY AREA CEN TA",
"ALB/SFY Bay Area Rem" => "ALB/SFY BAY AREA REM FOOD",
"ALB/SFY Big Sky TA" => "ALB/SFY BIG SKY CEN TA",
"ALB/SFY Big Sky Rem" => "ALB/SFY BIG SKY REM FOOD",
"ALB/SFY Dal & Ft Wth TA" => "ALB/SFY DALLAS/FT WORTH CEN TA",
"ALB/SFY Dal & Ft Wth Rem" => "ALB/SFY DALLAS/FT WORTH REM FOOD",
"ALB/SFY Denver TA" => "ALB/SFY DENVER CEN TA",
"ALB/SFY Denver Div TA" => "ALB/SFY DENVER DIV CEN TA",
"ALB/SFY Denver Div Rem" => "ALB/SFY DENVER DIV REM FOOD",
"ALB/SFY Denver Rem" => "ALB/SFY DENVER REM FOOD",
"ALB/SFY Eastern Div TA" => "ALB/SFY EASTERN DIV CEN TA",
"ALB/SFY Eastern Div Rem" => "ALB/SFY EASTERN DIV REM FOOD",
"ALB/SFY Hawaii Cluster" => "ALB/SFY HAWAII CENSUS CLUSTER",
"ALB/SFY Houston TA" => "ALB/SFY HOUSTON CEN TA",
"ALB/SFY Houston Div TA" => "ALB/SFY HOUSTON DIV CEN TA",
"ALB/SFY Houston Div Rem" => "ALB/SFY HOUSTON DIV REM FOOD",
"ALB/SFY Houston Rem" => "ALB/SFY HOUSTON REM FOOD",
"ALB/SFY Idaho TA" => "ALB/SFY IDAHO CEN TA",
"ALB/SFY Idaho Rem" => "ALB/SFY IDAHO REM FOOD",
"ALB/SFY Inland Empire TA" => "ALB/SFY INLAND EMPIRE CEN TA",
"ALB/SFY Inland Empire Rem" => "ALB/SFY INLAND EMPIRE REM FOOD",
"ALB/SFY Intermountain Div TA" => "ALB/SFY INTERMOUNTAIN DIV CEN TA",
"ALB/SFY Intermountain Div Rem" => "ALB/SFY INTERMOUNTAIN DIV REM FOOD",
"ALB/SFY Jewel Chicago TA" => "ALB/SFY JEWEL CHICAGO CEN TA",
"ALB/SFY Jewel Chicago Rem" => "ALB/SFY JEWEL CHICAGO REM FOOD",
"ALB/SFY Jewel Div TA" => "ALB/SFY JEWEL DIV CEN TA",
"ALB/SFY Jewel Div Rem" => "ALB/SFY JEWEL DIV REM FOOD",
"ALB/SFY Las Vegas TA" => "ALB/SFY LAS VEGAS CEN TA",
"ALB/SFY Las Vegas Rem" => "ALB/SFY LAS VEGAS REM FOOD",
"ALB/SFY Los Angeles County TA" => "ALB/SFY LOS ANGELES COUNTY CEN TA",
"ALB/SFY Los Angeles County Rem" => "ALB/SFY LOS ANGELES COUNTY REM FOOD",
"ALB/SFY Louisiana TA" => "ALB/SFY LOUISIANA CEN TA",
"ALB/SFY Louisiana Rem" => "ALB/SFY LOUISIANA REM FOOD",
"ALB/SFY Nor Cal Div TA" => "ALB/SFY NOR CAL DIV CEN TA",
"ALB/SFY Nor Cal Div Rem" => "ALB/SFY NOR CAL DIV REM FOOD",
"ALB/SFY Phoenix/Tucson TA" => "ALB/SFY PHOENIX TUCSON CEN TA",
"ALB/SFY Phoenix/Tucson Rem" => "ALB/SFY PHOENIX TUCSON REM FOOD",
"ALB/SFY Portland TA" => "ALB/SFY PORTLAND CEN TA",
"ALB/SFY Portland Div TA" => "ALB/SFY PORTLAND DIV CEN TA",
"ALB/SFY Portland Div Rem" => "ALB/SFY PORTLAND DIV REM FOOD",
"ALB/SFY Portland Rem" => "ALB/SFY PORTLAND REM FOOD",
"ALB/SFY San Diego TA" => "ALB/SFY SAN DIEGO CEN TA",
"ALB/SFY San Diego Rem" => "ALB/SFY SAN DIEGO REM FOOD",
"ALB/SFY Seattle TA" => "ALB/SFY SEATTLE CEN TA",
"ALB/SFY Seattle Div TA" => "ALB/SFY SEATTLE DIV CEN TA",
"ALB/SFY Seattle Div Rem" => "ALB/SFY SEATTLE DIV REM FOOD",
"ALB/SFY Seattle Rem" => "ALB/SFY SEATTLE REM FOOD",
"ALB/SFY Shaws Div TA" => "ALB/SFY SHAWS DIV CEN TA",
"ALB/SFY Shaws Div Rem" => "ALB/SFY SHAWS DIV REM FOOD",
"ALB/SFY Shaws Mass/Rhode Island TA" => "ALB/SFY SHAWS MASS/RHODE ISLAND CEN TA",
"ALB/SFY Shaws Mass/Rhode Island Rem" => "ALB/SFY SHAWS MASS/RHODE ISLAND REM FOOD",
"ALB/SFY Shaws New England TA" => "ALB/SFY SHAWS NEW ENGLAND CEN TA",
"ALB/SFY Shaws New England Rem" => "ALB/SFY SHAWS NEW ENGLAND REM FOOD",
"ALB/SFY So Cal Div TA" => "ALB/SFY SO CAL DIV CEN TA",
"ALB/SFY So Cal Div Rem" => "ALB/SFY SO CAL DIV REM FOOD",
"ALB/SFY Southern Div TA" => "ALB/SFY SOUTHERN DIV CEN TA",
"ALB/SFY Southern Div Rem" => "ALB/SFY SOUTHERN DIV REM FOOD",
"ALB/SFY Southwest Div TA" => "ALB/SFY SOUTHWEST DIV CEN TA",
"ALB/SFY Southwest Div Rem" => "ALB/SFY SOUTHWEST DIV REM FOOD",
"ALB/SFY Total Company TA" => "ALB/SFY TOTAL COMPANY CENSUS TA",
"ALB/SFY Total Company Rem" => "ALB/SFY TOTAL COMPANY REM FOOD",
"ALB/SFY United Div TA" => "ALB/SFY UNITED DIV CEN TA",
"ALB/SFY United Div Rem" => "ALB/SFY UNITED DIV REM FOOD",
"AWG Dallas & Ft Worth Rem" => "AWG DALLAS/FT WORTH REM MKT",
"AWG Dallas & Ft Worth TA" => "AWG DALLAS/FT WORTH TRADING AREA",
"AWG Kansas City Rem" => "AWG KANSAS CITY REM MKT",
"AWG Kansas City TA" => "AWG KANSAS CITY TRADING AREA",
"AWG Memphis Rem" => "AWG MEMPHIS REM MKT",
"AWG Memphis TA" => "AWG MEMPHIS TRADING AREA",
"AWG Nashville Rem" => "AWG NASHVILLE REM MKT",
"AWG Nashville TA" => "AWG NASHVILLE TRADING AREA",
"AWG Oklahoma City TA" => "AWG OKLAHOMA TRADING AREA",
"AWG Springfield Rem" => "AWG SPRINGFIELD REM MKT",
"AWG Springfield TA" => "AWG SPRINGFIELD TRADING AREA",
"B & B Foods TA" => "B & B FOODS CENSUS TA",
"Bashas Total TA" => "BASHAS CENSUS TRADING AREA",
"Bashas Rem" => "BASHAS COMP MINUS BASHAS",
"Bashas Total Rem" => "BASHAS INC. TTL CENSUS COMP MINUS BASHAS",
"Bashas TA" => "BASHAS INC. TTL CENSUS TRADING AREA",
"Big Y Total TA" => "BIG Y TOTAL CENSUS TRADING AREA",
"BI-LO East Region TA" => "BI-LO EAST REGION CENSUS TA",
"BI-LO Total Regions TA" => "BI-LO TOTAL REGIONS CENSUS TA",
"BI-LO West Region TA" => "BI-LO WEST REGION CENSUS TA",
"Brookshire Brothers Banner TA" => "BROOKSHIRE BROS BANNER CENSUS TA",
"Brookshire Brothers Total TA" => "BROOKSHIRE BROTHERS TOTAL CENSUS TA",
"Brookshire Brothers Total Rem" => "BROOKSHIRE BROTHERS TOTAL REM MKT",
"Cashwise Total Banner TA" => "CASHWISE TOTAL BANNER CENSUS TA",
"Central Grocers Total Rem" => "CENTRAL GROCERS TOTAL COMP MINUS TA",
"Central Grocers Total TA" => "CENTRAL GROCERS TOTAL TRADING AREA",
"Publix Charlotte Rem" => "CHAR PBLX CEN CMP MINUS CHAR PBLX",
"Giant Eagle Cleveland Rem" => "CLEVELAND GIANT EAGLE COMP MKT MINUS GE",
"Coborns Cashwise Total Corp TA" => "COBORNS CASHWISE TOTAL CORP CENSUS TA",
"Coborns Cashwise Total Corp Rem" => "COBORNS CASHWISE TOTAL CORP REM MKT",
"Coborns Total Banner TA" => "COBORNS TOTAL BANNER CENSUS TA",
"Giant Eagle Columbus Rem" => "COLUMBUS GIANT EAGLE COMP MKT MINUS GE",
"Cub Minneapolis TA" => "CUB MINNEAPOLIS CENSUS TA",
"Cub Minneapolis Rem" => "CUB MINNEAPOLIS REM MKT",
"Cub Total TA" => "CUB TOTAL CENSUS TA",
"Cub Total Rem" => "CUB TOTAL REM MKT",
"DeMoulas Total TA" => "DEMOULAS TOTAL CENSUS TRADING AREA",
"DeMoulas Total Rem" => "DEMOULAS TOTAL T.A. MINUS DEMOULAS",
"El Super Total TA" => "EL SUPER TOTAL CENSUS TRADE AREA",
"El Super Total Rem" => "EL SUPER TOTAL REM MKT",
"Fareway Des Moines TA" => "FAREWAY DES MOINES CENSUS TA",
"Fareway Des Moines Rem" => "FAREWAY DES MOINES REM MKT",
"Fareway Omaha TA" => "FAREWAY OMAHA CENSUS TA",
"Fareway Omaha Rem" => "FAREWAY OMAHA REM MKT",
"Fareway Total TA" => "FAREWAY TOTAL CENSUS TA",
"Fareway Total Rem" => "FAREWAY TOTAL REM MKT",
"Farm Fresh Total TA" => "FARM FRESH TOTAL CENSUS TA",
"Farm Fresh Total Rem" => "FARM FRESH TOTAL REM MKT",
"Fiesta Dal & Ft Worth TA" => "FIESTA DALLAS-FT WORTH CENSUS TA",
"Fiesta Dal & Ft Worth Rem" => "FIESTA DALLAS-FT WORTH COMP MKT MINUS TA",
"Fiesta Houston TA" => "FIESTA HOUSTON CENSUS TRADING AREA",
"Fiesta Houston Rem" => "FIESTA HOUSTON COMP MKT MINUS TA",
"Fiesta Total TA" => "FIESTA TOTAL CENSUS TRADING AREA",
"Fiesta Total Rem" => "FIESTA TOTAL COMP MKT MINUS TA",
"Food Lion Baltimore TA" => "FOOD LION BALTIMORE CTA",
"Food Lion Baltimore Rem" => "FOOD LION BALTIMORE REM MKT",
"Food Lion Charlotte TA" => "FOOD LION CHARLOTTE CTA",
"Food Lion Charlotte Rem" => "FOOD LION CHARLOTTE REM MKT",
"Food Lion DC 10 TA" => "FOOD LION DC 10 CTA",
"Food Lion DC 10 Rem" => "FOOD LION DC 10 REM MKT",
"Food Lion DC 20 TA" => "FOOD LION DC 20 CTA",
"Food Lion DC 20 Rem" => "FOOD LION DC 20 REM MKT",
"Food Lion DC 30 TA" => "FOOD LION DC 30 CTA",
"Food Lion DC 30 Rem" => "FOOD LION DC 30 REM MKT",
"Food Lion DC 4 TA" => "FOOD LION DC 4 CTA",
"Food Lion DC 4 Rem" => "FOOD LION DC 4 REM MKT",
"Food Lion DC 7 TA" => "FOOD LION DC 7 CTA",
"Food Lion DC 7 Rem" => "FOOD LION DC 7 REM MKT",
"Food Lion Greensboro TA" => "FOOD LION GREENSBORO CTA",
"Food Lion Greensboro Rem" => "FOOD LION GREENSBORO REM MKT",
"Food Lion Norfolk TA" => "FOOD LION NORFOLK CTA",
"Food Lion Norfolk Rem" => "FOOD LION NORFOLK REM MKT",
"Food Lion Raleigh TA" => "FOOD LION RALEIGH CTA",
"Food Lion Raleigh Rem" => "FOOD LION RALEIGH REM MKT",
"Food Lion Richmond TA" => "FOOD LION RICHMOND CTA",
"Food Lion Richmond Rem" => "FOOD LION RICHMOND REM MKT",
"Food Lion Total TA" => "FOOD LION TOTAL CTA",
"Food Lion Total Rem" => "FOOD LION TOTAL REM MKT",
"Food Lion Washington TA" => "FOOD LION WASHINGTON CTA",
"Food Lion Washington Rem" => "FOOD LION WASHINGTON REM MKT",
"Giant Carlisle Richmond Cluster" => "GIANT CARLISLE RICHMOND CENSUS CLUSTER",
"Giant Carlisle ex Rchmd Total TA" => "GIANT CARLISLE TOTAL CTA EX RCH",
"Giant Carlisle ex Rchmd Total Rem" => "GIANT CARLISLE TOTAL REM MKT EX RCH",
"Giant Eagle Cleveland TA" => "GIANT EAGLE CLEVELAND CENSUS TA",
"Giant Eagle Columbus TA" => "GIANT EAGLE COLUMBUS CENSUS TA",
"Giant Eagle Pittsburgh TA" => "GIANT EAGLE PITTSBURGH CENSUS TA",
"Giant Eagle Total TA" => "GIANT EAGLE TOTAL CENSUS TRADING AREA",
"Giant Landover Total TA" => "GIANT LANDOVER TOTAL CTA",
"Giant Landover Total Rem" => "GIANT LANDOVER TOTAL REM MKT",
"Hannaford MA TA" => "HANNAFORD MA CTA",
"Hannaford MA Rem" => "HANNAFORD MA REM MKT",
"Hannaford ME & NH TA" => "HANNAFORD ME/NH CTA",
"Hannaford ME & NH Rem" => "HANNAFORD ME/NH REM MKT",
"Hannaford NY & VT TA" => "HANNAFORD NY/VT CTA",
"Hannaford NY & VT Rem" => "HANNAFORD NY/VT REM MKT",
"Hannaford Total TA" => "HANNAFORD TOTAL CTA",
"Hannaford Total Rem" => "HANNAFORD TOTAL REM MKT",
"Harps Total TA" => "HARPS TOTAL CENSUS TRADING AREA",
"Harps Total Rem" => "HARPS TOTAL COMP MKT MINUS HARPS",
"Harveys Total Region TA" => "HARVEYS TOTAL REGION CENSUS TA",
"Heinen's Total TA" => "HEINEN'S TOTAL CENSUS TRADING AREA",
"Heinen's Total Rem" => "HEINEN'S TOTAL REM MKT",
"Hornbachers Total TA" => "HORNBACHERS TOTAL CENSUS CLUSTER",
"Hy-Vee Total TA" => "HY VEE TOTAL CENSUS TRADING AREA",
"Ingles Total Rem" => "INGLES TOTAL CMP MKT MINUS TA",
"Ingles Total TA" => "INGLES TOTAL TRADING AREA",
"Publix Jacksonville Rem" => "JACKSVL PBLX CEN CMP MINUS JACKSVL PBLX",
"KVAT Food City TA" => "KVAT TOTAL CENSUS TRADING AREA",
"Publix Lakeland Rem" => "LAKELND PBLX CEN CMP MINUS LAKELND PBLX",
"Lowes Food Banner Total TA" => "LOWES FOOD BANNER TOTAL CENSUS TA",
"Lucky Ttl + Save Mart Coastal TA" => "LUCKY PLUS SAVE MART COASTAL CENSUS TA",
"Lucky Ttl + Save Mart Coastal Rem" => "LUCKY PLUS SAVE MART COASTAL REM MKT",
"Lunds & Byerlys Total TA" => "LUNDS AND BYERLYS TOTAL CENSUS TA",
"Lunds & Byerlys Total Rem" => "LUNDS AND BYERLYS TTL COMP MKT MINUS TA",
"Martins Total TA" => "MARTIN'S TOTAL CENSUS TA",
"Martins Total Rem" => "MARTIN'S TOTAL RM",
"Publix Miami Rem" => "MIAMI PBLX CEN CMP MINUS MIAMI PBLX",
"Piggly Wiggly Carolina Total TA" => "PIGGLY WIGGLY CAROLINA TOTAL CENSUS CTA",
"Piggly Wiggly Carolina Total Rem" => "PIGGLY WIGGLY CAROLINA TOTAL REM MKT",
"Giant Eagle Pittsburgh Rem" => "PITTSBURGH GIANT EAGLE COMP MKT MINUS GE",
"Price Chopper Enterprises Total TA" => "PRICE CHOPPER TOTAL CENSUS TRADING AREA",
"Publix Atlanta TA" => "PUBLIX ATLANTA CENSUS TRADING AREA",
"Publix Charlotte TA" => "PUBLIX CHARLOTTE CENSUS TRADING AREA",
"Publix Jacksonville TA" => "PUBLIX JACKSONVILLE CENSUS TRADING AREA",
"Publix Lakeland TA" => "PUBLIX LAKELAND CENSUS TRADING AREA",
"Publix Miami TA" => "PUBLIX MIAMI CENSUS TRADING AREA",
"Publix Total TA" => "PUBLIX TOTAL CENSUS TRADING AREA",
"Raleys & Bel Air Banners Total TA" => "RALEYS & BEL AIR CTA",
"Raleys & Bel Air Banners Total Rem" => "RALEYS & BEL AIR REM MKT",
"Raleys & Bel Air Banners Sacramento TA" => "RALEYS & BEL AIR SAC CTA",
"Raleys & Bel Air Banners Sacramento Rem" => "RALEYS & BEL AIR SAC REM MKT",
"Raleys Corp TA" => "RALEYS CORP CTA",
"Raleys Corp Rem" => "RALEYS CORP REM MKT",
"BI-LO East Region Rem" => "REM FOOD BI-LO EAST REGION",
"BI-LO Total Regions Rem" => "REM FOOD BI-LO TOTAL REGION TA",
"BI-LO West Region Rem" => "REM FOOD BI-LO WEST REGION",
"Harveys Total Region Rem" => "REM FOOD HARVEYS TOTAL REGION TA",
"Winn-Dixie South FL Region TA" => "REM FOOD WD  SOUTH FL REGION TA",
"Winn-Dixie Alabama Miss Region TA" => "REM FOOD WD ALABAMA MISS REGION TA",
"Winn-Dixie Central FL Region TA" => "REM FOOD WD CENTRAL FL REGION TA",
"Winn-Dixie Louisiana Region TA" => "REM FOOD WD LOUISIANA REGION TA",
"Winn-Dixie North FL Region TA" => "REM FOOD WD NORTH FL REGION TA",
"Winn-Dixie Total Regions TA" => "REM FOOD WD TOTAL REGIONS TA",
"Winn-Dixie West FL Region TA" => "REM FOOD WD WEST FL REGION TA",
"SFY Denver Div Cluster" => "SFY DENVER DIV CENSUS CLUSTER",
"SFY Houston Div Cluster" => "SFY HOUSTON DIV CENSUS CLUSTER",
"SFY Intermountain Div Cluster" => "SFY INTERMOUNTAIN DIV CENSUS CLUSTER",
"SFY Portland Div Cluster" => "SFY PORTLAND DIV CENSUS CLUSTER",
"SFY Seattle Div Cluster" => "SFY SEATTLE DIV CENSUS CLUSTER",
"SFY So Cal Div Cluster" => "SFY SOCAL DIV CENSUS CLUSTER",
"SFY Southern Div Cluster" => "SFY SOUTHERN DIV CENSUS CLUSTER",
"SFY Southwest Div Cluster" => "SFY SOUTHWEST DIV CENSUS CLUSTER",
"SFY Total Div Cluster" => "SFY TOTAL CENSUS CLUSTER",
"SUPERVALU Champaign Rem" => "SUPERVALU CHAMPAIGN MARKET REM MKT",
"SUPERVALU Champaign TA" => "SUPERVALU CHAMPAIGN MARKET TA",
"SUPERVALU East Region Indep Rem" => "SUPERVALU EAST REGION REM MKT",
"SUPERVALU East Region Indep TA" => "SUPERVALU EAST REGION TA",
"SUPERVALU Ft Wayne Rem" => "SUPERVALU FT. WAYNE MARKET REM MKT",
"SUPERVALU Ft Wayne TA" => "SUPERVALU FT. WAYNE MARKET TA",
"SUPERVALU Green Bay Rem" => "SUPERVALU GREEN BAY MARKET REM MKT",
"SUPERVALU Green Bay TA" => "SUPERVALU GREEN BAY MARKET TA",
"SUPERVALU Midwest Division Rem" => "SUPERVALU MIDWEST DIVISION REM MKT",
"SUPERVALU Midwest Division TA" => "SUPERVALU MIDWEST DIVISION TA",
"SUPERVALU Minneapolis Rem" => "SUPERVALU MINNEAPOLIS MARKET REM MKT",
"SUPERVALU Minneapolis TA" => "SUPERVALU MINNEAPOLIS MARKET TA",
"SUPERVALU Northern Division Rem" => "SUPERVALU NORTHERN DIVISION REM MKT",
"SUPERVALU Northern Division TA" => "SUPERVALU NORTHERN DIVISION TA",
"SUPERVALU Northwest Division Rem" => "SUPERVALU NORTHWEST DIVISION REM MKT",
"SUPERVALU Northwest Division TA" => "SUPERVALU NORTHWEST DIVISION TA",
"SUPERVALU Pittsburgh Rem" => "SUPERVALU PITTSBURGH MARKET REM MKT",
"SUPERVALU Pittsburgh TA" => "SUPERVALU PITTSBURGH MARKET TA",
"SUPERVALU Retail Total TA" => "SUPERVALU RETAIL TOTAL CENSUS TA",
"SUPERVALU Retail Total Rem" => "SUPERVALU RETAIL TOTAL REM MKT",
"SUPERVALU Southeast Rem" => "SUPERVALU SOUTHEAST DIVISION REM MKT",
"SUPERVALU Southeast TA" => "SUPERVALU SOUTHEAST DIVISION TA",
"SUPERVALU Supplied Indep Rem" => "SUPERVALU SUPPLIED IND REM MKT",
"SUPERVALU Supplied Indep TA" => "SUPERVALU SUPPLIED IND TA",
"SUPERVALU Tacoma Rem" => "SUPERVALU TACOMA MARKET REM MKT",
"SUPERVALU Tacoma TA" => "SUPERVALU TACOMA MARKET TA",
"SUPERVALU Total Enterprise TA" => "SUPERVALU TOTAL ENTERPRISE CENSUS TA",
"SUPERVALU Total Enterprise Rem" => "SUPERVALU TOTAL ENTERPRISE REM MKT",
"SUPERVALU West Region Indep Rem" => "SUPERVALU WEST REGION REM MKT",
"SUPERVALU West Region Indep TA" => "SUPERVALU WEST REGION TA",
"Tops Central NY Rem" => "TOPS CENTRAL NY REM MKT",
"Tops Central NY TA" => "TOPS CENTRAL NY TA",
"Tops Eastern NY Rem" => "TOPS EASTERN NY REM MKT",
"Tops Eastern NY TA" => "TOPS EASTERN NY TA",
"Tops Total Rem" => "TOPS TOTAL REM MKT",
"Tops Total TA" => "TOPS TOTAL TA",
"Tops Western NY Rem" => "TOPS WESTERN NY REM MKT",
"Tops Western NY TA" => "TOPS WESTERN NY TA",
"Wegmans Buffalo Rem" => "WEGMAN'S BUFFALO CENSUS TRADING AREA",
"Wegmans Rochester Rem" => "WEGMAN'S ROCHESTER CENSUS TRADING AREA",
"Wegmans Syracuse Rem" => "WEGMAN'S SYRACUSE CENSUS TRADING AREA",
"Wegmans Total Rem" => "WEGMAN'S TOTAL CENSUS TRADING AREA",
"Weis Total TA" => "WEIS TOTAL CENSUS TRADING AREA",
"Weis Total Rem" => "WEIS TOTAL COMP MARKET MINUS TA",
  );

  #define our hash of AOD to Nitro measure names
  %measureMap = (
	"\$" => "\$ Vol",
	"\$ YA" => "\$ Vol - YAG",
	"Units" => "Unit Vol",
	"Units YA" => "Unit Vol - YAG",
	"Base \$" => "Base \$ Vol",
	"Base \$ YA" => "Base \$ Vol YAG",
	"Base Units" => "Base U Vol",
	"Base Units YA" => "Base U Vol - YAG",
	"Any Promo \$" => "Any Promo \$ Vol",
	"Any Promo \$ YA" => "Any Promo \$ Vol YAG",
	"Any Promo Units" => "Any Promo Unit Vol",
	"Any Promo Units YA" => "Any Promo Unit Vol YAG",
	"Feat w/o Disp \$" => "Feat Only \$ Vol",
	"Feat w/o Disp \$ YA" => "Feat Only \$ Vol YAG",
	"Feat w/o Disp Units" => "Feat Only Unit Vol",
	"Feat w/o Disp Units YA" => "Feat Only Unit Vol YAG",
	"Disp w/o Feat \$" => "Disp Only \$ Vol",
	"Disp w/o Feat \$ YA" => "Disp Only \$ Vol YAG",
	"Disp w/o Feat Units" => "Disp Only Unit Vol",
	"Disp w/o Feat Units YA" => "Disp Only Unit Vol YAG",
	"Feat & Disp \$" => "Feat & Disp \$ Vol",
	"Feat & Disp \$ YA" => "Feat & Disp \$ Vol YAG",
	"Feat & Disp Units" => "Feat & Disp Unit Vol",
	"Feat & Disp Units YA" => "Feat & Disp Unit Vol YAG",
	"Price Decr \$" => "TPR Only \$ Vol",
	"Price Decr \$ YA" => "TPR Only \$ Vol YAG",
	"Price Decr Units" => "TPR Only Unit Vol",
	"Price Decr Units YA" => "TPR Only Unit Vol YAG",
	"Incr \$" => "\$ Incr Vol",
	"Incr \$ CYA" => "\$ Incr Vol Act Chg vs YAG",
	"Incr Units" => "U Incr Vol",
	"Incr Units CYA" => "U Incr Vol Act Chg vs YAG",
	"Avg Unit Price" => "Unit Price",
	"Avg Unit Price CYA" => "Unit Price - Act Chg vs YAG",
	"%ACV Reach" => "% ACV (Max) Selling",
	"%ACV Reach YA" => "% ACV (Max) Selling YAG",
	"No Promo %ACV Reach" => "% ACV (Max) No Promo",
	"Any Promo %ACV Reach" => "% ACV (Max) Any Promo",
	"Feat w/o Disp %ACV Reach" => "% ACV (Max) Feat Only",
	"Disp w/o Feat %ACV Reach" => "% ACV (Max) Disp Only",
	"Feat & Disp %ACV Reach" => "% ACV (Max) Feat & Disp",
	"Price Decr %ACV Reach" => "% ACV (Max) TPR Only",
  );

  #instantiate our CSV object
  $csv = Text::CSV->new( {binary => 1} );

  open(INPUT, "$ARGV[0]");
  open(OUTPUT, ">$ARGV[1]");

  #parse the first header line (contains geo and time info)
  #Market : ShopRite Total TA • Period : Latest 52 Wks - W/E 08/12/17 • Product
  $line = <INPUT>;
  $line =~ m/^Market : (.*) .* Period : (.*) .* Product Share/;
  $geography = $1;
  $time = $2;

  if (length($geography) < 2)
  {
    $geography = "UNKNOWN";
  }
  if (length($time) < 2)
  {
    $time = "UNKNOWN";
  }

  #convert the geography to Nitro equivalent, if it exists
  $newGeo = $geoMap{$geography};
  if (length($newGeo) > 1)
  {
    $geography = $newGeo;
  }

  #parse the primary header line (2nd line in extracted CSV)
  $line = <INPUT>;
  $csv->parse($line);
  @columns = $csv->fields();

  $idx = 0;
  foreach $header (@columns)
  {

    #translate AOD measure names to Nitro measure names
    $translatedName = $measureMap{$header};
    if (length($translatedName) > 1)
    {
      $columns[$idx] = $translatedName;
    }
    $idx++;
  }

  $columns[0] = "Product";

  #push our static columns out to the front of the headers array
  @tmp = ('Geo', 'Time', 'UPC');
  push(@tmp, @columns);

  #output the headers
  $csv->combine(@tmp);
  $line = $csv->string();
  print OUTPUT "$line\n";

  while ($line = <INPUT>)
  {
    $csv->parse($line);
    @columns = $csv->fields();

    #extract the UPC from the end of the product name
    $product = $columns[0];
    $upc = "";
    if ($product =~ m/^.* (\d+)$/)
    {
      $upc = $1;
    }

    #skip rows without UPCs
    if (length($upc) < 9)
    {
      next;
    }

    @tmp = ($geography, $time, $upc);
    push(@tmp, @columns);

    $csv->combine(@tmp);
    $line = $csv->string();

    print OUTPUT "$line\n";
  }
