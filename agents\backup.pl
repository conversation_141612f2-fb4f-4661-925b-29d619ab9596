#!/usr/bin/perl

use lib "/opt/apache/app/";

use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;


my $debug;



#-------------------------------------------------------------------------
#
# Output debug data, if enabled
#

sub DBG
{
  my ($str) = @_;

  if ($debug == 1)
  {
    $date = localtime();
    print STDERR "$date: backup - $str\n";
  }
}



#-------------------------------------------------------------------------

  #determine if we're being run manually
  $debug = 0;
  if ($ARGV[0] eq "debug")
  {
    $debug = 1;
  }

  #redirect STDERR to the Koala error log
  if ($debug == 0)
  {
    close(STDOUT);
    close(STDERR);
    open(STDERR, ">>/opt/apache/htdocs/tmp/koala_error.log");
  }

  $db = KAPutil_connect_to_database();

  #see if we have any other backup jobs in the job table
  $query = "SELECT PID FROM app.jobs WHERE operation='BACKUP'";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;

  while (($pid) = $dbOutput->fetchrow_array)
  {

    #if the backup process really exists, exit so we don't duplicate efforts
    if ( -e "/proc/$pid")
    {
      DBG("Another backup process is already running - exiting\n");
      exit;
    }

    #else remove the dead entry from the jobs table
    else
    {
      DBG("Removing stale backup process $pid from job control table\n");
      $query = "DELETE FROM app.jobs WHERE PID=$pid AND operation='BACKUP'";
      $db->do($query);
    }
  }

  chdir("/opt/koala_backup");

  `/usr/bin/mysqldump -u app -p$Lib::KoalaConfig::password -e -B analytics > analytics.sql`;
  `/usr/bin/mysqldump -u app -p$Lib::KoalaConfig::password -e -B app > app.sql`;
  `/usr/bin/mysqldump -u app -p$Lib::KoalaConfig::password -e -B mysql > mysql.sql`;
  `/usr/bin/mysqldump -u app -p$Lib::KoalaConfig::password -e -B audit > audit.sql`;
  `/usr/bin/mysqldump -u app -p$Lib::KoalaConfig::password -e -B prep > prep.sql`;
  `/usr/bin/zip -9 analytics.zip analytics.sql`;
  `/usr/bin/zip -9 app.zip app.sql`;
  `/usr/bin/zip -9 mysql.zip mysql.sql`;
  `/usr/bin/zip -9 audit.zip audit.sql`;
  `/usr/bin/zip -9 prep.zip prep.sql`;
  unlink("analytics.sql");
  unlink("app.sql");
  unlink("mysql.sql");
  unlink("audit.sql");
  unlink("prep.sql");
  unlink("prep_data.sql");

  #maybe we've been gone a long time, so make sure we're still connected
  if (!($db->ping))
  {
    $db = KAPutil_connect_to_database();
  }

  $query = "SELECT ID, userID, UNIX_TIMESTAMP(lastUpdate), UNIX_TIMESTAMP(lastModified), UNIX_TIMESTAMP(lastBackup), name, ODBCexport \
      FROM dataSources WHERE lastBackup < lastModified ORDER BY ID ASC";
  $dbOutput = $db->prepare($query);
  $todo = $dbOutput->execute;

  $count = 0;
  while (($dsID, $userID, $lastUpdate, $lastModified, $lastBackup, $dsName, $ODBCexport) = $dbOutput->fetchrow_array)
  {

    $count++;
    DBG("Backup job $count of $todo");

    #see if backup needs to be taken (e.g., the data source has changed since the last backup)
    if (($lastUpdate < $lastBackup) && ($lastModified < $lastBackup))
    {
      DBG("$dsID doesn't need to be backed up");
      next;
    }

    #make sure it's OK for us to backup this datasource
    $ok = DSRutil_operation_ok($db, $dsID, 0, "BACKUP");
    if ($ok != 1)
    {
      DBG("Skipping in-use data source $dsName");
      next;
    }

    #if the data source hasn't been idle for at least 1 hour, leave it alone
    #to keep from locking a DS that's potentially in use
    $timeDiff = time() - $lastModified;
    $timeDiff = $timeDiff / 3600;
    if ($timeDiff < 2)
    {
      DBG("Skipping recently used data source $dsName\n");
      next;
    }

    #determine if we're using an external storage volume for capacity/IO reasons
    $orgID = KAPutil_get_user_org_id($db, $userID);

    $query = "SELECT dataStorage FROM app.orgs WHERE ID=$orgID";
    $dbOutput1 = $db->prepare($query);
    $dbOutput1->execute;
    ($dataDir) = $dbOutput1->fetchrow_array;
    if (length($dataDir) > 2)
    {
      $dataDir = $dataDir . "/koala_backup/";
    }
    else
    {
      $dataDir = "/opt/koala_backup/";
    }
    chdir($dataDir);

    DBG("Backing up $dsName...");
    KAPutil_job_store_status($db, 0, $dsID, 0, "BACKUP", "Backing up data source");
    $q_name = $db->quote($dsName);
    $query = "UPDATE app.jobs SET dsName=$q_name WHERE PID=$$ AND dsID=$dsID";
    $db->do($query);

    $dbName = "datasource_" . $dsID;
    $sqlFileName = "$dbName.sql";
    $zipFileName = "$dbName.zip";

    if (-e $sqlFileName)
    {
      unlink($sqlFileName);
    }
    if (-e $zipFileName)
    {
      unlink($zipFileName);
    }

    $ignore = "";
    if ($ODBCexport == 1)
    {
      $ignore = "--ignore-table=$dbName.export --ignore-table=$dbName._export ";
    }
    if ($ODBCexport == 2)
    {
      $ignore = "--ignore-table=$dbName.export --ignore-table=$dbName._export --ignore-table=$dbName.export_product --ignore-table=$dbName.export_geography --ignore-table=$dbName.export_time ";
    }

    $query2 = "SELECT ID FROM app.cubes WHERE dsID=$dsID";
    $dbOutput2 = $db->prepare($query2);
    $dbOutput2->execute;
    while (($rptID) = $dbOutput2->fetchrow_array)
    {
      $ignore .= "--ignore-table=$dbName._rptcube_$rptID ";
    }

    KAPutil_job_update_status($db, "Dumping database tables");

    `/usr/bin/mysqldump -u app -p$Lib::KoalaConfig::password -e -B $dbName $ignore | /usr/bin/zip -q > $zipFileName`;
    `printf "@ -\n@=$sqlFileName\n" | zipnote -w $zipFileName`;

    #maybe we've been gone a long time, so make sure we're still connected
    if (!($db->ping))
    {
      $db = KAPutil_connect_to_database();
    }

    $query = "UPDATE dataSources SET lastBackup=NOW() WHERE ID=$dsID";
    $db->do($query);

    DSRutil_clear_status($db);
  }
