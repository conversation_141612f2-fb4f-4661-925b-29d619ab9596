#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($kapDB);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Explore Data</TITLE>
<LINK REL="SHORTCUT ICON" HREF="$Lib::KoalaConfig::kapHostURL/favicon.png">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<SCRIPT SRC="/jsgrid/jsgrid.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-select-1.14.0-b2/js/bootstrap-select.min.js"></SCRIPT>
<SCRIPT SRC="/fusioncharts-3.18.0/js/fusioncharts.js"></SCRIPT>
<SCRIPT SRC="/fusioncharts-3.18.0/js/themes/fusioncharts.theme.zune.js"></SCRIPT>
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/jsgrid/jsgrid.min.css" REL="stylesheet">
<LINK HREF="/jsgrid/jsgrid-theme.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-select-1.14.0-b2/css/bootstrap-select.min.css" REL="stylesheet">
<LINK HREF="/fontawesome-5.10.2/css/all.min.css" REL="stylesheet">

<STYLE>
div.grid th { background: #e2e3e5 }

.invalid-row > td
{
  background: #f8d7da !important;
  border-color: #f8d7da;
}
</STYLE>

<SCRIPT>
const gridHeight = window.innerHeight - 200;
let selColumn = -1;

function selectCol(colID, colIdx)
{
  let idStr = '#dataGrid td:nth-child(' + colIdx + ')';
  let headStr = '#col_' + colID;

  \$('#dataGrid td').removeClass('bg-info');
  \$('#dataGrid td').removeClass('bg-opacity-25');
  \$('#dataGrid th').removeClass('bg-info');

  if (selColumn != colID)
  {
    \$(idStr).addClass('bg-info');
    \$(idStr).addClass('bg-opacity-25');
    \$(headStr).addClass('bg-info');
    selColumn = colID;
  }
  else
  {
    selColumn = -1;
  }
}



function showDlg(xformType)
{
  const url = xformType + '?f=$flowID&j=$jobID';

  \$('#wait-spinner').css('display', 'block');
  \$('#modal-dialog').load(url, function (response, status, xhr)
  {
    if (status == 'success')
    {
      let myModal = new bootstrap.Modal(document.getElementById('modal-dialog'));
      myModal.show();

      \$('#wait-spinner').css('display', 'none');
    }
  });
}



function showColumnDlg(xformType)
{
  const url = xformType + '?f=$flowID&j=$jobID&col=' + selColumn;

  if (selColumn < 1)
  {
    document.getElementById('warn-select-column').style.display = 'block';
    return;
  }

  \$('#wait-spinner').css('display', 'block');
  \$('#modal-dialog').load(url, function (response, status, xhr)
  {
    if (status == 'success')
    {
      let myModal = new bootstrap.Modal(document.getElementById('modal-dialog'));
      myModal.show();
      \$('#wait-spinner').css('display', 'none');
    }
  });
}



function showTransColTrimDataDlg()
{
  const url = 'transColTrimData.cld?f=$flowID&j=$jobID&col=' + selColumn;

  if (selColumn < 1)
  {
    document.getElementById('warn-select-column').style.display = 'block';
    return;
  }

  location.href=url;
}



function showTransColMatchProductsDlg()
{
  const url = 'transColMatchProducts.cld?f=$flowID&j=$jobID&col=' + selColumn;
  location.href=url;
}




function showTransCellWeightsDlg()
{
  const url = 'transCellWeightsMeasures.cld?f=$flowID&j=$jobID&col=' + selColumn;

  if (selColumn < 1)
  {
    document.getElementById('warn-select-column').style.display = 'block';
    return;
  }

  location.href=url;
}
END_HTML

  #build up string containing column header info and grid header HTML
  @orderedCols = prep_flow_order_columns($prepDB, $flowID, $jobID);
  $colSelStr = join(',', @orderedCols);

  $query = "SELECT ID, name, type FROM $masterColTable \
      ORDER BY FIELD(ID, $colSelStr)";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;

  $gridHeaders = "<tr class='jsgrid-jeader-row'>";

  $colIdx = 1;
  while (($colID, $colName, $colType) = $dbOutput->fetchrow_array)
  {
    $colImage = "";
    if ($colType eq "product")
    {
      $colImage = "<I CLASS='bi bi-cart-fill' TITLE='Product'></I>";
    }
    elsif ($colType eq "geography")
    {
      $colImage = "<I CLASS='bi bi-globe' TITLE='Geography'></I>";
    }
    elsif ($colType eq "time")
    {
      $colImage = "<I CLASS='bi bi-calendar-date-fill' TITLE='Time'></I>";
    }
    elsif ($colType eq "upc")
    {
      $colImage = "<I CLASS='bi bi-upc-scan' TITLE='UPC'></I>";
    }
    elsif ($colType eq "measure")
    {
      $colImage = "<I CLASS='bi bi-123' TITLE='Measure'></I>";
    }
    else
    {
      if ($colType eq "pseg")
      {
        $title = "Product Segmentation";
      }
      elsif ($colType eq "gseg")
      {
        $title = "Geography Segmentation";
      }
      elsif ($colType eq "tseg")
      {
        $title = "Time Segmentation";
      }
      elsif ($colType eq "pattr")
      {
        $title = "Product Attribute";
      }
      elsif ($colType eq "gattr")
      {
        $title = "Geography Attribute";
      }
      elsif ($colType eq "tattr")
      {
        $title = "Time Attribute";
      }
      elsif ($colType eq "palias")
      {
        $title = "Product Alias";
      }
      elsif ($colType eq "galias")
      {
        $title = "Geography Alias";
      }
      elsif ($colType eq "talias")
      {
        $title = "Time Alias";
      }

      $colImage = "<I CLASS='bi bi-list-task' TITLE='$title'></I>";
    }

    $gridFields .= "{name: \"column_$colID\", title: \"$colName\", type: \"text\", width: 175},\n";

    $gridHeaders .= "<th class='jsgrid-header-cell' style='width:175px;' id='col_$colID' onclick='selectCol($colID, $colIdx);'>$colImage $colName</th>";

    $colIdx++;
  }
  chop($gridFields);   chop($gridFields);
  $gridHeaders = $gridHeaders . "</tr>";

  print <<END_HTML;
\$(document).ready(function()
{

  \$(document).ajaxStart(function()
  {
    \$("#wait-spinner").css("display", "block");
  });

  \$(document).ajaxComplete(function()
  {
    \$("#wait-spinner").css("display", "none");
  });

  \$("#dataGrid").jsGrid(
  {
    width: "98%",
    height: gridHeight,
    sorting: true,
    autoload: true,
    selecting: false,
    paging: true,
    pageSize: 100,
    pageButtonCount: 5,
    pagerContainer: "#dataGridPager",
    loadIndication: true,
    pageLoading: true,

    controller:
    {
      loadData: function (filter)
      {
        let data = \$.Deferred();

        \$.ajax(
        {
          type: "GET",
          contentType: "application/json; charset=utf-8",
          url: "ajaxDataView.cld?f=$flowID&j=$jobID&idx=" + filter.pageIndex,
          dataType: "json"
        }).done(function(response)
        {
          data.resolve(response);
        });
        return data.promise();
      }
    },

    headerRowRenderer: function()
    {
      return "$gridHeaders";
    },

    rowClass: function(item, itemIndex)
    {
      if (item.valid == 1)
      {
        return "invalid-row";
      }
    },

    fields: [ $gridFields ]
  });



  \$('modal-dialog').on('hide.bs.modal', function ()
  {
    \$('#modal-dialog').removeData('bs.modal');
    \$('#modal-dialog .modal-content').html('');
  });

});


</SCRIPT>

</HEAD>

<BODY>

<DIV ID="warn-select-column" CLASS="alert alert-warning alert-dismissible fade show" role="alert" STYLE="display:none; width:50%; position:absolute; margin-left:25%;">
  <DIV CLASS="text-center"><STRONG>Select a column to perform this transform on.</STRONG></DIV>
  <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="alert"></BUTTON>
</DIV>

END_HTML

  $query = "SELECT validation FROM prep.jobs WHERE ID=$jobID";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($validationStatus) = $dbOutput->fetchrow_array;
  if ($validationStatus eq "ERROR")
  {
    print <<END_HTML;
<DIV CLASS="alert alert-danger alert-dismissible fade show" role="alert" STYLE="display:none; width:50%; position:absolute; margin-left:25%;">
  <DIV CLASS="text-center"><STRONG>Data validation failed</STRONG></DIV>
  <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="alert"></BUTTON>
</DIV>

END_HTML
  }

  print_html_navbar($kapDB, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-0">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="$Lib::KoalaConfig::kapHostURL/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?f=$flowID">Data Prep</A></LI>
    <LI CLASS="breadcrumb-item">$flowName</LI>
    <LI CLASS="breadcrumb-item active">Explore Data</LI>
  </OL>
</NAV>

<DIV ID="wait-spinner" STYLE="display:none; z-index:1001; width:100%; height:100%; position:absolute; top:0; left:0; background-color: rgba(0,0,0,0.15);">
 <DIV STYLE="position:absolute; top:50%; left:50%;">
  <I CLASS="fas fa-spinner fa-spin" STYLE="font-size:100px; color:blue;"></I>
 </DIV>
</DIV>

<DIV id="modal-dialog" class="modal" role="dialog">
</DIV>

END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  $flowID = $q->param('f');
  $jobID = $q->param('j');

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  $flowName = prep_flow_id_to_name($prepDB, $flowID);

  print_html_header();

  #make sure we have read privs for this data source
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to view jobs in this data flow.");
  }

  $query = "SELECT COUNT(ID) FROM $masterColTable";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  ($colCount) = $dbOutput->fetchrow_array;

  $query = "SELECT COUNT(ID) FROM $masterColTable WHERE type LIKE '%seg'";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  ($segCount) = $dbOutput->fetchrow_array;

  $query = "SELECT COUNT(ID) FROM $masterColTable WHERE type='measure'";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  ($measCount) = $dbOutput->fetchrow_array;

  $query = "SELECT rowCount FROM prep.jobs WHERE ID=$jobID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  ($rowCount) = $dbOutput->fetchrow_array;
  if ($rowCount >= 10_000)
  {
    $rowCount = prep_autoscale_number($rowCount);
  }


  print <<END_HTML;
<NAV CLASS="navbar navbar-expand-lg navbar-light bg-light border mb-4">
  <BUTTON CLASS="navbar-toggler" TYPE="button" data-bs-toggle="collapse" data-bs-target="#navbarNavDropdown">
    <SPAN CLASS="navbar-toggler-icon"></SPAN>
  </BUTTON>
  <DIV CLASS="collapse navbar-collapse justify-content-center" ID="navbarNavDropdown">
    <UL CLASS="navbar-nav">

      <LI CLASS="nav-item dropdown">
        <A CLASS="nav-link dropdown-toggle" data-bs-toggle="dropdown" HREF="#"><I CLASS="bi bi-eye"></I> Analyze</A>
        <UL CLASS="dropdown-menu">
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showDlg('xhrProfileRows.cld');">Profile Rows</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showDlg('xhrProfileCols.cld');">Profile Columns</A></LI>
          <LI CLASS="dropdown-divider"></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showColumnDlg('xhrAnalyzeNumeric.cld');">Numeric</A></LI>
          <LI CLASS="dropdown-divider"></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showColumnDlg('xhrAnalyzeText.cld');">Text</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showColumnDlg('xhrAnalyzeTextLength.cld')">Text Length</A></LI>
          <LI CLASS="dropdown-divider"></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showColumnDlg('xhrAnalyzeDate.cld')">Date</A></LI>
        </UL>
      </LI>

      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="showColumnDlg('xhrSearch.cld')"><I CLASS="bi bi-search"></I> Search</A></LI>

      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="showColumnDlg('xhrSort.cld')"><I CLASS="bi bi-sort-down"></I> Sorting</A></LI>

      <LI CLASS="nav-item dropdown">
        <A CLASS="nav-link dropdown-toggle" data-bs-toggle="dropdown" HREF="#"><I CLASS="bi bi-pencil"></I> Transform Column</A>
        <UL CLASS="dropdown-menu">
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showColumnDlg('xhrTransColType.cld')">Set Column Type</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showColumnDlg('xhrTransColName.cld')">Rename Column</A></LI>
          <LI CLASS="dropdown-divider"></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showColumnDlg('xhrTransColSplit.cld')">Split by Separator</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showColumnDlg('xhrTransColSplitLength.cld')">Split by Field Length</A></LI>
          <LI CLASS="dropdown-divider"></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showColumnDlg('xhrTransColLookup.cld')">Lookup Values</A></LI>
          <LI CLASS="dropdown-divider"></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showDlg('xhrTransColInsert.cld');">Insert Column</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showColumnDlg('xhrTransColCopy.cld')">Copy Column</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showColumnDlg('xhrTransColMerge.cld')">Merge Columns</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showColumnDlg('xhrTransColDiscard.cld')">Discard Column</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showDlg('xhrTransColDiscardEmpty.cld')">Discard Empty Columns</A></LI>
          <LI CLASS="dropdown-divider"></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showColumnDlg('xhrTransColTrimValues.cld')">Trim Values</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showTransColTrimDataDlg()">Trim Data</A></LI>
          <LI CLASS="dropdown-divider"></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showTransColMatchProductsDlg()">Match Products</A></LI>
        </UL>
      </LI>

      <LI CLASS="nav-item dropdown">
        <A CLASS="nav-link dropdown-toggle" data-bs-toggle="dropdown" HREF="#"><I CLASS="bi bi-pencil"></I> Transform Cells</A>
        <UL CLASS="dropdown-menu">
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showColumnDlg('xhrTransCellAppendPrepend.cld')">Append/Prepend Values</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showColumnDlg('xhrTransCellSet.cld')">Set Values</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showColumnDlg('xhrTransCellReplace.cld')">Replace Values</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showColumnDlg('xhrTransCellSearchReplace.cld')">Search & Replace</A></LI>
          <LI CLASS="dropdown-divider"></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showColumnDlg('xhrTransCellRound.cld')">Numeric Rounding</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showColumnDlg('xhrTransCellCeiling.cld')">Numeric Ceiling</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showColumnDlg('xhrTransCellFloor.cld')">Numeric Floor</A></LI>
          <LI CLASS="dropdown-divider"></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showColumnDlg('xhrTransCellNAzero.cld')">Convert NA to Zeroes</A></LI>
          <LI CLASS="dropdown-divider"></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showColumnDlg('xhrTransCellHTML.cld')">Convert HTML to Text</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showColumnDlg('xhrTransCellSpace.cld')">Clean Up White Space</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showColumnDlg('xhrTransCellCase.cld')">Set Capitalization Style</A></LI>
          <LI CLASS="dropdown-divider"></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showColumnDlg('xhrTransCellDate.cld')">Format Dates</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showColumnDlg('xhrTransCellEnrichDate.cld')">Enrich Dates</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showColumnDlg('xhrTransCellUPC.cld')">Format UPCs</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="showTransCellWeightsDlg()">Normalize Weights & Measures</A></LI>
        </UL>
      </LI>

      <LI CLASS="nav-item dropdown">
        <A CLASS="nav-link dropdown-toggle" data-bs-toggle="dropdown" HREF="#"><I CLASS="bi bi-list-task"></I> Recipe</A>
        <UL CLASS="dropdown-menu">
          <LI><A CLASS="dropdown-item" HREF="recipeEdit.cld?f=$flowID&j=$jobID">Edit Recipe</A></LI>
        </UL>
      </LI>

      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="validation.cld?f=$flowID&j=$jobID"><I CLASS="bi bi-hand-thumbs-up"></I> Validate</A></LI>

      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="flowExportData.cld?f=$flowID&j=$jobID"><I CLASS="bi bi-cloud-upload"></I> Export</A></LI>

    </UL>
  </DIV>
</NAV>

<DIV ID="dataGrid" CLASS="grid mx-auto" STYLE="font-size:13px;">
</DIV>

<DIV CLASS="row bg-secondary-subtle text-muted mx-auto" STYLE="width:98%; margin-top:-25px;">
  <DIV CLASS="col-auto">
    <I CLASS="bi bi-binoculars"></I>
  </DIV>
  <DIV CLASS="col-auto">
    <SMALL>$rowCount Rows</SMALL>
  </DIV>
  <DIV CLASS="col-auto">
    <SMALL>$colCount Columns</SMALL>
  </DIV>
  <DIV CLASS="col-auto">
    <SMALL>$measCount Measures</SMALL>
  </DIV>
  <DIV CLASS="col-auto">
    <SMALL>$segCount Segmentations</SMALL>
  </DIV>
</DIV>


<DIV ID="dataGridPager" CLASS="mx-auto" STYLE="font-size:13px; width:98%">
</DIV>


END_HTML

  print_html_footer();


#EOF
