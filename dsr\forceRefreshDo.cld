#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::BuildCube;
use Lib::DataSel;
use Lib::DataSources;
use Lib::DSRMeasures;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::ODBC;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Force Data Source Refresh</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Force Refresh</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');
  $structs = $q->param('structs');
  $meas = $q->param('meas');
  $odbc = $q->param('odbc');

  #sanitize parameters
  $dsID = utils_sanitize_integer($dsID);

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsSchema = "datasource_" . $dsID;

  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  #make sure we have write privs for this data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to force refresh this data source.");
  }

  $loadAvg = KAPutil_get_load_avg($db);
  if (($loadAvg >= 1.0) && ($acctType < 10))
  {
    exit_error("Your analytics cloud instance is overloaded. Please try again in a bit.");
  }
  if (($loadAvg >= 0.75) && ($acctType < 10))
  {
    $jobCount = KAPutil_get_user_jobs($db, $userID);

    #if the user already has more than a couple jobs running
    if ($jobCount > 1)
    {
      exit_error("Your analytics cloud is heavily loaded, and you already have at least one large job running. Wait a bit for that job to finish, and then try again.");
    }
  }

  utils_audit($db, $userID, "Forced data source refresh", $dsID, 0, 0);
  $activity = "$first $last forced a refresh of data source $dsName";
  utils_slack($activity);

  #split the copy process off into a background process
  $SIG{CHLD} = "IGNORE";
  if ($pid = fork())
  {
    #parent process - we're just going to finish up our display script
    print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-10 col-lg-7 col-xl-6"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Force Data Source Refresh</DIV>
        <DIV CLASS="card-body">

          The selected data source structures are being refreshed in the background for you.

          <P>&nbsp;<P>

          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" onClick="location.href='main.cld'"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

    print_html_footer();
  }

  else
  {
    #child process - do the actual refresh work

    #let Apache know not to wait on the child process
    close(STDIN);
    close(STDOUT);
    close(STDERR);
    open(STDERR, ">>/opt/apache/htdocs/tmp/koala_error.log") or die("Unable to open STDERR, $!");
    select(STDERR);
    $| = 1;

    #we're in a new process, so we need a new connection to the database
    $db = KAPutil_connect_to_database();

    #set our initial state in the jobs table
    KAPutil_job_store_status($db, $userID, $dsID, 0, "FORCE-REFRESH",
        "Forcing data source refresh");
    $q_name = $db->quote($dsName);
    $query = "UPDATE app.jobs SET dsName=$q_name WHERE PID=$$ AND dsID=$dsID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    if ($struct eq "on")
    {
      #
      #start by re-expanding all lists and aggregates
      #

      #expand product lists
      KAPutil_job_update_status($db, "Refreshing lists");

      $query = "SELECT ID FROM $dsSchema.product_list";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      KAPutil_handle_db_err($db, $status, $query);
      while (($id) = $dbOutput->fetchrow_array)
      {
        datasel_expand_script($db, $dsSchema, $id, "p", "l");
      }

      #expand geography lists
      $query = "SELECT ID FROM $dsSchema.geography_list";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      KAPutil_handle_db_err($db, $status, $query);
      while (($id) = $dbOutput->fetchrow_array)
      {
        datasel_expand_script($db, $dsSchema, $id, "g", "l");
      }

      #expand time lists
      $query = "SELECT ID FROM $dsSchema.time_list";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      KAPutil_handle_db_err($db, $status, $query);
      while (($id) = $dbOutput->fetchrow_array)
      {
        datasel_expand_script($db, $dsSchema, $id, "t", "l");
      }

      #expand measure lists
      $query = "SELECT ID FROM $dsSchema.measure_list";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      KAPutil_handle_db_err($db, $status, $query);
      while (($id) = $dbOutput->fetchrow_array)
      {
        datasel_expand_script($db, $dsSchema, $id, "m", "l");
      }

      #expand product aggregates
      KAPutil_job_update_status($db, "Refreshing aggregates");

      $query = "SELECT ID FROM $dsSchema.product_aggregate";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      KAPutil_handle_db_err($db, $status, $query);
      while (($id) = $dbOutput->fetchrow_array)
      {
        datasel_expand_script($db, $dsSchema, $id, "p", "a");
        datasel_expand_script($db, $dsSchema, $id, "p", "as");
      }

      #expand geography aggregates
      $query = "SELECT ID FROM $dsSchema.geography_aggregate";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      KAPutil_handle_db_err($db, $status, $query);
      while (($id) = $dbOutput->fetchrow_array)
      {
        datasel_expand_script($db, $dsSchema, $id, "g", "a");
        datasel_expand_script($db, $dsSchema, $id, "g", "as");
      }

      #expand time aggregates
      $query = "SELECT ID FROM $dsSchema.time_aggregate";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      KAPutil_handle_db_err($db, $status, $query);
      while (($id) = $dbOutput->fetchrow_array)
      {
        datasel_expand_script($db, $dsSchema, $id, "t", "a");
        datasel_expand_script($db, $dsSchema, $id, "t", "as");
      }

      #
      # Refresh merged products
      #

      KAPutil_job_update_status($db, "Refreshing merged products");
      DSRmergedprod_update($db, $dsSchema);
    }


    #
    # Recalculate all measures
    #

    if ($meas eq "on")
    {
      KAPutil_job_update_status($db, "Recalculating all measures");
      $query = "UPDATE $dsSchema.measures SET lastCalc=NULL";
      $status = $db->do($query);
      KAPutil_handle_db_err($db, $status, $query);
      DSRmeasures_recalculate_all_measures($db, $dsSchema);
    }

    #
    # Update last modified time (force external agents to update as well)
    #

    $query = "UPDATE app.dataSources SET lastModified=NOW() WHERE ID=$dsID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    #remove this task from the jobs table
    #NB: we're clearing our job from the table a little early so it doesn't
    #    interfere with the potential ODBC refresh job entry
    DSRutil_clear_status($db);

    if ($odbc eq "on")
    {
      $query = "SELECT ODBCexport FROM app.dataSources WHERE ID=$dsID";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      KAPutil_handle_db_err($db, $status, $query);
      ($ODBCexport) = $dbOutput->fetchrow_array;

      if ($ODBCexport == 1)
      {
        ODBC_export_tabular($dsID, $userID);
      }
      elsif ($ODBCexport == 2)
      {
        ODBC_export_star($dsID, $userID);
      }
    }

  }


#EOF
