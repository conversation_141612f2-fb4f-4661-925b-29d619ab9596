
package Lib::AIProductNaming;

use lib "/opt/apache/app/";

use Exporter;
use Lib::DSRstructures;
use Lib::DSRUtils;

our @ISA = ('Exporter');

our @EXPORT = qw(
    &AIProductNaming_improve_product_names
);


my %nielsenProductPackaging = (
  " BAG " => "BAG",
  " BSKT " => "BASKET",
  " BOX " => "BOX",
  " CAN " => "CAN",
  " CNST " => "CANISTER",
  " JAR " => "JAR",
  " MLDD TRY IN BAG" => "MOLDED TRAY IN BAG",
  " MLDD TRY IN WRP" => "MOLDED TRAY IN WRAP",
  " MLDD TRY " => "MOLDED TRAY",
  " TRY IN BAG " => "TRAY IN BAG",
  " TRY " => "TRAY",
  " TUB " => "TUB",
  " WRP " => "WRAP",
);


my %nielsenProductColors = (
  " PLST BL " => "BLUE",
  " PLST BLCK " => "BLACK",
  " PLST CLR " => "CLEAR",
  " PLST CRYS " => "CRYSTAL",
  " PLST GLD " => "GOLD",
  " PLST GRN " => "GREEN",
  " PLST ORNG " => "ORANGE",
  " PLST PNK " => "PINK",
  " PLST PRPL " => "PURPLE",
  " PLST RED " => "RED",
  " PLST SLVR " => "SILVER",
  " PLST WHT " => "WHITE",
  " PLST .* BL " => "BLUE",
  " PLST .* GRN " => "GREEN",
  " PLST .* PNK " => "PINK",
);



#-------------------------------------------------------------------------------
#
# Try to determine the color attribute from the supplied Nielsen item name.
#

sub AIProductNaming_attrs_color_from_name
{
  my ($tag);

  my ($prodName) = @_;

  foreach $tag (keys %nielsenProductColors)
  {
    if ($prodName =~ m/$tag/)
    {
      return($nielsenProductColors{$tag});
    }
  }

  return("");
}



#-------------------------------------------------------------------------------
#
# Try to determine the packaging attribute from the supplied Nielsen item name.
#

sub AIProductNaming_attrs_packaging_from_name
{
  my ($tag);

  my ($prodName) = @_;

  foreach $tag (keys %nielsenProductPackaging)
  {
    if ($prodName =~ m/$tag/)
    {
      return($nielsenProductPackaging{$tag});
    }
  }

  return("");
}



#-------------------------------------------------------------------------------
#
# Return the customer-specific hash that contains pre-trim markets based on
# the tag included in the customer's MKTTRIM field of the parsing options
# string.
#

sub AIProductNaming_attrs_base_size_from_name
{
  my ($tag);

  my ($prodName) = @_;

  #look for common size tags in a Nielsen abbreviated item name
  if ($prodName =~ m/.* (.*? OZ) /)
  {
    return($1);
  }
  elsif ($prodName =~ m/.* (.*? CT) /)
  {
    return($1);
  }
  elsif ($prodName =~ m/.* (.*? FL OZ) /)
  {
    return($1);
  }

  return("");
}



#-------------------------------------------------------------------------------
#
# Try to create nicer human-readable names for products using segmentation
# info. Rub a little heuristic AI on the resulting names to try to make them
# better than just concatenated segment info.
#

sub AIProductNaming_improve_product_names
{
  my ($dsSchema, $query, $dbOutput, $status, $appendUPC, $prodAlias, $id);
  my ($upcAttrID, $brandSegID, $catSegID, $segmentSegID, $organicSegID);
  my ($flavorSegID, $colorSegID, $materialSegID, $formSegID, $packageSegID);
  my ($sizeSegID, $packSizeSegID, $multiSegID, $brandSegmentID, $catSegmentID);
  my ($segmentSegmentID, $flavorSegmentID, $materialSegmentID);
  my ($organicSegmentID, $formSegmentID, $packageSegmentID, $colorSegmentID);
  my ($sizeSegmentID, $packSizeSegmentID, $multiSegmentID);
  my ($segName, $brandName, $catName, $segmentName, $organicName, $formName);
  my ($packageName, $flavorName, $colorName, $materialName, $sizeName);
  my ($packSizeName, $name, $q_name);
  my (%upcHash, %prodNameHash, %segHash, %segmentNameHash);
  my (%brandMembershipHash, %catMembershipHash, %segmentMembershipHash);
  my (%flavorMembershipHash, %organicMembershipHash, %colorMembershipHash);
  my (%materialMembershipHash, %formMembershipHash, %packageMembershipHash);
  my (%sizeMembershipHash, %packSizeMembershipHash, %multiMembershipHash);

  my ($db, $dsID) = @_;


  $dsSchema = "datasource_" . $dsID;

  #grab our settings from the database
  $query = "SELECT prodAlias, prodAliasAppendUPC FROM app.dataEnrichment \
      WHERE dsID=$dsID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($prodAlias, $appendUPC) = $dbOutput->fetchrow_array;

  #if we aren't set to create AI aliases for products, exit w/o doing anything
  if ($prodAlias < 1)
  {
    return;
  }

  #if we're going to append UPCs to the new names, get the ID of the UPC attr
  if ($appendUPC > 0)
  {
    $query = "SELECT ID FROM $dsSchema.product_attributes WHERE name='UPC'";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($upcAttrID) = $dbOutput->fetchrow_array;
    %upcHash = DSRattr_get_values_hash($db, $dsSchema, "p", $upcAttrID);
  }

  #get a hash of all segmentation names and IDs
  %segHash = DSRsegmentation_get_segmentations_hash($db, $dsID, "p");
  %segHash = reverse(%segHash);

  #hunt through the available segmentations, looking for the "best" one that
  #probably contains brand info
  $brandSegID = $segHash{'BRAND LOW'};

  if ($brandSegID < 1)
  {
    $brandSegID = $segHash{'BRAND HIGH'};
  }
  if ($brandSegID < 1)
  {
    $brandSegID = $segHash{'BRAND OWNER LOW'};
  }
  if ($brandSegID < 1)
  {
    $brandSegID = $segHash{'BRAND OWNER HIGH'};
  }

  #if we didn't find a common brand segmentation, take anything with "brand"
  #in the name
  if ($brandSegID < 1)
  {
    foreach $segName (keys %segHash)
    {
      if ($segName =~ m/brand/i)
      {
        $brandSegID = $segHash{$segName};
      }
    }
  }

  #look for category segmentation
  $catSegID = $segHash{'BC CATEGORY'};

  if ($catSegID < 1)
  {
    $catSegID = $segHash{'CATEGORY'};
  }

  #look for segment segmentation
  $segmentSegID = $segHash{'BC SEGMENT'};

  if ($segmentSegID < 1)
  {
    $segmentSegID = $segHash{'SEGMENT'};
  }

  #look for organic segmentation
  $organicSegID = $segHash{'HW ORGANIC'};

  #look for flavor segmentation
  $flavorSegID = $segHash{'FLAVOR'};
  if ($flavorSegID < 1)
  {
    $flavorSegID = $segHash{'SCENT'};
  }

  #look for a color segmentation
  $colorSegID = $segHash{'COLOR'};

  #look for a material substance segmentation
  $materialSegID = $segHash{'MATERIAL SUBSTANCE'};

  #look for form segmentation
  $formSegID = $segHash{'FORM'};

  #look for package segmentation
  $packageSegID = $segHash{'PACKAGE GENERAL SHAPE'};

  #look for size segmentation
  $sizeSegID = $segHash{'BASE SIZE'};

  if ($sizeSegID < 1)
  {
    $sizeSegID = $segHash{'SIZE'};
  }

  #look for pack size segmentation
  $packSizeSegID = $segHash{'PACK SIZE'};

  #look for multi pack segmentation
  $multiSegID = $segHash{'MULTI'};

  #if we're missing a key segmentation we need for the naming, error out
  if ($brandSegID < 1)
  {
    return(-1);
  }

  %segmentNameHash = DSRseg_get_segments_hash($db, $dsSchema, "p");
  %prodNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "p", 1);

  if ($brandSegID > 0)
  {
    %brandMembershipHash = DSRseg_get_segment_membership_hash($db, $dsSchema, "p", $brandSegID);
  }
  if ($catSegID > 0)
  {
    %catMembershipHash = DSRseg_get_segment_membership_hash($db, $dsSchema, "p", $catSegID);
  }
  if ($segmentSegID > 0)
  {
    %segmentMembershipHash = DSRseg_get_segment_membership_hash($db, $dsSchema, "p", $segmentSegID);
  }
  if ($flavorSegID > 0)
  {
    %flavorMembershipHash = DSRseg_get_segment_membership_hash($db, $dsSchema, "p", $flavorSegID);
  }
  if ($organicSegID > 0)
  {
    %organicMembershipHash = DSRseg_get_segment_membership_hash($db, $dsSchema, "p", $organicSegID);
  }
  if ($colorSegID > 0)
  {
    %colorMembershipHash = DSRseg_get_segment_membership_hash($db, $dsSchema, "p", $colorSegID);
  }
  if ($materialSegID > 0)
  {
    %materialMembershipHash = DSRseg_get_segment_membership_hash($db, $dsSchema, "p", $materialSegID);
  }
  if ($formSegID > 0)
  {
    %formMembershipHash = DSRseg_get_segment_membership_hash($db, $dsSchema, "p", $formSegID);
  }
  if ($packageSegID > 0)
  {
    %packageMembershipHash = DSRseg_get_segment_membership_hash($db, $dsSchema, "p", $packageSegID);
  }
  if ($sizeSegID > 0)
  {
    %sizeMembershipHash = DSRseg_get_segment_membership_hash($db, $dsSchema, "p", $sizeSegID);
  }
  if ($packSizeSegID > 0)
  {
    %packSizeMembershipHash = DSRseg_get_segment_membership_hash($db, $dsSchema, "p", $packSizeSegID);
  }
  if ($multiSegID > 0)
  {
    %multiMembershipHash = DSRseg_get_segment_membership_hash($db, $dsSchema, "p", $multiSegID);
  }

  $query = "SELECT ID FROM $dsSchema.products";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  while (($id) = $dbOutput->fetchrow_array)
  {
    $brandSegmentID = $brandMembershipHash{$id};
    $catSegmentID = $catMembershipHash{$id};
    $segmentSegmentID = $segmentMembershipHash{$id};
    $flavorSegmentID = $flavorMembershipHash{$id};
    $colorSegmentID = $colorMembershipHash{$id};
    $materialSegmentID = $materialMembershipHash{$id};
    $organicSegmentID = $organicMembershipHash{$id};
    $formSegmentID = $formMembershipHash{$id};
    $packageSegmentID = $packageMembershipHash{$id};
    $sizeSegmentID = $sizeMembershipHash{$id};
    $packSizeSegmentID = $packSizeMembershipHash{$id};
    $multiSegmentID = $multiMembershipHash{$id};

    $brandName = $segmentNameHash{$brandSegmentID};
    if ($brandName =~ m/no brand listed/i)
    {
      $brandName = "UNBRANDED";
    }

    if ($brandName =~ m/^(.*) CO\.$/)
    {
      $brandName = $1;
    }

    while ((length($brandName) > 24) && ($brandName =~ m/\s/))
    {
      $brandName =~ m/^(.*)\s.*$/;
      $brandName = $1;
    }

    $catName = $segmentNameHash{$catSegmentID};

    #if the category name is already in the brand, don't display twice
    if ($brandName =~ m/$catName/i)
    {
      $catName = "";
    }

    #transform category to singular if it's plural
    if (($catName =~ m/(.*)s$/i) && !($catName =~ m/.*ies$/i))
    {
      $catName = $1;
    }

    $segmentName = $segmentNameHash{$segmentSegmentID};

    #transform segment to singular if it's plural
    if ($segmentName =~ m/(.*)s$/i)
    {
      $segmentName = $1;
    }

    #if the segment is the same as the category, drop it
    if ($segmentName eq $catName)
    {
      $segmentName = "";
    }

    #strip "original/" off of front of segment name
    if ($segmentName =~ m/^original\/(.*)$/i)
    {
      $segmentName = $1;
    }

    $organicName = $segmentNameHash{$organicSegmentID};
    if ($organicName =~ m/not applicable/i)
    {
      $organicName = "";
    }

    $formName = $segmentNameHash{$formSegmentID};
    if ($formName =~ m/not applicable/i)
    {
      $formName = "";
    }
    if ($formName eq $segmentName)
    {
      $formName = "";
    }

    $packageName = $segmentNameHash{$packageSegmentID};
    if ($packageName eq "")
    {
      $packageName = AIProductNaming_attrs_packaging_from_name($prodNameHash{$id});
    }
    if ($packageName =~ m/not applicable|not stated/i)
    {
      $packageName = "";
    }
    if ($packageName eq $segmentName)
    {
      $packageName = "";
    }

    $flavorName = $segmentNameHash{$flavorSegmentID};
    if ($flavorName =~ m/unflavored|not applicable|not stated/i)
    {
      $flavorName = "";
    }
    if ($flavorName =~ m/not applicable/i)
    {
      $flavorName = "";
    }
    if ($flavorName eq $segmentName)
    {
      $flavorName = "";
    }

    #if we have a valid flavor, and the word "flavor" is in the segment, it's
    #probably duplicate info
    if ((length($flavorName) > 1) && ($segmentName =~ m/flavor/i))
    {
      $segmentName = "";
    }

    #if the segment name is part of the flavor name, drop it as dupe info
    if ($flavorName =~ m/$segmentName/i)
    {
      $segmentName = "";
    }

    #if the flavor name is part of the brand name, drop it as dupe info
    if ($brandName =~ m/$flavorName/i)
    {
      $flavorName = "";
    }

    $colorName = $segmentNameHash{$colorSegmentID};
    if ($colorName =~ m/not applicable|multiple color/i)
    {
      $colorName = "";
    }
    if (length($flavorName) > 0)
    {
      $colorName = "";
    }
    if (($flavorName eq "") && ($colorName eq ""))
    {
      $colorName = AIProductNaming_attrs_color_from_name($prodNameHash{$id});
    }

    $materialName = $segmentNameHash{$materialSegmentID};
    if ($materialName =~ m/not applicable/i)
    {
      $materialName = "";
    }
    if (length($flavorName) > 0)
    {
      $materialName = "";
    }

    $packSizeName = $segmentNameHash{$packSizeSegmentID};
    if ($packSizeName =~ m/not applicable/i)
    {
      $packSizeName = "";
    }
    if ($packSizeName =~ m/(.*) count/i)
    {
      $packSizeName = "$1-PACK";
    }

    $sizeName = $segmentNameHash{$sizeSegmentID};
    if ($sizeName =~ m/(.*) fluid ounce$/i)
    {
      $sizeName = "$1 FL OZ";
    }
    elsif ($sizeName =~ m/(.*) ounce$/i)
    {
      $sizeName = "$1 OZ";
    }
    elsif ($sizeName =~ m/(.*) count$/i)
    {
      $sizeName = "$1 CT";
    }

    if ($sizeName eq "")
    {
      $sizeName = AIProductNaming_attrs_base_size_from_name($prodNameHash{$id});
    }

    $name = "$brandName $catName $organicName $segmentName $formName $packageName $colorName $materialName $flavorName $packSizeName $sizeName";

    if ($appendUPC > 0)
    {
      $name .= " $upcHash{$id}";
    }

    #trim any extra whitespace out of the name
    $name =~ s/\s+/ /g;
    $q_name = $db->quote($name);

    $query = "UPDATE $dsSchema.products SET alias=$q_name WHERE ID=$id";
    $db->do($query);
  }
}



#-------------------------------------------------------------------------------


1;
