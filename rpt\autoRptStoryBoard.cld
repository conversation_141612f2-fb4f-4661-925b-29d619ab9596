#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::AutoReports;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Automated Reporting Story Board</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
\$(function()
{
  \$('[data-bs-toggle="popover"]').popover()
})
</SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/rpt/main">Reports</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">$storyNameHash{$story} Story</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser and get the forecast to be deleted
  $q = new CGI;
  $dsID = $q->param('ds');
  $story = $q->param('s');

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  $db = KAPutil_connect_to_database();

  #get our data source name
  $dsName = ds_id_to_name($db, $dsID);

  #provide a human-readable story name
  %storyNameHash = (
    "catov" => "Category Overview",
    "tlbr" => "Topline Business Review",
    "bldr" => "Baseline Drivers",
    "incdr" => "Incremental Drivers",
  );

  print_html_header();

  #make sure we have write privs for this data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to create reports in this data source.");
  }

  print <<END_HTML;
<P>&nbsp;</P>
<FORM METHOD="post" ACTION="autoRptBuild.cld">
<INPUT TYPE="hidden" NAME="ds" VALUE="$dsID">
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col">
END_HTML

  #if we're doing the category review demo deck
  if ($story eq "catov")
  {

    #make sure the DS has everything we need to generate the report
    $prereqHTML = autorpt_prereqs_catov_topline_brands($db, $dsID);
    if (length($prereqHTML) > 0)
    {
      $popOverContent = "This report requires:<BR><UL>$prereqHTML</UL>";
      $chkBoxStatus = "DISABLED";
      $chkBoxText = "Unavailable";
      $popOverIcon = "bi bi-info-circle";
    }
    else
    {
      $popOverContent = " ";
      $chkBoxStatus = "CHECKED";
      $chkBoxText = "Include";
      $popOverIcon = "";
    }

    print <<END_HTML;
      <DIV CLASS="row">
        <INPUT TYPE="hidden" NAME="s" VALUE="cr">

        <DIV CLASS="col">
          <DIV CLASS="card border-primary h-100">
            <DIV CLASS="card-header bg-primary text-white">Topline Brand Trends</DIV>
            <DIV CLASS="card-body">
              <IMG CLASS="img-thumbnail mx-auto d-block" SRC="/images/autorpt_catov_topline_brands.png">
              <P>
            </DIV>
            <DIV CLASS="card-footer border-white bg-white">
              <DIV CLASS="form-check">
                <INPUT CLASS="form-check-input" NAME="catov_topline_brands" ID="catov_topline_brands" TYPE="checkbox" $chkBoxStatus>
                <LABEL CLASS="form-check-label" FOR="catov_topline_brands">$chkBoxText</LABEL>
                <A HREF="#" data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                   data-bs-content="$popOverContent">
                   <I CLASS="$popOverIcon"></I>
                </A>
              </DIV>
            </DIV>
          </DIV>
        </DIV>
END_HTML

    $prereqHTML = autorpt_prereqs_catov_topline_retailers($db, $dsID);
    if (length($prereqHTML) > 0)
    {
      $popOverContent = "This report requires:<BR><UL>$prereqHTML</UL>";
      $chkBoxStatus = "DISABLED";
      $chkBoxText = "Unavailable";
      $popOverIcon = "bi bi-info-circle";
    }
    else
    {
      $popOverContent = " ";
      $chkBoxStatus = "CHECKED";
      $chkBoxText = "Include";
      $popOverIcon = "";
    }

    print <<END_HTML;
        <DIV CLASS="col">
          <DIV CLASS="card border-primary h-100">
            <DIV CLASS="card-header bg-primary text-white">Topline Retailer Trends</DIV>
            <DIV CLASS="card-body">
              <IMG CLASS="img-thumbnail mx-auto d-block" SRC="/images/autorpt_catov_topline_retailers.png">
              <P>
            </DIV>
            <DIV CLASS="card-footer border-white bg-white">
              <DIV CLASS="form-check">
                <INPUT CLASS="form-check-input" NAME="catov_topline_retailers" ID="catov_topline_retailers" TYPE="checkbox" $chkBoxStatus>
                <LABEL CLASS="form-check-label" FOR="catov_topline_retailers">$chkBoxText</LABEL>
                <A HREF="#" data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                   data-bs-content="$popOverContent">
                   <I CLASS="$popOverIcon"></I>
                </A>
              </DIV>
            </DIV>
          </DIV>
        </DIV>
END_HTML

    $prereqHTML = autorpt_prereqs_catov_brand_rank($db, $dsID);
    if (length($prereqHTML) > 0)
    {
      $popOverContent = "This report requires:<BR><UL>$prereqHTML</UL>";
      $chkBoxStatus = "DISABLED";
      $chkBoxText = "Unavailable";
      $popOverIcon = "bi bi-info-circle";
    }
    else
    {
      $popOverContent = " ";
      $chkBoxStatus = "CHECKED";
      $chkBoxText = "Include";
      $popOverIcon = "";
    }

    print <<END_HTML;
        <DIV CLASS="col">
          <DIV CLASS="card border-primary h-100">
            <DIV CLASS="card-header bg-primary text-white">Brand Ranking</DIV>
            <DIV CLASS="card-body">
              <IMG CLASS="img-thumbnail mx-auto d-block" SRC="/images/autorpt_catov_brand_rank.png">
              <P>
            </DIV>
            <DIV CLASS="card-footer border-white bg-white">
              <DIV CLASS="form-check">
                <INPUT CLASS="form-check-input" NAME="catov_brand_rank" ID="catov_brand_rank" TYPE="checkbox" $chkBoxStatus>
                <LABEL CLASS="form-check-label" FOR="catov_brand_rank">$chkBoxText</LABEL>
                <A HREF="#" data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                   data-bs-content="$popOverContent">
                   <I CLASS="$popOverIcon"></I>
                </A>
              </DIV>
            </DIV>
          </DIV>
        </DIV>
END_HTML

    $prereqHTML = autorpt_prereqs_catov_item_rank($db, $dsID);
    if (length($prereqHTML) > 0)
    {
      $popOverContent = "This report requires:<BR><UL>$prereqHTML</UL>";
      $chkBoxStatus = "DISABLED";
      $chkBoxText = "Unavailable";
      $popOverIcon = "bi bi-info-circle";
    }
    else
    {
      $popOverContent = " ";
      $chkBoxStatus = "CHECKED";
      $chkBoxText = "Include";
      $popOverIcon = "";
    }

    print <<END_HTML;
          <DIV CLASS="col">
            <DIV CLASS="card border-primary h-100">
            <DIV CLASS="card-header bg-primary text-white">Item Ranking</DIV>
            <DIV CLASS="card-body">
              <IMG CLASS="img-thumbnail mx-auto d-block" SRC="/images/autorpt_catov_item_rank.png">
              <P>
            </DIV>
            <DIV CLASS="card-footer border-white bg-white">
              <DIV CLASS="form-check">
                <INPUT CLASS="form-check-input" NAME="catov_item_rank" ID="catov_item_rank" TYPE="checkbox" $chkBoxStatus>
                <LABEL CLASS="form-check-label" FOR="catov_item_rank">$chkBoxText</LABEL>
                <A HREF="#" data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                   data-bs-content="$popOverContent">
                   <I CLASS="$popOverIcon"></I>
                </A>
              </DIV>
            </DIV>
          </DIV>
        </DIV>

      </DIV>  <!-- card row -->
      <P>
END_HTML

    $prereqHTML = autorpt_prereqs_catov_rtlr_distro_trends($db, $dsID);
    if (length($prereqHTML) > 0)
    {
      $popOverContent = "This report requires:<BR><UL>$prereqHTML</UL>";
      $chkBoxStatus = "DISABLED";
      $chkBoxText = "Unavailable";
      $popOverIcon = "bi bi-info-circle";
    }
    else
    {
      $popOverContent = " ";
      $chkBoxStatus = "CHECKED";
      $chkBoxText = "Include";
      $popOverIcon = "";
    }

    print <<END_HTML;
      <DIV CLASS="row">

        <DIV CLASS="col">
          <DIV CLASS="card border-primary h-100">
            <DIV CLASS="card-header bg-primary text-white">Retailer Distribution Trends</DIV>
            <DIV CLASS="card-body">
              <IMG CLASS="img-thumbnail mx-auto d-block" SRC="/images/autorpt_catov_rtlr_distro_trends.png">
              <P>
            </DIV>
            <DIV CLASS="card-footer border-white bg-white">
              <DIV CLASS="form-check">
                <INPUT CLASS="form-check-input" NAME="catov_rtlr_distro_trends" ID="catov_rtlr_distro_trends" TYPE="checkbox" $chkBoxStatus>
                <LABEL CLASS="form-check-label" FOR="catov_rtlr_distro_trends">$chkBoxText</LABEL>
                <A HREF="#" data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                   data-bs-content="$popOverContent">
                   <I CLASS="$popOverIcon"></I>
                </A>
              </DIV>
            </DIV>
          </DIV>
        </DIV>
END_HTML


    #make sure the DS has everything we need to generate the report
    $prereqHTML = autorpt_prereqs_catov_item_distro_trends($db, $dsID);
    if (length($prereqHTML) > 0)
    {
      $popOverContent = "This report requires:<BR><UL>$prereqHTML</UL>";
      $chkBoxStatus = "DISABLED";
      $chkBoxText = "Unavailable";
      $popOverIcon = "bi bi-info-circle";
    }
    else
    {
      $popOverContent = " ";
      $chkBoxStatus = "CHECKED";
      $chkBoxText = "Include";
      $popOverIcon = "";
    }

    print <<END_HTML;
        <DIV CLASS="col">
          <DIV CLASS="card border-primary h-100">
            <DIV CLASS="card-header bg-primary text-white">Item Distribution Trends</DIV>
            <DIV CLASS="card-body">
              <IMG CLASS="img-thumbnail mx-auto d-block" SRC="/images/autorpt_catov_item_distro_trends.png">
              <P>
            </DIV>
            <DIV CLASS="card-footer border-white bg-white">
              <DIV CLASS="form-check">
                <INPUT CLASS="form-check-input" NAME="catov_item_distro_trends" ID="catov_item_distro_trends" TYPE="checkbox" $chkBoxStatus>
                <LABEL CLASS="form-check-label" FOR="catov_item_distro_trends">$chkBoxText</LABEL>
                <A HREF="#" data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                   data-bs-content="$popOverContent">
                   <I CLASS="$popOverIcon"></I>
                </A>
              </DIV>
            </DIV>
          </DIV>
        </DIV>
END_HTML

    $prereqHTML = autorpt_prereqs_catov_retailer_acv_comp($db, $dsID);
    if (length($prereqHTML) > 0)
    {
      $popOverContent = "This report requires:<BR><UL>$prereqHTML</UL>";
      $chkBoxStatus = "DISABLED";
      $chkBoxText = "Unavailable";
      $popOverIcon = "bi bi-info-circle";
    }
    else
    {
      $popOverContent = " ";
      $chkBoxStatus = "CHECKED";
      $chkBoxText = "Include";
      $popOverIcon = "";
    }

    print <<END_HTML;
        <DIV CLASS="col">
          <DIV CLASS="card border-primary h-100">
            <DIV CLASS="card-header bg-primary text-white">Retailer ACV Comparison</DIV>
            <DIV CLASS="card-body">
              <IMG CLASS="img-thumbnail mx-auto d-block" SRC="/images/autorpt_catov_retailer_acv_comp.png">
              <P>
            </DIV>
            <DIV CLASS="card-footer border-white bg-white">
              <DIV CLASS="form-check">
                <INPUT CLASS="form-check-input" NAME="catov_retailer_acv_comp" ID="catov_retailer_acv_comp" TYPE="checkbox" $chkBoxStatus>
                <LABEL CLASS="form-check-label" FOR="catov_retailer_acv_comp">$chkBoxText</LABEL>
                <A HREF="#" data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                   data-bs-content="$popOverContent">
                   <I CLASS="$popOverIcon"></I>
                </A>
              </DIV>
            </DIV>
          </DIV>
        </DIV>
END_HTML

    $prereqHTML = autorpt_prereqs_catov_change_distro($db, $dsID);
    if (length($prereqHTML) > 0)
    {
      $popOverContent = "This report requires:<BR><UL>$prereqHTML</UL>";
      $chkBoxStatus = "DISABLED";
      $chkBoxText = "Unavailable";
      $popOverIcon = "bi bi-info-circle";
    }
    else
    {
      $popOverContent = " ";
      $chkBoxStatus = "CHECKED";
      $chkBoxText = "Include";
      $popOverIcon = "";
    }

    print <<END_HTML;
        <DIV CLASS="col">
          <DIV CLASS="card border-primary h-100">
            <DIV CLASS="card-header bg-primary text-white">Change in Distribution</DIV>
            <DIV CLASS="card-body">
              <IMG CLASS="img-thumbnail mx-auto d-block" SRC="/images/autorpt_catov_change_distro.png">
              <P>
            </DIV>
            <DIV CLASS="card-footer border-white bg-white">
              <DIV CLASS="form-check">
                <INPUT CLASS="form-check-input" NAME="catov_change_distro" ID="catov_change_distro" TYPE="checkbox" $chkBoxStatus>
                <LABEL CLASS="form-check-label" FOR="catov_change_distro">$chkBoxText</LABEL>
                <A HREF="#" data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                   data-bs-content="$popOverContent">
                   <I CLASS="$popOverIcon"></I>
                </A>
              </DIV>
            </DIV>
          </DIV>
        </DIV>
      </DIV>  <!-- card row -->
END_HTML

    $prereqHTML = autorpt_prereqs_catov_price_tracker($db, $dsID);
    if (length($prereqHTML) > 0)
    {
      $popOverContent = "This report requires:<BR><UL>$prereqHTML</UL>";
      $chkBoxStatus = "DISABLED";
      $chkBoxText = "Unavailable";
      $popOverIcon = "bi bi-info-circle";
    }
    else
    {
      $popOverContent = " ";
      $chkBoxStatus = "CHECKED";
      $chkBoxText = "Include";
      $popOverIcon = "";
    }

    print <<END_HTML;
  <P>
  <DIV CLASS="row">

    <DIV CLASS="col">
      <DIV CLASS="card border-primary h-100">
        <DIV CLASS="card-header bg-primary text-white">Price Tracker</DIV>
        <DIV CLASS="card-body">
          <IMG CLASS="img-thumbnail mx-auto d-block" SRC="/images/autorpt_catov_price_tracker.png">
          <P>
        </DIV>
        <DIV CLASS="card-footer border-white bg-white">
          <DIV CLASS="form-check">
            <INPUT CLASS="form-check-input" NAME="catov_price_tracker" ID="catov_price_tracker" TYPE="checkbox" $chkBoxStatus>
            <LABEL CLASS="form-check-label" FOR="catov_price_tracker">$chkBoxText</LABEL>
            <A HREF="#" data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
               data-bs-content="$popOverContent">
               <I CLASS="$popOverIcon"></I>
            </A>
          </DIV>
        </DIV>
      </DIV>
    </DIV>
END_HTML

    $prereqHTML = autorpt_prereqs_catov_units_sold_price($db, $dsID);
    if (length($prereqHTML) > 0)
    {
      $popOverContent = "This report requires:<BR><UL>$prereqHTML</UL>";
      $chkBoxStatus = "DISABLED";
      $chkBoxText = "Unavailable";
      $popOverIcon = "bi bi-info-circle";
    }
    else
    {
      $popOverContent = " ";
      $chkBoxStatus = "CHECKED";
      $chkBoxText = "Include";
      $popOverIcon = "";
    }

  print <<END_HTML;
    <DIV CLASS="col">
      <DIV CLASS="card border-primary h-100">
        <DIV CLASS="card-header bg-primary text-white">Units Sold by Price</DIV>
        <DIV CLASS="card-body">
          <IMG CLASS="img-thumbnail mx-auto d-block" SRC="/images/autorpt_catov_units_sold_price.png">
          <P>
        </DIV>
        <DIV CLASS="card-footer border-white bg-white">
          <DIV CLASS="form-check">
            <INPUT CLASS="form-check-input" NAME="catov_units_sold_price" ID="catov_units_sold_price" TYPE="checkbox" $chkBoxStatus>
            <LABEL CLASS="form-check-label" FOR="catov_units_sold_price">$chkBoxText</LABEL>
            <A HREF="#" data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
               data-bs-content="$popOverContent">
               <I CLASS="$popOverIcon"></I>
            </A>
          </DIV>
        </DIV>
      </DIV>
    </DIV>
END_HTML

    $prereqHTML = autorpt_prereqs_catov_promo_tracker_dollars($db, $dsID);
    if (length($prereqHTML) > 0)
    {
      $popOverContent = "This report requires:<BR><UL>$prereqHTML</UL>";
      $chkBoxStatus = "DISABLED";
      $chkBoxText = "Unavailable";
      $popOverIcon = "bi bi-info-circle";
    }
    else
    {
      $popOverContent = " ";
      $chkBoxStatus = "CHECKED";
      $chkBoxText = "Include";
      $popOverIcon = "";
    }

    print <<END_HTML;
        <DIV CLASS="col">
          <DIV CLASS="card border-primary h-100">
            <DIV CLASS="card-header bg-primary text-white">Promotion Tracker \$</DIV>
            <DIV CLASS="card-body">
              <IMG CLASS="img-thumbnail mx-auto d-block" SRC="/images/autorpt_catov_promo_tracker_dollars.png">
              <P>
            </DIV>
            <DIV CLASS="card-footer border-white bg-white">
              <DIV CLASS="form-check">
                <INPUT CLASS="form-check-input" NAME="catov_promo_tracker_dollars" ID="catov_promo_tracker_dollars" TYPE="checkbox" $chkBoxStatus>
                <LABEL CLASS="form-check-label" FOR="catov_promo_tracker_dollars">$chkBoxText</LABEL>
                <A HREF="#" data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                   data-bs-content="$popOverContent">
                   <I CLASS="$popOverIcon"></I>
                </A>
              </DIV>
            </DIV>
          </DIV>
        </DIV>
END_HTML

    $prereqHTML = autorpt_prereqs_catov_promo_tracker_units($db, $dsID);
    if (length($prereqHTML) > 0)
    {
      $popOverContent = "This report requires:<BR><UL>$prereqHTML</UL>";
      $chkBoxStatus = "DISABLED";
      $chkBoxText = "Unavailable";
      $popOverIcon = "bi bi-info-circle";
    }
    else
    {
      $popOverContent = " ";
      $chkBoxStatus = "CHECKED";
      $chkBoxText = "Include";
      $popOverIcon = "";
    }

    print <<END_HTML;
        <DIV CLASS="col">
          <DIV CLASS="card border-primary h-100">
            <DIV CLASS="card-header bg-primary text-white">Promo Tracker Units</DIV>
            <DIV CLASS="card-body">
              <IMG CLASS="img-thumbnail mx-auto d-block" SRC="/images/autorpt_catov_promo_tracker_units.png">
              <P>
            </DIV>
            <DIV CLASS="card-footer border-white bg-white">
              <DIV CLASS="form-check">
                <INPUT CLASS="form-check-input" NAME="catov_promo_tracker_units" ID="catov_promo_tracker_units" TYPE="checkbox" $chkBoxStatus>
                <LABEL CLASS="form-check-label" FOR="catov_promo_tracker_units">$chkBoxText</LABEL>
                <A HREF="#" data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                   data-bs-content="$popOverContent">
                   <I CLASS="$popOverIcon"></I>
                </A>
              </DIV>
            </DIV>
          </DIV>
        </DIV>
      </DIV>  <!-- card row -->
END_HTML

    $prereqHTML = autorpt_prereqs_catov_lift_vs_discount($db, $dsID);
    if (length($prereqHTML) > 0)
    {
      $popOverContent = "This report requires:<BR><UL>$prereqHTML</UL>";
      $chkBoxStatus = "DISABLED";
      $chkBoxText = "Unavailable";
      $popOverIcon = "bi bi-info-circle";
    }
    else
    {
      $popOverContent = " ";
      $chkBoxStatus = "CHECKED";
      $chkBoxText = "Include";
      $popOverIcon = "";
    }

    print <<END_HTML;
      <P>
      <DIV CLASS="row">

        <DIV CLASS="col-3">
          <DIV CLASS="card border-primary h-100">
            <DIV CLASS="card-header bg-primary text-white">Lift vs Discount</DIV>
            <DIV CLASS="card-body">
              <IMG CLASS="img-thumbnail mx-auto d-block" SRC="/images/autorpt_catov_lift_vs_discount.png">
              <P>
            </DIV>
            <DIV CLASS="card-footer border-white bg-white">
              <DIV CLASS="form-check">
                <INPUT CLASS="form-check-input" NAME="catov_lift_vs_discount" ID="catov_lift_vs_discount" TYPE="checkbox" $chkBoxStatus>
                <LABEL CLASS="form-check-label" FOR="catov_lift_vs_discount">$chkBoxText</LABEL>
                <A HREF="#" data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                   data-bs-content="$popOverContent">
                   <I CLASS="$popOverIcon"></I>
                </A>
              </DIV>
            </DIV>
          </DIV>
        </DIV>
END_HTML
  }


  #if we're doing the topline business review deck
  if ($story eq "tlbr")
  {
    $prereqHTML = autorpt_prereqs_tlbr_overview($db, $dsID);
    if (length($prereqHTML) > 0)
    {
      $popOverContent = "This report requires:<BR><UL>$prereqHTML</UL>";
      $chkBoxStatus = "DISABLED";
      $chkBoxText = "Unavailable";
      $popOverIcon = "bi bi-info-circle";
    }
    else
    {
      $popOverContent = " ";
      $chkBoxStatus = "CHECKED";
      $chkBoxText = "Include";
      $popOverIcon = "";
    }

    print <<END_HTML;
      <DIV CLASS="row">
        <INPUT TYPE="hidden" NAME="s" VALUE="tlbr">

        <DIV CLASS="col">
          <DIV CLASS="card border-primary h-100">
            <DIV CLASS="card-header bg-primary text-white">Topline Overview</DIV>
            <DIV CLASS="card-body">
              <IMG CLASS="img-thumbnail mx-auto d-block" SRC="/images/autorpt_tlbr_ov.png">
              <P>
            </DIV>
            <DIV CLASS="card-footer border-white bg-white">
              <DIV CLASS="form-check">
                <INPUT CLASS="form-check-input" NAME="tlbr_ov" ID="tlbr_ov" TYPE="checkbox" $chkBoxStatus>
                <LABEL CLASS="form-check-label" FOR="tlbr_ov">$chkBoxText</LABEL>
                <A HREF="#" data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                   data-bs-content="$popOverContent">
                   <I CLASS="$popOverIcon"></I>
                </A>
              </DIV>
            </DIV>
          </DIV>
        </DIV>
END_HTML

    $prereqHTML = autorpt_prereqs_tlbr_total_bus_crosscat($db, $dsID);
    if (length($prereqHTML) > 0)
    {
      $popOverContent = "This report requires:<BR><UL>$prereqHTML</UL>";
      $chkBoxStatus = "DISABLED";
      $chkBoxText = "Unavailable";
      $popOverIcon = "bi bi-info-circle";
    }
    else
    {
      $popOverContent = " ";
      $chkBoxStatus = "CHECKED";
      $chkBoxText = "Include";
      $popOverIcon = "";
    }

    print <<END_HTML;
        <DIV CLASS="col">
          <DIV CLASS="card border-primary h-100">
            <DIV CLASS="card-header bg-primary text-white">Total Business Cross Category</DIV>
            <DIV CLASS="card-body">
              <IMG CLASS="img-thumbnail mx-auto d-block" SRC="/images/autorpt_tlbr_totbustopline.png">
              <P>
            </DIV>
            <DIV CLASS="card-footer border-white bg-white">
              <DIV CLASS="form-check">
                <INPUT CLASS="form-check-input" NAME="tlbr_totbustopline" ID="tlbr_totbustopline" TYPE="checkbox" $chkBoxStatus>
                <LABEL CLASS="form-check-label" FOR="tlbr_totbustopline">$chkBoxText</LABEL>
                <A HREF="#" data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                   data-bs-content="$popOverContent">
                   <I CLASS="$popOverIcon"></I>
                </A>
              </DIV>
            </DIV>
          </DIV>
        </DIV>
END_HTML

    $prereqHTML = autorpt_prereqs_tlbr_category_snapshot($db, $dsID);
    if (length($prereqHTML) > 0)
    {
      $popOverContent = "This report requires:<BR><UL>$prereqHTML</UL>";
      $chkBoxStatus = "DISABLED";
      $chkBoxText = "Unavailable";
      $popOverIcon = "bi bi-info-circle";
    }
    else
    {
      $popOverContent = " ";
      $chkBoxStatus = "CHECKED";
      $chkBoxText = "Include";
      $popOverIcon = "";
    }

    print <<END_HTML;
        <DIV CLASS="col">
          <DIV CLASS="card border-primary h-100">
            <DIV CLASS="card-header bg-primary text-white">Category Snapshot</DIV>
            <DIV CLASS="card-body">
              <IMG CLASS="img-thumbnail mx-auto d-block" SRC="/images/autorpt_tlbr_cat_snap.png">
              <P>
            </DIV>
            <DIV CLASS="card-footer border-white bg-white">
              <DIV CLASS="form-check">
                <INPUT CLASS="form-check-input" NAME="tlbr_cat_snap" ID="tlbr_cat_snap" TYPE="checkbox" $chkBoxStatus>
                <LABEL CLASS="form-check-label" FOR="tlbr_cat_snap">$chkBoxText</LABEL>
                <A HREF="#" data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                   data-bs-content="$popOverContent">
                   <I CLASS="$popOverIcon"></I>
                </A>
              </DIV>
            </DIV>
          </DIV>
        </DIV>
END_HTML

    $prereqHTML = autorpt_prereqs_tlbr_retailer_item_trends($db, $dsID);
    if (length($prereqHTML) > 0)
    {
      $popOverContent = "This report requires:<BR><UL>$prereqHTML</UL>";
      $chkBoxStatus = "DISABLED";
      $chkBoxText = "Unavailable";
      $popOverIcon = "bi bi-info-circle";
    }
    else
    {
      $popOverContent = " ";
      $chkBoxStatus = "CHECKED";
      $chkBoxText = "Include";
      $popOverIcon = "";
    }

    print <<END_HTML;
        <DIV CLASS="col">
          <DIV CLASS="card border-primary h-100">
            <DIV CLASS="card-header bg-primary text-white">Retailers Driving Item Trends</DIV>
            <DIV CLASS="card-body">
              <IMG CLASS="img-thumbnail mx-auto d-block" SRC="/images/autorpt_tlbr_rtl_item_trends.png">
              <P>
            </DIV>
            <DIV CLASS="card-footer border-white bg-white">
              <DIV CLASS="form-check">
                <INPUT CLASS="form-check-input" NAME="tlbr_rtl_item_trends" ID="tlbr_rtl_item_trends" TYPE="checkbox" $chkBoxStatus>
                <LABEL CLASS="form-check-label" FOR="tlbr_rtl_item_trends">$chkBoxText</LABEL>
                <A HREF="#" data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                   data-bs-content="$popOverContent">
                   <I CLASS="$popOverIcon"></I>
                </A>
              </DIV>
            </DIV>
          </DIV>
        </DIV>

      </DIV>  <!-- card row -->

      <P>&nbsp;</P>
END_HTML

    $prereqHTML = autorpt_prereqs_tlbr_retailer_brand_trends($db, $dsID);
    if (length($prereqHTML) > 0)
    {
      $popOverContent = "This report requires:<BR><UL>$prereqHTML</UL>";
      $chkBoxStatus = "DISABLED";
      $chkBoxText = "Unavailable";
      $popOverIcon = "bi bi-info-circle";
    }
    else
    {
      $popOverContent = " ";
      $chkBoxStatus = "CHECKED";
      $chkBoxText = "Include";
      $popOverIcon = "";
    }

    print <<END_HTML;
      <DIV CLASS="row">

        <DIV CLASS="col-3">
          <DIV CLASS="card border-primary h-100">
            <DIV CLASS="card-header bg-primary text-white">Retailers Driving Brand Trends</DIV>
            <DIV CLASS="card-body">
              <IMG CLASS="img-thumbnail mx-auto d-block" SRC="/images/autorpt_tlbr_rtl_brand_trends.png">
              <P>
            </DIV>
            <DIV CLASS="card-footer border-white bg-white">
              <DIV CLASS="form-check">
                <INPUT CLASS="form-check-input" NAME="tlbr_rtl_brand_trends" ID="tlbr_rtl_brand_trends" TYPE="checkbox" $chkBoxStatus>
                <LABEL CLASS="form-check-label" FOR="tlbr_rtl_brand_trends">$chkBoxText</LABEL>
                <A HREF="#" data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                   data-bs-content="$popOverContent">
                   <I CLASS="$popOverIcon"></I>
                </A>
              </DIV>
            </DIV>
          </DIV>
        </DIV>
END_HTML

    $prereqHTML = autorpt_prereqs_tlbr_prod_trends($db, $dsID);
    if (length($prereqHTML) > 0)
    {
      $popOverContent = "This report requires:<BR><UL>$prereqHTML</UL>";
      $chkBoxStatus = "DISABLED";
      $chkBoxText = "Unavailable";
      $popOverIcon = "bi bi-info-circle";
    }
    else
    {
      $popOverContent = " ";
      $chkBoxStatus = "CHECKED";
      $chkBoxText = "Include";
      $popOverIcon = "";
    }

    print <<END_HTML;
        <DIV CLASS="col-3">
          <DIV CLASS="card border-primary h-100">
            <DIV CLASS="card-header bg-primary text-white">Products Driving Trends</DIV>
            <DIV CLASS="card-body">
              <IMG CLASS="img-thumbnail mx-auto d-block" SRC="/images/autorpt_tlbr_prod_trends.png">
              <P>
            </DIV>
            <DIV CLASS="card-footer border-white bg-white">
              <DIV CLASS="form-check">
                <INPUT CLASS="form-check-input" NAME="tlbr_prod_trends" ID="tlbr_prod_trends" TYPE="checkbox" $chkBoxStatus>
                <LABEL CLASS="form-check-label" FOR="tlbr_prod_trends">$chkBoxText</LABEL>
                <A HREF="#" data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                   data-bs-content="$popOverContent">
                   <I CLASS="$popOverIcon"></I>
                </A>
              </DIV>
            </DIV>
          </DIV>
        </DIV>

      </DIV>  <!-- card row -->
END_HTML
  }

  #if we're doing the baseline drivers deck
  if ($story eq "bldr")
  {
    $prereqHTML = autorpt_prereqs_bldr_base_vs_incr($db, $dsID);
    if (length($prereqHTML) > 0)
    {
      $popOverContent = "This report requires:<BR><UL>$prereqHTML</UL>";
      $chkBoxStatus = "DISABLED";
      $chkBoxText = "Unavailable";
      $popOverIcon = "bi bi-info-circle";
    }
    else
    {
      $popOverContent = " ";
      $chkBoxStatus = "CHECKED";
      $chkBoxText = "Include";
      $popOverIcon = "";
    }

    print <<END_HTML;
      <DIV CLASS="row">
        <INPUT TYPE="hidden" NAME="s" VALUE="bldr">

        <DIV CLASS="col">
          <DIV CLASS="card border-primary h-100">
            <DIV CLASS="card-header bg-primary text-white">Base vs Incremental Sales</DIV>
            <DIV CLASS="card-body">
              <IMG CLASS="img-thumbnail mx-auto d-block" SRC="/images/autorpt_bldr_base_vs_incr.png">
              <P>
            </DIV>
            <DIV CLASS="card-footer border-white bg-white">
              <DIV CLASS="form-check">
                <INPUT CLASS="form-check-input" NAME="bldr_base_vs_incr" ID="bldr_base_vs_incr" TYPE="checkbox" $chkBoxStatus>
                <LABEL CLASS="form-check-label" FOR="bldr_base_vs_incr">$chkBoxText</LABEL>
                <A HREF="#" data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                   data-bs-content="$popOverContent">
                   <I CLASS="$popOverIcon"></I>
                </A>
              </DIV>
            </DIV>
          </DIV>
        </DIV>
END_HTML

    $prereqHTML = autorpt_prereqs_bldr_cause_base_change($db, $dsID);
    if (length($prereqHTML) > 0)
    {
      $popOverContent = "This report requires:<BR><UL>$prereqHTML</UL>";
      $chkBoxStatus = "DISABLED";
      $chkBoxText = "Unavailable";
      $popOverIcon = "bi bi-info-circle";
    }
    else
    {
      $popOverContent = " ";
      $chkBoxStatus = "CHECKED";
      $chkBoxText = "Include";
      $popOverIcon = "";
    }

    print <<END_HTML;
        <DIV CLASS="col">
          <DIV CLASS="card border-primary h-100">
            <DIV CLASS="card-header bg-primary text-white">Causes of Base Change</DIV>
            <DIV CLASS="card-body">
              <IMG CLASS="img-thumbnail mx-auto d-block" SRC="/images/autorpt_bldr_cause_base_change.png">
              <P>
            </DIV>
            <DIV CLASS="card-footer border-white bg-white">
              <DIV CLASS="form-check">
                <INPUT CLASS="form-check-input" NAME="bldr_cause_base_change" ID="bldr_cause_base_change" TYPE="checkbox" $chkBoxStatus>
                <LABEL CLASS="form-check-label" FOR="bldr_cause_base_change">$chkBoxText</LABEL>
                <A HREF="#" data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                   data-bs-content="$popOverContent">
                   <I CLASS="$popOverIcon"></I>
                </A>
              </DIV>
            </DIV>
          </DIV>
        </DIV>
END_HTML

    $prereqHTML = autorpt_prereqs_bldr_distribution($db, $dsID);
    if (length($prereqHTML) > 0)
    {
      $popOverContent = "This report requires:<BR><UL>$prereqHTML</UL>";
      $chkBoxStatus = "DISABLED";
      $chkBoxText = "Unavailable";
      $popOverIcon = "bi bi-info-circle";
    }
    else
    {
      $popOverContent = " ";
      $chkBoxStatus = "CHECKED";
      $chkBoxText = "Include";
      $popOverIcon = "";
    }

    print <<END_HTML;
        <DIV CLASS="col">
          <DIV CLASS="card border-primary h-100">
            <DIV CLASS="card-header bg-primary text-white">Distribution</DIV>
            <DIV CLASS="card-body">
              <IMG CLASS="img-thumbnail mx-auto d-block" SRC="/images/autorpt_bldr_distribution.png">
              <P>
            </DIV>
            <DIV CLASS="card-footer border-white bg-white">
              <DIV CLASS="form-check">
                <INPUT CLASS="form-check-input" NAME="bldr_distribution" ID="bldr_distribution" TYPE="checkbox" $chkBoxStatus>
                <LABEL CLASS="form-check-label" FOR="bldr_distribution">$chkBoxText</LABEL>
                <A HREF="#" data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                   data-bs-content="$popOverContent">
                   <I CLASS="$popOverIcon"></I>
                </A>
              </DIV>
            </DIV>
          </DIV>
        </DIV>
END_HTML

    $prereqHTML = autorpt_prereqs_bldr_everyday_price($db, $dsID);
    if (length($prereqHTML) > 0)
    {
      $popOverContent = "This report requires:<BR><UL>$prereqHTML</UL>";
      $chkBoxStatus = "DISABLED";
      $chkBoxText = "Unavailable";
      $popOverIcon = "bi bi-info-circle";
    }
    else
    {
      $popOverContent = " ";
      $chkBoxStatus = "CHECKED";
      $chkBoxText = "Include";
      $popOverIcon = "";
    }

    print <<END_HTML;
        <DIV CLASS="col">
          <DIV CLASS="card border-primary h-100">
            <DIV CLASS="card-header bg-primary text-white">Everyday Price</DIV>
            <DIV CLASS="card-body">
              <IMG CLASS="img-thumbnail mx-auto d-block" SRC="/images/autorpt_bldr_everyday_price.png">
              <P>
            </DIV>
            <DIV CLASS="card-footer border-white bg-white">
              <DIV CLASS="form-check">
                <INPUT CLASS="form-check-input" NAME="bldr_everyday_price" ID="bldr_everyday_price" TYPE="checkbox" $chkBoxStatus>
                <LABEL CLASS="form-check-label" FOR="bldr_everyday_price">$chkBoxText</LABEL>
                <A HREF="#" data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                   data-bs-content="$popOverContent">
                   <I CLASS="$popOverIcon"></I>
                </A>
              </DIV>
            </DIV>
          </DIV>
        </DIV>

      </DIV>  <!-- card row -->
END_HTML

    $prereqHTML = autorpt_prereqs_bldr_everyday_price_vs_comp($db, $dsID);
    if (length($prereqHTML) > 0)
    {
      $popOverContent = "This report requires:<BR><UL>$prereqHTML</UL>";
      $chkBoxStatus = "DISABLED";
      $chkBoxText = "Unavailable";
      $popOverIcon = "bi bi-info-circle";
    }
    else
    {
      $popOverContent = " ";
      $chkBoxStatus = "CHECKED";
      $chkBoxText = "Include";
      $popOverIcon = "";
    }

    print <<END_HTML;
      <P>&nbsp;</P>

      <DIV CLASS="row">

        <DIV CLASS="col-3">
          <DIV CLASS="card border-primary h-100">
            <DIV CLASS="card-header bg-primary text-white">Everyday Price vs Competition</DIV>
            <DIV CLASS="card-body">
              <IMG CLASS="img-thumbnail mx-auto d-block" SRC="/images/autorpt_bldr_everyday_price_vs_comp.png">
              <P>
            </DIV>
            <DIV CLASS="card-footer border-white bg-white">
              <DIV CLASS="form-check">
                <INPUT CLASS="form-check-input" NAME="bldr_everyday_price_vs_comp" ID="bldr_everyday_price_vs_comp" TYPE="checkbox" $chkBoxStatus>
                <LABEL CLASS="form-check-label" FOR="bldr_everyday_price_vs_comp">$chkBoxText</LABEL>
                <A HREF="#" data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                   data-bs-content="$popOverContent">
                   <I CLASS="$popOverIcon"></I>
                </A>
              </DIV>
            </DIV>
          </DIV>
        </DIV>
END_HTML

    $prereqHTML = autorpt_prereqs_bldr_base_price_retailers($db, $dsID);
    if (length($prereqHTML) > 0)
    {
      $popOverContent = "This report requires:<BR><UL>$prereqHTML</UL>";
      $chkBoxStatus = "DISABLED";
      $chkBoxText = "Unavailable";
      $popOverIcon = "bi bi-info-circle";
    }
    else
    {
      $popOverContent = " ";
      $chkBoxStatus = "CHECKED";
      $chkBoxText = "Include";
      $popOverIcon = "";
    }

    print <<END_HTML;
        <DIV CLASS="col-3">
          <DIV CLASS="card border-primary h-100">
            <DIV CLASS="card-header bg-primary text-white">Base Pricing Across Retailers</DIV>
            <DIV CLASS="card-body">
              <IMG CLASS="img-thumbnail mx-auto d-block" SRC="/images/autorpt_bldr_base_price_retailers.png">
              <P>
            </DIV>
            <DIV CLASS="card-footer border-white bg-white">
              <DIV CLASS="form-check">
                <INPUT CLASS="form-check-input" NAME="bldr_base_price_retailers" ID="bldr_base_price_retailers" TYPE="checkbox" $chkBoxStatus>
                <LABEL CLASS="form-check-label" FOR="bldr_base_price_retailers">$chkBoxText</LABEL>
                <A HREF="#" data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                   data-bs-content="$popOverContent">
                   <I CLASS="$popOverIcon"></I>
                </A>
              </DIV>
            </DIV>
          </DIV>
        </DIV>

      </DIV>  <!-- card row -->
END_HTML
  }

  #if we're doing the incremental drivers deck
  if ($story eq "incdr")
  {
    $prereqHTML = autorpt_prereqs_incdr_incr_vs_base($db, $dsID);
    if (length($prereqHTML) > 0)
    {
      $popOverContent = "This report requires:<BR><UL>$prereqHTML</UL>";
      $chkBoxStatus = "DISABLED";
      $chkBoxText = "Unavailable";
      $popOverIcon = "bi bi-info-circle";
    }
    else
    {
      $popOverContent = " ";
      $chkBoxStatus = "CHECKED";
      $chkBoxText = "Include";
      $popOverIcon = "";
    }

    print <<END_HTML;
      <DIV CLASS="row">
        <INPUT TYPE="hidden" NAME="s" VALUE="incdr">

        <DIV CLASS="col">
          <DIV CLASS="card border-primary h-100">
            <DIV CLASS="card-header bg-primary text-white">Incremental vs Base Sales</DIV>
            <DIV CLASS="card-body">
              <IMG CLASS="img-thumbnail mx-auto d-block" SRC="/images/autorpt_incdr_incr_vs_base.png">
              <P>
            </DIV>
            <DIV CLASS="card-footer border-white bg-white">
              <DIV CLASS="form-check">
                <INPUT CLASS="form-check-input" NAME="incdr_incr_vs_base" ID="incdr_incr_vs_base" TYPE="checkbox" $chkBoxStatus>
                <LABEL CLASS="form-check-label" FOR="incdr_incr_vs_base">$chkBoxText</LABEL>
                <A HREF="#" data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                   data-bs-content="$popOverContent">
                   <I CLASS="$popOverIcon"></I>
                </A>
              </DIV>
            </DIV>
          </DIV>
        </DIV>
END_HTML

    $prereqHTML = autorpt_prereqs_incdr_cause_incr_change($db, $dsID);
    if (length($prereqHTML) > 0)
    {
      $popOverContent = "This report requires:<BR><UL>$prereqHTML</UL>";
      $chkBoxStatus = "DISABLED";
      $chkBoxText = "Unavailable";
      $popOverIcon = "bi bi-info-circle";
    }
    else
    {
      $popOverContent = " ";
      $chkBoxStatus = "CHECKED";
      $chkBoxText = "Include";
      $popOverIcon = "";
    }

    print <<END_HTML;
        <DIV CLASS="col">
          <DIV CLASS="card border-primary h-100">
            <DIV CLASS="card-header bg-primary text-white">Causes of Incremental Change</DIV>
            <DIV CLASS="card-body">
              <IMG CLASS="img-thumbnail mx-auto d-block" SRC="/images/autorpt_incdr_cause_incr_change.png">
              <P>
            </DIV>
            <DIV CLASS="card-footer border-white bg-white">
              <DIV CLASS="form-check">
                <INPUT CLASS="form-check-input" NAME="incdr_cause_incr_change" ID="incdr_cause_incr_change" TYPE="checkbox" $chkBoxStatus>
                <LABEL CLASS="form-check-label" FOR="incdr_cause_incr_change">$chkBoxText</LABEL>
                <A HREF="#" data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                   data-bs-content="$popOverContent">
                   <I CLASS="$popOverIcon"></I>
                </A>
              </DIV>
            </DIV>
          </DIV>
        </DIV>
END_HTML

    $prereqHTML = autorpt_prereqs_incdr_incr_change_details($db, $dsID);
    if (length($prereqHTML) > 0)
    {
      $popOverContent = "This report requires:<BR><UL>$prereqHTML</UL>";
      $chkBoxStatus = "DISABLED";
      $chkBoxText = "Unavailable";
      $popOverIcon = "bi bi-info-circle";
    }
    else
    {
      $popOverContent = " ";
      $chkBoxStatus = "CHECKED";
      $chkBoxText = "Include";
      $popOverIcon = "";
    }

    print <<END_HTML;
        <DIV CLASS="col">
          <DIV CLASS="card border-primary h-100">
            <DIV CLASS="card-header bg-primary text-white">Incremental Change Details</DIV>
            <DIV CLASS="card-body">
              <IMG CLASS="img-thumbnail mx-auto d-block" SRC="/images/autorpt_incdr_incr_change_details.png">
              <P>
            </DIV>
            <DIV CLASS="card-footer border-white bg-white">
              <DIV CLASS="form-check">
                <INPUT CLASS="form-check-input" NAME="incdr_incr_change_details" ID="incdr_incr_change_details" TYPE="checkbox" $chkBoxStatus>
                <LABEL CLASS="form-check-label" FOR="incdr_incr_change_details">$chkBoxText</LABEL>
                <A HREF="#" data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                   data-bs-content="$popOverContent">
                   <I CLASS="$popOverIcon"></I>
                </A>
              </DIV>
            </DIV>
          </DIV>
        </DIV>
END_HTML

    $prereqHTML = autorpt_prereqs_incdr_promo_freq($db, $dsID);
    if (length($prereqHTML) > 0)
    {
      $popOverContent = "This report requires:<BR><UL>$prereqHTML</UL>";
      $chkBoxStatus = "DISABLED";
      $chkBoxText = "Unavailable";
      $popOverIcon = "bi bi-info-circle";
    }
    else
    {
      $popOverContent = " ";
      $chkBoxStatus = "CHECKED";
      $chkBoxText = "Include";
      $popOverIcon = "";
    }

    print <<END_HTML;
        <DIV CLASS="col">
          <DIV CLASS="card border-primary h-100">
            <DIV CLASS="card-header bg-primary text-white">Promotion Frequency</DIV>
            <DIV CLASS="card-body">
              <IMG CLASS="img-thumbnail mx-auto d-block" SRC="/images/autorpt_incdr_promo_freq.png">
              <P>
            </DIV>
            <DIV CLASS="card-footer border-white bg-white">
              <DIV CLASS="form-check">
                <INPUT CLASS="form-check-input" NAME="incdr_promo_freq" ID="incdr_promo_freq" TYPE="checkbox" $chkBoxStatus>
                <LABEL CLASS="form-check-label" FOR="incdr_promo_freq">$chkBoxText</LABEL>
                <A HREF="#" data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                   data-bs-content="$popOverContent">
                   <I CLASS="$popOverIcon"></I>
                </A>
              </DIV>
            </DIV>
          </DIV>
        </DIV>

      </DIV>  <!-- card row -->
END_HTML

    $prereqHTML = autorpt_prereqs_incdr_promo_eff($db, $dsID);
    if (length($prereqHTML) > 0)
    {
      $popOverContent = "This report requires:<BR><UL>$prereqHTML</UL>";
      $chkBoxStatus = "DISABLED";
      $chkBoxText = "Unavailable";
      $popOverIcon = "bi bi-info-circle";
    }
    else
    {
      $popOverContent = " ";
      $chkBoxStatus = "CHECKED";
      $chkBoxText = "Include";
      $popOverIcon = "";
    }

    print <<END_HTML;
      <P>&nbsp;</P>

      <DIV CLASS="row">

        <DIV CLASS="col-3">
          <DIV CLASS="card border-primary h-100">
            <DIV CLASS="card-header bg-primary text-white">Promotion Effectiveness</DIV>
            <DIV CLASS="card-body">
              <IMG CLASS="img-thumbnail mx-auto d-block" SRC="/images/autorpt_incdr_promo_eff.png">
              <P>
            </DIV>
            <DIV CLASS="card-footer border-white bg-white">
              <DIV CLASS="form-check">
                <INPUT CLASS="form-check-input" NAME="incdr_promo_eff" ID="incdr_promo_eff" TYPE="checkbox" $chkBoxStatus>
                <LABEL CLASS="form-check-label" FOR="incdr_promo_eff">$chkBoxText</LABEL>
                <A HREF="#" data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                   data-bs-content="$popOverContent">
                   <I CLASS="$popOverIcon"></I>
                </A>
              </DIV>
            </DIV>
          </DIV>
        </DIV>
END_HTML

    $prereqHTML = autorpt_prereqs_incdr_promo_discount($db, $dsID);
    if (length($prereqHTML) > 0)
    {
      $popOverContent = "This report requires:<BR><UL>$prereqHTML</UL>";
      $chkBoxStatus = "DISABLED";
      $chkBoxText = "Unavailable";
      $popOverIcon = "bi bi-info-circle";
    }
    else
    {
      $popOverContent = " ";
      $chkBoxStatus = "CHECKED";
      $chkBoxText = "Include";
      $popOverIcon = "";
    }

    print <<END_HTML;
      <DIV CLASS="col-3">
        <DIV CLASS="card border-primary h-100">
          <DIV CLASS="card-header bg-primary text-white">Promotion Discounts</DIV>
          <DIV CLASS="card-body">
            <IMG CLASS="img-thumbnail mx-auto d-block" SRC="/images/autorpt_incdr_promo_discount.png">
            <P>
          </DIV>
          <DIV CLASS="card-footer border-white bg-white">
            <DIV CLASS="form-check">
              <INPUT CLASS="form-check-input" NAME="incdr_promo_discount" ID="incdr_promo_discount" TYPE="checkbox" $chkBoxStatus>
              <LABEL CLASS="form-check-label" FOR="incdr_promo_discount">$chkBoxText</LABEL>
              <A HREF="#" data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                 data-bs-content="$popOverContent">
                 <I CLASS="$popOverIcon"></I>
              </A>
            </DIV>
          </DIV>
        </DIV>
      </DIV>
END_HTML

    $prereqHTML = autorpt_prereqs_incdr_share_promos($db, $dsID);
    if (length($prereqHTML) > 0)
    {
      $popOverContent = "This report requires:<BR><UL>$prereqHTML</UL>";
      $chkBoxStatus = "DISABLED";
      $chkBoxText = "Unavailable";
      $popOverIcon = "bi bi-info-circle";
    }
    else
    {
      $popOverContent = " ";
      $chkBoxStatus = "CHECKED";
      $chkBoxText = "Include";
      $popOverIcon = "";
    }

    print <<END_HTML;
      <DIV CLASS="col-3">
        <DIV CLASS="card border-primary h-100">
          <DIV CLASS="card-header bg-primary text-white">Share of Promotion Incrementals</DIV>
          <DIV CLASS="card-body">
            <IMG CLASS="img-thumbnail mx-auto d-block" SRC="/images/autorpt_incdr_share_promos.png">
            <P>
          </DIV>
          <DIV CLASS="card-footer border-white bg-white">
            <DIV CLASS="form-check">
              <INPUT CLASS="form-check-input" NAME="incdr_share_promos" ID="incdr_share_promos" TYPE="checkbox" $chkBoxStatus>
              <LABEL CLASS="form-check-label" FOR="incdr_share_promos">$chkBoxText</LABEL>
              <A HREF="#" data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                 data-bs-content="$popOverContent">
                 <I CLASS="$popOverIcon"></I>
              </A>
            </DIV>
          </DIV>
        </DIV>
      </DIV>
END_HTML

    $prereqHTML = autorpt_prereqs_incdr_promo_pricing($db, $dsID);
    if (length($prereqHTML) > 0)
    {
      $popOverContent = "This report requires:<BR><UL>$prereqHTML</UL>";
      $chkBoxStatus = "DISABLED";
      $chkBoxText = "Unavailable";
      $popOverIcon = "bi bi-info-circle";
    }
    else
    {
      $popOverContent = " ";
      $chkBoxStatus = "CHECKED";
      $chkBoxText = "Include";
      $popOverIcon = "";
    }

    print <<END_HTML;
      <DIV CLASS="col-3">
        <DIV CLASS="card border-primary h-100">
          <DIV CLASS="card-header bg-primary text-white">Promotional Pricing</DIV>
          <DIV CLASS="card-body">
            <IMG CLASS="img-thumbnail mx-auto d-block" SRC="/images/autorpt_incdr_promo_pricing.png">
            <P>
          </DIV>
          <DIV CLASS="card-footer border-white bg-white">
            <DIV CLASS="form-check">
              <INPUT CLASS="form-check-input" NAME="incdr_promo_pricing" ID="incdr_promo_pricing" TYPE="checkbox" $chkBoxStatus>
              <LABEL CLASS="form-check-label" FOR="incdr_promo_pricing">$chkBoxText</LABEL>
              <A HREF="#" data-bs-container="body" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true"
                 data-bs-content="$popOverContent">
                 <I CLASS="$popOverIcon"></I>
              </A>
            </DIV>
          </DIV>
        </DIV>
      </DIV>

    </DIV>  <!-- card row -->
END_HTML
  }

  print <<END_HTML;
    </DIV>  <!-- col -->

  </DIV>  <!-- row -->

  <DIV CLASS="row">
    <DIV CLASS="col">
      <P>&nbsp;</P>
      <DIV CLASS="text-center">
        <BUTTON CLASS="btn btn-primary" TYPE="submit" ID="btn-submit">Create Reports <I CLASS="bi bi-arrow-right"></I></BUTTON>
      </DIV>
    </DIV>
  </DIV>

</DIV> <!-- container -->
</FORM>
<P>
END_HTML

  print_html_footer();

#EOF
