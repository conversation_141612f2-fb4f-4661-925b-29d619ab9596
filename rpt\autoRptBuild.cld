#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::AutoReports;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<DOCCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName Automated Report Creation</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
let statusTimer = setInterval(function(){displayStatus()}, 5000);

function displayStatus()
{
  const url = "/app/rpt/xhrAutoRptStatus.cld?ds=$dsID";

  \$.get(url, function(data, status)
  {
    let statusText = data;

    if (statusText.length < 2)
    {
      clearInterval(statusTimer);
      location.href='/app/rpt/main?ds=$dsID';
    }

    document.getElementById('progressDiv').innerHTML = statusText;
  });
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/rpt/main">Reports</A></LI>
    <LI CLASS="breadcrumb-item">$dsName</LI>
    <LI CLASS="breadcrumb-item active">Automated Reporting</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------
#
# Output the HTML for the web page that displays the status dialog
#

sub print_status_html
{
  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-11 col-md-8 col-lg-6 col-xl-5"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Building Reports</DIV>
        <DIV CLASS="card-body">
          <P>
          <DIV CLASS="progress">
            <DIV ID="progress-bar" CLASS="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" STYLE="width:100%;">
              Working...
            </DIV>
          </DIV>

          <P>
          <DIV CLASS="text-center">
            <DIV ID="progressDiv">Building reports</DIV>
          </DIV>

          <P>&nbsp;</P>
          Koala can finish building your reports in the background, and notify you when it's done. While it's working, you can work on other things.

          <P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" ID="btnAsync" onClick="location.href='/app/rpt/main'"><I CLASS="bi bi-speedometer"></I> Be More Productive</BUTTON>
          </DIV>
        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get the CGI input variables
  $dsID = $q->param('ds');
  $story = $q->param('s');

  #get the tags for the reports we're building
  $rptTags = "";
  $tagPresent = $q->param('catov_topline_brands');
  if ($tagPresent eq "on")
  {
    $rptTags .= "catov_topline_brands,";
  }
  $tagPresent = $q->param('catov_topline_retailers');
  if ($tagPresent eq "on")
  {
    $rptTags .= "catov_topline_retailers,";
  }
  $tagPresent = $q->param('catov_brand_rank');
  if ($tagPresent eq "on")
  {
    $rptTags .= "catov_brand_rank,";
  }
  $tagPresent = $q->param('catov_item_rank');
  if ($tagPresent eq "on")
  {
    $rptTags .= "catov_item_rank,";
  }
  $tagPresent = $q->param('catov_rtlr_distro_trends');
  if ($tagPresent eq "on")
  {
    $rptTags .= "catov_rtlr_distro_trends,";
  }
  $tagPresent = $q->param('catov_item_distro_trends');
  if ($tagPresent eq "on")
  {
    $rptTags .= "catov_item_distro_trends,";
  }
  $tagPresent = $q->param('catov_retailer_acv_comp');
  if ($tagPresent eq "on")
  {
    $rptTags .= "catov_retailer_acv_comp,";
  }
  $tagPresent = $q->param('catov_change_distro');
  if ($tagPresent eq "on")
  {
    $rptTags .= "catov_change_distro,";
  }
  $tagPresent = $q->param('catov_price_tracker');
  if ($tagPresent eq "on")
  {
    $rptTags .= "catov_price_tracker,";
  }
  $tagPresent = $q->param('catov_units_sold_price');
  if ($tagPresent eq "on")
  {
    $rptTags .= "catov_units_sold_price,";
  }
  $tagPresent = $q->param('catov_promo_tracker_dollars');
  if ($tagPresent eq "on")
  {
    $rptTags .= "catov_promo_tracker_dollars,";
  }
  $tagPresent = $q->param('catov_promo_tracker_units');
  if ($tagPresent eq "on")
  {
    $rptTags .= "catov_promo_tracker_units,";
  }
  $tagPresent = $q->param('catov_lift_vs_discount');
  if ($tagPresent eq "on")
  {
    $rptTags .= "catov_lift_vs_discount,";
  }

  $tagPresent = $q->param('tlbr_ov');
  if ($tagPresent eq "on")
  {
    $rptTags .= "tlbr_ov,";
  }
  $tagPresent = $q->param('tlbr_totbustopline');
  if ($tagPresent eq "on")
  {
    $rptTags .= "tlbr_totbustopline,";
  }
  $tagPresent = $q->param('tlbr_cat_snap');
  if ($tagPresent eq "on")
  {
    $rptTags .= "tlbr_cat_snap,";
  }
  $tagPresent = $q->param('tlbr_rtl_item_trends');
  if ($tagPresent eq "on")
  {
    $rptTags .= "tlbr_rtl_item_trends,";
  }
  $tagPresent = $q->param('tlbr_rtl_brand_trends');
  if ($tagPresent eq "on")
  {
    $rptTags .= "tlbr_rtl_brand_trends,";
  }
  $tagPresent = $q->param('tlbr_prod_trends');
  if ($tagPresent eq "on")
  {
    $rptTags .= "tlbr_prod_trends,";
  }

  $tagPresent = $q->param('bldr_base_vs_incr');
  if ($tagPresent eq "on")
  {
    $rptTags .= "bldr_base_vs_incr,";
  }
  $tagPresent = $q->param('bldr_cause_base_change');
  if ($tagPresent eq "on")
  {
    $rptTags .= "bldr_cause_base_change,";
  }
  $tagPresent = $q->param('bldr_distribution');
  if ($tagPresent eq "on")
  {
    $rptTags .= "bldr_distribution,";
  }
  $tagPresent = $q->param('bldr_everyday_price');
  if ($tagPresent eq "on")
  {
    $rptTags .= "bldr_everyday_price,";
  }
  $tagPresent = $q->param('bldr_everyday_price_vs_comp');
  if ($tagPresent eq "on")
  {
    $rptTags .= "bldr_everyday_price_vs_comp,";
  }
  $tagPresent = $q->param('bldr_base_price_retailers');
  if ($tagPresent eq "on")
  {
    $rptTags .= "bldr_base_price_retailers,";
  }

  $tagPresent = $q->param('incdr_incr_vs_base');
  if ($tagPresent eq "on")
  {
    $rptTags .= "incdr_incr_vs_base,";
  }
  $tagPresent = $q->param('incdr_cause_incr_change');
  if ($tagPresent eq "on")
  {
    $rptTags .= "incdr_cause_incr_change,";
  }
  $tagPresent = $q->param('incdr_incr_change_details');
  if ($tagPresent eq "on")
  {
    $rptTags .= "incdr_incr_change_details,";
  }
  $tagPresent = $q->param('incdr_promo_freq');
  if ($tagPresent eq "on")
  {
    $rptTags .= "incdr_promo_freq,";
  }
  $tagPresent = $q->param('incdr_promo_eff');
  if ($tagPresent eq "on")
  {
    $rptTags .= "incdr_promo_eff,";
  }
  $tagPresent = $q->param('incdr_promo_discount');
  if ($tagPresent eq "on")
  {
    $rptTags .= "incdr_promo_discount,";
  }
  $tagPresent = $q->param('incdr_share_promos');
  if ($tagPresent eq "on")
  {
    $rptTags .= "incdr_share_promos,";
  }
  $tagPresent = $q->param('incdr_promo_pricing');
  if ($tagPresent eq "on")
  {
    $rptTags .= "incdr_promo_pricing,";
  }

  chop($rptTags);

  $db = KAPutil_connect_to_database();

  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  #make sure we have write privs for this data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to create reports in this data source.");
  }

  #if there's already an auto report process running in this DS, join the status
  #stream
  $query = "SELECT operation FROM app.jobs \
      WHERE dsID=$dsID AND operation='AUTO-RPTS'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($operation) = $dbOutput->fetchrow_array;

  if ($operation eq "AUTO-RPTS")
  {
    print_status_html();
    exit;
  }

  #if the user is already using more than their fair share of a production cloud
  $loadAvg = KAPutil_get_load_avg($db);
  if (($loadAvg >= 0.75) && ($acctType < 10))
  {
    $jobCount = KAPutil_get_user_jobs($db, $userID);

    #if the user already has more than a couple jobs running
    if ($jobCount > 1)
    {
      exit_error("Your analytics cloud is heavily loaded, and you already have at least one large job running. Wait a bit for that job to finish, and then try again.");
    }
  }

  #fork a new process to do the actual import in the background
  $SIG{CHLD} = "IGNORE";
  if ($pid = fork())
  {
    #parent process

    print_status_html();

    $activity = "$first $last building auto reports for the $story story in $dsName";
    utils_slack($activity);
  }
  else
  {
    #child process

    #let Apache know not to wait on the child process
    close(STDIN);
    close(STDOUT);

    #redirect STDERR to the Koala error log
    close(STDERR);
    open(STDERR, ">>/opt/apache/htdocs/tmp/koala_error.log");
    select(STDERR);
    $| = 1;

    #reconnect to the database
    $db = KAPutil_connect_to_database();

    #set our initial state in the jobs table
    KAPutil_job_store_status($db, $userID, $dsID, 0, "AUTO-RPTS", "Creating reports for story");
    $q_name = $db->quote($dsName);
    $query = "UPDATE app.jobs SET dsName=$q_name WHERE PID=$$ AND dsID=$dsID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    #build the report storyboard
    autorpt_create_story($db, $userID, $orgID, $dsID, $story, $rptTags);

    DSRutil_clear_status($db);
  }

#EOF
