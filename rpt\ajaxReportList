#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::Reports;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  $dsID = $q->param('dsID');

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #save the current data source the user is looking at reports in
  $session->param(rpt_DS, $dsID);

  print("Expires: -1\n");
  print("Cache-Control: private, max-age=0");
  print("Content-type: application/json\n\n");

  #open up our JSON structure
  print("[\n");

  $db = KAPutil_connect_to_database();

  %userCubes = cube_list($db, $userID, $acctType, $dsID);
  foreach $cubeID (keys %userCubes)
  {
    $cubes .= $cubeID . ",";
  }
  chop($cubes);

  #if there aren't any reports in this data source, we're done
  if (length($cubes) < 1)
  {
    print("]\n");
    exit;
  }

  #build a hash of any cube jobs running in this data source
  $query = "SELECT cubeID, opInfo FROM app.jobs \
      WHERE dsID=$dsID AND operation IN ('CUBE-UPDATE', 'AUTO-RPTS')";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  while (($cubeID, $opInfo) = $dbOutput->fetchrow_array)
  {
    $jobsHash{$cubeID} = $opInfo;
  }

  #get the last data source update and modification times
  $query = "SELECT UNIX_TIMESTAMP(lastUpdate), UNIX_TIMESTAMP(lastModified) \
      FROM dataSources WHERE ID=$dsID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($dsLastUpdate, $dsLastModified) = $dbOutput->fetchrow_array;

  $query = "SELECT ID, name, userID, status, UNIX_TIMESTAMP(lastUpdate), lastUpdate \
      FROM cubes WHERE ID IN ($cubes) AND dsID = $dsID ORDER BY name";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  $count = 1;
  while (($rptID, $name, $rptUserID, $dsStatus, $lastUpdate, $updated) = $dbOutput->fetchrow_array)
  {
    $rptUser = utils_userID_to_name($db, $rptUserID);

    if ($jobsHash{$rptID} =~ m/^Update/)
    {
      $name = "$name (Updating)";
    }
    if ($jobsHash{$rptID} =~ m/^Wait/)
    {
      $name = "$name (Waiting)";
    }

    #if appropriate, add a "refresh needed" status icon
    if ($dsStatus =~ m/^ERROR:(.*)/)
    {
      $name = "<IMG SRC='/icons/warn_red16.png' HEIGHT='12px' TITLE='$1'> $name";
    }
    elsif ($lastUpdate < $dsLastModified)
    {
      $name = "<IMG SRC='/icons/warn_yellow16.png' HEIGHT='12px' TITLE='The data source has been modified since the last report refresh'> $name";
    }

    print <<JSON_LABEL;
    {
      "ID": $rptID,
      "Report": "$name",
      "Last Updated": "$updated",
      "Analyst": "$rptUser"
    }
JSON_LABEL

    if ($count < $status)
    {
      print(",");
    }

    $count++;
  }

  #close JSON structure
  print("]\n");

  #flush the CGI session info out to storage
  $session->flush();


#EOF
