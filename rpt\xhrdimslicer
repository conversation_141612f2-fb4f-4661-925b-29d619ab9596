#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::Reports;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $rptID = $q->param('rptID');
  $dim = $q->param('d');
  $segID = $q->param('seg');
  $segmentID = $q->param('segment');

  $db = KAPutil_connect_to_database();

  #grab the existing slicer configuration string
  $query = "SELECT slicers FROM app.cubes WHERE ID=$rptID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($slicersStr) = $dbOutput->fetchrow_array;

  #we've deliberately formatted the slicer string so the same functions we use
  #for visuals will also work here
  $segID = "PSEG_" . $segID;
  $slicersStr = reports_set_style($slicersStr, $segID, $segmentID);

  #update the slicers configuration string
  $q_slicersStr = $db->quote($slicersStr);
  $query = "UPDATE app.cubes SET slicers=$q_slicersStr WHERE ID=$rptID";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);

#EOF
