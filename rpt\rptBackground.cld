#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Report Background</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/fontawesome-5.10.2/css/all.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$("#btn-submit").prop('disabled', true);
  \$("#btn-submit").text("Please Wait...");
  return(true);
}
</SCRIPT>

<SCRIPT>
function submitForm()
{
  let chosen = 0;

  for (i = 0; i < document.options.rptBackground.length; i++)
  {
    if (document.options.rptBackground[i].checked)
    {
      chosen = document.options.rptBackground[i].value;
    }
  }

  let url = "rptBackground.cld?ds=$dsID&rpt=$rptID&bgImageID=" + chosen;

  \$.get(url, function(data, status)
  {
    location.href = "display.cld?rpt=$rptID&v=$visID";
  });
}
</SCRIPT>

<STYLE>
.custom-file-button input[type=file]
{
  margin-left: -2px !important;
}

.custom-file-button input[type=file]::-webkit-file-upload-button
{
  display: none;
}

.custom-file-button input[type=file]::file-selector-button
{
  display: none;
}

.custom-file-button:hover label
{
  background-color: #dde0e3;
  cursor: pointer;
}

.input-hidden
{
  position: absolute;
  left: -9999px;
}

input[type=radio]:checked + label
{
  border: 3px solid #337ab7;
}
</STYLE>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/rpt/main">Reports</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?rpt=$rptID">$rptName</A></LI>
   <LI CLASS="breadcrumb-item active">Report Background</LI>
   </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser and get the report to be expanded
  $q = new CGI;
  $rptID = $q->param('rpt');
  $dsID = $q->param('ds');
  $bgImageID = $q->param('bgImageID');

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  $db = KAPutil_connect_to_database();

  $rptName = cube_id_to_name($db, $rptID);

  print_html_header();

  #make sure we have write privs for this data cube
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to edit this report.");
  }


  ########################################################################
  #
  # This code block is called on submit to save the changes
  #

  if (defined($bgImageID))
  {
    $query = "UPDATE app.cubes SET background = $bgImageID WHERE ID=$rptID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    $cubeName = cube_id_to_name($db, $rptID);
    $dsID = cube_get_ds_id($db, $rptID);
    $dsName = ds_id_to_name($db, $dsID);

    utils_audit($db, $userID, "Changed report background", $dsID, $rptID, 0);
    $activity = "$first $last changed report background for $cubeName in $dsName";
    utils_slack($activity);

    exit;
  }


  #########################################################################
  #
  # Everything after this point is called to display the chart layout dialog
  #


  $query = "SELECT background FROM app.cubes WHERE ID=$rptID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($rptBackground) = $dbOutput->fetchrow_array;

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-11 col-lg-8 col-xl-7"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Report Background</DIV>
        <DIV CLASS="card-body">

          <P>
          <DIV CLASS="card bg-light">
            <DIV CLASS="card-body">
              <H6 CLASS="card-subtitle mb-2 text-muted">Upload a new background image:</H6>
              <FORM ACTION="/app/rpt/xhrHandleUpload.cld?ds=$dsID&rpt=$rptID" METHOD="post" ENCTYPE="multipart/form-data">
              <DIV CLASS="row">
                <DIV CLASS="col-auto">
                  <DIV CLASS="input-group custom-file-button">
                    <LABEL CLASS="input-group-text" FOR="inputGroupFile">Browse</LABEL>
                    <INPUT TYPE="file" CLASS="form-control" ID="inputGroupFile" NAME="bgFile" onchange="showUploadButton();">
                  </DIV>
                </DIV>
                <DIV CLASS="col-auto">
                  <INPUT ID="btn-upload-file" CLASS="btn btn-primary mx-auto" STYLE="visibility:hidden;" TYPE="submit" VALUE="Upload Image" NAME="submit">
                  <SCRIPT>
                  function showUploadButton()
                  {
                    document.getElementById('btn-upload-file').style.visibility = 'visible';
                  }
                  </SCRIPT>
                </DIV>
              </FORM>
              </DIV>
            </DIV>
          </DIV>

          <P>&nbsp;</P>
          <FORM NAME="options">
            <TABLE CLASS="mx-auto w-75">
            <TR>
              <TD ALIGN="center" VALIGN="top" WIDTH="50%">
                <INPUT TYPE="radio" NAME="rptBackground" ID="bg_0" VALUE="0" CLASS="input-hidden" $checked></INPUT>
                <LABEL FOR="bg_0" STYLE="width:150px;">
                  <I CLASS="fas fa-ban" STYLE="font-size:75px; color:lightblue;"></I><BR>
                  (No Background)
                </LABEL>
              </TD>
END_HTML

      #grab all available backgrounds for this user and display as options
      $query = "SELECT ID, name FROM app.reports_backgrounds WHERE userID=$userID";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      KAPutil_handle_db_err($db, $status, $query);
      $leftCol = 0;
      while (($ID, $name) = $dbOutput->fetchrow_array)
      {
        if ($ID == $rptBackground)
        {
          $checked = "CHECKED";
        }
        else
        {
          $checked = "";
        }

        #if we're outputting an image in the left column
        if ($leftCol == 1)
        {
          print("<TR>\n");
        }

        print <<END_HTML;
              <TD NOWRAP ALIGN="center" VALIGN="top" WIDTH="50%">
                <INPUT TYPE="radio" NAME="rptBackground" ID="bg_$ID" VALUE="$ID" CLASS="input-hidden" $checked></INPUT>
                <LABEL FOR="bg_$ID">
                  <IMG SRC="imageRetrieve.cld?t=b&id=$ID&w=150" WIDTH="150px;" CLASS="border" TITLE="">
                </LABEL>
                <SPAN STYLE="vertical-align:top;font-size:32px;position:relative;left:-26px;top:-10px;">
                  <A CLASS="text-decoration-none text-dark" HREF="imageDelete.cld?ds=$dsID&rpt=$rptID&t=b&id=$ID">&times;</A>
                </SPAN>
              </TD>
END_HTML

        #if we're outputting an image in the right column, close the row
        if ($leftCol == 0)
        {
          print("</TR>\n");
        }

        #flip the left/right column indicator
        $leftCol = $leftCol == 1 ? 0 : 1;
      }

      print <<END_HTML;
          </TABLE>

        <P>&nbsp;</P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="location.href='/app/rpt/display.cld?rpt=$rptID'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="submitForm()"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
          </DIV>
          </FORM>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
