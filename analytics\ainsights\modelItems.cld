#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::AutoReports;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::DataSources;
use Lib::AInsights::AInsights;
use Lib::AInsights::Utils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: AInsights Model Items</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-select-1.14.0-b2/css/bootstrap-select.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-select-1.14.0-b2/js/bootstrap-select.min.js"></SCRIPT>


<SCRIPT>
function checkForm(form)
{
  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Please Wait...');
  return(true);
}


function brandSegUpdate(segSel, segmentSel, firstRun, matchSegmentVal)
{
  let segID = document.getElementById('bseg').value;
  let urlStr = '/app/dsr/ajaxAPI.cld?ds=$dsID&dim=p&svc=segments&seg=' + segID;
  console.log(urlStr);

  \$(segmentSel).empty();
  \$.ajax(
  {
    url: urlStr,
    dataType: 'json',
    type: 'GET',
    success: function(response)
    {
      if (response != '')
      {
        for (i in response)
        {
          \$(segmentSel).append('<OPTION VALUE=' + response[i].id + '>'+response[i].name+'</OPTION>');
        }
        if ((firstRun == 1) && (matchSegmentVal.length > 0))
        {
          let valStr = matchSegmentVal;
          let vals = valStr.split(',');
          \$(segmentSel).val(vals);
        }
      }

      \$(segmentSel).selectpicker('refresh');
    },
    error: function(x, e) {console.log(e)}
  });
}

</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">AInsights</A></LI>
    <LI CLASS="breadcrumb-item">$dsName</LI>
    <LI CLASS="breadcrumb-item active">$actionHR</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $priceModelID = $q->param('pm');
  $action = $q->param('a');
  $dsID = $q->param('ds');
  $pricingName = $q->param('name');
  $pricingDesc = $q->param('desc');

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsSchema = "datasource_" . $dsID;
  $dsName = ds_id_to_name($db, $dsID);

  #if we're editing an existing model
  if ($priceModelID > 0)
  {
    $query = "SELECT ppgHierID FROM analytics.pricing \
        WHERE ID=$priceModelID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($ppgHierID) = $dbOutput->fetchrow_array;

    $brandSegID = AInsights_Utils_get_brand_seg_id($db, $priceModelID);
    $ownBrandID = AInsights_Utils_get_own_brand_id($db, $priceModelID);
    @geoIDs = AInsights_Utils_get_model_geo_ids($db, $priceModelID);

    foreach $geoID (@geoIDs)
    {
      $geoSelectedHash{$geoID} = 1;
    }

    $actionHR = "Editing Model $pricingName";
  }
  else
  {
    $actionHR = "New Model";
  }

  print_html_header();

  #make sure we have write privs for this pricing model
  $privs = AInsights_rights($db, $userID, $priceModelID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this model.");
  }

  %geoNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "g");

  %segNameHash = DSRsegmentation_get_segmentations_hash($db, $dsSchema, 'p');

  #if we don't have an already defined brand segmentation, try to guess a
  #good default
  if ($brandSegID < 1)
  {
    %segNameIDHash = reverse(%segNameHash);
    $brandSegID = $segNameIDHash{'BRAND HIGH'};
  }

  #pick the brand with the most items as our default "own brand" if we don't
  #already have one
  if ($ownBrandID < 1)
  {
    $query = "SELECT segmentID, COUNT(*) AS count \
        FROM $dsSchema.product_segment_item \
        WHERE segmentationID=$brandSegID \
        GROUP BY segmentID ORDER BY count DESC LIMIT 1";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($ownBrandID) = $dbOutput->fetchrow_array;
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-11 col-lg-8 col-xl-7"> <!-- content -->

      <FORM METHOD="post" ACTION="modelDetails.cld" onsubmit="return checkForm(this);">
      <INPUT TYPE="hidden" NAME="pm" VALUE="$priceModelID">
      <INPUT TYPE="hidden" NAME="a" VALUE="$action">
      <INPUT TYPE="hidden" NAME="ds" VALUE="$dsID">
      <INPUT TYPE="hidden" NAME="name" VALUE="$pricingName">
      <INPUT TYPE="hidden" NAME="desc" VALUE="$pricingDesc">

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Product & Geography</DIV>
        <DIV CLASS="card-body">

          Segmentation containing brand information:<BR>
          <SELECT required CLASS="form-select mx-1" NAME="bseg" ID="bseg" STYLE="width:auto;" required onChange="brandSegUpdate('seg-brandgMatch', '#seg-brand-segment', 0);">
END_HTML

  foreach $segID (sort {$segNameHash{$a} cmp $segNameHash{$b}} keys %segNameHash)
  {
    print("<OPTION VALUE=\"$segID\">$segNameHash{$segID}</OPTION>\n");
  }

  print <<END_HTML;
          </SELECT>
          <SCRIPT>
            \$('select#bseg').val('$brandSegID');
          </SCRIPT>

          <P></P>
          My brand of interest:
          <SELECT CLASS="selectpicker my-1" ID="seg-brand-segment" NAME="ob" required>
          </SELECT>
          <SCRIPT>
          \$(document).ready(function()
          {
            \$('#seg-brandgMatch').val('$ownBrandID');
            brandSegUpdate('seg-brandgMatch', '#seg-brand-segment', 1, '$ownBrandID');
          });
          </SCRIPT>

          <P></P>
          Hierarchy to use as a product promotion group:<BR>

          <SELECT required CLASS="form-select mx-1" NAME="p" ID="p" STYLE="width:auto;" required>
END_HTML

  #create/find a BRAND-SIZE hierarchy and use as default selection
  $brandSizeSegHierID = autorpt_create_seghier_brand_size($db, $dsID, 1);
  print("<OPTION VALUE=\"$brandSizeSegHierID\">Brand-Size</OPTION>\n");

  #set BRAND-SIZE to be our default
  if ($$ppgHierID < 1)
  {
    $ppgHierID = $brandSizeSegHierID;
  }

  $query = "SELECT ID, name FROM $dsSchema.product_seghierarchy ORDER BY name";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  while (($segHierID, $segHierName) = $dbOutput->fetchrow_array)
  {

    #skip the brand-size hierarchy we've already output
    if ($segHierID == $brandSizeSegHierID)
    {
      next;
    }

    print("<OPTION VALUE=\"$segHierID\">$segHierName</OPTION>\n");
  }

 print <<END_HTML;
          </SELECT>
          <SCRIPT>
            \$('select#p').val('$ppgHierID');
          </SCRIPT>

          <P>&nbsp;</P>

          Geographies to include:

          <P>
          <SELECT required CLASS="form-select mx-1" NAME="g" ID="g" STYLE="width:auto;" SIZE=10 MULTIPLE required>
END_HTML

  foreach $geoID (sort {$geoNameHash{$a} cmp $geoNameHash{$b}} keys %geoNameHash)
  {

    #if we're editing a model, keep the user's original selections - default is
    #we select all geographies that end in FOOD
    $selected = "";
    if (($action eq "e") && ($geoSelectedHash{$geoID} == 1))
    {
      $selected = "SELECTED";
    }
    elsif (($action ne "e") && ($geoNameHash{$geoID} =~ m/ TA$/i))
    {
      $selected = "SELECTED";
    }

    print("<OPTION $selected VALUE=\"$geoID\">$geoNameHash{$geoID}</OPTION>\n");
  }

 print <<END_HTML;
          </SELECT>

          <P>&nbsp;</P>

          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onclick="location.href='main.cld'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="submit" ID="btn-submit">Next <I CLASS="bi bi-arrow-right"></I></BUTTON>
          </DIV>

        </DIV>
      </DIV>

      </FORM>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
