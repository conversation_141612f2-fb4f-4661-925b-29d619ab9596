#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: $readableAction Segmentation Hierarchy Created</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">$readableAction Segmentation Hierarchy</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('dsID');
  $dim = $q->param('dim');
  $segHierName = $q->param('segHierName');
  $segHierID = $q->param('segHier');
  $hierarchy = $q->param('hierarchy');
  chop($hierarchy);

  if ($segHierID > 0)
  {
    $readableAction = "Edit";
    $textAction = "edited";
  }
  else
  {
    $readableAction = "New";
    $textAction = "created";
  }

  #run through the list of CGI parameters, extracting naming patterns and
  #building up our pattern string
  undef(%patternHash);
  $namePattern = "";
  @names = $q->param;
  foreach $name (@names)
  {
    if ($name =~ m/^pattern_(\d+)$/)
    {
      $level = $1;
      $pattern = $q->param($name);
      $patternHash{$level} = $pattern;
    }
  }
  foreach $level (sort {$patternHash{$a} cmp $patternHash{$b}} keys %patternHash)
  {
    $namePattern .= $level . ":" . $patternHash{$level} . ",";
  }

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsSchema = "datasource_" . $dsID;
  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  #make sure we have write privs for the data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to modify this data source.");
  }

  #get our dimension's table name
  $dimDB = KAPutil_get_dim_stub_name($dim);
  $segHierTable = $dimDB . "seghierarchy";

  #insert entry into db for new segmentation hierarchy
  $q_segHierName = $db->quote($segHierName);
  $q_hierarchy = $db->quote($hierarchy);
  $q_namePattern = $db->quote($namePattern);

  #if we're editing an existing segmentation hierarchy
  if ($segHierID > 0)
  {
    $query = "UPDATE $dsSchema.$segHierTable \
        SET name=$q_segHierName, segmentations=$q_hierarchy, namePattern=$q_namePattern \
        WHERE ID=$segHierID";
    $db->do($query);

    $activity = "$first $last edited segmentation hierarchy $name in $dsName";
    utils_audit($db, $userID, "Edited segmentation hierarchy $name", $dsID, 0, 0);
  }

  #else we're creating a new segmentation hierarchy
  else
  {
    $query = "INSERT INTO $dsSchema.$segHierTable \
        (name, segmentations, namePattern) \
        VALUES ($q_segHierName, $q_hierarchy, $q_namePattern)";
    $db->do($query);
    $segID = $db->{q{mysql_insertid}};

    utils_audit($db, $userID, "Created new segmentation hierarchy $name", $dsID, 0, 0);
    $activity = "$first $last created new segmentation hierarchy $name in $dsName";
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-11 col-md-8 col-lg-6 col-xl-5"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">$readableAction Segmentation Hierarchy</DIV>
        <DIV CLASS="card-body">

          The segmentation hierarchy $segHierName was $textAction successfully.

          <P>&nbsp;<P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='display.cld?ds=$dsID&dim=$dim'"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();
  utils_slack($activity);

#EOF
