#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSel;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName Data Selection</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<SCRIPT SRC="/jsgrid/jsgrid.min.js"></SCRIPT>
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/jsgrid/jsgrid.min.css" REL="stylesheet">
<LINK HREF="/jsgrid/jsgrid-theme.min.css" REL="stylesheet">
<LINK HREF="/fontawesome-5.10.2/css/all.min.css" REL="stylesheet">

<STYLE>
div.grid th
{
  background: #e2e3e5 !important;
}

.selected-row > td
{
  background: #c4e2ff !important;
  border-color: #c4e2ff;
}
</STYLE>

<SCRIPT>
function getSelectionStr(dim)
{
  let grid = '';

  if (dim == 'p')
  {
    grid = \$('#prodGrid').jsGrid('option', 'data');
  }
  else if (dim == 'g')
  {
    grid = \$('#geoGrid').jsGrid('option', 'data');
  }
  else if (dim == 't')
  {
    grid = \$('#timeGrid').jsGrid('option', 'data');
  }
  else if (dim == 'm')
  {
    grid = \$('#measGrid').jsGrid('option', 'data');
  }

  let selStr = '';
  for (let i = 0; i < grid.length; i++)
  {
    if (grid[i].selected == 1)
    {
      selStr = selStr + grid[i].id + ',';
    }
  }

  return(selStr);
}



function getSelectionIdx(dim)
{
  let grid = '';

  if (dim == 'p')
  {
    grid = \$('#prodGrid').jsGrid('option', 'data');
  }
  else if (dim == 'g')
  {
    grid = \$('#geoGrid').jsGrid('option', 'data');
  }
  else if (dim == 't')
  {
    grid = \$('#timeGrid').jsGrid('option', 'data');
  }
  else if (dim == 'm')
  {
    grid = \$('#measGrid').jsGrid('option', 'data');
  }

  let selStr = '';
  for (let i = 0; i < grid.length; i++)
  {
    if (grid[i].selected == 1)
    {
      selStr = selStr + i + ',';
    }
  }

  return(selStr);
}



function modifySelection(dim)
{
  let item = null;
  let grid = '';

  if (dim == 'p')
  {
    grid = \$('#prodGrid').jsGrid('option', 'data');
  }
  else if (dim == 'g')
  {
    grid = \$('#geoGrid').jsGrid('option', 'data');
  }
  else if (dim == 't')
  {
    grid = \$('#timeGrid').jsGrid('option', 'data');
  }
  else if (dim == 'm')
  {
    grid = \$('#measGrid').jsGrid('option', 'data');
  }

  for (let i = 0; i < grid.length; i++)
  {
    if ((grid[i].selected == 1) && (item == null))
    {
      item = grid[i].id;
    }
  }

  if (item == null)
  {
    alert('Please select an item to modify');
    return;
  }

  location.href='selectionMethod.cld?ds=$dsID&dim=' + dim +
      '&rptID=$rptID&modItem=' + item;
}



function removeSelection(dim)
{
  let val = getSelectionStr(dim);

  if (val.length == 0)
  {
    return;
  }

  location.href='?ds=$dsID&dim=' + dim + '&rptID=$rptID&action=r&item=' + val;
}



function topItem(dim)
{
  let val = getSelectionStr(dim);

  if (val.length == 0)
  {
    return;
  }

  location.href='?ds=$dsID&dim=' + dim + '&rptID=$rptID&action=t&item=' + val;
}



function upItem(dim)
{
  let selIdx = '';
  let val = getSelectionStr(dim);

  if (val.length == 0)
  {
    return;
  }

  selIdx = getSelectionIdx(dim);

  location.href='?ds=$dsID&dim=' + dim + '&rptID=$rptID&action=u&item=' +
      val + '&i=' + selIdx;
}



function downItem(dim)
{
  let selIdx = '';
  let val = getSelectionStr(dim);

  if (val.length == 0)
  {
    return;
  }

  selIdx = getSelectionIdx(dim);

  location.href='?ds=$dsID&dim=' + dim + '&rptID=$rptID&action=d&item=' +
      val + '&i=' + selIdx;
}



function bottomItem(dim)
{
  let val = getSelectionStr(dim);

  if (val.length == 0)
  {
    return;
  }

  location.href='?ds=$dsID&dim=' + dim + '&rptID=$rptID&action=b&item=' + val;
}
</SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  $rptDisp = "";
  if (length($name) > 0)
  {
    $rptDisp = "$name";
  }
  else
  {
    $reportName = cube_id_to_name($db, $rptID);
    if (length($reportName) > 1)
    {
      $rptDisp = "$reportName";
    }
  }

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/rpt/main">Reports</A></LI>
    <LI CLASS="breadcrumb-item">$rptDisp</LI>
    <LI CLASS="breadcrumb-item active">Data Selection</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

sub print_grid_javascript
{
  $dataBlock = datasel_get_script_json($db, $rptID, "c", "p", $dsID);

  $dimItems = datasel_expand_script($db, $dsSchema, $rptID, "p", "c", 1);
  $dimItemsCount = $dimItems =~ tr/,//;
  $dimItemsCount++;

  print <<END_HTML;
<SCRIPT>
function clearAllSelections(dim)
{
  let grid = '';

  if (dim == 'p')
  {
    grid = \$('#prodGrid').jsGrid('option', 'data');
  }
  else if (dim == 'g')
  {
    grid = \$('#geoGrid').jsGrid('option', 'data');
  }
  else if (dim == 't')
  {
    grid = \$('#timeGrid').jsGrid('option', 'data');
  }
  else if (dim == 'm')
  {
    grid = \$('#measGrid').jsGrid('option', 'data');
  }

  for (let i = 0; i < grid.length; i++)
  {
    grid[i].selected = 0;
  }

  if (dim == 'p')
  {
    \$('#prodGrid tr').removeClass('selected-row');
  }
  else if (dim == 'g')
  {
    \$('#geoGrid tr').removeClass('selected-row');
  }
  else if (dim == 't')
  {
    \$('#timeGrid tr').removeClass('selected-row');
  }
  else if (dim == 'm')
  {
    \$('#measGrid tr').removeClass('selected-row');
  }
}



function initSelections()
{
  const dim = '$dim';
  const idx = $itemIndex;
  let gridName = '';

  if (dim == 'p')
  {
    gridName = '#prodGrid';
  }
  else if (dim == 'g')
  {
    gridName = '#geoGrid';
  }
  else if (dim == 't')
  {
    gridName = '#timeGrid';
  }
  else if (dim == 'm')
  {
    gridName = '#measGrid';
  }

  if (idx.length > 0)
  {
    let i = 0;
    let grid = \$(gridName).jsGrid('option', 'data');

    \$selectedRow = \$(gridName).jsGrid('rowByItem', grid[idx[0]]).closest('tr');
    \$selectedRow[0].scrollIntoView();

    while (i < idx.length)
    {
      grid[idx[i]].selected = 1;
      \$selectedRow = \$(gridName).jsGrid('rowByItem', grid[idx[i]]).closest('tr');
      \$selectedRow.addClass('selected-row');
      i++;
    }
  }
}



let prodGridData = [ $dataBlock ];

\$(document).ready(function()
{

  \$('#prodGrid').jsGrid(
  {
    width: '425px',
    height: '200px',
    sorting: true,
    autoload: true,
    loadIndication: true,
    multiselect: true,

    data: prodGridData,

    rowClick: function(args)
    {

      //Shift + selection
      if (args.event.shiftKey)
      {
        document.getSelection().removeAllRanges();

        let i = 0;
        let firstSelection = -1;

        while ((i < this.data.length) && (firstSelection < 0))
        {
          if (this.data[i].selected == 1)
          {
            firstSelection = i;
          }
          i++;
        }

        i = 0;
        let curSelection = -1;
        while ((i < this.data.length) && (curSelection < 0))
        {
          if (args.item.id == this.data[i].id)
          {
            curSelection = i;
          }
          i++;
        }

        clearAllSelections('p');

        let start = '', stop = '';
        if (curSelection > firstSelection)
        {
          start = firstSelection;
          end = curSelection;
        }
        else
        {
          end = firstSelection;
          start = curSelection;
        }

        for (i=start; i <= end; i++)
        {
          this.data[i].selected = 1;
          \$selectedRow = \$('#prodGrid').jsGrid('rowByItem', this.data[i]).closest('tr');
          \$selectedRow.addClass('selected-row');
        }
      }

      //Ctrl+selection
      else if (event.ctrlKey || event.altKey || event.metaKey)
      {
        args.item.selected = 1;
        \$selectedRow = \$(args.event.target).closest('tr');
        \$selectedRow.addClass('selected-row');
      }

      //single selection
      else
      {
        clearAllSelections('p');
        args.item.selected = 1;
        \$selectedRow = \$(args.event.target).closest('tr');
        \$selectedRow.addClass('selected-row');
      }
    },

    fields: [
      {name: 'id', type: 'number', visible: false},
      {name: 'product', title: 'Products ($dimItemsCount)', type: 'text', width: 300},
      {name: 'type', title: 'Type', type: 'text', width: 85}
    ]

  });
END_HTML

$dataBlock = datasel_get_script_json($db, $rptID, "c", "g", $dsID);

$dimItems = datasel_expand_script($db, $dsSchema, $rptID, "g", "c", 1);
$dimItemsCount = $dimItems =~ tr/,//;
$dimItemsCount++;

print <<END_HTML;

let geoGridData = [ $dataBlock ];

\$('#geoGrid').jsGrid(
{
  width: '425px',
  height: '200px',
  sorting: true,
  autoload: true,
  loadIndication: true,
  multiselect: true,

  data: geoGridData,

  rowClick: function(args)
  {

    //Shift + selection
    if (args.event.shiftKey)
    {
      document.getSelection().removeAllRanges();

      let i = 0;
      let firstSelection = -1;
      while ((i < this.data.length) && (firstSelection < 0))
      {
        if (this.data[i].selected == 1)
        {
          firstSelection = i;
        }
        i++;
      }

      i = 0;
      let curSelection = -1;
      while ((i < this.data.length) && (curSelection < 0))
      {
        if (args.item.id == this.data[i].id)
        {
          curSelection = i;
        }
        i++;
      }

      clearAllSelections('g');

      let start, stop;
      if (curSelection > firstSelection)
      {
        start = firstSelection;
        end = curSelection;
      }
      else
      {
        end = firstSelection;
        start = curSelection;
      }

      for (i = start; i <= end; i++)
      {
        this.data[i].selected = 1;
        \$selectedRow = \$('#geoGrid').jsGrid('rowByItem', this.data[i]).closest('tr');
        \$selectedRow.addClass('selected-row');
      }
    }

    //Ctrl+selection
    else if (event.ctrlKey || event.altKey || event.metaKey)
    {
      args.item.selected = 1;
      \$selectedRow = \$(args.event.target).closest('tr');
      \$selectedRow.addClass('selected-row');
    }

    //single selection
    else
    {
      clearAllSelections('g');
      args.item.selected = 1;
      \$selectedRow = \$(args.event.target).closest('tr');
      \$selectedRow.addClass('selected-row');
    }
  },

  fields: [
    {name: 'id', type: 'number', visible: false},
    {name: 'product', title: 'Geographies ($dimItemsCount)', type: 'text', width: 300},
    {name: 'type', title: 'Type', type: 'text', width: 85}
  ]

});
END_HTML

$dataBlock = datasel_get_script_json($db, $rptID, "c", "t", $dsID);

$dimItems = datasel_expand_script($db, $dsSchema, $rptID, "t", "c", 1);
$dimItemsCount = $dimItems =~ tr/,//;
$dimItemsCount++;

print <<END_HTML;

let timeGridData = [ $dataBlock ];

\$('#timeGrid').jsGrid(
{
  width: '425px',
  height: '200px',
  sorting: true,
  autoload: true,
  loadIndication: true,
  multiselect: true,

  data: timeGridData,

  rowClick: function(args)
  {

    //Shift + selection
    if (args.event.shiftKey)
    {
      document.getSelection().removeAllRanges();

      let i = 0;
      let firstSelection = -1;
      while ((i < this.data.length) && (firstSelection < 0))
      {
        if (this.data[i].selected == 1)
        {
          firstSelection = i;
        }
        i++;
      }

      i = 0;
      let curSelection = -1;
      while ((i < this.data.length) && (curSelection < 0))
      {
        if (args.item.id == this.data[i].id)
        {
          curSelection = i;
        }
        i++;
      }

      clearAllSelections('t');

      let start, stop;
      if (curSelection > firstSelection)
      {
        start = firstSelection;
        end = curSelection;
      }
      else
      {
        end = firstSelection;
        start = curSelection;
      }

      for (i = start; i <= end; i++)
      {
        this.data[i].selected = 1;
        \$selectedRow = \$('#timeGrid').jsGrid('rowByItem', this.data[i]).closest('tr');
        \$selectedRow.addClass('selected-row');
      }
    }

    //Ctrl+selection
    else if (event.ctrlKey || event.altKey || event.metaKey)
    {
      args.item.selected = 1;
      \$selectedRow = \$(args.event.target).closest('tr');
      \$selectedRow.addClass('selected-row');
    }

    //single selection
    else
    {
      clearAllSelections('t');
      args.item.selected = 1;
      \$selectedRow = \$(args.event.target).closest('tr');
      \$selectedRow.addClass('selected-row');
    }
  },

  fields: [
    {name: 'id', type: 'number', visible: false},
    {name: 'product', title: 'Time Periods ($dimItemsCount)', type: 'text', width: 300},
    {name: 'type', title: 'Type', type: 'text', width: 85}
  ]

});

END_HTML

  $dataBlock = datasel_get_script_json($db, $rptID, "c", "m", $dsID);

  $dimItems = datasel_expand_script($db, $dsSchema, $rptID, "m", "c", 1);
  $dimItemsCount = $dimItems =~ tr/,//;
  $dimItemsCount++;

  print <<END_HTML;

let measGridData = [ $dataBlock ];

\$('#measGrid').jsGrid(
{
  width: '425px',
  height: '200px',
  sorting: true,
  autoload: true,
  loadIndication: true,
  multiselect: true,

  data: measGridData,

  rowClick: function(args)
  {

    //Shift + selection
    if (args.event.shiftKey)
    {
      document.getSelection().removeAllRanges();

      let i = 0;
      let firstSelection = -1;
      while ((i < this.data.length) && (firstSelection < 0))
      {
        if (this.data[i].selected == 1)
        {
          firstSelection = i;
        }
        i++;
      }

      i = 0;
      let curSelection = -1;
      while ((i < this.data.length) && (curSelection < 0))
      {
        if (args.item.id == this.data[i].id)
        {
          curSelection = i;
        }
        i++;
      }

      clearAllSelections('m');

      let start, stop;
      if (curSelection > firstSelection)
      {
        start = firstSelection;
        end = curSelection;
      }
      else
      {
        end = firstSelection;
        start = curSelection;
      }

      for (i = start; i <= end; i++)
      {
        this.data[i].selected = 1;
        \$selectedRow = \$('#measGrid').jsGrid('rowByItem', this.data[i]).closest('tr');
        \$selectedRow.addClass('selected-row');
      }
    }

    //Ctrl+selection
    else if (event.ctrlKey || event.altKey || event.metaKey)
    {
      args.item.selected = 1;
      \$selectedRow = \$(args.event.target).closest('tr');
      \$selectedRow.addClass('selected-row');
    }

    //single selection
    else
    {
      clearAllSelections('m');
      args.item.selected = 1;
      \$selectedRow = \$(args.event.target).closest('tr');
      \$selectedRow.addClass('selected-row');
    }
  },

  fields: [
    {name: 'id', type: 'number', visible: false},
    {name: 'product', title: 'Measures ($dimItemsCount)', type: 'text', width: 300},
    {name: 'type', title: 'Type', type: 'text', width: 85}
  ]

});


  //init grid selections, if needed
  initSelections();

});
</SCRIPT>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  $db = KAPutil_connect_to_database();

  #get CGI parameters
  $dsID = $q->param('ds');
  $rptID = $q->param('rptID');
  $dim = $q->param('dim');
  $action = $q->param('action');
  $item = $q->param('item');
  $modifyItem = $q->param('modItem');
  $itemIndex = $q->param('i');
  $selection = $q->param('selection');
  $selMethod = $q->param('method');

  $selOp = $q->param('selOp');

  $segmentID = $q->param('segment');
  @segmentIDs = $q->param('segment');
  $segmentationID = $q->param('segmentation');
  $segHierLevel = $q->param('segHierLevel');

  $selType = $q->param('type');
  $selDuration = $q->param('duration');
  $selStarting = $q->param('starting');
  $selEnding = $q->param('ending');

  $matchType = $q->param('matchType');

  #these CGI parameters are used if we're selecting items for a list
  $name = $q->param('name');
  $structType = $q->param('st');
  $structID = $q->param('sid');

  if ($rptID =~ m/^(\d+)\,.*/)
  {
    $rptID = $1;
  }

  #if we got an item index (used to maintain grid selection state), convert it
  #into a JS array
  if (length($itemIndex) > 0)
  {
    @tmp = split(',', $itemIndex);
    $itemIndex = "[";
    foreach $idx (@tmp)
    {
      if (($idx > 0) && ($action eq "u"))
      {
        $idx--;
      }
      elsif ($action eq "d")
      {
        $idx++;
      }
      $itemIndex = $itemIndex . "$idx,";
    }
    chop($itemIndex);
    $itemIndex = $itemIndex . "];";
  }
  else
  {
    $itemIndex = "[];";
  }

  #if we got a rptID but no dsID, grab it from the database
  if ((!defined($dsID)) && (defined($rptID)))
  {
    $query = "SELECT dsID FROM cubes WHERE ID=$rptID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($dsID) = $dbOutput->fetchrow_array;
  }

  $dsSchema = "datasource_" . $dsID;

  print_html_header();

  #figure out what the Cancel button should do
  if (defined($rptID))
  {
    $cancelHref = "/app/rpt/display.cld?rpt=$rptID";
  }
  else
  {
    $cancelHref = "/app/rpt/main";
  }

  #make sure the cube isn't being updated by another user, throw an error if so
  if (defined($rptID))
  {
    $query = "SELECT opInfo FROM app.jobs \
        WHERE cubeID=$rptID AND operation='CUBE-UPDATE'";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($opInfo) = $dbOutput->fetchrow_array;

    if ($opInfo =~ m/^Update/)
    {
      exit_warning("This report is currently being refreshed and can't be modified. Please wait for the refresh to finish, then try again.");
    }
  }

  #if we're being called to create a new report, insert into cubes table
  if ((!(defined($rptID))) && (defined($name)))
  {

    $q_name = $db->quote($name);
    $query = "INSERT INTO cubes (dsID, userID, orgID, name) \
        VALUES ($dsID, $userID, $orgID, $q_name)";
    $db->do($query);

    #get the unique ID for this data cube
    $rptID = $db->{q{mysql_insertid}};
  }

  #if we're being asked to edit a cube the user doesn't have rights to, error
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this report.");
  }


  ####################

  #get our dimension's DB name and script column for all dimension-dependent
  #actions we may be getting called to perform
  if ($dim eq "p")
  {
    $colName = "products";
    $scriptColName = "scriptProducts";
  }
  elsif ($dim eq "g")
  {
    $colName = "geographies";
    $scriptColName = "scriptGeographies";
  }
  elsif ($dim eq "t")
  {
    $colName = "timeperiods";
    $scriptColName = "scriptTimeperiods";
  }
  elsif ($dim eq "m")
  {
    $colName = "measures";
    $scriptColName = "scriptMeasures";
  }

  #if we're being called to remove items from a dimension's selection
  if ($action eq "r")
  {

    #grab the script that generates the list
    $query = "SELECT $scriptColName FROM app.cubes WHERE ID=$rptID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($oldScript) = $dbOutput->fetchrow_array;

    $newLine = datasel_delete_items($oldScript, $item);

    $q_newLine = $db->quote($newLine);

    #store the new script
    $query = "UPDATE app.cubes SET $scriptColName=$q_newLine WHERE ID=$rptID";
    $db->do($query);

    #re-expand the script into the list members
    datasel_expand_script($db, $dsSchema, $rptID, $dim, "c");
  }


  ########################

  #if we're being called to move item(s) to the top of the list
  if ($action eq "t")
  {

    #grab the script that generates the list
    $query = "SELECT $scriptColName FROM app.cubes WHERE ID=$rptID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($oldScript) = $dbOutput->fetchrow_array;

    $newScript = datasel_move_items_top($oldScript, $item);
    $q_newScript = $db->quote($newScript);

    #store the new script
    $query = "UPDATE app.cubes SET $scriptColName=$q_newScript WHERE ID=$rptID";
    $db->do($query);
  }

  #if we're being called to move item(s) to the bottom of the list
  if ($action eq "b")
  {

    #grab the script that generates the list
    $query = "SELECT $scriptColName FROM app.cubes WHERE ID=$rptID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($oldScript) = $dbOutput->fetchrow_array;

    $newScript = datasel_move_items_bottom($oldScript, $item);

    $q_newScript = $db->quote($newScript);

    #store the new script
    $query = "UPDATE app.cubes SET $scriptColName=$q_newScript WHERE ID=$rptID";
    $db->do($query);
  }

  #if we're being called to move up an item's position in the list
  if ($action eq "u")
  {

    #grab the script that generates the list
    $query = "SELECT $scriptColName FROM app.cubes WHERE ID=$rptID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($oldScript) = $dbOutput->fetchrow_array;

    $newScript = datasel_move_items_up($oldScript, $item);

    $q_newScript = $db->quote($newScript);

    #store the new script
    $query = "UPDATE app.cubes SET $scriptColName=$q_newScript WHERE ID=$rptID";
    $db->do($query);
  }

  #if we're being called to move down an item's position in the list
  if ($action eq "d")
  {

    #grab the script that generates the list
    $query = "SELECT $scriptColName FROM app.cubes WHERE ID=$rptID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($oldScript) = $dbOutput->fetchrow_array;

    $newScript = datasel_move_items_down($oldScript, $item);

    $q_newScript = $db->quote($newScript);

    #store the new script
    $query = "UPDATE app.cubes SET $scriptColName=$q_newScript WHERE ID=$rptID";
    $db->do($query);
  }


  ########################

  #if we're being sent the results of a selection
  if (defined($selection))
  {

    #if there's an extra comma on the end of the selection string, chop it
    if ($selection =~ m/,$/)
    {
      chop($selection);
    }

    #build up the script column
    #NB: The script is processed by datasel_expand_script() as the first step
    #    of the cube build process

    $dataSelOpts{'db'} = $db;
    $dataSelOpts{'dim'} = $dim;
    $dataSelOpts{'dbName'} = "app.cubes";
    $dataSelOpts{'structType'} = "c";
    $dataSelOpts{'structID'} = $rptID;
    $dataSelOpts{'selMethod'} = $selMethod;
    $dataSelOpts{'selection'} = $selection;
    $dataSelOpts{'modifyItem'} = $modifyItem;
    $dataSelOpts{'segHierLevel'} = $segHierLevel;
    $dataSelOpts{'segmentationID'} = $segmentationID;
    $dataSelOpts{'selOp'} = $selOp;
    $dataSelOpts{'selDuration'} = $selDuration;
    $dataSelOpts{'selType'} = $selType;
    $dataSelOpts{'selEnding'} = $selEnding;
    $dataSelOpts{'selStarting'} = $selStarting;
    $dataSelOpts{'matchType'} = $matchType;

    datasel_add_item(\%dataSelOpts, @segmentIDs);
  }

  print_grid_javascript();

  print <<END_HTML;
<P>
<CENTER>
  <TABLE STYLE="width:900px; border-collapse:collapse;">
    <TR>
      <TD COLSPAN="2" STYLE="text-align:center">
        <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="location.href='$cancelHref'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
        <BUTTON CLASS="btn btn-primary" TYPE="button" onclick="location.href='/app/rpt/cubeUpdate.cld?ds=$dsID&rptID=$rptID&c=datasel'">Done <I CLASS="bi bi-arrow-right"></I></BUTTON>
        <P>
      </TD>
    </TR>
    <TR>
      <TD STYLE="width: 400px; border-right:2px solid lightblue; border-bottom:2px solid lightblue; padding:10px;">
        <FORM METHOD="post" ACTION="selectionMethod.cld">
        <INPUT TYPE="hidden" NAME="ds" VALUE="$dsID">
        <INPUT TYPE="hidden" NAME="rptID" VALUE="$rptID">
        <INPUT TYPE="hidden" NAME="dim" VALUE="p">

        <TABLE>
          <TR>
            <TD>
              <DIV id="prodGrid" CLASS="grid" STYLE="margin:auto; font-size:13px;"></DIV>
            </TD>
            <TD><P>&nbsp;</P></TD>
            <TD STYLE="vertical-align:center; text-align:center;">
              <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="topItem('p')" TITLE="Move selected items to the top of the list"><I CLASS="bi bi-chevron-bar-up"></I></BUTTON>
              <P></P>
              <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="upItem('p')" TITLE="Move the selected items up"><I CLASS="bi bi-chevron-up"></I></BUTTON>
              <P></P>
              <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="downItem('p')" TITLE="Move the selected items down"><I CLASS="bi bi-chevron-down"></I></BUTTON>
              <P></P>
              <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="bottomItem('p')" TITLE="Move selected items to the bottom of the list"><I CLASS="bi bi-chevron-bar-down"></I></BUTTON>
            </TD>
          </TR>
        </TABLE>

        <P>
        <DIV CLASS="text-center">
          <BUTTON CLASS="btn btn-primary" TYPE="submit" TITLE="Add Products"><I CLASS="bi bi-plus-lg"></I> Add</BUTTON>
          <BUTTON CLASS="btn btn-primary" TYPE="button" TITLE="Modify the selected product" onClick="modifySelection('p')"><I CLASS="bi bi-pencil"></I> Modify</BUTTON>
          <BUTTON CLASS="btn btn-primary" TYPE="button" TITLE="Remove selected products" onClick="removeSelection('p')"><I CLASS="bi bi-trash"></I> Remove</BUTTON>
        </DIV>
        </FORM>
      </TD>

      <TD STYLE="width:400px; border-left:2px solid lightblue; border-bottom:2px solid lightblue; padding:10px;">
        <FORM METHOD="post" ACTION="selectionMethod.cld">
        <INPUT TYPE="hidden" NAME="ds" VALUE="$dsID">
        <INPUT TYPE="hidden" NAME="rptID" VALUE="$rptID">
        <INPUT TYPE="hidden" NAME="dim" VALUE="g">

        <TABLE>
          <TR>
            <TD>
              <DIV id="geoGrid" CLASS="grid" STYLE="margin:auto; font-size:13px;"></DIV>
            </TD>
            <TD><P>&nbsp;</P></TD>
            <TD STYLE="vertical-align:center; text-align:center;">
              <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="topItem('g')" TITLE="Move selected items to the top of the list"><I CLASS="bi bi-chevron-bar-up"></I></BUTTON>
              <P></P>
              <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="upItem('g')" TITLE="Move the selected items up"><I CLASS="bi bi-chevron-up"></I></BUTTON>
              <P></P>
              <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="downItem('g')" TITLE="Move the selected items down"><I CLASS="bi bi-chevron-down"></I></BUTTON>
              <P></P>
              <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="bottomItem('g')" TITLE="Move selected items to the bottom of the list"><I CLASS="bi bi-chevron-bar-down"></I></BUTTON>
            </TD>
          </TR>
        </TABLE>

        <P>
        <DIV CLASS="text-center">
          <BUTTON CLASS="btn btn-primary" TYPE="submit" TITLE="Add Geographies"><I CLASS="bi bi-plus-lg"></I> Add</BUTTON>
          <BUTTON CLASS="btn btn-primary" TYPE="button" TITLE="Modify the selected geography" onClick="modifySelection('g')"><I CLASS="bi bi-pencil"></I> Modify</BUTTON>
          <BUTTON CLASS="btn btn-primary" TYPE="button" TITLE="Remove selected geographies" onClick="removeSelection('g')"><I CLASS="bi bi-trash"></I> Remove</BUTTON>
        </DIV>
        </FORM>
      </TD>
    </TR>

    <TR>
      <TD STYLE="width:400px;  border-right:2px solid lightblue; border-top:2px solid lightblue; padding:10px;">
        <FORM METHOD="post" ACTION="selectionMethod.cld">
        <INPUT TYPE="hidden" NAME="ds" VALUE="$dsID">
        <INPUT TYPE="hidden" NAME="rptID" VALUE="$rptID">
        <INPUT TYPE="hidden" NAME="dim" VALUE="t">

        <TABLE>
          <TR>
            <TD>
              <DIV id="timeGrid" CLASS="grid" STYLE="margin:auto; font-size:13px;"></DIV>
            </TD>
            <TD><P>&nbsp;</P></TD>
            <TD STYLE="vertical-align:center; text-align:center;">
              <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="topItem('t')" TITLE="Move selected items to the top of the list"><I CLASS="bi bi-chevron-bar-up"></I></BUTTON>
              <P></P>
              <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="upItem('t')" TITLE="Move the selected items up"><I CLASS="bi bi-chevron-up"></I></BUTTON>
              <P></P>
              <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="downItem('t')" TITLE="Move the selected items down"><I CLASS="bi bi-chevron-down"></I></BUTTON>
              <P></P>
              <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="bottomItem('t')" TITLE="Move selected items to the bottom of the list"><I CLASS="bi bi-chevron-bar-down"></I></BUTTON>
            </TD>
          </TR>
        </TABLE>

        <P>
        <DIV CLASS="text-center">
          <BUTTON CLASS="btn btn-primary" TYPE="submit" TITLE="Add Time Periods"><I CLASS="bi bi-plus-lg"></I> Add</BUTTON>
          <BUTTON CLASS="btn btn-primary" TYPE="button" TITLE="Modify the selected time period" onClick="modifySelection('t')"><I CLASS="bi bi-pencil"></I> Modify</BUTTON>
          <BUTTON CLASS="btn btn-primary" TYPE="button" TITLE="Remove selected time periods" onClick="removeSelection('t')"><I CLASS="bi bi-trash"></I> Remove</BUTTON>
        </DIV>
        </FORM>
      </TD>
      <TD STYLE="width:400px; border-left:2px solid lightblue; border-top:2px solid lightblue; padding:10px;"">
        <FORM METHOD="post" ACTION="selectionMethod.cld">
        <INPUT TYPE="hidden" NAME="ds" VALUE="$dsID">
        <INPUT TYPE="hidden" NAME="rptID" VALUE="$rptID">
        <INPUT TYPE="hidden" NAME="dim" VALUE="m">

        <TABLE>
          <TR>
            <TD>
              <DIV id="measGrid" CLASS="grid" STYLE="margin:auto; font-size:13px;"></DIV>
            </TD>
            <TD><P>&nbsp;</P></TD>
            <TD STYLE="vertical-align:center; text-align:center;">
              <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="topItem('m')" TITLE="Move selected items to the top of the list"><I CLASS="bi bi-chevron-bar-up"></I></BUTTON>
              <P></P>
              <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="upItem('m')" TITLE="Move the selected items up"><I CLASS="bi bi-chevron-up"></I></BUTTON>
              <P></P>
              <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="downItem('m')" TITLE="Move the selected items down"><I CLASS="bi bi-chevron-down"></I></BUTTON>
              <P></P>
              <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="bottomItem('m')" TITLE="Move selected items to the bottom of the list"><I CLASS="bi bi-chevron-bar-down"></I></BUTTON>
            </TD>
          </TR>
        </TABLE>

        <P>
        <DIV CLASS="text-center">
          <BUTTON CLASS="btn btn-primary" TYPE="submit" TITLE="Add Measures"><I CLASS="bi bi-plus-lg"></I> Add</BUTTON>
          <BUTTON CLASS="btn btn-primary" TYPE="button" TITLE="Modify the selected measure" onClick="modifySelection('m')"><I CLASS="bi bi-pencil"></I> Modify</BUTTON>
          <BUTTON CLASS="btn btn-primary" TYPE="button" TITLE="Remove selected measures" onClick="removeSelection('m')"><I CLASS="bi bi-trash"></I> Remove</BUTTON>
        </DIV>
        </FORM>
      </TD>
    </TR>
  </TABLE>
</CENTER>
END_HTML

  print_html_footer();

#EOF
