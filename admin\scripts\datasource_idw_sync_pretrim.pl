#!/usr/bin/perl

use lib "/opt/apache/app/";

use DBI;
use Text::CSV;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::PrepClientTrim;
use Lib::WebUtils;



$DSID = 3231;
%GEOHASH = prep_client_trim_hash("esm");

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsSchema = "datasource_" . $DSID;

  #run through every geography in the specified datasource
  $query = "SELECT ID, name FROM $dsSchema.geographies ORDER BY name";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  while (($geoID, $geoName) = $dbOutput->fetchrow_array)
  {
    if ($GEOHASH{$geoName} != 1)
    {
      print("Deleting $geoName\n");

      $query = "DELETE FROM $dsSchema.geographies WHERE ID=$geoID";
      print("$query\n");
      $db->do($query);

      $query = "DELETE FROM $dsSchema.facts WHERE geographyID=$geoID";
      print("$query\n");
      $db->do("$query");
    }
  }

#EOF
