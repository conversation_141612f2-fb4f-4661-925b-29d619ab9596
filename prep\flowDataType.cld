#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;
use File::Copy;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($kapDB);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Set Data Types</TITLE>
<LINK REL="SHORTCUT ICON" HREF="$Lib::KoalaConfig::kapHostURL/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Saving...');
  return(true);
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($kapDB, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="$Lib::KoalaConfig::kapHostURL/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">Data Prep</A></LI>
    <LI CLASS="breadcrumb-item">$flowName</LI>
    <LI CLASS="breadcrumb-item active">Set Data Types</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $flowID = $q->param('f');
  $jobID = $q->param('j');
  $action = $q->param('a');

  #connect to the database
  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #perform strict state checking, otherwise return user to flowOpen
  $query = "SELECT state FROM prep.jobs WHERE ID=$jobID";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($state) = $dbOutput->fetchrow_array;

  #we should be in NESTED-CONVERT state when we're initially called
  if (($state ne "LOAD-DATA") && ($state ne "DATATYPE-WAIT"))
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: flowOpen.cld?f=$flowID\n\n");
    exit;
  }

  #set our state in case the user needs to resume
  $query = "UPDATE prep.jobs SET state='DATATYPE-WAIT' WHERE ID=$jobID";
  $prepDB->do($query);

  if ($action eq "e")
  {
    $actionVal = "Edit";
  }
  else
  {
    $actionVal = "New";
  }

  $flowName = prep_flow_id_to_name($prepDB, $flowID);

  print_html_header();

  #make sure we have write privs for this data source
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to work with jobs in this data flow.");
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-10 col-lg-7 col-xl-6"> <!-- content -->

      <FORM METHOD="post" ACTION="flowMergeData.cld" onsubmit="return checkForm(this);">
      <INPUT TYPE="hidden" NAME="f" VALUE="$flowID">
      <INPUT TYPE="hidden" NAME="j" VALUE="$jobID">
      <INPUT TYPE="hidden" NAME="a" VALUE="$action">

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Data Types</DIV>
        <DIV CLASS="card-body">

          <DIV CLASS="table-responsive">
            <TABLE CLASS="table table-hover table-bordered table-sm">
              <THEAD><TR>
                <TH>File</TD>
                <TH>Data</TD>
                <TH>Lookup</TD>
                <TH>Ignore</TD>
              </TR></THEAD>
END_HTML

  #if we're editing an existing flow, load up our file type info to display
  undef(%fileTypes);
  if ($action eq "e")
  {
    $query = "SELECT name, tabName, type FROM prep.file_types WHERE flowID=$flowID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;

    while (($name, $tabName, $type) = $dbOutput->fetchrow_array)
    {

      #deal with the kludge we had to use to get the DB primary key to work
      if ($tabName eq " ")
      {
        $tabName = "";
      }

      $key = "$name-$tabName";
      $fileTypes{$key} = $type;
    }
  }

  #get every file/tab uploaded by the user in this run
  $query = "SELECT ID, userFilename, tabName FROM prep.files \
      WHERE jobID=$jobID ORDER by userFilename, tabName";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;

  while (($id, $userFilename, $tabName) = $dbOutput->fetchrow_array)
  {

    #default file type is data
    $chkData = "CHECKED";
    $chkLookup = $chkIgnore = "";

    #if the user has already defined a type, use it
    $key = "$userFilename-$tabName";
    if ($fileTypes{$key} eq "data")
    {
      $chkData = "CHECKED";
      $chkLookup = $chkIgnore = "";
    }
    elsif ($fileTypes{$key} eq "lookup")
    {
      $chkLookup = "CHECKED";
      $chkData = $chkIgnore = "";
    }
    elsif ($fileTypes{$key} eq "ignore")
    {
      $chkIgnore = "CHECKED";
      $chkData = $chkLookup = "";
    }
    elsif ($key =~ m/lookup/i)
    {
      $chkLookup = "CHECKED";
      $chkData = $chkIgnore = "";
    }

    #make the tab name user-friendly if it exists
    if (length($tabName) > 0)
    {
      $tabName = "<EM>Tab:</EM> $tabName";
    }

    print <<END_HTML;
              <TR>
                <TD>$userFilename $tabName</TD>
                <TD CLASS="text-center">
                  <DIV CLASS="form-check form-check-inline" STYLE="margin-top:0px; margin-bottom:0px; text-align:center;">
                    <INPUT CLASS="form-check-input" NAME="F $id" ID="D_$id" VALUE="D $id" TYPE="radio" $chkData>
                    <LABEL CLASS="custom-control-label" FOR="D_$id">&nbsp;</LABEL>
                  </DIV>
                </TD>
                <TD CLASS="text-center">
                  <DIV CLASS="form-check form-check-inline" STYLE="margin-top:0px; margin-bottom:0px; text-align:center;">
                    <INPUT CLASS="form-check-input" NAME="F $id" ID="L_$id" VALUE="L $id" TYPE="radio" $chkLookup>
                    <LABEL CLASS="form-check-label" FOR="L_$id">&nbsp;</LABEL>
                  </DIV>
                </TD>
                <TD CLASS="text-center">
                  <DIV CLASS="form-check form-check-inline" STYLE="margin-top:0px; margin-bottom:0px; text-align:center;">
                    <INPUT CLASS="form-check-input" NAME="F $id" ID="I_$id" VALUE="I $id" TYPE="radio" $chkIgnore>
                    <LABEL CLASS="form-check-label" FOR="I_$id">&nbsp;</LABEL>
                  </DIV>
                </TD>
              </TR>
END_HTML
  }

  print <<END_HTML;
            </TABLE>
          </DIV>

          <P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onclick="location.href='main.cld'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-submit">Next <I CLASS="bi bi-arrow-right"></I></BUTTON>
          </DIV>

        </DIV>
      </DIV>

      </FORM>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();


#EOF
