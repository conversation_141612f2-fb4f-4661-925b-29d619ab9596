#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::DataSources;
use Lib::KoalaConfig;
use Lib::DSRCreateUpdate;
use Lib::DSRMeasures;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Update Data Source</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
let statusTimer = setInterval(function(){displayStatus()}, 5000);
let started = 0;
let emptyCounts = 0;

function displayStatus()
{
  let url = '/app/dsr/xhrDSstatus.cld?ds=$dsID';

  \$.get(url, function(data, status)
  {
    statusText = data;

    document.getElementById('progressDiv').innerHTML = statusText;

    if (statusText.search('ERROR') >= 0)
    {
      clearInterval(statusTimer);
      \$('#btnAsync').text('Cancel');
      document.getElementById('progress-bar').style.visibility = 'hidden';
    }

    if (statusText.length > 2)
    {
      started = 1;
    }
    else
    {
      emptyCounts++;
    }

    if (((statusText.length < 2) && (started == 1)) || (emptyCounts > 2))
    {
      clearInterval(statusTimer);
      location.href='$Lib::KoalaConfig::kapHostURL/app/dsr/display.cld?ds=$dsID';
    }

  });
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item">$dsName</LI>
    <LI CLASS="breadcrumb-item active">Update Data Source</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------
#
# Output the HTML for the web page that displays the status dialog
#

sub print_status_html
{
  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-11 col-md-8 col-lg-6 col-xl-5"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Updating Data Source</DIV>
        <DIV CLASS="card-body">

          <DIV CLASS="progress">
            <DIV ID="progress-bar" CLASS="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" STYLE="width:100%;">
            </DIV>
          </DIV>

          <P>&nbsp;</P>
          <DIV CLASS="text-center">
            <DIV ID="progressDiv">Beginning data update</DIV>
          </DIV>

          <P>&nbsp;</P>
          Koala can finish updating your data source in the background, and notify you when it's done. While it's working, you can work on other things.

          <P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" ID="btnAsync" onClick="location.href='?async=1&ds=$dsID'"><I CLASS="bi bi-speedometer"></I> Be More Productive</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->

END_HTML

  print_html_footer();
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get the CGI input variables
  $key = $q->param('key');
  $tabs = $q->param('tabs');
  $layout = $q->param('layout');
  $dsID = $q->param('ds');
  $pmatch = $q->param('pmatch');
  $gmatch = $q->param('gmatch');
  $tmatch = $q->param('tmatch');
  $async = $q->param('async');
  $options = $q->param('options');
  $dontOverwriteNames = $q->param('dontOverwriteNames');
  $normalizationScript = $q->param('scriptID');

  #our unique key for this run is the userID and the key we were passed
  $uniqueKey = "$userID.$key";

  #connect to database
  $db = KAPutil_connect_to_database();

  #if we were just requested to go into async mode
  if (defined($async))
  {
    $userName = utils_userID_to_name($db, $userID);
    $query = "UPDATE app.jobs SET opInfo = 'Update|$userName|$email' \
        WHERE dsID=$dsID AND opInfo LIKE 'Update%'; ";
    $db->do($query);
    print("Location: $Lib::KoalaConfig::kapHostURL/app/dsr/main.cld\n\n");
    exit;
  }

  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  #make sure we have write privs for the data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to modify this data source.");
  }

  #if the data source is already being updated by another process, join the
  #status stream
  $query = "SELECT opInfo FROM app.jobs WHERE dsID=$dsID AND operation='DS-UPDATE'";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($opInfo) = $dbOutput->fetchrow_array;

  if ($opInfo =~ m/^Update/)
  {
    print_status_html();
    exit;
  }

  #set the initial values for the data source import/update options
  #NB: These will probably never change from the initial import, but we need to
  #    give the user the option just in case
  $appendUPC = 0;
  $compressWS = 0;
  if ($options =~ m/appendUPC/)
  {
    $appendUPC = 1;
  }
  if ($options =~ m/compressWS/)
  {
    $compressWS = 1;
  }

  $query = "UPDATE dataSources \
      SET appendUPC=$appendUPC, compressWS=$compressWS WHERE ID=$dsID";
  $db->do($query);

  #fork a new process to do the actual update in the background
  $SIG{CHLD} = "IGNORE";
  if ($pid = fork())
  {
    #parent process

    print_status_html();

    #log the update to Slack
    $dsName = ds_id_to_name($db, $dsID);
    $activity = "$first $last updating data source $dsName, layout: $layout, Excel tabs: $tabs, product match: $pmatch, geography match: $gmatch, time match: $tmatch, options: $options";
    utils_slack($activity);
  }
  else
  {
    #child process

    #let Apache know not to wait on the child process
    close(STDIN);
    close(STDOUT);
    close(STDERR);

    $excelTabs = "\"$tabs\"";

    #put our error messages & output in the Apache logs
    open(STDERR, ">>/opt/apache/htdocs/tmp/dsimport_$dsID.log") or die("Unable to open STDERR, $!");
    open(STDOUT, ">>/opt/apache/htdocs/tmp/dsimport_$dsID.log") or die ("Unable to open STDOUT, $!");

    #connect to database
    $db = KAPutil_connect_to_database();

    #ds_update($childDB, $userID, $dsID, $layout, $pmatch, $gmatch, $tmatch, $key, $excelTabs, $options, $normalizationScript);
    ds_create_update($db, $userID, $dsID, 0, $layout, $pmatch, $gmatch,
        $tmatch, $key, $excelTabs, $options, $dontOverwriteNames, $normalizationScript);
  }



#EOF
