#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;
use Statistics::R;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::WebUtils;
use Lib::Forecasts;


#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Elasticity Model Job Progress</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
let statusTimer = setInterval(function(){displayStatus()}, 1000);
let statCount = 0;

function displayStatus()
{
  let url = 'xhrRefreshStatus.cld?a=$fcID&ds=$dsID';

  \$.get(url, function(data, status)
  {
    let statusText = data;

    if (statusText.length < 5)
    {
      statusText = '0% Loading Data';
    }

    if (statusText.search('DONE') == 0)
    {
      \$('#progress-bar').css('width', '100%');
      document.getElementById('progress-bar').innerHTML = '100%';
      \$('#progressDiv').hide();
      \$('#detailedDiv').hide();
      clearInterval(statusTimer);
      location.href='display.cld?fcID=$fcID';
    }
    else
    {
      statElements = statusText.split('|');

      pct = statElements[0];
      statusText = statElements[1];

      if (pct > 5)
      {
        \$('#progress-bar').css('width', pct+'%');
      }

      document.getElementById('progress-bar').innerHTML = pct + '%';
      document.getElementById('progressDiv').innerHTML = statusText;
    }

    if (statCount > 5)
    {
      clearInterval(statusTimer);
      statusTimer = setInterval(function(){displayStatus()}, 5000);
    }
    statCount++;
  });
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">KCast</A></LI>
    <LI CLASS="breadcrumb-item">$dsName</LI>
    <LI CLASS="breadcrumb-item active">Build Forecast</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------
#
# Output the HTML for the web page that displays the status dialog
#

sub print_status_html
{
  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-11 col-md-8 col-lg-6 col-xl-5"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">$actionHR Forecast</DIV>
        <DIV CLASS="card-body">

          <DIV CLASS="progress">
            <DIV ID="progress-bar" CLASS="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" STYLE="width:5%;">
              0%
            </DIV>
          </DIV>

          <P>
          <DIV CLASS="text-center">
            <DIV ID="progressDiv">Refreshing Forecast</DIV>
          </DIV>

          <P>&nbsp;</P>
          Koala can continue working in the background while you work on other things.

          <P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" ID="btnAsync" onClick="location.href='main.cld'"><I CLASS="bi bi-speedometer"></I> Be More Productive</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get the CGI input variables
  $fcID = $q->param('f');
  $action = $q->param('a');
  $dsID = $q->param('ds');
  $name = $q->param('name');
  $description = $q->param('description');
  $fcastType = $q->param('type');
  $measureID = $q->param('measure');
  $predict = $q->param('predict');
  $periodType = $q->param('periodType');
  $modelStartTime = $q->param('modelStartTime');
  $productIDs = $q->param('productIDs');
  $geographyIDs = $q->param('geographyIDs');

  @productIDs = split(/ /, $productIDs);
  @geographyIDs = split(/ /, $geographyIDs);

  $products = join(',', @productIDs);
  $geographies = join(',', @geographyIDs);

  #convert the periodType to a numeric frequency
  if ($periodType eq "Weekly")
  {
    $frequency = 52;
  }
  elsif ($periodType eq "Monthly")
  {
    $frequency = 12;
  }
  elsif ($periodType eq "Quarterly")
  {
    $frequency = 4;
  }
  elsif ($periodType eq "Yearly")
  {
    $frequency = 1;
  }
  else
  {
    $frequency = 1;
  }

  $db = KAPutil_connect_to_database();

  #make sure we have write privs for this forecast
  $privs = forecast_rights($db, $userID, $fcID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this forecast.");
  }

  if ($dsID < 1)
  {
    $query = "SELECT dsID FROM analytics.forecasts WHERE ID=$fcID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($dsID) = $dbOutput->fetchrow_array;
  }

  $dsSchema = "datasource_" . $dsID;

  #if we're (maybe) rejoining an existing forecast job
  if (($fcID > 0) && ($action ne "e"))
  {
    $query = "SELECT PID FROM app.jobs \
        WHERE analyticsID=$elasticID AND operation='FORECAST'";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($pid) = $dbOutput->fetchrow_array;
    if ($pid > 0)
    {
      print_html_header();
      print_status_html();
      exit;
    }
  }

  #figure out the list of time periods we're going to use to build a model
  $query = "SELECT type, duration, endDate FROM $dsSchema.timeperiods \
      WHERE ID=$modelStartTime";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($type, $duration, $endDate) = $dbOutput->fetchrow_array;

  $query = "SELECT ID FROM $dsSchema.timeperiods \
      WHERE type=$type AND duration=$duration AND endDate >= '$endDate' \
      ORDER BY endDate ASC";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  while (($id) = $dbOutput->fetchrow_array)
  {
    push(@timeIDs, $id);
  }
  $times = join(',', @timeIDs);

  #make CGI input SQL-friendly
  $q_name = $db->quote($name);
  $q_type = $db->quote($fcastType);
  $q_description = $db->quote($description);
  $q_measureID = $db->quote($measureID);
  $q_times = $db->quote($times);
  $q_products = $db->quote($products);
  $q_geographies = $db->quote($geographies);

  #if we're editing an existing model, store any potentially updated info
  if (($fcID > 0) && ($action eq "e"))
  {
    #store the elasticity info and get our ID
    $query = "UPDATE analytics.forecasts SET name = $q_name, userID = $userID, \
        timeperiods = $q_times, products = $q_products, \
        geographies = $q_geographies, measureID = $q_measureID, \
        forecastType = $q_type, periodType = $frequency, \
        futureperiods = $predict, dsID = $dsID, description = $q_description \
        WHERE ID=$fcID";
    $db->do($query);
  }

  #if we weren't passed the ID of an existing elasticity study, we need to
  #create a new one
  if ($fcID < 1)
  {
    $query = "INSERT INTO analytics.forecasts (name, userID, timeperiods, products, geographies, measureID, forecastType, periodType, futureperiods, dsID, description) \
        VALUES ($q_name, $userID, $q_times, $q_products, $q_geographies, $q_measureID, $q_type, $frequency, $predict, $dsID, $q_description)";
    $db->do($query);

    #get the unique ID for this data source
    $fcID = $db->{q{mysql_insertid}};
  }

  print_html_header();

  $SIG{CHLD} = "IGNORE";
  if ($pid = fork())
  {
    #parent process - we're just going to finish up our display script

    print_status_html();
  }

  else
  {
    #child process - do the actual forecast creation/refresh

    #let Apache know not to wait on the child process
    close(STDIN);
    close(STDOUT);

    #redirect STDERR to the Koala error log
    close(STDERR);
    open(STDERR, ">>/opt/apache/htdocs/tmp/koala_error.log");
    select(STDERR);
    $| = 1;

    #we're in a new process, so we need a new connection to the database
    $db = KAPutil_connect_to_database();

    forecast_build($db, $dsID, $fcID, $userID);
  }


#EOF
