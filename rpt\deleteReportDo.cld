#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Delete Report</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/rpt/main">Reports</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Delete $objectTypeCaps</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser and get the forecast to be deleted
  $q = new CGI;
  $rptIDStr = $q->param('rptID');
  $dsID = $q->param('dsID');

  @rptIDs = split(',', $rptIDStr);
  $rptIDStr = join(',', @rptIDs);

  #temporarily restrict to 1 report at a time for deletion
  $rptID = $rptIDs[0];

  #set some human-readable UI text
  $objectType = "report";
  $objectTypeCaps = "Report";
  $objectArticlePast = "has";
  if (scalar(@rptIDs) > 1)
  {
    $objectType = "reports";
    $objectTypeCaps = "Reports";
    $objectArticlePast = "have";
  }

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  $dsSchema = "datasource_" . $dsID;
  $rptCube = "_rptcube_" . $rptID;

  $db = KAPutil_connect_to_database();

  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  #make sure we have write privs for this data cube
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to delete these reports.");
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-10 col-lg-7 col-xl-6"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Delete $objectTypeCaps</DIV>
        <DIV CLASS="card-body">

          <P>
          The selected $objectType $objectArticlePast been deleted:
          <P>
          <TABLE CLASS="table table-striped table-sm table-bordered mx-auto">
END_HTML

  #get the selected report names from the database
  $query = "SELECT name, ID FROM app.cubes WHERE ID IN ($rptIDStr)";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  while (($rptName, $rptID) = $dbOutput->fetchrow_array)
  {
    $rptCube = "_rptcube_" . $rptID;

    #delete the forecast cubes from the database
    KAPutil_db_delete_table($db, $dsSchema, $rptCube);

    #remove the cube definition data
    $query = "DELETE FROM cubes WHERE ID=$rptID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    #remove any visuals associated with the cube
    $query = "DELETE FROM visuals WHERE cubeID=$rptID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    utils_audit($db, $userID, "Deleted report $cubeName from $dsName", $dsID, $rptID, 0);

    $activity = "$first $last deleted report $cubeName from $dsName";
    utils_slack($activity);

    print("<TR><TD>$rptName</TD></TR>\n");
  }

  print <<END_HTML;
          </TABLE>

          <P>&nbsp;</P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='/app/rpt/main?ds=$dsID'"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
          </DIV>
          </FORM>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
