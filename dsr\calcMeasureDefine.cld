#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  #output Content-type header
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  #output HTML
  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: $action Calculated Measure</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Please Wait...');
  return(true);
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">$action Calculated Measure $measName</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');
  $measName = $q->param('measureName');
  $measType = $q->param('measType');
  $calcBeforeAgg = $q->param('calcBeforeAgg');
  $measureID = $q->param('measID');

  $dsID = utils_sanitize_integer($dsID);
  if (!defined($dsID))
  {
    exit_early_error($session, "Invalid data source");
  }
  $measureID = utils_sanitize_integer($measureID);
  $measName = utils_sanitize_string($measName);

  if (defined($calcBeforeAgg))
  {
    $calcBeforeAgg = 1;
  }
  else
  {
    $calcBeforeAgg = 0;
  }

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsSchema = "datasource_" . $dsID;

  $dsName = ds_id_to_name($db, $dsID);

  $action = "New";

  #if we're editing an existing measure, grab the info we need
  if ($measureID > 0)
  {
    $query = "SELECT calculation FROM $dsSchema.measures WHERE ID=$measureID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($calculation) = $dbOutput->fetchrow_array;

    #extract the measure type from the calculation string
    $calculation =~ m/^(.*?)\|/;

    $action = "Edit";
  }

  #------------------------

  #if the user is asking us to create one of the more complex measures, fire
  #off a re-direct to that measure's definition page(s)
  if (($measType eq "share") || ($measType eq "index"))
  {
    print($session->header());
    print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META HTTP-EQUIV="refresh" CONTENT="0; URL=calcMeasureIdxShrVar1.cld?ds=$dsID&measureName=$measName&measType=$measType&measID=$measureID&calcBeforeAgg=$calcBeforeAgg">
</HEAD>
<BODY>
</BODY>
</HTML>
END_HTML
    exit;
  }

  #------------------------

  if ($measType eq "calc")
  {
    print($session->header());
    print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META HTTP-EQUIV="refresh" CONTENT="0; URL=calcMeasureCalculator.cld?ds=$dsID&measureName=$measName&measType=$measType&measID=$measureID&calcBeforeAgg=$calcBeforeAgg">
</HEAD>
<BODY>
</BODY>
</HTML>
END_HTML
    exit;
  }

  print_html_header();

  #make sure we have write privs for the data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to modify the data source.");
  }

  #get hash of measure names and IDs
  %measureNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "m");

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-11 col-lg-8 col-xl-7"> <!-- content -->

      <FORM METHOD="post" ACTION="/app/dsr/calcMeasureSave.cld" onsubmit="return checkForm(this);">
      <INPUT TYPE="hidden" NAME="ds" VALUE="$dsID">
      <INPUT TYPE="hidden" NAME="measName" VALUE="$measName">
      <INPUT TYPE="hidden" NAME="measType" VALUE="$measType">
      <INPUT TYPE="hidden" NAME="calcBeforeAgg" VALUE="$calcBeforeAgg">
      <INPUT TYPE="hidden" NAME="measureID" VALUE="$measureID">

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Calculated Measure</DIV>
        <DIV CLASS="card-body">
END_HTML

  #------------------------

  #if we're doing a % Change or change calculated measure
  if (($measType eq "pct_change") || ($measType eq "change"))
  {

    #if we're editing an existing pct_change/change measure
    if ($measureID > 0)
    {
      $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|/;
      $baseMeasure = $1;
      $period = $2;
      $periodType = $3;
    }

    print("<P></P>\n");
    print <<END_HTML;
          <LABEL FOR="measure">Which measure do you want to calculate a 1-year change for?</LABEL>
          <SELECT CLASS='form-select mx-1' NAME='measure' ID='measure' STYLE="width:auto;" required>
END_HTML

    foreach $id (sort {$measureNameHash{$a} cmp $measureNameHash{$b}} keys %measureNameHash)
    {
      if ($id != $measureID)
      {
        print(" <OPTION VALUE=$id>$measureNameHash{$id}</OPTION>\n");
      }
    }

    print <<END_HTML;
          </SELECT>
          <SCRIPT>
            \$('select#measure').val('$baseMeasure');
          </SCRIPT>
END_HTML
  }

  #------------------------

  #if we're doing a ratio measure
  elsif ($measType eq "ratio")
  {

    #if we're editing an existing pct_change/change measure
    if ($measureID > 0)
    {
      $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|/;
      $anteMeasure = $1;
      $consMeasure = $2;
      $convertPct = $3;
    }

    $checked = "";
    if ($convertPct > 0)
    {
      $checked = "CHECKED";
    }

    print <<END_HTML;
          <DIV CLASS="container">
            <DIV CLASS="row align-items-end">
              <DIV CLASS="col">
                <LABEL FOR="measure1">Measure:</LABEL>
                <SELECT CLASS="form-select mx-1" NAME="measure1" ID="measure1" required>
END_HTML

    foreach $id (sort {$measureNameHash{$a} cmp $measureNameHash{$b}} keys %measureNameHash)
    {
      if ($id != $measureID)
      {
        print(" <OPTION VALUE=$id>$measureNameHash{$id}</OPTION>\n");
      }
    }

    print <<END_HTML;
                </SELECT>
              </DIV>
              <DIV CLASS="col">
                <LABEL FOR="measure2">Measure to divide by:</LABEL>
                <SELECT CLASS="form-select mx-1" NAME="measure2" ID="measure2" required>
END_HTML

    foreach $id (sort {$measureNameHash{$a} cmp $measureNameHash{$b}} keys %measureNameHash)
    {
      if ($id != $measureID)
      {
        print(" <OPTION VALUE=$id>$measureNameHash{$id}</OPTION>\n");
      }
    }

    print <<END_HTML;
                </SELECT>
                <SCRIPT>
                  \$('select#measure1').val('$anteMeasure');
                  \$('select#measure2').val('$consMeasure');
                </SCRIPT>
              </DIV>
            </DIV>
          </DIV>

          <P>
          <DIV CLASS="form-check">
            <INPUT CLASS="form-check-input" TYPE="checkbox" NAME="convertPct" ID="convertPct" $checked>
            <LABEL CLASS="form-check-label" FOR="convertPct">Convert to a percentage (multiply by 100)</LABEL>
          </DIV>
END_HTML
  }

  #------------------------

  #if we're doing a multiplication calculated measure
  elsif ($measType eq "multiplication")
  {

    $constant = "1.0";
    undef(%selMeasures);

    if ($measureID > 0)
    {
      $calculation =~ m/.*?\|(.*?)\|(.*?)\|/;
      $measures = $1;
      $constant = $2;

      @measureArray = split(',', $measures);
      foreach $id (@measureArray)
      {
        $selMeasures{$id} = 1;
      }
    }

    #output possible measures in alpha order in a select box
    print <<END_HTML;
          <LABEL FOR='multimeasures'>Select the measures to be multiplied together:</LABEL>
          <SELECT CLASS='form-select mx-1' NAME='multimeasures' ID='multimeasures' SIZE=\"10\" MULTIPLE='true' required>
END_HTML

    foreach $id (sort {$measureNameHash{$a} cmp $measureNameHash{$b}} keys %measureNameHash)
    {
      if ($id != $measureID)
      {
        if ($selMeasures{$id} == 1)
        {
          print(" <OPTION SELECTED VALUE=$id>$measureNameHash{$id}</OPTION>\n");
        }
        else
        {
          print(" <OPTION VALUE=$id>$measureNameHash{$id}</OPTION>\n");
        }
      }
    }

    print <<END_HTML;
          </SELECT>
          <DIV CLASS="row my-3">
            <DIV CLASS="col text-end mt-1">
              <LABEL FOR="constant">Constant to multiply by:</LABEL>
            </DIV>
            <DIV CLASS="col text-start">
              <INPUT TYPE="number" CLASS="form-control mx-1" NAME="constant" ID="constant" VALUE="$constant" STYLE="width:5em;" required></INPUT>
            </DIV>
          </DIV>
END_HTML
  }

  #------------------------

  #if we're doing a sum calculated measure
  elsif ($measType eq "sum")
  {
    undef(%selMeasures);
    if ($measureID > 0)
    {
      $calculation =~ m/.*?\|(.*?)\|/;
      $measures = $1;

      @measureArray = split(',', $measures);
      foreach $id (@measureArray)
      {
        $selMeasures{$id} = 1;
      }
    }

    #output possible measures in alpha order in a select box
    print <<END_HTML;
          <LABEL FOR ='multimeasures'>Select the measures to be summed together:</LABEL>
          <SELECT NAME='multimeasures' ID='multimeasures' CLASS='form-select mx-1' SIZE='10' MULTIPLE='true' required>
END_HTML

    foreach $id (sort {$measureNameHash{$a} cmp $measureNameHash{$b}} keys %measureNameHash)
    {
      if ($id != $measureID)
      {
        if ($selMeasures{$id} == 1)
        {
          print(" <OPTION SELECTED VALUE=$id>$measureNameHash{$id}</OPTION>\n");
        }
        else
        {
          print(" <OPTION VALUE=$id>$measureNameHash{$id}</OPTION>\n");
        }
      }
    }

    print <<END_HTML;
          </SELECT>
END_HTML
  }

  #------------------------

  #if we're doing a "% change between measures" measure
  elsif ($measType eq "pct_change_meas")
  {

    if ($measureID > 0)
    {
      $calculation =~ m/.*?\|(.*?)\|(.*?)\|/;
      $measure1 = $1;
      $measure2 = $2;
    }

    print <<END_HTML;
    <DIV CLASS="container">
      <DIV CLASS="row align-items-end">
        <DIV CLASS="col">
          <LABEL FOR="measure1">Measure:</LABEL>
          <SELECT CLASS="form-select mx-1" NAME="measure1" ID="measure1" required>
END_HTML

    foreach $id (sort {$measureNameHash{$a} cmp $measureNameHash{$b}} keys %measureNameHash)
    {
      if ($id != $measureID)
      {
        print(" <OPTION VALUE=$id>$measureNameHash{$id}</OPTION>\n");
      }
    }

    print <<END_HTML;
                </SELECT>
              </DIV>
              <DIV CLASS="col">
                <LABEL FOR="measure2">Measure to compare with:</LABEL>
                <SELECT CLASS="form-select mx-1" NAME='measure2' ID="measure2" required>
END_HTML

    foreach $id (sort {$measureNameHash{$a} cmp $measureNameHash{$b}} keys %measureNameHash)
    {
      if ($id != $measureID)
      {
        print(" <OPTION VALUE=$id>$measureNameHash{$id}</OPTION>\n");
      }
    }

    print <<END_HTML;
                </SELECT>
                <SCRIPT>
                  \$('select#measure1').val('$measure1');
                  \$('select#measure2').val('$measure2');
                </SCRIPT>
              </DIV>
            </DIV>
          </DIV>
END_HTML
  }

  #------------------------

  #if we're doing a lag calculated measure
  elsif ($measType eq "lag")
  {

    if ($measureID > 0)
    {
      $calculation =~ m/.*?\|(.*?)\|/;
      $measure = $1;
    }

    #output possible measures in alpha order in a select box
    print <<END_HTML;
          <LABEL FOR="measure">Which measure do you want to calculate a lag for?</LABEL>
          <SELECT CLASS='form-select my-1' NAME='measure' ID='measure' STYLE="width:auto;" required>
END_HTML

    foreach $id (sort {$measureNameHash{$a} cmp $measureNameHash{$b}} keys %measureNameHash)
    {
      if ($id != $measureID)
      {
        print(" <OPTION VALUE=$id>$measureNameHash{$id}</OPTION>\n");
      }
    }

    print <<END_HTML;
          </SELECT>
          <SCRIPT>
            \$('select#measure').val('$measure');
          </SCRIPT>
END_HTML
  }

  #------------------------

  #if we're doing a lead calculated measure
  elsif ($measType eq "lead")
  {

    if ($measureID > 0)
    {
      $calculation =~ m/.*?\|(.*?)\|/;
      $measure = $1;
    }

    #output possible measures in alpha order in a select box
    print <<END_HTML;
          <LABEL FOR="measure">Which measure do you want to calculate a lead for?</LABEL>
          <SELECT CLASS='form-select my-1' NAME='measure' ID='measure' STYLE="width:auto;" required>
END_HTML

    foreach $id (sort {$measureNameHash{$a} cmp $measureNameHash{$b}} keys %measureNameHash)
    {
      if ($id != $measureID)
      {
        print(" <OPTION VALUE=$id>$measureNameHash{$id}</OPTION>\n");
      }
    }

    print <<END_HTML;
          </SELECT>
          <SCRIPT>
            \$('select#measure').val('$measure');
          </SCRIPT>
END_HTML
  }

  #------------------------

  #if we're doing a moving total measure
  elsif ($measType eq "mov_total")
  {

    if ($measureID > 0)
    {
      $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|/;
      $baseMeasure = $1;
      $periods = $2;
      $timeDirection = $3;
    }
    else
    {
      $periods = 3;
    }

    print <<END_HTML;
          <DIV CLASS="row">
            <DIV CLASS="col-auto mt-1">
              Calculate a moving total on
            </DIV>
            <DIV CLASS="col-auto text-start">
              <SELECT CLASS="form-select my-1 text-start" NAME='measure1' ID="measure1" required>
END_HTML

    foreach $id (sort {$measureNameHash{$a} cmp $measureNameHash{$b}} keys %measureNameHash)
    {
      if ($id != $measureID)
      {
        print(" <OPTION VALUE=$id>$measureNameHash{$id}</OPTION>\n");
      }
    }

    print <<END_HTML;
              </SELECT>
            </DIV>
          </DIV>

          <DIV CLASS="row">
            <DIV CLASS="col-auto mt-1">
              for
            </DIV>
            <DIV CLASS="col-auto">
              <INPUT CLASS="form-control mx-1 my-1" NAME="constant" ID="constant" TYPE="number" VALUE='$periods' STYLE="width:5em;" min="1" required>
            </DIV>
            <DIV CLASS="col-auto mt-1">
              periods in the
            </DIV>
            <DIV CLASS="col-auto">
              <SELECT CLASS="form-select mx-1 my-1" NAME="timeDirection" ID="timeDirection" required>
                <OPTION VALUE="past">past</OPTION>
                <OPTION VALUE="future">future</OPTION>
              </SELECT>
              <SCRIPT>
                \$('select#measure1').val('$baseMeasure');
                \$('select#timeDirection').val('$timeDirection');
              </SCRIPT>
            </DIV>
          </DIV>
END_HTML
  }

  #------------------------

  #if we're doing a moving average measure
  elsif ($measType eq "mov_avg")
  {

    if ($measureID > 0)
    {
      $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|/;
      $baseMeasure = $1;
      $periods = $2;
      $timeDirection = $3;
    }
    else
    {
      $periods = 3;
    }

    print <<END_HTML;
          <DIV CLASS="row mb-1">
            <DIV CLASS="col-auto mt-1">
              Calculate a moving average on
            </DIV>
            <DIV CLASS="col-auto">
              <SELECT CLASS="form-select" NAME='measure1' ID="measure1" required>
END_HTML

    foreach $id (sort {$measureNameHash{$a} cmp $measureNameHash{$b}} keys %measureNameHash)
    {
      if ($id != $measureID)
      {
        print(" <OPTION VALUE=$id>$measureNameHash{$id}</OPTION>\n");
      }
    }

    print <<END_HTML;
              </SELECT>
            </DIV>
          </DIV>

          <DIV CLASS="row">
            <DIV CLASS="col-auto mt-1">
              for
            </DIV>
            <DIV CLASS="col-auto">
              <INPUT CLASS="form-control" TYPE="number" NAME="constant" ID="constant" STYLE="width:5em;" VALUE="$periods" min="1" required>
            </DIV>
            <DIV CLASS="col-auto mt-1">
              periods in the
            </DIV>
            <DIV CLASS="col-auto">
              <SELECT CLASS="form-select" NAME="timeDirection" ID="timeDirection">
                <OPTION VALUE="past">past</OPTION>
                <OPTION VALUE="future">future</OPTION>
              </SELECT>
              <SCRIPT>
                \$('select#measure1').val('$baseMeasure');
                \$('select#timeDirection').val('$timeDirection');
              </SCRIPT>
            </DIV>
          </DIV>
END_HTML
  }

  #------------------------

  #if we're doing a year to date measure
  elsif ($measType eq "ytd")
  {

    if ($measureID > 0)
    {
      $calculation =~ m/.*?\|(.*?)\|/;
      $baseMeasure = $1;
    }

    print <<END_HTML;
          <LABEL FOR="measure1">Calculate a year-to-date total for</LABEL>
          <SELECT CLASS="form-select my-1" NAME='measure1' ID="measure1" STYLE="width:auto;" required>
END_HTML

    foreach $id (sort {$measureNameHash{$a} cmp $measureNameHash{$b}} keys %measureNameHash)
    {
      if ($id != $measureID)
      {
        print(" <OPTION VALUE=$id>$measureNameHash{$id}</OPTION>\n");
      }
    }

    print <<END_HTML;
          </SELECT>
          <SCRIPT>
            \$('select#measure1').val('$baseMeasure');
          </SCRIPT>
END_HTML
  }

  #------------------------

  #if we're doing a difference calculated measure
  elsif ($measType eq "difference")
  {

    if ($measureID > 0)
    {
      $calculation =~ m/.*?\|(.*?)\|(.*?)\|/;
      $measure1 = $1;
      $measure2 = $2;
    }

    print <<END_HTML;
          <DIV CLASS="container">
            <DIV CLASS="row align-items-end">
              <DIV CLASS="col">
                <LABEL FOR="measure1">Measure to subtract from:</LABEL>
                <SELECT CLASS="form-select my-1" NAME='measure1' ID="measure1" SIZE=10 MULTIPLE required>
END_HTML

    foreach $id (sort {$measureNameHash{$a} cmp $measureNameHash{$b}} keys %measureNameHash)
    {
      if ($id != $measureID)
      {
        print(" <OPTION VALUE=$id>$measureNameHash{$id}</OPTION>\n");
      }
    }

    print <<END_HTML;
                </SELECT>
              </DIV>
              <DIV CLASS="col">
                <LABEL FOR="measure2">Measure to be subtracted:</LABEL>
                <SELECT CLASS="form-select my-1" NAME='measure2' ID="measure2" SIZE=10 MULTIPLE required>
END_HTML

    foreach $id (sort {$measureNameHash{$a} cmp $measureNameHash{$b}} keys %measureNameHash)
    {
      if ($id != $measureID)
      {
        print(" <OPTION VALUE=$id>$measureNameHash{$id}</OPTION>\n");
      }
    }

    print <<END_HTML;
                </SELECT>
                <SCRIPT>
                  \$('select#measure1').val($measure1);
                  \$('select#measure2').val($measure2);
                </SCRIPT>
              </DIV>
            </DIV>
          </DIV>
END_HTML
  }

  #------------------------

  #if we're doing a count measure
  elsif ($measType eq "count")
  {

    if ($measureID > 0)
    {
      $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|(.*?)\|(.*?)\|/;
      $dim = $1;
      $useCond = $2;
      $measure = $3;
      $conditional = $4;
      $constant = $5;

      if ($useCond > 0)
      {
        $useCond = "CHECKED";
      }
      else
      {
        $useCond = "";
      }
    }
    else
    {
      $dim = "p";
      $measure = 1;
      $conditional = "gt";
      $constant = 1;
    }

    print <<END_HTML;
          <DIV CLASS="row">
            <DIV CLASS="col-auto mt-1">
              Count the number of items in the
            </DIV>
            <DIV CLASS="col-auto">
              <SELECT CLASS="form-select" NAME="dim" ID="dim" required>
                <OPTION VALUE="p">Product</OPTION>
                <OPTION VALUE="g">Geography</OPTION>
                <OPTION VALUE="t">Time Periods</OPTION>
              </SELECT>
            </DIV>
            <DIV CLASS="col-auto mt-1">
              dimension.
            </DIV>
          </DIV>

          <P>&nbsp;</P>

          <DIV CLASS="row mb-1">
            <DIV CLASS="col-auto">
              <DIV CLASS="form-check">
                <INPUT CLASS="form-check-input" TYPE="checkbox" NAME="useCond" ID="useCond" $useCond>
                <LABEL CLASS="form-check-label" FOR="useCond">Only count items where</LABEL>
              </DIV>
            </DIV>
            <DIV CLASS="col-auto">
              <SELECT CLASS="form-select" NAME='measure1' ID="measure1">
END_HTML

    foreach $id (sort {$measureNameHash{$a} cmp $measureNameHash{$b}} keys %measureNameHash)
    {
      if ($id != $measureID)
      {
        print(" <OPTION VALUE=$id>$measureNameHash{$id}</OPTION>\n");
      }
    }

    print <<END_HTML;
              </SELECT>
            </DIV>
          </DIV>

          <DIV CLASS="row">
            <DIV CLASS="col-auto ms-4 mt-1">
              is
            </DIV>
            <DIV CLASS="col-auto">
              <SELECT CLASS="form-select" NAME="conditional" ID="conditional">
                <OPTION VALUE="eq">equal to</OPTION>
                <OPTION VALUE="gt">greater than</OPTION>
                <OPTION VALUE="lt">less than</OPTION>
              </SELECT>
            </DIV>
            <DIV CLASS="col-auto">
              <INPUT CLASS="form-control" NAME="constant" ID="constant" TYPE="number" VALUE="$constant" STYLE="width:75px;">
            </DIV>
            <SCRIPT>
              \$('select#dim').val('$dim');
              \$('select#measure1').val('$measure');
              \$('select#conditional').val('$conditional');
            </SCRIPT>
          </DIV>
END_HTML
  }

  #------------------------

  print <<END_HTML;
          <P>&nbsp;<P>

          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onclick="location.href='display.cld?ds=$dsID&dim=m'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-submit">Next <I CLASS="bi bi-arrow-right"></I></BUTTON>
          </DIV>

          </FORM>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
