#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::BuildCube;
use Lib::DataSel;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::Reports;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<DOCCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName Update Report</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
let statusTimer = setInterval(function(){displayStatus()}, 5000);

function displayStatus()
{
  const url = "/app/rpt/xhrCubeStatus.cld?c=$rptID";

  \$.get(url, function(data, status)
  {
    let statusText = data;

    if (statusText.length < 2)
    {
      statusText = "Building cube";
    }

    if (statusText.length == 5)  //DONE\n
    {
      clearInterval(statusTimer);
      location.href='/app/rpt/display.cld?rpt=$rptID';
    }

    document.getElementById('progressDiv').innerHTML = statusText;
  });
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/rpt/main">Reports</A></LI>
    <LI CLASS="breadcrumb-item">$rptName</LI>
    <LI CLASS="breadcrumb-item active">Refresh Report</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------
#
# Output the HTML for the web page that displays the status dialog
#

sub print_status_html
{
  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-11 col-md-8 col-lg-6 col-xl-5"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Building Report Data Cube</DIV>
        <DIV CLASS="card-body">

          <P>
          <DIV CLASS="progress">
            <DIV ID="progress-bar" CLASS="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" STYLE="width:100%;">
              Working...
            </DIV>
          </DIV>

          <P>
          <DIV CLASS="text-center">
            <DIV ID="progressDiv">Building report cube</DIV>
          </DIV>

          <P>&nbsp;</P>
          Koala can finish building your report in the background, and notify you when it's done. While it's working, you can work on other things.

          <P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" ID="btnAsync" onClick="location.href='/app/rpt/main'"><I CLASS="bi bi-speedometer"></I> Be More Productive</BUTTON>
          </DIV>
        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get the CGI input variables
  $dsID = $q->param('ds');
  $rptID = $q->param('rptID');
  $caller = $q->param('c');

  $dsSchema = "datasource_" . $dsID;
  $rptCube = "_rptcube_" . $rptID;

  $db = KAPutil_connect_to_database();

  $dsName = ds_id_to_name($db, $dsID);
  $rptName = report_id_to_name($db, $rptID);

  print_html_header();

  #if the cube is already being updated by another process, join the status
  #stream
  $query = "SELECT opInfo FROM app.jobs \
      WHERE cubeID=$rptID AND operation='CUBE-UPDATE'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($opInfo) = $dbOutput->fetchrow_array;

  if (($opInfo =~ m/^Update/) || ($opInfo =~ m/^Wait/))
  {
    print_status_html();
    exit;
  }


#NB: commenting this out for now, since it persists the "too large" error
#    even after the user has changed the definition of the report. If it's
#    still too big, the actual cube update process will identify that.
#    To make this work here, we really need to re-expand all of the data
#    selector scripts first. That's probably going to take too long for
#    massive and complicated reports
=pod
  #make sure the cube isn't ridiculously large
  @productIDs = datasel_get_dimension_items($db, $rptID, "p");
  @geographyIDs = datasel_get_dimension_items($db, $rptID, "g");
  @timeIDs = datasel_get_dimension_items($db, $rptID, "t");

  $numProds = scalar @productIDs;
  $numGeos = scalar @geographyIDs;
  $numTimes = scalar @timeIDs;
  $cubeRows = $numProds * $numGeos * $numTimes;

  if ($cubeRows > 5_500_000)
  {
    $db->do("UPDATE cubes SET status='ERROR:Report is too large' WHERE ID=$rptID");
    exit_error("Whoa, there! You're trying to create a report with $cubeRows rows, which is more than your current Koala cloud can handle. Please contact Koala about expanding your cloud capacity, or edit the report so it contains less items. $cubeRows = $numProds * $numGeos * $numTimes");
  }
=cut

  #if the user is already using more than their fair share of a production cloud
  $loadAvg = KAPutil_get_load_avg($db);
  if (($loadAvg >= 0.75) && ($acctType < 10))
  {
    $jobCount = KAPutil_get_user_jobs($db, $userID);

    #if the user already has more than a couple jobs running
    if ($jobCount > 1)
    {
      exit_error("Your analytics cloud is heavily loaded, and you already have at least one large job running. Wait a bit for that job to finish, and then try again.");
    }
  }

  #fork a new process to do the actual import in the background
  $SIG{CHLD} = "IGNORE";
  if ($pid = fork())
  {
    #parent process

    print_status_html();

    $activity = "$first $last building cube $rptName in $dsName";
    utils_slack($activity);

  }
  else
  {
    #child process

    #let Apache know not to wait on the child process
    close(STDIN);
    close(STDOUT);

    #redirect STDERR to the Koala error log
    close(STDERR);
    open(STDERR, ">>/opt/apache/htdocs/tmp/koala_error.log");
    select(STDERR);
    $| = 1;

    #reconnect to the database
    $db = KAPutil_connect_to_database();

    #build the cube
    cube_build($db, $dsSchema, $rptID, $userID);
  }

#EOF
