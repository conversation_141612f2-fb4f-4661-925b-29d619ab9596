#!/usr/bin/perl

use Text::CSV;

#Import WinCMS POS data for ESM-Ferolie

  #instantiate our CSV object
  $csv = Text::CSV->new( {binary => 1} );

  open(INPUT, "$ARGV[0]");
  open(OUTPUT, ">$ARGV[1]");

  #if the file is TSV, convert to CSV
  if ($ARGV[0] =~ m/\.tsv$/i)
  {

    $tempName = $ARGV[0] . "_TEMP";
    open(TEMP, ">$tempName");

    while ($line = <INPUT>)
    {
      $line =~ s/\t/\,/g;
      print TEMP $line;
    }

    close(TEMP);
    close(INPUT);

    unlink($ARGV[0]);
    rename($tempName, $ARGV[0]);

    open(INPUT, "$ARGV[0]");
  }

  #parse the primary header line
  $line = <INPUT>;
  $csv->parse($line);
  @columns = $csv->fields();

  $idx = 0;
  foreach $header (@columns)
  {
    if ($header =~ m/Measures/)
    {
      $columns[$idx] = "Products";
    }
    elsif ($header =~ m/UNITS/)
    {
      $columns[$idx] = "Unit Sales";
      $columns[$idx+1] = "Unit Sales YAGO";
    }
    elsif ($header =~ m/NSALES/)
    {
      $columns[$idx] = "Dollar Sales";
      $columns[$idx+1] = "Dollar Sales YAGO";
    }
    elsif ($header =~ m/Brand/)
    {
      $columns[$idx] = "PSEG:Brand";
    }
    elsif ($header =~ m/Segment/)
    {
      $columns[$idx] = "PSEG:Segment";
    }
    elsif ($header =~ m/Category/)
    {
      $columns[$idx] = "PSEG:Category";
    }
    $idx++;
  }

  push(@columns, "Time");
  push(@columns, "Geography");

  #output the headers
  $csv->combine(@columns);
  $line = $csv->string();
  print OUTPUT "$line\n";

  #the next line contains the fixed date info - parse it out
  $line = <INPUT>;
  $csv->parse($line);
  @columns = $csv->fields();
  $rawDate = $columns[1];	#12/28/2014 - 03/28/2015 (13)
  $rawDate =~ m/^.*? \- (.*?) \((\d+)\)/;
  $dateString = "$2 Weeks Ending $1";

  while ($line = <INPUT>)
  {

    $csv->parse($line);
    @columns = $csv->fields();

    push(@columns, $dateString);
    push(@columns, "TOTAL ALL STORES");

    $csv->combine(@columns);
    $line = $csv->string();

    print OUTPUT "$line\n";
  }
