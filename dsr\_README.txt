
Bootstrap Grid Relative Sizing

XS  SM  MD  LG  XL  Pix
--  --  --  --  --  ---
12  12  12  12  10  960
12  12  12  11  9   855
12  12  12  10  8   760
12  12  11  8   7   665
12  12  10  7   6   570
12  11  8   6   5   475
12  8   6   5   4   380
12  6   5   4   3   285


JOB CONTROL
-----------
- DS-UPDATE
  * Failsafe that detects dead updates
  * Cancel operation that halts update, rolls back

- CUBE-UPDATE
  * Add state information
  * Cancel operation that halts and clears jobs

- Ability to pause user-initiated jobs?



EXCEL EXPORT UPDATE
-------------------
- Disable selection output for multiple tables in report



AGGREGATE SUBTRACTION
---------------------
- Tree view dimension display shows add/subtract
- Support for subtraction in cube value aggregation
- Transfer structure support
- Manual data select shows added & subtracted items in list




MISC ANALYTICS
--------------
- Add DB error detection/handling to all scripts - WebUtils module?
- UI to walk users through selecting geo coding for geographies in maps
- Support for a color attribute in each dimension item, used by reporting



MISC VISUALS
--------------
- Update logout icon to match rest of UI
- Rewrite to use JSON instead of XML
- Improve PPT export file name
- Some kind of spinner/loading indication on charts & maps


MISC
-----
- Minify/obfuscate code, try HTML::Packer
- Sanitize CGI arguments
- Hash/obfuscate CGI GET arguments
- Placeholders in form inputs
