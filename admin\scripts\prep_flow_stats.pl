#!/usr/bin/perl

use lib "/opt/apache/app/";

use DBI;

use Lib::KoalaConfig;
use Lib::PrepFlows;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::PrepUtils;
use Lib::WebUtils;


#
# Output per-broker and per-user data flow counts (schedule and not).
#


  #connect to the database
  $db = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  %prepFlowNameHash = prep_flow_get_name_hash($prepDB);
  %userNameHash = utils_get_user_hash($db);
  %orgNameHash = utils_get_org_hash($db);

  #build hash of user org membership
  foreach $orgID (keys %orgNameHash)
  {
    $query = "SELECT ID FROM app.users WHERE orgID=$orgID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    while (($userID) = $dbOutput->fetchrow_array)
    {
      $userOrgHash{$userID} = $orgID;
    }
  }

  #for each user, get number of flows & number of daily/weekly scheduled flows
  foreach $userID (keys %userNameHash)
  {
    $orgID = $userOrgHash{$userID};

    $flowCount = 0;
    $flowIDstr = "";
    $query = "SELECT ID FROM prep.flows WHERE userID=$userID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    while (($flowID) = $dbOutput->fetchrow_array)
    {
      $flowCount++;
      $flowIDstr .= "$flowID,";
    }
    chop($flowIDstr);

    $userFlowCount{$userID} = $flowCount;
    $orgFlowCount{$orgID} += $flowCount;

    if (length($flowIDstr) > 0)
    {
      $query = "SELECT COUNT(*) FROM prep.schedule \
          WHERE flowID IN ($flowIDstr) AND sched IN ('daily', 'weekly')";
      $dbOutput = $prepDB->prepare($query);
      $dbOutput->execute;
      ($schedFlowCount) = $dbOutput->fetchrow_array;

      $userSchedFlowCount{$userID} = $schedFlowCount;
      $orgSchedFlowCount{$orgID} += $schedFlowCount;

      $query = "SELECT SUM(lastFileSize) FROM prep.schedule \
          WHERE flowID IN ($flowIDstr)";
      $dbOutput = $prepDB->prepare($query);
      $dbOutput->execute;
      ($schedFlowSize) = $dbOutput->fetchrow_array;

      $userSchedFlowSize{$userID} = $schedFlowSize;
      $orgSchedFlowSize{$orgID} += $schedFlowSize;

      $query = "SELECT COUNT(*) FROM prep.schedule \
          WHERE flowID IN ($flowIDstr) AND sched = 'monthly'";
      $dbOutput = $prepDB->prepare($query);
      $dbOutput->execute;
      ($schedFlowCount) = $dbOutput->fetchrow_array;

      $userSched108FlowCount{$userID} = $schedFlowCount;
      $orgSched108FlowCount{$orgID} += $schedFlowCount;
    }
  }

  print("\n\n----------------------------------------------\n\n");

  print("Organization,Total Flows,Scheduled Update Flows,Scheduled 108 Week Flows,Scheduled Flow Size (GB)\n");
  foreach $orgID (keys %orgFlowCount)
  {
    if ($orgSchedFlowCount{$orgID} < 1)
    {
      next;
    }

    $orgSchedFlowSize{$orgID} /= 1_000_000_000;
    $orgSchedFlowSize{$orgID} = sprintf("%.2f", $orgSchedFlowSize{$orgID});
    print("$orgNameHash{$orgID},$orgFlowCount{$orgID},$orgSchedFlowCount{$orgID},$orgSched108FlowCount{$orgID},$orgSchedFlowSize{$orgID}\n");
  }

  print("\n\n----------------------------------------------\n\n");

  print("User,Organization,Total Flows,Scheduled Update Flows,Scheduled 108 Week Flows,Scheduled Flow Size (GB)\n");
  foreach $userID (keys %userFlowCount)
  {
    if ($userFlowCount{$userID} < 1)
    {
      next;
    }

    $userSchedFlowSize{$userID} /= 1_000_000_000;
    $userSchedFlowSize{$userID} = sprintf("%.2f", $userSchedFlowSize{$userID});

    $orgID = $userOrgHash{$userID};
    $orgName = $orgNameHash{$orgID};
    print("$userNameHash{$userID},$orgName,$userFlowCount{$userID},$userSchedFlowCount{$userID},$userSched108FlowCount{$userID},$userSchedFlowSize{$userID}\n");
  }


#EOF
