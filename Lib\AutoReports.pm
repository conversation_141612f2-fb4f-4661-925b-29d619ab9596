
package Lib::AutoReports;

use lib "/opt/apache/app/";

use Exporter;
use Lib::BuildCube;
use Lib::Cubes;
use Lib::DataSel;
use Lib::DSRMeasures;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::Reports;

our @ISA = ('Exporter');

our @EXPORT = qw(
    &autorpt_create_seghier_brand_size
    &autorpt_find_meas_dollars
    &autorpt_create_meas_avg_unit_price
    &autorpt_prereqs_catov_topline_brands
    &autorpt_prereqs_catov_topline_retailers
    &autorpt_prereqs_catov_brand_rank
    &autorpt_prereqs_catov_item_rank
    &autorpt_prereqs_catov_rtlr_distro_trends
    &autorpt_prereqs_catov_item_distro_trends
    &autorpt_prereqs_catov_retailer_acv_comp
    &autorpt_prereqs_catov_change_distro
    &autorpt_prereqs_catov_price_tracker
    &autorpt_prereqs_catov_units_sold_price
    &autorpt_prereqs_catov_promo_tracker_dollars
    &autorpt_prereqs_catov_promo_tracker_units
    &autorpt_prereqs_catov_lift_vs_discount
    &autorpt_prereqs_tlbr_overview
    &autorpt_prereqs_tlbr_total_bus_crosscat
    &autorpt_prereqs_tlbr_category_snapshot
    &autorpt_prereqs_tlbr_retailer_item_trends
    &autorpt_prereqs_tlbr_retailer_brand_trends
    &autorpt_prereqs_tlbr_prod_trends
    &autorpt_prereqs_bldr_base_vs_incr
    &autorpt_prereqs_bldr_cause_base_change
    &autorpt_prereqs_bldr_distribution
    &autorpt_prereqs_bldr_everyday_price
    &autorpt_prereqs_bldr_everyday_price_vs_comp
    &autorpt_prereqs_bldr_base_price_retailers
    &autorpt_prereqs_incdr_incr_vs_base
    &autorpt_prereqs_incdr_cause_incr_change
    &autorpt_prereqs_incdr_incr_change_details
    &autorpt_prereqs_incdr_promo_freq
    &autorpt_prereqs_incdr_promo_eff
    &autorpt_prereqs_incdr_promo_discount
    &autorpt_prereqs_incdr_share_promos
    &autorpt_prereqs_incdr_promo_pricing
    &autorpt_create_story);



=pod
Inventory:

autorpt_find_all

autorpt_find_seg_brand
autorpt_find_seg_category
autorpt_find_seg_manufacturer
autorpt_find_prod_seg_size
autorpt_find_prods_nth_largest_segment
autorpt_find_seghier_brand_size
autorpt_find_seghier_manufacturer_brand
autorpt_create_seghier_manufacturer_brand
autorpt_create_seghier_brand_size

autorpt_find_geo_retailers
autorpt_find_geo_total_us

autorpt_find_recent_52_weeks
autorpt_find_recent_n_cum_weeks
autorpt_create_recent_n_cum_weeks

autorpt_meas_format_currency
autorpt_meas_format_percent
autorpt_meas_format_negatives
autorpt_meas_format_decimals

autorpt_find_meas_acv_reach
autorpt_find_meas_acv_reach_cya
autorpt_find_meas_acv_reach_pctchg_ya
autorpt_find_meas_acv_reach_ya
autorpt_find_meas_any_promo_dollars
autorpt_find_meas_any_promo_disc
autorpt_find_meas_any_promo_incr_dollars
autorpt_find_meas_any_promo_incr_dollars_ya
autorpt_find_meas_any_promo_units
autorpt_find_meas_any_promo_unit_price
autorpt_find_meas_avg_num_items
autorpt_find_meas_avg_num_items_cya
autorpt_find_meas_avg_num_items_pctchg_ya
autorpt_find_meas_avg_unit_price
autorpt_find_meas_avg_unit_price_pctchg_ya
autorpt_find_meas_avg_unit_price_ya
autorpt_find_meas_base_dollars
autorpt_find_meas_base_dollars_ya
autorpt_find_meas_base_unit_price
autorpt_find_meas_base_unit_price_pctchg_ya
autorpt_find_meas_base_units
autorpt_find_meas_dollars
autorpt_find_meas_dollars_ya
autorpt_find_meas_dollars_cya
autorpt_find_meas_dollar_pctchg_ya
autorpt_find_meas_dollar_pct_lift
autorpt_find_meas_dollar_change_base_velocity_change
autorpt_find_meas_dollar_change_tdp_change
autorpt_find_meas_dollar_shr_category
autorpt_find_meas_dollar_shr_category_ya
autorpt_find_meas_dollar_shr_category_cya
autorpt_find_meas_incr_dollars
autorpt_find_meas_incr_dollars_cya
autorpt_find_meas_incr_dollars_ya
autorpt_find_meas_incr_dollars_shr_cat
autorpt_find_meas_incr_dollars_shr_cat_ya
autorpt_find_meas_incr_dollars_shr_cat_cya
autorpt_find_meas_incr_units
autorpt_find_meas_pct_dollars_any_promo
autorpt_find_meas_pct_dollars_base
autorpt_find_meas_pct_dollars_incr
autorpt_find_meas_promo_vehicle_base_dollars
autorpt_find_meas_promo_vehicle_base_units
autorpt_find_meas_promo_vehicle_cww
autorpt_find_meas_promo_vehicle_cww_cya
autorpt_find_meas_promo_vehicle_dollar_pct_lift
autorpt_find_meas_promo_vehicle_dollar_pct_lift_cya
autorpt_find_meas_promo_vehicle_dollars
autorpt_find_meas_promo_vehicle_incr_dollars
autorpt_find_meas_promo_vehicle_incr_dollars_cya
autorpt_find_meas_promo_vehicle_incr_dollars_ya
autorpt_find_meas_promo_vehicle_incr_dollars_shr_cat
autorpt_find_meas_promo_vehicle_incr_dollars_shr_cat_cya
autorpt_find_meas_promo_vehicle_incr_dollars_shr_cat_ya
autorpt_find_meas_promo_vehicle_unit_price
autorpt_find_meas_promo_vehicle_unit_price_cya
autorpt_find_meas_promo_vehicle_unit_price_pct_disc
autorpt_find_meas_promo_vehicle_unit_price_pct_disc_cya
autorpt_find_meas_promo_vehicle_units
autorpt_find_meas_tdp
autorpt_find_meas_tdp_cya
autorpt_find_meas_tdp_ya
autorpt_find_meas_units
autorpt_find_meas_units_ya
autorpt_find_meas_units_cya
autorpt_find_meas_units_pctchg_ya
autorpt_find_meas_units_shr_category
autorpt_find_meas_units_shr_category_ya
autorpt_find_meas_units_shr_category_cya

autorpt_create_meas_acv_reach_cya
autorpt_create_meas_acv_reach_pctchg_ya
autorpt_create_meas_any_promo_dollars
autorpt_create_meas_any_promo_disc
autorpt_create_meas_any_promo_incr_dollars
autorpt_create_meas_any_promo_incr_dollars_ya
autorpt_create_meas_any_promo_units
autorpt_create_meas_avg_num_items
autorpt_create_meas_avg_num_items_cya
autorpt_create_meas_avg_num_items_pctchg_ya
autorpt_create_meas_avg_num_items_ya
autorpt_create_meas_avg_unit_price
autorpt_create_meas_avg_unit_price_pctchg_ya
autorpt_create_meas_avg_unit_price_ya
autorpt_create_meas_base_dollars_ya
autorpt_create_meas_base_unit_price
autorpt_create_meas_base_unit_price_pctchg_ya
autorpt_create_meas_promo_vehicle_incr_dollars_shr_cat
autorpt_create_meas_dollars_cya
autorpt_create_meas_dollar_pctchg_ya
autorpt_create_meas_dollar_pct_lift
autorpt_create_meas_dollar_change_base_velocity_change
autorpt_create_meas_dollar_change_tdp_change
autorpt_create_meas_dollar_shr_cat
autorpt_create_meas_dollar_shr_cat_ya
autorpt_create_meas_dollar_shr_cat_cya
autorpt_create_meas_incr_dollars
autorpt_create_meas_incr_dollars_cya
autorpt_create_meas_incr_dollars_ya
autorpt_create_meas_incr_dollars_shr_cat
autorpt_create_meas_incr_dollars_shr_cat_ya
autorpt_create_meas_incr_dollars_shr_cat_cya
autorpt_create_meas_incr_units
autorpt_create_meas_pct_dollars_any_promo
autorpt_create_meas_pct_dollars_base
autorpt_create_meas_pct_dollars_incr
autorpt_create_meas_promo_vehicle_cww_cya
autorpt_create_meas_promo_vehicle_dollar_pct_lift_cya
autorpt_create_meas_promo_vehicle_incr_dollars
autorpt_create_meas_promo_vehicle_incr_dollars_cya
autorpt_create_meas_promo_vehicle_incr_dollars_ya
autorpt_create_meas_promo_vehicle_incr_dollars_shr_cat
autorpt_create_meas_promo_vehicle_incr_dollars_shr_cat_cya
autorpt_create_meas_promo_vehicle_incr_dollars_shr_cat_ya
autorpt_create_meas_promo_vehicle_unit_price_cya
autorpt_create_meas_promo_vehicle_unit_price_pct_disc_cya
autorpt_create_meas_tdp
autorpt_create_meas_tdp_cya
autorpt_create_meas_tdp_ya
autorpt_create_meas_units_cya
autorpt_create_meas_units_pctchg_ya
autorpt_create_meas_units_shr_cat
autorpt_create_meas_units_shr_cat_ya
autorpt_create_meas_units_shr_cat_cya

autorpt_prereqs_meas_acv_reach
autorpt_prereqs_meas_acv_reach_cya
autorpt_prereqs_meas_any_promo_dollars
autorpt_prereqs_meas_any_promo_incr_dollars
autorpt_prereqs_meas_any_promo_units
autorpt_prereqs_meas_avg_unit_price
autorpt_prereqs_meas_avg_unit_price_pctchg_ya
autorpt_prereqs_meas_base_dollars
autorpt_prereqs_meas_base_units
autorpt_prereqs_meas_base_unit_price
autorpt_prereqs_meas_base_unit_price_pctchg_ya
autorpt_prereqs_meas_dollars
autorpt_prereqs_meas_dollars_cya
autorpt_prereqs_meas_dollar_pctchg_ya
autorpt_prereqs_meas_dollar_pct_lift
autorpt_prereqs_meas_dollar_shr_category
autorpt_prereqs_meas_dollar_shr_category_cya
autorpt_prereqs_meas_incr_dollars
autorpt_prereqs_meas_incr_dollars_ya
autorpt_prereqs_meas_incr_dollars_shr_category
autorpt_prereqs_meas_incr_units
autorpt_prereqs_meas_incr_units_ya
autorpt_prereqs_meas_incr_units_shr_category
autorpt_prereqs_meas_promo_vehicle_dollars
autorpt_prereqs_meas_promo_vehicle_cww
autorpt_prereqs_meas_promo_vehicle_dollar_pct_lift
autorpt_prereqs_meas_promo_vehicle_incr_dollars_cya
autorpt_prereqs_meas_promo_vehicle_incr_dollars_shr_cat
autorpt_prereqs_meas_promo_vehicle_unit_price_pct_disc
autorpt_prereqs_meas_promo_vehicle_units
autorpt_prereqs_meas_tdp
autorpt_prereqs_meas_units
autorpt_prereqs_meas_units_cya
autorpt_prereqs_meas_units_pctchg_ya
autorpt_prereqs_meas_units_shr_category
autorpt_prereqs_meas_units_shr_category_cya

autorpt_select_n_items
autorpt_select_all_items

autorpt_select_prod_n_segments

autorpt_select_time_52_weeks
autorpt_select_time_n_cum_weeks

autorpt_select_meas_by_name

=cut



our $TELEMETRY = 0;

our %promoNameMapping = (
    "disp wo feat" => "Disp w/o Feat",
    "feat and disp" => "Feat & Disp",
    "feat wo disp" => "Feat w/o Disp",
    "price decr" => "Price Decr",
);



#-------------------------------------------------------------------------
#
# Output telemetry data, if enabled
#

sub DBG
{
  my ($str) = @_;

  if ($TELEMETRY == 1)
  {
    print STDERR "$str\n";
  }
}



#-------------------------------------------------------------------------
#
# Handle a database error of some kind during autoreport generation
#

sub autorpt_db_err
{
  my ($date);

  my ($db, $status, $text) = @_;

  if (!defined($status))
  {
    $date = localtime();
    print STDERR "$date: $text\n";
    if ($db->errstr =~ m/^MySQL server has gone away/)
    {
      die("Lost connection to database, terminating");
    }
  }
}



#-------------------------------------------------------------------------
#
# Handle a database error of some kind during autoreport generation
#

sub autorpt_uniquify_prereqs
{
  my (@prereqs);
  my (%prereqsHash);

  my ($prereqHTML) = @_;

  @prereqs = split('\n', $prereqHTML);
  $prereqsHash{$_}++ for (@prereqs);

  $prereqHTML = "";
  foreach $prereq (sort keys %prereqsHash)
  {
    $prereqHTML .= "$prereq\n";
  }

  return($prereqHTML);
}



 #-------------------------------------------------------------------------
 #
 # Find all items for the specified dimension
 #

 sub autorpt_find_all
 {
   my ($scriptlet, $query, $dbOutput, $status, $itemID, $itemStr, $dbStub);
   my ($dsSchema);

   my ($db, $dsID, $dim, $noScript) = @_;

   #if we're sending back a scriptlet, keep it simple
   if ($noScript < 1)
   {
     $scriptlet = "M:ALL";
     return($scriptlet);
   }

   $dsSchema = "datasource_" . $dsID;

   #get the database table name for the selected dimension
   if ($dim eq "p")
   {
     $dbStub = "products";
   }
   elsif ($dim eq "g")
   {
     $dbStub = "geographies";
   }
   elsif ($dim eq "t")
   {
     $dbStub = "timeperiods";
   }
   elsif ($dim eq "m")
   {
     $dbStub = "measures";
   }

   $query = "SELECT ID FROM $dsSchema.$dbStub ORDER BY name";
   $dbOutput = $db->prepare($query);
   $status = $dbOutput->execute;
   autorpt_db_err($db, $status, $query);
   while (($itemID) = $dbOutput->fetchrow_array)
   {
     $itemStr .= $itemStr . "$itemID,";
   }
   chop($itemStr);

   return($itemStr);
 }



###############################################################################
#
#
#
#
#                                 PRODUCTS
#
#
#
#
###############################################################################



#-------------------------------------------------------------------------
#
# Automatically find the best segmentation to use for brand info
#

sub autorpt_find_seg_brand
{
  my ($brandSegID, $segID, $scriptlet, $segName);
  my (%segHash);

  my ($db, $dsID, $noScript) = @_;

  #get a hash of all segmentation names and IDs
  %segHash = DSRsegmentation_get_segmentations_hash($db, $dsID, "p");
  %segHash = reverse(%segHash);

  #hunt through the available segmentations, looking for the "best" one that
  #probably contains brand info
  $brandSegID = $segHash{'BRAND HIGH'};

  if ($brandSegID < 1)
  {
    $brandSegID = $segHash{'BRAND LOW'};
  }
  if ($brandSegID < 1)
  {
    $brandSegID = $segHash{'BRAND OWNER HIGH'};
  }
  if ($brandSegID < 1)
  {
    $brandSegID = $segHash{'BRAND OWNER LOW'};
  }

  #if we didn't find a common brand segmentation, take anything with "brand"
  #in the name
  if ($brandSegID < 1)
  {
    foreach $segName (keys %segHash)
    {
      if ($segName =~ m/brand/i)
      {
        $brandSegID = $segHash{$segName};
      }
    }
  }

  $scriptlet = "M:SEG_$brandSegID";
  if ($noScript > 0)
  {
    return($brandSegID);
  }
  else
  {
    return($scriptlet);
  }
}



#-------------------------------------------------------------------------
#
# Automatically find the best segmentation to use for size info
#

sub autorpt_find_prod_seg_size
{
  my ($sizeSegID, $scriptlet);
  my (%segHash);

  my ($db, $dsID, $noScript) = @_;

  #get a hash of all segmentation names and IDs
  %segHash = DSRsegmentation_get_segmentations_hash($db, $dsID, "p");
  %segHash = reverse(%segHash);

  #hunt through the available segmentations, looking for the "best" one that
  #probably contains size info
  $sizeSegID = $segHash{'BASE SIZE'};

  if ($sizeSegID < 1)
  {
    $sizeSegID = $segHash{'SIZE'};
  }
  if ($sizeSegID < 1)
  {
    $sizeSegID = $segHash{'PACK SIZE'};
  }

  $scriptlet = "M:SEG_$sizeSegID";
  if ($noScript > 0)
  {
    return($sizeSegID);
  }
  else
  {
    return($scriptlet);
  }
}



#-------------------------------------------------------------------------
#
# Automatically find the best segmentation to use for category info
#

sub autorpt_find_seg_category
{
  my ($catSegID, $segName, $scriptlet);
  my (%segHash);

  my ($db, $dsID, $noScript) = @_;

  #get a hash of all segmentation names and IDs
  %segHash = DSRsegmentation_get_segmentations_hash($db, $dsID, "p");
  %segHash = reverse(%segHash);

  #hunt through the available segmentations, looking for the "best" one that
  #probably contains category info
  $catSegID = $segHash{'BC Category'};

  if ($catSegID < 1)
  {
    $catSegID = $segHash{'Category'};
  }
  if ($catSegID < 1)
  {
    $catSegID = $segHash{'Type'};
  }
  if ($catSegID < 1)
  {
    $catSegID = $segHash{'Type Level'};
  }

  #if we didn't find a common category segmentation, take anything with
  #"category" or "type" in the name
  if ($catSegID < 1)
  {
    foreach $segName (keys %segHash)
    {
      if ($segName =~ m/category/i)
      {
        $catSegID = $segHash{$segName};
      }
    }
  }
  if ($catSegID < 1)
  {
    foreach $segName (keys %segHash)
    {
      if ($segName =~ m/type/i)
      {
        $catSegID = $segHash{$segName};
      }
    }
  }

  $scriptlet = "M:SEG_$catSegID";
  if ($noScript > 0)
  {
    return($catSegID);
  }
  else
  {
    return($scriptlet);
  }
}



#-------------------------------------------------------------------------
#
# Automatically find the best segmentation to use for manufacturer info
#

sub autorpt_find_seg_manufacturer
{
  my ($manufacturerSegID, $segName, $scriptlet);
  my (%segHash);

  my ($db, $dsID, $noScript) = @_;

  #get a hash of all segmentation names and IDs
  %segHash = DSRsegmentation_get_segmentations_hash($db, $dsID, "p");
  %segHash = reverse(%segHash);

  #hunt through the available segmentations, looking for the "best" one that
  #probably contains category info
  $manufacturerSegID = $segHash{'Brand Owner High'};

  if ($manufacturerSegID < 1)
  {
    $manufacturerSegID = $segHash{'Brand Owner Low'};
  }
  if ($manufacturerSegID < 1)
  {
    $manufacturerSegID = $segHash{'Vendor Level'};
  }
  if ($manufacturerSegID < 1)
  {
    $manufacturerSegID = $segHash{'Brand Owner'};
  }

  #if we didn't find a common category segmentation, take anything with
  #"brand owner" or "vendor" in the name
  if ($manufacturerSegID < 1)
  {
    foreach $segName (keys %segHash)
    {
      if ($segName =~ m/brand owner/i)
      {
        $manufacturerSegID = $segHash{$segName};
      }
    }
  }
  if ($manufacturerSegID < 1)
  {
    foreach $segName (keys %segHash)
    {
      if ($segName =~ m/vendor/i)
      {
        $manufacturerSegID = $segHash{$segName};
      }
    }
  }

  $scriptlet = "M:SEG_$manufacturerSegID";
  if ($noScript > 0)
  {
    return($manufacturerSegID);
  }
  else
  {
    return($scriptlet);
  }
}



#-------------------------------------------------------------------------
#
# Find the products that are members of the nth largest segment in the
# specified segmentation.
#

sub autorpt_find_prods_nth_largest_segment
{
  my ($query, $dbOutput, $status, $nth, $segID, $privateSegStr, $whereClause);
  my ($segmentID, $itemStr, $scriptlet, $dsSchema, $itemID);
  my (%segmentNameHash);

  my ($db, $dsID, $segType, $nth, $noScript) = @_;

  $dsSchema = "datasource_" . $dsID;

  $nth--;  #SQL LIMIT is 0-based in MySQL

  #figure out which segmentation we're selecting from
  if ($segType eq "brand")
  {
    $segID = autorpt_find_seg_brand($db, $dsID, 1);
  }
  elsif ($segType eq "category")
  {
    $segID = autorpt_find_seg_category($db, $dsID, 1);
  }
  elsif ($segType =~ m/^\d+$/)
  {
    $segID = $segType;
  }
  else
  {
    return;
    #XXX maybe select any segment from any segmentation instead?
  }

  #ignore private label and store brands
  %segmentNameHash = DSRseg_get_segments_hash($db, $dsSchema, "p", $segID);
  %segmentNameHash = reverse(%segmentNameHash);
  if ($segmentNameHash{'PRIVATE LABEL'} > 0)
  {
    $privateSegStr .= "$segmentNameHash{'PRIVATE LABEL'},";
  }
  if ($segmentNameHash{'STORE BRAND'} > 0)
  {
    $privateSegStr .= "$segmentNameHash{'STORE BRAND'},";
  }
  if ($segmentNameHash{'NO BRAND LISTED'} > 0)
  {
    $privateSegStr .= "$segmentNameHash{'NO BRAND LISTED'},";
  }
  chop($privateSegStr);
  if (length($privateSegStr) > 0)
  {
    $whereClause = "AND segmentID NOT IN ($privateSegStr)";
  }

  #find the segment with the most items in the specified segmentation
  $query = "SELECT segmentID, COUNT(*) AS count \
      FROM $dsSchema.product_segment_item \
      WHERE segmentationID=$segID $whereClause \
      GROUP BY segmentID ORDER BY count DESC LIMIT $nth, 1";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  autorpt_db_err($db, $status, $query);
  ($segmentID) = $dbOutput->fetchrow_array;

  if ($noScript > 0)
  {
    $itemStr = "";
    $query = "SELECT itemID FROM $dsSchema.product_segment_item \
        WHERE segmentID=$segmentID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    autorpt_db_err($db, $status, $query);
    while (($itemID) = $dbOutput->fetchrow_array)
    {
      $itemStr .= "$itemID,";
    }
    chop($itemStr);
    return($segmentID);
  }
  else
  {
    $scriptlet = "H:SEG_$segID $segmentID";
    return($scriptlet);
  }
}



#-------------------------------------------------------------------------
#
# Search for an existing manufacturer-brand segmentation hierarchy.
# NB: The selection script returned by this function selects all hierarchy
#     items at the brand level. There are separate functions to make
#     alternate selections.
#

sub autorpt_find_seghier_manufacturer_brand
{
  my ($query, $dbOutput, $status, $manufacturerSegID, $brandSegID, $segHierID);
  my ($dsSchema, $segmentStr, $scriptlet, $segmentID, $name);

  my ($db, $dsID, $noScript) = @_;

  $dsSchema = "datasource_" . $dsID;

  #since the name can be just about anything, we're going to look solely based
  #on the construction of the hierarchy (manufacturer-brand)
  $manufacturerSegID = autorpt_find_seg_manufacturer($db, $dsID, 1);
  $brandSegID = autorpt_find_seg_brand($db, $dsID, 1);

  $query = "SELECT ID FROM $dsSchema.product_seghierarchy \
      WHERE segmentations='$manufacturerSegID,$brandSegID'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  autorpt_db_err($db, $status, $query);
  ($segHierID) = $dbOutput->fetchrow_array;
  if ($segHierID < 1)
  {
    return("");
  }

  #get all of the segment IDs for brand segmentation for sel script use
  $segmentStr = "";
  $query = "SELECT ID, name FROM $dsSchema.product_segment \
      WHERE segmentationID=$brandSegID ORDER BY name";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  autorpt_db_err($db, $status, $query);
  while (($segmentID, $name) = $dbOutput->fetchrow_array)
  {
    $segmentStr .= "$segmentID ";
  }
  chop($segmentStr);

  $scriptlet = "SH:SHS_$segHierID $brandSegID $brandSegID $segmentStr";
  if ($noScript > 0)
  {
    return($segHierID);
  }
  else
  {
    return($scriptlet);
  }
}



#-------------------------------------------------------------------------
#
# If there isn't already a manufacturer-brand hierarchy in the data source,
# create one. Return the selection script for the hierarchy.
#

sub autorpt_create_seghier_manufacturer_brand
{
  my ($dsSchema, $query, $status, $scriptlet, $manufacturerSegID, $brandSegID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if we already have a manufacturer-brand hierarchy
  $scriptlet = autorpt_find_seghier_manufacturer_brand($db, $dsID);

  #if we don't, create one
  if (length($scriptlet) < 1)
  {
    $manufacturerSegID = autorpt_find_seg_manufacturer($db, $dsID, 1);
    $brandSegID = autorpt_find_seg_brand($db, $dsID, 1);

    $query = "INSERT INTO $dsSchema.product_seghierarchy \
        (name, segmentations, namePattern) \
        VALUES ('Manufacturer-Brand', '$manufacturerSegID,$brandSegID', '1:1,2:1_2,')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    #cheat a little and use the find function to produce a script so we don't
    #have to duplicate that code here
    $scriptlet = autorpt_find_seghier_manufacturer_brand($db, $dsID);
  }

  return($scriptlet);
}



#-------------------------------------------------------------------------
#
# Search for an existing brand-size segmentation hierarchy.
# NB: The selection script returned by this function selects all hierarchy
#     items at the size level. There are separate functions to make
#     alternate selections.
#

sub autorpt_find_seghier_brand_size
{
  my ($query, $dbOutput, $status, $sizeSegID, $brandSegID, $segHierID);
  my ($dsSchema, $segmentStr, $scriptlet, $segmentID, $name);

  my ($db, $dsID, $noScript) = @_;

  $dsSchema = "datasource_" . $dsID;

  #since the name can be just about anything, we're going to look solely based
  #on the construction of the hierarchy (manufacturer-brand)
  $sizeSegID = autorpt_find_prod_seg_size($db, $dsID, 1);
  $brandSegID = autorpt_find_seg_brand($db, $dsID, 1);

  $query = "SELECT ID FROM $dsSchema.product_seghierarchy \
      WHERE segmentations='$brandSegID,$sizeSegID'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  autorpt_db_err($db, $status, $query);
  ($segHierID) = $dbOutput->fetchrow_array;
  if ($segHierID < 1)
  {
    return("");
  }

  #get all of the segment IDs for brand segmentation for sel script use
  $segmentStr = "";
  $query = "SELECT ID,name FROM $dsSchema.product_segment \
      WHERE segmentationID=$sizeSegID ORDER BY name";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  autorpt_db_err($db, $status, $query);
  while (($segmentID, $name) = $dbOutput->fetchrow_array)
  {
    $segmentStr .= "$segmentID ";
  }
  chop($segmentStr);

  $scriptlet = "SH:SHS_$segHierID $brandSegID $brandSegID $segmentStr";
  if ($noScript > 0)
  {
    return($segHierID);
  }
  else
  {
    return($scriptlet);
  }
}



#-------------------------------------------------------------------------
#
# If there isn't already a brand-size hierarchy in the data source,
# create one. Return the selection script for the hierarchy.
#

sub autorpt_create_seghier_brand_size
{
  my ($dsSchema, $query, $status, $scriptlet, $sizeSegID, $brandSegID);

  my ($db, $dsID, $noScript) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if we already have a brand-size hierarchy
  $scriptlet = autorpt_find_seghier_brand_size($db, $dsID, $noScript);

  #if we don't, create one
  if (length($scriptlet) < 1)
  {
    $sizeSegID = autorpt_find_prod_seg_size($db, $dsID, 1);
    $brandSegID = autorpt_find_seg_brand($db, $dsID, 1);

    $query = "INSERT INTO $dsSchema.product_seghierarchy \
        (name, segmentations, namePattern) \
        VALUES ('Brand-Size', '$brandSegID,$sizeSegID', '1:1,2:1_2,')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    #cheat a little and use the find function to produce a script so we don't
    #have to duplicate that code here
    $scriptlet = autorpt_find_seghier_brand_size($db, $dsID, $noScript);
  }

  return($scriptlet);
}








###############################################################################
#
#
#
#
#                               GEOGRAPHIES
#
#
#
#
###############################################################################



#-------------------------------------------------------------------------
#
# Find and return a list of retailer geographies in the specified data source
#

sub autorpt_find_geo_retailers
{
  my ($query, $dbOutput, $status, $dsSchema, $retailersStr, $listID);
  my ($scriptlet, $geoName, $count);
  my (%geoNameHash);

  my ($db, $dsID) = @_;

  $retailersStr = "";
  $dsSchema = "datasource_" . $dsID;

  %geoNameHash = dsr_get_item_name_hash($db, $dsID, "g");
  %geoNameHash = reverse(%geoNameHash);

  #look for a geography list containing the word "retailers"
  $query = "SELECT ID FROM $dsSchema.geography_list \
      WHERE name LIKE '%retailer%'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  autorpt_db_err($db, $status, $query);
  ($listID) = $dbOutput->fetchrow_array;
  if ($listID > 0)
  {
    $scriptlet = "M:LIS_$listID";
    return($scriptlet);
  }

  #if there are geos that end in " TA" or variants, use a match selection
  $query = "SELECT ID FROM $dsSchema.geographies WHERE name LIKE '% TA'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  autorpt_db_err($db, $status, $query);
  if ($status > 0)
  {
    $scriptlet = "MATCH:end TA";
    return($scriptlet);
  }
  $query = "SELECT ID FROM $dsSchema.geographies WHERE name LIKE '% CTA%'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  autorpt_db_err($db, $status, $query);
  if ($status > 0)
  {
    $scriptlet = "MATCH:contain CTA";
    return($scriptlet);
  }
  $query = "SELECT ID FROM $dsSchema.geographies \
      WHERE name LIKE '% TRADING AREA%'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  autorpt_db_err($db, $status, $query);
  if ($status > 0)
  {
    $scriptlet = "MATCH:contain TRADING AREA";
    return($scriptlet);
  }

  #if we didn't find any that end in TA... screw it, use the first 10
  if (length($retailersStr) < 1)
  {
    $count = 0;
    foreach $geoName (sort keys %geoNameHash)
    {
      if ($count < 10)
      {
        $scriptlet .= "M:$geoNameHash{$geoName},";
        $count++;
      }
    }
  }
  chop($scriptlet);

  return($scriptlet);
}



#-------------------------------------------------------------------------
#
# Find and return a Total US geography in the specified data source
#

sub autorpt_find_geo_total_us
{
  my ($query, $dbOutput, $status, $dsSchema, $geoID, $scriptlet);
  my (%geoNameHash);

  my ($db, $dsID) = @_;

  $geoID = "";
  $dsSchema = "datasource_" . $dsID;

  %geoNameHash = dsr_get_item_name_hash($db, $dsID, "g");
  %geoNameHash = reverse(%geoNameHash);

  #look for common Nielsen and IRI names
  $geoID = $geoNameHash{'Total US xAOC'};

  #try to find anything that says "total us" in it
  if ($geoID < 1)
  {
    $query = "SELECT ID FROM $dsSchema.geographies \
        WHERE name LIKE '%total us%'";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    autorpt_db_err($db, $status, $query);
    if ($status > 0)
    {
      ($geoID) = $dbOutput->fetchrow_array;
    }
  }

  $scriptlet = "M:$geoID";

  return($scriptlet);
}







###############################################################################
#
#
#
#
#                              TIME PERIODS
#
#
#
#
###############################################################################



#-------------------------------------------------------------------------
#
# Find and return a datasel script that expands to the most recent 52 one
# week time periods.
#

sub autorpt_find_recent_52_weeks
{
  my ($query, $dbOutput, $status, $timeStr, $timeID, $dsSchema, $scriptlet);

  my ($db, $dsID, $noScript) = @_;

  $dsSchema = "datasource_" . $dsID;

  #if we need to send back a list of 52 1 week time periods
  if ($noScript > 0)
  {
    $query = "SELECT ID FROM $dsSchema.timeperiods \
        WHERE type=30 AND duration=1 ORDER BY endDate DESC LIMIT 52";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    autorpt_db_err($db, $status, $query);
    while (($timeID) = $dbOutput->fetchrow_array)
    {
      $timeStr .= "$timeID,";
    }
    chop($timeStr);
    return($timeStr);
  }

  #for now, we're going to cheat and just wish real hard that there are 52
  #one-week time periods in the data source
  $scriptlet = "RECENT:52 1 30 now";

  return($scriptlet);
}



#-------------------------------------------------------------------------
#
# Find and return a datasel script that expands to the most recent n one
# week time periods.
#

sub autorpt_find_recent_n_weeks
{
  my ($query, $dbOutput, $status, $timeID, $timeStr, $scriptlet, $dsSchema);

  my ($db, $dsID, $numWeeks, $noScript) = @_;

  $dsSchema = "datasource_" . $dsID;

  #if we need to send back a list of 52 1 week time periods
  if ($noScript > 0)
  {
    $query = "SELECT ID FROM $dsSchema.timeperiods \
        WHERE type=30 AND duration=1 ORDER BY endDate DESC LIMIT $numWeeks";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    autorpt_db_err($db, $status, $query);
    while (($timeID) = $dbOutput->fetchrow_array)
    {
      $timeStr .= "$timeID,";
    }
    chop($timeStr);
    return($timeStr);
  }

  #for now, we're going to cheat and just wish real hard that there are 52
  #one-week time periods in the data source
  $scriptlet = "RECENT:$numWeeks 1 30 now";

  return($scriptlet);
}



#-------------------------------------------------------------------------
#
# Find and return the most recent cumulative week time period with the specified
# number of weeks. If there isn't a base time period, look for agg.
#

sub autorpt_find_recent_n_cum_weeks
{
  my ($query, $dbOutput, $status, $timeID, $dsSchema, $scriptlet);

  my ($db, $dsID, $weeks, $noScript) = @_;

  $dsSchema = "datasource_" . $dsID;

  #look for most recent n week base time period
  $query = "SELECT ID FROM $dsSchema.timeperiods \
      WHERE type=30 AND duration=$weeks ORDER BY endDate DESC LIMIT 1";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  autorpt_db_err($db, $status, $query);
  ($timeID) = $dbOutput->fetchrow_array;
  if ($timeID > 0)
  {
    $scriptlet = "RECENT:1 $weeks 30 now";
  }

  #if no luck, try for an aggregate of n 1-week time periods
  if ($timeID < 1)
  {
    $query = "SELECT ID FROM $dsSchema.time_aggregate \
        WHERE addScript='RECENT:$weeks 1 30 now'";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    autorpt_db_err($db, $status, $query);
    ($timeID) = $dbOutput->fetchrow_array;
    if ($timeID > 0)
    {
      $timeID = "AGG_$timeID";
      $scriptlet = "M:$timeID";
    }
  }

  if ($noScript > 0)
  {
    return($timeID);
  }
  else
  {
    return($scriptlet);
  }
}



#-------------------------------------------------------------------------
#
# If there isn't already something in the data source that resembles a
# n-week cumulative time period, create one. Return the ID of the newly
# created n-week item.
#

sub autorpt_create_recent_n_cum_weeks
{
  my ($query, $status, $dsSchema, $scriptlet, $timeID, $timeCount);

  my ($db, $dsID, $weeks) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if we already have a 52 week cumulative time period
  $scriptlet = autorpt_find_recent_n_cum_weeks($db, $dsID, $weeks);

  #if we don't, try creating one
  if (length($scriptlet) < 1)
  {

    #see if we have enough weekly data to build a matching aggreggate
    $query = "SELECT COUNT(ID) FROM $dsSchema.timeperiods WHERE \
        type=30 AND duration=1";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    autorpt_db_err($db, $status, $query);
    ($timeCount) = $dbOutput->fetchrow_array;

    #if there aren't enough weekly time periods in the DS, return nothing
    if ($timeCount < $weeks)
    {
      return;
    }

    #if there's enough weekly time periods in the DS, build an aggregate
    $query = "INSERT INTO $dsSchema.time_aggregate \
        (name, addScript, appendEndDate) \
        VALUES ('$weeks W/E ', 'RECENT:$weeks 1 30 now', 1)";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $timeID = $db->{q{mysql_insertid}};
    datasel_expand_script($db, $dsSchema, $timeID, "t", "a");
    $scriptlet = "M:AGG_$timeID";
  }

  return($scriptlet);
}








###############################################################################
#
#
#
#
#                                MEASURES
#
#
#
#
###############################################################################



#-------------------------------------------------------------------------
#
# Set the specified measure to be formatted with a currency symbol.
#

sub autorpt_meas_format_currency
{
  my ($query, $dbOutput, $status, $dsSchema, $formatStr);
  my (@formats);

  my ($db, $dsID, $measureID) = @_;

  if ($measureID < 1)
  {
    return;
  }

  $dsSchema = "datasource_" . $dsID;

  $query = "SELECT format FROM $dsSchema.measures WHERE ID=$measureID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  autorpt_db_err($db, $status, $query);
  ($formatStr) = $dbOutput->fetchrow_array;

  @formats = split(',', $formatStr);

  #set the measure to be "decorated" with a currency symbol
  $formats[2] = 1;

  $formatStr = join(',', @formats);

  $query = "UPDATE $dsSchema.measures SET format='$formatStr' \
      WHERE ID=$measureID";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------
#
# Set the specified measure to be formatted with a percent symbol.
#

sub autorpt_meas_format_percent
{
  my ($query, $dbOutput, $status, $dsSchema, $formatStr);
  my (@formats);

  my ($db, $dsID, $measureID) = @_;

  if ($measureID < 1)
  {
    return;
  }

  $dsSchema = "datasource_" . $dsID;

  $query = "SELECT format FROM $dsSchema.measures WHERE ID=$measureID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  autorpt_db_err($db, $status, $query);
  ($formatStr) = $dbOutput->fetchrow_array;

  @formats = split(',', $formatStr);

  #set the measure to be "decorated" with a percent symbol
  $formats[2] = 2;

  $formatStr = join(',', @formats);

  $query = "UPDATE $dsSchema.measures SET format='$formatStr' \
      WHERE ID=$measureID";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------
#
# Set the specified measure to have enhanced negative number formatting.
#

sub autorpt_meas_format_negatives
{
  my ($query, $dbOutput, $status, $dsSchema, $formatStr);
  my (@formats);

  my ($db, $dsID, $measureID) = @_;

  if ($measureID < 1)
  {
    return;
  }

  $dsSchema = "datasource_" . $dsID;

  $query = "SELECT format FROM $dsSchema.measures WHERE ID=$measureID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  autorpt_db_err($db, $status, $query);
  ($formatStr) = $dbOutput->fetchrow_array;

  @formats = split(',', $formatStr);

  #set the measure to have negative values displayed in red parantheses
  $formats[3] = 4;

  $formatStr = join(',', @formats);

  $query = "UPDATE $dsSchema.measures SET format='$formatStr' \
      WHERE ID=$measureID";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------
#
# Set the specified measure to have a fixed number of decimals for display
#

sub autorpt_meas_format_decimals
{
  my ($query, $dbOutput, $status, $formatStr, $dsSchema);
  my (@formats);

  my ($db, $dsID, $measureID, $decimals) = @_;

  if ($measureID < 1)
  {
    return;
  }

  $dsSchema = "datasource_" . $dsID;

  $query = "SELECT format FROM $dsSchema.measures WHERE ID=$measureID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  autorpt_db_err($db, $status, $query);
  ($formatStr) = $dbOutput->fetchrow_array;

  @formats = split(',', $formatStr);

  #set the measure to be displayed with the set number of decimals
  $formats[0] = $decimals;

  $formatStr = join(',', @formats);

  $query = "UPDATE $dsSchema.measures SET format='$formatStr' \
      WHERE ID=$measureID";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------
#
# Search the measures that already exist in the data source, by name.
# Used by the autorpt_find_meas_* functions to determine if a measure,
# possibly known by one of many pipe-delimited names, already exists in the
# data source.
#

sub autorpt_find_meas_by_name
{
  my ($dsSchema, $measureID, $measName);
  my (@measNames);
  my (%baseMeasureHash);

  my ($db, $dsID, $measNameString) = @_;

  $dsSchema = "datasource_" . $dsID;

  #TODO: for now, we're retrieving the measure list every time we get called.
  #   That's a little inefficient, but it guarantees we pick up any measures
  #   that we've already created as part of the auto report process. In the
  #   future, we can have all of the measure create functions update the
  #   hash so we only hit the database the first time we're called.

  #get all of the measures in the data source, lower-case them, and reverse the
  #hash so we can search by name
  %baseMeasureHash = dsr_get_base_item_name_hash($db, $dsSchema, "m", 1);
  foreach $measureID (keys %baseMeasureHash)
  {
    $baseMeasureHash{$measureID} = lc($baseMeasureHash{$measureID});
  }
  %baseMeasureHash = reverse(%baseMeasureHash);

  #lowercase the search string, and split into array of search strings ordered
  #by preference
  $measNameString = lc($measNameString);
  @measNames = split('\|', $measNameString);
  foreach $measName (@measNames)
  {
    if ($baseMeasureHash{$measName} > 0)
    {
      return($baseMeasureHash{$measName});
    }
  }

  #we didn't find a match, so return undef
  return;
}



#-------------------------------------------------------------------------
#
# Search the measures that already exist in the data source, by calculation.
# Used by the autorpt_find_meas_* functions to determine if a measure,
# identified by its formula regardless of name, already exists in the
# data source.
#

sub autorpt_find_meas_by_formula
{
  my ($query, $dbOutput, $status, $dsSchema, $q_measFormulaString, $measureID);

  my ($db, $dsID, $measFormulaString) = @_;

  $dsSchema = "datasource_" . $dsID;
  $q_measFormulaString = $db->quote($measFormulaString);

  $query = "SELECT ID FROM $dsSchema.measures \
      WHERE calculation LIKE $q_measFormulaString";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  autorpt_db_err($db, $status, $query);
  ($measureID) = $dbOutput->fetchrow_array;

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing %ACV Reach.
#

sub autorpt_find_meas_acv_reach
{
  my ($measureID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "\%ACV Reach|Max \% ACV|Average Weekly ACV Distribution|ACV Wtd Dist|\%ACV");

  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 1);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing ACV Reach change from last year
#

sub autorpt_find_meas_acv_reach_cya
{
  my ($measureID, $acvReachMeas, $acvReachYAMeas);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "\%ACV Reach CYA|\%ACV Reach Change YAGO");

  #if no luck, look for a difference between %ACV Reach and its YA
  if ($measureID < 1)
  {
    $acvReachMeas = autorpt_find_meas_acv_reach($db, $dsID);
    $acvReachYAMeas = autorpt_find_meas_acv_reach_ya($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "difference|$acvReachMeas|$acvReachYAMeas|");
  }

  #if still no luck, look for a pct change between measures on the ACV Reach/YA
  if ($measureID < 1)
  {
    $acvReachMeas = autorpt_find_meas_acv_reach($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "change|$acvReachMeas|1|year|");
  }

  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 1);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing ACV Reach % change from last year
#

sub autorpt_find_meas_acv_reach_pctchg_ya
{
  my ($measureID, $acvReachMeas, $acvReachYAMeas);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "\%ACV Reach \%Chg YA|\%ACV Reach % CYA|\%ACV Reach Pct CYA|\%ACV Reach Pct Change");

  #if still no luck, look for a pct change between measures on the ACV Reach/YA
  if ($measureID < 1)
  {
    $acvReachMeas = autorpt_find_meas_acv_reach($db, $dsID);
    $acvReachYAMeas = autorpt_find_meas_acv_reach_ya($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "pct_change_meas|$acvReachMeas|$acvReachYAMeas|");
  }

  autorpt_meas_format_percent($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 1);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing %ACV Reach YA.
#

sub autorpt_find_meas_acv_reach_ya
{
  my ($measureID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "\%ACV Reach YA|Max \% ACV, Yago|Average Weekly ACV Distribution Year Ago|ACV Wtd Dist YA");

  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 1);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the Any Promo $ measure, if one exists
#

sub autorpt_find_meas_any_promo_dollars
{
  my ($measureID, $dwofMeasID, $fanddMeasID, $fwodMeasID, $pdMeasID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID, "Any Promo \$");

  if ($measureID < 1)
  {
    $dwofMeasID = autorpt_find_meas_promo_vehicle_dollars($db, $dsID, "disp wo feat");
    $fanddMeasID = autorpt_find_meas_promo_vehicle_dollars($db, $dsID, "feat and disp");
    $fwodMeasID = autorpt_find_meas_promo_vehicle_dollars($db, $dsID, "feat wo disp");
    $pdMeasID = autorpt_find_meas_promo_vehicle_dollars($db, $dsID, "price decr");
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "sum|$dwofMeasID,$fanddMeasID,$fwodMeasID,$pdMeasID|");
  }

  autorpt_meas_format_currency($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 0);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the Any Promo Disc measure, if one exists
# NB: This isn't an "official" measure, but one that seems to be in common
#   enough usage.
#

sub autorpt_find_meas_any_promo_disc
{
  my ($measureID, $basePriceMeasID, $promoPriceMeasID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "Any Promo Disc|Discount \%");

  if ($measureID < 1)
  {
    $basePriceMeasID = autorpt_find_meas_base_unit_price($db, $dsID);
    $promoPriceMeasID = autorpt_find_meas_any_promo_unit_price($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "ratio|$basePriceMeasID|$promoPriceMeasID|");
  }

  autorpt_meas_format_percent($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 1);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the Any Promo Incr $ measure, if one exists
#

sub autorpt_find_meas_any_promo_incr_dollars
{
  my ($measureID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "Any Promo Incr \$");

  autorpt_meas_format_currency($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 0);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the Any Promo Incr $ YA measure, if one exists
#

sub autorpt_find_meas_any_promo_incr_dollars_ya
{
  my ($measureID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "Any Promo Incr \$ YA");

  autorpt_meas_format_currency($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 0);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the Any Promo Units measure, if one exists
#

sub autorpt_find_meas_any_promo_units
{
  my ($measureID, $dwofMeasID, $fanddMeasID, $fwodMeasID, $pdMeasID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "Any Promo Units");

  if ($measureID < 1)
  {
    $dwofMeasID = autorpt_find_meas_promo_vehicle_units($db, $dsID, "disp wo feat");
    $fanddMeasID = autorpt_find_meas_promo_vehicle_units($db, $dsID, "feat and disp");
    $fwodMeasID = autorpt_find_meas_promo_vehicle_units($db, $dsID, "feat wo disp");
    $pdMeasID = autorpt_find_meas_promo_vehicle_units($db, $dsID, "price decr");
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "sum|$dwofMeasID,$fanddMeasID,$fwodMeasID,$pdMeasID|");
  }

  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 0);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the Any Promo Unit Price measure, if one exists
#

sub autorpt_find_meas_any_promo_unit_price
{
  my ($measureID, $promoDolsMeasID, $promoUnitsMeasID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "Any Promo Unit Price");

  if ($measureID < 1)
  {
    $promoDolsMeasID = autorpt_find_meas_any_promo_dollars($db, $dsID);
    $promoUnitsMeasID = autorpt_find_meas_any_promo_units($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "ratio|$promoDolsMeasID|$promoUnitsMeasID|");
  }

  autorpt_meas_format_currency($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 0);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing average number of items
#

sub autorpt_find_meas_avg_num_items
{
  my ($measureID, $tdpMeasID, $acvMeasID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "Avg # Items|Avg Num Items|Average Number Items");

  #if still no luck, look for a count of scanned (positive sales) products
  if ($measureID < 1)
  {
    $tdpMeasID = autorpt_find_meas_tdp($db, $dsID);
    $acvMeasID = autorpt_find_meas_acv_reach($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "ratio|$tdpMeasID|$acvMeasID|0|");
  }

  autorpt_meas_format_decimals($db, $dsID, $measureID, 1);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing average number of items CYA
#

sub autorpt_find_meas_avg_num_items_cya
{
  my ($measureID, $avgNumItemsMeasID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "Avg # Items CYA|Avg Num Items CYA|Average Number Items CYA");

  #if still no luck, look for a count of scanned (positive sales) products
  if ($measureID < 1)
  {
    $avgNumItemsMeasID = autorpt_find_meas_avg_num_items($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "change|$avgNumItemsMeasID|1|year|");
  }

  autorpt_meas_format_decimals($db, $dsID, $measureID, 1);
  autorpt_meas_format_negatives($db, $dsID, $measureID);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing yearly % change in number of items
#

sub autorpt_find_meas_avg_num_items_pctchg_ya
{
  my ($measureID, $avgNumItemsMeasID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "Avg # Items \%Chg YA|Avg Num Items % CYA|Average Num Items % Chg|Average Num Items Pct Chg|Average Num Items Pct CYA");

  #if still no luck, look for a matching calculated measure
  if ($measureID < 1)
  {
    $avgNumItemsMeasID = autorpt_find_meas_avg_num_items($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "pct_change|$avgNumItemsMeasID|1|year|");
  }

  autorpt_meas_format_percent($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 1);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing weighted Avg # Items YA
#

sub autorpt_find_meas_avg_num_items_ya
{
  my ($measureID, $tdpYAMeasID, $acvYAMeasID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "Avg # Items YA|Avg Num Items YA|");

  #if still no luck, look for a matching calculated measure
  if ($measureID < 1)
  {
    $tdpYAMeasID = autorpt_find_meas_tdp_ya($db, $dsID);
    $acvYAMeasID = autorpt_find_meas_acv_reach_ya($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "ratio|$tdpYAMeasID|$acvYAMeasID|0|");
  }

  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 1);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing average unit price
#

sub autorpt_find_meas_avg_unit_price
{
  my ($measureID, $dolSalesMeasID, $unitSalesMeasID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "Avg Unit Price|Average Retail Price|Avg Retail Price");

  #if still no luck, look for a ratio of dollars to units
  if ($measureID < 1)
  {
    $dolSalesMeasID = autorpt_find_meas_dollars($db, $dsID);
    $unitSalesMeasID = autorpt_find_meas_units($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "ratio|$dolSalesMeasID|$unitSalesMeasID|0|");
  }

  autorpt_meas_format_currency($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 2);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing avg unit price % change from last year
#

sub autorpt_find_meas_avg_unit_price_pctchg_ya
{
  my ($measureID, $avgPriceMeasID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "Avg Unit Price \%Chg YA|Avg Retail Price % CYA|Avg Retail Price Pct CYA|Avg Retail Price Pct Change");

  #if still no luck, look for a pct change on the avg unit price
  if ($measureID < 1)
  {
    $avgPriceMeasID = autorpt_find_meas_avg_unit_price($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "pct_change|$avgPriceMeasID|1|year|");
  }

  autorpt_meas_format_percent($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 1);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing average unit price YA
#

sub autorpt_find_meas_avg_unit_price_ya
{
  my ($measureID, $dolSalesYAMeasID, $unitSalesYAMeasID, $priceMeasID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "Avg Unit Price YA|Average Retail Price YA|Avg Retail Price YA");

  #if no luck, look for a ratio of dollars to units using YA measures
  if ($measureID < 1)
  {
    $dolSalesYAMeasID = autorpt_find_meas_dollars_ya($db, $dsID);
    $unitSalesYAMeasID = autorpt_find_meas_units_ya($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "ratio|$dolSalesYAMeasID|$unitSalesYAMeasID|0|");
  }

  #if still no luck, look for a lag on Avg Unit Price
  if ($measureID < 1)
  {
    $priceMeasID = autorpt_find_meas_avg_unit_price($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "lag|$priceMeasID|1|0|");
  }

  autorpt_meas_format_currency($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 2);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing base dollar sales.
#

sub autorpt_find_meas_base_dollars
{
  my ($measureID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "Base \$|Base Dollars|Base \$ Sales");

  autorpt_meas_format_currency($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 2);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing base dollar sales YA.
#

sub autorpt_find_meas_base_dollars_ya
{
  my ($measureID, $baseDolSalesMeasID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "Base \$ YA|Base Dollars YA|Base Dollars, Yago|Base \$ Sales YA");

  #if we didn't find one by name, see if we can find a calculated measure
  #that fits the bill
  if ($measureID < 1)
  {
    $baseDolSalesMeasID = autorpt_find_meas_base_dollars($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "lag|$baseDolSalesMeasID|1|year|");
  }

  autorpt_meas_format_currency($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 2);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing base unit price
#

sub autorpt_find_meas_base_unit_price
{
  my ($measureID, $baseDolSalesMeasID, $baseUnitSalesMeasID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID, "Base Unit Price|Base Price");

  #if still no luck, look for a ratio on the base $ and base units
  if ($measureID < 1)
  {
    $baseDolSalesMeasID = autorpt_find_meas_base_dollars($db, $dsID);
    $baseUnitSalesMeasID = autorpt_find_meas_base_units($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "ratio|$baseDolSalesMeasID|$baseUnitSalesMeasID|0|");
  }

  autorpt_meas_format_currency($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 2);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing base unit price % change from last year
#

sub autorpt_find_meas_base_unit_price_pctchg_ya
{
  my ($measureID, $basePriceMeasID);

  my ($db, $dsID, $noScript) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "Base Unit Price \%Chg YA|Base Price % CYA|Base Price Pct CYA|Base Price Pct Change");

  #if still no luck, look for a pct change on the base unit price
  if ($measureID < 1)
  {
    $basePriceMeasID = autorpt_find_meas_base_unit_price($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "pct_change|$basePriceMeasID|1|year|");
  }

  autorpt_meas_format_percent($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 1);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing base dollar sales.
#

sub autorpt_find_meas_base_units
{
  my ($measureID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID, "Base Units|Base Unit Sales");

  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 0);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing dollar sales.
#

sub autorpt_find_meas_dollars
{
  my ($measureID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID, "\$|Dollars|Dollar Sales|\$ Sales");

  autorpt_meas_format_currency($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 2);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing dollar sales YA.
#

sub autorpt_find_meas_dollars_ya
{
  my ($measureID, $dolSalesMeasID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "\$ YA|Dollars YA|Dollar Sales YAG|\$ Sales YA");

  #if still no luck, look for a lag on the dollar sales measure
  if ($measureID < 1)
  {
    $dolSalesMeasID = autorpt_find_meas_dollars($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID, "lag|$dolSalesMeasID|1|year|");
  }

  autorpt_meas_format_currency($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 2);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing the change in dollar sales.
#

sub autorpt_find_meas_dollars_cya
{
  my ($measureID, $dolSalesMeasID, $dolSalesYAMeasID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "\$ Change|Dollar Change|Dollar CYA|\$ CYA");

  #if we didn't find one by name, see if we can find a calculated % change
  #measure that fits the bill
  if ($measureID < 1)
  {
    $dolSalesMeasID = autorpt_find_meas_dollars($db, $dsID);
    $dolSalesYAMeasID = autorpt_find_meas_dollars_ya($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "difference|$dolSalesMeasID|$dolSalesYAMeasID|");
  }

  #ok, if still nothing try looking for a calculated change on the $ measure
  if ($measureID < 1)
  {
    $dolSalesMeasID = autorpt_find_meas_dollars($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "change|$dolSalesMeasID|1|year|");
  }

  autorpt_meas_format_currency($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 2);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing the % change in dollar sales.
#

sub autorpt_find_meas_dollar_pctchg_ya
{
  my ($measureID, $dolSalesMeasID, $dolSalesYAMeasID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "\$ \%Chg YA|Dollar % CYA|\$ % Chg YAG|\$ % Chg YA|\$ % CYA");

  #if we didn't find one by name, see if we can find a calculated % change
  #measure that fits the bill
  if ($measureID < 1)
  {
    $dolSalesMeasID = autorpt_find_meas_dollars($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "pct_change|$dolSalesMeasID|%");
  }

  #ok, if still nothing try looking for a calculated % change between measures
  if ($measureID < 1)
  {
    $dolSalesYAMeasID = autorpt_find_meas_dollars_ya($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "pct_change_meas|$dolSalesMeasID|$dolSalesYAMeasID|");
  }

  autorpt_meas_format_percent($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 1);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing $ % Lift
#

sub autorpt_find_meas_dollar_pct_lift
{
  my ($measureID, $baseDolSalesMeasID, $incrDolSalesMeasID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "\$ \% Lift|\$ Pct Lift|Dollar Pct Lift|\% Lift, Dollars");

  if ($measureID < 1)
  {
    $baseDolSalesMeasID = autorpt_find_meas_base_dollars($db, $dsID);
    $incrDolSalesMeasID = autorpt_find_meas_incr_dollars($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "ratio|$incrDolSalesMeasID|$baseDolSalesMeasID|1");
  }

  autorpt_meas_format_percent($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 1);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing the $ change due to base velocity
# change.
#

sub autorpt_find_meas_dollar_change_base_velocity_change
{
  my ($measureID, $baseDolSalesMeasID, $tdpMeasID, $baseDolSalesYAMeasID);
  my ($tdpYAMeasID);

  my ($db, $dsID, $noScript) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "\$ Chg Due To Base Velocity Chg|Dol Chg Due To Base Velocity Chg|Dol CYA Due To Base Velocity CYA");

  #if we didn't find one by name, see if we can find a calculated measure
  #that fits the bill
  if ($measureID < 1)
  {
    $baseDolSalesMeasID = autorpt_find_meas_base_dollars($db, $dsID);
    $tdpMeasID = autorpt_find_meas_tdp($db, $dsID);
    $baseDolSalesYAMeasID = autorpt_find_meas_base_dollars_ya($db, $dsID);
    $tdpYAMeasID = autorpt_find_meas_tdp_ya($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "calc|measure_$tdpMeasID*(measure_$baseDolSalesMeasID/measure_$tdpMeasID)-(measure_$baseDolSalesYAMeasID/measure_$tdpYAMeasID)|");
  }

  autorpt_meas_format_currency($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 2);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing the $ change due to change in TDP.
#

sub autorpt_find_meas_dollar_change_tdp_change
{
  my ($measureID, $baseDolSalesYAMeasID, $tdpYAMeasID, $tdpCYAMeasID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "\$ Chg Due To TDP Chg|Dol Chg Due To TDP Chg|Dol CYA Due To TDP CYA");

  #if we didn't find one by name, see if we can find a calculated measure
  #that fits the bill
  if ($measureID < 1)
  {
    $baseDolSalesYAMeasID = autorpt_find_meas_base_dollars_ya($db, $dsID);
    $tdpYAMeasID = autorpt_find_meas_tdp_ya($db, $dsID);
    $tdpCYAMeasID = autorpt_find_meas_tdp_cya($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "calc|(measure_$baseDolSalesYAMeasID/measure_$tdpYAMeasID)*measure_$tdpCYAMeasID|");
  }

  autorpt_meas_format_currency($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 2);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing the dollar share of the specified
# segmentation.
#

sub autorpt_find_meas_dollar_shr_category
{
  my ($measureID, $dolSalesMeasID, $catSegID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "\$ Shr of Category|\$ Share of Category|\$ Share Cat");

  #if we didn't find one by name, see if we can find a calculated % change
  #measure that fits the bill
  if ($measureID < 1)
  {
    $dolSalesMeasID = autorpt_find_meas_dollars($db, $dsID);
    $catSegID = autorpt_find_seg_category($db, $dsID, 1);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "share|p|$dolSalesMeasID|$catSegID|");
  }

  autorpt_meas_format_percent($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 1);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing the YA dollar share of the specified
# segmentation.
#

sub autorpt_find_meas_dollar_shr_category_ya
{
  my ($measureID, $dolSalesYAMeasID, $catSegID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "\$ Shr of Category YA|\$ Share of Category YA|\$ Share Cat YA");

  #if we didn't find one by name, see if we can find a calculated % change
  #measure that fits the bill
  if ($measureID < 1)
  {
    $dolSalesYAMeasID = autorpt_find_meas_dollars_ya($db, $dsID);
    $catSegID = autorpt_find_seg_category($db, $dsID, 1);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "share|p|$dolSalesYAMeasID|$catSegID|");
  }

  autorpt_meas_format_percent($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 1);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing the year over year change in the
# dollar share of the specified segmentation.
#

sub autorpt_find_meas_dollar_shr_category_cya
{
  my ($measureID, $dolShareMeasID, $dolShareYAMeasID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "\$ Shr of Category CYA|\$ Share of Category CYA|\$ Share of Category Change|\$ Share Cat Change");

  #if we didn't find one by name, see if we can find a calculated difference
  #measure that fits the bill
  if ($measureID < 1)
  {
    $dolShareMeasID = autorpt_find_meas_dollar_shr_category($db, $dsID);
    $dolShareYAMeasID = autorpt_find_meas_dollar_shr_category_ya($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "difference|$dolShareMeasID|$dolShareYAMeasID|");
  }

  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 1);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing incremental dollar sales.
#

sub autorpt_find_meas_incr_dollars
{
  my ($measureID, $dolSalesMeasID, $baseDolSalesMeasID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "Incr \$|Incr Dollars|Incremental \$|Incremental Dollars|Incr \$ Sales");

  #if still no luck, look for a difference between dollar sales and base sales
  if ($measureID < 1)
  {
    $dolSalesMeasID = autorpt_find_meas_dollars($db, $dsID);
    $baseDolSalesMeasID = autorpt_find_meas_base_dollars($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "difference|$dolSalesMeasID|$baseDolSalesMeasID|");
  }

  autorpt_meas_format_currency($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 2);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing incremental $ sales CYA.
#

sub autorpt_find_meas_incr_dollars_cya
{
  my ($measureID, $incrDolsMeasID, $IncrDolsYAMeasID, $incrDolSalesMeasID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "Incr \$ CYA|Incr Dollars CYA|Incremental \$ CYA|Incremental Dollars CYA");

  #if still no luck, look for a difference between Incr $ and Incr $ YA
  if ($measureID < 1)
  {
    $incrDolsMeasID = autorpt_find_meas_incr_dollars($db, $dsID);
    $IncrDolsYAMeasID = autorpt_find_meas_incr_dollars_ya($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "difference|$incrDolsMeasID|$IncrDolsYAMeasID|");
  }

  #if still no luck, look for a change YA on incr $
  if ($measureID < 1)
  {
    $incrDolSalesMeasID = autorpt_find_meas_incr_dollars($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "change|$incrDolSalesMeasID|1|year|");
  }

  autorpt_meas_format_currency($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 2);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing incremental dollar sales YA.
#

sub autorpt_find_meas_incr_dollars_ya
{
  my ($measureID, $dolSalesMeasYAID, $baseDolSalesMeasYAID);
  my ($incrDolSalesMeasID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "Incr \$ YA|Incr Dollars YA|Incremental \$ YA|Incremental Dollars YA|Incr \$ Sales YA");

  #if still no luck, look for a difference between $ YA and base $ YA
  if ($measureID < 1)
  {
    $dolSalesMeasYAID = autorpt_find_meas_dollars_ya($db, $dsID);
    $baseDolSalesMeasYAID = autorpt_find_meas_base_dollars_ya($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "difference|$dolSalesMeasYAID|$baseDolSalesMeasYAID|");
  }

  #if still no luck, look for a lag on incr $
  if ($measureID < 1)
  {
    $incrDolSalesMeasID = autorpt_find_meas_incr_dollars($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "lag|$incrDolSalesMeasID|");
  }

  autorpt_meas_format_currency($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 2);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing the Incr $ share of category.
#

sub autorpt_find_meas_incr_dollars_shr_cat
{
  my ($measureID, $incrDolSalesMeasID, $catSegID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "Incr \$ Shr of Category|Incr \$ Share of Category|Incr \$ Share Cat");

  #if we didn't find one by name, see if we can find a calculated % change
  #measure that fits the bill
  if ($measureID < 1)
  {
    $incrDolSalesMeasID = autorpt_find_meas_incr_dollars($db, $dsID);
    $catSegID = autorpt_find_seg_category($db, $dsID, 1);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "share|p|$incrDolSalesMeasID|$catSegID|");
  }

  autorpt_meas_format_percent($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 1);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing the Incr $ YA share of category.
#

sub autorpt_find_meas_incr_dollars_shr_cat_ya
{
  my ($measureID, $incrDolSalesYAMeasID, $catSegID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "Incr \$ Shr of Category YA|Incr \$ Share of Category YA|Incr \$ Share Cat YA");

  #if we didn't find one by name, see if we can find a calculated % change
  #measure that fits the bill
  if ($measureID < 1)
  {
    $incrDolSalesYAMeasID = autorpt_find_meas_incr_dollars_ya($db, $dsID);
    $catSegID = autorpt_find_seg_category($db, $dsID, 1);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "share|p|$incrDolSalesYAMeasID|$catSegID|");
  }

  autorpt_meas_format_percent($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 1);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing the year over year change in the
# Incr $ share of category.
#

sub autorpt_find_meas_incr_dollars_shr_cat_cya
{
  my ($measureID, $incrDolShareMeasID, $incrDolShareYAMeasID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "Incr \$ Shr of Category CYA|Incr \$ Share of Category CYA|Incr \$ Share of Category Change|Incr \$ Share Cat Change");

  #if we didn't find one by name, see if we can find a calculated difference
  #measure that fits the bill
  if ($measureID < 1)
  {
    $incrDolShareMeasID = autorpt_find_meas_incr_dollars_shr_cat($db, $dsID);
    $incrDolShareYAMeasID = autorpt_find_meas_incr_dollars_shr_cat_ya($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "difference|$incrDolShareMeasID|$incrDolShareYAMeasID|");
  }

  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 1);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing incremental unit sales.
#

sub autorpt_find_meas_incr_units
{
  my ($measureID, $unitSalesMeasID, $baseUnitsSalesMeasID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "Incr Units|Incremental Units|Incr Unit Sales");

  #if still no luck, look for a difference between unit sales and base sales
  if ($measureID < 1)
  {
    $unitSalesMeasID = autorpt_find_meas_units($db, $dsID);
    $baseUnitsSalesMeasID = autorpt_find_meas_base_units($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "difference|$unitSalesMeasID|$baseUnitsSalesMeasID|");
  }

  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 0);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing % $ Any Promo.
#

sub autorpt_find_meas_pct_dollars_any_promo
{
  my ($measureID, $promoDolSalesMeasID, $dolSalesMeasID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "% \$ Any Promo|% \$, Any Promo");

  if ($measureID < 1)
  {
    $promoDolSalesMeasID = autorpt_find_meas_any_promo_dollars($db, $dsID);
    $dolSalesMeasID = autorpt_find_meas_dollars($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "ratio|$promoDolSalesMeasID|$dolSalesMeasID|1");
  }

  autorpt_meas_format_percent($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 1);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing % $ Base.
#

sub autorpt_find_meas_pct_dollars_base
{
  my ($measureID, $dolSalesMeasID, $baseDolSalesMeasID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "% \$ Base|Pct Dollars Base");

  #if still no luck, look for a ratio between dollar sales and base sales
  if ($measureID < 1)
  {
    $dolSalesMeasID = autorpt_find_meas_dollars($db, $dsID);
    $baseDolSalesMeasID = autorpt_find_meas_base_dollars($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "ratio|$baseDolSalesMeasID|$dolSalesMeasID|");
  }

  autorpt_meas_format_percent($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 1);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing % $ Incr.
#

sub autorpt_find_meas_pct_dollars_incr
{
  my ($measureID, $dolSalesMeasID, $incrDolSalesMeasID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "% \$ Incr|Pct Dollars Incr");

  #if still no luck, look for a ratio between dollar sales and base sales
  if ($measureID < 1)
  {
    $dolSalesMeasID = autorpt_find_meas_dollars($db, $dsID);
    $incrDolSalesMeasID = autorpt_find_meas_incr_dollars($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "ratio|$incrDolSalesMeasID|$dolSalesMeasID|");
  }

  autorpt_meas_format_percent($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 1);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing Base $ for the specified promotional
# vehicle.
#

sub autorpt_find_meas_promo_vehicle_base_dollars
{
  my ($promoMeasName, $measureID);

  my ($db, $dsID, $promoType) = @_;

  $promoMeasName = $promoNameMapping{$promoType} . " Base \$";

  $measureID = autorpt_find_meas_by_name($db, $dsID, $promoMeasName);

  autorpt_meas_format_currency($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 0);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing Base Units for the specified
# promotional vehicle.
#

sub autorpt_find_meas_promo_vehicle_base_units
{
  my ($promoMeasName, $measureID);

  my ($db, $dsID, $promoType) = @_;

  $promoMeasName = $promoNameMapping{$promoType} . " Base Units";

  $measureID = autorpt_find_meas_by_name($db, $dsID, $promoMeasName);

  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 0);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing CWW (cume weighted weeks) for the
# specified promotional vehicle
#

sub autorpt_find_meas_promo_vehicle_cww
{
  my ($promoMeasName, $measureID);

  my ($db, $dsID, $promoType) = @_;

  $promoMeasName = $promoNameMapping{$promoType} . " CWW";

  $measureID = autorpt_find_meas_by_name($db, $dsID, $promoMeasName);

  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 1);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing CWW CYA for the specified promotional
# vehicle.
#

sub autorpt_find_meas_promo_vehicle_cww_cya
{
  my ($promoMeasName, $measureID, $promoCWWMeasID);

  my ($db, $dsID, $promoType) = @_;

  $promoMeasName = $promoNameMapping{$promoType} . " CWW CYA";

  $measureID = autorpt_find_meas_by_name($db, $dsID, $promoMeasName);

  #if not found try looking for a calculated change on the promo measure
  if ($measureID < 1)
  {
    $promoCWWMeasID = autorpt_find_meas_promo_vehicle_cww($db, $dsID, $promoType);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "change|$promoCWWMeasID|1|year|");
  }

  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 1);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing $ % Lift for the specified promotional
# vehicle.
#

sub autorpt_find_meas_promo_vehicle_dollar_pct_lift
{
  my ($promoMeasName, $measureID);

  my ($db, $dsID, $promoType) = @_;

  $promoMeasName = $promoNameMapping{$promoType} . " \$ % Lift";

  $measureID = autorpt_find_meas_by_name($db, $dsID, $promoMeasName);

  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 1);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing $ % Lift CYA for the specified
# promotional vehicle.
#

sub autorpt_find_meas_promo_vehicle_dollar_pct_lift_cya
{
  my ($measureID, $promoMeasName, $promoMeasID);

  my ($db, $dsID, $promoType) = @_;

  $promoMeasName = $promoNameMapping{$promoType} . " \$ % Lift CYA";

  $measureID = autorpt_find_meas_by_name($db, $dsID, $promoMeasName);

  #if not found try looking for a calculated change on the promo measure
  if ($measureID < 1)
  {
    $promoMeasID = autorpt_find_meas_promo_vehicle_dollar_pct_lift($db, $dsID, $promoType);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "change|$promoMeasID|1|year|");
  }

  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 1);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing $ for the specified
# promotional vehicle.
#

sub autorpt_find_meas_promo_vehicle_dollars
{
  my ($promoMeasName, $measureID);

  my ($db, $dsID, $promoType) = @_;

  #Nielsen promo vehicle naming
  $promoMeasName = $promoNameMapping{$promoType} . " \$";

  if ($promoType eq "disp wo feat")
  {
    $promoMeasName .= "|Promo - Display Only \$";   #IRI
    $promoMeasName .= "|Dollars, Display Only";   #SPINS
  }
  elsif ($promoType eq "feat and disp")
  {
    $promoMeasName .= "|Promo - Feat and Disp \$";   #IRI
    $promoMeasName .= "|Dollars, Feature & Display";   #SPINS
  }
  elsif ($promoType eq "feat wo disp")
  {
    $promoMeasName .= "|Promo - Feature Only \$";   #IRI
    $promoMeasName .= "|Dollars, Feature Only";   #SPINS
  }
  elsif ($promoType eq "price decr")
  {
    $promoMeasName .= "|Price Decr Only \$";   #Nielsen Connect
    $promoMeasName .= "|Promo - TPR \$";   #IRI
    $promoMeasName .= "|Dollars, TPR";   #SPINS
  }

  $measureID = autorpt_find_meas_by_name($db, $dsID, $promoMeasName);

  autorpt_meas_format_currency($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 0);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing Incr $ for the specified
# promotional vehicle.
#

sub autorpt_find_meas_promo_vehicle_incr_dollars
{
  my ($measureID, $promoMeasName, $promoDollarMeasID, $promoDollarBaseMeasID);

  my ($db, $dsID, $promoType) = @_;

  $promoMeasName = $promoNameMapping{$promoType} . " Incr \$";

  $measureID = autorpt_find_meas_by_name($db, $dsID, $promoMeasName);

  #if still no luck, look for a difference between total and base
  if ($measureID < 1)
  {
    $promoDollarMeasID = autorpt_find_meas_promo_vehicle_dollars($db, $dsID, $promoType);
    $promoDollarBaseMeasID = autorpt_find_meas_promo_vehicle_base_dollars($db, $dsID, $promoType);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "difference|$promoDollarMeasID|$promoDollarBaseMeasID|");
  }

  autorpt_meas_format_currency($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 0);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing Incr $ CYA for the specified
# promotional vehicle.
#

sub autorpt_find_meas_promo_vehicle_incr_dollars_cya
{
  my ($measureID, $promoMeasName, $promoIncrDollarMeasID);

  my ($db, $dsID, $promoType) = @_;

  $promoMeasName = $promoNameMapping{$promoType} . " Incr \$ CYA";

  $measureID = autorpt_find_meas_by_name($db, $dsID, $promoMeasName);

  #if still no luck, look for a CYA on Disp w/o Feat Incr $
  if ($measureID < 1)
  {
    $promoIncrDollarMeasID = autorpt_find_meas_promo_vehicle_incr_dollars($db, $dsID, $promoType);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "change|$promoIncrDollarMeasID|1|year|");
  }

  autorpt_meas_format_currency($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 2);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing the specified promotional vehicle's
# Incr $ YA
#

sub autorpt_find_meas_promo_vehicle_incr_dollars_ya
{
  my ($measureID, $promoIncrDollarMeasID, $promoMeasName);

  my ($db, $dsID, $promoType) = @_;

  $promoMeasName = $promoNameMapping{$promoType} . " Incr \$ YA";

  $measureID = autorpt_find_meas_by_name($db, $dsID, $promoMeasName);

  #if still no luck, look for a lag
  if ($measureID < 1)
  {
    $promoIncrDollarMeasID = autorpt_find_meas_promo_vehicle_incr_dollars($db, $dsID, $promoType);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "lag|$promoIncrDollarMeasID|1|year|");
  }

  autorpt_meas_format_currency($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 2);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing the specified promotional vehicle's
#  Incr $ Shr of Cat
#

sub autorpt_find_meas_promo_vehicle_incr_dollars_shr_cat
{
  my ($promoMeasName, $measureID, $incrDolSalesMeasID, $catSegID);

  my ($db, $dsID, $promoType) = @_;

  $promoMeasName = $promoNameMapping{$promoType} . " Incr \$ Shr of Cat";

  $measureID = autorpt_find_meas_by_name($db, $dsID, $promoMeasName);

  #if we didn't find one by name, see if we can find a calculated share
  #measure that fits the bill
  if ($measureID < 1)
  {
    $incrDolSalesMeasID = autorpt_find_meas_promo_vehicle_incr_dollars($db, $dsID, $promoType);
    $catSegID = autorpt_find_seg_category($db, $dsID, 1);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "share|p|$incrDolSalesMeasID|$catSegID|");
  }

  autorpt_meas_format_percent($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 1);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing the specified promotional vehicle's
# Incr $ Shr of Cat CYA
#

sub autorpt_find_meas_promo_vehicle_incr_dollars_shr_cat_cya
{
  my ($promoMeasName, $measureID, $dolShareMeasID, $dolShareYAMeasID);

  my ($db, $dsID, $promoType) = @_;

  $promoMeasName = $promoNameMapping{$promoType} . " Incr \$ Shr of Cat CYA";

  $measureID = autorpt_find_meas_by_name($db, $dsID, $promoMeasName);

  #if we didn't find one by name, see if we can find a calculated share
  #measure that fits the bill
  if ($measureID < 1)
  {
    $dolShareMeasID = autorpt_find_meas_promo_vehicle_incr_dollars_shr_cat($db, $dsID, $promoType);
    $dolShareYAMeasID = autorpt_find_meas_promo_vehicle_incr_dollars_shr_cat_ya($db, $dsID, $promoType);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "difference|$dolShareMeasID|$dolShareYAMeasID|");
  }

  autorpt_meas_format_percent($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 1);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing the specified promotional vehicle's
#  Incr $ Shr of Cat YA
#

sub autorpt_find_meas_promo_vehicle_incr_dollars_shr_cat_ya
{
  my ($promoMeasName, $measureID, $catSegID, $promoDolSalesMeasID);

  my ($db, $dsID, $promoType) = @_;

  $promoMeasName = $promoNameMapping{$promoType} . " Incr \$ Shr of Cat YA";

  $measureID = autorpt_find_meas_by_name($db, $dsID, $promoMeasName);

  #if we didn't find one by name, see if we can find a calculated share
  #measure that fits the bill
  if ($measureID < 1)
  {
    $promoDolSalesMeasID = autorpt_find_meas_promo_vehicle_incr_dollars_ya($db, $dsID, $promoType);
    $catSegID = autorpt_find_seg_category($db, $dsID, 1);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "share|p|$promoDolSalesMeasID|$catSegID|");
  }

  autorpt_meas_format_percent($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 1);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing the specified promotional vehicle's
# Unit Price
#

sub autorpt_find_meas_promo_vehicle_unit_price
{
  my ($promoMeasName, $measureID);

  my ($db, $dsID, $promoType) = @_;

  $promoMeasName = $promoNameMapping{$promoType} . " Unit Price";

  $measureID = autorpt_find_meas_by_name($db, $dsID, $promoMeasName);

  autorpt_meas_format_currency($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 2);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing the specified promotional vehicle's
# Unit Price CYA
#

sub autorpt_find_meas_promo_vehicle_unit_price_cya
{
  my ($measureID, $promoMeasName, $promoMeasID);

  my ($db, $dsID, $promoType) = @_;

  $promoMeasName = $promoNameMapping{$promoType} . " Unit Price CYA";

  $measureID = autorpt_find_meas_by_name($db, $dsID, $promoMeasName);

  #if not found try looking for a calculated change on the promo measure
  if ($measureID < 1)
  {
    $promoMeasID = autorpt_find_meas_promo_vehicle_unit_price($db, $dsID, $promoType);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "change|$promoMeasID|1|year|");
  }

  autorpt_meas_format_currency($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 2);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing the specified promotional vehicle's
# Unit Price % Disc
#

sub autorpt_find_meas_promo_vehicle_unit_price_pct_disc
{
  my ($promoMeasName, $measureID);

  my ($db, $dsID, $promoType) = @_;

  $promoMeasName = $promoNameMapping{$promoType} . " Unit Price % Disc";

  $measureID = autorpt_find_meas_by_name($db, $dsID, $promoMeasName);

  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 1);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing the specified promotional vehicle's
# Unit Price % Disc CYA
#

sub autorpt_find_meas_promo_vehicle_unit_price_pct_disc_cya
{
  my ($promoMeasName, $measureID);

  my ($db, $dsID, $promoType) = @_;

  $promoMeasName = $promoNameMapping{$promoType} . " Unit Price % Disc CYA";

  $measureID = autorpt_find_meas_by_name($db, $dsID, $promoMeasName);

  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 1);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing the specified promotional vehicle's
# Units
#

sub autorpt_find_meas_promo_vehicle_units
{
  my ($promoMeasName, $measureID);

  my ($db, $dsID, $promoType) = @_;

  $promoMeasName = $promoNameMapping{$promoType} . " Units";

  if ($promoType eq "disp wo feat")
  {
    $promoMeasName .= "|Units, Display Only";   #SPINS
  }
  elsif ($promoType eq "feat and disp")
  {
    $promoMeasName .= "|Units, Feature & Display";   #SPINS
  }
  elsif ($promoType eq "feat wo disp")
  {
    $promoMeasName .= "|Units, Feature Only";   #SPINS
  }
  elsif ($promoType eq "price decr")
  {
    $promoMeasName .= "|Price Decr Only Units";   #Nielsen Connect
    $promoMeasName .= "|Units, TPR";   #SPINS
  }

  $measureID = autorpt_find_meas_by_name($db, $dsID, $promoMeasName);

  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 0);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing total distribution points.
#

sub autorpt_find_meas_tdp
{
  my ($measureID, $acvReachMeasID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "TDP|Total Distribution Points|Total Distr Pts|Total Distribution Pts");

  #if still no luck, look for a sum of %ACV reach
  if ($measureID < 1)
  {
    $acvReachMeasID = autorpt_find_meas_acv_reach($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "sum|$acvReachMeasID|");
  }

  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 1);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing total distribution points CYA.
#

sub autorpt_find_meas_tdp_cya
{
  my ($measureID, $tdpMeasID, $tdpYAMeasID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "TDP CYA|Total Distribution Points CYA|Total Distr Pts CYA|Total Distribution Pts CYA");

  #if still no luck, look for a TDP lag calculation
  if ($measureID < 1)
  {
    $tdpMeasID = autorpt_find_meas_tdp($db, $dsID);
    $tdpYAMeasID = autorpt_find_meas_tdp_ya($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "difference|$tdpMeasID|$tdpYAMeasID|");
  }

  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 1);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing total distribution points YA.
#

sub autorpt_find_meas_tdp_ya
{
  my ($measureID, $tdpMeasID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "TDP YA|Total Distribution Points YA|Total Distr Pts YA|Total Distribution Pts YA");

  #if still no luck, look for a TDP lag calculation
  if ($measureID < 1)
  {
    $tdpMeasID = autorpt_find_meas_tdp($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "lag|$tdpMeasID|1|year|");
  }

  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 1);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing unit sales.
#

sub autorpt_find_meas_units
{
  my ($measureID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID, "Units|Unit Sales");

  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 0);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing unit sales YA.
#

sub autorpt_find_meas_units_ya
{
  my ($measureID, $unitSalesMeasID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID, "Units YA|Unit Sales YAG");

  #if still no luck, look for a lag on the unit sales measure
  if ($measureID < 1)
  {
    $unitSalesMeasID = autorpt_find_meas_units($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "lag|$unitSalesMeasID|%");
  }

  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 0);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing the change in unit sales.
#

sub autorpt_find_meas_units_cya
{
  my ($measureID, $unitSalesMeasID, $unitSalesYAMeasID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "Unit CYA|Unit Change|Units Change");

  #if we didn't find one by name, see if we can find a calculated % change
  #measure that fits the bill
  if ($measureID < 1)
  {
    $unitSalesMeasID = autorpt_find_meas_units($db, $dsID);
    $unitSalesYAMeasID = autorpt_find_meas_units_ya($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "difference|$unitSalesMeasID|$unitSalesYAMeasID|");
  }

  #ok, if still nothing try looking for a calculated change on the unit measure
  if ($measureID < 1)
  {
    $unitSalesMeasID = autorpt_find_meas_units($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "change|$unitSalesMeasID|1|year|");
  }

  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 0);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing the % change in unit sales.
#

sub autorpt_find_meas_units_pctchg_ya
{
  my ($measureID, $unitSalesMeasID, $unitSalesYAMeasID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "Units \%Chg YA|Unit % CYA|Unit % Chg YAG|Unit % Chg YA");

  #if we didn't find one by name, see if we can find a calculated % change
  #measure that fits the bill
  if ($measureID < 1)
  {
    $unitSalesMeasID = autorpt_find_meas_units($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "pct_change|$unitSalesMeasID|%");
  }

  #ok, if still nothing try looking for a calculated % change between measures
  if ($measureID < 1)
  {
    $unitSalesYAMeasID = autorpt_find_meas_units_ya($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "pct_change_meas|$unitSalesMeasID|$unitSalesYAMeasID|");
  }

  autorpt_meas_format_percent($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 1);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing the unit share of the specified
# segmentation.
#

sub autorpt_find_meas_units_shr_category
{
  my ($measureID, $unitSalesMeasID, $catSegID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "Units Shr of Category|Unit Share of Category|Unit Share Cat");

  #if we didn't find one by name, see if we can find a calculated % change
  #measure that fits the bill
  if ($measureID < 1)
  {
    $unitSalesMeasID = autorpt_find_meas_units($db, $dsID);
    $catSegID = autorpt_find_seg_category($db, $dsID, 1);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "share|p|$unitSalesMeasID|$catSegID|");
  }

  autorpt_meas_format_percent($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 1);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing the YA unit share of the specified
# segmentation.
#

sub autorpt_find_meas_units_shr_category_ya
{
  my ($measureID, $unitSalesYAMeasID, $catSegID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "Units Shr of Category YA|Unit Share of Category YA|Unit Share Cat YA");

  #if we didn't find one by name, see if we can find a calculated % change
  #measure that fits the bill
  if ($measureID < 1)
  {
    $unitSalesYAMeasID = autorpt_find_meas_units_ya($db, $dsID);
    $catSegID = autorpt_find_seg_category($db, $dsID, 1);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "share|p|$unitSalesYAMeasID|$catSegID|");
  }

  autorpt_meas_format_percent($db, $dsID, $measureID);
  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 1);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Find and return the measure containing the year over year change in the
# unit share of the specified segmentation.
#

sub autorpt_find_meas_units_shr_category_cya
{
  my ($measureID, $unitShareMeasID, $unitShareYAMeasID);

  my ($db, $dsID) = @_;

  $measureID = autorpt_find_meas_by_name($db, $dsID,
      "Units Shr of Category CYA|Unit Share of Category CYA|Unit Share of Category Change|Unit Share Cat Change");

  #if we didn't find one by name, see if we can find a calculated difference
  #measure that fits the bill
  if ($measureID < 1)
  {
    $unitShareMeasID = autorpt_find_meas_units_shr_category($db, $dsID);
    $unitShareYAMeasID = autorpt_find_meas_units_shr_category_ya($db, $dsID);
    $measureID = autorpt_find_meas_by_formula($db, $dsID,
        "difference|$unitShareMeasID|$unitShareYAMeasID|");
  }

  autorpt_meas_format_negatives($db, $dsID, $measureID);
  autorpt_meas_format_decimals($db, $dsID, $measureID, 1);

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a calculated measure for %ACV Reach CYA if one doesn't already exit.
#

sub autorpt_create_meas_acv_reach_cya
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($acvReachMeasID, $acvReachYAMeasID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_acv_reach_cya($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure \%ACV Reach CYA");

    $acvReachMeasID = autorpt_find_meas_acv_reach($db, $dsID);
    $acvReachYAMeasID = autorpt_find_meas_acv_reach_ya($db, $dsID);

    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('\%ACV Reach CYA', 'difference|$acvReachMeasID|$acvReachYAMeasID|', '0,1,0,1')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the average baseline price if one doesn't
# already exist, and return a selection scriptlet for it.
#

sub autorpt_create_meas_acv_reach_pctchg_ya
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($acvReachMeasID, $acvReachYAMeasID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_acv_reach_pctchg_ya($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure \%ACV Reach \%Chg YA");

    $acvReachMeasID = autorpt_find_meas_acv_reach($db, $dsID);
    $acvReachYAMeasID = autorpt_find_meas_acv_reach_ya($db, $dsID);

    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('\%ACV Reach \%Chg YA', 'pct_change_meas|$acvReachMeasID|$acvReachYAMeasID|', '1,1,2,4')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the Any Promo $ if one doesn't already exist
#

sub autorpt_create_meas_any_promo_dollars
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($promoDwoFMeasID, $promoFandDMeasID, $promoFwoDMeasID, $promoTPRMeasID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_any_promo_dollars($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure Any Promo \$");

    $promoDwoFMeasID = autorpt_find_meas_promo_vehicle_dollars($db, $dsID, "disp wo feat");
    $promoFandDMeasID = autorpt_find_meas_promo_vehicle_dollars($db, $dsID, "feat and disp");
    $promoFwoDMeasID = autorpt_find_meas_promo_vehicle_dollars($db, $dsID, "feat wo disp");
    $promoTPRMeasID = autorpt_find_meas_promo_vehicle_dollars($db, $dsID, "price decr");

    #create the calculated measure
    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('Any Promo \$', 'sum|$promoDwoFMeasID,$promoFandDMeasID,$promoFwoDMeasID,$promoTPRMeasID|', '2,1,1,4')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the Any Promo Disc if one doesn't already exist
#

sub autorpt_create_meas_any_promo_disc
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($basePriceMeasID, $promoPriceMeasID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_any_promo_disc($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure Any Promo Disc");

    $basePriceMeasID = autorpt_create_meas_base_unit_price($db, $dsID);
    $promoPriceMeasID = autorpt_create_meas_any_promo_unit_price($db, $dsID);

    #create the calculated measure
    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('Any Promo Disc', 'ratio|$basePriceMeasID|$promoPriceMeasID|1|', '2,1,0,1')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the Any Promo Incr $
#

sub autorpt_create_meas_any_promo_incr_dollars
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($promoDwoFMeasID, $promoFandDMeasID, $promoFwoDMeasID, $promoTPRMeasID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_any_promo_incr_dollars($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure Any Promo Incr \$");

    $promoDwoFMeasID = autorpt_create_meas_promo_vehicle_incr_dollars($db, $dsID, "disp wo feat");
    $promoFandDMeasID = autorpt_create_meas_promo_vehicle_incr_dollars($db, $dsID, "feat and disp");
    $promoFwoDMeasID = autorpt_create_meas_promo_vehicle_incr_dollars($db, $dsID, "feat wo disp");
    $promoTPRMeasID = autorpt_create_meas_promo_vehicle_incr_dollars($db, $dsID, "price decr");

    #create the calculated measure
    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('Any Promo Incr \$', 'sum|$promoDwoFMeasID,$promoFandDMeasID,$promoFwoDMeasID,$promoTPRMeasID|', '2,1,1,4')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create the Any Promo Incr \$ YA measure if one doesn't already exist.
#

sub autorpt_create_meas_any_promo_incr_dollars_ya
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($promoMeasID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_any_promo_incr_dollars_ya($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure Any Promo Incr \$ YA");

    $promoMeasID = autorpt_create_meas_any_promo_incr_dollars($db, $dsID);

    #create the calculated measure
    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('Any Promo Incr \$ YA', 'lag|$promoMeasID|1|year|', '2,1,1,4')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the Any Promo Units if one doesn't already exist
#

sub autorpt_create_meas_any_promo_units
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($promoDwoFMeasID, $promoFandDMeasID, $promoFwoDMeasID, $promoTPRMeasID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_any_promo_units($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure Any Promo Units");

    $promoDwoFMeasID = autorpt_find_meas_promo_vehicle_units($db, $dsID, "disp wo feat");
    $promoFandDMeasID = autorpt_find_meas_promo_vehicle_units($db, $dsID, "feat and disp");
    $promoFwoDMeasID = autorpt_find_meas_promo_vehicle_units($db, $dsID, "feat wo disp");
    $promoTPRMeasID = autorpt_find_meas_promo_vehicle_units($db, $dsID, "price decr");

    #create the calculated measure
    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('Any Promo Units', 'sum|$promoDwoFMeasID,$promoFandDMeasID,$promoFwoDMeasID,$promoTPRMeasID|', '2,1,0,1')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the Any Promo Unit Price if one doesn't already exist
#

sub autorpt_create_meas_any_promo_unit_price
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($dolsMeasID, $unitsMeasID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_any_promo_unit_price($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure Any Promo Unit Price");

    $dolsMeasID = autorpt_create_meas_any_promo_dollars($db, $dsID);
    $unitsMeasID = autorpt_create_meas_any_promo_units($db, $dsID);

    #create the calculated measure
    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('Any Promo Unit Price', 'ratio|$dolsMeasID|$unitsMeasID|0|', '2,1,0,1')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the average number of items if one doesn't
# already exist, and return a selection scriptlet for it.
#

sub autorpt_create_meas_avg_num_items
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($tdpMeasID, $acvMeasID);

  my ($db, $dsID, $noScript) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_avg_num_items($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure Avg Num Items");

    $tdpMeasID = autorpt_create_meas_tdp($db, $dsID);
    $acvMeasID = autorpt_find_meas_acv_reach($db, $dsID);

    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('Avg # Items', 'ratio|$tdpMeasID|$acvMeasID|0|', '0,1,0,1')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the average number of items CYA if one doesn't
# already exist, and return a selection scriptlet for it.
#

sub autorpt_create_meas_avg_num_items_cya
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($avgNumItemsMeasID, $avgNumItemsYAMeasID);

  my ($db, $dsID, $noScript) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_avg_num_items_cya($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure Avg # Items CYA");

    $avgNumItemsMeasID = autorpt_create_meas_avg_num_items($db, $dsID);
    $avgNumItemsYAMeasID = autorpt_create_meas_avg_num_items_ya($db, $dsID);

    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('Avg # Items CYA', 'difference|$avgNumItemsMeasID|$avgNumItemsYAMeasID|', '0,1,0,1')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the % change in average number of items if one
# doesn't already exist, and return a selection scriptlet for it.
#

sub autorpt_create_meas_avg_num_items_pctchg_ya
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($avgNumItemsMeasID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_avg_num_items_pctchg_ya($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure Avg # of Items \%Chg YA");

    $avgNumItemsMeasID = autorpt_create_meas_avg_num_items($db, $dsID);

    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('Avg # of Items \%Chg YA', 'pct_change|$avgNumItemsMeasID|1|year|', '1,1,2,4')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the average number of items YA if one doesn't
# already exist.
#

sub autorpt_create_meas_avg_num_items_ya
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($tdpYAMeasID, $acvYAMeasID);

  my ($db, $dsID, $noScript) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_avg_num_items_ya($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure Avg # Items YA");

    $tdpYAMeasID = autorpt_create_meas_tdp_ya($db, $dsID);
    $acvYAMeasID = autorpt_find_meas_acv_reach_ya($db, $dsID);

    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('Avg # Items YA', 'ratio|$tdpYAMeasID|$acvYAMeasID|0|', '0,1,0,1')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the average retail price if one doesn't
# already exist, and return a selection scriptlet for it.
#

sub autorpt_create_meas_avg_unit_price
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($dolSalesMeasID, $unitSalesMeasID);

  my ($db, $dsID, $noScript) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_avg_unit_price($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure Avg Unit Price");

    $dolSalesMeasID = autorpt_find_meas_dollars($db, $dsID);
    $unitSalesMeasID = autorpt_find_meas_units($db, $dsID);

    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('Avg Unit Price', 'ratio|$dolSalesMeasID|$unitSalesMeasID|0|', '2,1,1,4')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the retail price % CYA if one doesn't
# already exist, and return a selection scriptlet for it.
#

sub autorpt_create_meas_avg_unit_price_pctchg_ya
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($avgPriceMeasID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_avg_unit_price_pctchg_ya($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure Avg Unit Price \%Chg YA");

    $avgPriceMeasID = autorpt_create_meas_avg_unit_price($db, $dsID);

    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('Avg Unit Price \%Chg YA', 'pct_change|$avgPriceMeasID|1|year|', '1,1,2,4')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the retail price YA if one doesn't
# already exist.
#

sub autorpt_create_meas_avg_unit_price_ya
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($dolSalesYAMeasID, $unitSalesYAMeasID, $avgPriceMeasID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_avg_unit_price_ya($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure Avg Unit Price YA");

    $dolSalesYAMeasID = autorpt_find_meas_dollars_ya($db, $dsID);
    $unitSalesYAMeasID = autorpt_find_meas_units_ya($db, $dsID);
    if (($dolSalesYAMeasID > 0) && ($unitSalesYAMeasID > 0))
    {
      $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
          VALUES ('Avg Unit Price YA', 'ratio|$dolSalesYAMeasID|$unitSalesYAMeasID|0|', '2,1,1,4')";
    }
    else
    {
      $avgPriceMeasID = autorpt_create_meas_avg_unit_price($db, $dsID);
      $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
          VALUES ('Avg Unit Price YA', 'lag|$avgPriceMeasID|1|year|', '2,1,1,4')";
    }

    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the base dollars YA if one doesn't
# already exist, and return a selection scriptlet for it.
#

sub autorpt_create_meas_base_dollars_ya
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($baseDolSalesMeasID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_base_dollars_ya($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure Base \$ YA");

    $baseDolSalesMeasID = autorpt_find_meas_base_dollars($db, $dsID);

    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('Base \$ YA', 'lag|$baseDolSalesMeasID|1|year|', '2,1,1,4')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the average baseline price if one doesn't
# already exist, and return a selection scriptlet for it.
#

sub autorpt_create_meas_base_unit_price
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($baseDolSalesMeasID, $baseUnitSalesMeasID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_base_unit_price($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure Base Unit Price");

    $baseDolSalesMeasID = autorpt_find_meas_base_dollars($db, $dsID);
    $baseUnitSalesMeasID = autorpt_find_meas_base_units($db, $dsID);

    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('Base Unit Price', 'ratio|$baseDolSalesMeasID|$baseUnitSalesMeasID|0|', '2,1,1,4')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the base unit price % CYA if one doesn't
# already exist, and return a selection scriptlet for it.
#

sub autorpt_create_meas_base_unit_price_pctchg_ya
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($basePriceMeasID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_base_unit_price_pctchg_ya($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure Base Unit Price \%Chg YA");

    $basePriceMeasID = autorpt_create_meas_base_unit_price($db, $dsID);

    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('Base Unit Price \%Chg YA', 'pct_change|$basePriceMeasID|1|year|', '1,1,2,4')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the change in dollar sales if one doesn't
# already exist, and return a selection scriptlet for it.
#

sub autorpt_create_meas_dollars_cya
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($dolSalesMeasID, $dolSalesYAMeasID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_dollars_cya($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure \$ CYA");

    $dolSalesMeasID = autorpt_find_meas_dollars($db, $dsID);
    $dolSalesYAMeasID = autorpt_find_meas_dollars_ya($db, $dsID);

    #if there is a YA measure, create a difference between the two measures
    #(faster calc)
    if (($dolSalesYAMeasID > 0) && ($dolSalesMeasID > 0))
    {
      $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
          VALUES ('\$ CYA', 'difference|$dolSalesMeasID|$dolSalesYAMeasID|', '2,1,0,1')";
      $status = $db->do($query);
      autorpt_db_err($db, $status, $query);
      $measureID = $db->{q{mysql_insertid}};

      $colName = "measure_" . $measureID;
      $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
      $status = $db->do($query);
      autorpt_db_err($db, $status, $query);

      DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
    }

    #if we have at least a dollar sales measure, do a straight change
    elsif ($dolSalesMeasID > 0)
    {
      $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
          VALUES ('\$ Change', 'change|$dolSalesMeasID|1|year|', '2,1,0,1')";
      $status = $db->do($query);
      autorpt_db_err($db, $status, $query);
      $measureID = $db->{q{mysql_insertid}};

      $colName = "measure_" . $measureID;
      $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
      $status = $db->do($query);
      autorpt_db_err($db, $status, $query);

      DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
    }
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the dollar sales YA if one doesn't
# already exist.
#

sub autorpt_create_meas_dollars_ya
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($dolSalesMeasID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_dollars_ya($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure \$ YA");

    #see if we can do the calc as a difference
    undef($query);
    $dolSalesMeasID = autorpt_find_meas_dollars($db, $dsID);
    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('\$ YA', 'lag|$dolSalesMeasID|1|year|', '2,1,1,4')";

    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the % change in dollar sales if one doesn't
# already exist, and return a selection scriptlet for it.
#

sub autorpt_create_meas_dollar_pctchg_ya
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($dolSalesMeasID, $dolSalesYAMeasID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_dollar_pctchg_ya($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure \$ \%Chg YA");

    $dolSalesMeasID = autorpt_find_meas_dollars($db, $dsID);
    $dolSalesYAMeasID = autorpt_find_meas_dollars_ya($db, $dsID);

    #if there is a YA measure, create a pct % between measures (faster calc)
    if (($dolSalesYAMeasID > 0) && ($dolSalesMeasID > 0))
    {
      $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
          VALUES ('\$ \%Chg YA', 'pct_change_meas|$dolSalesMeasID|$dolSalesYAMeasID|', '1,1,2,1')";
      $status = $db->do($query);
      autorpt_db_err($db, $status, $query);
      $measureID = $db->{q{mysql_insertid}};

      $colName = "measure_" . $measureID;
      $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
      $status = $db->do($query);
      autorpt_db_err($db, $status, $query);

      DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
    }

    #if we have at least a dollar sales measure, do a straight % change
    elsif ($dolSalesMeasID > 0)
    {
      $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
          VALUES ('\$ \%Chg YA', 'pct_change|$dolSalesMeasID|1|year|', '1,1,2,1')";
      $status = $db->do($query);
      autorpt_db_err($db, $status, $query);
      $measureID = $db->{q{mysql_insertid}};

      $colName = "measure_" . $measureID;
      $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
      $status = $db->do($query);
      autorpt_db_err($db, $status, $query);

      DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
    }
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing $ % Lift
#

sub autorpt_create_meas_dollar_pct_lift
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($baseDolSalesMeasID, $incrDolSalesMeasID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_dollar_pct_lift($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure \$ \% Lift");

    #see if we can do the calc as a difference
    $baseDolSalesMeasID = autorpt_find_meas_base_dollars($db, $dsID);
    $incrDolSalesMeasID = autorpt_create_meas_incr_dollars($db, $dsID);
    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('\$ \% Lift', 'ratio|$incrDolSalesMeasID|$baseDolSalesMeasID|1|', '2,1,1,4')";

    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the $ change due to base velocity change if one
# doesn't already exist, and return a selection scriptlet for it.
#

sub autorpt_create_meas_dollar_change_base_velocity_change
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($baseDolSalesMeasID, $tdpMeasID, $baseDolSalesYAMeasID, $tdpYAMeasID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_dollar_change_base_velocity_change($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure \$ Chg Due To Base Velocity Chg");

    $baseDolSalesMeasID = autorpt_find_meas_base_dollars($db, $dsID);
    $tdpMeasID = autorpt_create_meas_tdp($db, $dsID);
    $baseDolSalesYAMeasID = autorpt_create_meas_base_dollars_ya($db, $dsID);
    $tdpYAMeasID = autorpt_create_meas_tdp_ya($db, $dsID);

    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('\$ Chg Due To Base Velocity Chg', 'calc|measure_$tdpMeasID*(measure_$baseDolSalesMeasID/measure_$tdpMeasID)-(measure_$baseDolSalesYAMeasID/measure_$tdpYAMeasID)|', '2,1,1,4')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the $ change due to TDP change if one doesn't
# already exist, and return a selection scriptlet for it.
#

sub autorpt_create_meas_dollar_change_tdp_change
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($baseDolSalesYAMeasID, $tdpYAMeasID, $tdpCYAMeasID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_dollar_change_tdp_change($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure \$ Chg Due To TDP Chg");

    $baseDolSalesYAMeasID = autorpt_create_meas_base_dollars_ya($db, $dsID);
    $tdpYAMeasID = autorpt_create_meas_tdp_ya($db, $dsID);
    $tdpCYAMeasID = autorpt_create_meas_tdp_cya($db, $dsID);

    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('\$ Chg Due To TDP Chg', 'calc|(measure_$baseDolSalesYAMeasID/measure_$tdpYAMeasID)*measure_$tdpCYAMeasID|', '2,1,1,4')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the dollar share of category if one doesn't
# already exist, and return a selection scriptlet for it.
#

sub autorpt_create_meas_dollar_shr_cat
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($dolSalesMeasID, $catSegID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_dollar_shr_category($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure \$ Shr of Category");

    $dolSalesMeasID = autorpt_find_meas_dollars($db, $dsID);
    $catSegID = autorpt_find_seg_category($db, $dsID, 1);

    #create the calculated measure
    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('\$ Shr of Category', 'share|p|$dolSalesMeasID|seg|$catSegID|', '1,1,2,1')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the YA dollar share of category if one doesn't
# already exist, and return a selection scriptlet for it.
#

sub autorpt_create_meas_dollar_shr_cat_ya
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($dolSalesYAMeasID, $catSegID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_dollar_shr_category_ya($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure \$ Shr of Category YA");

    $dolSalesYAMeasID = autorpt_create_meas_dollars_ya($db, $dsID);
    $catSegID = autorpt_find_seg_category($db, $dsID, 1);

    #create the calculated measure
    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('\$ Shr of Category YA', 'share|p|$dolSalesYAMeasID|seg|$catSegID|', '1,1,2,1')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the year over year change in dollar share of
# category if one doesn't already exist.
#

sub autorpt_create_meas_dollar_shr_cat_cya
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($dolShareCatMeasID, $dolShareCatYAMeasID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_dollar_shr_category_cya($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure \$ Shr of Category CYA");

    $dolShareCatMeasID = autorpt_create_meas_dollar_shr_cat($db, $dsID);
    $dolShareCatYAMeasID = autorpt_create_meas_dollar_shr_cat_ya($db, $dsID);

    #create the calculated measure
    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('\$ Shr of Category CYA', 'difference|$dolShareCatMeasID|$dolShareCatYAMeasID|', '2,1,0,1')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the incremental dollar sales if one doesn't
# already exist, and return a selection scriptlet for it.
#

sub autorpt_create_meas_incr_dollars
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($dolSalesMeasID, $baseDolSalesMeasID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_incr_dollars($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure Incr \$");

    $dolSalesMeasID = autorpt_find_meas_dollars($db, $dsID);
    $baseDolSalesMeasID = autorpt_find_meas_base_dollars($db, $dsID);

    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('Incr \$', 'difference|$dolSalesMeasID|$baseDolSalesMeasID|', '2,1,1,4')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the incremental $ sales CYA if one doesn't
# already exist.
#

sub autorpt_create_meas_incr_dollars_cya
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($salesMeasID, $salesMeasYAID, $incrDolMeasID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_incr_dollars_cya($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure Incr \$ CYA");

    #see if we can do the calc as a difference
    undef($query);
    $salesMeasID = autorpt_create_meas_incr_dollars($db, $dsID);
    $salesMeasYAID = autorpt_create_meas_incr_dollars_ya($db, $dsID);
    if (($salesMeasID > 0) && ($salesMeasYAID > 0))
    {
      $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
          VALUES ('Incr \$ CYA', 'difference|$salesMeasID|$salesMeasYAID|', '2,1,1,4')";
    }

    #if not, see if we can do it as a change
    if (!defined($query))
    {
      $incrDolMeasID = autorpt_create_meas_incr_dollars($db, $dsID);
      if ($incrDolMeasID > 0)
      {
        $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
            VALUES ('Incr \$ CYA', 'change|$incrDolMeasID|1|year|', '2,1,1,4')";
      }
    }

    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the incremental dollar sales YA if one doesn't
# already exist.
#

sub autorpt_create_meas_incr_dollars_ya
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($dolSalesMeasYAID, $baseDolSalesMeasYAID, $incrDolMeasID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_incr_dollars_ya($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure Incr \$ YA");

    #see if we can do the calc as a difference
    undef($query);
    $dolSalesMeasYAID = autorpt_find_meas_dollars_ya($db, $dsID);
    $baseDolSalesMeasYAID = autorpt_create_meas_base_dollars_ya($db, $dsID);
    if (($dolSalesMeasYAID > 0) && ($baseDolSalesMeasYAID > 0))
    {
      $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
          VALUES ('Incr \$ YA', 'difference|$dolSalesMeasYAID|$baseDolSalesMeasYAID|', '2,1,1,4')";
    }

    #if not, see if we can do it as a lag
    if (!defined($query))
    {
      $incrDolMeasID = autorpt_create_meas_incr_dollars($db, $dsID);
      if ($incrDolMeasID > 0)
      {
        $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
            VALUES ('Incr \$ YA', 'lag|$incrDolMeasID|1|year|', '2,1,1,4')";
      }
    }

    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the Incr $ share of category if one doesn't
# already exist.
#

sub autorpt_create_meas_incr_dollars_shr_cat
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($dolSalesMeasID, $catSegID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_incr_dollars_shr_cat($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure Incr \$ Shr of Category");

    $dolSalesMeasID = autorpt_find_meas_incr_dollars($db, $dsID);
    $catSegID = autorpt_find_seg_category($db, $dsID, 1);

    #create the calculated measure
    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('Incr \$ Shr of Category', 'share|p|$dolSalesMeasID|seg|$catSegID|', '1,1,2,1')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the Incr $ YA share of category if one doesn't
# already exist.
#

sub autorpt_create_meas_incr_dollars_shr_cat_ya
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($dolSalesYAMeasID, $catSegID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_incr_dollars_shr_cat_ya($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure Incr \$ Shr of Category YA");

    $dolSalesYAMeasID = autorpt_create_meas_incr_dollars_ya($db, $dsID);
    $catSegID = autorpt_find_seg_category($db, $dsID, 1);

    #create the calculated measure
    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('Incr \$ Shr of Category YA', 'share|p|$dolSalesYAMeasID|seg|$catSegID|', '1,1,2,1')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the year over year change in Incr $ share of
# category if one doesn't already exist.
#

sub autorpt_create_meas_incr_dollars_shr_cat_cya
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($dolShareCatMeasID, $dolShareCatYAMeasID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_incr_dollars_shr_cat_cya($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure Incr \$ Shr of Category CYA");

    $dolShareCatMeasID = autorpt_create_meas_incr_dollars_shr_cat($db, $dsID);
    $dolShareCatYAMeasID = autorpt_create_meas_incr_dollars_shr_cat_ya($db, $dsID);

    #create the calculated measure
    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('Incr \$ Shr of Category CYA', 'difference|$dolShareCatMeasID|$dolShareCatYAMeasID|', '2,1,0,1')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the incremental unit sales if one doesn't
# already exist, and return a selection scriptlet for it.
#

sub autorpt_create_meas_incr_units
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($unitSalesMeasID, $baseUnitSalesMeasID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_incr_units($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure Incr Units");

    $unitSalesMeasID = autorpt_find_meas_units($db, $dsID);
    $baseUnitSalesMeasID = autorpt_find_meas_base_units($db, $dsID);

    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('Incr Units', 'difference|$unitSalesMeasID|$baseUnitSalesMeasID|', '2,1,1,4')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing % $ Any Promo if one doesn't
# already exist, and return its ID.
#

sub autorpt_create_meas_pct_dollars_any_promo
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($promoDolSalesMeasID, $dolSalesMeasID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_pct_dollars_any_promo($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure % \$ Base");

    $promoDolSalesMeasID = autorpt_create_meas_any_promo_dollars($db, $dsID);
    $dolSalesMeasID = autorpt_find_meas_dollars($db, $dsID);

    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('\% \$ Any Promo', 'ratio|$promoDolSalesMeasID|$dolSalesMeasID|1|', '1,1,2,4')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the pct of dollars from base sales if one doesn't
# already exist, and return its ID.
#

sub autorpt_create_meas_pct_dollars_base
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($dolSalesMeasID, $baseDolSalesMeasID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_pct_dollars_base($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure % \$ Base");

    $dolSalesMeasID = autorpt_find_meas_dollars($db, $dsID);
    $baseDolSalesMeasID = autorpt_find_meas_base_dollars($db, $dsID);

    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('\% \$ Base', 'ratio|$baseDolSalesMeasID|$dolSalesMeasID|1|', '1,1,2,4')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the pct of dollars from incr sales if one doesn't
# already exist, and return its ID.
#

sub autorpt_create_meas_pct_dollars_incr
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($dolSalesMeasID, $incrDolSalesMeasID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_pct_dollars_incr($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure % \$ Incr");

    $dolSalesMeasID = autorpt_find_meas_dollars($db, $dsID);
    $incrDolSalesMeasID = autorpt_create_meas_incr_dollars($db, $dsID);

    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('\% \$ Incr', 'ratio|$incrDolSalesMeasID|$dolSalesMeasID|1|', '1,1,2,4')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing CWW CYA for the specified promotional vehicle,
# if one doesn't already exist.
#

sub autorpt_create_meas_promo_vehicle_cww_cya
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($promoMeasName, $promoMeasID);

  my ($db, $dsID, $promoType) = @_;

  $dsSchema = "datasource_" . $dsID;
  $promoMeasName = $promoNameMapping{$promoType} . " CWW CYA";

  #see if such a measure already exists
  $measureID = autorpt_find_meas_promo_vehicle_cww_cya($db, $dsID, $promoType);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure $promoMeasName");

    $promoMeasID = autorpt_find_meas_promo_vehicle_cww($db, $dsID, $promoType);

    if ($promoMeasID > 0)
    {
      $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
          VALUES ('$promoMeasName', 'change|$promoMeasID|1|year|', '2,1,0,1')";
      $status = $db->do($query);
      autorpt_db_err($db, $status, $query);
      $measureID = $db->{q{mysql_insertid}};

      $colName = "measure_" . $measureID;
      $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
      $status = $db->do($query);
      autorpt_db_err($db, $status, $query);

      DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
    }
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing $ % Lift CYA for the specified promotional vehicle,
# if one doesn't already exist.
#

sub autorpt_create_meas_promo_vehicle_dollar_pct_lift_cya
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($promoMeasName, $promoMeasID);

  my ($db, $dsID, $promoType) = @_;

  $dsSchema = "datasource_" . $dsID;
  $promoMeasName = $promoNameMapping{$promoType} . " \$ % Lift CYA";

  #see if such a measure already exists
  $measureID = autorpt_find_meas_promo_vehicle_dollar_pct_lift_cya($db, $dsID, $promoType);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure $promoMeasName");

    $promoMeasID = autorpt_find_meas_promo_vehicle_dollar_pct_lift($db, $dsID, $promoType);

    if ($promoMeasID > 0)
    {
      $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
          VALUES ('$promoMeasName', 'change|$promoMeasID|1|year|', '2,1,0,1')";
      $status = $db->do($query);
      autorpt_db_err($db, $status, $query);
      $measureID = $db->{q{mysql_insertid}};

      $colName = "measure_" . $measureID;
      $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
      $status = $db->do($query);
      autorpt_db_err($db, $status, $query);

      DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
    }
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing Incr $ for the specified promotional vehicle,
# if one doesn't already exist.
#

sub autorpt_create_meas_promo_vehicle_incr_dollars
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($promoMeasName, $promoDollarMeasID, $promoDollarBaseMeasID);

  my ($db, $dsID, $promoType) = @_;

  $dsSchema = "datasource_" . $dsID;
  $promoMeasName = $promoNameMapping{$promoType} . " Incr \$";

  #see if such a measure already exists
  $measureID = autorpt_find_meas_promo_vehicle_incr_dollars($db, $dsID, $promoType);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure $promoMeasName");

    $promoDollarMeasID = autorpt_find_meas_promo_vehicle_dollars($db, $dsID, $promoType);
    $promoDollarBaseMeasID = autorpt_find_meas_promo_vehicle_base_dollars($db, $dsID, $promoType);

    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('$promoMeasName', 'difference|$promoDollarMeasID|$promoDollarBaseMeasID|', '2,1,1,4')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing Incr $ CYA for the specified promotional vehicle,
# if one doesn't already exist.
#

sub autorpt_create_meas_promo_vehicle_incr_dollars_cya
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($promoMeasName, $promoDollarMeasID);

  my ($db, $dsID, $promoType) = @_;

  $dsSchema = "datasource_" . $dsID;
  $promoMeasName = $promoNameMapping{$promoType} . " Incr \$ CYA";

  #see if such a measure already exists
  $measureID = autorpt_find_meas_promo_vehicle_incr_dollars_cya($db, $dsID, $promoType);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure $promoMeasName");

    $promoDollarMeasID = autorpt_create_meas_promo_vehicle_incr_dollars($db, $dsID, $promoType);

    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('$promoMeasName', 'change|$promoDollarMeasID|1|year|', '2,1,1,4')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing Incr $ YA for the specified promotional vehicle,
# if one doesn't already exist.
#

sub autorpt_create_meas_promo_vehicle_incr_dollars_ya
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($promoMeasName, $promoDollarMeasID);

  my ($db, $dsID, $promoType) = @_;

  $dsSchema = "datasource_" . $dsID;
  $promoMeasName = $promoNameMapping{$promoType} . " Incr \$ YA";

  #see if such a measure already exists
  $measureID = autorpt_find_meas_promo_vehicle_incr_dollars_ya($db, $dsID, $promoType);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure $promoMeasName");

    $promoDollarMeasID = autorpt_create_meas_promo_vehicle_incr_dollars($db, $dsID, $promoType);

    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('$promoMeasName', 'lag|$promoDollarMeasID|1|year|', '2,1,1,4')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing Incr $ Shr of Cat for the specified promotional
# vehicle.
#

sub autorpt_create_meas_promo_vehicle_incr_dollars_shr_cat
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($promoMeasName, $catSegID, $promoIncrMeasID);

  my ($db, $dsID, $promoType) = @_;

  $dsSchema = "datasource_" . $dsID;
  $promoMeasName = $promoNameMapping{$promoType} . " Incr \$ Shr of Cat";

  #see if such a measure already exists
  $measureID = autorpt_find_meas_promo_vehicle_incr_dollars_shr_cat($db, $dsID, $promoType);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure $promoMeasName");

    $promoIncrMeasID = autorpt_create_meas_promo_vehicle_incr_dollars($db, $dsID, $promoType);
    $catSegID = autorpt_find_seg_category($db, $dsID, 1);

    #create the calculated measure
    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('$promoMeasName', 'share|p|$promoIncrMeasID|seg|$catSegID|', '1,1,0,4')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing Incr $ Shr of Cat CYA for the specified promotional
# vehicle.
#

sub autorpt_create_meas_promo_vehicle_incr_dollars_shr_cat_cya
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($promoMeasName, $dolShareMeasID, $dolShareYAMeasID, $catSegID);

  my ($db, $dsID, $promoType) = @_;

  $dsSchema = "datasource_" . $dsID;
  $promoMeasName = $promoNameMapping{$promoType} . " Incr \$ Shr of Cat CYA";

  #see if such a measure already exists
  $measureID = autorpt_find_meas_promo_vehicle_incr_dollars_shr_cat_cya($db, $dsID, $promoType);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure $promoMeasName");

    $dolShareMeasID = autorpt_create_meas_promo_vehicle_incr_dollars_shr_cat($db, $dsID, $promoType);
    $dolShareYAMeasID = autorpt_create_meas_promo_vehicle_incr_dollars_shr_cat_ya($db, $dsID, $promoType);
    $catSegID = autorpt_find_seg_category($db, $dsID, 1);

    #create the calculated measure
    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('$promoMeasName', 'difference|$dolShareMeasID|$dolShareYAMeasID|', '1,1,0,4')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing Incr $ Shr of Cat YA for the specified promotional
# vehicle.
#

sub autorpt_create_meas_promo_vehicle_incr_dollars_shr_cat_ya
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($promoMeasName, $promoIncrMeasID, $catSegID);

  my ($db, $dsID, $promoType) = @_;

  $dsSchema = "datasource_" . $dsID;
  $promoMeasName = $promoNameMapping{$promoType} . " Incr \$ Shr of Cat YA";

  #see if such a measure already exists
  $measureID = autorpt_find_meas_promo_vehicle_incr_dollars_shr_cat_ya($db, $dsID, $promoType);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure $promoMeasName");

    $promoIncrMeasID = autorpt_create_meas_promo_vehicle_incr_dollars_shr_cat($db, $dsID, $promoType);
    $catSegID = autorpt_find_seg_category($db, $dsID, 1);

    #create the calculated measure
    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('$promoMeasName', 'share|p|$promoIncrMeasID|seg|$catSegID|', '1,1,0,4')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing Unit Price CYA for the specified promotional vehicle,
# if one doesn't already exist.
#

sub autorpt_create_meas_promo_vehicle_unit_price_cya
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($promoMeasName, $promoMeasID);

  my ($db, $dsID, $promoType) = @_;

  $dsSchema = "datasource_" . $dsID;
  $promoMeasName = $promoNameMapping{$promoType} . " Unit Price CYA";

  #see if such a measure already exists
  $measureID = autorpt_find_meas_promo_vehicle_unit_price_cya($db, $dsID, $promoType);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure $promoMeasName");

    $promoMeasID = autorpt_find_meas_promo_vehicle_unit_price($db, $dsID, $promoType);

    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('$promoMeasName', 'change|$promoMeasID|1|year|', '2,1,1,4')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing Unit Price % Disc CYA for the specified
# promotional vehicle, if one doesn't already exist.
#

sub autorpt_create_meas_promo_vehicle_unit_price_pct_disc_cya
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($promoMeasName, $promoMeasID);

  my ($db, $dsID, $promoType) = @_;

  $dsSchema = "datasource_" . $dsID;
  $promoMeasName = $promoNameMapping{$promoType} . " Unit Price % Disc CYA";

  #see if such a measure already exists
  $measureID = autorpt_find_meas_promo_vehicle_unit_price_pct_disc_cya($db, $dsID, $promoType);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure $promoMeasName");

    $promoMeasID = autorpt_find_meas_promo_vehicle_unit_price_pct_disc($db, $dsID, $promoType);

    if ($promoMeasID > 0)
    {
      $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
          VALUES ('$promoMeasName', 'change|$promoMeasID|1|year|', '2,1,0,1')";
      $status = $db->do($query);
      autorpt_db_err($db, $status, $query);
      $measureID = $db->{q{mysql_insertid}};

      $colName = "measure_" . $measureID;
      $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
      $status = $db->do($query);
      autorpt_db_err($db, $status, $query);

      DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
    }
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the total distribution points if one doesn't
# already exist, and return a selection scriptlet for it.
#

sub autorpt_create_meas_tdp
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($acvReachMeasID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_tdp($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure TDP");

    $acvReachMeasID = autorpt_find_meas_acv_reach($db, $dsID);

    $query = "INSERT INTO $dsSchema.measures (name, calculation, calcBeforeAgg, prodAggRule, geoAggRule, timeAggRule, format) \
        VALUES ('TDP', 'sum|$acvReachMeasID|', 1, 'SUM', 'SUM', 'SUM', '1,1,0,4')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the total distribution points CYA if one doesn't
# already exist, and return a selection scriptlet for it.
#

sub autorpt_create_meas_tdp_cya
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($tdpMeasID, $tdpYAMeasID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_tdp_cya($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure TDP CYA");

    $tdpMeasID = autorpt_create_meas_tdp($db, $dsID);
    $tdpYAMeasID = autorpt_create_meas_tdp_ya($db, $dsID);

    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('TDP CYA', 'difference|$tdpMeasID|$tdpYAMeasID|', '1,1,0,4')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the total distribution points YA if one doesn't
# already exist, and return a selection scriptlet for it.
#

sub autorpt_create_meas_tdp_ya
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($acvReachYAMeasID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_tdp_ya($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    $acvReachYAMeasID = autorpt_find_meas_acv_reach_ya($db, $dsID);

    KAPutil_job_update_status($db, "Creating measure TDP YA");

    $query = "INSERT INTO $dsSchema.measures (name, calculation, calcBeforeAgg, prodAggRule, geoAggRule, timeAggRule, format) \
        VALUES ('TDP YA', 'sum|$acvReachYAMeasID|', 1, 'SUM', 'SUM', 'SUM', '1,1,0,4')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the change in unit sales if one doesn't
# already exist, and return a selection scriptlet for it.
#

sub autorpt_create_meas_units_cya
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($unitSalesMeasID, $unitSalesYAMeasID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_units_cya($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure Units CYA");

    $unitSalesMeasID = autorpt_find_meas_units($db, $dsID);
    $unitSalesYAMeasID = autorpt_find_meas_units_ya($db, $dsID);

    #if there is a YA measure, create a difference between the two measures
    #(faster calc)
    if (($unitSalesYAMeasID > 0) && ($unitSalesMeasID > 0))
    {
      $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
          VALUES ('Units CYA', 'difference|$unitSalesMeasID|$unitSalesYAMeasID|', '2,1,0,1')";
      $status = $db->do($query);
      autorpt_db_err($db, $status, $query);
      $measureID = $db->{q{mysql_insertid}};

      $colName = "measure_" . $measureID;
      $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
      $status = $db->do($query);
      autorpt_db_err($db, $status, $query);

      DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
    }

    #if we have at least a unit sales measure, do a straight change
    elsif ($unitSalesMeasID > 0)
    {
      $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
          VALUES ('Unit CYA', 'change|$unitSalesMeasID|1|year|', '2,1,0,1')";
      $status = $db->do($query);
      autorpt_db_err($db, $status, $query);
      $measureID = $db->{q{mysql_insertid}};

      $colName = "measure_" . $measureID;
      $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
      $status = $db->do($query);
      autorpt_db_err($db, $status, $query);

      DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
    }
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the % change in unit sales if one doesn't
# already exist, and return a selection scriptlet for it.
#

sub autorpt_create_meas_units_pctchg_ya
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($unitSalesYAMeasID, $unitSalesMeasID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_units_pctchg_ya($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure Units \%Chg YA");

    $unitSalesMeasID = autorpt_find_meas_units($db, $dsID);
    $unitSalesYAMeasID = autorpt_find_meas_units_ya($db, $dsID);

    #if there is a YA measure, create a pct % between measures (faster calc)
    if (($unitSalesYAMeasID > 0) && ($unitSalesMeasID > 0))
    {
      $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
          VALUES ('Units \%Chg YA', 'pct_change_meas|$unitSalesMeasID|$unitSalesYAMeasID|', '1,1,2,1')";
      $status = $db->do($query);
      autorpt_db_err($db, $status, $query);
      $measureID = $db->{q{mysql_insertid}};

      $colName = "measure_" . $measureID;
      $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
      $status = $db->do($query);
      autorpt_db_err($db, $status, $query);

      DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
    }

    #if we have at least a unit sales measure, do a straight % change
    elsif ($unitSalesMeasID > 0)
    {
      $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
          VALUES ('Units \%Chg YA', 'pct_change|$unitSalesMeasID|1|year|', '1,1,2,1')";
      $status = $db->do($query);
      autorpt_db_err($db, $status, $query);
      $measureID = $db->{q{mysql_insertid}};

      $colName = "measure_" . $measureID;
      $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
      $status = $db->do($query);
      autorpt_db_err($db, $status, $query);

      DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
    }
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the unit share of category if one doesn't
# already exist, and return a selection scriptlet for it.
#

sub autorpt_create_meas_units_shr_cat
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($unitSalesMeasID, $catSegID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_units_shr_category($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure Units Shr of Category");

    $unitSalesMeasID = autorpt_find_meas_units($db, $dsID);
    $catSegID = autorpt_find_seg_category($db, $dsID, 1);

    #create the calculated measure
    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('Units Shr of Category', 'share|p|$unitSalesMeasID|seg|$catSegID|', '1,1,2,1')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the YA unit share of category if one doesn't
# already exist, and return a selection scriptlet for it.
#

sub autorpt_create_meas_units_shr_cat_ya
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($unitSalesYAMeasID, $catSegID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_units_shr_category_ya($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure Units Shr of Category YA");

    $unitSalesYAMeasID = autorpt_find_meas_units_ya($db, $dsID);
    $catSegID = autorpt_find_seg_category($db, $dsID, 1);

    #create the calculated measure
    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('Units Shr of Category YA', 'share|p|$unitSalesYAMeasID|seg|$catSegID|', '1,1,2,1')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Create a measure containing the year over year change in unit share of
# category if one doesn't already exist.
#

sub autorpt_create_meas_units_shr_cat_cya
{
  my ($query, $dbOutput, $status, $dsSchema, $measureID, $colName);
  my ($unitShareCatMeasID, $unitShareCatYAMeasID);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #see if such a measure already exists
  $measureID = autorpt_find_meas_units_shr_category_cya($db, $dsID);

  #if we didn't find an already existing measure, try calculating it
  if ($measureID < 1)
  {
    KAPutil_job_update_status($db, "Creating measure Unit Share of Category CYA");

    $unitShareCatMeasID = autorpt_create_meas_units_shr_cat($db, $dsID);
    $unitShareCatYAMeasID = autorpt_create_meas_units_shr_cat_ya($db, $dsID);

    #create the calculated measure
    $query = "INSERT INTO $dsSchema.measures (name, calculation, format) \
        VALUES ('Units Shr of Category CYA', 'difference|$unitShareCatMeasID|$unitShareCatYAMeasID|', '2,1,0,1')";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);
    $measureID = $db->{q{mysql_insertid}};

    $colName = "measure_" . $measureID;
    $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
    $status = $db->do($query);
    autorpt_db_err($db, $status, $query);

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID);
  }

  return($measureID);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for %ACV Reach
#

sub autorpt_prereqs_meas_acv_reach
{
  my ($measureID);

  my ($db, $dsID, $prereqHTML) = @_;

  $measureID = autorpt_find_meas_acv_reach($db, $dsID);
  if ($measureID < 1)
  {
    $prereqHTML .= "<LI>\%ACV Reach measure</LI>\n";
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for %ACV Reach CYA
#

sub autorpt_prereqs_meas_acv_reach_cya
{
  my ($measureID);

  my ($db, $dsID, $prereqHTML) = @_;

  $measureID = autorpt_find_meas_acv_reach_cya($db, $dsID);
  if ($measureID < 1)
  {
    $measureID = autorpt_find_meas_acv_reach($db, $dsID);
    if ($measureID < 1)
    {
      $prereqHTML .= "<LI>\%ACV Reach measure</LI>\n";
    }
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for ACV %Reach %Chg YA measure
#

sub autorpt_prereqs_meas_acv_reach_pctchg_ya
{
  my ($measureID);

  my ($db, $dsID, $prereqHTML) = @_;

  $measureID = autorpt_find_meas_acv_reach_pctchg_ya($db, $dsID);
  if ($measureID < 1)
  {
    $measureID = autorpt_find_meas_acv_reach($db, $dsID);
    if ($measureID < 1)
    {
      $prereqHTML .= "<LI>\%ACV Reach measure</LI>\n";
    }
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for Any Promo $ measure
#

sub autorpt_prereqs_meas_any_promo_dollars
{
  my ($measureID);

  my ($db, $dsID, $prereqHTML) = @_;

  $measureID = autorpt_find_meas_any_promo_dollars($db, $dsID);
  if ($measureID < 1)
  {
    $measureID = autorpt_find_meas_promo_vehicle_dollars($db, $dsID, "disp wo feat");
    if ($measureID < 1)
    {
      $prereqHTML .= "<LI>Disp w/o Feat \$ measure</LI>\n";
    }
    $measureID = autorpt_find_meas_promo_vehicle_dollars($db, $dsID, "feat and disp");
    if ($measureID < 1)
    {
      $prereqHTML .= "<LI>Feat & Disp \$ measure</LI>\n";
    }
    $measureID = autorpt_find_meas_promo_vehicle_dollars($db, $dsID, "feat wo disp");
    if ($measureID < 1)
    {
      $prereqHTML .= "<LI>Feat w/o Disp \$ measure</LI>\n";
    }
    $measureID = autorpt_find_meas_promo_vehicle_dollars($db, $dsID, "price decr");
    if ($measureID < 1)
    {
      $prereqHTML .= "<LI>Price Decr \$ measure</LI>\n";
    }
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for Any Promo Incr $ measure
#

sub autorpt_prereqs_meas_any_promo_incr_dollars
{
  my ($measureID);

  my ($db, $dsID, $prereqHTML) = @_;

  $measureID = autorpt_find_meas_any_promo_incr_dollars($db, $dsID);
  if ($measureID < 1)
  {
    $measureID = autorpt_find_meas_promo_vehicle_incr_dollars($db, $dsID, "disp wo feat");
    if ($measureID < 1)
    {
      $prereqHTML .= "<LI>Disp w/o Feat Incr \$ measure</LI>\n";
    }
    $measureID = autorpt_find_meas_promo_vehicle_incr_dollars($db, $dsID, "feat and disp");
    if ($measureID < 1)
    {
      $prereqHTML .= "<LI>Feat & Disp Incr \$ measure</LI>\n";
    }
    $measureID = autorpt_find_meas_promo_vehicle_incr_dollars($db, $dsID, "feat wo disp");
    if ($measureID < 1)
    {
      $prereqHTML .= "<LI>Feat w/o Disp Incr \$ measure</LI>\n";
    }
    $measureID = autorpt_find_meas_promo_vehicle_incr_dollars($db, $dsID, "price decr");
    if ($measureID < 1)
    {
      $prereqHTML .= "<LI>Price Decr Incr \$ measure</LI>\n";
    }
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for Any Promo Units measure
#

sub autorpt_prereqs_meas_any_promo_units
{
  my ($measureID);

  my ($db, $dsID, $prereqHTML) = @_;

  $measureID = autorpt_find_meas_any_promo_unitss($db, $dsID);
  if ($measureID < 1)
  {
    $measureID = autorpt_find_meas_promo_vehicle_units($db, $dsID, "disp wo feat");
    if ($measureID < 1)
    {
      $prereqHTML .= "<LI>Disp w/o Feat Units measure</LI>\n";
    }
    $measureID = autorpt_find_meas_promo_vehicle_units($db, $dsID, "feat and disp");
    if ($measureID < 1)
    {
      $prereqHTML .= "<LI>Feat & Disp Units measure</LI>\n";
    }
    $measureID = autorpt_find_meas_promo_vehicle_units($db, $dsID, "feat wo disp");
    if ($measureID < 1)
    {
      $prereqHTML .= "<LI>Feat w/o Disp Units measure</LI>\n";
    }
    $measureID = autorpt_find_meas_promo_vehicle_units($db, $dsID, "price decr");
    if ($measureID < 1)
    {
      $prereqHTML .= "<LI>Price Decr Units measure</LI>\n";
    }
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for Avg Num Items
#

sub autorpt_prereqs_meas_avg_num_items
{
  my ($measureID);

  my ($db, $dsID, $prereqHTML) = @_;

  $measureID = autorpt_find_meas_avg_num_items($db, $dsID);
  if ($measureID < 1)
  {
    $measureID = autorpt_find_meas_acv_reach($db, $dsID);
    if ($measureID < 1)
    {
      $prereqHTML .= "<LI>\%ACV Reach measure</LI>\n";
    }

    autorpt_prereqs_meas_tdp($db, $dsID, $prereqHTML);
  }
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for Avg Unit Price
#

sub autorpt_prereqs_meas_avg_unit_price
{
  my ($measureID);

  my ($db, $dsID, $prereqHTML) = @_;

  #make sure we havea $ measure
  $measureID = autorpt_find_meas_avg_unit_price($db, $dsID);
  if ($measureID < 1)
  {
    $measureID = autorpt_find_meas_dollars($db, $dsID);
    if ($measureID < 1)
    {
      $prereqHTML .= "<LI>Dollar Sales measure</LI>\n";
    }
    $measureID = autorpt_find_meas_units($db, $dsID);
    if ($measureID < 1)
    {
      $prereqHTML .= "<LI>Units measure</LI>\n";
    }
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for Base Unit Price %Chg YA measure
#

sub autorpt_prereqs_meas_avg_unit_price_pctchg_ya
{
  my ($measureID);

  my ($db, $dsID, $prereqHTML) = @_;

  $measureID = autorpt_find_meas_avg_unit_price_pctchg_ya($db, $dsID);
  if ($measureID < 1)
  {
    $measureID = autorpt_find_meas_dollars($db, $dsID);
    if ($measureID < 1)
    {
      $prereqHTML .= "<LI>Dollar Sales measure</LI>\n";
    }
    $measureID = autorpt_find_meas_units($db, $dsID);
    if ($measureID < 1)
    {
      $prereqHTML .= "<LI>Units measure</LI>\n";
    }
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for Base $
#

sub autorpt_prereqs_meas_base_dollars
{
  my ($measureID);

  my ($db, $dsID, $prereqHTML) = @_;

  #make sure we havea $ measure
  $measureID = autorpt_find_meas_base_dollars($db, $dsID);
  if ($measureID < 1)
  {
    $prereqHTML .= "<LI>Base Dollar Sales measure</LI>\n";
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for Base Units
#

sub autorpt_prereqs_meas_base_units
{
  my ($measureID);

  my ($db, $dsID, $prereqHTML) = @_;

  #make sure we havea $ measure
  $measureID = autorpt_find_meas_base_units($db, $dsID);
  if ($measureID < 1)
  {
    $prereqHTML .= "<LI>Base Unit Sales measure</LI>\n";
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for Base Unit Price
#

sub autorpt_prereqs_meas_base_unit_price
{
  my ($measureID);

  my ($db, $dsID, $prereqHTML) = @_;

  $measureID = autorpt_find_meas_base_unit_price($db, $dsID);
  if ($measureID < 1)
  {
    $measureID = autorpt_find_meas_base_dollars($db, $dsID);
    if ($measureID < 1)
    {
      $prereqHTML .= "<LI>Base Dollar Sales measure</LI>\n";
    }
    $measureID = autorpt_find_meas_base_units($db, $dsID);
    if ($measureID < 1)
    {
      $prereqHTML .= "<LI>Base Units measure</LI>\n";
    }
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for Base Unit Price %Chg YA measure
#

sub autorpt_prereqs_meas_base_unit_price_pctchg_ya
{
  my ($measureID);

  my ($db, $dsID, $prereqHTML) = @_;

  $measureID = autorpt_find_meas_base_unit_price_pctchg_ya($db, $dsID);
  if ($measureID < 1)
  {
    $measureID = autorpt_find_meas_base_units($db, $dsID);
    if ($measureID < 1)
    {
      $prereqHTML .= "<LI>Base Units measure</LI>\n";
    }
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for $
#

sub autorpt_prereqs_meas_dollars
{
  my ($measureID);

  my ($db, $dsID, $prereqHTML) = @_;

  #make sure we havea $ measure
  $measureID = autorpt_find_meas_dollars($db, $dsID);
  if ($measureID < 1)
  {
    $prereqHTML .= "<LI>Dollar Sales measure</LI>\n";
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for $ Chg due to Base Velocity Chg
#

sub autorpt_prereqs_meas_dollar_change_base_velocity_change
{
  my ($measureID);

  my ($db, $dsID, $prereqHTML) = @_;

  $measureID = autorpt_find_meas_dollar_change_base_velocity_change($db, $dsID);
  if ($measureID < 1)
  {
    $measureID = autorpt_find_meas_base_dollars($db, $dsID, $prereqHTML);
    if ($measureID < 1)
    {
      $prereqHTML .= "<LI>Base \$ measure</LI>\n";
    }

    $prereqHTML = autorpt_prereqs_meas_tdp($db, $dsID, $prereqHTML);
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for $ Chg due to TDP Chg
#

sub autorpt_prereqs_meas_dollar_change_tdp_change
{
  my ($measureID);

  my ($db, $dsID, $prereqHTML) = @_;

  $measureID = autorpt_find_meas_dollar_change_tdp_change($db, $dsID);
  if ($measureID < 1)
  {
    $measureID = autorpt_find_meas_base_dollars($db, $dsID, $prereqHTML);
    if ($measureID < 1)
    {
      $prereqHTML .= "<LI>Base \$ measure</LI>\n";
    }

    $prereqHTML = autorpt_prereqs_meas_tdp($db, $dsID, $prereqHTML);
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for $ CYA measure
#

sub autorpt_prereqs_meas_dollars_cya
{
  my ($measureID);

  my ($db, $dsID, $prereqHTML) = @_;

  $measureID = autorpt_find_meas_dollars_cya($db, $dsID);
  if ($measureID < 1)
  {
    $measureID = autorpt_find_meas_dollars($db, $dsID);
    if ($measureID < 1)
    {
      $prereqHTML .= "<LI>Dollar Sales measure</LI>\n";
    }
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for $ %Chg YA measure
#

sub autorpt_prereqs_meas_dollar_pctchg_ya
{
  my ($measureID);

  my ($db, $dsID, $prereqHTML) = @_;

  $measureID = autorpt_find_meas_dollar_pctchg_ya($db, $dsID);
  if ($measureID < 1)
  {
    $measureID = autorpt_find_meas_dollars($db, $dsID);
    if ($measureID < 1)
    {
      $prereqHTML .= "<LI>Dollar Sales measure</LI>\n";
    }
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for Incr $ measure
#

sub autorpt_prereqs_meas_dollar_pct_lift
{
  my ($measureID, $dolSalesMeasID, $baseMeasureID);

  my ($db, $dsID, $prereqHTML) = @_;

  $measureID = autorpt_find_meas_dollar_pct_lift($db, $dsID);
  if ($measureID < 1)
  {
    $dolSalesMeasID = autorpt_find_meas_dollars($db, $dsID);
    $baseMeasureID = autorpt_find_meas_base_dollars($db, $dsID, 1);
    if ($dolSalesMeasID < 1)
    {
      $prereqHTML .= "<LI>Dollar Sales measure</LI>\n";
    }
    if ($baseMeasureID < 1)
    {
      $prereqHTML .= "<LI>Base Dollar Sales measure</LI>\n";
    }
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for $ Shr Category measure
#

sub autorpt_prereqs_meas_dollar_shr_category
{
  my ($measureID, $catSegID);

  my ($db, $dsID, $prereqHTML) = @_;

  $measureID = autorpt_find_meas_dollar_shr_category($db, $dsID);
  if ($measureID < 1)
  {
    $measureID = autorpt_find_meas_dollars($db, $dsID);
    $catSegID = autorpt_find_seg_category($db, $dsID, 1);
    if ($measureID < 1)
    {
      $prereqHTML .= "<LI>Dollar Sales measure</LI>\n";
    }
    if ($catSegID < 1)
    {
      $prereqHTML .= "<LI>Category segmentation</LI>\n";
    }
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for $ Shr Category CYA measure
#

sub autorpt_prereqs_meas_dollar_shr_category_cya
{
  my ($measureID, $catSegID);

  my ($db, $dsID, $prereqHTML) = @_;

  $measureID = autorpt_find_meas_dollar_shr_category_cya($db, $dsID);
  if ($measureID < 1)
  {
    $measureID = autorpt_find_meas_dollars($db, $dsID);
    $catSegID = autorpt_find_seg_category($db, $dsID, 1);
    if ($measureID < 1)
    {
      $prereqHTML .= "<LI>Dollar Sales measure</LI>\n";
    }
    if ($catSegID < 1)
    {
      $prereqHTML .= "<LI>Category segmentation</LI>\n";
    }
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for Incr $ measure
#

sub autorpt_prereqs_meas_incr_dollars
{
  my ($measureID, $dolSalesMeasID, $baseMeasureID);

  my ($db, $dsID, $prereqHTML) = @_;

  $measureID = autorpt_find_meas_incr_dollars($db, $dsID);
  if ($measureID < 1)
  {
    $dolSalesMeasID = autorpt_find_meas_dollars($db, $dsID);
    $baseMeasureID = autorpt_find_meas_base_dollars($db, $dsID, 1);
    if ($dolSalesMeasID < 1)
    {
      $prereqHTML .= "<LI>Dollar Sales measure</LI>\n";
    }
    if ($baseMeasureID < 1)
    {
      $prereqHTML .= "<LI>Base Dollar Sales measure</LI>\n";
    }
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for Incr $ YA measure
#

sub autorpt_prereqs_meas_incr_dollars_ya
{
  my ($measureID, $dolSalesMeasID, $baseMeasureID, $incrDolSalesMeasID);

  my ($db, $dsID, $prereqHTML) = @_;

  $measureID = autorpt_find_meas_incr_dollars_ya($db, $dsID);
  if ($measureID < 1)
  {
    $incrDolSalesMeasID = autorpt_find_meas_incr_dollars($db, $dsID);

    if ($incrDolSalesMeasID < 1)
    {
      $dolSalesMeasID = autorpt_find_meas_dollars_ya($db, $dsID);
      $baseMeasureID = autorpt_find_meas_base_dollars_ya($db, $dsID, 1);
      if ($dolSalesMeasID < 1)
      {
        $prereqHTML .= "<LI>Dollar Sales YA measure</LI>\n";
      }
      if ($baseMeasureID < 1)
      {
        $prereqHTML .= "<LI>Base Dollar Sales YA measure</LI>\n";
      }
    }
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for Incr $ Shr Category measure
#

sub autorpt_prereqs_meas_incr_dollars_shr_category
{
  my ($measureID, $catSegID);

  my ($db, $dsID, $prereqHTML) = @_;

  $measureID = autorpt_find_meas_incr_dollars_shr_cat($db, $dsID);
  if ($measureID < 1)
  {
    $measureID = autorpt_find_meas_incr_dollars($db, $dsID);
    $catSegID = autorpt_find_seg_category($db, $dsID, 1);
    if ($measureID < 1)
    {
      $prereqHTML .= "<LI>Incremental Dollar Sales measure</LI>\n";
    }
    if ($catSegID < 1)
    {
      $prereqHTML .= "<LI>Category segmentation</LI>\n";
    }
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for Incr Units measure
#

sub autorpt_prereqs_meas_incr_units
{
  my ($measureID, $unitSalesMeasID, $baseMeasureID);

  my ($db, $dsID, $prereqHTML) = @_;

  $measureID = autorpt_find_meas_incr_units($db, $dsID);
  if ($measureID < 1)
  {
    $unitSalesMeasID = autorpt_find_meas_units($db, $dsID);
    $baseMeasureID = autorpt_find_meas_base_units($db, $dsID, 1);
    if ($unitSalesMeasID < 1)
    {
      $prereqHTML .= "<LI>Unit Sales measure</LI>\n";
    }
    if ($baseMeasureID < 1)
    {
      $prereqHTML .= "<LI>Base Unit Sales measure</LI>\n";
    }
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for Incr Units YA measure
#

sub autorpt_prereqs_meas_incr_units_ya
{
  my ($measureID, $unitSalesMeasID, $baseMeasureID, $incrUnitSalesMeasID);

  my ($db, $dsID, $prereqHTML) = @_;

  $measureID = autorpt_find_meas_incr_units_ya($db, $dsID);
  if ($measureID < 1)
  {
    $incrUnitSalesMeasID = autorpt_find_meas_incr_units($db, $dsID);

    if ($incrUnitSalesMeasID < 1)
    {
      $unitSalesMeasID = autorpt_find_meas_units_ya($db, $dsID);
      $baseMeasureID = autorpt_find_meas_base_units_ya($db, $dsID, 1);
      if ($unitSalesMeasID < 1)
      {
        $prereqHTML .= "<LI>Unit Sales YA measure</LI>\n";
      }
      if ($baseMeasureID < 1)
      {
        $prereqHTML .= "<LI>Base Unit Sales YA measure</LI>\n";
      }
    }
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for Incr Units Shr Category measure
#

sub autorpt_prereqs_meas_incr_units_shr_category
{
  my ($measureID, $catSegID);

  my ($db, $dsID, $prereqHTML) = @_;

  $measureID = autorpt_find_meas_incr_units_shr_cat($db, $dsID);
  if ($measureID < 1)
  {
    $measureID = autorpt_find_meas_incr_units($db, $dsID);
    $catSegID = autorpt_find_seg_category($db, $dsID, 1);
    if ($measureID < 1)
    {
      $prereqHTML .= "<LI>Incremental Unit Sales measure</LI>\n";
    }
    if ($catSegID < 1)
    {
      $prereqHTML .= "<LI>Category segmentation</LI>\n";
    }
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for % $ base measure
#

sub autorpt_prereqs_meas_pct_dollars_base
{
  my ($measureID, $baseMeasureID);

  my ($db, $dsID, $prereqHTML) = @_;

  $measureID = autorpt_find_meas_pct_dollars_base($db, $dsID);
  if ($measureID < 1)
  {
    $measureID = autorpt_find_meas_dollars($db, $dsID);
    $baseMeasureID = autorpt_find_meas_base_dollars($db, $dsID, 1);
    if ($measureID < 1)
    {
      $prereqHTML .= "<LI>Dollar Sales measure</LI>\n";
    }
    if ($baseMeasureID < 1)
    {
      $prereqHTML .= "<LI>Base Dollar Sales measure</LI>\n";
    }
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for % $ incr measure
#

sub autorpt_prereqs_meas_pct_dollars_incr
{
  my ($measureID, $baseMeasureID);

  my ($db, $dsID, $prereqHTML) = @_;

  $measureID = autorpt_find_meas_pct_dollars_incr($db, $dsID);
  if ($measureID < 1)
  {
    $measureID = autorpt_find_meas_dollars($db, $dsID);
    $baseMeasureID = autorpt_find_meas_base_dollars($db, $dsID, 1);
    if ($measureID < 1)
    {
      $prereqHTML .= "<LI>Dollar Sales measure</LI>\n";
    }
    if ($baseMeasureID < 1)
    {
      $prereqHTML .= "<LI>Base Dollar Sales measure</LI>\n";
    }
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for the specified promo vehicle $
#

sub autorpt_prereqs_meas_promo_vehicle_dollars
{
  my ($measureID, $promoMeasName);

  my ($db, $dsID, $promoType, $prereqHTML) = @_;

  $measureID = autorpt_find_meas_promo_vehicle_dollars($db, $dsID, $promoType);
  if ($measureID < 1)
  {
    $promoMeasName = $promoNameMapping{$promoType};
    $prereqHTML .= "<LI>$promoMeasName \$ measure</LI>\n";
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for CWW in the specified
# promotional vehicle.
#

sub autorpt_prereqs_meas_promo_vehicle_cww
{
  my ($measureID, $promoMeasName);

  my ($db, $dsID, $promoType, $prereqHTML) = @_;

  $measureID = autorpt_find_meas_promo_vehicle_cww($db, $dsID, $promoType);
  if ($measureID < 1)
  {
    $promoMeasName = $promoNameMapping{$promoType} . " CWW";
    $prereqHTML .= "<LI>$promoMeasName measure</LI>\n";
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for $ % Lift in the specified
# promotional vehicle.
#

sub autorpt_prereqs_meas_promo_vehicle_dollar_pct_lift
{
  my ($measureID, $promoMeasName);

  my ($db, $dsID, $promoType, $prereqHTML) = @_;

  $measureID = autorpt_find_meas_promo_vehicle_dollar_pct_lift($db, $dsID, $promoType);
  if ($measureID < 1)
  {
    $promoMeasName = $promoNameMapping{$promoType} . " \$ % Lift";
    $prereqHTML .= "<LI>$promoMeasName measure</LI>\n";
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for Incr $ CYA in the specified
# promotional vehicle.
#

sub autorpt_prereqs_meas_promo_vehicle_incr_dollars_cya
{
  my ($measureID, $promoMeasName);

  my ($db, $dsID, $promoType, $prereqHTML) = @_;

  $measureID = autorpt_find_meas_promo_vehicle_incr_dollars_cya($db, $dsID, $promoType);
  if ($measureID < 1)
  {
    $measureID = autorpt_find_meas_promo_vehicle_incr_dollars($db, $dsID, $promoType);
    if ($measureID < 1)
    {
      $promoMeasName = $promoNameMapping{$promoType} . " Incr \$";
      $prereqHTML .= "<LI>$promoMeasName measure</LI>\n";
    }
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for Incr $ Shr Cat for the specified
# promotional vehicle.
#

sub autorpt_prereqs_meas_promo_vehicle_incr_dollars_shr_cat
{
  my ($measureID, $catSegID, $promoMeasName);

  my ($db, $dsID, $promoType, $prereqHTML) = @_;

  $measureID = autorpt_find_meas_promo_vehicle_incr_dollars_shr_cat($db, $dsID, $promoType);
  if ($measureID < 1)
  {
    $measureID = autorpt_find_meas_promo_vehicle_incr_dollars($db, $dsID, $promoType);
    $catSegID = autorpt_find_seg_category($db, $dsID, 1);
    if ($measureID < 1)
    {
      $promoMeasName = $promoNameMapping{$promoType} . " Incr \$";
      $prereqHTML .= "<LI>$promoMeasName measure</LI>\n";
    }
    if ($catSegID < 1)
    {
      $prereqHTML .= "<LI>Category segmentation</LI>\n";
    }
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for unit price in the specified
# promotional vehicle.
#

sub autorpt_prereqs_promo_vehicle_unit_price
{
  my ($measureID, $promoMeasName);

  my ($db, $dsID, $promoType, $prereqHTML) = @_;

  $promoMeasName = $promoNameMapping{$promoType} . " Unit Price";

  $measureID = autorpt_find_meas_promo_vehicle_unit_price($db, $dsID, $promoType);
  if ($measureID < 1)
  {
    $prereqHTML .= "<LI>$promoMeasName measure</LI>\n";
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for the specified promo vehicle units
#

sub autorpt_prereqs_meas_promo_vehicle_units
{
  my ($measureID, $promoMeasName);

  my ($db, $dsID, $promoType, $prereqHTML) = @_;

  $measureID = autorpt_find_meas_promo_vehicle_units($db, $dsID, $promoType);
  if ($measureID < 1)
  {
    $promoMeasName = $promoNameMapping{$promoType};
    $prereqHTML .= "<LI>$promoMeasName Units measure</LI>\n";
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for Unit Price % Disc for the specified
# promotional vehicle.
#

sub autorpt_prereqs_meas_promo_vehicle_unit_price_pct_disc
{
  my ($measureID, $promoMeasName);

  my ($db, $dsID, $promoType, $prereqHTML) = @_;

  $measureID = autorpt_find_meas_promo_vehicle_unit_price_pct_disc($db, $dsID, $promoType);
  if ($measureID < 1)
  {
    $promoMeasName = $promoNameMapping{$promoType} . " Unit Price % Disc";
    $prereqHTML .= "<LI>$promoMeasName measure</LI>\n";
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for TDP measure
#

sub autorpt_prereqs_meas_tdp
{
  my ($measureID);

  my ($db, $dsID, $prereqHTML) = @_;

  $measureID = autorpt_find_meas_tdp($db, $dsID);
  if ($measureID < 1)
  {
    $measureID = autorpt_find_meas_acv_reach($db, $dsID);
    if ($measureID < 1)
    {
      $prereqHTML .= "<LI>TDP measure</LI>\n";
    }
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for Units
#

sub autorpt_prereqs_meas_units
{
  my ($measureID);

  my ($db, $dsID, $prereqHTML) = @_;

  $measureID = autorpt_find_meas_units($db, $dsID);
  if ($measureID < 1)
  {
    $prereqHTML .= "<LI>Units measure</LI>\n";
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for Units CYA measure
#

sub autorpt_prereqs_meas_units_cya
{
  my ($measureID);

  my ($db, $dsID, $prereqHTML) = @_;

  $measureID = autorpt_find_meas_units_cya($db, $dsID);
  if ($measureID < 1)
  {
    $measureID = autorpt_find_meas_units($db, $dsID);
    if ($measureID < 1)
    {
      $prereqHTML .= "<LI>Unit Sales measure</LI>\n";
    }
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for Units %Chg YA measure
#

sub autorpt_prereqs_meas_units_pctchg_ya
{
  my ($measureID);

  my ($db, $dsID, $prereqHTML) = @_;

  $measureID = autorpt_find_meas_units_pctchg_ya($db, $dsID);
  if ($measureID < 1)
  {
    $measureID = autorpt_find_meas_units($db, $dsID);
    if ($measureID < 1)
    {
      $prereqHTML .= "<LI>Unit Sales measure</LI>\n";
    }
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for Unit Shr Category measure
#

sub autorpt_prereqs_meas_units_shr_category
{
  my ($measureID, $catSegID);

  my ($db, $dsID, $prereqHTML) = @_;

  $measureID = autorpt_find_meas_units_shr_category($db, $dsID);
  if ($measureID < 1)
  {
    $measureID = autorpt_find_meas_units($db, $dsID);
    $catSegID = autorpt_find_seg_category($db, $dsID, 1);
    if ($measureID < 1)
    {
      $prereqHTML .= "<LI>Units measure</LI>\n";
    }
    if ($catSegID < 1)
    {
      $prereqHTML .= "<LI>Category segmentation</LI>\n";
    }
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for Units Shr Category CYA measure
#

sub autorpt_prereqs_meas_units_shr_category_cya
{
  my ($measureID, $catSegID);

  my ($db, $dsID, $prereqHTML) = @_;

  $measureID = autorpt_find_meas_units_shr_category_cya($db, $dsID);
  if ($measureID < 1)
  {
    $measureID = autorpt_find_meas_units($db, $dsID);
    $catSegID = autorpt_find_seg_category($db, $dsID, 1);
    if ($measureID < 1)
    {
      $prereqHTML .= "<LI>Units measure</LI>\n";
    }
    if ($catSegID < 1)
    {
      $prereqHTML .= "<LI>Category segmentation</LI>\n";
    }
  }

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Select the specified number of items for the specified dimension in the
# specified visual.
#

sub autorpt_select_n_items
{
  my ($query, $dbOutput, $status, $dimColName, $selColName, $dimItemsStr);
  my ($dimItem, $count, $q_items);
  my (@dimItems);

  my ($db, $cubeID, $visID, $dim, $numItems) = @_;

  if ($dim eq "p")
  {
    $dimColName = "products";
    $selColName = "selProducts";
  }
  elsif ($dim eq "g")
  {
    $dimColName = "geographies";
    $selColName = "selGeographies";
  }
  elsif ($dim eq "t")
  {
    $dimColName = "timeperiods";
    $selColName = "selTimeperiods";
  }
  elsif ($dim eq "m")
  {
    $dimColName = "measures";
    $selColName = "selMeasures";
  }

  #grab all items for the specified dimension present in the cube
  $query = "SELECT $dimColName FROM app.cubes WHERE ID=$cubeID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  autorpt_db_err($db, $status, $query);
  ($dimItemsStr) = $dbOutput->fetchrow_array;

  #turn the item string into an array
  @dimItems = split(',', $dimItemsStr);

  #get the specified number of items
  $count = 0;
  $dimItemsStr = "";
  foreach $dimItem (@dimItems)
  {
    if ($count < $numItems)
    {
      $dimItemsStr .= "$dimItem,";
    }
    $count++;
  }
  chop($dimItemsStr);

  #set the selection in the specified visual element
  $q_items = $db->quote($dimItemsStr);
  $query = "UPDATE app.visuals SET $selColName=$q_items WHERE ID=$visID";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------
#
# Select all items in a specified dimension in the specified visual.
#

sub autorpt_select_all_items
{
  my ($query, $dbOutput, $status, $dimColName, $selColName, $dimItemsStr);
  my ($q_items);

  my ($db, $cubeID, $visID, $dim) = @_;

  if ($dim eq "p")
  {
    $dimColName = "products";
    $selColName = "selProducts";
  }
  elsif ($dim eq "g")
  {
    $dimColName = "geographies";
    $selColName = "selGeographies";
  }
  elsif ($dim eq "t")
  {
    $dimColName = "timeperiods";
    $selColName = "selTimeperiods";
  }
  elsif ($dim eq "m")
  {
    $dimColName = "measures";
    $selColName = "selMeasures";
  }

  #grab all items for the specified dimension present in the cube
  $query = "SELECT $dimColName FROM app.cubes WHERE ID=$cubeID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  autorpt_db_err($db, $status, $query);
  ($dimItemsStr) = $dbOutput->fetchrow_array;

  #set the selection in the specified visual element
  $q_items = $db->quote($dimItemsStr);
  $query = "UPDATE app.visuals SET $selColName=$q_items WHERE ID=$visID";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------
#
# Select up to the specified number of segments from the specified
# segmentation.
#

sub autorpt_select_prod_n_segments
{
  my ($query, $dbOutput, $status, $segID, $dsSchema, $segmentID, $productsStr);
  my ($i, $prodID, $selStr, $count, $q_items);
  my (@segmentOrderArray, @prodItems);
  my (%segmentHash, %isect, %union);

  my ($db, $dsID, $cubeID, $visID, $segType, $numItems) = @_;

  $dsSchema = "datasource_" . $dsID;

  #figure out which segmentation we're selecting from
  if ($segType eq "brand")
  {
    $segID = autorpt_find_seg_brand($db, $dsID, 1);
  }
  elsif ($segType eq "category")
  {
    $segID = autorpt_find_seg_category($db, $dsID, 1);
  }
  elsif ($segType =~ m/^\d+$/)
  {
    $segID = $segType;
  }
  else
  {
    return;
    #XXX maybe select any segment from any segmentation instead?
  }

  #get a hash of all segments in the specified segmentation
  %segmentHash = DSRseg_get_segments_hash($db, $dsSchema, "p", $segID);

  #get an array of all segments in the specified segmentation, sorted by size
  $query = "SELECT segmentID, COUNT(*) AS count \
      FROM $dsSchema.product_segment_item \
      WHERE segmentationID=$segID \
      GROUP BY segmentID ORDER BY count DESC";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  autorpt_db_err($db, $status, $query);
  while (($segmentID) = $dbOutput->fetchrow_array)
  {
    push(@segmentOrderArray, "SMT_$segmentID");
  }

  #grab all items for the specified dimension present in the cube
  $query = "SELECT products FROM app.cubes WHERE ID=$cubeID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  autorpt_db_err($db, $status, $query);
  ($productsStr) = $dbOutput->fetchrow_array;

  #turn the item string into an array for matching purposes
  @prodItems = split(',', $productsStr);

  #intersect the size-ordered array of segments with the segments available in
  #the report so we end up with a size-ordered array of segments in the report
  foreach $i (@prodItems, @segmentOrderArray) { $union{$i}++ && $isect{$i}++ };
  undef(@prodItems);
  foreach $prodID (@segmentOrderArray)
  {
    if ($isect{$prodID} > 0)
    {
      push(@prodItems, $prodID);
    }
  }

  #get the current product selection string
  $query = "SELECT selProducts FROM app.visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  autorpt_db_err($db, $status, $query);
  ($selStr) = $dbOutput->fetchrow_array;

  if (length($selStr) > 0)
  {
    $selStr .= ",";
  }

  #get the specified number of items
  $count = 0;
  foreach $prodID (@prodItems)
  {
    if ($count < $numItems)
    {
      if ($prodID =~ m/SMT_(\d+)/)
      {
        if (length($segmentHash{$1}) > 1)
        {
          $selStr .= "$prodID,";
          $count++;
        }
      }
    }
  }
  chop($selStr);

  #set the selection in the specified visual element
  $q_items = $db->quote($selStr);
  $query = "UPDATE app.visuals SET selProducts=$q_items WHERE ID=$visID";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------
#
# Select the most recent 52 1-week time periods for the specified visual
#

sub autorpt_select_time_52_weeks
{
  my ($query, $dbOutput, $status, $dsSchema, $timeStr, $timeID, $q_items);

  my ($db, $dsID, $cubeID, $visID) = @_;

  $dsSchema = "datasource_" . $dsID;

  #NB: I think I may be cheating here by pulling the IDs straight from the
  #    data source instead of the cube definition, but this should always be
  #    valid in this code base...

  #grab the 52 most recent 1-week time periods from the data source
  $query = "SELECT ID FROM $dsSchema.timeperiods \
      WHERE type=30 AND duration=1 ORDER BY endDate DESC LIMIT 52";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  autorpt_db_err($db, $status, $query);
  while (($timeID) = $dbOutput->fetchrow_array)
  {
    $timeStr .= "$timeID,";
  }
  chop($timeStr);

  #set the selection in the specified visual element
  $q_items = $db->quote($timeStr);
  $query = "UPDATE app.visuals SET selTimeperiods=$q_items WHERE ID=$visID";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------
#
# Select the most recent cumulative week time period with the specified number
# of weeks for the specified visual
#

sub autorpt_select_time_n_cum_weeks
{
  my ($query, $dbOutput, $status, $selString, $timeID, $q_selString);

  my ($db, $dsID, $cubeID, $visID, $weeks) = @_;

  $timeID = autorpt_find_recent_n_cum_weeks($db, $dsID, $weeks, 1);

  #add the time to the list of selected time periods for the visual
  $query = "SELECT selTimeperiods FROM app.visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  autorpt_db_err($db, $status, $query);
  ($selString) = $dbOutput->fetchrow_array;
  if (length($selString) > 0)
  {
    $selString .= ",$timeID";
  }
  else
  {
    $selString = $timeID;
  }

  #save the new selection
  $q_selString = $db->quote($selString);
  $query = "UPDATE app.visuals SET selTimeperiods=$q_selString WHERE ID=$visID";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------
#
# Select the specified measure for the specified visual.
#

sub autorpt_select_meas_by_name
{
  my ($query, $dbOutput, $status, $measureID, $selString, $q_selString);

  my ($db, $dsID, $cubeID, $visID, $measureName) = @_;

  #figure out which measure we're looking for, and do it
  if ($measureName eq "acv reach")
  {
    $measureID = autorpt_find_meas_acv_reach($db, $dsID);
  }
  elsif ($measureName eq "acv reach cya")
  {
    $measureID = autorpt_find_meas_acv_reach_cya($db, $dsID);
  }
  elsif ($measureName eq "acv reach pctchg ya")
  {
    $measureID = autorpt_find_meas_acv_reach_pctchg_ya($db, $dsID);
  }
  elsif ($measureName eq "acv reach ya")
  {
    $measureID = autorpt_find_meas_acv_reach_ya($db, $dsID);
  }
  elsif ($measureName eq "any promo dollars")
  {
    $measureID = autorpt_find_meas_any_promo_dollars($db, $dsID);
  }
  elsif ($measureName eq "any promo disc")
  {
    $measureID = autorpt_find_meas_any_promo_disc($db, $dsID);
  }
  elsif ($measureName eq "any promo incr dollars")
  {
    $measureID = autorpt_find_meas_any_promo_incr_dollars($db, $dsID);
  }
  elsif ($measureName eq "any promo incr dollars ya")
  {
    $measureID = autorpt_find_meas_any_promo_incr_dollars_ya($db, $dsID);
  }
  elsif ($measureName eq "any promo units")
  {
    $measureID = autorpt_find_meas_any_promo_units($db, $dsID);
  }
  elsif ($measureName eq "any promo unit price")
  {
    $measureID = autorpt_find_meas_any_promo_unit_price($db, $dsID);
  }
  elsif ($measureName eq "avg num items")
  {
    $measureID = autorpt_find_meas_avg_num_items($db, $dsID);
  }
  elsif ($measureName eq "avg num items cya")
  {
    $measureID = autorpt_find_meas_avg_num_items_cya($db, $dsID);
  }
  elsif ($measureName eq "avg num items pctchg ya")
  {
    $measureID = autorpt_find_meas_avg_num_items_pctchg_ya($db, $dsID);
  }
  elsif ($measureName eq "avg unit price")
  {
    $measureID = autorpt_find_meas_avg_unit_price($db, $dsID);
  }
  elsif ($measureName eq "avg unit price ya")
  {
    $measureID = autorpt_find_meas_avg_unit_price_ya($db, $dsID);
  }
  elsif ($measureName eq "avg unit price pctchg ya")
  {
    $measureID = autorpt_find_meas_avg_unit_price_pctchg_ya($db, $dsID);
  }
  elsif ($measureName eq "base dollars")
  {
    $measureID = autorpt_find_meas_base_dollars($db, $dsID);
  }
  elsif ($measureName eq "base dollars ya")
  {
    $measureID = autorpt_find_meas_base_dollars_ya($db, $dsID);
  }
  elsif ($measureName eq "base unit price")
  {
    $measureID = autorpt_find_meas_base_unit_price($db, $dsID);
  }
  elsif ($measureName eq "base unit price pctchg ya")
  {
    $measureID = autorpt_find_meas_base_unit_price_pctchg_ya($db, $dsID);
  }
  elsif ($measureName eq "base units")
  {
    $measureID = autorpt_find_meas_base_units($db, $dsID);
  }
  elsif ($measureName eq "disp wo feat base dollars")
  {
    $measureID = autorpt_find_meas_promo_vehicle_base_dollars($db, $dsID, "disp wo feat");
  }
  elsif ($measureName eq "disp wo feat base units")
  {
    $measureID = autorpt_find_meas_promo_vehicle_base_units($db, $dsID, "disp wo feat");
  }
  elsif ($measureName eq "disp wo feat cww")
  {
    $measureID = autorpt_find_meas_promo_vehicle_cww($db, $dsID, "disp wo feat");
  }
  elsif ($measureName eq "disp wo feat cww cya")
  {
    $measureID = autorpt_find_meas_promo_vehicle_cww_cya($db, $dsID, "disp wo feat");
  }
  elsif ($measureName eq "disp wo feat dollar pct lift")
  {
    $measureID = autorpt_find_meas_promo_vehicle_dollar_pct_lift($db, $dsID, "disp wo feat");
  }
  elsif ($measureName eq "disp wo feat dollar pct lift cya")
  {
    $measureID = autorpt_find_meas_promo_vehicle_dollar_pct_lift_cya($db, $dsID, "disp wo feat");
  }
  elsif ($measureName eq "disp wo feat dollars")
  {
    $measureID = autorpt_find_meas_promo_vehicle_dollars($db, $dsID, "disp wo feat");
  }
  elsif ($measureName eq "disp wo feat incr dollars")
  {
    $measureID = autorpt_find_meas_promo_vehicle_incr_dollars($db, $dsID, "disp wo feat");
  }
  elsif ($measureName eq "disp wo feat incr dollars cya")
  {
    $measureID = autorpt_find_meas_promo_vehicle_incr_dollars_cya($db, $dsID, "disp wo feat");
  }
  elsif ($measureName eq "disp wo feat incr dollars share cat")
  {
    $measureID = autorpt_find_meas_promo_vehicle_incr_dollars_shr_cat($db, $dsID, "disp wo feat");
  }
  elsif ($measureName eq "disp wo feat incr dollars share cat cya")
  {
    $measureID = autorpt_find_meas_promo_vehicle_incr_dollars_shr_cat_cya($db, $dsID, "disp wo feat");
  }
  elsif ($measureName eq "disp wo feat incr dollars share cat ya")
  {
    $measureID = autorpt_find_meas_promo_vehicle_incr_dollars_shr_cat_ya($db, $dsID, "disp wo feat");
  }
  elsif ($measureName eq "disp wo feat unit price")
  {
    $measureID = autorpt_find_meas_promo_vehicle_unit_price($db, $dsID, "disp wo feat");
  }
  elsif ($measureName eq "disp wo feat unit price cya")
  {
    $measureID = autorpt_find_meas_promo_vehicle_unit_price_cya($db, $dsID, "disp wo feat");
  }
  elsif ($measureName eq "disp wo feat unit price pct disc")
  {
    $measureID = autorpt_find_meas_promo_vehicle_unit_price_pct_disc($db, $dsID, "disp wo feat");
  }
  elsif ($measureName eq "disp wo feat unit price pct disc cya")
  {
    $measureID = autorpt_find_meas_promo_vehicle_unit_price_pct_disc_cya($db, $dsID, "disp wo feat");
  }
  elsif ($measureName eq "disp wo feat units")
  {
    $measureID = autorpt_find_meas_promo_vehicle_units($db, $dsID, "disp wo feat");
  }
  elsif ($measureName eq "dollars")
  {
    $measureID = autorpt_find_meas_dollars($db, $dsID);
  }
  elsif ($measureName eq "dollars ya")
  {
    $measureID = autorpt_find_meas_dollars_ya($db, $dsID);
  }
  elsif ($measureName eq "dollar cya")
  {
    $measureID = autorpt_find_meas_dollars_cya($db, $dsID);
  }
  elsif ($measureName eq "dollar pctchg ya")
  {
    $measureID = autorpt_find_meas_dollar_pctchg_ya($db, $dsID);
  }
  elsif ($measureName eq "dollar pct lift")
  {
    $measureID = autorpt_find_meas_dollar_pct_lift($db, $dsID);
  }
  elsif ($measureName eq "dollar change base velocity change")
  {
    $measureID = autorpt_find_meas_dollar_change_base_velocity_change($db, $dsID);
  }
  elsif ($measureName eq "dollar change tdp change")
  {
    $measureID = autorpt_find_meas_dollar_change_tdp_change($db, $dsID);
  }
  elsif ($measureName eq "dollar shr category")
  {
    $measureID = autorpt_find_meas_dollar_shr_category($db, $dsID);
  }
  elsif ($measureName eq "dollar shr category ya")
  {
    $measureID = autorpt_find_meas_dollar_shr_category_ya($db, $dsID);
  }
  elsif ($measureName eq "dollar shr category cya")
  {
    $measureID = autorpt_find_meas_dollar_shr_category_cya($db, $dsID);
  }
  elsif ($measureName eq "feat and disp base dollars")
  {
    $measureID = autorpt_find_meas_promo_vehicle_base_dollars($db, $dsID, "feat and disp");
  }
  elsif ($measureName eq "feat and disp base units")
  {
    $measureID = autorpt_find_meas_promo_vehicle_base_units($db, $dsID, "feat and disp");
  }
  elsif ($measureName eq "feat and disp cww")
  {
    $measureID = autorpt_find_meas_promo_vehicle_cww($db, $dsID, "feat and disp");
  }
  elsif ($measureName eq "feat and disp cww cya")
  {
    $measureID = autorpt_find_meas_promo_vehicle_cww_cya($db, $dsID, "feat and disp");
  }
  elsif ($measureName eq "feat and disp dollar pct lift")
  {
    $measureID = autorpt_find_meas_promo_vehicle_dollar_pct_lift($db, $dsID, "feat and disp");
  }
  elsif ($measureName eq "feat and disp dollar pct lift cya")
  {
    $measureID = autorpt_find_meas_promo_vehicle_dollar_pct_lift_cya($db, $dsID, "feat and disp");
  }
  elsif ($measureName eq "feat and disp dollars")
  {
    $measureID = autorpt_find_meas_promo_vehicle_dollars($db, $dsID, "feat and disp");
  }
  elsif ($measureName eq "feat and disp incr dollars")
  {
    $measureID = autorpt_find_meas_promo_vehicle_incr_dollars($db, $dsID, "feat and disp");
  }
  elsif ($measureName eq "feat and disp incr dollars cya")
  {
    $measureID = autorpt_find_meas_promo_vehicle_incr_dollars_cya($db, $dsID, "feat and disp");
  }
  elsif ($measureName eq "feat and disp incr dollars share cat")
  {
    $measureID = autorpt_find_meas_promo_vehicle_incr_dollars_shr_cat($db, $dsID, "feat and disp");
  }
  elsif ($measureName eq "feat and disp incr dollars share cat cya")
  {
    $measureID = autorpt_find_meas_promo_vehicle_incr_dollars_shr_cat_cya($db, $dsID, "feat and disp");
  }
  elsif ($measureName eq "feat and disp incr dollars share cat ya")
  {
    $measureID = autorpt_find_meas_promo_vehicle_incr_dollars_shr_cat_ya($db, $dsID, "feat and disp");
  }
  elsif ($measureName eq "feat and disp unit price")
  {
    $measureID = autorpt_find_meas_promo_vehicle_unit_price($db, $dsID, "feat and disp");
  }
  elsif ($measureName eq "feat and disp unit price cya")
  {
    $measureID = autorpt_find_meas_promo_vehicle_unit_price_cya($db, $dsID, "feat and disp");
  }
  elsif ($measureName eq "feat and disp unit price pct disc")
  {
    $measureID = autorpt_find_meas_promo_vehicle_unit_price_pct_disc($db, $dsID, "feat and disp");
  }
  elsif ($measureName eq "feat and disp unit price pct disc cya")
  {
    $measureID = autorpt_find_meas_promo_vehicle_unit_price_pct_disc_cya($db, $dsID, "feat and disp");
  }
  elsif ($measureName eq "feat and disp units")
  {
    $measureID = autorpt_find_meas_promo_vehicle_units($db, $dsID, "feat and disp");
  }
  elsif ($measureName eq "feat wo disp base dollars")
  {
    $measureID = autorpt_find_meas_promo_vehicle_base_dollars($db, $dsID, "feat wo disp");
  }
  elsif ($measureName eq "feat wo disp base units")
  {
    $measureID = autorpt_find_meas_promo_vehicle_base_units($db, $dsID, "feat wo disp");
  }
  elsif ($measureName eq "feat wo disp cww")
  {
    $measureID = autorpt_find_meas_promo_vehicle_cww($db, $dsID, "feat wo disp");
  }
  elsif ($measureName eq "feat wo disp cww cya")
  {
    $measureID = autorpt_find_meas_promo_vehicle_cww_cya($db, $dsID, "feat wo disp");
  }
  elsif ($measureName eq "feat wo disp dollar pct lift")
  {
    $measureID = autorpt_find_meas_promo_vehicle_dollar_pct_lift($db, $dsID, "feat wo disp");
  }
  elsif ($measureName eq "feat wo disp dollar pct lift cya")
  {
    $measureID = autorpt_find_meas_promo_vehicle_dollar_pct_lift_cya($db, $dsID, "feat wo disp");
  }
  elsif ($measureName eq "feat wo disp dollars")
  {
    $measureID = autorpt_find_meas_promo_vehicle_dollars($db, $dsID, "feat wo disp");
  }
  elsif ($measureName eq "feat wo disp incr dollars")
  {
    $measureID = autorpt_find_meas_promo_vehicle_incr_dollars($db, $dsID, "feat wo disp");
  }
  elsif ($measureName eq "feat wo disp incr dollars cya")
  {
    $measureID = autorpt_find_meas_promo_vehicle_incr_dollars_cya($db, $dsID, "feat wo disp");
  }
  elsif ($measureName eq "feat wo disp incr dollars share cat")
  {
    $measureID = autorpt_find_meas_promo_vehicle_incr_dollars_shr_cat($db, $dsID, "feat wo disp");
  }
  elsif ($measureName eq "feat wo disp incr dollars share cat cya")
  {
    $measureID = autorpt_find_meas_promo_vehicle_incr_dollars_shr_cat_cya($db, $dsID, "feat wo disp");
  }
  elsif ($measureName eq "feat wo disp incr dollars share cat ya")
  {
    $measureID = autorpt_find_meas_promo_vehicle_incr_dollars_shr_cat_ya($db, $dsID, "feat wo disp");
  }
  elsif ($measureName eq "feat wo disp unit price")
  {
    $measureID = autorpt_find_meas_promo_vehicle_unit_price($db, $dsID, "feat wo disp");
  }
  elsif ($measureName eq "feat wo disp unit price cya")
  {
    $measureID = autorpt_find_meas_promo_vehicle_unit_price_cya($db, $dsID, "feat wo disp");
  }
  elsif ($measureName eq "feat wo disp unit price pct disc")
  {
    $measureID = autorpt_find_meas_promo_vehicle_unit_price_pct_disc($db, $dsID, "feat wo disp");
  }
  elsif ($measureName eq "feat wo disp unit price pct disc cya")
  {
    $measureID = autorpt_find_meas_promo_vehicle_unit_price_pct_disc_cya($db, $dsID, "feat wo disp");
  }
  elsif ($measureName eq "feat wo disp units")
  {
    $measureID = autorpt_find_meas_promo_vehicle_units($db, $dsID, "feat wo disp");
  }
  elsif ($measureName eq "incr dollars")
  {
    $measureID = autorpt_find_meas_incr_dollars($db, $dsID);
  }
  elsif ($measureName eq "incr dollars cya")
  {
    $measureID = autorpt_find_meas_incr_dollars_cya($db, $dsID);
  }
  elsif ($measureName eq "incr dollars ya")
  {
    $measureID = autorpt_find_meas_incr_dollars_ya($db, $dsID);
  }
  elsif ($measureName eq "incr dollars shr cat")
  {
    $measureID = autorpt_find_meas_incr_dollars_shr_cat($db, $dsID);
  }
  elsif ($measureName eq "incr dollars shr cat cya")
  {
    $measureID = autorpt_find_meas_incr_dollars_shr_cat_cya($db, $dsID);
  }
  elsif ($measureName eq "incr dollars shr cat ya")
  {
    $measureID = autorpt_find_meas_incr_dollars_shr_cat_ya($db, $dsID);
  }
  elsif ($measureName eq "incr units")
  {
    $measureID = autorpt_find_meas_incr_units($db, $dsID);
  }
  elsif ($measureName eq "pct dollars base")
  {
    $measureID = autorpt_find_meas_pct_dollars_base($db, $dsID);
  }
  elsif ($measureName eq "pct dollars incr")
  {
    $measureID = autorpt_find_meas_pct_dollars_incr($db, $dsID);
  }
  elsif ($measureName eq "price decr base dollars")
  {
    $measureID = autorpt_find_meas_promo_vehicle_base_dollars($db, $dsID, "price decr");
  }
  elsif ($measureName eq "price decr base units")
  {
    $measureID = autorpt_find_meas_promo_vehicle_base_units($db, $dsID, "price decr");
  }
  elsif ($measureName eq "price decr cww")
  {
    $measureID = autorpt_find_meas_promo_vehicle_cww($db, $dsID, "price decr");
  }
  elsif ($measureName eq "price decr cww cya")
  {
    $measureID = autorpt_find_meas_promo_vehicle_cww_cya($db, $dsID, "price decr");
  }
  elsif ($measureName eq "price decr dollar pct lift")
  {
    $measureID = autorpt_find_meas_promo_vehicle_dollar_pct_lift($db, $dsID, "price decr");
  }
  elsif ($measureName eq "price decr dollar pct lift cya")
  {
    $measureID = autorpt_find_meas_promo_vehicle_dollar_pct_lift_cya($db, $dsID, "price decr");
  }
  elsif ($measureName eq "price decr dollars")
  {
    $measureID = autorpt_find_meas_promo_vehicle_dollars($db, $dsID, "price decr");
  }
  elsif ($measureName eq "price decr incr dollars")
  {
    $measureID = autorpt_find_meas_promo_vehicle_incr_dollars($db, $dsID, "price decr");
  }
  elsif ($measureName eq "price decr incr dollars cya")
  {
    $measureID = autorpt_find_meas_promo_vehicle_incr_dollars_cya($db, $dsID, "price decr");
  }
  elsif ($measureName eq "price decr incr dollars share cat")
  {
    $measureID = autorpt_find_meas_promo_vehicle_incr_dollars_shr_cat($db, $dsID, "price decr");
  }
  elsif ($measureName eq "price decr incr dollars share cat cya")
  {
    $measureID = autorpt_find_meas_promo_vehicle_incr_dollars_shr_cat_cya($db, $dsID, "price decr");
  }
  elsif ($measureName eq "price decr incr dollars share cat ya")
  {
    $measureID = autorpt_find_meas_promo_vehicle_incr_dollars_shr_cat_ya($db, $dsID, "price decr");
  }
  elsif ($measureName eq "price decr unit price")
  {
    $measureID = autorpt_find_meas_promo_vehicle_unit_price($db, $dsID, "price decr");
  }
  elsif ($measureName eq "price decr unit price cya")
  {
    $measureID = autorpt_find_meas_promo_vehicle_unit_price_cya($db, $dsID, "price decr");
  }
  elsif ($measureName eq "price decr unit price pct disc")
  {
    $measureID = autorpt_find_meas_promo_vehicle_unit_price_pct_disc($db, $dsID, "price decr");
  }
  elsif ($measureName eq "price decr unit price pct disc cya")
  {
    $measureID = autorpt_find_meas_promo_vehicle_unit_price_pct_disc_cya($db, $dsID, "price decr");
  }
  elsif ($measureName eq "price decr units")
  {
    $measureID = autorpt_find_meas_promo_vehicle_units($db, $dsID, "price decr");
  }
  elsif ($measureName eq "tdp")
  {
    $measureID = autorpt_find_meas_tdp($db, $dsID);
  }
  elsif ($measureName eq "tdp cya")
  {
    $measureID = autorpt_find_meas_tdp_cya($db, $dsID);
  }
  elsif ($measureName eq "tdp ya")
  {
    $measureID = autorpt_find_meas_tdp_ya($db, $dsID);
  }
  elsif ($measureName eq "units")
  {
    $measureID = autorpt_find_meas_units($db, $dsID);
  }
  elsif ($measureName eq "units ya")
  {
    $measureID = autorpt_find_meas_units_ya($db, $dsID);
  }
  elsif ($measureName eq "unit cya")
  {
    $measureID = autorpt_find_meas_units_cya($db, $dsID);
  }
  elsif ($measureName eq "units pctchg ya")
  {
    $measureID = autorpt_find_meas_units_pctchg_ya($db, $dsID);
  }
  elsif ($measureName eq "units shr category")
  {
    $measureID = autorpt_find_meas_units_shr_category($db, $dsID);
  }
  elsif ($measureName eq "units shr category ya")
  {
    $measureID = autorpt_find_meas_units_shr_category_ya($db, $dsID);
  }
  elsif ($measureName eq "units shr category cya")
  {
    $measureID = autorpt_find_meas_units_shr_category_cya($db, $dsID);
  }

  #add the measure to the list of selected measured for the visual
  $query = "SELECT selMeasures FROM app.visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  autorpt_db_err($db, $status, $query);
  ($selString) = $dbOutput->fetchrow_array;
  if (length($selString) > 0)
  {
    $selString .= ",$measureID";
  }
  else
  {
    $selString = $measureID;
  }

  #save the new selection
  $q_selString = $db->quote($selString);
  $query = "UPDATE app.visuals SET selMeasures=$q_selString WHERE ID=$visID";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
}









###############################################################################
#
#
#
#
#                        REPORTS: CATEGORY OVERVIEW
#
#
#
#
###############################################################################




#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for category overview brand topline report
#

sub autorpt_prereqs_catov_topline_brands
{
  my ($prereqHTML);

  my ($db, $dsID) = @_;

  $prereqHTML = "";
  $prereqHTML = autorpt_prereqs_meas_dollars($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_dollar_pctchg_ya($db, $dsID, $prereqHTML);

  $prereqHTML = autorpt_uniquify_prereqs($prereqHTML);

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Insert a brand top-line report into the specified data source
#

sub autorpt_insert_catov_topline_brands
{
  my ($dsSchema, $query, $status, $cubeID, $visID);
  my ($prodScript, $geoScript, $timeScript, $measScript, $style, $q_style);
  my ($dolPctChgMeasID, $dolMeasID);

  my ($db, $userID, $orgID, $dsID, $story) = @_;

  $dsSchema = "datasource_" . $dsID;

  #determine product selection
  $prodScript = autorpt_find_seg_brand($db, $dsID);

  #determine geography selection
  $geoScript = autorpt_find_geo_total_us($db, $dsID);
  $geoScript .= ",";
  $geoScript .= autorpt_find_geo_retailers($db, $dsID);

  #insert time periods (52 most recent 1-weeks, most recent cumulatives)
  $timeScript = autorpt_create_recent_n_cum_weeks($db, $dsID, 52);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 26);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 13);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 4);
  $timeScript .= ",";
  $timeScript .= autorpt_find_recent_52_weeks($db, $dsID);

  #insert measures
  $measScript = "M:" . autorpt_find_meas_dollars($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_dollar_pctchg_ya($db, $dsID);

  #insert basic cube definition
  $query = "INSERT INTO app.cubes (dsID, userID, orgID, name, scriptProducts, scriptGeographies, scriptTimeperiods, scriptMeasures) \
      VALUES ($dsID, $userID, $orgID, 'Topline Sales Trends By Brand', '$prodScript', '$geoScript', '$timeScript', '$measScript')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $cubeID = $db->{q{mysql_insertid}};

  #build the cube
  KAPutil_job_update_status($db, "Creating Category Overview: Topline Sales Trends by Brand report");
  cube_build($db, $dsSchema, $cubeID, $userID);

  #
  #insert the visuals and set their initial selections
  #

  #insert top 52-week line chart
  $style = ",type:Lines,width:1.0,height:0.5,xpct:0.0,ypct:0.0,zindex:1,";
  $style .= "caption:\"Weekly {MEAS} by Brand\",captionFontSize:24,subcaption:\"{GEOG}\",";
  $style .= "showValues:0,labelStep:4,labelFontSize:14,legendItemFontSize:14,";
  $style .= "lockTimes:1,lockMeasures:1,";
  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x, graph_y) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 't', 'p')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #take the 3 largest brands and the first geography
  autorpt_select_prod_n_segments($db, $dsID, $cubeID, $visID, "brand", 3);
  autorpt_select_n_items($db, $cubeID, $visID, "g", 1);

  #select the most recent 52 1-week time periods
  autorpt_select_time_52_weeks($db, $dsID, $cubeID, $visID);

  #select the dollars measure
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "dollars");

  #insert bottom left bar chart
  $dolPctChgMeasID = autorpt_find_meas_dollar_pctchg_ya($db, $dsID);
  $style = ",type:2DColumns,showLegend:0,";
  $style .= "width:0.5,height:0.5,xpct:0.0,ypct:0.5,zindex:2,";
  $style .= "filterMeas1:$dolPctChgMeasID,filterOp1:top,filterNum1:8,allOthers:false,";
  $style .= "caption:\"{MEAS} for Top Brands\",subcaption:\"{TIME}, {GEOG}\",captionFontSize:24,";
  $style .= "labelFontSize:14,showValues:1,lockMeasures:1,valueFontSize:14,";
  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x, graph_y) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 'p', 'm')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #select all brands and first geography
  autorpt_select_all_items($db, $cubeID, $visID, "p");
  autorpt_select_n_items($db, $cubeID, $visID, "g", 1);

  #select 52 week cumulative time period
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);

  #select dollar % chg measure
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "dollar pctchg ya");

  #insert bottom right pie chart
  $dolMeasID = autorpt_find_meas_dollars($db, $dsID);
  $style = ",type:2DPie,width:0.5,height:0.5,xpct:0.5,ypct:0.5,zindex:3,";
  $style .= "caption:\"{MEAS} for Top Brands\",subcaption:\"{TIME}, {GEOG}\",captionFontSize:24,";
  $style .= "filterMeas1:$dolMeasID,filterOp1:top,filterNum1:7,allOthers:true,";
  $style .= "labelFontSize:14,showLegend:0,lockProducts:1,lockMeasures:1,";
  $style .= "valueFontSize:14,showLabels:1,";
  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x, graph_y) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 'p', 'm')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #select all brands and first geography
  autorpt_select_all_items($db, $cubeID, $visID, "p");
  autorpt_select_n_items($db, $cubeID, $visID, "g", 1);

  #select 52 week cumulative time period
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);

  #select dollar measure
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "dollars");
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for category overview topline retailer report
#

sub autorpt_prereqs_catov_topline_retailers
{
  my ($prereqHTML);

  my ($db, $dsID) = @_;

  $prereqHTML = "";
  $prereqHTML = autorpt_prereqs_meas_dollars($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_dollar_pctchg_ya($db, $dsID, $prereqHTML);

  $prereqHTML = autorpt_uniquify_prereqs($prereqHTML);

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Insert a top-line retailer trend report into the specified data source
#

sub autorpt_insert_catov_topline_retailers
{
  my ($dsSchema, $query, $status, $cubeID, $visID);
  my ($prodScript, $geoScript, $timeScript, $measScript, $style, $q_style);
  my ($dolPctChgMeasID, $dolMeasID);

  my ($db, $userID, $orgID, $dsID, $story) = @_;

  $dsSchema = "datasource_" . $dsID;

  #determine product selection
  $prodScript = autorpt_find_seg_brand($db, $dsID);

  #determine geography selection
  $geoScript = autorpt_find_geo_total_us($db, $dsID);
  $geoScript .= ",";
  $geoScript .= autorpt_find_geo_retailers($db, $dsID);

  #insert time periods (52 most recent 1-weeks, most recent cumulatives)
  $timeScript = autorpt_create_recent_n_cum_weeks($db, $dsID, 52);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 26);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 13);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 4);
  $timeScript .= ",";
  $timeScript .= autorpt_find_recent_52_weeks($db, $dsID);

  #insert measures
  $measScript = "M:" . autorpt_find_meas_dollars($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_dollar_pctchg_ya($db, $dsID);

  #insert basic cube definition
  $query = "INSERT INTO app.cubes (dsID, userID, orgID, name, scriptProducts, scriptGeographies, scriptTimeperiods, scriptMeasures) \
      VALUES ($dsID, $userID, $orgID, 'Topline Sales Trends By Retailer', '$prodScript', '$geoScript', '$timeScript', '$measScript')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $cubeID = $db->{q{mysql_insertid}};

  #build the cube
  KAPutil_job_update_status($db, "Creating Category Overview:Topline Sales Trends by Retailer report");
  cube_build($db, $dsSchema, $cubeID, $userID);

  #
  #insert the visuals and set their initial selections
  #

  #insert top 52-week line chart
  $style = ",type:Lines,width:1.0,height:0.5,xpct:0.0,ypct:0.0,zindex:1,";
  $style .= "caption:\"Weekly {MEAS} by Retailer\",captionFontSize:24,subcaption:\"{PROD}\",";
  $style .= "showValues:0,labelStep:4,labelFontSize:14,legendItemFontSize:14,";
  $style .= "lockTimes:1,lockMeasures:1,";
  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x, graph_y) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 't', 'g')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #take the largest brand and the first 3 geographies
  autorpt_select_prod_n_segments($db, $dsID, $cubeID, $visID, "brand", 1);
  autorpt_select_n_items($db, $cubeID, $visID, "g", 3);

  #select the most recent 52 1-week time periods
  autorpt_select_time_52_weeks($db, $dsID, $cubeID, $visID);

  #select the dollars measure
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "dollars");

  #insert bottom left bar chart
  $dolPctChgMeasID = autorpt_find_meas_dollar_pctchg_ya($db, $dsID);
  $style = ",type:2DColumns,showLegend:0,";
  $style .= "width:0.5,height:0.5,xpct:0.0,ypct:0.5,zindex:2,";
  $style .= "filterMeas1:$dolPctChgMeasID,filterOp1:top,filterNum1:8,allOthers:false,";
  $style .= "caption:\"{MEAS} for Top Retailers\",subcaption:\"{TIME}, {PROD}\",captionFontSize:24,";
  $style .= "labelFontSize:14,showValues:1,lockMeasures:1,valueFontSize:14,";
  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x, graph_y) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 'g', 'm')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #select largest brand and all geographies
  autorpt_select_prod_n_segments($db, $dsID, $cubeID, $visID, "brand", 1);
  autorpt_select_all_items($db, $cubeID, $visID, "g", 1);

  #select 52 week cumulative time period
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);

  #select dollar % chg measure
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "dollar pctchg ya");

  #insert bottom right pie chart
  $dolMeasID = autorpt_find_meas_dollars($db, $dsID);
  $style = ",type:2DPie,width:0.5,height:0.5,xpct:0.5,ypct:0.5,zindex:3,";
  $style .= "caption:\"{MEAS} for Top Retailers\",subcaption:\"{TIME}, {PROD}\",captionFontSize:24,";
  $style .= "filterMeas1:$dolMeasID,filterOp1:top,filterNum1:7,allOthers:true,";
  $style .= "labelFontSize:14,showLegend:0,lockProducts:1,lockMeasures:1,";
  $style .= "valueFontSize:14,showLabels:1,";
  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x, graph_y) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 'g', 'm')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #select largest brand and all geographies
  autorpt_select_prod_n_segments($db, $dsID, $cubeID, $visID, "brand", 1);
  autorpt_select_all_items($db, $cubeID, $visID, "g", 1);

  #select 52 week cumulative time period
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);

  #select dollar measure
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "dollars");
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for category overview brand rank report
#

sub autorpt_prereqs_catov_brand_rank
{
  my ($prereqHTML);

  my ($db, $dsID) = @_;

  $prereqHTML = "";
  $prereqHTML = autorpt_prereqs_meas_dollars($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_dollar_pctchg_ya($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_dollars_cya($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_dollar_shr_category($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_dollar_shr_category_cya($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_units($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_units_pctchg_ya($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_units_cya($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_units_shr_category($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_units_shr_category_cya($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_avg_unit_price($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_avg_unit_price_pctchg_ya($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_acv_reach($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_acv_reach_pctchg_ya($db, $dsID, $prereqHTML);

  $prereqHTML = autorpt_uniquify_prereqs($prereqHTML);

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Insert an overview brand rank report into the specified data source
#

sub autorpt_insert_catov_brand_rank
{
  my ($dsSchema, $query, $status, $cubeID, $visID);
  my ($prodScript, $geoScript, $timeScript, $measScript, $style, $q_style);

  my ($db, $userID, $orgID, $dsID, $story) = @_;

  $dsSchema = "datasource_" . $dsID;

  #determine product selection
  $prodScript = autorpt_find_seg_category($db, $dsID);
  $prodScript .= ",";
  $prodScript .= autorpt_find_seg_brand($db, $dsID);

  #determine geography selection
  $geoScript = autorpt_find_geo_total_us($db, $dsID);
  $geoScript .= ",";
  $geoScript .= autorpt_find_geo_retailers($db, $dsID);

  #insert time periods (52 most recent 1-weeks, most recent cumulatives)
  $timeScript = autorpt_create_recent_n_cum_weeks($db, $dsID, 52);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 26);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 13);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 4);

  #insert measures
  $measScript = "M:" . autorpt_find_meas_dollars($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_dollar_pctchg_ya($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_dollars_cya($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_dollar_shr_cat($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_dollar_shr_cat_cya($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_find_meas_units($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_units_pctchg_ya($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_units_cya($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_units_shr_cat($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_units_shr_cat_cya($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_avg_unit_price($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_avg_unit_price_pctchg_ya($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_find_meas_acv_reach($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_acv_reach_pctchg_ya($db, $dsID);

  #insert basic cube definition
  $query = "INSERT INTO app.cubes (dsID, userID, orgID, name, scriptProducts, scriptGeographies, scriptTimeperiods, scriptMeasures) \
      VALUES ($dsID, $userID, $orgID, 'Brand Rank', '$prodScript', '$geoScript', '$timeScript', '$measScript')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $cubeID = $db->{q{mysql_insertid}};

  #build the cube
  KAPutil_job_update_status($db, "Creating Category Overview: Brand Rank report");
  cube_build($db, $dsSchema, $cubeID, $userID);

  #
  #insert the visuals and set their initial selections
  #

  #insert brand ranking table
  $style = "width:1.0,height:1.0,xpct:0,ypct:0,zindex:1,";
  $style .= "excludeNA:1,sortMeas1:1,sortOrder1:DESC,title:\"Brand Rank\",subcaption:\"{TIME}, {GEOG}\",captionFontSize:24,";
  $style .= "tableStyle:default,headerSticky:1,headerFontSize:14,valueFontSize:14,";
  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, tableRowDims, tableFilterDims) \
      VALUES ($cubeID, $dsID, 'table', $q_style, 'p', 'g,t')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #take all brands and the first geography
  autorpt_select_all_items($db, $cubeID, $visID, "p");
  autorpt_select_n_items($db, $cubeID, $visID, "g", 1);

  #select the most recent 52-week time period
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);

  #display all measures in the table
  autorpt_select_all_items($db, $cubeID, $visID, "m");
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for category overview item rank report
#

sub autorpt_prereqs_catov_item_rank
{
  my ($prereqHTML);

  my ($db, $dsID) = @_;

  $prereqHTML = "";
  $prereqHTML = autorpt_prereqs_meas_dollars($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_dollar_pctchg_ya($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_dollars_cya($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_dollar_shr_category($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_dollar_shr_category_cya($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_units($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_units_pctchg_ya($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_units_cya($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_units_shr_category($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_units_shr_category_cya($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_avg_unit_price($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_avg_unit_price_pctchg_ya($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_acv_reach($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_acv_reach_pctchg_ya($db, $dsID, $prereqHTML);

  $prereqHTML = autorpt_uniquify_prereqs($prereqHTML);

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Insert an overview item rank report into the specified data source
#

sub autorpt_insert_catov_item_rank
{
  my ($dsSchema, $query, $status, $cubeID, $visID);
  my ($prodScript, $geoScript, $timeScript, $measScript, $style, $q_style);

  my ($db, $userID, $orgID, $dsID, $story) = @_;

  $dsSchema = "datasource_" . $dsID;

  #determine product selection
  $prodScript = autorpt_find_seg_category($db, $dsID);
  $prodScript .= ",";
  $prodScript .= autorpt_find_all($db, $dsID, "p");

  #determine geography selection
  $geoScript = autorpt_find_geo_total_us($db, $dsID);
  $geoScript .= ",";
  $geoScript .= autorpt_find_geo_retailers($db, $dsID);

  #insert time periods (52 most recent 1-weeks, most recent cumulatives)
  $timeScript = autorpt_create_recent_n_cum_weeks($db, $dsID, 52);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 26);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 13);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 4);

  #insert measures
  $measScript = "M:" . autorpt_find_meas_dollars($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_dollar_pctchg_ya($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_dollars_cya($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_dollar_shr_cat($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_dollar_shr_cat_cya($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_find_meas_units($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_units_pctchg_ya($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_units_cya($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_units_shr_cat($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_units_shr_cat_cya($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_avg_unit_price($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_avg_unit_price_pctchg_ya($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_find_meas_acv_reach($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_acv_reach_pctchg_ya($db, $dsID);

  #insert basic cube definition
  $query = "INSERT INTO app.cubes (dsID, userID, orgID, name, scriptProducts, scriptGeographies, scriptTimeperiods, scriptMeasures) \
      VALUES ($dsID, $userID, $orgID, 'Item Rank', '$prodScript', '$geoScript', '$timeScript', '$measScript')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $cubeID = $db->{q{mysql_insertid}};

  #build the cube
  KAPutil_job_update_status($db, "Creating Category Overview: Item Rank report");
  cube_build($db, $dsSchema, $cubeID, $userID);

  #
  #insert the visuals and set their initial selections
  #

  #insert brand ranking table
  $style = "width:1.0,height:1.0,xpct:0,ypct:0,zindex:1,";
  $style .= "excludeNA:1,sortMeas1:1,sortOrder1:DESC,title:\"Item Rank\",subcaption:\"{TIME}, {GEOG}\",captionFontSize:24,";
  $style .= "tableStyle:default,headerSticky:1,headerFontSize:14,valueFontSize:14,";
  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, tableRowDims, tableFilterDims) \
      VALUES ($cubeID, $dsID, 'table', $q_style, 'p', 'g,t')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #take all brands and the first geography
  autorpt_select_all_items($db, $cubeID, $visID, "p");
  autorpt_select_n_items($db, $cubeID, $visID, "g", 1);

  #select the most recent 52-week time period
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);

  #display all measures in the table
  autorpt_select_all_items($db, $cubeID, $visID, "m");
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for category overview retailer distribution
# report
#

sub autorpt_prereqs_catov_rtlr_distro_trends
{
  my ($prereqHTML);

  my ($db, $dsID) = @_;

  $prereqHTML = "";
  $prereqHTML = autorpt_prereqs_meas_acv_reach($db, $dsID, $prereqHTML);

  $prereqHTML = autorpt_uniquify_prereqs($prereqHTML);

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Insert a distribution by retailer report into the specified data source
#

sub autorpt_insert_catov_rtlr_distro_trends
{
  my ($dsSchema, $query, $status, $cubeID, $visID);
  my ($prodScript, $geoScript, $timeScript, $measScript, $style, $q_style);
  my ($acvMeasID);

  my ($db, $userID, $orgID, $dsID, $story) = @_;

  $dsSchema = "datasource_" . $dsID;

  #use products from the largest brand
  $prodScript = autorpt_find_prods_nth_largest_segment($db, $dsID, "brand", 1);

  #determine geography selection
  $geoScript = autorpt_find_geo_retailers($db, $dsID);

  #insert time periods (up to 104 most recent 1-weeks)
  $timeScript = autorpt_find_recent_n_weeks($db, $dsID, 104);

  #insert distribution measure
  $measScript = "M:" . autorpt_find_meas_acv_reach($db, $dsID);

  #insert basic cube definition
  $query = "INSERT INTO app.cubes (dsID, userID, orgID, name, scriptProducts, scriptGeographies, scriptTimeperiods, scriptMeasures) \
      VALUES ($dsID, $userID, $orgID, 'Retailer Distribution Trends', '$prodScript', '$geoScript', '$timeScript', '$measScript')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $cubeID = $db->{q{mysql_insertid}};

  #build the cube
  KAPutil_job_update_status($db, "Creating Category Overview: Retailer Distribution Trends report");
  cube_build($db, $dsSchema, $cubeID, $userID);

  #
  #insert the visuals and set their initial selections
  #

  #insert distribution by retailer over time table
  $acvMeasID = autorpt_find_meas_acv_reach($db, $dsID);
  $style = "width:1.0,height:1.0,xpct:0,ypct:0,";
  $style .= "condFormat1:$acvMeasID gt 80 92d050,condFormat2:$acvMeasID ib 65 80 ffff00,condFormat3:$acvMeasID lt 65 ff0000,";
  $style .= "valueFontSize:14,title:\"Retailer Distribution Trend for {PROD}\",captionAlignment:left,captionFontSize:24,";
  $style .= "tableStyle:default,headerFontSize:14,valueWrapDimension:0,headerSticky:1,";

  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, tableRowDims, tableFilterDims, tableColDims) \
      VALUES ($cubeID, $dsID, 'table', $q_style, 'g', 'p', 't')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #take all geos and the first product
  autorpt_select_n_items($db, $cubeID, $visID, "p", 1);
  autorpt_select_all_items($db, $cubeID, $visID, "g");

  #select all available 1-week time periods
  autorpt_select_all_items($db, $cubeID, $visID, "t");

  #display all measures in the table
  autorpt_select_all_items($db, $cubeID, $visID, "m");
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for category overview item distribution
# report
#

sub autorpt_prereqs_catov_item_distro_trends
{
  my ($prereqHTML);

  my ($db, $dsID) = @_;

  $prereqHTML = "";
  $prereqHTML = autorpt_prereqs_meas_acv_reach($db, $dsID, $prereqHTML);

  $prereqHTML = autorpt_uniquify_prereqs($prereqHTML);

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Insert a distribution by item report into the specified data source
#

sub autorpt_insert_catov_item_distro_trends
{
  my ($dsSchema, $query, $status, $cubeID, $visID);
  my ($prodScript, $geoScript, $timeScript, $measScript, $style, $q_style);
  my ($acvMeasID);

  my ($db, $userID, $orgID, $dsID, $story) = @_;

  $dsSchema = "datasource_" . $dsID;

  #use products from the largest brand
  $prodScript = autorpt_find_prods_nth_largest_segment($db, $dsID, "brand", 1);

  #determine geography selection
  $geoScript = autorpt_find_geo_retailers($db, $dsID);

  #insert time periods (up to 104 most recent 1-weeks)
  $timeScript = autorpt_find_recent_n_weeks($db, $dsID, 104);

  #insert distribution measure
  $measScript = "M:" . autorpt_find_meas_acv_reach($db, $dsID);

  #insert basic cube definition
  $query = "INSERT INTO app.cubes (dsID, userID, orgID, name, scriptProducts, scriptGeographies, scriptTimeperiods, scriptMeasures) \
      VALUES ($dsID, $userID, $orgID, 'Item Distribution Trends', '$prodScript', '$geoScript', '$timeScript', '$measScript')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $cubeID = $db->{q{mysql_insertid}};

  #build the cube
  KAPutil_job_update_status($db, "Creating Category Overview: Item Distribution Trends report");
  cube_build($db, $dsSchema, $cubeID, $userID);

  #
  #insert the visuals and set their initial selections
  #

  #insert distribution by retailer over time table
  $acvMeasID = autorpt_find_meas_acv_reach($db, $dsID);
  $style = "width:1.0,height:1.0,xpct:0,ypct:0,";
  $style .= "condFormat1:$acvMeasID gt 80 92d050,condFormat2:$acvMeasID ib 65 80 ffff00,condFormat3:$acvMeasID lt 65 ff0000,";
  $style .= "valueFontSize:14,title:\"Item Distribution Trend for {GEOG}\",captionAlignment:left,captionFontSize:24,";
  $style .= "tableStyle:default,headerFontSize:14,valueWrapDimension:0,headerSticky:1,";

  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, tableRowDims, tableFilterDims, tableColDims) \
      VALUES ($cubeID, $dsID, 'table', $q_style, 'p', 'g', 't')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #take all geos and the first product
  autorpt_select_all_items($db, $cubeID, $visID, "p");
  autorpt_select_n_items($db, $cubeID, $visID, "g", 1);

  #select all available 1-week time periods
  autorpt_select_all_items($db, $cubeID, $visID, "t");

  #display all measures in the table
  autorpt_select_all_items($db, $cubeID, $visID, "m");
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for category overview item distribution
# report
#

sub autorpt_prereqs_catov_retailer_acv_comp
{
  my ($prereqHTML);

  my ($db, $dsID) = @_;

  $prereqHTML = "";
  $prereqHTML = autorpt_prereqs_meas_acv_reach($db, $dsID, $prereqHTML);

  $prereqHTML = autorpt_uniquify_prereqs($prereqHTML);

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Insert a distribution by item report into the specified data source
#

sub autorpt_insert_catov_retailer_acv_comp
{
  my ($dsSchema, $query, $status, $cubeID, $visID);
  my ($prodScript, $geoScript, $timeScript, $measScript, $style, $q_style);
  my ($acvMeasID);

  my ($db, $userID, $orgID, $dsID, $story) = @_;

  $dsSchema = "datasource_" . $dsID;

  #use products from the largest brand
  $prodScript = autorpt_find_prods_nth_largest_segment($db, $dsID, "brand", 1);

  #determine geography selection
  $geoScript = autorpt_find_geo_retailers($db, $dsID);

  #insert time periods (52 most recent 1-weeks, most recent cumulatives)
  $timeScript = autorpt_create_recent_n_cum_weeks($db, $dsID, 52);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 26);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 13);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 4);

  #insert distribution measure
  $measScript = "M:" . autorpt_find_meas_acv_reach($db, $dsID);

  #insert basic cube definition
  $query = "INSERT INTO app.cubes (dsID, userID, orgID, name, scriptProducts, scriptGeographies, scriptTimeperiods, scriptMeasures) \
      VALUES ($dsID, $userID, $orgID, 'Retailer ACV Comparison', '$prodScript', '$geoScript', '$timeScript', '$measScript')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $cubeID = $db->{q{mysql_insertid}};

  #build the cube
  KAPutil_job_update_status($db, "Creating Category Overview: Retailer ACV Comparison report");
  cube_build($db, $dsSchema, $cubeID, $userID);

  #
  #insert the visuals and set their initial selections
  #

  #insert distribution by retailer over time table
  $acvMeasID = autorpt_find_meas_acv_reach($db, $dsID);
  $style = "width:1.0,height:1.0,xpct:0,ypct:0,";
  $style .= "condFormat1:$acvMeasID gt 80 92d050,condFormat2:$acvMeasID ib 65 80 ffff00,condFormat3:$acvMeasID lt 65 ff0000,";
  $style .= "valueFontSize:14,title:\"Retailer ACV Comparison\",subcaption:\"{TIME}\",captionFontSize:24,";
  $style .= "tableStyle:default,headerFontSize:14,valueWrapDimension:0,headerSticky:1,";

  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, tableRowDims, tableFilterDims, tableColDims) \
      VALUES ($cubeID, $dsID, 'table', $q_style, 'p', 't', 'g')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #take all geos and the first product
  autorpt_select_all_items($db, $cubeID, $visID, "p");
  autorpt_select_all_items($db, $cubeID, $visID, "g");

  #select 52-week cumulative time period
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);

  #display all measures in the table
  autorpt_select_all_items($db, $cubeID, $visID, "m");
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for category overview change in distribution
# report
#

sub autorpt_prereqs_catov_change_distro
{
  my ($prereqHTML);

  my ($db, $dsID) = @_;

  $prereqHTML = "";
  $prereqHTML = autorpt_prereqs_meas_acv_reach_cya($db, $dsID, $prereqHTML);

  $prereqHTML = autorpt_uniquify_prereqs($prereqHTML);

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Insert a change in distribution report into the specified data source
#

sub autorpt_insert_catov_change_distro
{
  my ($dsSchema, $query, $status, $cubeID, $visID);
  my ($prodScript, $geoScript, $timeScript, $measScript, $style, $q_style);
  my ($acvMeasID, $acvCYAMeasID);

  my ($db, $userID, $orgID, $dsID, $story) = @_;

  $dsSchema = "datasource_" . $dsID;

  #use products from the largest brand
  $prodScript = autorpt_find_prods_nth_largest_segment($db, $dsID, "brand", 1);

  #determine geography selection
  $geoScript = autorpt_find_geo_retailers($db, $dsID);

  #insert time periods (52 most recent 1-weeks, most recent cumulatives)
  $timeScript = autorpt_create_recent_n_cum_weeks($db, $dsID, 52);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 26);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 13);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 4);

  #insert distribution measure
  $measScript = "M:" . autorpt_find_meas_acv_reach($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_acv_reach_cya($db, $dsID);

  #insert basic cube definition
  $query = "INSERT INTO app.cubes (dsID, userID, orgID, name, scriptProducts, scriptGeographies, scriptTimeperiods, scriptMeasures) \
      VALUES ($dsID, $userID, $orgID, 'Changes in Distribution', '$prodScript', '$geoScript', '$timeScript', '$measScript')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $cubeID = $db->{q{mysql_insertid}};

  #build the cube
  KAPutil_job_update_status($db, "Creating Category Overview: Changes in Distribution report");
  cube_build($db, $dsSchema, $cubeID, $userID);

  #
  #insert the visuals and set their initial selections
  #

  #insert distribution by retailer over time table
  $acvMeasID = autorpt_find_meas_acv_reach($db, $dsID);
  $acvCYAMeasID = autorpt_find_meas_acv_reach_cya($db, $dsID);
  $style = "width:1.0,height:1.0,xpct:0,ypct:0,";
  $style .= "condFormat1:$acvMeasID gt 80 92d050,condFormat2:$acvMeasID ib 65 80 ffff00,condFormat3:$acvMeasID lt 65 ff0000,";
  $style .= "condFormat4:$acvCYAMeasID gt 0 92d050,condFormat5:$acvCYAMeasID lt 0 ff0000,";
  $style .= "valueFontSize:14,title:\"Retailer ACV Comparison\",subcaption:\"{TIME}\",captionFontSize:24,";
  $style .= "tableStyle:default,headerFontSize:14,valueWrapDimension:0,headerSticky:1,";

  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, tableRowDims, tableFilterDims, tableColDims) \
      VALUES ($cubeID, $dsID, 'table', $q_style, 'g', 't', 'p')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #take all geos and the first product
  autorpt_select_all_items($db, $cubeID, $visID, "p");
  autorpt_select_all_items($db, $cubeID, $visID, "g");

  #select 52-week cumulative time period
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);

  #display all measures in the table
  autorpt_select_all_items($db, $cubeID, $visID, "m");
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for category overview price tracker
# report
#

sub autorpt_prereqs_catov_price_tracker
{
  my ($prereqHTML);

  my ($db, $dsID) = @_;

  $prereqHTML = "";
  $prereqHTML = autorpt_prereqs_meas_avg_unit_price($db, $dsID, $prereqHTML);

  $prereqHTML = autorpt_uniquify_prereqs($prereqHTML);

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Insert a price tracker report into the specified data source
#

sub autorpt_insert_catov_price_tracker
{
  my ($dsSchema, $query, $status, $cubeID, $visID);
  my ($prodScript, $geoScript, $timeScript, $measScript, $style, $q_style);

  my ($db, $userID, $orgID, $dsID, $story) = @_;

  $dsSchema = "datasource_" . $dsID;

  #use products from the largest brand
  $prodScript = autorpt_find_prods_nth_largest_segment($db, $dsID, "brand", 1);

  #determine geography selection
  $geoScript = autorpt_find_geo_retailers($db, $dsID);

  #insert time periods (52 most recent 1-weeks, most recent cumulatives)
  $timeScript = autorpt_create_recent_n_cum_weeks($db, $dsID, 52);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 26);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 13);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 4);

  #insert distribution measure
  $measScript = "M:" . autorpt_create_meas_avg_unit_price($db, $dsID);

  #insert basic cube definition
  $query = "INSERT INTO app.cubes (dsID, userID, orgID, name, scriptProducts, scriptGeographies, scriptTimeperiods, scriptMeasures) \
      VALUES ($dsID, $userID, $orgID, 'Price Tracker', '$prodScript', '$geoScript', '$timeScript', '$measScript')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $cubeID = $db->{q{mysql_insertid}};

  #build the cube
  KAPutil_job_update_status($db, "Creating Category Overview: Price Tracker report");
  cube_build($db, $dsSchema, $cubeID, $userID);

  #
  #insert the visuals and set their initial selections
  #

  #insert price tracker table
  $style = "width:1.0,height:1.0,xpct:0,ypct:0,";
  $style .= "valueFontSize:14,title:\"Price Tracker\",subcaption:\"{TIME}\",captionFontSize:24,";
  $style .= "tableStyle:default,headerFontSize:14,valueWrapDimension:0,headerSticky:1,";
  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, tableRowDims, tableFilterDims, tableColDims) \
      VALUES ($cubeID, $dsID, 'table', $q_style, 'p', 't', 'g')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #take all products and geos
  autorpt_select_all_items($db, $cubeID, $visID, "p");
  autorpt_select_all_items($db, $cubeID, $visID, "g");

  #select 52-week cumulative time period
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);

  #display all measures in the table
  autorpt_select_all_items($db, $cubeID, $visID, "m");
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for category overview: units sold by price
# report
#

sub autorpt_prereqs_catov_units_sold_price
{
  my ($prereqHTML);

  my ($db, $dsID) = @_;

  $prereqHTML = "";
  $prereqHTML = autorpt_prereqs_meas_avg_unit_price($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_units($db, $dsID, $prereqHTML);

  $prereqHTML = autorpt_uniquify_prereqs($prereqHTML);

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Insert a units sold by price report into the specified data source
#

sub autorpt_insert_catov_units_sold_price
{
  my ($dsSchema, $query, $status, $cubeID, $visID);
  my ($prodScript, $geoScript, $timeScript, $measScript, $style, $q_style);
  my ($avgPriceMeasID, $unitsMeasID);

  my ($db, $userID, $orgID, $dsID, $story) = @_;

  $dsSchema = "datasource_" . $dsID;

  #use products from the largest brand
  $prodScript = autorpt_find_prods_nth_largest_segment($db, $dsID, "brand", 1);

  #determine geography selection
  $geoScript = autorpt_find_geo_retailers($db, $dsID);

  #insert time periods (up to 104 most recent 1-weeks)
  $timeScript = autorpt_find_recent_n_weeks($db, $dsID, 104);

  #insert distribution measure
  $measScript = "M:" . autorpt_create_meas_avg_unit_price($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_find_meas_units($db, $dsID);

  #insert basic cube definition
  $query = "INSERT INTO app.cubes (dsID, userID, orgID, name, scriptProducts, scriptGeographies, scriptTimeperiods, scriptMeasures) \
      VALUES ($dsID, $userID, $orgID, 'Units Sold by Price', '$prodScript', '$geoScript', '$timeScript', '$measScript')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $cubeID = $db->{q{mysql_insertid}};

  #build the cube
  KAPutil_job_update_status($db, "Creating Category Overview: Units Sold by Price report");
  cube_build($db, $dsSchema, $cubeID, $userID);

  #
  #insert the visuals and set their initial selections
  #

  #insert price tracker table
  $avgPriceMeasID = autorpt_find_meas_avg_unit_price($db, $dsID);
  $unitsMeasID = autorpt_find_meas_units($db, $dsID);
  $style = ",type:Scatter,measureX:$unitsMeasID,measureY:$avgPriceMeasID,";
  $style .= "width:1.0,height:1.0,xpct:0,ypct:0,showLegend:0,";
  $style .= "caption:\"Units Sold by Average Price\",subcaption:\"Weekly Sales at {GEOG}\",";
  $style .= "captionFontSize:24,xAxisName:\"Units\",yAxisName:\"Average Price\",";
  $style .= "xAxisNameFontSize:16,yAxisNameFontSize:16,showValues:0,";
  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 't')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #select first product and geography
  autorpt_select_n_items($db, $cubeID, $visID, "p", 1);
  autorpt_select_n_items($db, $cubeID, $visID, "g", 1);

  #select all available weekly time periods
  autorpt_select_all_items($db, $cubeID, $visID, "t");

  #display all measures
  autorpt_select_all_items($db, $cubeID, $visID, "m");
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for category overview: $ promo tracker
# report
#

sub autorpt_prereqs_catov_promo_tracker_dollars
{
  my ($prereqHTML);

  my ($db, $dsID) = @_;

  $prereqHTML = "";
  $prereqHTML = autorpt_prereqs_meas_acv_reach($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_dollars($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_base_dollars($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_incr_dollars($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_promo_vehicle_dollars($db, $dsID, "disp wo feat", $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_promo_vehicle_dollars($db, $dsID, "feat and disp", $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_promo_vehicle_dollars($db, $dsID, "feat wo disp", $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_promo_vehicle_dollars($db, $dsID, "price decr", $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_avg_unit_price($db, $dsID, $prereqHTML);

  $prereqHTML = autorpt_uniquify_prereqs($prereqHTML);

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Insert a $ promo tracker report into the specified data source
#

sub autorpt_insert_catov_promo_tracker_dollars
{
  my ($dsSchema, $query, $status, $cubeID, $visID);
  my ($prodScript, $geoScript, $timeScript, $measScript, $style, $q_style);
  my ($baseDolsMeasID, $incrDolsMeasID, $avgPriceMeasID, $avgPriceYAMeasID);
  my ($dolsYAMeasID);

  my ($db, $userID, $orgID, $dsID, $story) = @_;

  $dsSchema = "datasource_" . $dsID;

  #use products from the largest brand
  $prodScript = autorpt_find_prods_nth_largest_segment($db, $dsID, "brand", 1);

  #determine geography selection
  $geoScript = autorpt_find_geo_retailers($db, $dsID);

  #insert time periods (up to 52 most recent 1-weeks)
  $timeScript = autorpt_find_recent_n_weeks($db, $dsID, 52);

  #insert distribution measure
  $measScript = "M:" . autorpt_find_meas_dollars($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_find_meas_dollars_ya($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_find_meas_base_dollars($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_incr_dollars($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_avg_unit_price($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_avg_unit_price_ya($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_find_meas_promo_vehicle_dollars($db, $dsID, "disp wo feat");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_find_meas_promo_vehicle_dollars($db, $dsID, "feat and disp");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_find_meas_promo_vehicle_dollars($db, $dsID, "feat wo disp");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_find_meas_promo_vehicle_dollars($db, $dsID, "price decr");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_find_meas_acv_reach($db, $dsID);

  #insert basic cube definition
  $query = "INSERT INTO app.cubes (dsID, userID, orgID, name, scriptProducts, scriptGeographies, scriptTimeperiods, scriptMeasures) \
      VALUES ($dsID, $userID, $orgID, 'Promotion Tracker \$', '$prodScript', '$geoScript', '$timeScript', '$measScript')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $cubeID = $db->{q{mysql_insertid}};

  #build the cube
  KAPutil_job_update_status($db, "Creating Category Overview: Promotion Tracker \$ report");
  cube_build($db, $dsSchema, $cubeID, $userID);

  #
  #insert the visuals and set their initial selections
  #

  #insert bump chart
  $baseDolsMeasID = autorpt_find_meas_base_dollars($db, $dsID);
  $incrDolsMeasID = autorpt_find_meas_incr_dollars($db, $dsID);
  $avgPriceMeasID = autorpt_find_meas_avg_unit_price($db, $dsID);
  $avgPriceYAMeasID = autorpt_find_meas_avg_unit_price_ya($db, $dsID);
  $dolsYAMeasID = autorpt_find_meas_dollars_ya($db, $dsID);
  $style = ",type:DualY,width:1.0,height:0.6,xpct:0.0,ypct:0.0,zindex:1,";
  $style .= "caption:\"Promo Dollar Sales for {PROD}\",subcaption:\"{GEOG}\",captionFontSize:24,";
  $style .= "showValues:0,";
  $style .= "comboSecondaryAxis:$avgPriceMeasID-$avgPriceYAMeasID-,comboGraphAsLine:$avgPriceMeasID-$avgPriceYAMeasID-,comboGraphAsColumn:$baseDolsMeasID-$incrDolsMeasID-,comboGraphAsArea:$dolsYAMeasID-,";
  $style .= "lockTimes:1,labelDisplay:rotate,labelFontSize:12,legendItemFontSize:14,";

  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x, graph_y) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 't', 'm')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #select first product and geography
  autorpt_select_n_items($db, $cubeID, $visID, "p", 1);
  autorpt_select_n_items($db, $cubeID, $visID, "g", 1);

  #select all available weekly time periods
  autorpt_select_all_items($db, $cubeID, $visID, "t");

  #select bump chart measures
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "dollars ya");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "base dollars");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "incr dollars");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "avg unit price");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "avg unit price ya");

  #insert promo data table
  $style = "width:1.0,height:0.4,xpct:0,ypct:0.6,";
  $style .= "zindex:2,sortMeas1:time,sortOrder1:DESC,";
  $style .= "valueFontSize:14,tableStyle:default,headerWrap:1,headerFontSize:14,";
  $style .= "valueWrapDimension:0,valueWrap:0,";
  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, tableRowDims, tableFilterDims) \
      VALUES ($cubeID, $dsID, 'table', $q_style, 't', 'p,g')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #select first product and geography
  autorpt_select_n_items($db, $cubeID, $visID, "p", 1);
  autorpt_select_n_items($db, $cubeID, $visID, "g", 1);

  #select all available weekly time periods
  autorpt_select_all_items($db, $cubeID, $visID, "t");

  #display all measures in the table
  autorpt_select_all_items($db, $cubeID, $visID, "m");
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for category overview: $ promo tracker
# report
#

sub autorpt_prereqs_catov_promo_tracker_units
{
  my ($prereqHTML);

  my ($db, $dsID) = @_;

  $prereqHTML = "";
  $prereqHTML = autorpt_prereqs_meas_acv_reach($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_units($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_base_units($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_incr_units($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_promo_vehicle_units($db, $dsID, "disp wo feat", $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_promo_vehicle_units($db, $dsID, "feat and disp", $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_promo_vehicle_units($db, $dsID, "feat wo disp", $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_promo_vehicle_units($db, $dsID, "price decr", $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_avg_unit_price($db, $dsID, $prereqHTML);

  $prereqHTML = autorpt_uniquify_prereqs($prereqHTML);

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Insert a units promo tracker report into the specified data source
#

sub autorpt_insert_catov_promo_tracker_units
{
  my ($dsSchema, $query, $status, $cubeID, $visID);
  my ($prodScript, $geoScript, $timeScript, $measScript, $style, $q_style);
  my ($baseUnitsMeasID, $incrUnitsMeasID, $avgPriceMeasID, $avgPriceYAMeasID);
  my ($unitsYAMeasID);

  my ($db, $userID, $orgID, $dsID, $story) = @_;

  $dsSchema = "datasource_" . $dsID;

  #use products from the largest brand
  $prodScript = autorpt_find_prods_nth_largest_segment($db, $dsID, "brand", 1);

  #determine geography selection
  $geoScript = autorpt_find_geo_retailers($db, $dsID);

  #insert time periods (up to 52 most recent 1-weeks)
  $timeScript = autorpt_find_recent_n_weeks($db, $dsID, 52);

  #insert distribution measure
  $measScript = "M:" . autorpt_find_meas_units($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_find_meas_units_ya($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_find_meas_base_units($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_incr_units($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_avg_unit_price($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_avg_unit_price_ya($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_find_meas_promo_vehicle_units($db, $dsID, "disp wo feat");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_find_meas_promo_vehicle_units($db, $dsID, "feat and disp");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_find_meas_promo_vehicle_units($db, $dsID, "feat wo disp");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_find_meas_promo_vehicle_units($db, $dsID, "price decr");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_find_meas_acv_reach($db, $dsID);

  #insert basic cube definition
  $query = "INSERT INTO app.cubes (dsID, userID, orgID, name, scriptProducts, scriptGeographies, scriptTimeperiods, scriptMeasures) \
      VALUES ($dsID, $userID, $orgID, 'Promotion Tracker Units', '$prodScript', '$geoScript', '$timeScript', '$measScript')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $cubeID = $db->{q{mysql_insertid}};

  #build the cube
  KAPutil_job_update_status($db, "Creating Category Overview: Promotion Tracker Units report");
  cube_build($db, $dsSchema, $cubeID, $userID);

  #
  #insert the visuals and set their initial selections
  #

  #insert bump chart
  $baseUnitsMeasID = autorpt_find_meas_base_units($db, $dsID);
  $incrUnitsMeasID = autorpt_find_meas_incr_units($db, $dsID);
  $avgPriceMeasID = autorpt_find_meas_avg_unit_price($db, $dsID);
  $avgPriceYAMeasID = autorpt_find_meas_avg_unit_price_ya($db, $dsID);
  $unitsYAMeasID = autorpt_find_meas_units_ya($db, $dsID);
  $style = ",type:DualY,width:1.0,height:0.6,xpct:0.0,ypct:0.0,zindex:1,";
  $style .= "caption:\"Promo Unit Sales for {PROD}\",subcaption:\"{GEOG}\",captionFontSize:24,";
  $style .= "showValues:0,";
  $style .= "comboSecondaryAxis:$avgPriceMeasID-$avgPriceYAMeasID-,comboGraphAsLine:$avgPriceMeasID-$avgPriceYAMeasID-,comboGraphAsColumn:$baseUnitsMeasID-$incrUnitsMeasID-,comboGraphAsArea:$unitsYAMeasID-,";
  $style .= "lockTimes:1,labelDisplay:rotate,labelFontSize:12,legendItemFontSize:14,";

  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x, graph_y) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 't', 'm')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #select first product and geography
  autorpt_select_n_items($db, $cubeID, $visID, "p", 1);
  autorpt_select_n_items($db, $cubeID, $visID, "g", 1);

  #select all available weekly time periods
  autorpt_select_all_items($db, $cubeID, $visID, "t");

  #select bump chart measures
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "units ya");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "base units");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "incr units");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "avg unit price");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "avg unit price ya");

  #insert promo data table
  $style = "width:1.0,height:0.4,xpct:0,ypct:0.6,";
  $style .= "zindex:2,sortMeas1:time,sortOrder1:DESC,";
  $style .= "valueFontSize:14,tableStyle:default,headerWrap:1,headerFontSize:14,";
  $style .= "valueWrapDimension:0,valueWrap:0,";
  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, tableRowDims, tableFilterDims) \
      VALUES ($cubeID, $dsID, 'table', $q_style, 't', 'p,g')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #select first product and geography
  autorpt_select_n_items($db, $cubeID, $visID, "p", 1);
  autorpt_select_n_items($db, $cubeID, $visID, "g", 1);

  #select all available weekly time periods
  autorpt_select_all_items($db, $cubeID, $visID, "t");

  #display all measures in the table
  autorpt_select_all_items($db, $cubeID, $visID, "m");
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for category overview: $ promo tracker
# report
#

sub autorpt_prereqs_catov_lift_vs_discount
{
  my ($prereqHTML);

  my ($db, $dsID) = @_;

  $prereqHTML = "";
  $prereqHTML = autorpt_prereqs_meas_dollar_pct_lift($db, $dsID, $prereqHTML);

  $prereqHTML = autorpt_uniquify_prereqs($prereqHTML);

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Insert a lift vs discount report into the specified data source
#

sub autorpt_insert_catov_lift_vs_discount
{
  my ($dsSchema, $query, $status, $cubeID, $visID);
  my ($prodScript, $geoScript, $timeScript, $measScript, $style, $q_style);
  my ($dolPctLiftMeasID, $pctDolPromoMeasID, $promoDiscMeasID);

  my ($db, $userID, $orgID, $dsID, $story) = @_;

  $dsSchema = "datasource_" . $dsID;

  #use products at the brand level
  $prodScript = autorpt_find_seg_brand($db, $dsID);

  #determine geography selection
  $geoScript = autorpt_find_geo_total_us($db, $dsID);
  $geoScript .= ",";
  $geoScript .= autorpt_find_geo_retailers($db, $dsID);

  #insert time periods (most recent cumulatives)
  $timeScript = autorpt_create_recent_n_cum_weeks($db, $dsID, 52);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 26);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 13);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 4);

  #insert measures
  $measScript = "M:" . autorpt_create_meas_dollar_pct_lift($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_pct_dollars_any_promo($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_any_promo_disc($db, $dsID);

  #insert basic cube definition
  $query = "INSERT INTO app.cubes (dsID, userID, orgID, name, scriptProducts, scriptGeographies, scriptTimeperiods, scriptMeasures) \
      VALUES ($dsID, $userID, $orgID, 'Lift vs Discount', '$prodScript', '$geoScript', '$timeScript', '$measScript')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $cubeID = $db->{q{mysql_insertid}};

  #build the cube
  KAPutil_job_update_status($db, "Creating Category Overview: Lift vs Discount report");
  cube_build($db, $dsSchema, $cubeID, $userID);

  #
  #insert the visuals and set their initial selections
  #

  #insert bubble chart
  $dolPctLiftMeasID = autorpt_find_meas_dollar_pct_lift($db, $dsID);
  $pctDolPromoMeasID = autorpt_find_meas_pct_dollars_any_promo($db, $dsID);
  $promoDiscMeasID = autorpt_find_meas_any_promo_disc($db, $dsID);
  $style = ",type:Bubble,width:1.0,height:1.0,xpct:0.0,ypct:0.0,";
  $style .= "measureX:$promoDiscMeasID,measureY:$dolPctLiftMeasID,measureZ:$pctDolPromoMeasID,showRegressionLine:1,";
  $style .= "showLabels:1,labelFontSize:14,showValues:1,valueFontSize:14,";
  $style .= "showLegend:0,filterMeas1:$pctDolPromoMeasID,filterOp1:top,filterNum1:7,";
  $style .= "caption:\"Lift vs Discount Promo Impact for {PROD}\",subcaption:\"Bubble Size=% \$, Any Promo\",captionFontSize:20,";
  $style .= "xAxisName:\"Discount %\",yAxisName:\"% Lift\",xAxisNameFontSize:14,yAxisNameFontSize:14,";
  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 'g')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #take the largest brand and all geographies
  autorpt_select_prod_n_segments($db, $dsID, $cubeID, $visID, "brand", 1);
  autorpt_select_all_items($db, $cubeID, $visID, "g");

  #select the most recent 52 1-week time periods
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);

  #select the measures
  autorpt_select_all_items($db, $cubeID, $visID, "m");
}









###############################################################################
#
#
#
#
#                   REPORTS: TOP-LINE BUSINESS REVIEW
#
#
#
#
###############################################################################




#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for overview top-line report
#

sub autorpt_prereqs_tlbr_overview
{
  my ($prereqHTML);

  my ($db, $dsID) = @_;

  $prereqHTML = "";
  $prereqHTML = autorpt_prereqs_meas_dollar_pctchg_ya($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_units_pctchg_ya($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_dollar_shr_category($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_dollar_shr_category_cya($db, $dsID, $prereqHTML);

  $prereqHTML = autorpt_uniquify_prereqs($prereqHTML);

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Insert overview top-line report into the specified data source
#

sub autorpt_insert_tlbr_overview
{
  my ($dsSchema, $query, $status, $cubeID, $visID);
  my ($prodScript, $geoScript, $timeScript, $measScript, $style, $q_style);
  my ($dolShareCatMeasID, $dolShareCatCYAMeasID);

  my ($db, $userID, $orgID, $dsID, $story) = @_;

  $dsSchema = "datasource_" . $dsID;

  #determine product selection
  $prodScript = autorpt_find_seg_category($db, $dsID);
  $prodScript .= ",";
  $prodScript .= autorpt_find_seg_brand($db, $dsID);

  #determine geography selection
  $geoScript = autorpt_find_geo_total_us($db, $dsID);
  $geoScript .= ",";
  $geoScript .= autorpt_find_geo_retailers($db, $dsID);

  #insert time periods (52 most recent 1-weeks, most recent cumulatives)
  $timeScript = autorpt_create_recent_n_cum_weeks($db, $dsID, 52);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 26);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 13);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 4);
  $timeScript .= ",";
  $timeScript .= autorpt_find_recent_52_weeks($db, $dsID);

  #insert measures
  $measScript = "M:" . autorpt_create_meas_dollar_pctchg_ya($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_units_pctchg_ya($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_dollar_shr_cat($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_dollar_shr_cat_cya($db, $dsID);

  #insert basic cube definition
  $query = "INSERT INTO app.cubes (dsID, userID, orgID, name, scriptProducts, scriptGeographies, scriptTimeperiods, scriptMeasures) \
      VALUES ($dsID, $userID, $orgID, 'Topline Overview', '$prodScript', '$geoScript', '$timeScript', '$measScript')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $cubeID = $db->{q{mysql_insertid}};

  #build the cube
  KAPutil_job_update_status($db, "Creating Topline Overview report");
  cube_build($db, $dsSchema, $cubeID, $userID);

  #
  #insert the visuals and set their initial selections
  #

  #insert top left column chart
  $style = ",type:2DColumns,showLabels:1,showLegend:1,legendPosition:bottom,width:0.5,height:0.5,xpct:0.0,ypct:0.0,";
  $style .= "caption:\"\%Chg in \$/Units\",subcaption:\"{PROD}, {GEOG}\",captionFontSize:24,zindex:1,";
  $style .= "showValues:1,decimals:1,placeValuesInside:0,valueFontSize:14,labelDisplay:auto,";
  $style .= "labelStep:1,labelFontSize:14,legendItemFontSize:12,";
  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x, graph_y) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 't', 'm')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #take the first brand and the first geography
  autorpt_select_prod_n_segments($db, $dsID, $cubeID, $visID, "brand", 1);
  autorpt_select_n_items($db, $cubeID, $visID, "g", 1);

  #select cumulative time periods
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 26);
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 13);
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 4);

  #select the dollars % change and units % change measures
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "dollar pctchg ya");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "units pctchg ya");

  #insert top-right dual-Y chart
  $dolShareCatMeasID = autorpt_find_meas_dollar_shr_category($db, $dsID);
  $dolShareCatCYAMeasID = autorpt_find_meas_dollar_shr_category_cya($db, $dsID);
  $style = ",type:DualY,width:0.5,height:0.5,xpct:0.5,ypct:0.0,zindex:2,";
  $style .= "comboSecondaryAxis:$dolShareCatCYAMeasID,comboGraphAsColumn:$dolShareCatMeasID,comboGraphAsLine:$dolShareCatCYAMeasID,";
  $style .= "caption:\"Share of Category\",subcaption:\"{PROD}, {GEOG}\",captionFontSize:24,";
  $style .= "showLabels:1,labelDisplay:auto,labelStep:1,labelFontSize:14,showValues:1,formatNumberScale:1,";
  $style .= "decimals:2,placeValuesInside:0,valueFontSize:14,legendItemFontSize:12,";
  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x, graph_y) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 't', 'm')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #take the first brand and the first geography
  autorpt_select_prod_n_segments($db, $dsID, $cubeID, $visID, "brand", 1);
  autorpt_select_n_items($db, $cubeID, $visID, "g", 1);

  #select cumulative time periods
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 26);
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 13);
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 4);

  #select the share of category and change in category share
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "dollar shr category");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "dollar shr category cya");

  #insert bottom line chart
  $style = ",type:Lines,width:1.0,height:0.5,xpct:0.0,ypct:0.5,zindex:3,showValues:0,formatNumberScale:1,decimals:1,";
  $style .= "placeValuesInside:0,showLabels:1,labelDisplay:auto,labelStep:4,labelFontSize:14,";
  $style .= "caption:\"{MEAS} Trend\",subcaption:\"{GEOG}\",captionFontSize:24,";
  $style .= "showLegend:1,legendPosition:bottom,legendItemFontSize:12,";
  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x, graph_y) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 't', 'p')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #take the first category and the first geography
  autorpt_select_prod_n_segments($db, $dsID, $cubeID, $visID, "category", 1);
  autorpt_select_prod_n_segments($db, $dsID, $cubeID, $visID, "brand", 3);
  autorpt_select_n_items($db, $cubeID, $visID, "g", 1);

  #select the most recent 52 1-week time periods
  autorpt_select_time_52_weeks($db, $dsID, $cubeID, $visID);

  #select the dollars % change measure
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "dollar pctchg ya");
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for Total Business Cross-Category top-line
# report
#

sub autorpt_prereqs_tlbr_total_bus_crosscat
{
  my ($prereqHTML);

  my ($db, $dsID) = @_;

  $prereqHTML = "";
  $prereqHTML = autorpt_prereqs_meas_dollars($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_dollar_pctchg_ya($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_dollar_shr_category($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_dollar_shr_category_cya($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_units($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_units_pctchg_ya($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_units_shr_category($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_units_shr_category_cya($db, $dsID, $prereqHTML);

  $prereqHTML = autorpt_uniquify_prereqs($prereqHTML);

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Insert a top-line Total Business Cross-Category into the specified DS
#

sub autorpt_insert_tlbr_total_bus_crosscat
{
  my ($dsSchema, $query, $status, $cubeID, $visID);
  my ($prodScript, $geoScript, $timeScript, $measScript, $style, $q_style);

  my ($db, $userID, $orgID, $dsID, $story) = @_;

  DBG("Creating report Top-Line Business Review:Total Business Cross-Category");

  $dsSchema = "datasource_" . $dsID;

  #determine product selection
  $prodScript = autorpt_find_seg_category($db, $dsID);
  $prodScript .= ",";
  $prodScript .= autorpt_create_seghier_manufacturer_brand($db, $dsID);

  #determine geography selection
  $geoScript = autorpt_find_geo_total_us($db, $dsID);
  $geoScript .= ",";
  $geoScript .= autorpt_find_geo_retailers($db, $dsID);

  #insert time periods (most recent cumulatives)
  $timeScript = autorpt_create_recent_n_cum_weeks($db, $dsID, 52);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 26);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 13);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 4);

  #insert measures
  $measScript = "M:" . autorpt_find_meas_dollars($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_dollar_pctchg_ya($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_dollar_shr_cat($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_dollar_shr_cat_cya($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_find_meas_units($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_units_pctchg_ya($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_units_shr_cat($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_units_shr_cat_cya($db, $dsID);

  #insert basic cube definition
  $query = "INSERT INTO app.cubes (dsID, userID, orgID, name, scriptProducts, scriptGeographies, scriptTimeperiods, scriptMeasures) \
      VALUES ($dsID, $userID, $orgID, 'Total Business Cross-Category', '$prodScript', '$geoScript', '$timeScript', '$measScript')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $cubeID = $db->{q{mysql_insertid}};

  #build the cube
  KAPutil_job_update_status($db, "Creating Total Business Cross-Category report");
  cube_build($db, $dsSchema, $cubeID, $userID);

  #
  #insert the visuals and set their initial selections
  #

  #insert table
  $style = "width:1.0,height:1.0,xpct:0,ypct:0,headerFontColor:#ffffff,headerBgColor:#333333,headerWrap:1,";
  $style = "headerSticky:1,headerFontSize:14,headerOutline:bottom,valueFontColor:#333333,valueBgColor:#ffffff,";
  $style = "valueAlternateFontColor:#333333,valueAlternateBgColor:#efefef,valueWrapDimension:0,valueWrap:0,valueFontSize:14,";
  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, tableRowDims, tableFilterDims, tableColDims, design) \
      VALUES ($cubeID, $dsID, 'table', 'p', 'g', 't', $q_style)";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #take the first couple brands and the first geography
  autorpt_select_all_items($db, $cubeID, $visID, "p");
  autorpt_select_n_items($db, $cubeID, $visID, "g", 1);

  #select 52, 26, and 4-week cumulative time periods
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 26);
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 4);

  #display all measures in the table
  autorpt_select_all_items($db, $cubeID, $visID, "m");
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for category snapshot top-line report
#

sub autorpt_prereqs_tlbr_category_snapshot
{
  my ($prereqHTML);

  my ($db, $dsID) = @_;

  $prereqHTML = "";
  $prereqHTML = autorpt_prereqs_meas_dollars($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_dollar_shr_category_cya($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_units($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_units_shr_category_cya($db, $dsID, $prereqHTML);

  $prereqHTML = autorpt_uniquify_prereqs($prereqHTML);

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Insert a topline business review category snapshot report into the specified
# data source
#

sub autorpt_insert_tlbr_category_snapshot
{
  my ($dsSchema, $query, $status, $cubeID, $visID);
  my ($prodScript, $geoScript, $timeScript, $measScript, $style, $q_style);
  my ($dolSalesMeasID);

  my ($db, $userID, $orgID, $dsID, $story) = @_;

  DBG("Creating report Top-Line Business Review:Category Snapshot");

  $dsSchema = "datasource_" . $dsID;

  #determine product selection
  $prodScript = autorpt_find_seg_brand($db, $dsID);

  #determine geography selection
  $geoScript = autorpt_find_geo_total_us($db, $dsID);
  $geoScript .= ",";
  $geoScript .= autorpt_find_geo_retailers($db, $dsID);

  #insert time periods (52 most recent 1-weeks, most recent cumulatives)
  $timeScript = autorpt_create_recent_n_cum_weeks($db, $dsID, 52);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 26);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 13);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 4);

  #insert measures
  $measScript = "M:" . autorpt_find_meas_dollars($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_dollar_shr_cat_cya($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_find_meas_units($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_units_shr_cat_cya($db, $dsID);

  #insert basic cube definition
  $query = "INSERT INTO app.cubes (dsID, userID, orgID, name, scriptProducts, scriptGeographies, scriptTimeperiods, scriptMeasures) \
      VALUES ($dsID, $userID, $orgID, 'Category Snapshot', '$prodScript', '$geoScript', '$timeScript', '$measScript')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $cubeID = $db->{q{mysql_insertid}};

  #build the cube
  KAPutil_job_update_status($db, "Creating Category Snapshot report");
  cube_build($db, $dsSchema, $cubeID, $userID);

  #
  #insert the visuals and set their initial selections
  #

  #insert left-side pie chart
  $dolSalesMeasID = autorpt_find_meas_dollars($db, $dsID);
  $style = ",type:2DDonut,showLabels:0,showLegend:1,legendPosition:bottom,width:0.5,height:1.0,xpct:0.0,ypct:0.0,zindex:1,";
  $style .= "filterMeas1:$dolSalesMeasID,filterOp1:top,filterNum1:10,allOthers:true,";
  $style .= "legendItemFontSize:12,caption:\"Share for Top Brands\",subcaption:\"{GEOG} {TIME}\",captionFontSize:24,";
  $style .= "showValues:1,formatNumberScale:1,showPercentValues:1,decimals:1,valueFontSize:12,";
  $style .= "labelDisplay:auto,labelStep:1,labelFontSize:12,";
  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 'p')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #take all of the brands and the first geography
  autorpt_select_all_items($db, $cubeID, $visID, "p");
  autorpt_select_n_items($db, $cubeID, $visID, "g", 1);

  #select 52-week cumulative time period
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);

  #select the dollars measure
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "dollars");

  #insert bar chart on right
  $style = ",type:2DBars,showLabels:1,showLegend:1,legendPosition:bottom,width:0.5,height:1.0,xpct:0.5,ypct:0.0,zindex:2,";
  $style .= "filterMeas1:$dolSalesMeasID,filterOp1:top,filterNum1:10,allOthers:false,";
  $style .= "caption:\"Share Change for Top Brands\",subcaption:\"{GEOG} {TIME}\",captionFontSize:24,";
  $style .= "showValues:1,formatNumberScale:1,decimals:1,placeValuesInside:0,valueFontSize:12,";
  $style .= "legendItemFontSize:12,labelFontColor:#333333,labelDisplay:auto,labelStep:1,labelFontSize:12,";
  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x, graph_y) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 'p', 'm')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #take all of the brands and the first geography
  autorpt_select_all_items($db, $cubeID, $visID, "p");
  autorpt_select_n_items($db, $cubeID, $visID, "g", 1);

  #select 52-week cumulative time period
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);

  #select the dollars measure
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "dollar shr category cya");
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for Retailers Driving Item Trends top-line
# report
#

sub autorpt_prereqs_tlbr_retailer_item_trends
{
  my ($prereqHTML);

  my ($db, $dsID) = @_;

  $prereqHTML = "";
  $prereqHTML = autorpt_prereqs_meas_dollars_cya($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_dollar_pctchg_ya($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_units_cya($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_units_pctchg_ya($db, $dsID, $prereqHTML);

  $prereqHTML = autorpt_uniquify_prereqs($prereqHTML);

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Insert a top-line Retailers Driving Item Trends report into the specified DS
#

sub autorpt_insert_tlbr_retailer_item_trends
{
  my ($dsSchema, $query, $status, $cubeID, $visID);
  my ($prodScript, $geoScript, $timeScript, $measScript, $style, $q_style);
  my ($dolPctChangeMeasID);

  my ($db, $userID, $orgID, $dsID, $story) = @_;

  DBG("Creating report Top-Line Business Review:Retailers Driving Item Trends");

  $dsSchema = "datasource_" . $dsID;

  #determine product selection
  $prodScript = autorpt_find_prods_nth_largest_segment($db, $dsID, "brand", 1);

  #determine geography selection
  $geoScript = autorpt_find_geo_retailers($db, $dsID);

  #insert time periods (most recent cumulatives)
  $timeScript = autorpt_create_recent_n_cum_weeks($db, $dsID, 52);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 26);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 13);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 4);

  #insert measures
  $measScript = "M:" . autorpt_create_meas_dollar_pctchg_ya($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_dollars_cya($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_units_pctchg_ya($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_units_cya($db, $dsID);

  #insert basic cube definition
  $query = "INSERT INTO app.cubes (dsID, userID, orgID, name, scriptProducts, scriptGeographies, scriptTimeperiods, scriptMeasures) \
      VALUES ($dsID, $userID, $orgID, 'Retailers Driving Item Trends', '$prodScript', '$geoScript', '$timeScript', '$measScript')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $cubeID = $db->{q{mysql_insertid}};

  #build the cube
  KAPutil_job_update_status($db, "Creating Retailers Driving Item Trends report");
  cube_build($db, $dsSchema, $cubeID, $userID);

  #
  #insert the visuals and set their initial selections
  #

  #insert left bar chart
  $dolPctChangeMeasID = autorpt_find_meas_dollar_pctchg_ya($db, $dsID);
  $style = ",type:2DBars,showLabels:1,showLegend:1,legendPosition:bottom,width:0.5,height:1.0,xpct:0,ypct:0,";
  $style .= "filterMeas1:$dolPctChangeMeasID,filterOp1:top,filterNum1:10,allOthers:false,";
  $style .= "caption:\"% Dollar Change\",subcaption:\"{PROD}, {TIME}\",captionFontSize:24,";
  $style .= "showValues:1,formatNumberScale:1,decimals:1,placeValuesInside:0,valueFontSize:14,";
  $style .= "labelDisplay:auto,labelStep:1,labelFontSize:14,zindex:1,";
  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x, graph_y) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 'g', 'm')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #take the first item
  autorpt_select_n_items($db, $cubeID, $visID, "p", 1);

  #take all geographies
  autorpt_select_all_items($db, $cubeID, $visID, "g");

  #select 52 week cumulative time period
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);

  #select dollar % chg measure
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "dollar pctchg ya");

  #insert right bar chart
  $style = ",type:2DBars,showLabels:1,showLegend:1,legendPosition:bottom,width:0.5,height:1.0,xpct:0.5,ypct:0.0,zindex:2,";
  $style .= "caption:\"Actual Dollar Change\",subcaption:\"{PROD}, {TIME}\",captionFontSize:24,";
  $style .= "filterMeas1:$dolPctChangeMeasID,filterOp1:top,filterNum1:10,allOthers:false,";
  $style .= "labelDisplay:auto,labelStep:1,labelFontSize:14,showValues:1,formatNumberScale:1,decimals:1,";
  $style .= "placeValuesInside:0,valueFontSize:14,";
  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x, graph_y) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 'g', 'm')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #take the first item
  autorpt_select_n_items($db, $cubeID, $visID, "p", 1);

  #take all geographies
  autorpt_select_all_items($db, $cubeID, $visID, "g");

  #select 52 week cumulative time period
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);

  #select dollar % chg measure
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "dollar cya");
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for Retailers Driving Brand Trends top-line
# report
#

sub autorpt_prereqs_tlbr_retailer_brand_trends
{
  my ($prereqHTML);

  my ($db, $dsID) = @_;

  $prereqHTML = "";
  $prereqHTML = autorpt_prereqs_meas_dollars_cya($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_units_cya($db, $dsID, $prereqHTML);

  $prereqHTML = autorpt_uniquify_prereqs($prereqHTML);

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Insert a top-line Retailer Driving Brand Trends report into the specified DS
#

sub autorpt_insert_tlbr_retailer_brand_trends
{
  my ($dsSchema, $query, $status, $cubeID, $visID);
  my ($prodScript, $geoScript, $timeScript, $measScript, $style, $q_style);
  my ($dolChangeMeasID);

  my ($db, $userID, $orgID, $dsID, $story) = @_;

  DBG("Creating report Top-Line Business Review:Retailers Driving Brand Trends");

  $dsSchema = "datasource_" . $dsID;

  #determine product selection
  $prodScript = autorpt_find_seg_category($db, $dsID);
  $prodScript .= ",";
  $prodScript .= autorpt_find_seg_brand($db, $dsID);

  #determine geography selection
  $geoScript = autorpt_find_geo_retailers($db, $dsID);

  #insert time periods (52 most recent 1-weeks, most recent cumulatives)
  $timeScript = autorpt_create_recent_n_cum_weeks($db, $dsID, 52);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 26);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 13);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 4);

  #insert measures
  $measScript = "M:" . autorpt_create_meas_dollars_cya($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_units_cya($db, $dsID);

  #insert basic cube definition
  $query = "INSERT INTO app.cubes (dsID, userID, orgID, name, scriptProducts, scriptGeographies, scriptTimeperiods, scriptMeasures) \
      VALUES ($dsID, $userID, $orgID, 'Retailers Driving Brand Trends', '$prodScript', '$geoScript', '$timeScript', '$measScript')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $cubeID = $db->{q{mysql_insertid}};

  #build the cube
  KAPutil_job_update_status($db, "Creating Retailers Driving Brand Trends report");
  cube_build($db, $dsSchema, $cubeID, $userID);

  #
  #insert the visuals and set their initial selections
  #

  #insert left bar chart (top retailers)
  $dolChangeMeasID = autorpt_find_meas_dollars_cya($db, $dsID);
  $style = ",type:2DBars,showLabels:1,showLegend:1,legendPosition:bottom,width:0.5,height:1.0,xpct:0,ypct:0,";
  $style .= "filterMeas1:$dolChangeMeasID,filterOp1:top,filterNum1:10,allOthers:false,";
  $style .= "caption:\"Top Growth Retailers\",subcaption:\"{PROD}, {TIME}\",captionFontSize:24,";
  $style .= "showValues:1,formatNumberScale:1,decimals:1,placeValuesInside:0,valueFontSize:14,";
  $style .= "labelDisplay:auto,labelStep:1,labelFontSize:14,zindex:1,";
  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 'g')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  autorpt_select_n_items($db, $cubeID, $visID, "p", 1);

  autorpt_select_all_items($db, $cubeID, $visID, "g");

  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);

  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "dollar cya");

  #insert right side pie chart
  $style = ",type:2DBars,showLabels:1,showLegend:1,legendPosition:bottom,width:0.5,height:1.0,xpct:0.5,ypct:0.0,zindex:2,";
  $style .= "caption:\"Retailers in Decline\",subcaption:\"{PROD}, {TIME}\",captionFontSize:24,";
  $style .= "filterMeas1:$dolChangeMeasID,filterOp1:bottom,filterNum1:10,allOthers:false,";
  $style .= "labelDisplay:auto,labelStep:1,labelFontSize:14,showValues:1,formatNumberScale:1,decimals:1,";
  $style .= "placeValuesInside:0,valueFontSize:14,";

  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 'g')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #select the first brand segment
  autorpt_select_n_items($db, $cubeID, $visID, "p", 1);

  #select all geographies (they get filtered to bottom 10)
  autorpt_select_all_items($db, $cubeID, $visID, "g");

  #select 52 week cumulative time period
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);

  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "dollar cya");
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for Products Driving Trends top-line report
#

sub autorpt_prereqs_tlbr_prod_trends
{
  my ($prereqHTML);

  my ($db, $dsID) = @_;

  $prereqHTML = "";
  $prereqHTML = autorpt_prereqs_meas_dollars_cya($db, $dsID, $prereqHTML);

  $prereqHTML = autorpt_uniquify_prereqs($prereqHTML);

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Insert a top-line Products Driving Trends report into the specified DS
#

sub autorpt_insert_tlbr_prod_trends
{
  my ($dsSchema, $query, $status, $cubeID, $visID);
  my ($prodScript, $geoScript, $timeScript, $measScript, $style, $q_style);
  my ($dolChangeMeasID, $brandSegID);

  my ($db, $userID, $orgID, $dsID, $story) = @_;

  DBG("Creating report Top-Line Business Review:Products Driving Trends");

  $dsSchema = "datasource_" . $dsID;

  #determine product selection
  $prodScript = autorpt_find_all($db, $dsID, "p");

  #determine geography selection
  $geoScript = autorpt_find_geo_total_us($db, $dsID);
  $geoScript .= ",";
  $geoScript .= autorpt_find_geo_retailers($db, $dsID);

  #insert time periods (52 most recent 1-weeks, most recent cumulatives)
  $timeScript = autorpt_create_recent_n_cum_weeks($db, $dsID, 52);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 26);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 13);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 4);

  #insert measures
  $measScript = "M:" . autorpt_create_meas_dollars_cya($db, $dsID);
  $brandSegID = autorpt_find_seg_brand($db, $dsID, 1);
  $brandSegID = "PSEG_" . $brandSegID;
  $measScript .= ",M:$brandSegID";

  #insert basic cube definition
  $query = "INSERT INTO app.cubes (dsID, userID, orgID, name, scriptProducts, scriptGeographies, scriptTimeperiods, scriptMeasures, slicers) \
      VALUES ($dsID, $userID, $orgID, 'Products Driving Trends', '$prodScript', '$geoScript', '$timeScript', '$measScript', '$brandSegID:0,')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $cubeID = $db->{q{mysql_insertid}};

  #build the cube
  KAPutil_job_update_status($db, "Creating Products Driving Trends report");
  cube_build($db, $dsSchema, $cubeID, $userID);

  #
  #insert the visuals and set their initial selections
  #

  #insert left bar chart of top products
  $dolChangeMeasID = autorpt_find_meas_dollars_cya($db, $dsID);
  $style = ",type:2DBars,showLabels:1,showLegend:1,legendPosition:bottom,width:0.5,height:1.0,xpct:0,ypct:0,zindex:1,";
  $style .= "filterMeas1:$dolChangeMeasID,filterOp1:top,filterNum1:20,allOthers:false,caption:\"Top Growth Products\",subcaption:\"{GEOG}, {TIME}\",";
  $style .= "captionFontSize:24,legendItemFontSize:12,labelDisplay:auto,labelStep:1,labelFontSize:12,";
  $style .= "showValues:1,formatNumberScale:1,showPercentValues:1,decimals:0,placeValuesInside:0,";
  $style .= "valueFontSize:12,";
  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 'p')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #take all of the products and the first geography
  autorpt_select_all_items($db, $cubeID, $visID, "p");
  autorpt_select_n_items($db, $cubeID, $visID, "g", 1);

  #select 52-week cumulative time period
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);

  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "dollar cya");

  #insert right bar chart of bottom products
  $style = ",type:2DBars,showLabels:1,showLegend:1,legendPosition:bottom,width:0.5,height:1.0,xpct:0.5,ypct:0.0,zindex:2,";
  $style .= "filterMeas1:$dolChangeMeasID,filterOp1:bottom,filterNum1:20,allOthers:false,caption:\"Declining Products\",subcaption:\"{GEOG}, {TIME}\",";
  $style .= "captionFontSize:24,legendItemFontSize:12,labelDisplay:auto,labelStep:1,labelFontSize:12,";
  $style .= "showValues:1,formatNumberScale:1,decimals:0,placeValuesInside:0,valueFontSize:12,showValuesBg:0,";
  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 'p')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #take all of the products and the first geography
  autorpt_select_all_items($db, $cubeID, $visID, "p");
  autorpt_select_n_items($db, $cubeID, $visID, "g", 1);

  #select 52-week cumulative time period
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);

  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "dollar cya");
}







###############################################################################
#
#
#
#
#                   REPORTS: BASELINE DRIVERS
#
#
#
#
###############################################################################





#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for baseline trends decomposition report
#

sub autorpt_prereqs_bldr_base_vs_incr
{
  my ($prereqHTML);

  my ($db, $dsID) = @_;

  $prereqHTML = "";
  $prereqHTML = autorpt_prereqs_meas_pct_dollars_base($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_pct_dollars_incr($db, $dsID, $prereqHTML);

  $prereqHTML = autorpt_uniquify_prereqs($prereqHTML);

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Insert a baseline trend decomp report into the specified data source
#

sub autorpt_insert_bldr_base_vs_incr
{
  my ($dsSchema, $query, $status, $cubeID, $visID);
  my ($prodScript, $geoScript, $timeScript, $measScript, $style, $q_style);
  my ($baseDolSalesMeasID);

  my ($db, $userID, $orgID, $dsID, $story) = @_;

  DBG("Creating report Baseline Drivers:Base vs Incremental Sales");

  $dsSchema = "datasource_" . $dsID;

  #determine product selection
  $prodScript = autorpt_find_prods_nth_largest_segment($db, $dsID, "brand", 1);

  #determine geography selection
  $geoScript = autorpt_find_geo_total_us($db, $dsID);
  $geoScript .= ",";
  $geoScript .= autorpt_find_geo_retailers($db, $dsID);

  #insert time periods (52 most recent 1-weeks, most recent cumulatives)
  $timeScript = autorpt_create_recent_n_cum_weeks($db, $dsID, 52);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 26);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 13);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 4);

  #insert measures
  $measScript ="M:" . autorpt_create_meas_pct_dollars_base($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_pct_dollars_incr($db, $dsID);

  #insert basic cube definition
  $query = "INSERT INTO app.cubes (dsID, userID, orgID, name, scriptProducts, scriptGeographies, scriptTimeperiods, scriptMeasures) \
      VALUES ($dsID, $userID, $orgID, 'Base vs Incremental Sales', '$prodScript', '$geoScript', '$timeScript', '$measScript')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $cubeID = $db->{q{mysql_insertid}};

  #build the cube
  KAPutil_job_update_status($db, "Creating Base vs Incremental Sales report");
  cube_build($db, $dsSchema, $cubeID, $userID);

  #
  #insert the visuals and set their initial selections
  #

  #insert column chart of top products
  $baseDolSalesMeasID = autorpt_find_meas_pct_dollars_base($db, $dsID);
  $style = ",type:Stacked2DColumns,showLabels:1,showLegend:1,legendPosition:bottom,width:1.0,height:1.0,xpct:0.0,ypct:0.0,";
  $style .= "caption:\"Base vs Incremental Sales\",subcaption:\"{GEOG}, {TIME}\",captionFontSize:24,";
  $style .= "legendItemFontSize:12,labelFontSize:12,showValues:1,placeValuesInside:0,valueFontSize:12,";
  $style .= "filterMeas1:$baseDolSalesMeasID,filterOp1:top,filterNum1:25,allOthers:false,";

  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x, graph_y) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 'p', 'm')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #take all of the products and the first geography
  autorpt_select_all_items($db, $cubeID, $visID, "p");
  autorpt_select_n_items($db, $cubeID, $visID, "g", 1);

  #select 52-week cumulative time period
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);

  #select measures
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "pct dollars base");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "pct dollars incr");
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for baseline causes of base change report
#

sub autorpt_prereqs_bldr_cause_base_change
{
  my ($prereqHTML);

  my ($db, $dsID) = @_;

  $prereqHTML = "";
  $prereqHTML = autorpt_prereqs_meas_dollar_change_tdp_change($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_dollar_change_base_velocity_change($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_acv_reach($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_base_unit_price($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_base_unit_price_pctchg_ya($db, $dsID, $prereqHTML);

  $prereqHTML = autorpt_uniquify_prereqs($prereqHTML);

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Insert a baseline cause of decline report into the specified data source
#

sub autorpt_insert_bldr_cause_base_change
{
  my ($dsSchema, $query, $status, $cubeID, $visID);
  my ($prodScript, $geoScript, $timeScript, $measScript, $style, $q_style);

  my ($db, $userID, $orgID, $dsID, $story) = @_;

  DBG("Creating report Baseline Drivers:Causes of Base Change");

  $dsSchema = "datasource_" . $dsID;

  #determine product selection
  $prodScript = autorpt_find_seg_category($db, $dsID);
  $prodScript .= ",";
  $prodScript .= autorpt_create_seghier_manufacturer_brand($db, $dsID);

  #determine geography selection
  $geoScript = autorpt_find_geo_total_us($db, $dsID);
  $geoScript .= ",";
  $geoScript .= autorpt_find_geo_retailers($db, $dsID);

  #insert time periods (52 most recent 1-weeks, most recent cumulatives)
  $timeScript = autorpt_create_recent_n_cum_weeks($db, $dsID, 52);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 26);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 13);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 4);

  #insert measures
  $measScript = "M:" . autorpt_create_meas_dollar_change_tdp_change($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_dollar_change_base_velocity_change($db, $dsID);
  $measScript .= ",M:";
  $measScript .= autorpt_find_meas_acv_reach($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_acv_reach_pctchg_ya($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_base_unit_price($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_base_unit_price_pctchg_ya($db, $dsID);

  #insert basic cube definition
  $query = "INSERT INTO app.cubes (dsID, userID, orgID, name, scriptProducts, scriptGeographies, scriptTimeperiods, scriptMeasures) \
      VALUES ($dsID, $userID, $orgID, 'Causes of Base Change', '$prodScript', '$geoScript', '$timeScript', '$measScript')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $cubeID = $db->{q{mysql_insertid}};

  #build the cube
  KAPutil_job_update_status($db, "Creating Causes of Base Change report");
  cube_build($db, $dsSchema, $cubeID, $userID);

  #
  #insert the visuals and set their initial selections
  #

  #insert table
  $style = "width:1.0,height:1.0,xpct:0,ypct:0,headerFontSize:14,valueFontSize:14,excludeNA:1,";
  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, tableRowDims, tableFilterDims, tableColDims) \
      VALUES ($cubeID, $dsID, 'table', $q_style, 'p', 'g', 't')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  autorpt_select_all_items($db, $cubeID, $visID, "p");
  autorpt_select_n_items($db, $cubeID, $visID, "g", 1);

  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 26);
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 4);

  #select measure
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "dollar change tdp change");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "dollar change base velocity change");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "acv reach");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "acv reach pctchg ya");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "base unit price");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "base unit price pctchg ya");
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for baseline causes of base change report
#

sub autorpt_prereqs_bldr_distribution
{
  my ($prereqHTML);

  my ($db, $dsID) = @_;

  $prereqHTML = "";
  $prereqHTML = autorpt_prereqs_meas_acv_reach($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_acv_reach_cya($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_avg_num_items($db, $dsID, $prereqHTML);

  $prereqHTML = autorpt_uniquify_prereqs($prereqHTML);

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Insert a baseline distribution report into the specified data source
#

sub autorpt_insert_bldr_distribution
{
  my ($dsSchema, $query, $status, $cubeID, $visID);
  my ($prodScript, $geoScript, $timeScript, $measScript, $style, $q_style);
  my ($acvMeasID, $acvCYAMeasID, $avgNumItemsMeasID, $avgNumItemsCYAMeasID);

  my ($db, $userID, $orgID, $dsID, $story) = @_;

  DBG("Creating report Baseline Drivers:Distribution");

  $dsSchema = "datasource_" . $dsID;

  #determine product selection
  $prodScript = autorpt_find_seg_brand($db, $dsID);

  #determine geography selection
  $geoScript = autorpt_find_geo_total_us($db, $dsID);
  $geoScript .= ",";
  $geoScript .= autorpt_find_geo_retailers($db, $dsID);

  #insert time periods (52 most recent 1-weeks, most recent cumulatives)
  $timeScript = autorpt_create_recent_n_cum_weeks($db, $dsID, 52);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 26);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 13);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 4);

  #insert measures
  $measScript = "M:" . autorpt_find_meas_acv_reach($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_acv_reach_cya($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_avg_num_items($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_avg_num_items_cya($db, $dsID);

  #insert basic cube definition
  $query = "INSERT INTO app.cubes (dsID, userID, orgID, name, scriptProducts, scriptGeographies, scriptTimeperiods, scriptMeasures) \
      VALUES ($dsID, $userID, $orgID, 'Distribution', '$prodScript', '$geoScript', '$timeScript', '$measScript')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $cubeID = $db->{q{mysql_insertid}};

  #build the cube
  KAPutil_job_update_status($db, "Creating Distribution report");
  cube_build($db, $dsSchema, $cubeID, $userID);

  #
  #insert the visuals and set their initial selections
  #

  #insert top dual-Y chart for ACV and CYA
  $acvMeasID = autorpt_find_meas_acv_reach($db, $dsID);
  $acvCYAMeasID = autorpt_find_meas_acv_reach_cya($db, $dsID);
  $style = ",type:DualY,showLabels:1,showLegend:1,legendPosition:bottom,";
  $style .= "width:1.00,height:0.50,xpct:0,ypct:0,zindex:1,";
  $style .= "labelDisplay:rotate,labelStep:1,labelFontSize:14,";
  $style .= "showValues:1,placeValuesInside:0,valueFontSize:14,";
  $style .= "caption:\"ACV\",subcaption:\"{GEOG}, {TIME}\",captionFontSize:24,";
  $style .= "comboSecondaryAxis:$acvCYAMeasID-,comboGraphAsLine:$acvCYAMeasID-,comboGraphAsColumn:$acvMeasID-,";
  $style .= "filterMeas1:$acvMeasID,filterOp1:top,filterNum1:20,";
  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x, graph_y) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 'p', 'm')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #take all of the products and the first geography
  autorpt_select_all_items($db, $cubeID, $visID, "p");
  autorpt_select_n_items($db, $cubeID, $visID, "g", 1);

  #select 52-week cumulative time period
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);

  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "acv reach cya");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "acv reach");

  #insert bottom dual-Y chart for weighted avg # items selling
  $avgNumItemsMeasID = autorpt_find_meas_avg_num_items($db, $dsID);
  $avgNumItemsCYAMeasID = autorpt_find_meas_avg_num_items_cya($db, $dsID);
  $style = ",type:DualY,showLabels:1,showLegend:1,legendPosition:bottom,";
  $style .= "width:1.00,height:0.50,xpct:0.0,ypct:0.5,zindex:2,";
  $style .= "labelDisplay:rotate,labelStep:1,labelFontSize:14,";
  $style .= "showValues:1,placeValuesInside:0,valueFontSize:14,";
  $style .= "caption:\"Weighted Avg # of Items Selling\",subcaption:\"{GEOG}, {TIME}\",captionFontSize:24,";
  $style .= "filterMeas1:$avgNumItemsMeasID,filterOp1:top,filterNum1:20,comboSecondaryAxis:$avgNumItemsCYAMeasID-,comboGraphAsLine:$avgNumItemsCYAMeasID-,comboGraphAsColumn:$avgNumItemsMeasID-,";
  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x, graph_y) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 'p', 'm')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #take all of the products and the first geography
  autorpt_select_all_items($db, $cubeID, $visID, "p");
  autorpt_select_n_items($db, $cubeID, $visID, "g", 1);

  #select 52-week cumulative time period
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);

  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "avg num items");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "avg num items cya");
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for baseline everyday price report
#

sub autorpt_prereqs_bldr_everyday_price
{
  my ($prereqHTML);

  my ($db, $dsID) = @_;

  $prereqHTML = "";
  $prereqHTML = autorpt_prereqs_meas_base_unit_price($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_base_unit_price_pctchg_ya($db, $dsID, $prereqHTML);

  $prereqHTML = autorpt_uniquify_prereqs($prereqHTML);

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Insert a baseline everyday price report into the specified data source
#

sub autorpt_insert_bldr_everyday_price
{
  my ($dsSchema, $query, $status, $cubeID, $visID);
  my ($prodScript, $geoScript, $timeScript, $measScript, $style, $q_style);
  my ($basePriceMeasID, $basePricePctChgMeasID);

  my ($db, $userID, $orgID, $dsID, $story) = @_;

  DBG("Creating report Baseline Drivers:Everyday Price");

  $dsSchema = "datasource_" . $dsID;

  #determine product selection
  $prodScript = autorpt_find_prods_nth_largest_segment($db, $dsID, "brand", 1);

  #determine geography selection
  $geoScript = autorpt_find_geo_total_us($db, $dsID);
  $geoScript .= ",";
  $geoScript .= autorpt_find_geo_retailers($db, $dsID);

  #insert time periods (52 most recent 1-weeks, most recent cumulatives)
  $timeScript = autorpt_create_recent_n_cum_weeks($db, $dsID, 52);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 26);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 13);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 4);

  #insert measures
  $measScript = "M:" . autorpt_create_meas_base_unit_price($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_base_unit_price_pctchg_ya($db, $dsID);

  #insert basic cube definition
  $query = "INSERT INTO app.cubes (dsID, userID, orgID, name, scriptProducts, scriptGeographies, scriptTimeperiods, scriptMeasures) \
      VALUES ($dsID, $userID, $orgID, 'Everyday Price', '$prodScript', '$geoScript', '$timeScript', '$measScript')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $cubeID = $db->{q{mysql_insertid}};

  #build the cube
  KAPutil_job_update_status($db, "Creating Everyday Price report");
  cube_build($db, $dsSchema, $cubeID, $userID);

  #
  #insert the visuals and set their initial selections
  #

  #insert dual-Y chart of base unit prices and their %Chg
  $basePriceMeasID = autorpt_find_meas_base_unit_price($db, $dsID);
  $basePricePctChgMeasID = autorpt_find_meas_base_unit_price_pctchg_ya($db, $dsID);
  $style = ",type:DualY,showLabels:1,width:1,height:1,xpct:0,ypct:0,zindex:1,";
  $style .= "caption:\"Everyday Pricing\",subcaption:\"{GEOG}, {TIME}\",";
  $style .= "captionFontSize:24,labelDisplay:auto,labelStep:1,labelFontSize:14,";
  $style .= "showValues:1,formatNumberScale:1,valueFontSize:14,";
  $style .= "filterMeas1:$basePriceMeasID,filterOp1:top,filterNum1:10,allOthers:false,";
  $style .= "comboSecondaryAxis:$basePricePctChgMeasID,comboGraphAsLine:$basePricePctChgMeasID,comboGraphAsColumn:$basePriceMeasID,";
  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x, graph_y) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 'p', 'm')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #take all of the products and the first geography
  autorpt_select_all_items($db, $cubeID, $visID, "p");
  autorpt_select_n_items($db, $cubeID, $visID, "g", 1);

  #select 52-week cumulative time period
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);

  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "base unit price");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "base unit price pctchg ya");
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for baseline everyday price vs comp report
#

sub autorpt_prereqs_bldr_everyday_price_vs_comp
{
  my ($prereqHTML);

  my ($db, $dsID) = @_;

  $prereqHTML = "";
  $prereqHTML = autorpt_prereqs_meas_base_unit_price($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_base_unit_price_pctchg_ya($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_avg_unit_price($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_avg_unit_price_pctchg_ya($db, $dsID, $prereqHTML);

  $prereqHTML = autorpt_uniquify_prereqs($prereqHTML);

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Insert a baseline everyday price vs comp report into the specified data source
#

sub autorpt_insert_bldr_everyday_price_vs_comp
{
  my ($dsSchema, $query, $status, $cubeID, $visID);
  my ($prodScript, $geoScript, $timeScript, $measScript, $style, $q_style);
  my ($avgUnitPriceMeasID);

  my ($db, $userID, $orgID, $dsID, $story) = @_;

  $dsSchema = "datasource_" . $dsID;

  DBG("Creating report Baseline Drivers:Everyday Prices vs Competition");

  #determine product selection - largest brand and some competing products
  $prodScript = autorpt_find_prods_nth_largest_segment($db, $dsID, "brand", 1);
  $prodScript .= ",";
  $prodScript .= autorpt_find_prods_nth_largest_segment($db, $dsID, "brand", 2);

  #determine geography selection
  $geoScript = autorpt_find_geo_total_us($db, $dsID);
  $geoScript .= ",";
  $geoScript .= autorpt_find_geo_retailers($db, $dsID);

  #insert time periods (52 most recent 1-weeks, most recent cumulatives)
  $timeScript = autorpt_create_recent_n_cum_weeks($db, $dsID, 52);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 26);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 13);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 4);

  #insert measures
  $measScript = "M:" . autorpt_create_meas_base_unit_price($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_base_unit_price_pctchg_ya($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_avg_unit_price($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_avg_unit_price_pctchg_ya($db, $dsID);

  #insert basic cube definition
  $query = "INSERT INTO app.cubes (dsID, userID, orgID, name, scriptProducts, scriptGeographies, scriptTimeperiods, scriptMeasures) \
      VALUES ($dsID, $userID, $orgID, 'Everyday Prices vs Competition', '$prodScript', '$geoScript', '$timeScript', '$measScript')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $cubeID = $db->{q{mysql_insertid}};

  #build the cube
  KAPutil_job_update_status($db, "Creating Everyday Prices vs Competition report");
  cube_build($db, $dsSchema, $cubeID, $userID);

  #
  #insert the visuals and set their initial selections
  #

  #insert left column chart of per-product base unit price
  $avgUnitPriceMeasID = autorpt_find_meas_avg_unit_price($db, $dsID);
  $style = ",type:2DColumns,showLabels:1,showLegend:1,legendPosition:bottom,canvasBgColor:#ffffff,width:0.5,height:1.0,xpct:0.0,ypct:0.0,";
  $style .= "filterMeas1:$avgUnitPriceMeasID,filterOp1:top,filterNum1:10,allOthers:false,";
  $style .= "labelFontSize:14,showValues:1,decimals:2,placeValuesInside:0,valueFontSize:14,";
  $style .= "caption:\"Everyday Price Gap vs Comp\",subcaption:\"{GEOG}, {TIME}\",captionFontSize:24,";

  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x, graph_y) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 'p', 'm')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #take all of the products and the first geography
  autorpt_select_all_items($db, $cubeID, $visID, "p");
  autorpt_select_n_items($db, $cubeID, $visID, "g", 1);

  #select 52-week cumulative time period
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);

  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "base unit price");

  #insert right bar chart of per-product base dollars % cya
  $style = ",type:2DBars,legendPosition:bottom,width:0.5,height:1.0,xpct:0.5,ypct:0.0,zindex:2,";
  $style .= "filterMeas1:$avgUnitPriceMeasID,filterOp1:top,filterNum1:10,allOthers:false,";
  $style .= "caption:\"Everyday Price % Chg vs Comp\",subcaption:\"{GEOG}, {TIME}\",captionFontSize:24,";
  $style .= "showValues:1,formatNumberScale:1,placeValuesInside:0,valueFontSize:14,";
  $style .= "labelDisplay:auto,labelStep:1,labelFontSize:14,";

  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x, graph_y) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 'p', 'm')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #take all of the products and the first geography
  autorpt_select_all_items($db, $cubeID, $visID, "p");
  autorpt_select_n_items($db, $cubeID, $visID, "g", 1);

  #select 52-week cumulative time period
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);

  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "base unit price pctchg ya");
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for baseline pricing across retailers report
#

sub autorpt_prereqs_bldr_base_price_retailers
{
  my ($prereqHTML);

  my ($db, $dsID) = @_;

  $prereqHTML = "";
  $prereqHTML = autorpt_prereqs_meas_base_unit_price($db, $dsID, $prereqHTML);

  $prereqHTML = autorpt_uniquify_prereqs($prereqHTML);

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Insert a baseline pricing across retailers report into the specified data source
#

sub autorpt_insert_bldr_base_price_retailers
{
  my ($dsSchema, $query, $status, $cubeID, $visID);
  my ($prodScript, $geoScript, $timeScript, $measScript, $style, $q_style);

  my ($db, $userID, $orgID, $dsID, $story) = @_;

  DBG("Creating report Baseline Drivers:Base Pricing Across Retailers");

  $dsSchema = "datasource_" . $dsID;

  #determine product selection
  $prodScript = autorpt_find_prods_nth_largest_segment($db, $dsID, "brand", 1);

  #determine geography selection
  $geoScript = autorpt_find_geo_total_us($db, $dsID);
  $geoScript .= ",";
  $geoScript .= autorpt_find_geo_retailers($db, $dsID);

  #insert time periods (52 most recent 1-weeks, most recent cumulatives)
  $timeScript = autorpt_create_recent_n_cum_weeks($db, $dsID, 52);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 26);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 13);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 4);

  #insert measures
  $measScript = "M:" . autorpt_create_meas_base_unit_price($db, $dsID);

  #insert basic cube definition
  $query = "INSERT INTO app.cubes (dsID, userID, orgID, name, scriptProducts, scriptGeographies, scriptTimeperiods, scriptMeasures) \
      VALUES ($dsID, $userID, $orgID, 'Base Pricing Across Retailers', '$prodScript', '$geoScript', '$timeScript', '$measScript')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $cubeID = $db->{q{mysql_insertid}};

  #build the cube
  KAPutil_job_update_status($db, "Creating Base Pricing Across Retailers report");
  cube_build($db, $dsSchema, $cubeID, $userID);

  #
  #insert the visuals and set their initial selections
  #

  #insert column chart of top products
  $style = ",type:TreeMap,minColor:fd625e,maxColor:01b8aa,width:1.00,height:1.00,xpct:0,ypct:0,";
  $style .= "showLegend:0,caption:\"Base Pricing Across Retailers\",subcaption:\"{PROD},{TIME}\",captionFontSize:24,";

  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 'g')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #take 5 products and all geographies
  autorpt_select_n_items($db, $cubeID, $visID, "p", 1);
  autorpt_select_all_items($db, $cubeID, $visID, "g");

  #select 52-week cumulative time period
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);

  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "base unit price");
}





###############################################################################
#
#
#
#
#                   REPORTS: INCREMENTAL DRIVERS
#
#
#
#
###############################################################################





#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for incremental trends decomposition report
#

sub autorpt_prereqs_incdr_incr_vs_base
{
  my ($prereqHTML);

  my ($db, $dsID) = @_;

  $prereqHTML = "";
  $prereqHTML = autorpt_prereqs_meas_pct_dollars_base($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_pct_dollars_incr($db, $dsID, $prereqHTML);

  $prereqHTML = autorpt_uniquify_prereqs($prereqHTML);

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Insert an incremental trend decomp report into the specified data source
#

sub autorpt_insert_incdr_incr_vs_base
{
  my ($dsSchema, $query, $status, $cubeID, $visID);
  my ($prodScript, $geoScript, $timeScript, $measScript, $style, $q_style);
  my ($pctDolsIncrMeasID);

  my ($db, $userID, $orgID, $dsID, $story) = @_;

  DBG("Creating report Incremental Drivers:Incremental vs Base Sales");

  $dsSchema = "datasource_" . $dsID;

  #determine product selection
  $prodScript = autorpt_find_prods_nth_largest_segment($db, $dsID, "brand", 1);

  #determine geography selection
  $geoScript = autorpt_find_geo_total_us($db, $dsID);
  $geoScript .= ",";
  $geoScript .= autorpt_find_geo_retailers($db, $dsID);

  #insert time periods (52 most recent 1-weeks, most recent cumulatives)
  $timeScript = autorpt_create_recent_n_cum_weeks($db, $dsID, 52);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 26);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 13);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 4);

  #insert measures
  $measScript = "M:" . autorpt_create_meas_pct_dollars_base($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_pct_dollars_incr($db, $dsID);

  #insert basic cube definition
  $query = "INSERT INTO app.cubes (dsID, userID, orgID, name, scriptProducts, scriptGeographies, scriptTimeperiods, scriptMeasures) \
      VALUES ($dsID, $userID, $orgID, 'Incremental vs Base Sales', '$prodScript', '$geoScript', '$timeScript', '$measScript')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $cubeID = $db->{q{mysql_insertid}};

  #build the cube
  KAPutil_job_update_status($db, "Creating Incremental vs Base Sales report");
  cube_build($db, $dsSchema, $cubeID, $userID);

  #
  #insert the visuals and set their initial selections
  #

  #insert column chart of top products
  $pctDolsIncrMeasID = autorpt_find_meas_pct_dollars_incr($db, $dsID);
  $style = ",type:Stacked2DColumns,width:1.0,height:1.0,xpct:0.0,ypct:0.0,";
  $style .= "caption:\"Incremental vs Base Sales\",subcaption:\"{GEOG}, {TIME}\",captionFontSize:24,";
  $style .= "legendItemFontSize:12,labelFontSize:12,valueFontSize:12,";
  $style .= "filterMeas1:$pctDolsIncrMeasID,filterOp1:top,filterNum1:25,allOthers:false,";

  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x, graph_y) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 'p', 'm')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #take all of the products and the first geography
  autorpt_select_all_items($db, $cubeID, $visID, "p");
  autorpt_select_n_items($db, $cubeID, $visID, "g", 1);

  #select 52-week cumulative time period
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);

  #select measures
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "pct dollars incr");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "pct dollars base");
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for causes of incremental change report
#

sub autorpt_prereqs_incdr_cause_incr_change
{
  my ($prereqHTML, $segID);

  my ($db, $dsID) = @_;

  $prereqHTML = "";
  $prereqHTML = autorpt_prereqs_meas_dollars($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_incr_dollars($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_promo_vehicle_incr_dollars_cya($db, $dsID, "disp wo feat", $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_promo_vehicle_incr_dollars_cya($db, $dsID, "feat and disp", $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_promo_vehicle_incr_dollars_cya($db, $dsID, "feat wo disp", $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_promo_vehicle_incr_dollars_cya($db, $dsID, "price decr", $prereqHTML);

  $segID = autorpt_find_seg_category($db, $dsID, 1);
  if ($segID < 1)
  {
    $prereqHTML .= "<LI>Category segmentation</LI>\n";
  }

  $segID = autorpt_find_seg_manufacturer($db, $dsID, 1);
  if ($segID < 1)
  {
    $prereqHTML .= "<LI>Manufacturer/brand owner segmentation</LI>\n";
  }

  $prereqHTML = autorpt_uniquify_prereqs($prereqHTML);

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Insert an incremental cause of change report into the specified data source
#

sub autorpt_insert_incdr_cause_incr_change
{
  my ($dsSchema, $query, $status, $cubeID, $visID);
  my ($prodScript, $geoScript, $timeScript, $measScript, $style, $q_style);

  my ($db, $userID, $orgID, $dsID, $story) = @_;

  DBG("Creating report Incremental Drivers:Causes of Incremental Change");

  $dsSchema = "datasource_" . $dsID;

  #determine product selection
  $prodScript = autorpt_find_seg_category($db, $dsID);
  $prodScript .= ",";
  $prodScript .= autorpt_create_seghier_manufacturer_brand($db, $dsID);

  #determine geography selection
  $geoScript = autorpt_find_geo_total_us($db, $dsID);
  $geoScript .= ",";
  $geoScript .= autorpt_find_geo_retailers($db, $dsID);

  #insert time periods (52 most recent 1-weeks, most recent cumulatives)
  $timeScript = autorpt_create_recent_n_cum_weeks($db, $dsID, 52);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 26);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 13);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 4);

  #insert measures
  $measScript = "M:" . autorpt_find_meas_dollars($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_dollars_cya($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_incr_dollars($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_incr_dollars_cya($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_promo_vehicle_incr_dollars_cya($db, $dsID, "disp wo feat");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_promo_vehicle_incr_dollars_cya($db, $dsID, "feat and disp");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_promo_vehicle_incr_dollars_cya($db, $dsID, "feat wo disp");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_promo_vehicle_incr_dollars_cya($db, $dsID, "price decr");

  #insert basic cube definition
  $query = "INSERT INTO app.cubes (dsID, userID, orgID, name, scriptProducts, scriptGeographies, scriptTimeperiods, scriptMeasures) \
      VALUES ($dsID, $userID, $orgID, 'Causes of Incremental Change', '$prodScript', '$geoScript', '$timeScript', '$measScript')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $cubeID = $db->{q{mysql_insertid}};

  #build the cube
  KAPutil_job_update_status($db, "Creating Causes of Incremental Change report");
  cube_build($db, $dsSchema, $cubeID, $userID);

  #
  #insert the visuals and set their initial selections
  #

  #insert table
  $style = "width:1.0,height:1.0,xpct:0,ypct:0,headerFontSize:14,valueFontSize:14,excludeNA:1,";
  $style .= "title:\"Causes of Incremental Change\",subcaption:\"{GEOG}, {TIME}\",captionFontSize:24,";
  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, tableRowDims, tableFilterDims) \
      VALUES ($cubeID, $dsID, 'table', $q_style, 'p', 'g,t')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  autorpt_select_all_items($db, $cubeID, $visID, "p");
  autorpt_select_n_items($db, $cubeID, $visID, "g", 1);

  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);

  #select all measures
  autorpt_select_all_items($db, $cubeID, $visID, "m");
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for incremental change details report
#

sub autorpt_prereqs_incdr_incr_change_details
{
  my ($prereqHTML);

  my ($db, $dsID) = @_;

  $prereqHTML = "";
  $prereqHTML = autorpt_prereqs_meas_any_promo_incr_dollars($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_promo_vehicle_incr_dollars_cya($db, $dsID, "disp wo feat", $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_promo_vehicle_incr_dollars_cya($db, $dsID, "feat and disp", $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_promo_vehicle_incr_dollars_cya($db, $dsID, "feat wo disp", $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_promo_vehicle_incr_dollars_cya($db, $dsID, "price decr", $prereqHTML);

  $prereqHTML = autorpt_uniquify_prereqs($prereqHTML);

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Insert an incremental change details report into the specified data source
#

sub autorpt_insert_incdr_incr_change_details
{
  my ($dsSchema, $query, $status, $cubeID, $visID);
  my ($prodScript, $geoScript, $timeScript, $measScript, $style, $q_style);
  my ($incrDolMeasID);

  my ($db, $userID, $orgID, $dsID, $story) = @_;

  DBG("Creating report Incremental Drivers:Incremental Change Details");

  $dsSchema = "datasource_" . $dsID;

  #determine product selection
  $prodScript = autorpt_find_seg_category($db, $dsID);
  $prodScript .= ",";
  $prodScript .= autorpt_find_seg_brand($db, $dsID);

  #determine geography selection
  $geoScript = autorpt_find_geo_total_us($db, $dsID);
  $geoScript .= ",";
  $geoScript .= autorpt_find_geo_retailers($db, $dsID);

  #insert time periods (52 most recent 1-weeks, most recent cumulatives)
  $timeScript = autorpt_create_recent_n_cum_weeks($db, $dsID, 52);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 26);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 13);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 4);

  #insert measures
  $measScript = "M:" . autorpt_create_meas_any_promo_incr_dollars_ya($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_promo_vehicle_incr_dollars_cya($db, $dsID, "disp wo feat");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_promo_vehicle_incr_dollars_cya($db, $dsID, "feat and disp");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_promo_vehicle_incr_dollars_cya($db, $dsID, "feat wo disp");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_promo_vehicle_incr_dollars_cya($db, $dsID, "price decr");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_any_promo_incr_dollars($db, $dsID);

  #insert basic cube definition
  $query = "INSERT INTO app.cubes (dsID, userID, orgID, name, scriptProducts, scriptGeographies, scriptTimeperiods, scriptMeasures) \
      VALUES ($dsID, $userID, $orgID, 'Incremental Change Details', '$prodScript', '$geoScript', '$timeScript', '$measScript')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $cubeID = $db->{q{mysql_insertid}};

  #build the cube
  KAPutil_job_update_status($db, "Creating Incremental Change Details report");
  cube_build($db, $dsSchema, $cubeID, $userID);

  #
  #insert the visuals and set their initial selections
  #

  #insert waterfall chart of incremental measures
  $incrDolMeasID = autorpt_find_meas_any_promo_incr_dollars($db, $dsID);
  $style = ",type:Waterfall,width:1.00,height:1.00,xpct:0,ypct:0,";
  $style .= "caption:\"Incremental Change Details for {PROD}\",subcaption:\"{GEOG}, {TIME}\",captionFontSize:24,";
  $style .= "valueFontSize:12,labelFontSize:14,showSumAtEnd:0,cumeSumValue:$incrDolMeasID,";

  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 'm')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #take the first product & first geography
  autorpt_select_n_items($db, $cubeID, $visID, "p", 1);
  autorpt_select_n_items($db, $cubeID, $visID, "g", 1);

  #select 52-week cumulative time period
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);

  #select all measures
  autorpt_select_all_items($db, $cubeID, $visID, "m");
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for causes of incremental change report
#

sub autorpt_prereqs_incdr_promo_freq
{
  my ($prereqHTML);

  my ($db, $dsID) = @_;

  $prereqHTML = "";
  $prereqHTML = autorpt_prereqs_meas_promo_vehicle_cww($db, $dsID, "disp wo feat", $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_promo_vehicle_cww($db, $dsID, "feat and disp", $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_promo_vehicle_cww($db, $dsID, "feat wo disp", $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_promo_vehicle_cww($db, $dsID, "price decr", $prereqHTML);

  $prereqHTML = autorpt_uniquify_prereqs($prereqHTML);

  #NB: not bothering to run a pre-req check for the CYA version of each above
  #    measure because the pre-req is the measure we're checking for above.

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Insert a causes of incremental change report into the specified data source
#

sub autorpt_insert_incdr_promo_freq
{
  my ($dsSchema, $query, $status, $cubeID, $visID);
  my ($prodScript, $geoScript, $timeScript, $measScript, $style, $q_style);

  my ($db, $userID, $orgID, $dsID, $story) = @_;

  DBG("Creating report Incremental Drivers:Promotion Frequency");

  $dsSchema = "datasource_" . $dsID;

  #determine product selection
  $prodScript = autorpt_find_seg_category($db, $dsID);
  $prodScript .= ",";
  $prodScript .= autorpt_find_prods_nth_largest_segment($db, $dsID, "brand", 1);

  #determine geography selection
  $geoScript = autorpt_find_geo_total_us($db, $dsID);
  $geoScript .= ",";
  $geoScript .= autorpt_find_geo_retailers($db, $dsID);

  #insert time periods (52 most recent 1-weeks, most recent cumulatives)
  $timeScript = autorpt_create_recent_n_cum_weeks($db, $dsID, 52);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 26);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 13);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 4);

  #insert measures
  $measScript = "M:" . autorpt_find_meas_promo_vehicle_cww($db, $dsID, "disp wo feat");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_find_meas_promo_vehicle_cww($db, $dsID, "feat and disp");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_find_meas_promo_vehicle_cww($db, $dsID, "feat wo disp");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_find_meas_promo_vehicle_cww($db, $dsID, "price decr");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_promo_vehicle_cww_cya($db, $dsID, "disp wo feat");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_promo_vehicle_cww_cya($db, $dsID, "feat and disp");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_promo_vehicle_cww_cya($db, $dsID, "feat wo disp");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_promo_vehicle_cww_cya($db, $dsID, "price decr");

  #insert basic cube definition
  $query = "INSERT INTO app.cubes (dsID, userID, orgID, name, scriptProducts, scriptGeographies, scriptTimeperiods, scriptMeasures) \
      VALUES ($dsID, $userID, $orgID, 'Promotion Frequency', '$prodScript', '$geoScript', '$timeScript', '$measScript')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $cubeID = $db->{q{mysql_insertid}};

  #build the cube
  KAPutil_job_update_status($db, "Creating Promotion Frequency report");
  cube_build($db, $dsSchema, $cubeID, $userID);

  #
  #insert the visuals and set their initial selections
  #

  #insert left stacked column of promo frequencies
  $style = ",type:Stacked2DColumns,width:0.5,height:1.00,xpct:0,ypct:0,";
  $style .= "caption:\"Promotion Frequencies\",subcaption:\"{GEOG}, {TIME}\",captionFontSize:24,";
  $style .= "valueFontSize:12,labelFontSize:12,";
  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x, graph_y) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 'p', 'm')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #take all products & first geography
  autorpt_select_all_items($db, $cubeID, $visID, "p");
  autorpt_select_n_items($db, $cubeID, $visID, "g", 1);

  #select 52-week cumulative time period
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);

  #select the four promo CWW measures
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "disp wo feat cww");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "feat and disp cww");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "feat wo disp cww");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "price decr cww");

  #insert right stacked bars of change in promo frequencies
  $style = ",type:Stacked2DBars,width:0.5,height:1.0,xpct:0.5,ypct:0.00,zindex:2,";
  $style .= "caption:\"Change in Promotion Frequency\",subcaption:\"{GEOG}, {TIME}\",captionFontSize:24,";
  $style .= "valueFontSize:12,labelFontSize:12,legendItemFontSize:12,";
  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x, graph_y) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 'p', 'm')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #take all products & first geography
  autorpt_select_all_items($db, $cubeID, $visID, "p");
  autorpt_select_n_items($db, $cubeID, $visID, "g", 1);

  #select 52-week cumulative time period
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);

  #select the four promo CWW measures
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "disp wo feat cww cya");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "feat and disp cww cya");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "feat wo disp cww cya");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "price decr cww cya");
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for incremental change:promo effectiveness
# report
#

sub autorpt_prereqs_incdr_promo_eff
{
  my ($prereqHTML);

  my ($db, $dsID) = @_;

  $prereqHTML = "";
  $prereqHTML = autorpt_prereqs_meas_promo_vehicle_dollar_pct_lift($db, $dsID, "disp wo feat", $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_promo_vehicle_dollar_pct_lift($db, $dsID, "feat and disp", $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_promo_vehicle_dollar_pct_lift($db, $dsID, "feat wo disp", $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_promo_vehicle_dollar_pct_lift($db, $dsID, "price decr", $prereqHTML);

  #NB: not bothering to run a pre-req check for the CYA version of each above
  #    measure because the pre-req is the measure we're checking for above.

  $prereqHTML = autorpt_uniquify_prereqs($prereqHTML);

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Insert anincremental change:promo effectiveness report into the specified
# data source
#

sub autorpt_insert_incdr_promo_eff
{
  my ($dsSchema, $query, $status, $cubeID, $visID);
  my ($prodScript, $geoScript, $timeScript, $measScript, $style, $q_style);

  my ($db, $userID, $orgID, $dsID, $story) = @_;

  DBG("Creating report Incremental Drivers:Promotion Effectiveness");

  $dsSchema = "datasource_" . $dsID;

  #determine product selection
  $prodScript = autorpt_find_seg_category($db, $dsID);
  $prodScript .= ",";
  $prodScript .= autorpt_find_prods_nth_largest_segment($db, $dsID, "brand", 1);

  #determine geography selection
  $geoScript = autorpt_find_geo_total_us($db, $dsID);
  $geoScript .= ",";
  $geoScript .= autorpt_find_geo_retailers($db, $dsID);

  #insert time periods (52 most recent 1-weeks, most recent cumulatives)
  $timeScript = autorpt_create_recent_n_cum_weeks($db, $dsID, 52);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 26);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 13);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 4);

  #insert measures
  $measScript = "M:" . autorpt_find_meas_promo_vehicle_dollar_pct_lift($db, $dsID, "disp wo feat");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_find_meas_promo_vehicle_dollar_pct_lift($db, $dsID, "feat and disp");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_find_meas_promo_vehicle_dollar_pct_lift($db, $dsID, "feat wo disp");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_find_meas_promo_vehicle_dollar_pct_lift($db, $dsID, "price decr");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_promo_vehicle_dollar_pct_lift_cya($db, $dsID, "disp wo feat");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_promo_vehicle_dollar_pct_lift_cya($db, $dsID, "feat and disp");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_promo_vehicle_dollar_pct_lift_cya($db, $dsID, "feat wo disp");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_promo_vehicle_dollar_pct_lift_cya($db, $dsID, "price decr");

  #insert basic cube definition
  $query = "INSERT INTO app.cubes (dsID, userID, orgID, name, scriptProducts, scriptGeographies, scriptTimeperiods, scriptMeasures) \
      VALUES ($dsID, $userID, $orgID, 'Promotion Effectiveness', '$prodScript', '$geoScript', '$timeScript', '$measScript')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $cubeID = $db->{q{mysql_insertid}};

  #build the cube
  KAPutil_job_update_status($db, "Creating Promotion Effectiveness report");
  cube_build($db, $dsSchema, $cubeID, $userID);

  #
  #insert the visuals and set their initial selections
  #

  #insert left stacked column of promo frequencies
  $style = ",type:2DColumns,width:0.5,height:1.00,xpct:0,ypct:0,";
  $style .= "caption:\"Promotion Effectiveness\",subcaption:\"{GEOG}, {TIME}\",captionFontSize:24,";
  $style .= "valueFontSize:12,labelFontSize:12,legendItemFontSize:12,";
  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x, graph_y) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 'p', 'm')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #take all products & first geography
  autorpt_select_all_items($db, $cubeID, $visID, "p");
  autorpt_select_n_items($db, $cubeID, $visID, "g", 1);

  #select 52-week cumulative time period
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);

  #select the four promo CWW measures
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "disp wo feat dollar pct lift");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "feat and disp dollar pct lift");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "feat wo disp dollar pct lift");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "price decr dollar pct lift");

  #insert right stacked bars of change in promo frequencies
  $style = ",type:2DBars,width:0.5,height:1.0,xpct:0.5,ypct:0.00,zindex:2,";
  $style .= "caption:\"Change in Promotion Effectiveness\",subcaption:\"{GEOG}, {TIME}\",captionFontSize:24,";
  $style .= "valueFontSize:12,labelFontSize:12,legendItemFontSize:12,";
  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x, graph_y) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 'p', 'm')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #take all products & first geography
  autorpt_select_all_items($db, $cubeID, $visID, "p");
  autorpt_select_n_items($db, $cubeID, $visID, "g", 1);

  #select 52-week cumulative time period
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);

  #select the four promo CWW measures
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "disp wo feat dollar pct lift cya");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "feat and disp dollar pct lift cya");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "feat wo disp dollar pct lift cya");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "price decr dollar pct lift cya");
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for incremental change:promo discounting
# report
#

sub autorpt_prereqs_incdr_promo_discount
{
  my ($prereqHTML);

  my ($db, $dsID) = @_;

  $prereqHTML = "";
  $prereqHTML = autorpt_prereqs_meas_promo_vehicle_unit_price_pct_disc($db, $dsID, "disp wo feat", $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_promo_vehicle_unit_price_pct_disc($db, $dsID, "feat and disp", $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_promo_vehicle_unit_price_pct_disc($db, $dsID, "feat wo disp", $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_promo_vehicle_unit_price_pct_disc($db, $dsID, "price decr", $prereqHTML);

  #NB: not bothering to run a pre-req check for the CYA version of each above
  #    measure because the pre-req is the measure we're checking for above.

  $prereqHTML = autorpt_uniquify_prereqs($prereqHTML);

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Insert anincremental change:promo discounting report into the specified
# data source
#

sub autorpt_insert_incdr_promo_discount
{
  my ($dsSchema, $query, $status, $cubeID, $visID);
  my ($prodScript, $geoScript, $timeScript, $measScript, $style, $q_style);

  my ($db, $userID, $orgID, $dsID, $story) = @_;

  DBG("Creating report Incremental Drivers:Promotion Discounts");

  $dsSchema = "datasource_" . $dsID;

  #determine product selection
  $prodScript = autorpt_find_seg_category($db, $dsID);
  $prodScript .= ",";
  $prodScript .= autorpt_find_prods_nth_largest_segment($db, $dsID, "brand", 1);

  #determine geography selection
  $geoScript = autorpt_find_geo_total_us($db, $dsID);
  $geoScript .= ",";
  $geoScript .= autorpt_find_geo_retailers($db, $dsID);

  #insert time periods (52 most recent 1-weeks, most recent cumulatives)
  $timeScript = autorpt_create_recent_n_cum_weeks($db, $dsID, 52);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 26);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 13);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 4);

  #insert measures
  $measScript = "M:" . autorpt_find_meas_promo_vehicle_unit_price_pct_disc($db, $dsID, "disp wo feat");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_find_meas_promo_vehicle_unit_price_pct_disc($db, $dsID, "feat and disp");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_find_meas_promo_vehicle_unit_price_pct_disc($db, $dsID, "feat wo disp");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_find_meas_promo_vehicle_unit_price_pct_disc($db, $dsID, "price decr");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_promo_vehicle_unit_price_pct_disc_cya($db, $dsID, "disp wo feat");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_promo_vehicle_unit_price_pct_disc_cya($db, $dsID, "feat and disp");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_promo_vehicle_unit_price_pct_disc_cya($db, $dsID, "feat wo disp");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_promo_vehicle_unit_price_pct_disc_cya($db, $dsID, "price decr");

  #insert basic cube definition
  $query = "INSERT INTO app.cubes (dsID, userID, orgID, name, scriptProducts, scriptGeographies, scriptTimeperiods, scriptMeasures) \
      VALUES ($dsID, $userID, $orgID, 'Promotion Discounts', '$prodScript', '$geoScript', '$timeScript', '$measScript')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $cubeID = $db->{q{mysql_insertid}};

  #build the cube
  KAPutil_job_update_status($db, "Creating Promotion Discounts report");
  cube_build($db, $dsSchema, $cubeID, $userID);

  #
  #insert the visuals and set their initial selections
  #

  #insert left stacked column of promo frequencies
  $style = ",type:2DColumns,width:0.5,height:1.00,xpct:0,ypct:0,";
  $style .= "caption:\"Promotion Discounts\",subcaption:\"{GEOG}, {TIME}\",captionFontSize:24,";
  $style .= "valueFontSize:12,labelFontSize:12,legendItemFontSize:12,";
  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x, graph_y) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 'p', 'm')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #take all products & first geography
  autorpt_select_all_items($db, $cubeID, $visID, "p");
  autorpt_select_n_items($db, $cubeID, $visID, "g", 1);

  #select 52-week cumulative time period
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);

  #select the four promo CWW measures
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "disp wo feat unit price pct disc");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "feat and disp unit price pct disc");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "feat wo disp unit price pct disc");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "price decr unit price pct disc");

  #insert right stacked bars of change in promo frequencies
  $style = ",type:2DBars,width:0.5,height:1.0,xpct:0.5,ypct:0.00,zindex:2,";
  $style .= "caption:\"Change in Promotion Discounts\",subcaption:\"{GEOG}, {TIME}\",captionFontSize:24,";
  $style .= "valueFontSize:12,labelFontSize:12,legendItemFontSize:12,";
  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x, graph_y) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 'p', 'm')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #take all products & first geography
  autorpt_select_all_items($db, $cubeID, $visID, "p");
  autorpt_select_n_items($db, $cubeID, $visID, "g", 1);

  #select 52-week cumulative time period
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);

  #select the four promo CWW measures
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "disp wo feat unit price pct disc cya");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "feat and disp unit price pct disc cya");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "feat wo disp unit price pct disc cya");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "price decr unit price pct disc cya");
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for incremental change:share of promo
# incrementals report
#

sub autorpt_prereqs_incdr_share_promos
{
  my ($prereqHTML);

  my ($db, $dsID) = @_;

  $prereqHTML = "";
  $prereqHTML = autorpt_prereqs_meas_dollar_shr_category($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_incr_dollars_shr_category($db, $dsID, $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_promo_vehicle_incr_dollars_shr_cat($db, $dsID, "disp wo feat", $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_promo_vehicle_incr_dollars_shr_cat($db, $dsID, "feat and disp", $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_promo_vehicle_incr_dollars_shr_cat($db, $dsID, "feat wo disp", $prereqHTML);
  $prereqHTML = autorpt_prereqs_meas_promo_vehicle_incr_dollars_shr_cat($db, $dsID, "price decr", $prereqHTML);

  #NB: not bothering to run a pre-req check for the CYA version of each above
  #    measure because the pre-req is the measure we're checking for above.

  $prereqHTML = autorpt_uniquify_prereqs($prereqHTML);

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Insert anincremental change:share of promo incrementals report into the
# specified data source
#

sub autorpt_insert_incdr_share_promos
{
  my ($dsSchema, $query, $status, $cubeID, $visID);
  my ($prodScript, $geoScript, $timeScript, $measScript, $style, $q_style);
  my ($incrShrMeasID);

  my ($db, $userID, $orgID, $dsID, $story) = @_;

  DBG("Creating report Incremental Drivers:Share of Category Incrementals");

  $dsSchema = "datasource_" . $dsID;

  #determine product selection
  $prodScript = autorpt_find_seg_brand($db, $dsID);

  #determine geography selection
  $geoScript = autorpt_find_geo_total_us($db, $dsID);
  $geoScript .= ",";
  $geoScript .= autorpt_find_geo_retailers($db, $dsID);

  #insert time periods (52 most recent 1-weeks, most recent cumulatives)
  $timeScript = autorpt_create_recent_n_cum_weeks($db, $dsID, 52);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 26);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 13);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 4);

  #insert measures
  $measScript = "M:" . autorpt_create_meas_dollar_shr_cat($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_incr_dollars_shr_cat($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_promo_vehicle_incr_dollars_shr_cat($db, $dsID, "disp wo feat");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_promo_vehicle_incr_dollars_shr_cat($db, $dsID, "feat and disp");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_promo_vehicle_incr_dollars_shr_cat($db, $dsID, "feat wo disp");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_promo_vehicle_incr_dollars_shr_cat($db, $dsID, "price decr");
  $measScript .= ",";

  $measScript .= "M:" . autorpt_create_meas_dollar_shr_cat_cya($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_incr_dollars_shr_cat_cya($db, $dsID);
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_promo_vehicle_incr_dollars_shr_cat_cya($db, $dsID, "disp wo feat");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_promo_vehicle_incr_dollars_shr_cat_cya($db, $dsID, "feat and disp");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_promo_vehicle_incr_dollars_shr_cat_cya($db, $dsID, "feat wo disp");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_promo_vehicle_incr_dollars_shr_cat_cya($db, $dsID, "price decr");

  #insert basic cube definition
  $query = "INSERT INTO app.cubes (dsID, userID, orgID, name, scriptProducts, scriptGeographies, scriptTimeperiods, scriptMeasures) \
      VALUES ($dsID, $userID, $orgID, 'Share of Category Incrementals', '$prodScript', '$geoScript', '$timeScript', '$measScript')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $cubeID = $db->{q{mysql_insertid}};

  #build the cube
  KAPutil_job_update_status($db, "Creating Share of Category Incrementals report");
  cube_build($db, $dsSchema, $cubeID, $userID);

  #
  #insert the visuals and set their initial selections
  #

  #insert left stacked column of promo frequencies
  $incrShrMeasID = autorpt_find_meas_incr_dollars_shr_cat($db, $dsID);
  $style = ",type:2DColumns,width:0.5,height:1.00,xpct:0,ypct:0,";
  $style .= "caption:\"Category Incremental Share\",subcaption:\"{GEOG}, {TIME}\",captionFontSize:24,";
  $style .= "valueFontSize:12,labelFontSize:12,filterMeas1:$incrShrMeasID,filterOp1:top,filterNum1:5,";
  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x, graph_y) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 'p', 'm')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #take all products & first geography
  autorpt_select_all_items($db, $cubeID, $visID, "p");
  autorpt_select_n_items($db, $cubeID, $visID, "g", 1);

  #select 52-week cumulative time period
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);

  #select the four promo CWW measures
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "dollar shr category");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "incr dollars shr cat");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "disp wo feat incr dollars share cat");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "feat and disp incr dollars share cat");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "feat wo disp incr dollars share cat");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "price decr incr dollars share cat");

  #insert right stacked bars of change in promo frequencies
  $style = ",type:2DBars,width:0.5,height:1.0,xpct:0.5,ypct:0.00,zindex:2,";
  $style .= "caption:\"Change in Category Incremental Share\",subcaption:\"{GEOG}, {TIME}\",captionFontSize:24,";
  $style .= "valueFontSize:12,labelFontSize:12,filterMeas1:$incrShrMeasID,filterOp1:top,filterNum1:5,";
  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x, graph_y) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 'p', 'm')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #take all products & first geography
  autorpt_select_all_items($db, $cubeID, $visID, "p");
  autorpt_select_n_items($db, $cubeID, $visID, "g", 1);

  #select 52-week cumulative time period
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);

  #select the four promo CWW measures
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "dollar shr category cya");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "incr dollars shr cat cya");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "disp wo feat incr dollars share cat cya");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "feat and disp incr dollars share cat cya");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "feat wo disp incr dollars share cat cya");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "price decr incr dollars share cat cya");
}



#-------------------------------------------------------------------------
#
# Lightweight pre-requisite check for incremental change:promotional pricing
# report
#

sub autorpt_prereqs_incdr_promo_pricing
{
  my ($prereqHTML);

  my ($db, $dsID) = @_;

  $prereqHTML = "";
  $prereqHTML = autorpt_prereqs_promo_vehicle_unit_price($db, $dsID, "disp wo feat", $prereqHTML);
  $prereqHTML = autorpt_prereqs_promo_vehicle_unit_price($db, $dsID, "feat and disp", $prereqHTML);
  $prereqHTML = autorpt_prereqs_promo_vehicle_unit_price($db, $dsID, "feat wo disp", $prereqHTML);
  $prereqHTML = autorpt_prereqs_promo_vehicle_unit_price($db, $dsID, "price decr", $prereqHTML);

  #NB: not bothering to run a pre-req check for the CYA version of each above
  #    measure because the pre-req is the measure we're checking for above.

  $prereqHTML = autorpt_uniquify_prereqs($prereqHTML);

  return($prereqHTML);
}



#-------------------------------------------------------------------------
#
# Insert anincremental change:promotional pricing report into the
# specified data source
#

sub autorpt_insert_incdr_promo_pricing
{
  my ($dsSchema, $query, $status, $cubeID, $visID);
  my ($prodScript, $geoScript, $timeScript, $measScript, $style, $q_style);

  my ($db, $userID, $orgID, $dsID, $story) = @_;

  DBG("Creating report Incremental Drivers:Promotional Pricing");

  $dsSchema = "datasource_" . $dsID;

  #determine product selection
  $prodScript = autorpt_find_seg_category($db, $dsID);
  $prodScript .= ",";
  $prodScript .= autorpt_find_prods_nth_largest_segment($db, $dsID, "brand", 1);

  #determine geography selection
  $geoScript = autorpt_find_geo_total_us($db, $dsID);
  $geoScript .= ",";
  $geoScript .= autorpt_find_geo_retailers($db, $dsID);

  #insert time periods (52 most recent 1-weeks, most recent cumulatives)
  $timeScript = autorpt_create_recent_n_cum_weeks($db, $dsID, 52);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 26);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 13);
  $timeScript .= ",";
  $timeScript .= autorpt_create_recent_n_cum_weeks($db, $dsID, 4);

  #insert measures
  $measScript = "M:" . autorpt_find_meas_promo_vehicle_unit_price($db, $dsID, "disp wo feat");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_find_meas_promo_vehicle_unit_price($db, $dsID, "feat and disp");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_find_meas_promo_vehicle_unit_price($db, $dsID, "feat wo disp");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_find_meas_promo_vehicle_unit_price($db, $dsID, "price decr");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_promo_vehicle_unit_price_cya($db, $dsID, "disp wo feat");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_promo_vehicle_unit_price_cya($db, $dsID, "feat and disp");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_promo_vehicle_unit_price_cya($db, $dsID, "feat wo disp");
  $measScript .= ",";
  $measScript .= "M:" . autorpt_create_meas_promo_vehicle_unit_price_cya($db, $dsID, "price decr");

  #insert basic cube definition
  $query = "INSERT INTO app.cubes (dsID, userID, orgID, name, scriptProducts, scriptGeographies, scriptTimeperiods, scriptMeasures) \
      VALUES ($dsID, $userID, $orgID, 'Promotional Pricing', '$prodScript', '$geoScript', '$timeScript', '$measScript')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $cubeID = $db->{q{mysql_insertid}};

  #build the cube
  KAPutil_job_update_status($db, "Creating Promotional Pricing report");
  cube_build($db, $dsSchema, $cubeID, $userID);

  #
  #insert the visuals and set their initial selections
  #

  #insert left stacked column of promo frequencies
  $style = ",type:2DColumns,width:0.5,height:1.00,xpct:0,ypct:0,";
  $style .= "caption:\"Promotional Unit Price\",subcaption:\"{GEOG}, {TIME}\",captionFontSize:24,";
  $style .= "valueFontSize:12,labelFontSize:12,legendItemFontSize:12,";
  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x, graph_y) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 'p', 'm')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #take all products & first geography
  autorpt_select_all_items($db, $cubeID, $visID, "p");
  autorpt_select_n_items($db, $cubeID, $visID, "g", 1);

  #select 52-week cumulative time period
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);

  #select the four promo CWW measures
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "disp wo feat unit price");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "feat and disp unit price");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "feat wo disp unit price");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "price decr unit price");

  #insert right stacked bars of change in promo frequencies
  $style = ",type:2DBars,width:0.5,height:1.0,xpct:0.5,ypct:0.00,zindex:2,";
  $style .= "caption:\"Change in Promotion Pricing\",subcaption:\"{GEOG}, {TIME}\",captionFontSize:24,";
  $style .= "valueFontSize:12,labelFontSize:12,legendItemFontSize:12,";
  $q_style = $db->quote($style);
  $query = "INSERT INTO app.visuals (cubeID, dsID, type, design, graph_x, graph_y) \
      VALUES ($cubeID, $dsID, 'chart', $q_style, 'p', 'm')";
  $status = $db->do($query);
  autorpt_db_err($db, $status, $query);
  $visID = $db->{q{mysql_insertid}};

  #take all products & first geography
  autorpt_select_all_items($db, $cubeID, $visID, "p");
  autorpt_select_n_items($db, $cubeID, $visID, "g", 1);

  #select 52-week cumulative time period
  autorpt_select_time_n_cum_weeks($db, $dsID, $cubeID, $visID, 52);

  #select the four promo CWW measures
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "disp wo feat unit price cya");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "feat and disp unit price cya");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "feat wo disp unit price cya");
  autorpt_select_meas_by_name($db, $dsID, $cubeID, $visID, "price decr unit price cya");
}



#-------------------------------------------------------------------------
#
# Create all specified reports (if possible) in the specified data source
#

sub autorpt_create_story
{
  my ($ok, $report);
  my (@reports);

  my ($db, $userID, $orgID, $dsID, $story, $rptTags) = @_;

  #if our DS is being used for a blocking operation, set the cube build to wait
  $ok = DSRutil_operation_ok($db, $dsID, 0, "AUTO-RPTS");
  while ($ok != 1)
  {
    KAPutil_job_update_state($db, "WAIT");
    KAPutil_job_update_status($db, "Waiting for another job in this data source to finish");

    sleep(60);

    $ok = DSRutil_operation_ok($db, $dsID, 0, "AUTO-RPTS");
  }

  KAPutil_job_update_state($db, "CREATE");
  KAPutil_job_update_status($db, "Creating automatic reporting");

  #create each requested report, if possible
  @reports = split(',', $rptTags);

  foreach $report (@reports)
  {

    #category overview reports
    if ($report eq "catov_topline_brands")
    {
      autorpt_insert_catov_topline_brands($db, $userID, $orgID, $dsID, $story);
    }
    elsif ($report eq "catov_topline_retailers")
    {
      autorpt_insert_catov_topline_retailers($db, $userID, $orgID, $dsID, $story);
    }
    elsif ($report eq "catov_brand_rank")
    {
      autorpt_insert_catov_brand_rank($db, $userID, $orgID, $dsID, $story);
    }
    elsif ($report eq "catov_item_rank")
    {
      autorpt_insert_catov_item_rank($db, $userID, $orgID, $dsID, $story);
    }
    elsif ($report eq "catov_rtlr_distro_trends")
    {
      autorpt_insert_catov_rtlr_distro_trends($db, $userID, $orgID, $dsID, $story);
    }
    elsif ($report eq "catov_item_distro_trends")
    {
      autorpt_insert_catov_item_distro_trends($db, $userID, $orgID, $dsID, $story);
    }
    elsif ($report eq "catov_retailer_acv_comp")
    {
      autorpt_insert_catov_retailer_acv_comp($db, $userID, $orgID, $dsID, $story);
    }
    elsif ($report eq "catov_change_distro")
    {
      autorpt_insert_catov_change_distro($db, $userID, $orgID, $dsID, $story);
    }
    elsif ($report eq "catov_price_tracker")
    {
      autorpt_insert_catov_price_tracker($db, $userID, $orgID, $dsID, $story);
    }
    elsif ($report eq "catov_units_sold_price")
    {
      autorpt_insert_catov_units_sold_price($db, $userID, $orgID, $dsID, $story);
    }
    elsif ($report eq "catov_promo_tracker_dollars")
    {
      autorpt_insert_catov_promo_tracker_dollars($db, $userID, $orgID, $dsID, $story);
    }
    elsif ($report eq "catov_promo_tracker_units")
    {
      autorpt_insert_catov_promo_tracker_units($db, $userID, $orgID, $dsID, $story);
    }
    elsif ($report eq "catov_lift_vs_discount")
    {
      autorpt_insert_catov_lift_vs_discount($db, $userID, $orgID, $dsID, $story);
    }
    elsif ($report eq "catov_organic_sales_trends")
    {
      autorpt_insert_catov_organic_sales_trends($db, $userID, $orgID, $dsID, $story);
    }


    #top-line business review reports
    elsif ($report eq "tlbr_ov")
    {
      autorpt_insert_tlbr_overview($db, $userID, $orgID, $dsID, $story);
    }
    elsif ($report eq "tlbr_totbustopline")
    {
      autorpt_insert_tlbr_total_bus_crosscat($db, $userID, $orgID, $dsID, $story);
    }
    elsif ($report eq "tlbr_cat_snap")
    {
      autorpt_insert_tlbr_category_snapshot($db, $userID, $orgID, $dsID, $story);
    }
    elsif ($report eq "tlbr_rtl_item_trends")
    {
      autorpt_insert_tlbr_retailer_item_trends($db, $userID, $orgID, $dsID, $story);
    }
    elsif ($report eq "tlbr_rtl_brand_trends")
    {
      autorpt_insert_tlbr_retailer_brand_trends($db, $userID, $orgID, $dsID, $story);
    }
    elsif ($report eq "tlbr_prod_trends")
    {
      autorpt_insert_tlbr_prod_trends($db, $userID, $orgID, $dsID, $story);
    }

    #baseline drivers reports
    elsif ($report eq "bldr_base_vs_incr")
    {
      autorpt_insert_bldr_base_vs_incr($db, $userID, $orgID, $dsID, $story);
    }
    elsif ($report eq "bldr_cause_base_change")
    {
      autorpt_insert_bldr_cause_base_change($db, $userID, $orgID, $dsID, $story);
    }
    elsif ($report eq "bldr_distribution")
    {
      autorpt_insert_bldr_distribution($db, $userID, $orgID, $dsID, $story);
    }
    elsif ($report eq "bldr_everyday_price")
    {
      autorpt_insert_bldr_everyday_price($db, $userID, $orgID, $dsID, $story);
    }
    elsif ($report eq "bldr_everyday_price_vs_comp")
    {
      autorpt_insert_bldr_everyday_price_vs_comp($db, $userID, $orgID, $dsID, $story);
    }
    elsif ($report eq "bldr_base_price_retailers")
    {
      autorpt_insert_bldr_base_price_retailers($db, $userID, $orgID, $dsID, $story);
    }

    #incremental drivers reports
    elsif ($report eq "incdr_incr_vs_base")
    {
      autorpt_insert_incdr_incr_vs_base($db, $userID, $orgID, $dsID, $story);
    }
    elsif ($report eq "incdr_cause_incr_change")
    {
      autorpt_insert_incdr_cause_incr_change($db, $userID, $orgID, $dsID, $story);
    }
    elsif ($report eq "incdr_incr_change_details")
    {
      autorpt_insert_incdr_incr_change_details($db, $userID, $orgID, $dsID, $story);
    }
    elsif ($report eq "incdr_promo_freq")
    {
      autorpt_insert_incdr_promo_freq($db, $userID, $orgID, $dsID, $story);
    }
    elsif ($report eq "incdr_promo_eff")
    {
      autorpt_insert_incdr_promo_eff($db, $userID, $orgID, $dsID, $story);
    }
    elsif ($report eq "incdr_promo_discount")
    {
      autorpt_insert_incdr_promo_discount($db, $userID, $orgID, $dsID, $story);
    }
    elsif ($report eq "incdr_share_promos")
    {
      autorpt_insert_incdr_share_promos($db, $userID, $orgID, $dsID, $story);
    }
    elsif ($report eq "incdr_promo_pricing")
    {
      autorpt_insert_incdr_promo_pricing($db, $userID, $orgID, $dsID, $story);
    }
  }

  DSRutil_clear_status($db, "AUTO-RPTS");
}


1;
