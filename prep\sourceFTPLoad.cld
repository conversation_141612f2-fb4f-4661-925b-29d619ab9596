#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;
use Net::FTP;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepSources;
use Lib::PrepUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($kapDB);

  print <<END_HTML;
<DOCCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Download FTP Data</TITLE>
<LINK REL="SHORTCUT ICON" HREF="$Lib::KoalaConfig::kapHostURL/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
let statusTimer = setInterval(function(){displayStatus()}, 1000);
let statCount = 0;

function displayStatus()
{
  const url = 'ajaxAPI.cld?svc=job_op_status&j=$jobID';

  \$.get(url, function(data, status)
  {
    let statusText = data;
    let statElements = statusText.split('|');

    if (statusText.search('ERR') == 0)
    {
      clearInterval(statusTimer);
      document.getElementById('div-op-title').innerHTML = "Error transferring data";
      \$('#progress-bar').css('width', '100%');
      \$('#progress-bar').addClass('bg-danger');
      document.getElementById('progress-bar').innerHTML = 'ERROR';

      document.getElementById('div-op-details').innerHTML = statElements[1];
      return;
    }

    else if (statusText.search('DONE') == 0)
    {
      \$('#progress-bar').css('width', '100%');
      clearInterval(statusTimer);
      location.href='flowExtractData.cld?f=$flowID&key=$jobID&a=$action';
      return;
    }

    let opTitle = statElements[0];
    let opPct = statElements[1];
    let opDetails = statElements[2];
    let opTimeEstimate = statElements[3];
    let opExtra = statElements[4];
    document.getElementById('div-op-title').innerHTML = opTitle;
    if (opPct.length > 0)
    {
      document.getElementById('progress-bar').innerHTML = opPct + '%';
      \$('#progress-bar').css('width', opPct+'%');
    }
    document.getElementById('div-op-details').innerHTML = opDetails;
    document.getElementById('div-op-extra').innerHTML = opExtra;
  });

  if (statCount > 5)
  {
    clearInterval(statusTimer);
    statusTimer = setInterval(function(){displayStatus()}, 5000);
  }
  statCount++;
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($kapDB, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="$Lib::KoalaConfig::kapHostURL/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">Data Prep</A></LI>
    <LI CLASS="breadcrumb-item">$flowName</LI>
    <LI CLASS="breadcrumb-item active">Import FTP Data</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------
#
# Output the HTML for the web page that displays the status dialog
#

sub print_status_html
{
  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-11 col-md-8 col-lg-6 col-xl-5"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Downloading Data via FTP</DIV>
        <DIV CLASS="card-body">

        <H5 ID="div-op-title"></H5>
        <DIV CLASS="progress" style="height:25px;">
          <DIV ID="progress-bar" CLASS="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" STYLE="width:0%;">
          </DIV>
        </DIV>

        <P>
        <DIV ID="div-op-details"></DIV>
        <DIV ID="div-op-extra"></DIV>

        <P>&nbsp;</P>
        <DIV CLASS="text-center">
          <BUTTON CLASS="btn btn-primary" ID="btnAsync" onClick="location.href='main.cld'"><I CLASS="bi bi-speedometer"></I> Be More Productive</SPAN></BUTTON>
        </DIV>

      </DIV>
    </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get the CGI input variables
  $flowID = $q->param('f');
  $key = $q->param('key');
  $jobID = $q->param('j');
  $ftpserver = $q->param('ftpserver');
  $ftpuser = $q->param('ftpuser');
  $ftppass = $q->param('ftppass');
  $ftppath = $q->param('ftppath');
  $action = $q->param('a');

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  $flowName = prep_flow_id_to_name($prepDB, $flowID);

  #see if we're rejoining an existing job
  if ($jobID > 0)
  {
    $query = "SELECT state FROM prep.jobs WHERE ID=$jobID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($state) = $dbOutput->fetchrow_array;

    #if the job is actually pulling data from an FTP server
    if ($state eq "LOAD-FTP")
    {
      print_html_header();
      print_status_html();
      exit;
    }

    #else we've already moved on, and need to have the user try again
    elsif (length($state) > 0)
    {
      print("Status: 302 Moved temporarily\n");
      print("Location: flowOpen.cld?f=$flowID\n\n");
      exit;
    }
  }

  #make sure we have write privs for this data flow
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs ne "W")
  {
    print_html_header();
    exit_error("You don't have privileges to work with jobs in this data flow.");
  }

  #if the user didn't enter an FTP user name, assume it's "anonymous"
  if (length($ftpuser) < 1)
  {
    $ftpuser = "anonymous";

    if (length($ftppass) < 1)
    {
      $ftppass = $email;
    }
  }

  #store the FTP access info
  $sourceInfo = "FTP=$ftpserver|USER=$ftpuser|PASS=$ftppass|PATH=$ftppath";
  $q_sourceInfo = $prepDB->quote($sourceInfo);
  $query = "UPDATE prep.flows SET sourceInfo=$q_sourceInfo, lastRun=NOW() \
      WHERE ID=$flowID";
  $prepDB->do($query);

  #insert our job entry into the database and get back our job ID
  $jobID = prep_flow_create_interactive_job($prepDB, $flowID, $userID, "LOAD-FTP");

  if (lc($ftpserver) eq "nielsen")
  {
    $category = $ftppath;
    if ($ftppath =~ m/(.*)\.zip/)
    {
      $category = $1;
    }
    prep_audit($prepDB, $userID, "Started a new interactive job to load $category from the Nielsen IDW|$jobID", $flowID);
  }
  else
  {
    prep_audit($prepDB, $userID, "Started a new interactive job|$jobID", $flowID);
  }
  utils_slack("PREP: $first $last started a new interactive FTP load in $flowName");

  print_html_header();

  #fork a new process to do the actual data extraction in the background
  $SIG{CHLD} = "IGNORE";
  if ($pid = fork())
  {
    #parent process

    print_status_html();
  }
  else
  {
    #child process

    #let Apache know not to wait on the child process
    close(STDIN);
    close(STDOUT);

    #redirect STDERR to the Koala error log
    close(STDERR);
    open(STDERR, ">>/opt/apache/htdocs/tmp/koala_error.log");
    select(STDERR);
    $| = 1;

    #reconnect to the database
    $prepDB = PrepUtils_connect_to_database();

    $status = prep_source_ftp($prepDB, $jobID, $key);
  }

#EOF
