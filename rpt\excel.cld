#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSel;
use Lib::DSRUtils;
use Lib::Reports;
use Lib::WebUtils;

#
# NB: Throughout this source file, the comments and variable names refer to
#     "categories", which are the items plotted on the X axis, and "values",
#     which are the corresponding set of values plotted on the Y axis.
#



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  print("Content-type: text/plain\n\n");

  #get the CGI input variables
  $connectionID = $q->param('id');
  $dimItems = $q->param('items');
  $prodFilter = $q->param('p');
  $geoFilter = $q->param('g');
  $timeFilter = $q->param('t');
  $measureFilter = $q->param('m');

  $dimItems = utils_sanitize_dim($dimItems);

  #connect to the database
  $db = KAPutil_connect_to_database();

  #for now, the connection ID is just the report ID - needs to be a little
  #more obfuscated and contain auth info
  $connectionID =~ m/^(\d+).*/;
  $rptID = $1;

  #get data source ID and assemble report cube name
  $dsID = cube_get_ds_id($db, $rptID);

  if ($dsID < 1)
  {
    print("ERROR: Koala report does not exist\n");
    exit;
  }

  $dsSchema = "datasource_" . $dsID;
  $rptCube = "_rptcube_" . $rptID;

  #if we're being called to return a list of dimension items for the cube
  if (length($dimItems) > 0)
  {
    if ($dimItems eq "p")
    {
      $colName = "products";
    }
    elsif ($dimItems eq "g")
    {
      $colName = "geographies";
    }
    elsif ($dimItems eq "t")
    {
      $colName = "timeperiods";
    }
    elsif ($dimItems eq "m")
    {
      $colName = "measures";
    }
    else
    {
      print("ERROR: Invalid Koala dimension specified\n");
      exit;
    }

    #get a hash of the item names for the specified dimension
    %itemNames = dsr_get_item_name_hash($db, $dsSchema, $dimItems);

    #pull the in-order list of dimension items from the cube definition
    $query = "SELECT $colName FROM app.cubes WHERE ID=$rptID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($itemsStr) = $dbOutput->fetchrow_array;

    #get the name of each item ID, and output to Excel
    @itemIDs = split(',', $itemsStr);
    foreach $itemID (@itemIDs)
    {

      #only output the item if we have a human-readable name for it (probably
      #deleted and awaiting removal otherwise)
      if (length($itemNames{$itemID}) > 0)
      {
        print("$itemNames{$itemID}\t$itemID\n");
      }
    }

    #all done with this request
    exit;
  }

  #update view stats tables
  $query = "UPDATE cubes SET lastCloudBacked=NOW() WHERE ID=$rptID";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);

  $query = "INSERT INTO audit.stats_cubes (cubeID, year, month, viewsCloudBacked) \
      VALUES ($rptID, YEAR(NOW()), MONTH(NOW()), 1) \
      ON DUPLICATE KEY UPDATE viewsCloudBacked=viewsCloudBacked+1";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);

  #get the measure IDs from the cubes table
  $query = "SELECT measures FROM cubes WHERE ID=$rptID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($measureIDString) = $dbOutput->fetchrow_array;
  @measureIDs = split(',', $measureIDString);

  #get displayable names for our DSR dimensions
  %productNames = dsr_get_item_name_hash($db, $dsSchema, "p");
  %geographyNames = dsr_get_item_name_hash($db, $dsSchema, "g");
  %timeperiodNames = dsr_get_item_name_hash($db, $dsSchema, "t");
  %measureNames = dsr_get_item_name_hash($db, $dsSchema, "m");

  #output header row
  $header = "Product\tGeography\tTime Period";
  foreach $measureID (@measureIDs)
  {
    $header .= "\t" . $measureNames{$measureID};
  }
  print("$header\n");

  #see if we have a table design with sorting/filtering we need to respect
  $query = "SELECT design FROM visuals WHERE cubeID=$rptID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($tableDesign) = $dbOutput->fetchrow_array;

  #build up WHERE clause if we were given any filter info
  #NB: basically, we're returning a slice instead of the whole cube
  $whereClause = "";

  $excludeNA = reports_get_style($tableDesign, "excludeNA");

  if (length($prodFilter) > 0)
  {
    $q_prodFilter = $db->quote($prodFilter);
    $whereClause = "WHERE product=$q_prodFilter";
  }

  if (length($geoFilter) > 0)
  {
    $q_geoFilter = $db->quote($geoFilter);

    if (length($whereClause) > 1)
    {
      $whereClause .= "AND geography=$q_geoFilter";
    }
    else
    {
      $whereClause = "WHERE geography=$q_geoFilter";
    }
  }

  if (length($timeFilter) > 0)
  {
    $q_timeFilter = $db->quote($timeFilter);

    if (length($whereClause) > 1)
    {
      $whereClause .= "AND time=$q_timeFilter";
    }
    else
    {
      $whereClause = "WHERE time=$q_timeFilter";
    }
  }

  #handle an "Exclude NA and 0" filtering directive
  if ($excludeNA == 1)
  {

    #if the user wants the entire cube
    if (length($whereClause) < 2)
    {
      $whereClause = "WHERE ";
      $excludeZeroClause = "(";
      $excludeNAClause = " AND (";
    }
    else
    {
      $excludeZeroClause = " AND (";
      $excludeNAClause = " AND (";
    }

    $first = 1;
    foreach $id (@measureIDs)
    {
      if ($id =~ m/^\d+$/)
      {
        if ($first == 1)
        {
          $first = 0;
          $excludeZeroClause  .= "measure_$id != 0";
          $excludeNAClause  .= "measure_$id IS NOT NULL";
        }
        else
        {
          $excludeZeroClause  .= " OR measure_$id != 0";
          $excludeNAClause  .= " OR measure_$id IS NOT NULL";
        }
      }
    }

    $excludeZeroClause .= ")";
    $excludeNAClause .= ")";
    $whereClause .= $excludeZeroClause . $excludeNAClause;
  }

  #
  #      ORDER BY
  #

  #handle the ORDER BY portion of our SQL query, start by looking for an
  #explicit user-defined sort order we should be using
  $sortMeas1 = reports_get_style($tableDesign, "sortMeas1");
  $sortMeas2 = reports_get_style($tableDesign, "sortMeas2");
  $sortMeas3 = reports_get_style($tableDesign, "sortMeas3");
  $sortOrder1 = reports_get_style($tableDesign, "sortOrder1");
  $sortOrder2 = reports_get_style($tableDesign, "sortOrder2");
  $sortOrder3 = reports_get_style($tableDesign, "sortOrder3");

  #if the first sorting criteria is specified
  if (($sortMeas1 > 0) || (length($sortMeas1) > 1))
  {
    $orderBy = "ORDER BY ";

    #figure out our fully-qualified measure ID if a financial measure
    if ($sortMeas1 =~ /^\d+$/)
    {
      $sortMeas1 = "measure_" . $sortMeas1;
    }
    elsif ($sortMeas1 eq "time")
    {
      $sortMeas1 = "sortTime";
    }

    $orderBy .= "$sortMeas1 $sortOrder1 ";

    #if the second sorting criteria is specified
    if (($sortMeas2 > 0) || (length($sortMeas2) > 1))
    {

      #figure out our fully-qualified measure ID if a financial measure
      if ($sortMeas2 =~ /^\d+$/)
      {
        $sortMeas2 = "measure_" . $sortMeas2;
      }
      elsif ($sortMeas2 eq "time")
      {
        $sortMeas2 = "sortTime";
      }

      $orderBy .= ", $sortMeas2 $sortOrder2 ";

      #if the third sorting criteria is specified
      if (($sortMeas3 > 0) || (length($sortMeas3) > 1))
      {

        #figure out our fully-qualified measure ID if a financial measure
        if ($sortMeas3 =~ /^\d+$/)
        {
          $sortMeas3 = "measure_" . $sortMeas3;
        }
        elsif ($sortMeas3 eq "time")
        {
          $sortMeas3 = "sortTime";
        }

        $orderBy .= ", $sortMeas3 $sortOrder3 ";
      }
    }
  }

  #elsif we're displaying the entire set of reporting data, sort by how user
  #selected products and then by time
  elsif ((length($prodFilter) < 1) && (length($geoFilter) < 1) && (length($timeFilter) < 1))
  {

    #get the in-order report item selections for geos and products
    @dimProducts = datasel_get_dimension_items($db, $rptID, "p");
    foreach $item (@dimProducts)
    {
      $prodDisplayIDs .= "'$item',";
    }
    chop($prodDisplayIDs);
    @dimGeographies = datasel_get_dimension_items($db, $rptID, "g");
    foreach $item (@dimGeographies)
    {
      $geoDisplayIDs .= "'$item',";
    }
    chop($geoDisplayIDs);

    $orderBy = "ORDER BY FIELD(product, $prodDisplayIDs), ";

    $orderBy .= "FIELD(geography, $geoDisplayIDs), ";
    $orderBy .= "sortTime DESC";
  }

  #if we don't have a time filter (in other words, we're returning all time
  #periods), sort the results by time period
  elsif (length($timeFilter) < 1)
  {
    $orderBy = "ORDER BY sortTime DESC";
  }

  #elsif the measure is numerical
  elsif ($measureIDs[0] =~ m/^\d/)
  {
    $orderBy = "ORDER BY measure_$measureIDs[0] DESC";
  }

  #else order the results by the first attribute/seg
  else
  {
    $orderBy = "ORDER BY $measureIDs[0] DESC";
  }

  #extract and output every row of data from the cube
  $query = "SELECT product, geography, time ";
  foreach $measureID (@measureIDs)
  {
    if ($measureID =~ m/^\d+/)
    {
      $query .= ",measure_$measureID";
    }
    else
    {
      $query .= ",$measureID";
    }
  }
  $query .= " FROM $dsSchema.$rptCube $whereClause $orderBy";

  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  @dataRow = $dbOutput->fetchrow_array;

  while (defined($dataRow[0]))
  {
    #convert dimensions from IDs to names
    $dataRow[0] = $productNames{$dataRow[0]};
    $dataRow[1] = $geographyNames{$dataRow[1]};
    $dataRow[2] = $timeperiodNames{$dataRow[2]};

    $line = join("\t", @dataRow);
    print("$line\n");

    @dataRow = $dbOutput->fetchrow_array;
  }

#EOF
