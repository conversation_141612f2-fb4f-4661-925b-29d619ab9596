#!/usr/bin/perl

use lib "/opt/apache/app/";

use DBI;
use Email::Valid;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::Pricing;
use Lib::WebUtils;


my $debug;


#-------------------------------------------------------------------------
#
# Output debug data, if enabled

sub DBG
{
  my ($str) = @_;

  if ($debug == 1)
  {
    print STDERR "$str\n";
  }
}



#-------------------------------------------------------------------------

  #figure out how many parallel jobs we're supposed to run, default=1
#  $maxProcs = $Lib::KoalaConfig::maxCubeUpdateProcs;
#XXX Need to create a new config variable for max analytics refresh procs
  if ($maxProcs < 1)
  {
    $maxProcs = 1;
  }

  #determine if we're being run manually
  $debug = 0;
  if ($ARGV[0] eq "debug")
  {
    $debug = 1;
  }

  #redirect STDERR to the Koala error log
  if ($debug == 0)
  {
    close(STDOUT);
    close(STDERR);
    open(STDERR, ">>/opt/apache/htdocs/tmp/koala_error.log");
  }

  #connect to the master database
  $db = KAPutil_connect_to_database();

  #subtract running analytics update jobs to see how many new ones we're going to run
  $query = "SELECT COUNT(*) FROM app.jobs \
      WHERE operation LIKE 'ANALYTICS-%' AND userID=0";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($runningJobs) = $dbOutput->fetchrow_array;

  $maxProcs = $maxProcs - $runningJobs;
  if (($maxProcs < 1) && ($debug == 0))
  {
    DBG("analyticsUpdate: System is already running $runningJobs, no available slots");
    exit;
  }

  #grab every DS's ID and last update timestamp, and hash them
  $query = "SELECT ID, UNIX_TIMESTAMP(lastUpdate) FROM dataSources \
      WHERE deleted = 0";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  while (($dsID, $lastUpdate) = $dbOutput->fetchrow_array)
  {

    #if it's been at least 2 minutes since the data source was last modified
    $timeSinceUpdate = time() - $lastUpdate;
    $timeSinceUpdate = $timeSinceUpdate / 60;
    if ($timeSinceUpdate > 2)
    {

      #add the data source to list to be considered for analytics updates
      $dsUpdateHash{$dsID} = $lastUpdate;
    }
  }

  #remove data sources being used by other processes from consideration
  $query = "SELECT dsID FROM app.jobs";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  while (($dsID) = $dbOutput->fetchrow_array)
  {
    delete($dsUpdateHash{$dsID});
  }

  #cycle through every elasticity model on the system, determining which ones
  #need to be updated
  undef(%updateHash);
  $query = "SELECT ID, dsID, UNIX_TIMESTAMP(lastRun) FROM analytics.pricing \
      WHERE ISNULL(status) OR status='DONE' ORDER BY dsID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;

  DBG("Examining $status elasticity models to see if they need to be refreshed");
  while (($elasticID, $dsID, $lastRun) = $dbOutput->fetchrow_array)
  {

    #if the underlying data source has been updated more recently than the cube
    if ($dsUpdateHash{$dsID} > $lastRun)
    {
      $updateHash{$dsID} = $updateHash{$dsID} . "$elasticID,";
    }
  }

  $processCount = 0;
  foreach $dsID (keys %updateHash)
  {
    $elasticIDStr = $updateHash{$dsID};
    DBG("Elasticity model $elasticIDStr in $dsID needs to be refreshed");

    #if we haven't hit our maximum process limit
    if ($processCount < $maxProcs)
    {

      #fire off the child process
      if ($pid = fork())
      {
        #parent process

        #increment count of active processes
        $processCount++;

        #give everything 10 seconds to get initialized before going again
        sleep(10);
      }

      #else we're the child process
      else
      {
        $childDB = KAPutil_connect_to_database();

        @elasticIDs = split(',', $elasticIDStr);
        foreach $elasticID (@elasticIDs)
        {

          #make sure the cube hasn't been updated by another process since we
          #started running
          $query = "SELECT UNIX_TIMESTAMP(lastRun) FROM analytics.pricing \
              WHERE ID=$elasticID";
          $dbOutput = $childDB->prepare($query);
          $dbOutput->execute;
          ($lastRun) = $dbOutput->fetchrow_array;
          if ($lastRun > $dsUpdateHash{$dsID})
          {
            DBG("Another process has already updated elasticity model $elasticID, skipping");
            next;
          }

          #rebuild the elasticity model
          DBG("Rebuilding $elasticID in $dsID");
          AInsights_build_model($childDB, $dsID, $elasticID, 0);
        }

        $dsName = ds_id_to_name($childDB, $dsID);
        utils_slack("Background agent refreshing elasticity models in $dsName");

        #we're done processing all elasticity models in this DS, so terminate process
        exit;
      }
    }

    #wait here until an empty process slot opens up
    if ($processCount >= $maxProcs)
    {
      wait();
      $processCount--;
    }
  }

  #wait here until the last cube building processes finish
  while ($processCount > 0)
  {
    wait();
    $processCount--;
  }


#EOF
