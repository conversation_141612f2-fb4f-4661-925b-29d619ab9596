#!/usr/bin/perl

use Text::CSV;

#Import C&S Key Foods Spin data for ESM-Ferolie

  open(INPUT, "$ARGV[0]");
  open(OUTPUT, ">$ARGV[1]");

  #instantiate our CSV object
  $csv = Text::CSV->new( {binary => 1} );

  #first line is headers
  $line = <INPUT>;
  $csv->parse($line);
  @columns = $csv->fields();

  $idx = 0;
  foreach $header (@columns)
  {
    if ($header eq "ITEM#")
    {
      $columns[$idx] = "pattr:ITEM#";
      $itemNumCol = $idx;
    }
    elsif ($header eq "SAP")
    {
      $columns[$idx] = "pattr:SAP";
    }
    elsif ($header eq "SAP Article")
    {
      $columns[$idx] = "pattr:SAP Article";
    }
    elsif ($header eq "DESCRIPTION")
    {
      $columns[$idx] = "Product";
      $productCol = $idx;
    }
    elsif ($header eq "UPC")
    {
      $upcCol = $idx;
    }
    elsif ($header eq "FROM")
    {
      $columns[$idx] = "Time Period";
    }
    elsif ($header eq "VENDOR#")
    {
      $columns[$idx] = "pseg:VENDOR#";
    }

    $idx++;
  }

  #add our geo header
  @tmp = ('Geography');
  push(@tmp, @columns);

  #output the headers
  $csv->combine(@tmp);
  $line = $csv->string();
  print OUTPUT "$line\n";


  while ($line = <INPUT>)
  {

    $csv->parse($line);
    @columns = $csv->fields();

    #format the date column
    if ($columns[3] =~ m/.* (\d+\/\d+\/\d+)$/)
    {
      $columns[3] = "1 MONTH ENDING $1";
    }

    #format the UPC (assumes 11 digit UPC)
    $columns[$upcCol] = sprintf("%011d", $columns[$upcCol]);
    $columns[$upcCol] =~ m/(\d)(\d\d\d\d\d)(\d\d\d\d\d)/;
    $upc = "$1-$2-$3-$3";
    $columns[$upcCol] = $upc;

    if ($columns[$itemNumCol] eq "#")
    {
      $columns[$itemNumCol] = "(NO ITEM #)";
    }

    #add the ITEM# to the end of the product name
    $columns[$productCol] = "$columns[$productCol] $columns[$itemNumCol]";

    #push our geography and end date onto the beginning of the line
    @tmp = ('Key Foods');
    push(@tmp, @columns);

    $csv->combine(@tmp);
    $line = $csv->string();

    print OUTPUT "$line\n";
  }
