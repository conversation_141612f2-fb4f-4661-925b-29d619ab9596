#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::Reports;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $rptID = $q->param('rptID');
  $visID = $q->param('v');
  $showLabels = $q->param('showLabels');
  $useSNameInLabels = $q->param('useSNameInLabels');
  $includeValueInLabels = $q->param('includeValueInLabels');
  $labelFontColor = $q->param('labelFontColor');
  $labelFontSize = $q->param('labelFontSize');
  $labelFont = $q->param('labelFont');

  #fix up the CGI parameters from the submitted form
  if (defined($showLabels))
  {
    $showLabels = ($showLabels eq "false") ? 0 : 1;
  }

  if (defined($useSNameInLabels))
  {
    $useSNameInLabels = ($useSNameInLabels eq "false") ? 0 : 1;
  }

  if (defined($includeValueInLabels))
  {
    $includeValueInLabels = ($includeValueInLabels eq "false") ? 0 : 1;
  }

  $labelFontColor = "#" . $labelFontColor;

  $db = KAPutil_connect_to_database();

  #make sure we have edit privs for this report
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this report.");
  }

  #get the map label details from the database
  $query = "SELECT design FROM visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($mapDesign) = $dbOutput->fetchrow_array;


  ########################################################################
  #
  # This code block is called on submit to save the changes
  #

  if (defined($showLabels))
  {
    $mapDesign = reports_set_style($mapDesign, "showLabels", $showLabels);
    $mapDesign = reports_set_style($mapDesign, "useSNameInLabels", $useSNameInLabels);
    $mapDesign = reports_set_style($mapDesign, "includeValueInLabels", $includeValueInLabels);
    $mapDesign = reports_set_style($mapDesign, "labelFontColor", $labelFontColor);
    $mapDesign = reports_set_style($mapDesign, "labelFontSize", $labelFontSize);

    if ($labelFont eq "Helvetica")
    {
      $mapDesign = reports_remove_style($mapDesign, "labelFont");
    }
    else
    {
      $mapDesign = reports_set_style($mapDesign, "labelFont", $labelFont);
    }

    $q_mapDesign = $db->quote($mapDesign);
    $query = "UPDATE visuals SET design = $q_mapDesign WHERE ID=$visID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    $cubeName = cube_id_to_name($db, $rptID);
    $dsID = cube_get_ds_id($db, $rptID);
    $dsName = ds_id_to_name($db, $dsID);

    utils_audit($db, $userID, "Changed map data labels", $dsID, $rptID, 0);
    $activity = "$first $last changed map data labels for $cubeName in $dsName";
    utils_slack($activity);

    exit;
  }


  #########################################################################
  #
  # Everything after this point is called to display the chart layout dialog
  #

  #extract map label options from design string
  $showLabels = reports_get_style($mapDesign, "showLabels");
  $useSNameInLabels = reports_get_style($mapDesign, "useSNameInLabels");
  $includeValueInLabels = reports_get_style($mapDesign, "includeValueInLabels");
  $labelFontColor = reports_get_style($mapDesign, "labelFontColor");
  $labelFontSize = reports_get_style($mapDesign, "labelFontSize");
  $labelFont = reports_get_style($mapDesign, "labelFont");

  #set appropriate defaults
  if (!(defined($showLabels)))
  {
    $showLabels = 1;
  }
  if (!(defined($useSNameInLabels)))
  {
    $useSNameInLabels = 0;
  }
  if (!(defined($includeValueInLabels)))
  {
    $includeValueInLabels = 0;
  }
  if (length($labelFontColor) < 7)
  {
    $labelFontColor = "#333333";
  }
  if ($labelFontSize < 3)
  {
    $labelFontSize = "10";
  }
  if (length($labelFont) < 3)
  {
    $labelFont = "Helvetica";
  }

  $showLabels = ($showLabels eq "1") ? "CHECKED" : "";
  $useSNameInLabels = ($useSNameInLabels eq "1") ? "CHECKED" : "";
  $includeValueInLabels = ($includeValueInLabels eq "1") ? "CHECKED" : "";

  print <<END_HTML;
<SCRIPT>
function submitForm()
{
  let showLabels = \$("#showLabels").prop("checked");
  let useSNameInLabels = \$("#useSNameInLabels").prop("checked");
  let includeValueInLabels = \$("#includeValueInLabels").prop("checked");
  let labelFontColor = document.getElementById('labelFontColor').value;
  let labelFontSize = document.getElementById('labelFontSize').value;
  let labelFont = document.getElementById('labelFont').value;

  //knock # off of color strings
  labelFontColor = labelFontColor.substr(1);

  let url = "xhrMapLabels?rptID=$rptID&v=$visID&showLabels=" + showLabels +
      "&useSNameInLabels=" + useSNameInLabels +
      "&includeValueInLabels=" + includeValueInLabels +
      "&labelFontColor=" + labelFontColor + "&labelFontSize=" + labelFontSize +
      "&labelFont=" + labelFont;

  \$.get(url, function(data, status)
  {
    location.href = "display.cld?rpt=$rptID&v=$visID";
  });
}
</SCRIPT>

<DIV CLASS="modal-dialog" STYLE="overflow-y: initial !important;">
  <DIV CLASS="modal-content">
    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Map Labels</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">

      <FORM>
      <TABLE CLASS="mx-auto">
        <TR>
          <TD STYLE="text-align:right;">
            Display map labels&nbsp;
          </TD>
          <TD>
            <DIV CLASS="form-check form-switch">
              <INPUT TYPE="checkbox" CLASS="form-check-input" NAME="showLabels" ID="showLabels" data-offstyle="secondary" $showLabels>
              <LABEL CLASS="form-check-label" FOR="showLabels">&nbsp;</LABEL>
            </DIV>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Use short names in labels&nbsp;
          </TD>
          <TD>
            <DIV CLASS="form-check form-switch">
              <INPUT TYPE="checkbox" CLASS="form-check-input" NAME="useSNameInLabels" ID="useSNameInLabels" data-offstyle="secondary" $useSNameInLabels>
              <LABEL CLASS="form-check-label" FOR="useSNameInLabels">&nbsp;</LABEL>
            </DIV>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Display values in labels&nbsp;
          </TD>
          <TD>
            <DIV CLASS="form-check form-switch">
              <INPUT TYPE="checkbox" CLASS="form-check-input" NAME="includeValueInLabels" ID="includeValueInLabels" data-offstyle="secondary" $includeValueInLabels>
              <LABEL CLASS="form-check-label" FOR="includeValueInLabels">&nbsp;</LABEL>
            </DIV>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Color:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="color" NAME="labelFontColor" ID="labelFontColor" VALUE="$labelFontColor" STYLE="width:50px;"></DIV>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Text size:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="number" NAME="labelFontSize" ID="labelFontSize" STYLE="width:75px;" VALUE="$labelFontSize" min=3>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Font family:&nbsp;
          </TD>
          <TD>
            <SELECT CLASS="form-select" NAME="labelFont" ID="labelFont">
              <OPTION VALUE="Arial">Arial</OPTION>
              <OPTION VALUE="Arial Black">Arial Black</OPTION>
              <OPTION VALUE="Comic Sans MS">Comic Sans MS</OPTION>
              <OPTION VALUE="Courier New">Courier New</OPTION>
              <OPTION VALUE="Helvetica">Helvetica</OPTION>
              <OPTION VALUE="Impact">Impact</OPTION>
              <OPTION VALUE="Tahoma">Tahoma</OPTION>
              <OPTION VALUE="Times New Roman">Times New Roman</OPTION>
              <OPTION VALUE="Verdana">Verdana</OPTION>
            </SELECT>
            <SCRIPT>
              \$("select#labelFont").val("$labelFont");
            </SCRIPT>
          </TD>
        </TR>

      </TABLE>
      </FORM>

    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="submitForm()"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML

#EOF
