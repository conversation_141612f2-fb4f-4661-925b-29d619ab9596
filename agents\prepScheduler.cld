#!/usr/bin/perl

use lib "/opt/apache/app/";

use DBI;

use Lib::KoalaConfig;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepRecipes;
use Lib::PrepSources;
use Lib::PrepUtils;
use Lib::WebUtils;


my $debug;

#list of the user IDs of "priority users" whose data flows will run first
if ($Lib::KoalaConfig::cloudname eq "beacon")
{
#  $priorityUserIDs = "47,49,50,66";
  $priorityUserIDs = "0";
#  %priorityUserIDHash = (
#    47 => 1,
#    49 => 1,
#    50 => 1,
#    66 => 1);
}
else
{
  $priorityUserIDs = "0";
}

#-------------------------------------------------------------------------
#
# Output debug data, if enabled
#

sub DBG
{
  my ($str) = @_;

  if ($debug == 1)
  {
    print STDERR "$str\n";
  }
}



#-------------------------------------------------------------------------
#
# Handle a database error of some kind - now using enhanced PrepUtils error handling
#

sub sched_db_err
{
  my ($prepDB, $status, $text) = @_;

  # Use the enhanced error handling from PrepUtils
  return PrepUtils_handle_db_err($prepDB, $status, $text);
}



#-------------------------------------------------------------------------

  #make sure we're running as a non-privileged user
  if ($UID == 0)
  {
    $daemonUID = getpwnam('daemon');
    $EUID = $daemonUID;
  }

  #determine if we're being run manually
  $debug = 0;
  if ($ARGV[0] eq "debug")
  {
    $debug = 1;
  }

  #redirect STDERR to the Koala error log
  if ($debug == 0)
  {
    close(STDOUT);
    close(STDIN);
    close(STDERR);
    open(STDERR, ">>/opt/apache/htdocs/tmp/koala_error.log");
    select(STDERR);
    $| = 1;
  }

  #connect to the master database
  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #make sure the prep cloud isn't out of storage
  $usagePct = prep_flow_storage($acctType);
  if ($usagePct > 99)
  {
    DBG("Data Prep cloud is out of storage - not running any scheduled jobs.");
    exit;
  }

  #---------------------------------------------------------------------------

  #figure out how many jobs we should try running

  #let's start by seeing if we're running during business hours
  #NB: we're defining business hours as 7am-7pm on a weekday
  $query = "SELECT HOUR(NOW()), DAYOFWEEK(NOW()) FROM prep.flows LIMIT 1";

  # Add exception handling for SQL statement
  eval {
    $dbOutput = $prepDB->prepare($query);
    $status = $dbOutput->execute;
    sched_db_err($prepDB, $status, $query);
    ($hour, $day) = $dbOutput->fetchrow_array;
  };
  if ($@) {
    my $date = localtime();
    print STDERR "$date: Exception getting business hours: $@\n";
    print STDERR "$date: Failed query: $query\n";

    # Send email notification for critical scheduler error
    PrepUtils_send_exception_email("Scheduler Startup Error", $@, $query, "prepScheduler.cld::business_hours");
    exit(1);
  }

  $businessHours = 1;
  if (($hour < 7) || ($hour > 19))
  {
    $businessHours = 0;
  }
  if (($day == 1) || ($day == 7))
  {
    $businessHours = 0;
  }

  #let's see how loaded the system is
  $runningJobs = prep_running_jobs($prepDB);

  $query = "SELECT COUNT(*) FROM prep.jobs \
      WHERE userID=0 AND state != 'LOADED' AND state NOT LIKE 'ERROR%' AND opInfo != 'DONE'";

  # Add exception handling for SQL statement
  eval {
    $dbOutput = $prepDB->prepare($query);
    $status = $dbOutput->execute;
    sched_db_err($prepDB, $status, $query);
    ($runningSchedJobs) = $dbOutput->fetchrow_array;
  };
  if ($@) {
    my $date = localtime();
    print STDERR "$date: Exception getting running scheduled jobs count: $@\n";
    print STDERR "$date: Failed query: $query\n";

    # Send email notification for critical scheduler error
    PrepUtils_send_exception_email("Scheduler Job Count Error", $@, $query, "prepScheduler.cld::job_count");
    exit(1);
  }

  #if this is a small system, run 1 flow unless system is overloaded
  if ($Lib::KoalaConfig::prepCores <= 4)
  {
    $businessHours = "SMALL";
    if ($runningJobs < 4)
    {
      $maxProcs = 1;
    }
    else
    {
      DBG("System is already running $runningJobs jobs of $Lib::KoalaConfig::prepCores");
      exit;
    }
  }

  $loadRatio = $runningJobs / $Lib::KoalaConfig::prepCores;

  #if we're inside business hours
  if ($businessHours == 1)
  {

    #leave at least 3 empty slots for user jobs
    $maxProcs = $Lib::KoalaConfig::prepCores - $runningJobs - 3;

    #but we've gotta get stuff done, so we're taking 9 slots no matter what
    if (($maxProcs < 9) && ($runningSchedJobs < 9))
    {
      $maxProcs = 9 - $runningSchedJobs;
    }
  }

  #if we're outside business hours, take all but one available slot
  if ($businessHours == 0)
  {
    $maxProcs = $Lib::KoalaConfig::prepCores - $runningSchedJobs - 1;
  }

  if ($maxProcs < 1)
  {
    DBG("System overused, not running any scheduled jobs");
    exit;
  }

  DBG("Going to run up to $maxProcs data flows");

  #get names of data flows for audit/Slack output purposes
  %flowNameHash = prep_flow_get_name_hash($prepDB);


  #----------------------------------------------------------------------------

  # mark weekly and monthly scheduled data flows as "needsRun" if applicable
  # NB: we're doing this because analysts invariably manage to overload their
  #     Prep clouds so weekly flows can't complete on their assigned day and
  #     monthly flows won't finish in their assigned month

  #get our numerical "day of week"
  $query = "SELECT DAYOFWEEK(NOW())";

  # Add exception handling for SQL statement
  eval {
    $dbOutput = $prepDB->prepare($query);
    $status = $dbOutput->execute;
    sched_db_err($prepDB, $status, $query);
    ($dbDay) = $dbOutput->fetchrow_array;
  };
  if ($@) {
    my $date = localtime();
    print STDERR "$date: Exception getting day of week: $@\n";
    print STDERR "$date: Failed query: $query\n";
    exit(1);
  }
  $dbDay--;

  #grab every weekly data flow that is scheduled to run today, hasn't yet
  #run today, and hasn't already been marked as "needs to run"
  $query = "SELECT flowID FROM prep.schedule \
      WHERE needsRun=0 AND sched='weekly' AND details LIKE '1 $dbDay' AND (DAYOFMONTH(lastRun) != DAYOFMONTH(NOW()) OR ISNULL(lastRun))";

  # Add exception handling for SQL statement
  eval {
    $dbOutput = $prepDB->prepare($query);
    $status = $dbOutput->execute;
    sched_db_err($prepDB, $status, $query);
    $flowIDStr = "";
    while (($flowID) = $dbOutput->fetchrow_array)
    {
      $flowIDStr .= "$flowID,";
    }
  };
  if ($@) {
    my $date = localtime();
    print STDERR "$date: Exception getting weekly flows: $@\n";
    print STDERR "$date: Failed query: $query\n";
    exit(1);
  }
  chop($flowIDStr);

  #mark any flows we found that match the heuristics as "needs to run"
  if (length($flowIDStr) > 0)
  {
    $query = "UPDATE prep.schedule SET needsRun=1 WHERE flowID IN ($flowIDStr)";

    # Add exception handling for SQL statement
    eval {
      $status = $prepDB->do($query);
      sched_db_err($prepDB, $status, $query);
    };
    if ($@) {
      my $date = localtime();
      print STDERR "$date: Exception updating weekly flows needsRun: $@\n";
      print STDERR "$date: Failed query: $query\n";

      # Send email notification for non-critical scheduler error
      PrepUtils_send_exception_email("Weekly Flow Update Error", $@, $query, "prepScheduler.cld::weekly_flows");
      # Don't exit here as this is not critical
    }
  }

  #get our numerical month of year
  $query = "SELECT MONTH(NOW())";

  # Add exception handling for SQL statement
  eval {
    $dbOutput = $prepDB->prepare($query);
    $status = $dbOutput->execute;
    sched_db_err($prepDB, $status, $query);
    ($curMonth) = $dbOutput->fetchrow_array;
  };
  if ($@) {
    my $date = localtime();
    print STDERR "$date: Exception getting current month: $@\n";
    print STDERR "$date: Failed query: $query\n";
    exit(1);
  }

  #grab every monthly data flow that is scheduled to run this month, hasn't
  #yet run this month, and hasn't already been marked as "needs to run"
  $query = "SELECT flowID FROM prep.schedule \
      WHERE needsRun=0 AND sched='monthly' AND details='$curMonth' AND (MONTH(lastRun) != MONTH(NOW()) OR ISNULL(lastRun))";

  # Add exception handling for SQL statement
  eval {
    $dbOutput = $prepDB->prepare($query);
    $status = $dbOutput->execute;
    sched_db_err($prepDB, $status, $query);
    $flowIDStr = "";
    while (($flowID) = $dbOutput->fetchrow_array)
    {
      $flowIDStr .= "$flowID,";
    }
  };
  if ($@) {
    my $date = localtime();
    print STDERR "$date: Exception getting monthly flows: $@\n";
    print STDERR "$date: Failed query: $query\n";
    exit(1);
  }
  chop($flowIDStr);

  #mark any flows we found that match the heuristics as "needs to run"
  if (length($flowIDStr) > 0)
  {
    $query = "UPDATE prep.schedule SET needsRun=1 WHERE flowID IN ($flowIDStr)";

    # Add exception handling for SQL statement
    eval {
      $status = $prepDB->do($query);
      sched_db_err($prepDB, $status, $query);
    };
    if ($@) {
      my $date = localtime();
      print STDERR "$date: Exception updating monthly flows needsRun: $@\n";
      print STDERR "$date: Failed query: $query\n";
      # Don't exit here as this is not critical
    }
  }


  #----------------------------------------------------------------------------

  # build up a string of data source IDs in priority order
  # NB: First comes "priority users", and then in numerical order. Would be
  #     nice to order based on last modification date, but we'd have to be
  #     careful since we update the modification date after each flow runs,
  #     which screws up data sources that have multiple data flows
  $dsOrderStr = "";
  $query = "SELECT DISTINCT dsID FROM flows \
      WHERE userID IN ($priorityUserIDs) AND dsID > 0 ORDER BY dsID";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  while (($dsID) = $dbOutput->fetchrow_array)
  {
    $dsOrderStr .= "$dsID,";
  }

  #next come non-Nielsen IDW data flows, since they can get buried in giant
  #monthly syndicated updates
  $query = "SELECT DISTINCT dsID FROM flows \
      WHERE userID NOT IN ($priorityUserIDs) AND dsID > 0 AND sourceInfo NOT LIKE 'FTP=nielsen%' \
      ORDER BY dsID";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  while (($dsID) = $dbOutput->fetchrow_array)
  {
    $dsOrderStr .= "$dsID,";
  }

  chop($dsOrderStr);
  if (length($dsOrderStr) > 0)
  {
    $query = "SELECT DISTINCT dsID FROM flows \
        WHERE dsID NOT IN ($dsOrderStr) AND dsID > 0 ORDER BY dsID";
    $dsOrderStr .= ",";
  }
  else
  {
    $query = "SELECT DISTINCT dsID FROM flows WHERE dsID > 0 ORDER BY dsID";
  }
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  while (($dsID) = $dbOutput->fetchrow_array)
  {
    $dsOrderStr .= "$dsID,";
  }
  chop($dsOrderStr);

  #build up a hash of all flows that have an active job - we're going to skip
  #them to avoid stepping on something an analyst is doing
  $query = "SELECT flowID FROM prep.jobs \
      WHERE state != 'LOADED' AND state NOT LIKE 'ERROR%'";

  # Add exception handling for SQL statement
  eval {
    $db_flows = $prepDB->prepare($query);
    $status = $db_flows->execute;
    sched_db_err($prepDB, $status, $query);
    while (($flowID) = $db_flows->fetchrow_array)
    {
      $runningFlowsHash{$flowID} = 1;
    }
  };
  if ($@) {
    my $date = localtime();
    print STDERR "$date: Exception getting running flows: $@\n";
    print STDERR "$date: Failed query: $query\n";
    exit(1);
  }

  #grab the user ID for all running flows
  #NB: we use this to be "fair" and not run too many flows for a single user
  #    at the expense of everyone else getting their flows to run
  $query = "SELECT jobs.flowID, flows.userID, flows.dsID FROM jobs \
      LEFT JOIN flows ON jobs.flowID = flows.ID \
      WHERE jobs.state !='LOADED' AND jobs.state NOT LIKE 'ERROR%' AND flows.dsID > 0";

  # Add exception handling for SQL statement
  eval {
    $dbOutput = $prepDB->prepare($query);
    $status = $dbOutput->execute;
    sched_db_err($prepDB, $status, $query);
    while (($flowID, $userID, $dsID) = $dbOutput->fetchrow_array)
    {
      $usersRunningFlowsHash{$userID} = 1;
      $dsHash{$dsID} = 1;
    }
  };
  if ($@) {
    my $date = localtime();
    print STDERR "$date: Exception getting user running flows: $@\n";
    print STDERR "$date: Failed query: $query\n";
    exit(1);
  }

  #grab all of the scheduled flows and their details from the database. Order
  #by last run date in ascending order to give preference to longest-time-since-
  #last-run flows.
  $query = "SELECT schedule.flowID, flows.userID, flows.dsID, schedule.sched, schedule.details, schedule.lastRun, schedule.lastFileSize, schedule.lastFileDate, schedule.needsRun \
      FROM schedule LEFT JOIN flows ON schedule.flowID = flows.ID \
      WHERE flows.dsID > 0 \
      ORDER BY FIELD(flows.dsID, $dsOrderStr)";

  # Add exception handling for SQL statement
  eval {
    $db_flows = $prepDB->prepare($query);
    $status = $db_flows->execute;
    sched_db_err($prepDB, $status, $query);
  };
  if ($@) {
    my $date = localtime();
    print STDERR "$date: Exception getting scheduled flows: $@\n";
    print STDERR "$date: Failed query: $query\n";
    exit(1);
  }

  $runCount = 0;

  # First, check if we've already collected enough processes to run
  # This avoids unnecessary processing of flows we won't run anyway
  my $flowsToCollect = $maxProcs + 2;

  while (($flowID, $flowOwnerID, $dsID, $sched, $details, $lastRun, $lastFileSize, $lastFileDate, $needsRun) = $db_flows->fetchrow_array)
  {
    # Exit the loop early if we've already collected enough flows
    if (scalar(@runFlowIDs) >= $flowsToCollect)
    {
      DBG("Already collected $flowsToCollect flows, stopping flow collection");
      last;
    }

    # Quick checks to filter out flows we definitely won't run
    # This avoids unnecessary processing

    #if there's already a job running in the data flow
    if ($runningFlowsHash{$flowID} > 0)
    {
      DBG("Skipping $flowID - there's already a job running in this flow\n\t$flowNameHash{$flowID}");
      next;
    }

    #if we're already doing a flow for this dsID, skip it
    if ($dsHash{$dsID} > 0)
    {
      DBG("Skipping $flowID - already doing a flow for this data source\n\t$flowNameHash{$flowID} in $dsID");
      next;
    }

    #if the flow is scheduled for never, skip it
    if ($sched eq "never")
    {
      next;
    }


    #--------------------------------------------

    #if the flow is set to run daily
    if ($sched eq "daily")
    {

      #details field contains the nth day we should run on
      if ($details < 1)
      {
        $details = 1;
      }

      #see if it's been at least 24 hours since our last schedule run
      #NB: we're checking every 12 hours for 1-day schedules just to be extra
      #    careful - we want to make sure we don't miss a day of output from
      #    "true" daily providers like AFS (852 data)
      if ($details == 1)
      {
        $query = "SELECT flowID FROM prep.schedule \
            WHERE (DATE_SUB(NOW(), INTERVAL 12 HOUR) > lastRun OR ISNULL(lastRun)) AND flowID=$flowID";
      }
      else
      {
        $query = "SELECT flowID FROM prep.schedule \
            WHERE (DATE_SUB(NOW(), INTERVAL $details DAY) > lastRun OR ISNULL(lastRun)) AND flowID=$flowID";
      }
      $dbOutput = $prepDB->prepare($query);
      $dbOutput->execute;
      ($tmp) = $dbOutput->fetchrow_array;

      #if we're not due to try a scheduled run of the data flow, skip it
      if (($tmp ne $flowID) && ($needsRun < 1))
      {
        next;
      }
    }


    #---------------------------------------

    #if the flow is set to run weekly
    elsif ($sched eq "weekly")
    {

      #details field contains nth weeks to run, and day to run on
      $week = 1;
      $day = 1;
      if ($details =~ m/^(\d+) (\d)$/)
      {
        $week = $1;
        $day = $2;
        $day++;
      }

      #make sure it's the right day of the week
      $query = "SELECT DAYOFWEEK(NOW())";
      $dbOutput = $prepDB->prepare($query);
      $dbOutput->execute;
      ($dbDay) = $dbOutput->fetchrow_array;
      if (($day != $dbDay) && ($needsRun < 1))
      {
        next;
      }

      #make sure we haven't been run since the specified number of weeks ago
      #NB: we're subtracting one day from the actual number of days just to
      #    cover any overrun eventualities
      $dbDays = ($week * 7) - 1;
      $query = "SELECT flowID FROM prep.schedule \
          WHERE (DATE_SUB(NOW(), INTERVAL $dbDays DAY) > lastRun OR ISNULL(lastRun)) AND flowID=$flowID";
      $dbOutput = $prepDB->prepare($query);
      $dbOutput->execute;
      ($tmp) = $dbOutput->fetchrow_array;
      if (($tmp ne $flowID) && ($needsRun < 1))
      {
        next;
      }
    }


    #---------------------------------------

    #if the flow is set to run monthly
    elsif ($sched eq "monthly")
    {

      #details field contains a single month to run inside of
      if ($details =~ m/^(\d)$/)
      {
        $month = $1;
      }

      #make sure it's the right month
      $query = "SELECT MONTH(DATE(NOW()))";
      $dbOutput = $prepDB->prepare($query);
      $dbOutput->execute;
      ($dbMonth) = $dbOutput->fetchrow_array;

      if (($month != $dbMonth) && ($needsRun < 1))
      {
        next;
      }

      #make sure we haven't been run in the past couple days
      $query = "SELECT flowID FROM prep.schedule \
          WHERE (DATE_SUB(NOW(), INTERVAL 2 DAY) > lastRun OR ISNULL(lastRun)) AND flowID=$flowID";
      $dbOutput = $prepDB->prepare($query);
      $dbOutput->execute;
      ($tmp) = $dbOutput->fetchrow_array;
      if (($tmp != $flowID) && ($needsRun < 1))
      {
        next;
      }
    }

    #figure out what type of source our flow has
    $query = "SELECT userID, source, sourceInfo FROM prep.flows WHERE ID=$flowID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($userID, $source, $sourceInfo) = $dbOutput->fetchrow_array;

    #force a skip of any flow that we can't run non-interactively
    if (($source eq "Manual") || ($source eq "Paste"))
    {
      next;
    }

    #force a skip of anything daily/weekly that's set to pull a 108 data set
    #from a Nielsen AOD IDW instance
    if (($sourceInfo =~ m/^FTP=nielsen\|USER=beacon/i) && (($sched eq 'daily') || ($sched eq 'weekly')))
    {
      if (!($sourceInfo =~ m/update/))
      {
        DBG("prepScheduler: Skipping daily/weekly AOD IDW data flow using 108 week data set");
        next;
      }
    }

    #if it's possible to determine if we don't need to re-run the flow, do it
    $fileSize = 0;
    $fileDate = "";
    if ($source eq "Web")
    {
      ($fileSize, $fileDate) = prep_source_web_newdata($prepDB, $flowID);
    }

    elsif ($source eq "FTP")
    {
      ($fileSize, $fileDate) = prep_source_ftp_newdata($prepDB, $flowID);
    }

    elsif ($source eq "Koala")
    {
      ($fileSize, $fileDate) = prep_source_koala_newdata($kapDB, $prepDB, $flowID);
    }

    DBG("prepScheduler: Got filesize of $fileSize, date of $fileDate for $flowID");

    #if something went wrong when we looked for new data
    if (($fileSize < 1) || (length($fileDate) < 5))
    {
      $query = "UPDATE prep.schedule SET lastRun=NOW() WHERE flowID=$flowID";
      $prepDB->do($query);
      DBG("prepScheduler: Skipping flow $flowID, couldn't determine data size/date");
      next;
    }

    if ($fileSize < 1)
    {
      $fileSize = 0;
    }

    if (length($fileDate) < 5)
    {
      $fileDate = '1990-01-01 00:00:00';
    }

    #if the data flow isn't updating a data source
    if ($dsID < 1)
    {
      $query = "UPDATE prep.schedule \
          SET lastRun=NOW(), lastFileSize=$fileSize, lastFileDate='$lastFileDate', needsRun=0 \
          WHERE flowID=$flowID";
      $prepDB->do($query);
      DBG("prepScheduler: Skipping flow $flowID, not updating a data source");
      next;
    }

    #check to see if there's new data
    $query = "SELECT flowID FROM prep.schedule \
        WHERE flowID=$flowID AND ((lastFileSize != $fileSize OR lastFileDate != '$fileDate') OR (ISNULL(lastFileSize) OR ISNULL(lastFileDate)))";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($tmp) = $dbOutput->fetchrow_array;

    #if not, update the lastRun in schedule and skip it
    if (!defined($tmp))
    {
      $query = "UPDATE prep.schedule \
          SET lastRun=NOW(), needsRun=0 WHERE flowID=$flowID";
      $prepDB->do($query);
      DBG("prepScheduler: Skipping flow $flowID, no new data found");
      next;
    }

    #if there are any active jobs in the flow, skip it (and log the skip)
    $query = "SELECT opInfo FROM prep.jobs \
        WHERE flowID = $flowID AND opInfo NOT LIKE 'DONE%' AND opInfo NOT LIKE 'ERR|%' AND (state != 'LOADED' OR mode = 'run')";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($opInfo) = $dbOutput->fetchrow_array;

    if (defined($opInfo))
    {
      DBG("Skipping $flowID - another job for the flow is already active ($opInfo)");
      next;
    }

    #make sure it's been a reasonable amount of time since the last
    #time something happened in this data flow (so we don't step on active
    #work)
    $query = "SELECT MIN(DATEDIFF(NOW(), lastAction)) FROM prep.jobs \
        WHERE flowID=$flowID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($datediff) = $dbOutput->fetchrow_array;

    if (defined($datediff))
    {
      if ($datediff < 1)
      {
        DBG("Skipping $flowID - data flow has been active in the past 24 hours");
#        next;
      }
    }

    #we're supposed to update a data source when we're done
    if ($dsID > 0)
    {

      #if another data source operation is going on
      $ok = DSRutil_operation_ok($kapDB, $dsID, 0, "DS-UPDATE");
      if ($ok != 1)
      {
        DBG("Skipping $flowID - an Analytics job is currently using the data source");
        next;
      }

      #make sure the analytics node isn't out of storage
      $usagePct = KAPutil_get_org_quota_used($kapDB, $userID);
      if ($usagePct > 99)
      {
        DBG("Skipping $flowID, Analytics node is out of storage.");
        next;
      }
    }

    #if the flow is owned by a user that already has a running flow, add it to
    #the "maybe run" list
    if (($usersRunningFlowsHash{$flowOwnerID} > 0) &&
        ($priorityUserIDHash{$flowOwnerID} < 1))
    {
      DBG("Adding flow $flowID to our list to maybe run ($flowOwnerID already has a running job)");
      push(@maybeRunFlowIDs, $flowID);
      $dsHash{$dsID} = 1;
      next;
    }

    #if we've made this far, then the flow is eligible to be run
    DBG("Adding flow $flowID to our list to be run");
    push(@runFlowIDs, $flowID);
    $dsHash{$dsID} = 1;
    $usersRunningFlowsHash{$flowOwnerID} = 1;
  }


  #-----------------------------------------------------------------------------


  #have the system auto-reap any child processes we fork off
  $SIG{CHLD} = 'IGNORE';

  #run as many flows as we can get away with
  $runCount = 0;
  push(@runFlowIDs, @maybeRunFlowIDs);
  foreach $flowID (@runFlowIDs)
  {

    #split off a child process to run the next step in the flow
    if ($pid = fork())
    {
      #parent process - do nothing and continue processing jobs

      $runCount++;
    }

    #else we're the child process
    else
    {
      my $prepDB = PrepUtils_connect_to_database();
      my $kapDB = KAPutil_connect_to_database();

      #clear any "needs to be run" flag
      $query = "UPDATE prep.schedule SET needsRun=0 WHERE flowID=$flowID";

      # Add exception handling for SQL statement
      eval {
        $status = $prepDB->do($query);
        sched_db_err($prepDB, $status, $query);
      };
      if ($@) {
        my $date = localtime();
        print STDERR "$date: Exception clearing needsRun flag: $@\n";
        print STDERR "$date: Failed query: $query\n";
        # Continue anyway as this is not critical
      }

      #give everything a few seconds to settle down
      sleep(2);

      #fail safe check to make sure we're not accidentally running a flow
      #that is already running
      $query = "SELECT ID FROM prep.jobs WHERE flowID=$flowID AND state NOT IN ('LOADED', 'ERROR')";

      # Add exception handling for SQL statement
      eval {
        $dbOutput = $prepDB->prepare($query);
        $status = $dbOutput->execute;
        sched_db_err($prepDB, $status, $query);
        ($jobID) = $dbOutput->fetchrow_array;
      };
      if ($@) {
        my $date = localtime();
        print STDERR "$date: Exception checking for existing jobs: $@\n";
        print STDERR "$date: Failed query: $query\n";
        exit;
      }
      if ($jobID > 0)
      {
        print STDERR "Job $jobID already running for flow $flowID, exiting\n";
        exit;
      }

      $jobID = prep_flow_create_job($prepDB, $flowID, 0);

      #run the flow
      prep_run_flow($prepDB, $kapDB, 0, $flowID, $jobID);

      #since we just did a run, we want to update the data used by the
      #scheduler to determine when new data is available
      prep_flow_update_schedule_status($prepDB, $flowID);

      utils_slack("PREP: Scheduler ran data flow $flowNameHash{$flowID}");

      exit;
    }

    #limit each process to running 3 flows
    if ($runCount >= $maxProcs)
    {
      exit;
    }
  }


#EOF
