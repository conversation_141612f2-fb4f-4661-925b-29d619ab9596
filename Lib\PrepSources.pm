
package Lib::PrepSources;

use lib "/opt/apache/app/";

use Exporter;
use DBI;
use LWP::UserAgent;
use Text::CSV_XS;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepUtils;

our @ISA = ('Exporter');

our @EXPORT = qw(
    &prep_source_expand_custom
    &prep_source_web
    &prep_source_ftp
    &prep_source_amazon
    &prep_source_database
    &prep_source_koala
    &prep_source_web_newdata
    &prep_source_ftp_newdata
    &prep_source_koala_newdata
  );



#define hash of custom expansion tags (cloudname + user-entered ID)
my %customSystems = (
  "bair-nielsen" => "*************",
  "beacon-nielsen" => "*************",
  "vista-nielsen" => "*************",
  "vista-iri" => "*************",
  "test-nielsen" => "*************",
  "demo-nielsen" => "localhost",
  "dev-nielsen" => "localhost",);



#-------------------------------------------------------------------------
#
# Expand any custom tags in a user URL (we use these to keep things simple,
# and enforce usage of internal cloud networking).
#

sub prep_source_expand_custom
{
  my ($tag);

  my ($ftpserver) = @_;


  $ftpserver = lc($ftpserver);
  $tag = $Lib::KoalaConfig::cloudname . "-" . $ftpserver;
  if (length($customSystems{$tag}) > 1)
  {
    $ftpserver = $customSystems{$tag};
  }

  return($ftpserver);
}



#-------------------------------------------------------------------------
#
# Perform a web data download for the specified data flow
#

sub prep_source_web
{
  my ($query, $dbOutput, $userID, $flowID, $url, $ua, $response, $filename);
  my ($OUTPUT, $localfile, $count, $resp, $data, $opInfo, $q_opInfo, $pct);
  my ($ttlDown, $code, $status);

  my ($prepDB, $jobID) = @_;


  #set the "title" of the operation for UI
  PrepUtils_set_job_op_title($prepDB, $jobID, "Loading data from the web");
  $startTime = PrepUtils_get_current_timestamp($prepDB);

  #grab the stored job info
  $query = "SELECT userID, flowID FROM prep.jobs WHERE ID=$jobID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($userID, $flowID) = $dbOutput->fetchrow_array;

  #grab the URL to be downloaded
  $query = "SELECT sourceInfo FROM prep.flows WHERE ID=$flowID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($url) = $dbOutput->fetchrow_array;
  $url =~ m/^URL=(.*)$/;
  $url = $1;

  $query = "UPDATE prep.jobs SET PID=$$, opInfo='0|Transferring data' WHERE ID=$jobID";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);
  PrepUtils_set_job_op_details($prepDB, $jobID, "Transferring data");
  PrepUtils_set_job_op_pct($prepDB, $jobID, 0);

  #download file using LWP
  $url =~ m/.*\/(.*)$/;
  $filename = $1;
  $localfile = "/opt/apache/app/tmp/prep.$userID.$jobID.$filename";
  PrepUtils_set_job_op_details($prepDB, $jobID, "Transferring <TT>$filename</TT>");
  open(OUTPUT, ">$localfile");
  $ua = LWP::UserAgent->new();
  $count = 0;
  $resp = $ua->get($url, ":content_cb" => sub
  {
    my ($data, $response) = @_;

    $ttlDown += length($data);
    $count++;
    if (($count % 100) == 0)
    {
      my $size = $response->content_length;
      $pct = int(($ttlDown / $size) * 100);
      $opInfo = "$pct|Transferring data";
      $q_opInfo = $prepDB->quote($opInfo);
      $query = "UPDATE prep.jobs SET opInfo=$q_opInfo WHERE ID=$jobID";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);
      PrepUtils_set_job_op_pct($prepDB, $jobID, $pct);
    }

    print OUTPUT $data;
  });

  close(OUTPUT);

  $code = $resp->code;
  if ($resp->code != 200)
  {
    $opInfo = "ERR|$code:" . $resp->message;
    $q_opInfo = $prepDB->quote($opInfo);
    $query = "UPDATE prep.jobs
        SET PID=NULL, opInfo=$q_opInfo, state='ERROR', lastAction=NOW()
        WHERE ID=$jobID";
  }
  else
  {
    $query = "UPDATE prep.jobs
        SET PID=NULL, opInfo='DONE|Transferring data', lastAction=NOW()
        WHERE ID=$jobID";
  }
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");
  PrepUtils_set_job_op_details($prepDB, $jobID, "");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "");

  return($resp);
}



#-------------------------------------------------------------------------
#
# Perform an FTP data download for the specified data flow
#

sub prep_source_ftp
{
  my ($query, $dbOutput, $ftpInfo, $ftpserver, $ftpuser, $ftppass, $status);
  my ($ftppath, $filepath, $filename, $localfile, $ftp, $userID, $flowID, $tag);
  my ($uri, $ua, $count, $pct, $resp, $ttlDown, $opInfo, $q_opInfo, $code);

  my ($prepDB, $jobID) = @_;


  #set the "title" of the operation for UI
  PrepUtils_set_job_op_title($prepDB, $jobID, "Transferring data");
  $startTime = PrepUtils_get_current_timestamp($prepDB);

  #grab the stored job info
  $query = "SELECT userID, flowID FROM prep.jobs WHERE ID=$jobID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($userID, $flowID) = $dbOutput->fetchrow_array;

  $query = "UPDATE prep.jobs SET PID=$$, opInfo='0|Transferring Data'
      WHERE ID=$jobID";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);
  PrepUtils_set_job_op_details($prepDB, $jobID, "Transferring data");
  PrepUtils_set_job_op_pct($prepDB, $jobID, 0);

  #grab the info needed to perform the FTP download
  $query = "SELECT sourceInfo FROM prep.flows WHERE ID=$flowID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($ftpInfo) = $dbOutput->fetchrow_array;
  $ftpInfo =~ m/^FTP=(.*?)\|USER=(.*?)\|PASS=(.*?)\|PATH=(.*)$/;
  $ftpserver = $1;
  $ftpuser = $2;
  $ftppass = $3;
  $ftppath = $4;

  #handle any special tag the user might be using for the FTP server
  $ftpserver = lc($ftpserver);
  $tag = $Lib::KoalaConfig::cloudname . "-" . $ftpserver;
  if (length($customSystems{$tag}) > 1)
  {
    $ftpserver = $customSystems{$tag};

    if ($tag =~ m/nielsen/i)
    {
      $query = "UPDATE prep.job_history
          SET filenames='Nielsen IDW ' WHERE jobID=$jobID";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);
    }
  }

  #build fully-qualified file name to request from FTP server
  if ($ftppath =~ m/(.*\/)(.*)$/)
  {
    $filepath = $1;
    $filename = $2;
   }
  else
  {
    $filepath = "";
    $filename = $ftppath;
  }

  $localfile = "/opt/apache/app/tmp/prep.$userID.$jobID.$filename";

  #build up FTP URI
  $uri = "ftp://$ftpuser:$ftppass\@$ftpserver/$ftppath";

  #download file using LWP's FTP capability
  PrepUtils_set_job_op_details($prepDB, $jobID, "Transferring <TT>$filename</TT>");
  open(OUTPUT, ">$localfile");
  $ua = LWP::UserAgent->new();
  $count = 0;
  $resp = $ua->get($uri, ":content_cb" => sub
  {
    my ($data, $response) = @_;

    $ttlDown += length($data);
    $count++;
    if (($count % 100) == 0)
    {
      my $size = $response->content_length;
      if ($size > 0)
      {
        $pct = int(($ttlDown / $size) * 100);
      }
      else
      {
        $pct = 0;
      }
      $opInfo = "$pct|Transferring data";
      $q_opInfo = $prepDB->quote($opInfo);
      $query = "UPDATE prep.jobs SET opInfo=$q_opInfo WHERE ID=$jobID";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);
      PrepUtils_set_job_op_pct($prepDB, $jobID, $pct);
    }

    print OUTPUT $data;
  });

  close(OUTPUT);

  $code = $resp->code;
  if ($resp->code != 200)
  {
    $opInfo = $resp->message;
    $opInfo =~ s/LWP::Protocol::My//;
    $opInfo = "ERR|" . $opInfo;
    $q_opInfo = $prepDB->quote($opInfo);
    $query = "UPDATE prep.jobs
        SET PID=NULL, opInfo=$q_opInfo, state='ERROR', lastAction=NOW()
        WHERE ID=$jobID";
  }
  else
  {
    $query = "UPDATE prep.jobs
        SET PID=NULL, opInfo='DONE', lastAction=NOW()
        WHERE ID=$jobID";
  }
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");
  PrepUtils_set_job_op_details($prepDB, $jobID, "");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "");

  return($resp);
}



#-------------------------------------------------------------------------
#
# Transfer a file from Amazon S3
#

sub prep_source_amazon
{
  my ($query, $dbOutput, $userID, $flowID, $sourceInfo, $accessKey);
  my ($secretKey, $bucketName, $filePath, $filepath, $filename, $localfile);
  my ($output, $opInfo, $q_opInfo, $status);

  my ($prepDB, $jobID) = @_;


  #set the "title" of the operation for UI
  PrepUtils_set_job_op_title($prepDB, $jobID, "Loading data from Amazon S3");
  $startTime = PrepUtils_get_current_timestamp($prepDB);

  #grab the stored job info
  $query = "SELECT userID, flowID FROM prep.jobs WHERE ID=$jobID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($userID, $flowID) = $dbOutput->fetchrow_array;

  #grab the info needed to perform the transfer from the Amazon S3 bucket
  $query = "SELECT sourceInfo FROM prep.flows WHERE ID=$flowID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($sourceInfo) = $dbOutput->fetchrow_array;

  $sourceInfo =~ m/^ACCESS=(.*?)\|SECRET=(.*?)\|BUCKET=(.*?)\|PATH=(.*)$/;
  $accessKey = $1;
  $secretKey = $2;
  $bucketName = $3;
  $filePath = $4;

  #get our filename, minus the path
  if ($filePath =~ m/(.*\/)(.*)$/)
  {
    $filepath = $1;
    $filename = $2;
   }
  else
  {
    $filepath = "";
    $filename = $filePath;
  }

  #the name we're going to copy the file to
  $localfile = "/opt/apache/app/tmp/prep.$userID.$jobID.$filename";

  $query = "UPDATE prep.jobs SET PID=$$, opInfo='0|Transferring $filepath'
      WHERE ID=$jobID";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);
  PrepUtils_set_job_op_details($prepDB, $jobID, "Transferring data");
  PrepUtils_set_job_op_pct($prepDB, $jobID, 0);

  #retrieve the file from Amazon S3
  #NB: http://s3tools.org/s3cmd-howto
  PrepUtils_set_job_op_details($prepDB, $jobID, "Transferring <TT>$filepath</TT>");
  chdir("/opt/apache/app/tmp/");
  $SIG{CHLD} = "DEFAULT";
  $output = `s3cmd --access_key=$accessKey --secret_key=$secretKey get s3://$bucketName/$filePath 2>&1`;

  #basic error handling
  if ($output =~ m/ERROR/i)
  {
    $opInfo = "ERR|" . $output;
    $opInfo = substr($opInfo, 0, 80);
    $q_opInfo = $prepDB->quote($opInfo);
    $query = "UPDATE prep.jobs
        SET PID=NULL, opInfo=$q_opInfo, state='ERROR', lastAction=NOW()
        WHERE ID=$jobID";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);
    return;
  }

  #rename the retrieved file to our local name
  rename($filename, $localfile);

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");
  PrepUtils_set_job_op_details($prepDB, $jobID, "");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "");

  $query = "UPDATE prep.jobs
      SET PID=NULL, opInfo='DONE|Transferring data', lastAction=NOW()
      WHERE ID=$jobID";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);
}



#-------------------------------------------------------------------------
#
# Transfer a file from an external database table
#

sub prep_source_database
{
  my ($query, $dbOutput, $userID, $flowID, $sourceInfo, $dbType, $dbHost);
  my ($dbTable, $dbUser, $dbPass, $Localfile, $OUTPUT, $csv, $dbh, $localfile);
  my ($dbSchema, $dbConnectStr, $status, $dbh_output);
  my (@vals);

  my ($prepDB, $jobID, $key) = @_;


  #set the "title" of the operation for UI
  PrepUtils_set_job_op_title($prepDB, $jobID, "Loading data from external database");
  $startTime = PrepUtils_get_current_timestamp($prepDB);

  #grab the stored job info
  $query = "SELECT userID, flowID FROM prep.jobs WHERE ID=$jobID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($userID, $flowID) = $dbOutput->fetchrow_array;

  #grab the info needed to perform the database xfer
  $query = "SELECT sourceInfo FROM prep.flows WHERE ID=$flowID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($sourceInfo) = $dbOutput->fetchrow_array;

  $sourceInfo =~ m/^TYPE=(.*?)\|HOST=(.*?)\|TABLE=(.*?)\|USER=(.*?)\|PASS=(.*)$/;
  $dbType = $1;
  $dbHost = $2;
  $dbTable = $3;
  $dbUser = $4;
  $dbPass = $5;

  $query = "UPDATE prep.jobs SET PID=$$, opInfo='0|Transferring Data'
      WHERE ID=$jobID";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  PrepUtils_set_job_op_title($prepDB, $jobID, "Loading data from $dbType database");
  PrepUtils_set_job_op_details($prepDB, $jobID, "Transferring data from $dbTable");
  PrepUtils_set_job_op_pct($prepDB, $jobID, 0);

  #the name we're going to write data out to
  $localfile = "/opt/apache/app/tmp/prep.$userID.$key.dbdump.csv";
  open(OUTPUT, ">$localfile");

  $csv = Text::CSV->new( {binary => 1} );

  #build up our DBI connection string
  $dbTable =~ m/^(.*?)\./;
  $dbSchema = $1;
  $dbConnectStr = "DBI:$dbType:$dbSchema;host=$dbHost";

  #connect to the database
  $dbh = DBI->connect($dbConnectStr, $dbUser, $dbPass);
  if (!defined($dbh))
  {
    $query = "UPDATE prep.jobs
        SET PID=NULL, opInfo='ERR|Unable to connect to database', state='ERROR'
        WHERE ID=$jobID";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);
    return;
  }

  $query = "SELECT * FROM $dbTable";
  $dbOutput = $dbh->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($dbh, $status, $query);
  @vals = $dbh_output->fetchrow_array;

  while (defined($vals[0]))
  {
    $csv->print(OUTPUT, \@vals);
    print OUTPUT "\n";

    @vals = $dbh_output->fetchrow_array;
  }

  close(OUTPUT);

  $query = "UPDATE prep.jobs
      SET PID=NULL, opInfo='DONE|Transferring data', lastAction=NOW()
      WHERE ID=$jobID";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");
  PrepUtils_set_job_op_details($prepDB, $jobID, "");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "");
}



#-------------------------------------------------------------------------
#
# Transfer data from a Koala data source (including segments & attributes)
#

sub prep_source_koala
{
  my ($query, $dbOutput, $userID, $flowID, $sourceInfo, $dsID, $dsSchema);
  my ($localfile, $OUTPUT, $upcID, $segID, $measureID, $measSubQ, $measID);
  my ($dbh, $totalRows, $rowsDone, $pct, $opInfo, $q_opInfo, $idx, $data);
  my ($prodID, $geoID, $timeID, $segmentID, $status, $start, $curPos, $dsName);
  my (@prodSegIDs, @geoSegIDs, @timeSegIDs, @prodSegs, @geoSegs, @timeSegs);
  my (@rowData);
  my (%prodNameHash, %geoNameHash, %timeNameHash, %measNameHash, %upcHash);

  my ($kapDB, $prepDB, $jobID) = @_;


  #set the "title" of the operation for UI
  PrepUtils_set_job_op_title($prepDB, $jobID, "Loading data from Koala Analytics");
  $startTime = PrepUtils_get_current_timestamp($prepDB);

  #grab the stored job info
  $query = "SELECT userID, flowID FROM prep.jobs WHERE ID=$jobID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($userID, $flowID) = $dbOutput->fetchrow_array;

  #grab the info needed to perform the FTP download
  $query = "SELECT sourceInfo FROM prep.flows WHERE ID=$flowID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($sourceInfo) = $dbOutput->fetchrow_array;

  $sourceInfo =~ m/^DSID=(.*)$/;
  $dsID = $1;

  $dsSchema = "datasource_" . $dsID;
  $dsName = ds_id_to_name($kapDB, $dsID);

  $query = "UPDATE prep.jobs SET PID=$$, opInfo='0|Transferring data'
      WHERE ID=$jobID";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);
  PrepUtils_set_job_op_details($prepDB, $jobID, "Transferring $dsName");

  #the name we're going to write data out to
  $localfile = "/opt/apache/app/tmp/prep.$userID.$jobID.Koala.csv";
  open(OUTPUT, ">$localfile");

  #get the item names for all dimensions
  %prodNameHash = dsr_get_item_name_hash($kapDB, $dsSchema, "p");
  %geoNameHash = dsr_get_item_name_hash($kapDB, $dsSchema, "g");
  %timeNameHash = dsr_get_item_name_hash($kapDB, $dsSchema, "t");
  %measNameHash = dsr_get_item_name_hash($kapDB, $dsSchema, "m");

  #if there's a UPC attribute in this data source, find its ID and get val hash
  $query = "SELECT ID FROM $dsSchema.product_attributes WHERE name = 'UPC'";
  $dbOutput = $kapDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($kapDB, $status, $query);
  ($upcID) = $dbOutput->fetchrow_array;
  undef(%upcHash);
  if (defined($upcID))
  {
    %upcHash = DSRattr_get_values_hash($kapDB, $dsSchema, "p", $upcID);
  }

  #get the ID of every segmentation in our DS
  @prodSegIDs = DSRsegmentation_get_segmentations_array($kapDB, $dsSchema, "p");
  @geoSegIDs = DSRsegmentation_get_segmentations_array($kapDB, $dsSchema, "g");
  @timeSegIDs = DSRsegmentation_get_segmentations_array($kapDB, $dsSchema, "t");

  #build up an array of segment membership hashes for each segmentation
  foreach $segID (@prodSegIDs)
  {
    my %memHash = DSRseg_get_segment_membership_hash($kapDB, $dsSchema, "p", $segID);
    push(@prodSegs, \%memHash);
  }
  foreach $segID (@geoSegIDs)
  {
    my %memHash = DSRseg_get_segment_membership_hash($kapDB, $dsSchema, "g", $segID);
    push(@geoSegs, \%memHash);
  }
  foreach $segID (@timeSegIDs)
  {
    my %memHash = DSRseg_get_segment_membership_hash($kapDB, $dsSchema, "t", $segID);
    push(@timeSegs, \%memHash);
  }

  #output the CSV file's header line
  print OUTPUT "Product,Geography,Time Period,";
  foreach $measureID (sort {$a<=>$b} keys %measNameHash)
  {
    if ($measureID =~ m/^\d+$/)
    {
      print OUTPUT "\"$measNameHash{$measureID}\",";
    }
  }
  foreach $segID (@prodSegIDs)
  {
    $segID = "SEG_" . $segID;
    print OUTPUT "\"PSEG:$prodNameHash{$segID}\",";
  }
  foreach $segID (@geoSegIDs)
  {
    $segID = "SEG_" . $segID;
    print OUTPUT "\"GSEG:$geoNameHash{$segID}\",";
  }
  foreach $segID (@timeSegIDs)
  {
    $segID = "SEG_" . $segID;
    print OUTPUT "\"TSEG:$timeNameHash{$segID}\",";
  }
  if (defined($upcID))
  {
    print OUTPUT "PATTR:UPC,";
  }

  print OUTPUT "\n";

  #get the list of measures that are in the table
  $measSubQ = "";
  $query = "SELECT ID FROM $dsSchema.measures ORDER BY ID";
  $dbOutput = $kapDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($kapDB, $status, $query);
  while (($measID) = $dbOutput->fetchrow_array)
  {
    $measSubQ .= "measure_" . $measID . ",";
  }
  chop($measSubQ);

  #get the number of rows we're going to output for status display
  $query = "SELECT count(*) FROM $dsSchema.facts";
  $dbOutput = $kapDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($kapDB, $status, $query);
  ($totalRows) = $dbOutput->fetchrow_array;

  $curPos = 0;
  $status = 1;
  $rowsDone = 0;
  while ($status > 0)
  {
    $start = $curPos + 1;

    #grab every row from the data source's facts table
    $query = "SELECT productID, geographyID, timeID, $measSubQ
        FROM $dsSchema.facts LIMIT $start, 100000";
    $dbOutput = $kapDB->prepare($query);
    $status = $dbOutput->execute;
    PrepUtils_handle_db_err($kapDB, $status, $query);

    #run through every line from the facts table, and output as CSV
    while (@rowData = $dbOutput->fetchrow_array)
    {
      if (($rowsDone % 5000) == 0)
      {
        $pct = ($rowsDone / $totalRows) * 100;
        $pct = int($pct);
        $opInfo = "$pct|Transferring data";
        $q_opInfo = $prepDB->quote($opInfo);
        $query = "UPDATE prep.jobs SET opInfo=$q_opInfo WHERE ID=$jobID";
        $status = $prepDB->do($query);
        PrepUtils_handle_db_err($prepDB, $status, $query);
        PrepUtils_set_job_op_pct($prepDB, $jobID, $pct);
      }

      #convert dimension IDs to human-readable text
      $prodID = $rowData[0];
      $geoID = $rowData[1];
      $timeID = $rowData[2];
      $rowData[0] = "\"$prodNameHash{$rowData[0]}\"";
      $rowData[1] = "\"$geoNameHash{$rowData[1]}\"";
      $rowData[2] = "\"$timeNameHash{$rowData[2]}\"";

      #write out the measure data
      foreach $data (@rowData)
      {
        print OUTPUT "$data,";
      }

      #output segment name for each segmentation
      $idx = 0;
      foreach $segID (@prodSegIDs)
      {
        $segmentID = $prodSegs[$idx]->{$prodID};
        $segmentID = "SMT_" . $segmentID;
        print OUTPUT "\"$prodNameHash{$segmentID}\",";
        $idx++;
      }

      $idx = 0;
      foreach $segID (@geoSegIDs)
      {
        $segmentID = $geoSegs[$idx]->{$geoID};
        $segmentID = "SMT_" . $segmentID;
        print OUTPUT "\"$geoNameHash{$segmentID}\",";
        $idx++;
      }

      $idx = 0;
      foreach $segID (@timeSegIDs)
      {
        $segmentID = $timeSegs[$idx]->{$timeID};
        $segmentID = "SMT_" . $segmentID;
        print OUTPUT "\"$timeNameHash{$segmentID}\",";
        $idx++;
      }

      if (defined($upcID))
      {
        print OUTPUT "$upcHash{$prodID},";
      }

      print OUTPUT "\n";

      $rowsDone++;
    }

    $curPos = $curPos + 100_000;
  }

  close(OUTPUT);

  $query = "UPDATE prep.jobs
      SET PID=NULL, opInfo='DONE|Transferring data', lastAction=NOW()
      WHERE ID=$jobID";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");
  PrepUtils_set_job_op_details($prepDB, $jobID, "");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "");
}



#-------------------------------------------------------------------------
#
# Get the size and modification date of the specified URL, if possible.
# (Used by scheduled data flow runs to see if we really need to run the
# flow again.)
#

sub prep_source_web_newdata
{
  my ($query, $dbOutput, $status, $url, $ua, $resp, $fileSize);
  my ($lastModified);

  my ($prepDB, $flowID) = @_;


  #grab the URL to be checked
  $query = "SELECT sourceInfo FROM prep.flows WHERE ID=$flowID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($url) = $dbOutput->fetchrow_array;
  $url =~ m/^URL=(.*)$/;
  $url = $1;

  #check URL size and date using LWP
  $ua = LWP::UserAgent->new();
  $resp = $ua->head($url);

  #if we hit an error
  $code = $resp->code;
  if ($resp->code != 200)
  {
    return;
  }

  $fileSize = $resp->content_length;
  $lastModified = $resp->last_modified;

  #turn the last-modified date into an SQL-style date
  $query = "SELECT FROM_UNIXTIME($lastModified) FROM prep.schedule";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($lastModified) = $dbOutput->fetchrow_array;

  return($fileSize, $lastModified);
}



#-------------------------------------------------------------------------
#
# Get the size and modification date of the specified FTP file, if possible.
# (Used by scheduled data flow runs to see if we really need to run the
# flow again.)
#

sub prep_source_ftp_newdata
{
  my ($query, $dbOutput, $status, $ftpInfo, $ftpserver, $ftpuser, $filename);
  my ($ftppass, $ftppath, $tag, $uri, $ua, $resp, $lastModified, $fileSize);
  my ($filepath);

  my ($prepDB, $flowID) = @_;


  #grab the info needed to perform the FTP download
  $query = "SELECT sourceInfo FROM prep.flows WHERE ID=$flowID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($ftpInfo) = $dbOutput->fetchrow_array;
  $ftpInfo =~ m/^FTP=(.*?)\|USER=(.*?)\|PASS=(.*?)\|PATH=(.*)$/;
  $ftpserver = $1;
  $ftpuser = $2;
  $ftppass = $3;
  $ftppath = $4;

  #handle any special tag the user might be using for the FTP server
  $ftpserver = lc($ftpserver);
  $tag = $Lib::KoalaConfig::cloudname . "-" . $ftpserver;
  if (length($customSystems{$tag}) > 1)
  {
    $ftpserver = $customSystems{$tag};
  }

  #build fully-qualified file name to request from FTP server
  if ($ftppath =~ m/(.*\/)(.*)$/)
  {
    $filepath = $1;
    $filename = $2;
   }
  else
  {
    $filepath = "";
    $filename = $ftppath;
  }

  #build up FTP URI
  $uri = "ftp://$ftpuser:$ftppass\@$ftpserver/$ftppath";

  #check URL size and date using LWP
  $ua = LWP::UserAgent->new();
  $resp = $ua->head($uri);

  #if we hit an error
  $code = $resp->code;
  $fileSize = $resp->content_length;
  $lastModified = $resp->last_modified;

  #there are some really shitty FTP-as-a-service providers that don't
  #have correct implementations and do weird things, but we can
  #still get a valid Last-Modified value from despite the error.
  #If that's the case, set what we can and dummy up the rest
  if (($code == 500) && ($lastModified > 10))
  {
    $code = 200;
    $fileSize = 1;
  }

  if ($code != 200)
  {
    return;
  }

  #turn the last-modified date into an SQL-style date
  $query = "SELECT FROM_UNIXTIME($lastModified) FROM prep.schedule";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($lastModified) = $dbOutput->fetchrow_array;

  return($fileSize, $lastModified);
}



#-------------------------------------------------------------------------
#
# Get the modification date of the specified Koala data source.
# (Used by scheduled data flow runs to see if we really need to run the
# flow again.)
#
# NB: The file size is always 0, since the last modified date is all we need
#     to be sure a Koala DS hasn't changed underneath us.
#

sub prep_source_koala_newdata
{
  my ($query, $dbOutput, $status, $lastModified, $dsID, $sourceInfo);

  my ($kapDB, $prepDB, $flowID) = @_;


  #grab the info needed to perform the check
  $query = "SELECT sourceInfo FROM prep.flows WHERE ID=$flowID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($sourceInfo) = $dbOutput->fetchrow_array;

  $sourceInfo =~ m/^DSID=(.*)$/;
  $dsID = $1;

  $query = "SELECT lastModified FROM app.dataSources WHERE ID=$dsID";
  $dbOutput = $kapDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($lastModified) = $dbOutput->fetchrow_array;

  return(0, $lastModified);
}



#-------------------------------------------------------------------------


1;
