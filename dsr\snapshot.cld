#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #get CGI parameters
  $dsID = $q->param('ds');
  $userID = $q->param('user');

  #split the copy process off into a background process
  $SIG{CHLD} = "IGNORE";
  if ($pid = fork())
  {
    #parent process - print out basic HTML OK message

    print("Content-type: text/html\n\n");
    print("OK\n");
  }

  else
  {
    #child process - do the actual snapshot work

    #we're in a new process, so we need a new connection to the database
    $db = KAPutil_connect_to_database();

    #redirect STDERR to the Koala error log
    close(STDERR);
    open(STDERR, ">>/opt/apache/htdocs/tmp/koala_error.log");

    #let Apache know not to wait on the child process
    close(STDIN);
    close(STDOUT);

    #determine if we're using an external table space for capacity/IO reasons
    $query = "SELECT userID FROM app.dataSources WHERE ID=$dsID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    ($ownerID) = $dbOutput->fetchrow_array;

    $orgID = KAPutil_get_user_org_id($db, $ownerID);

    $query = "SELECT dataStorage FROM app.orgs WHERE ID=$orgID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    ($dataDir) = $dbOutput->fetchrow_array;
    if (length($dataDir) > 2)
    {
      $dataDir = $dataDir . "/logs/";
      chdir("$dataDir");
    }
    else
    {
      $dataDir = "";
      chdir("/opt/apache/app/logs/");
    }

    $dbName = "datasource_" . $dsID;
    $dsSchema = $dbName;
    $fileStub = $dbName . "_" . time();
    $sqlFileName = "$fileStub.sql";
    $zipFileName = "$fileStub.zip";

    #if we're an automated tool (Data Prep), and the last thing that updated
    #the data source was also an automated tool, and it happened in the last
    #12 hours... then don't actually create a snapshot for performance and
    #storage space reasons
    if ($userID == 0)
    {
      $query = "SELECT userID, UNIX_TIMESTAMP(NOW()) - UNIX_TIMESTAMP(timestamp) \
          FROM $dsSchema.update_history ORDER BY ID DESC LIMIT 1";
      $dbOutput = $db->prepare($query);
      $dbOutput->execute;
      ($lastUserID, $lastTimeStamp) = $dbOutput->fetchrow_array;

      $lastTimeStamp = $lastTimeStamp / 3600;

      if (($lastUserID == 0) && ($lastTimeStamp < 12))
      {
        #create the update history entry without a rollback option
        $query = "INSERT INTO $dsSchema.update_history (userID, timestamp) \
            VALUES ($userID, NOW())";
        $status = $db->do($query);

        #let the caller know we're done
        $query = "UPDATE app.jobs SET status='Done backing up data source' \
            WHERE dsID=$dsID AND operation = 'DS-UPDATE'";
        $db->do($query);

        exit;
      }
    }

    #dump out the data source's SQL (includes all cubes depending on the database)
    #NB: we're using a pipeline to directly dump the data into a zip file,
    #    avoiding some I/O required to dump a temp file and then zipping it.
    #    zip won't let us specify a name on the command line, so we're using
    #    zipnote to rewrite the filename inside the zip file after the fact
    `/usr/bin/mysqldump -h$Lib::KoalaConfig::dbServerName -u app -p$Lib::KoalaConfig::password --ignore-table=$dbName.export --ignore-table=$dbName._export $dbName | /usr/bin/zip -q > $zipFileName`;
    `printf "@ -\n@=$sqlFileName\n" | zipnote -w $zipFileName`;

    #write out our update run info and get unique ID (for roll back)
    $zipFileName = $dataDir . $zipFileName;
    $query = "INSERT INTO $dsSchema.update_history \
        (userID, timestamp, filename) VALUES ($userID, NOW(), '$zipFileName')";
    $status = $db->do($query);

    #let the caller know we're done
    $query = "UPDATE app.jobs SET status='Done backing up data source' \
        WHERE dsID=$dsID AND operation = 'DS-UPDATE'";
    $db->do($query);
  }


#EOF
