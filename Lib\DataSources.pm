
package Lib::DataSources;

use lib "/opt/apache/app/";

use Exporter;
use Lib::DSRMeasures;
use Lib::WebUtils;

our @ISA = ('Exporter');

our @EXPORT = qw(&ds_rights
    &ds_rights_hash
    &ds_get_owner_hash
    &ds_list
    &ds_types
    &ds_archive
    &ds_id_to_name
    &ds_get_owner
    &ds_prune_time_periods
    &ds_get_name_hash
    &ds_get_update_hash);



#-------------------------------------------------------------------------------
#
# Handle a database error of some kind during data source utility operations
#

sub ds_db_err
{
  my ($date);

  my ($db, $status, $text) = @_;

  if (!defined($status))
  {
    $date = localtime();
    print STDERR "$date: $text\n";
    if ($db->errstr =~ m/^MySQL server has gone away/)
    {
      die("Lost connection to database, terminating");
    }
  }
}



#-------------------------------------------------------------------------------
#
# Return an array of data source IDs that the specified user has some level
# of privilege for (used to display lists of data sources in UI)
#

sub ds_list
{
  my ($query, $dbOutput, $privs, $ownerID, $Rusers, $RWusers, $ruser, $dsID);
  my ($status);
  my (@userSources, @rusers, @rwusers);
  my (%seen);

  my ($db, $userID, $acctType, $privType) = @_;

  undef(@userSources);

  #data sources owned by the user are first on the list
  $query = "SELECT ID FROM dataSources WHERE deleted=0 AND userID=$userID \
      ORDER BY name";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ds_db_err($db, $status, $query);

  while (($dsID) = $dbOutput->fetchrow_array)
  {
    push(@userSources, $dsID);
  }

  #get the list of data sources stored on the system, with priv info
  $query = "SELECT ID, userID, Rusers, RWusers FROM dataSources WHERE deleted=0 \
      ORDER BY name";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ds_db_err($db, $status, $query);

  #run through the list of data sources
  while (($dsID, $ownerID, $Rusers, $RWusers) = $dbOutput->fetchrow_array)
  {

    #split the user lists into arrays, and combine
    if ($privType eq "W")
    {
      undef(@rusers);
    }
    else
    {
      @rusers = split(',', $Rusers);
    }
    @rwusers = split(',', $RWusers);
    push(@rusers, @rwusers);

    #push the data source owner's ID onto the combined array
    push(@rusers, $ownerID);

    #see if the user has privs, and add the data source ID to the returned
    #array if so
    foreach $ruser (@rusers)
    {
      if (($ruser == $userID) || ($acctType > 4))
      {
        push(@userSources, $dsID);
      }
    }
  }

  #unique-ify the list of data sources
  @userSources = grep { !$seen{$_}++ } @userSources;

  return(@userSources);
}



#-------------------------------------------------------------------------------
#
# Build an array of all data source types on the current cloud, including
# defaults, sorted by name
#

sub ds_types
{
  my ($query, $dbOutput, $status, $type);
  my (%tmp);
  my (@dsTypes);

  my ($db) = @_;

  undef(@dsTypes);

  #set our pre-defined types that everybody gets
  $tmp{'IRI'} = 1;
  $tmp{'Nielsen'} = 1;
  $tmp{'Nielsen Answers'} = 1;
  $tmp{'POS'} = 1;
  $tmp{'RetailLink'} = 1;
  $tmp{'Shipping'} = 1;
  $tmp{'SPINS'} = 1;

  #get all types defined in the customer cloud instance
  $query = "SELECT DISTINCT type FROM dataSources";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ds_db_err($db, $status, $query);

  while (($type) = $dbOutput->fetchrow_array)
  {
    $tmp{$type} = 1;
  }

  #convert the hash to an alpha-sorted array
  foreach $type (sort keys %tmp)
  {
    push(@dsTypes, $type);
  }

  return(@dsTypes);
}



#-------------------------------------------------------------------------------
#
# Determine what rights the specified user has to the specified data source.
# Returns a single character: "N" for no rights, "R" for read-only, "W" for
# write.
#

sub ds_rights
{
  my ($query, $dbOutput, $privs, $ownerID, $Rusers, $RWusers, $ruser, $rwuser);
  my ($techSupport, $userEmail, $status);
  my (@rusers, @rwusers);

  my ($db, $userID, $dsID, $acctType) = @_;

  #admins can access everything
  if ($acctType > 4)
  {
    return("W");
  }

  #get the list of read and read/write users for the specified data source
  $query = "SELECT userID, Rusers, RWusers FROM dataSources WHERE ID=$dsID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ds_db_err($db, $status, $query);
  ($ownerID, $Rusers, $RWusers) = $dbOutput->fetchrow_array;

  #if the user owns the data source, they have full rights
  if ($userID == $ownerID)
  {
    return("W");
  }

  #split the user lists into arrays
  @rusers = split(',', $Rusers);
  @rwusers = split(',', $RWusers);

  #see if the user has read/write privs
  foreach $rwuser (@rwusers)
  {
    if ($rwuser == $userID)
    {
      return("W");
    }
  }

  #see if the user has read privs
  foreach $ruser (@rusers)
  {
    if ($ruser == $userID)
    {
      return("R");
    }
  }

  #if we made it this far, the user has no privs on the data source
  return("N");
}



#-------------------------------------------------------------------------------
#
# Create a hash of the rights the specified user has on every data source on
# the cloud. (We're doing this to avoid repeatedly calling ds_rights, which
# results in a storm of tiny SQL queries instead of one small query.)
#

sub ds_rights_hash
{
  my ($query, $dbOutput, $privs, $ownerID, $Rusers, $RWusers, $ruser, $rwuser);
  my ($status, $dsID);
  my (@rusers, @rwusers);
  my (%rightsHash);

  my ($db, $userID, $acctType) = @_;

  #get the list of read and read/write users for the specified data source
  $query = "SELECT ID, userID, Rusers, RWusers FROM dataSources WHERE ID=$dsID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ds_db_err($db, $status, $query);
  while (($dsID, $ownerID, $Rusers, $RWusers) = $dbOutput->fetchrow_array)
  {

    #admins can access everything
    if ($acctType > 4)
    {
      $rightsHash{$dsID} = "W";
      next;
    }

    #if the user owns the data source, they have full rights
    if ($userID == $ownerID)
    {
      $rightsHash{$dsID} = "W";
      next;
    }

    #split the user lists into arrays
    @rusers = split(',', $Rusers);
    @rwusers = split(',', $RWusers);

    #see if the user has read privs
    foreach $ruser (@rusers)
    {
      if ($ruser == $userID)
      {
        $rightsHash{$dsID} = "R";
      }
    }

    #see if the user has write privs
    foreach $rwuser (@rwusers)
    {
      if ($rwuser == $userID)
      {
        $rightsHash{$dsID} = "W";
      }
    }

    #if the user has no privs on the data source
    if (!defined($rightsHash{$dsID}))
    {
      $rightsHash{$dsID} = "N";
    }
  }

  return(%rightsHash);
}



#-------------------------------------------------------------------------------
#
# Build and return a hash of data source owners, hashed by ID
#

sub ds_get_owner_hash
{
  my ($query, $dbOutput, $status, $id, $ownerID);
  my (%dsOwners);

  my ($db) = @_;

  #grab all data sources on the system, and their owners
  $query = "SELECT ID, userID FROM app.dataSources";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ds_db_err($db, $status, $query);

  while (($id, $ownerID) = $dbOutput->fetchrow_array)
  {
    $dsOwners{$id} = $ownerID;
  }

  return(%dsOwners);
}



#-------------------------------------------------------------------------------
#
# Create an archive of the specified data source (basically, a zipped and
# encrypted dump of the data source and its related lines from the cubes,
# reports, and dataSources tables).
#

sub ds_archive
{
  my ($dbName, $sqlFileName, $zipFileName);

  my ($db, $dsID) = @_;

  chdir("/opt/apache/app/logs/");

  $dbName = "datasource_" . $dsID;
  $sqlFileName = "$dbName.sql";
  $zipFileName = "$dbName.zip";

  #dump out the data source's SQL (includes all cubes depending on the database)
  `/usr/bin/mysqldump -h$Lib::KoalaConfig::dbServerName -u app -p$Lib::KoalaConfig::password --ignore-table=$dbName.export --ignore-table=$dbName._export $dbName > $sqlFileName`;

  #compress the dump
  `/usr/bin/zip $zipFileName $sqlFileName`;

  #remove the old uncompressed file
  unlink($sqlFileName);
}



#-------------------------------------------------------------------------------
#
# Build and return a hash of all data source names, hashed by ID
#

sub ds_get_name_hash
{
  my ($query, $dbOutput, $id, $name, $status);
  my (%dsNames);
  my ($db) =@_;

  undef(%dsNames);

  #add user names to hash
  $query = "SELECT ID, name FROM dataSources WHERE deleted = 0";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ds_db_err($db, $status, $query);

  while (($id, $name) = $dbOutput->fetchrow_array)
  {
    $dsNames{$id} = $name;
  }

  return(%dsNames);
}



#-------------------------------------------------------------------------------
#
# If the specified data source has a "prune time periods" setting, apply it
#

sub ds_prune_time_periods
{
  my ($query, $dbOutput, $status, $timePruning, $duration, $type, $timeIDstr);
  my ($mostRecentTime, $cutoffTime, $removedData, $dsSchema, $timeID, $name);
  my ($timePruningAdvanced, $count, $deleteStr);

  my ($db, $dsID) = @_;

  $dsSchema = "datasource_" . $dsID;
  $removedData = 0;

  #get the time period pruning settings for the specified data source
  $query = "SELECT timePruning, timePruningAdvanced FROM app.dataSources WHERE ID=$dsID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ds_db_err($db, $status, $query);
  ($timePruning, $timePruningAdvanced) = $dbOutput->fetchrow_array;

  #parse out the duration and type of pruning
  if ($timePruning =~ m/^(\d+) (.*?)$/)
  {
    $duration = $1;
    $type = $2;

    #convert the CPG date type to an SQL date type
    chop($type);

    #start by determining the most recent time period in the data source
    $query = "SELECT endDate FROM $dsSchema.timeperiods ORDER BY endDate DESC LIMIT 1";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    ds_db_err($db, $status, $query);
    ($mostRecentTime) = $dbOutput->fetchrow_array;

    #now go back the specified amount of time
    $query = "SELECT DATE_SUB('$mostRecentTime', INTERVAL $duration $type) \
        FROM $dsSchema.timeperiods";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    ds_db_err($db, $status, $query);
    ($cutoffTime) = $dbOutput->fetchrow_array;

    #build up the query to find time periods older than our retention setting
    $query = "SELECT ID, name FROM $dsSchema.timeperiods \
        WHERE endDate <= '$cutoffTime' AND endDate NOT LIKE '0000-00-00 %'";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    ds_db_err($db, $status, $query);

    #run through the returned time IDs, and remove data associated with them
    while (($timeID, $name) = $dbOutput->fetchrow_array)
    {
      $timeIDstr .= "$timeID,";
    }
    chop($timeIDstr);

    if (length($timeIDstr) > 0)
    {
      $removedData = 1;

      $query = "DELETE FROM $dsSchema.timeperiods WHERE ID IN ($timeIDstr)";
      $status = $db->do($query);
      ds_db_err($db, $status, $query);

      $query = "DELETE FROM $dsSchema.facts WHERE timeID IN ($timeIDstr)";
      $status = $db->do($query);
      ds_db_err($db, $status, $query);
    }
  }

  #perform "advanced" time pruning, if analyst wants us to
  if (length($timePruningAdvanced) > 0)
  {
    @timePruningCmds = split(/\|/, $timePruningAdvanced);
    foreach $timePruningCmd (@timePruningCmds)
    {
      if ($timePruningCmd =~ m/^(\d+) (\d+) (\d+)$/)
      {
        $type = $1;
        $duration = $2;
        $count = $3;

        #get the list of time period IDs to delete for the type/duration combo
        #NB: it looks like a kludge to me, but MySQL recommends using a large
        #   number for the limit to get all rows from the offset until the end
        #   of the table - hence the 2048 limit in the below SQL
        $query = "SELECT ID FROM $dsSchema.timeperiods
            WHERE type=$type AND duration=$duration
            ORDER BY endDate DESC LIMIT $count, 2048";
        $dbOutput = $db->prepare($query);
        $status = $dbOutput->execute;
        ds_db_err($db, $status, $query);
        while (($id) = $dbOutput->fetchrow_array)
        {
          $deleteStr .= "$id,";
        }
        chop($deleteStr);

        if (length($deleteStr) > 0)
        {
          $query = "DELETE FROM $dsSchema.timeperiods WHERE ID IN ($deleteStr)";
          $status = $db->do($query);
          ds_db_err($db, $status, $query);

          $query = "DELETE FROM $dsSchema.facts WHERE timeID IN ($deleteStr)";
          $status = $db->do($query);
          ds_db_err($db, $status, $query);
        }
      }
    }
  }

}



#-------------------------------------------------------------------------------
#
# Return the text name of the data source with the supplied ID
#

sub ds_id_to_name
{
  my ($query, $dbOutput, $name, $status);

  my ($db, $dsID) = @_;

  #handle invalid arguments
  if ($dsID < 1)
  {
    return("");
  }

  $query = "SELECT name FROM dataSources WHERE ID=$dsID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ds_db_err($db, $status, $query);
  ($name) = $dbOutput->fetchrow_array;

  return($name);
}



#-------------------------------------------------------------------------------
#
# Return the userID of the owner of the specified data source.
#

sub ds_get_owner
{
  my ($query, $dbOutput, $status, $ownerID);

  my ($db, $dsID) = @_;

  #handle invalid arguments
  if ($dsID < 1)
  {
    return;
  }

  $query = "SELECT userID FROM dataSources WHERE ID=$dsID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ds_db_err($db, $status, $query);
  ($ownerID) = $dbOutput->fetchrow_array;

  return($ownerID);
}



#-------------------------------------------------------------------------------
#
# Build & return a hash of the update times for each data source, keyed by ID
#

sub ds_get_update_hash
{
  my ($query, $dbOutput, $id, $update, $status);
  my (%dsUpdateHash);
  my ($db) = @_;

  $query = "SELECT ID, UNIX_TIMESTAMP(lastUpdate) FROM app.dataSources";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ds_db_err($db, $status, $query);

  while (($id, $update) = $dbOutput->fetchrow_array)
  {
    $dsUpdateHash{$id} = $update;
  }

  return(%dsUpdateHash);
}


#-------------------------------------------------------------------------------



1;
