#!/usr/bin/perl

use lib "/opt/apache/app/";

use DBI;

use Lib::KoalaConfig;
use Lib::PrepFlows;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::PrepUtils;
use Lib::WebUtils;


#
# Output per-broker and per-user data flow counts (schedule and not).
#


  #connect to the database
  $db = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  %prepFlowNameHash = prep_flow_get_name_hash($prepDB);
  %dsNameHash = ds_get_name_hash($db);

  #find every flow that isn't attached to a DS and hasn't been used in 14 days
#  $query = "SELECT ID, dsID FROM prep.flows WHERE DATE_SUB(NOW(), INTERVAL 14 DAY) > lastRun ORDER BY name";
  $query = "SELECT ID, dsID FROM prep.flows WHERE isnull(lastRun) ORDER BY name";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  while (($flowID, $dsID) = $dbOutput->fetchrow_array)
  {
    if (length($dsNameHash{$dsID}) < 1)
    {
      print "Deleting $prepFlowNameHash{$flowID}\n";

  prep_audit($prepDB, 20, "Clean up process deleted data flow $flowName", $flowID);

  #remove any files associated with the data flow
  #NB: the nightly cleanup job will grab any actual leftover data
  $query = "DELETE FROM prep.files WHERE flowID=$flowID";
  $prepDB->do($query);

  #remove any file types associated with the data flow
  $query = "DELETE FROM prep.file_types WHERE flowID=$flowID";
  $prepDB->do($query);

  #remove any validation steps
  $query = "DELETE FROM prep.validation WHERE flowID=$flowID";
  $prepDB->do($query);

  #remove any recipe steps
  $query = "DELETE FROM prep.recipes WHERE flowID=$flowID";
  $prepDB->do($query);

  #remove any scheduling associated with the flow
  $query = "DELETE FROM prep.schedule WHERE flowID=$flowID";
  $prepDB->do($query);

  #remove any scheduling associated with the flow
  $query = "DELETE FROM prep.trim_values WHERE flowID=$flowID";
  $prepDB->do($query);

  #remove any jobs associated with the data flow
  $query = "SELECT ID FROM prep.jobs WHERE flowID=$flowID";
  $dbOutput1 = $prepDB->prepare($query);
  $dbOutput1->execute;
  while (($jobID) = $dbOutput1->fetchrow_array)
  {
    prep_job_clear($prepDB, $flowID, $jobID);
  }

  #remove the data flow
  $query = "DELETE FROM prep.flows WHERE ID=$flowID";
  $prepDB->do($query);

    }
  }


#EOF
