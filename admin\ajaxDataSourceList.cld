#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dispRpt = $q->param('r');
  $admin = $q->param('a');

  #by default, display size report
  if (length($dispRpt) < 1)
  {
    $dispRpt = "size";
  }

  #make sure it's really an admin requesting admin mode
  if ($acctType < 5)
  {
    $admin = 0;
  }

  print("Expires: -1\n");
  print("Cache-Control: private, max-age=0");
  print("Content-type: application/json\n\n");

  #get the list of the user's data sources and output them
  $db = KAPutil_connect_to_database();

  @userSources = ds_list($db, $userID, $acctType);
  $sources = join(',', @userSources);

  #if we're not in admin mode, only show the current user's data sources
  if ($admin < 1)
  {
    $userFilter = "userID=$userID AND";
  }
  else
  {
    $userFilter = "";
  }

  print("[\n");

  ############### No Updates in 3 Months ###############

  if ($dispRpt eq "noupdate3")
  {
    $query = "SELECT ID, name, type, lastUpdate, lastModified, userID \
        FROM dataSources \
        WHERE $userFilter deleted = 0 AND DATE_SUB(NOW(), INTERVAL 3 MONTH) > lastUpdate \
        ORDER BY lastUpdate";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);

    $count = 1;
    while (($dsID, $name, $type, $lastUpdate, $lastModified, $dsUserID) = $dbOutput->fetchrow_array)
    {

      $dsUserID = utils_userID_to_name($db, $dsUserID);
      $dsSchema = "datasource_" . $dsID;

      #get the various sizes for the data sources
      $query = "SELECT SUM(data_length), SUM(index_length) FROM information_schema.TABLES WHERE information_schema.TABLES.table_schema = '$dsSchema'";
      $dbOutput1 = $db->prepare($query);
      $status1 = $dbOutput1->execute;
      KAPutil_handle_db_err($db, $status1, $query);
      ($sizeData, $sizeIndex) = $dbOutput1->fetchrow_array;
      $totalSize = $sizeData + $sizeIndex;

      #convert size to GB
      $totalSize = $totalSize / 1_000_000;
      $totalSize = sprintf("%.2f", $totalSize);

      print <<JSON_LABEL;
 {
  "ID": $dsID,
  "Name": "$name",
  "Type": "$type",
  "Last Update": "$lastUpdate",
  "Last Modified": "$lastModified",
  "Owner": "$dsUserID",
  "Size (MB)": "$totalSize"
 }
JSON_LABEL

      if ($count < $status)
      {
        print(",");
      }

      $count++;
    }
  }


  ############### Unused ###############

  if ($dispRpt eq "unused")
  {
    $jsonStr = "";

    $query = "SELECT ID, name, type, lastUpdate, userID FROM dataSources \
        WHERE $userFilter deleted = 0 AND ODBCexport < 1 ORDER BY name";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);

    $count = 1;
    while (($dsID, $name, $type, $lastUpdate, $dsUserID) = $dbOutput->fetchrow_array)
    {
      $dsUserID = utils_userID_to_name($db, $dsUserID);
      $dsSchema = "datasource_" . $dsID;

      $query = "SELECT COUNT(table_name) FROM information_schema.tables \
          WHERE table_schema = '$dsSchema' AND table_name LIKE '_rpt%'";
      $dbOutput1 = $db->prepare($query);
      $status1 = $dbOutput1->execute;
      ($dsRptCount) = $dbOutput1->fetchrow_array;
      KAPutil_handle_db_err($db, $status1, $query);

      if ($dsRptCount < 1)
      {

        #get the various sizes for the data sources
        $query = "SELECT SUM(data_length), SUM(index_length) \
            FROM information_schema.TABLES \
            WHERE information_schema.TABLES.table_schema = '$dsSchema'";
        $dbOutput1 = $db->prepare($query);
        $status1 = $dbOutput1->execute;
        KAPutil_handle_db_err($db, $status1, $query);
        ($sizeData, $sizeIndex) = $dbOutput1->fetchrow_array;
        $totalSize = $sizeData + $sizeIndex;

        #convert size to GB
        $totalSize = $totalSize / 1_000_000;
        $totalSize = sprintf("%.2f", $totalSize);

        $jsonStr = $jsonStr . " \
 { \
  \"ID\": $dsID, \
  \"Name\": \"$name\", \
  \"Type\": \"$type\", \
  \"Last Update\": \"$lastUpdate\", \
  \"Exported\": \"No\", \
  \"Reports\": \"0\", \
  \"Size (MB)\": \"$totalSize\", \
  \"Owner\": \"$dsUserID\" \
 },\n";
      }

      $count++;
    }

    chop($jsonStr);	chop($jsonStr);
    print("$jsonStr");
  }


  ############### Size ###############

  if ($dispRpt eq "size")
  {
    $query = "SELECT ID, name, type, lastUpdate, userID, description, ODBCexport, ODBCmanual, ODBCstatus, UNIX_TIMESTAMP(ODBCexported), UNIX_TIMESTAMP(lastModified) \
        FROM dataSources WHERE $userFilter deleted = 0 ORDER BY name";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);

    $count = 1;
    while (($dsID, $name, $type, $lastUpdate, $dsUserID, $description, $ODBCexport, $ODBCmanual, $ODBCstatus, $ODBCexported, $lastModified) = $dbOutput->fetchrow_array)
    {
      $dsUserID = utils_userID_to_name($db, $dsUserID);
      $dsSchema = "datasource_" . $dsID;

      #get the various sizes for the data sources
      $query = "SELECT SUM(data_length), SUM(index_length), SUM(data_free) \
          FROM information_schema.TABLES \
          WHERE information_schema.TABLES.table_schema = '$dsSchema'";
      $dbOutput1 = $db->prepare($query);
      $status1 = $dbOutput1->execute;
      KAPutil_handle_db_err($db, $status1, $query);
      ($sizeData, $sizeIndex, $sizeDataFree) = $dbOutput1->fetchrow_array;
      $totalSize = $sizeData + $sizeIndex + $sizeDataFree;

      $query = "SELECT data_length, index_length, data_free FROM information_schema.TABLES \
          WHERE information_schema.TABLES.table_schema = '$dsSchema' AND information_schema.TABLES.table_name = 'facts'";
      $dbOutput1 = $db->prepare($query);
      $status1 = $dbOutput1->execute;
      KAPutil_handle_db_err($db, $status1, $query);
      ($sizeData, $sizeIndex, $sizeDataFree) = $dbOutput1->fetchrow_array;
      $dataSize = $sizeData + $sizeIndex + $sizeDataFree;

      $query = "SELECT SUM(data_length), SUM(index_length), SUM(data_free) \
          FROM information_schema.TABLES \
          WHERE information_schema.TABLES.table_schema = '$dsSchema' AND information_schema.TABLES.table_name LIKE '_rptcube_%'";
      $dbOutput1 = $db->prepare($query);
      $status1 = $dbOutput1->execute;
      KAPutil_handle_db_err($db, $status1, $query);
      ($sizeData, $sizeIndex, $sizeDataFree) = $dbOutput1->fetchrow_array;
      $rptSize = $sizeData + $sizeIndex + $sizeDataFree;

      $query = "SELECT data_length, index_length, data_free FROM information_schema.TABLES \
          WHERE information_schema.TABLES.table_schema = '$dsSchema' AND information_schema.TABLES.table_name LIKE 'export%'";
      $dbOutput1 = $db->prepare($query);
      $status1 = $dbOutput1->execute;
      KAPutil_handle_db_err($db, $status1, $query);
      ($sizeData, $sizeIndex, $sizeDataFree) = $dbOutput1->fetchrow_array;
      $ODBCsize = $sizeData + $sizeIndex + $sizeDataFree;

      #convert size to GB
      $totalSize = $totalSize / 1_000_000;
      $totalSize = sprintf("%.2f", $totalSize);
      $dataSize = $dataSize / 1_000_000;
      $dataSize = sprintf("%.2f", $dataSize);
      $rptSize = $rptSize / 1_000_000;
      $rptSize = sprintf("%.2f", $rptSize);
      $ODBCsize = $ODBCsize / 1_000_000;
      $ODBCsize = sprintf("%.2f", $ODBCsize);

      print <<JSON_LABEL;
 {
  "ID": $dsID,
  "Name": "$name",
  "Type": "$type",
  "Last Update": "$lastUpdate",
  "Owner": "$dsUserID",
  "Description": "$description",
  "Total Size (MB)": "$totalSize",
  "Data Size (MB)": "$dataSize",
  "Reports Size (MB)": "$rptSize",
  "ODBC Size (MB)": "$ODBCsize"
 }
JSON_LABEL

      if ($count < $status)
      {
        print(",");
      }

      $count++;
    }
  }

  print("]\n");

#EOF
