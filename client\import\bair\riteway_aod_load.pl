#!/usr/bin/perl

use Text::CSV;

#load ESM's custom AOD download format


  #instantiate our CSV object
  $csv = Text::CSV->new( {binary => 1} );

  open(INPUT, "$ARGV[0]");
  open(OUTPUT, ">$ARGV[1]");

  #burn first line of file if it contains the "Edit Data Selection" tag
  $line = <INPUT>;
  if ($line =~ m/^Edit Data/)
  {
    $line = <INPUT>;
  }

  #parse the first header line (contains geo and time info)
  #Market : ShopRite Total TA • Period : Latest 52 Wks - W/E 08/12/17 • Product
  $line =~ m/^.*? : (.*?) • /;
  $geography = $1;

  $line =~ m/ Period : (.*?) • /;
  $time = $1;

  if (length($geography) < 2)
  {
    $geography = "UNKNOWN";
  }
  if (length($time) < 2)
  {
    $time = "UNKNOWN";
  }

  #parse the primary header line (2nd line in extracted CSV)
  $line = <INPUT>;
  $csv->parse($line);
  @columns = $csv->fields();

  $columns[0] = "Product";

  #push our static columns out to the front of the headers array
  @tmp = ('Geo', 'Time', 'UPC');
  push(@tmp, @columns);

  #output the headers
  $csv->combine(@tmp);
  $line = $csv->string();
  print OUTPUT "$line\n";

  while ($line = <INPUT>)
  {

    $csv->parse($line);
    @columns = $csv->fields();

    #extract the UPC from the end of the product name
    $product = $columns[0];
    $upc = "";
    if ($product =~ m/^.* (\d+)$/)
    {
      $upc = $1;
    }

    #skip rows without UPCs
    if (length($upc) < 9)
    {
      next;
    }


    @tmp = ($geography, $time, $upc);
    push(@tmp, @columns);

    $csv->combine(@tmp);
    $line = $csv->string();

    print OUTPUT "$line\n";

  }
