#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::Reports;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName Reports</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<SCRIPT SRC="/jsgrid/jsgrid.min.js"></SCRIPT>
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/jsgrid/jsgrid.min.css" REL="stylesheet">
<LINK HREF="/jsgrid/jsgrid-theme.min.css" REL="stylesheet">

<STYLE>
div.grid th
{
  background: #e2e3e5 !important;
}

.selected-row > td
{
  background: #c4e2ff !important;
  border-color: #c4e2ff;
}
</STYLE>


<SCRIPT>
//global
selectedRpt = 0;
loadURL = "ajaxReportList?dsID=$dsID";

let gridHeight = window.innerHeight - 320;
gridHeight = Math.max(gridHeight, 150);

let statusTimer = setInterval(function(){refreshDisplay()}, 120000);

\$(document).ready(function()
{
  \$("#dsGrid").jsGrid(
  {
    width: "100%",
    height: gridHeight,
    sorting: true,
    autoload: true,
    loadIndication: true,

    controller:
    {
      loadData: function (filter)
      {
        return \$.ajax(
        {
          type: "GET",
          contentType: "application/json; charset=utf-8",
          dataType: "json",
          url: loadURL
        });
      }
    },

    rowClick: function(args)
    {
      /*
      selectedRpt = args.item.ID;

      \$("#dsGrid tr").removeClass("selected-row");

      \$selectedRow = \$(args.event.target).closest("tr");
      \$selectedRow.addClass("selected-row");
*/

      //Shift + selection
      if (args.event.shiftKey)
      {
        document.getSelection().removeAllRanges();

        let i = 0;
        let firstSelection = -1;
        while ((i < this.data.length) && (firstSelection < 0))
        {
          if (this.data[i].selected == 1)
          {
            firstSelection = i;
          }
          i++;
        }

        i = 0;
        let curSelection = -1;
        while ((i < this.data.length) && (curSelection < 0))
        {
          if (args.item.ID == this.data[i].ID)
          {
            curSelection = i;
          }
          i++;
        }

        clearAllSelections();

        let start, stop;
        if (curSelection > firstSelection)
        {
          start = firstSelection;
          end = curSelection;
        }
        else
        {
          end = firstSelection;
          start = curSelection;
        }

        for (i = start; i <= end; i++)
        {
          this.data[i].selected = 1;
          \$selectedRow = \$('#dsGrid').jsGrid('rowByItem',
              this.data[i]).closest('tr');
          \$selectedRow.addClass('selected-row');
        }

      }

      //Ctrl+selection
      else if (event.ctrlKey || event.altKey || event.metaKey)
      {
        args.item.selected = 1;
        \$selectedRow = \$(args.event.target).closest('tr');
        \$selectedRow.addClass('selected-row');
      }

      //single selection
      else
      {
        clearAllSelections();
        args.item.selected = 1;
        \$selectedRow = \$(args.event.target).closest('tr');
        \$selectedRow.addClass('selected-row');
      }

    },

    rowDoubleClick: function(args)
    {
      let selectedRpts = getSelectionStr();
      location.href='display.cld?rpt=' + selectedRpts;
    },

    fields: [
      {name: "ID", type: "number", visible: false},
      {name: "Report", type: "text", width: 365},
      {name: "Last Updated", type: "text", width: 125},
      {name: "Analyst", type: "text", width: 175}
    ]
  });
});



function clearAllSelections()
{
  let grid = \$('#dsGrid').jsGrid('option', 'data');

  for (let i = 0; i < grid.length; i++)
  {
    grid[i].selected = 0;
  }

  \$('#dsGrid tr').removeClass('selected-row');
}



function getSelectionStr()
{
  let grid = \$('#dsGrid').jsGrid('option', 'data');
  let selStr = '';

  for (let i = 0; i < grid.length; i++)
  {
    if (grid[i].selected == 1)
    {
      selStr = selStr + grid[i].ID + ',';
    }
  }

  return(selStr);
}



function refreshDisplay()
{
  \$('#dsGrid').jsGrid('render');
}



function automatedReporting()
{
  let curDS = document.getElementById('ds').value;
  let autoURL = "autoRptStory.cld?ds=" + curDS;

  location.href = autoURL;
}



function createNewReport()
{
  let curDS = document.getElementById('ds').value;
  let createURL = "newRPTname.cld?datasource=" + curDS;

  location.href = createURL;
}



function openSelectedReport()
{
  let selectedRpts = getSelectionStr();

  if (selectedRpts.length == 0)
  {
    return;
  }
  location.href='/app/rpt/display.cld?rpt=' + selectedRpts;
}



function copySelectedReport()
{
  let selectedRpts = getSelectionStr();
  let curDS = document.getElementById('ds').value;
  location.href='/app/rpt/copyReport.cld?r=' + selectedRpts + "&ds=" + curDS;
}



function editSelectedCube()
{
  let selectedRpts = getSelectionStr();

  if (selectedRpts.length == 0)
  {
    return;
  }
  location.href='/app/datasel/datasel.cld?rptID=' + selectedRpts;
}



function cubeSharing()
{
  let selectedRpts = getSelectionStr();

  if (selectedRpts.length == 0)
  {
    return;
  }
  location.href='accessControl.cld?c=' + selectedRpts;
}



function cubeProperties()
{
  let selectedRpts = getSelectionStr();

  if (selectedRpts.length == 0)
  {
    return;
  }
  location.href='properties.cld?c=' + selectedRpts;
}



function deleteSelectedReport()
{
  let selectedRpts = getSelectionStr();

  if (selectedRpts.length == 0)
  {
    return;
  }
  location.href='/app/rpt/deleteReport.cld?rpt=' + selectedRpts;
}



function refreshAll()
{
  let value = document.getElementById('ds').value;
  location.href='/app/rpt/refreshAll.cld?ds=' + value;
}



function cubeHistory()
{
  let selectedRpts = getSelectionStr();

  if (selectedRpts.length == 0)
  {
    return;
  }
  location.href='history.cld?rptID=' + selectedRpts;
}



function cubeStatistics()
{
  let selectedRpts = getSelectionStr();

  if (selectedRpts.length == 0)
  {
    return;
  }
  location.href='statistics.cld?rptID=' + selectedRpts;
}



let refreshTimer = setInterval(function(){flipDS()}, 60000);

function flipDS()
{
  let newDS = document.getElementById('ds').value;

  loadURL = "ajaxReportList?dsID=" + newDS;
  \$("#dsGrid").jsGrid("loadData");

  document.getElementById('ownerDiv').innerHTML = dsUserArray[newDS];
  document.getElementById('descDiv').innerHTML = dsDescArray[newDS];
}
</SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-0">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item active">Reports</LI>
  </OL>
</NAV>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #by default, show the customer reports for the last DS they looked at (the
  #dsID is stored in the user's cookie by ajaxReportList)
  $dsID = $session->param(rpt_DS);

  #if we were passed a dsID as a parameter, use it instead
  $ds = $q->param('ds');
  if ($ds > 0)
  {
    $dsID = $ds;
  }

  $db = KAPutil_connect_to_database();

  #determine if the user is allowed to see Beta features
  $query = "SELECT betaTester FROM app.users WHERE ID=$userID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($betaTester) = $dbOutput->fetchrow_array;

  #get the list of data sources the user has access to
  @userSources = ds_list($db, $userID, $acctType);
  $sources = join(',', @userSources);

  #now get the list of data sources that contain a report shared with this user
  @userSources = reports_ds_list($db, $userID);
  $tmp = join(',', @userSources);
  $sources = $sources . "," . $tmp;

  #knock any leading or trailing commas off of the list of sources
  if ($sources =~ m/^,(.*)/)
  {
    $sources = $1;
  }
  if ($sources =~ m/(.*),$/)
  {
    $sources = $1;
  }

  #if we still don't have a default DS to display, pick the first one
  if ($dsID < 1)
  {
    $dsID = $userSources[0];
  }

  print_html_header();

  if ($acctType > 0)
  {
    print <<END_HTML;
<NAV CLASS="navbar navbar-expand-lg navbar-light bg-light border">

  <BUTTON CLASS="navbar-toggler" TYPE="button" data-bs-toggle="collapse" data-bs-target="#navbarNavDropdown">
    <SPAN CLASS="navbar-toggler-icon"></SPAN>
  </BUTTON>

  <DIV CLASS="collapse navbar-collapse justify-content-center" ID="navbarNavDropdown">
    <UL CLASS="navbar-nav">
      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="automatedReporting()" TITLE="Automatically generate reports"><I CLASS="bi bi-magic"></I> Automatic</A></LI>

      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="openSelectedReport()" TITLE="Open the selected report"><I CLASS='bi bi-folder2-open'></I> Open</A></LI>

      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="createNewReport()" TITLE="Create a new report"><I CLASS="bi bi-plus-lg"></I> New</A></LI>

      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="copySelectedReport()" TITLE="Copy the selected report"><I CLASS="bi bi-clipboard-plus"></I> Copy</A></LI>

      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="editSelectedCube()" TITLE="Edit the selected report"><I CLASS="bi bi-pencil"></I> Modify</A></LI>

      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="cubeSharing()" TITLE="Share a report with other users"><I CLASS="bi bi-person"></I> Sharing</A></LI>

      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="cubeProperties()" TITLE="Set the report's properties"><I CLASS="bi bi-gear"></I> Properties</A></LI>

      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="deleteSelectedReport()" TITLE="Delete the selected report"><I CLASS="bi bi-trash"></I> Delete</A></LI>

      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="refreshAll()" TITLE="Refresh all reports in this data source"><I CLASS="bi bi-arrow-clockwise"></I> Refresh All</A></LI>

      <LI CLASS="nav-item dropdown">
        <A CLASS="nav-link dropdown-toggle" data-bs-toggle="dropdown" HREF="#">
          <I CLASS="bi bi-three-dots-vertical"></I>More
        </A>
        <UL CLASS="dropdown-menu">
          <LI><A CLASS="dropdown-item" HREF="#" onclick="cubeHistory()">History</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="cubeStatistics()">Statistics</A></LI>
        </UL>
      </LI>

    </UL>
  </DIV>
</NAV>
END_HTML
  }

  print <<END_HTML;
<P>
<DIV CLASS="container">
  <DIV CLASS="row">

    <DIV CLASS="col-xl-1"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 col-xxl-12"> <!-- content -->
END_HTML

  print <<END_HTML;
      <P>
      <TABLE>
        <TR>
          <TD><B>Data Source:&nbsp;</B></TD>
          <TD>
END_HTML

  %userIDHash = utils_get_user_hash($db);

  #get the list of data sources the user has access to
  @userSources = ds_list($db, $userID, $acctType);
  $sources = join(',', @userSources);

  #now get the list of data sources that contain a report shared with this user
  @userSources = reports_ds_list($db, $userID);
  $tmp = join(',', @userSources);
  $sources = $sources . "," . $tmp;

  #knock any leading or trailing commas off of the list of sources
  if ($sources =~ m/^,(.*)/)
  {
    $sources = $1;
  }
  if ($sources =~ m/(.*),$/)
  {
    $sources = $1;
  }

  $query = "SELECT ID, name, userID, description FROM dataSources \
      WHERE ID IN ($sources) AND deleted = 0 ORDER BY name";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  #output a select row for each data source the user has privs to
  print("<SELECT CLASS='form-select' id=\"ds\" STYLE=\"width:350px;\" onChange=\"flipDS()\">\n");

  while (($id, $name, $dsUser, $dsDesc) = $dbOutput->fetchrow_array)
  {

    #set the default data source to be the first one we see
    if ($dsID < 1)
    {
      $dsID = $id;
    }

    #highlight data sources that belong to the current user
    $CSSstyle = "";
    if ($dsUser == $userID)
    {
      $CSSstyle = "background:lightcyan;";
    }
    print("<OPTION STYLE='$CSSstyle' VALUE=\"$id\">$name</OPTION>\n");

    #set our initial datasource display values
    if ($id == $dsID)
    {
      $initUser = $userIDHash{$dsUser};
      $initDesc = $dsDesc;
    }

    #build up hashes of DS info (these get turned into JS arrays at bottom of
    #page)
    $dsUserHash{$id} = $dsUser;
    $dsDescHash{$id} = $dsDesc;
  }

  print <<END_HTML;
            </SELECT>
            <SCRIPT>
              \$("select#ds").val("$dsID");
            </SCRIPT>
          </TD>
        </TR>
        <TR>
          <TD STYLE="text-align:right;"><B>Analyst:&nbsp;</B></TD>
          <TD><DIV ID="ownerDiv">$initUser</DIV></TD>
        </TR>
        <TR>
          <TD STYLE="text-align:right;"><B>Description:&nbsp;</B></TD>
          <TD><DIV ID="descDiv">$initDesc</DIV></TD>
        </TR>
      </TABLE>

      <P></P>
      <DIV ID="dsGrid" CLASS="grid" STYLE="margin:auto; font-size:13px;"></DIV>

      <SCRIPT>
        dsUserArray = [0];
        dsDescArray = [0];
END_HTML

  foreach $id (keys %dsUserHash)
  {
    $name = $dsUserHash{$id};
    $name = $userIDHash{$name};
    $name =~ s/\'/\\'/g;
    print("dsUserArray[$id] = '$name';\n");
  }
  foreach $id (keys %dsDescHash)
  {
    $desc = $dsDescHash{$id};
    $desc =~ s/\'/\\'/g;
    print("dsDescArray[$id] = '$desc';\n");
  }

  print <<END_HTML;
      </SCRIPT>

    </DIV>  <!-- content -->

    <DIV CLASS="col-xl-1"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
