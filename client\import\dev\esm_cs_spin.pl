#!/usr/bin/perl

use Text::CSV;

#Import C&S Spin data for ESM-Ferolie

  #instantiate our CSV object
  $csv = Text::CSV->new( {binary => 1} );

  open(INPUT, "$ARGV[0]");
  open(OUTPUT, ">$ARGV[1]");

  #parse the primary header line
  $line = <INPUT>;
  $csv->parse($line);
  @columns = $csv->fields();

  $idx = 0;
  foreach $header (@columns)
  {
    if ($header eq "Customer")
    {
      $columns[$idx] = "Geography";
    }
    elsif ($header eq "Description")
    {
      $columns[$idx] = "Product";
    }
    elsif ($header eq "YYYYMM")
    {
      $timeCol = $idx;
      $columns[$idx] = "Time Period";
    }
    elsif ($header eq "Vendor_Name")
    {
      $columns[$idx] = "PSEG:COMPANY";
    }

    $idx++;
  }

  #output the headers
  $csv->combine(@columns);
  $line = $csv->string();
  print OUTPUT "$line\n";

  while ($line = <INPUT>)
  {

    $csv->parse($line);
    @columns = $csv->fields();

    $columns[$timeCol] =~ m/(\d\d\d\d)(\d\d)/;
    $year = $1;
    $month = $2;

    if ($month eq "02")
    {
      if (($year % 4) == 0)
      {
        $day = 29;
      }
      else
      {
        $day = 28;
      }
    }
    elsif (($month eq "04") || ($month eq "06") || ($month eq "09") ||
        ($month eq "11"))
    {
      $day = 30;
    }
    else
    {
      $day = 31;
    }

    $columns[$timeCol] = "1 Month Ending $month/$day/$year";

    $csv->combine(@columns);
    $line = $csv->string();

    print OUTPUT "$line\n";
  }
