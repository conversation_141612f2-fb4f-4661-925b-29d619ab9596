#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::BuildCube;
use Lib::Cubes;
use Lib::DataSel;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Copy Report</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  $rptName = cube_id_to_name($db, $cubeID);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/rpt/main">Reports</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?rpt=$cubeID">$rptName</A></LI>
    <LI CLASS="breadcrumb-item active">Copy Report</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser and get the forecast to be deleted
  $q = new CGI;
  $cubeID = $q->param('srcRpts');
  $destDSID = $q->param('destDS');

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  $db = KAPutil_connect_to_database();

  print_html_header();

  #make sure we have read/write privs for this data cube
  $privs = cube_rights($db, $userID, $cubeID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to copy this report.");
  }

  #make sure this report isn't being updated
  $query = "SELECT opInfo FROM app.jobs \
      WHERE cubeID=$cubeID AND operation='CUBE-UPDATE'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($opInfo) = $dbOutput->fetchrow_array;
  if ((length($opInfo) > 1) && (!($opInfo =~ m/^ERROR/)))
  {
    exit_warning("This report is currently being refreshed, and can't be copied until the refresh completes.");
  }

  #grab all of the cube definition info for the source report
  $query = "SELECT name, dsID, lastUpdate, scriptProducts, scriptGeographies, scriptTimeperiods, scriptMeasures, products, geographies, timeperiods, measures \
      FROM cubes WHERE ID=$cubeID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($srcName, $dsID, $lastUpdate, $scriptProducts, $scriptGeographies, $scriptTimeperiods, $scriptMeasures, $products, $geographies, $timeperiods, $measures) = $dbOutput->fetchrow_array;

  $name = "Copy of $srcName";

  #build up our schema and source cube table names
  $dsSchema = "datasource_" . $dsID;
  $rptCube = "_rptcube_" . $cubeID;

  #if we're copying the report into the same data source
  if ($destDSID == 0)
  {
    $destDSID = $dsID;
    $destSchema = $dsSchema;
  }

  #else we're transferring it to another data source
  else
  {
    $destSchema = "datasource_" . $destDSID;

    #transfer the data selections to the new data source's items
    $scriptProducts = datasel_transfer_script($db, $dsSchema, $destSchema, "p", $scriptProducts);
    $scriptGeographies = datasel_transfer_script($db, $dsSchema, $destSchema, "g", $scriptGeographies);
    $scriptTimeperiods = datasel_transfer_script($db, $dsSchema, $destSchema, "t", $scriptTimeperiods);
    $scriptMeasures = datasel_transfer_script($db, $dsSchema, $destSchema, "m", $scriptMeasures);
  }

  #create a copy of the cube definition in the master cubes tables
  $q_name = $db->quote($name);
  $query = "INSERT INTO cubes (dsID, userID, orgID, name, lastUpdate, scriptProducts, scriptGeographies, scriptTimeperiods, scriptMeasures, products, geographies, timeperiods, measures) \
      VALUES ($destDSID, $userID, $orgID, $q_name, '$lastUpdate', '$scriptProducts', '$scriptGeographies', '$scriptTimeperiods', '$scriptMeasures', '$products', '$geographies', '$timeperiods', '$measures')";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);
  $newCubeID = $db->{q{mysql_insertid}};

  #copy all of the visuals in the source report to the destination
  $query = "SELECT type, design, tableRowDims, tableFilterDims, tableColDims, graph_x, graph_y, graph_z, selProducts, selGeographies, selTimeperiods, selMeasures \
      FROM visuals WHERE cubeID=$cubeID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  while (($reportType, $design, $tableRowDims, $tableFilterDims, $tableColDims, $graph_x, $graph_y, $graph_z, $selProducts, $selGeographies, $selTimeperiods, $selMeasures) = $dbOutput->fetchrow_array)
  {
    $q_design = $db->quote($design);

    $query = "INSERT INTO visuals (cubeID, dsID, type, design, tableRowDims, tableFilterDims, tableColDims, graph_x, graph_y, graph_z, selProducts, selGeographies, selTimeperiods, selMeasures) \
        VALUES ($newCubeID, $destDSID, '$reportType', $q_design, '$tableRowDims', '$tableFilterDims', '$tableColDims', '$graph_x', '$graph_y', '$graph_z', '$selProducts', '$selGeographies', '$selTimeperiods', '$selMeasures')";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);
  }

  #build up our new source cube table name
  $newRptCube = "_rptcube_" . $newCubeID;

  #if we're copying the cube to the same DS, we can just copy the data
  if ($dsID == $destDSID)
  {

    #create a copy of the source cube table structure
    $query = "CREATE TABLE $dsSchema.$newRptCube LIKE $dsSchema.$rptCube";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    #lock the destination and source report tables
    $query = "LOCK TABLES $dsSchema.$newRptCube WRITE, $dsSchema.$rptCube READ";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    #populate the new cube with the data from the original cube
    $query = "INSERT $dsSchema.$newRptCube SELECT * FROM $dsSchema.$rptCube";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    #unlock the report cube tables
    $query = "UNLOCK TABLES";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);
  }

  #else if we're moving to a different DS, we need to build cube from scratch
  else
  {

    #fork a new process to do the actual refresh in the background
    $SIG{CHLD} = "IGNORE";
    if ($pid = fork())
    {
      #parent process - NOOP

    }
    else
    {
      #child process

      #let Apache know not to wait on the child process
      close(STDIN);
      close(STDOUT);

      #redirect STDERR to the Koala error log
      close(STDERR);
      open(STDERR, ">>/opt/apache/htdocs/tmp/koala_error.log");
      select(STDERR);
      $| = 1;

      #reconnect to the database
      $db = KAPutil_connect_to_database();

      #wipe out the old set of selections so reasonable defaults will be set
      #inside the new data source
      $query = "UPDATE app.visuals \
          SET selProducts=NULL, selGeographies=NULL, selTimeperiods=NULL, selMeasures=NULL \
          WHERE cubeID=$newCubeID";
      $status = $db->do($query);
      KAPutil_handle_db_err($db, $status, $query);

      cube_build($db, $destSchema, $newCubeID, $userID);
      exit;
    }
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-11 col-md-8 col-lg-6 col-xl-5"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Copy Report</DIV>
        <DIV CLASS="card-body">

          <P>
          The selected report has been copied to $name.

          <P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='main'"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
          </DIV>
          </FORM>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  $dsName = ds_id_to_name($db, $dsID);
  $destDSName = ds_id_to_name($db, $destDSID);

  utils_audit($db, $userID, "Copied report to $name in $destDSName", $dsID, $cubeID, 0);
  utils_audit($db, $userID, "Copied from $srcName in $dsName", $dsID, $newCubeID, 0);

  $activity = "$first $last copied report $name in $dsName";
  utils_slack($activity);

  print_html_footer();

#EOF
