#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;
use Text::CSV;

use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::KoalaConfig;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::Social;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($kapDB);

  print <<END_HTML;
<DOCCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Exporting Data</TITLE>
<LINK REL="SHORTCUT ICON" HREF="$Lib::KoalaConfig::kapHostURL/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>

let statusTimer = setInterval(function(){displayStatus()}, 1000);
let statCount = 0;

function displayStatus()
{
  const url = 'ajaxAPI.cld?svc=job_op_status&j=$jobID';

  \$.get(url, function(data, status)
  {
    let statusText = data;
    let statElements = statusText.split('|');

    if (statusText.includes('Starting update of Koala data source'))
    {
      //\$('#progress-bar').css('width', '100%');
      //document.getElementById('progress-bar').innerHTML = '100%';
      //\$('#progressDiv').hide();
      clearInterval(statusTimer);
      location.href='/app/dsr/updateDSwork.cld?ds=$dsID';
    }

    else if (statusText.search('ERR') == 0)
    {
      clearInterval(statusTimer);
      document.getElementById('div-op-title').innerHTML = "Error transferring data";
      \$('#progress-bar').css('width', '100%');
      \$('#progress-bar').addClass('bg-danger');
      document.getElementById('progress-bar').innerHTML = 'ERROR';
      document.getElementById('div-op-details').innerHTML = statElements[1];
    }

    else
    {
      let opTitle = statElements[0];
      let opPct = statElements[1];
      let opDetails = statElements[2];
      let opTimeEstimate = statElements[3];
      let opExtra = statElements[4];
      document.getElementById('div-op-title').innerHTML = opTitle;
      if (opPct.length > 0)
      {
        document.getElementById('progress-bar').innerHTML = opPct + '%';
        \$('#progress-bar').css('width', opPct+'%');
      }
      document.getElementById('div-op-details').innerHTML = opDetails;
      document.getElementById('div-op-extra').innerHTML = opExtra;
    }

    if (statCount == 5)
    {
      clearInterval(statusTimer);
      statusTimer = setInterval(function(){displayStatus()}, 5000);
    }
    statCount++;
  });
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($kapDB, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="$Lib::KoalaConfig::kapHostURL/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?f=$flowID">Data Prep</A></LI>
    <LI CLASS="breadcrumb-item">$flowName</LI>
    <LI CLASS="breadcrumb-item active">Export to Koala Data Source</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------
#
# Output the HTML for the web page that displays the status dialog
#

sub print_status_html
{
  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-11 col-md-8 col-lg-6 col-xl-5"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Exporting Data to Koala</DIV>
        <DIV CLASS="card-body">

          <H5 ID="div-op-title"></H5>
          <DIV CLASS="progress" style="height:25px;">
            <DIV ID="progress-bar" CLASS="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" STYLE="width:0%;">
            </DIV>
          </DIV>

          <P>
          <DIV ID="div-op-details"></DIV>
          <DIV ID="div-op-extra"></DIV>

          <P>&nbsp;</P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" ID="btnAsync" onClick="location.href='main.cld?f=$flowID'"><I CLASS="bi bi-speedometer"></I> Be More Productive</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get the CGI input variables
  $exportDest = $q->param('dest');
  $flowID = $q->param('f');
  $jobID = $q->param('j');
  $dsID = $q->param('ds');
  $dsName = $q->param('dsName');
  $dsType = $q->param('dsType');
  $desc = $q->param('desc');
  $appendUPC = $q->param('appendUPC');
  $compressWS = $q->param('compressWS');
  $dontOverwriteNames = $q->param('dontOverwriteNames');
  $pmatch = $q->param('pmatch');
  $gmatch = $q->param('gmatch');
  $tmatch = $q->param('tmatch');

  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #connect to the database
  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  $flowName = prep_flow_id_to_name($prepDB, $flowID);

  #make sure we have read privs for this data source
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if (($privs ne "R") && ($privs ne "W"))
  {
    print_html_header();
    exit_error("You don't have privileges to export this data flow.");
  }

  #make sure we have write privs for the data source (if not creating a new one)
  if ($dsID > 0)
  {
    $privs = ds_rights($kapDB, $userID, $dsID, $acctType);
    if ($privs ne "W")
    {
      print_html_header();
      exit_error("You don't have privileges to modify this data source.");
    }
  }

  #if we're rejoining an existing job
  if ($jobID > 0)
  {
    $query = "SELECT state FROM prep.jobs WHERE ID=$jobID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($state) = $dbOutput->fetchrow_array;
    if (($state =~ m/^EXP-KOALA/) || ($state =~ m/^UPDATE-KOALA/))
    {
      $query = "SELECT dsID FROM prep.flows WHERE ID=$flowID";
      $dbOutput = $prepDB->prepare($query);
      $dbOutput->execute;
      ($dsID) = $dbOutput->fetchrow_array;
      print_html_header();
      print_status_html();
      exit;
    }
  }

  #make sure the analytics node isn't out of storage
  $usagePct = KAPutil_get_org_quota_used($kapDB, $userID);
  if ($usagePct > 99)
  {
    print_html_header();
    exit_error("Your Koala Analytics cloud is out of storage - please contact your Koala account manager to order more.")
  }

  #make sure the prep cloud isn't out of storage
  $usagePct = prep_flow_storage($acctType);
  if ($usagePct > 99)
  {
    print_html_header();
    exit_error("Your Koala Data Prep cloud is out of storage - please contact your Koala account manager to order more.")
  }

  Social_clear_prep_export_ready_items($kapDB, $flowID);

  #grab the data source update settings specific to this flow, and save them
  if ($appendUPC eq "on")
  {
    $appendUPC = 1;
  }
  else
  {
    $appendUPC = 0;
  }
  if ($compressWS eq "on")
  {
    $compressWS = 1;
  }
  else
  {
    $compressWS = 0;
  }
  if ($dontOverwriteNames eq "on")
  {
    $dontOverwriteNames = 1;
  }
  else
  {
    $dontOverwriteNames = 0;
  }

  if (length($pmatch) < 1)
  {
    $pmatch = "auto";
  }
  if (length($gmatch) < 1)
  {
    $pmatch = "auto";
  }
  if (length($tmatch) < 1)
  {
    $pmatch = "auto";
  }
  $query = "UPDATE prep.flows \
      SET appendUPC=$appendUPC, compressWS=$compressWS, dontOverwriteNames=$dontOverwriteNames, pmatch='$pmatch', gmatch='$gmatch', tmatch='$tmatch' \
      WHERE ID=$flowID";
  $prepDB->do($query);

  #save the destination DS if we're exporting to an existing DS
  if ($dsID > 1)
  {
    $query = "UPDATE prep.flows SET dsID=$dsID WHERE ID=$flowID";
    $prepDB->do($query);
  }

  #if we need to create a new data source...
  $createNewDS = 0;
  if ($dsID < 1)
  {
    $createNewDS = 1;

    #do a little basic error checking to keep analysts from shooting themselves
    $query = "SELECT ID FROM $masterColTable WHERE type='product'";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($prodColID) = $dbOutput->fetchrow_array;
    if ($prodColID < 1)
    {
      print_html_header();
      exit_warning("Your data is missing a product column, and can't be exported to Koala Analytics");
    }

    $q_dsName = $kapDB->quote($dsName);
    $q_dsType = $kapDB->quote($dsType);
    $q_dsDescription = $kapDB->quote($dsDescription);

    #insert an entry in the master datasource table
    $query = "INSERT INTO dataSources (userID,name,type,lastUpdate,description,appendUPC,compressWS) VALUES ($userID, $q_dsName, $q_dsType, NOW(), $q_dsDescription, $appendUPC, $compressWS)";
    $kapDB->do($query);

    #get the unique ID for this data source
    $dsID = $kapDB->{q{mysql_insertid}};

    $query = "UPDATE prep.flows SET dsID=$dsID WHERE ID=$flowID";
    $prepDB->do($query);
  }

  print_html_header();

  #if we're updating a data source that already has something running in it,
  #error out
  $ok = DSRutil_operation_ok($kapDB, $dsID, 0, "DS-UPDATE");
  if ($ok != 1)
  {
    exit_warning("Another job is already using this data source - please wait until it finishes and try again later.")
  }

  if (length($dsName) > 0)
  {
    prep_audit($prepDB, $userID, "Exported to new Koala data source $dsName", $flowID);
    utils_slack("PREP: $first $last exported $flowName to new Koala data source $dsName");
  }
  else
  {
    $dsName = ds_id_to_name($kapDB, $dsID);
    prep_audit($prepDB, $userID, "Exported to Koala data source $dsName", $flowID);
    utils_slack("PREP: $first $last updated Koala data source $dsName with $flowName");
  }

  #fork a new process to do the actual data dumping in the background
  $SIG{CHLD} = "IGNORE";
  if ($pid = fork)
  {
    #parent process

    print_status_html();
  }
  else
  {
    #child process

    #let Apache know not to wait on the child process
    close(STDIN);
    close(STDOUT);

    #redirect STDERR to the Koala error log
    close(STDERR);
    open(STDERR, ">>/opt/apache/htdocs/tmp/koala_error.log");
    select(STDERR);
    $| = 1;

    #reconnect to the database
    $kapDB = KAPutil_connect_to_database();
    $prepDB = PrepUtils_connect_to_database();

    prep_flow_export_koala($prepDB, $kapDB, $flowID, $jobID, $userID, $dsID, $createNewDS, $dsName, $desc);
  }

#EOF
