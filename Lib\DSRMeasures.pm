
package Lib::DSRMeasures;

use Exporter;

use lib "/opt/apache/app";

use Lib::BuildCube;
use Lib::DataSel;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::KAPSegmentation;

our @ISA = ('Exporter');

our @EXPORT = qw(
    &DSRmeasures_get_calcmeasure_hash
    DSRmeasures_get_basemeasures_hash
    &DSRmeasures_get_agg_rules
    &DSRmeasures_get_agg_rule
    &DSRmeasures_get_type_hint
    &DSRmeasures_get_agg_measure_val
    &DSRmeasures_get_cube_calc_measures_array
    &DSRmeasures_get_calc_measure_dependencies
    &DSRmeasures_recalculate_outofdate_measures
    &DSRmeasures_get_calc_type_name
    &DSRmeasures_calc_change
    &DSRmeasures_calc_pct_change
    &DSRmeasures_calc_ratio
    &DSRmeasures_calc_multiplication
    &DSRmeasures_calc_sum
    &DSRmeasures_calc_difference
    &DSRmeasures_calc_pct_change_meas
    &DSRmeasures_calc_lag
    &DSRmeasures_calc_lead
    &DSRmeasures_calc_mov_avg
    &DSRmeasures_calc_mov_total
    &DSRmeasures_calc_ytd
    &DSRmeasures_calc_count
    &DSRmeasures_calc_index
    &DSRmeasures_calc_share
    &DSRmeasures_calc_calc
    &DSRmeasures_recalculate_cube_measure
    &DSRmeasures_recalculate_all_measures
    &DSRmeasures_recalculate_measure
    &DSRmeasures_format_text
    &DSRmeasures_format_html
    &DSRmeasures_format_excel
    &DSRmeasures_get_conditional_color
    &DSRmeasures_get_format
    &DSRmeasures_get_format_hash
    &DSRmeasures_human_readable_formula
    &DSRmeasures_name_by_id
    &DSRmeasures_id_by_name);


#------------------------------------------------------------------------

#Variables global to this module (used for hashing memberships and values to
#cut down on database queries)

my $_cacheDSID;
my $_cacheCubeID;

my %_cachedAggRules;

my %_cachedCalculations;
my %_cachedCalcBeforeAgg;
my %_cachedCalcMeasName;

my %_prodMemberships;
my %_geoMemberships;

my %_cachedAggMeasureVals;

my %_cachedYagoTimes;
my %_cachedYaheadTimes;

my %_measureColExists;

my %_measureCalculated;



#-------------------------------------------------------------------------
#
# Handle a database error of some kind during measure calculation
#

sub meas_db_err
{
  my ($date);

  my ($db, $status, $text) = @_;


  if (!defined($status))
  {
    $date = localtime();
    print STDERR "$date: $text\n";
    if ($db->errstr =~ m/^MySQL server has gone away/)
    {
      die("Lost connection to database, terminating");
    }
  }
}



#------------------------------------------------------------------------
#
# Return a hash of info about every calculated measure in the specified DS,
# keyed by measure ID
#

sub DSRmeasures_get_calcmeasure_hash
{
  my ($query, $dbOutput, $measureID, $calculation, $calcType, $status);
  my (%measureInfoHash);

  my ($db, $dsSchema) = @_;


  undef(%measureInfoHash);

  $query = "SELECT ID, calculation FROM $dsSchema.measures \
      WHERE !isnull(calculation)";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  meas_db_err($db, $status, $query);

  while (($measureID, $calculation) = $dbOutput->fetchrow_array)
  {
    $calculation =~ m/^(.*?)\|/;
    $calcType = $1;
    $calcType = DSRmeasures_get_calc_type_name($calcType);
    $measureInfoHash{$measureID} = $calcType;
  }

  return(%measureInfoHash);
}



#------------------------------------------------------------------------
#
# Return an ID-keyed hash of all base (non-calcualted) measures in the specified
# data source.
#

sub DSRmeasures_get_basemeasures_hash
{
  my ($query, $dbOutput, $measureID, $measName, $status);
  my (%baseMeasureHash);

  my ($db, $dsSchema) = @_;


  if ($dsSchema =~ m/^\d+$/)
  {
    $dsSchema = "datasource_" . $dsSchema;
  }

  $query = "SELECT ID, name FROM $dsSchema.measures WHERE isnull(calculation)";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  meas_db_err($db, $status, $query);

  while (($measureID, $measName) = $dbOutput->fetchrow_array)
  {
    $baseMeasureHash{$measureID} = $measName;
  }

  return(%baseMeasureHash);
}



#------------------------------------------------------------------------
#
# Clear out our persistent caches if we're working in a new DS or new cube
#

sub DSRmeasures_cache_ok
{
  my ($dsID, $rptID) = @_;

  if ($dsID =~ m/datasource_(\d+)/)
  {
    $dsID = $1;
  }

  #if we're starting work in a new data source
  if ($_cacheDSID != $dsID)
  {
    $_cacheDSID = $dsID;

    #clear the cached measure aggregation rules
    undef(%_cachedAggRules);

    #clear cached calculations
    undef(%_cachedCalculations);
    undef(%_cachedCalcBeforeAgg);
    undef(%_cachedCalcMeasName);

    #clear the product & geography membership caches
    undef(%_prodMemberships);
    undef(%_geoMemberships);

    #clear the cached roll-up values
    undef(%_cachedAggMeasureVals);

    #clear the cached year ago/year ahead time periods
    undef(%_cachedYagoTimes);
    undef(%_cachedYaheadTimes);
  }

  if ((keys %_cachedAggMeasureVals) > 1_000_000)
  {
    undef(%_cachedAggMeasureVals);
  }


  #if we're starting work in a new cube
  if ($_cacheCubeID != $rptID)
  {
    $_cacheCubeID = $rptID;

    #clear the measure column cache
    undef(%_measureColExists);

    undef(%_measureCalculated);
  }
}



#-------------------------------------------------------------------------
#
# Get the Product, Geography, & Time aggregation rules for the specified
# measure, if they exist.
#

sub DSRmeasures_get_agg_rules
{
  my ($val);

  my ($mName) = @_;


  #lowercase the measure name to make matching simpler
  $mName = lc($mName);

  #try turning a bunch of year-ago abbreviations into something common
  if ($mName =~ m/^(.*) yag$/)
  {
    $mName = "$1 ya";
  }
  if ($mName =~ m/^(.*) yago$/)
  {
    $mName = "$1 ya";
  }
  if ($mName =~ m/^(.*) yrago$/)
  {
    $mName = "$1 ya";
  }
  if ($mName =~ m/^(.*) year ago$/)
  {
    $mName = "$1 ya";
  }

  #strip off some IRI-specific qualifiers that don't impact agg rules
  if ($mName =~ m/^(.*), no special pack$/)
  {
    $mName = $1;
  }

  #manually specify the aggregation rules hashes
  my %measAggRules = (
    "\$" => "SUM,SUM,SUM",
    "\$ 2ya" => "SUM,SUM,SUM",
    "\$ cya" => "SUM,SUM,SUM",
    "\$ / \$mm acv" => 'SUM,AVG,SUM',
    "\$ act chg vs ya" => "SUM,SUM,SUM",
    "\$ chg ya" => "SUM,SUM,SUM",
    "\$ incr vol" => "SUM,SUM,SUM",
    "\$ incr vol act chg vs ya" => "SUM,SUM,SUM",
    "\$ shr" => "AVG,AVG,SUM",
    "\$ shr ~ act chg vs prev" => "AVG,AVG,SUM",
    "\$ shr ~ act chg vs ya" => "AVG,AVG,SUM",
    "\$ vol" => "SUM,SUM,SUM",
    "\$ vol ~ prev" => "SUM,SUM,SUM",
    "\$ ya" => "SUM,SUM,SUM",
    "%acv" => "MAX,AVG,MAX",
    "%acv ya" => "MAX,AVG,MAX",
    "% acv [avg] selling" => "MAX,AVG,MAX",
    "% acv [avg] selling ya" => "MAX,AVG,MAX",
    "% acv [max] any promo" => "MAX,AVG,MAX",
    "% acv [max] disp only" => "MAX,AVG,MAX",
    "% acv [max] feat & disp" => "MAX,AVG,MAX",
    "% acv [max] feat only" => "MAX,AVG,MAX",
    "% acv [max] no promo" => "MAX,AVG,MAX",
    "% acv [max] selling" => "MAX,MAX,MAX",
    "% acv [max] selling ya" => "MAX,MAX,MAX",
    "% acv [max] tpr only" => "MAX,AVG,MAX",
    "% acv (% stores selling)" => "MAX,AVG,MAX",
    "% acv (avg) selling" => "MAX,AVG,MAX",
    "% acv (max) any promo" => "MAX,AVG,MAX",
    "% acv (max) disp only" => "MAX,AVG,MAX",
    "% acv (max) feat & disp" => "MAX,AVG,MAX",
    "% acv (max) feat only" => "MAX,AVG,MAX",
    "% acv (max) no promo" => "MAX,AVG,MAX",
    "% acv (max) selling" => "MAX,AVG,MAX",
    "% acv (max) selling ya" => "MAX,AVG,MAX",
    "% acv (max) tpr only" => "MAX,AVG,MAX",
    "% acv, any display" => "MAX,AVG,MAX",
    "% acv, any feature" => "MAX,AVG,MAX",
    "% acv, any merchandising" => "MAX,AVG,MAX",
    "% acv, display only" => "MAX,AVG,MAX",
    "% acv, feature and display" => "MAX,AVG,MAX",
    "% acv, feature only" => "MAX,AVG,MAX",
    "% acv, price reduction only" => "MAX,AVG,MAX",
    "% acv reach" => "MAX,AVG,MAX",
    "% acv reach ya" => "MAX,AVG,MAX",
    "%acv reach" => "MAX,AVG,MAX",
    "%acv reach ya" => "MAX,AVG,MAX",
    "% dollars, any merchandising" => "AVG,AVG,AVG",
    "% dollars, display only" => "AVG,AVG,AVG",
    "% dollars, feature & display" => "AVG,AVG,AVG",
    "% dollars, feature only" => "AVG,AVG,AVG",
    "% dollars, price reduction only" => "AVG,AVG,AVG",
    "% incrs in dollars, any merchandising" => "AVG,AVG,AVG",
    "% incrs in dollars, display only" => "AVG,AVG,AVG",
    "% incrs in dollars, feature and display" => "AVG,AVG,AVG",
    "% incrs in dollars, price reduction only" => "AVG,AVG,AVG",
    "% incrs in volume, any merchandising" => "AVG,AVG,AVG",
    "% incrs in volume, display only" => "AVG,AVG,AVG",
    "% incrs in volume, feature and display" => "AVG,AVG,AVG",
    "% incrs in volume, feature only" => "AVG,AVG,AVG",
    "% incrs in volume, prc red only" => "AVG,AVG,AVG",
    "% incrs in volume, price reduction only" => "AVG,AVG,AVG",
    "% stores selling" => "MAX,AVG,MAX",
    "% stores, selling" => "MAX,AVG,MAX",
    "% units, any merchandising" => "AVG,AVG,AVG",
    "% units, display only" => "AVG,AVG,AVG",
    "% units, feature and display" => "AVG,AVG,AVG",
    "% units, feature only" => "AVG,AVG,AVG",
    "% units, price reduction only" => "AVG,AVG,AVG",
    "% volume, any merchandising" => "AVG,AVG,AVG",
    "% volume, display only" => "AVG,AVG,AVG",
    "% volume, feature and display" => "AVG,AVG,AVG",
    "% volume, feature only" => "AVG,AVG,AVG",
    "% volume, price reduction only" => "AVG,AVG,AVG",
    "acv (\$mm)" => "MAX,SUM,MAX",
    "acv any disp" => "MAX,AVG,MAX",
    "acv any feat" => "MAX,AVG,MAX",
    "acv disp only" => "MAX,AVG,MAX",
    "acv feat & disp" => "MAX,AVG,MAX",
    "acv feat only" => "MAX,AVG,MAX",
    "acv max" => "MAX,AVG,MAX",
    "acv price red only" => "MAX,AVG,MAX",
    "acv weighted distribution" => "MAX,AVG,MAX",
    "acv wtd dist" => "MAX,AVG,MAX",
    "add, cum wghtd wks, any display" => "SUM,AVG,MAX",
    "add, cum wghtd wks, any merchandising" => "SUM,AVG,MAX",
    "add wghted wks, any merchandising" => "AVG,AVG,MAX",
    "add wghted wks, display only" => "AVG,AVG,MAX",
    "add wghted wks, feature and display" => "AVG,AVG,MAX",
    "add wghted wks, feature only" => "AVG,AVG,MAX",
    "add wghted wks, price reduction only" => "AVG,AVG,MAX",
    "any display without feature ~ percent acv" => "MAX,AVG,MAX",
    "any display without feature ~ sales dollars" => "SUM,SUM,SUM",
    "any display without feature ~ sales units" => "SUM,SUM,SUM",
    "any feature and display ~ percent acv" => "MAX,AVG,MAX",
    "any feature and display ~ sales dollars" => "SUM,SUM,SUM",
    "any feature and display ~ sales units" => "SUM,SUM,SUM",
    "any feature without display ~ percent acv" => "MAX,AVG,MAX",
    "any feature without display ~ sales dollars" => "SUM,SUM,SUM",
    "any feature without display ~ sales units" => "SUM,SUM,SUM",
    "any promo ~ percent acv" => "MAX,AVG,MAX",
    "any promo ~ sales dollars" => "SUM,SUM,SUM",
    "any promo ~ sales units" => "SUM,SUM,SUM",
    "any promo \$" => "SUM,SUM,SUM",
    "any promo \$ vol" => "SUM,SUM,SUM",
    "any promo \$ vol ya" => "SUM,SUM,SUM",
    "any promo \$ ya" => "SUM,SUM,SUM",
    "any promo %acv reach" => "MAX,AVG,MAX",
    "any promo unit vol" => "SUM,SUM,SUM",
    "any promo unit vol ya" => "SUM,SUM,SUM",
    "any promo units" => "SUM,SUM,SUM",
    "any promo units ya" => "SUM,SUM,SUM",
    "average items per store" => "AVG,AVG,SUM",
    "average items carried" => "AVG,AVG,AVG",
    "average items carried +/- chg, yago" => "AVG,AVG,AVG",
    "average items carried, ya" => "AVG,AVG,AVG",
    "average n-promoted price per unit" => "AVG,AVG,AVG",
    "average n-promoted price per volume" => "AVG,AVG,AVG",
    "average promoted price per unit" => "AVG,AVG,AVG",
    "average promoted price per volume" => "AVG,AVG,AVG",
    "average retail price" => "AVG,AVG,AVG",
    "avg % acv" => "AVG,AVG,AVG",
    "avg % acv +/- chg, yago" => "AVG,AVG,AVG",
    "avg % acv, ya" => "AVG,AVG,AVG",
    "avg price per unit" => "AVG,AVG,AVG",
    "avg price per volume" => "AVG,AVG,AVG",
    "avg unit price" => "AVG,AVG,AVG",
    "avg unit price cya" => "AVG,AVG,AVG",
    "avg unit price ya" => "AVG,AVG,AVG",
    "avg unit price, any merchandising" => "AVG,AVG,AVG",
    "avg unit price, display only" => "AVG,AVG,AVG",
    "avg unit price, feature and display" => "AVG,AVG,AVG",
    "avg unit price, feature only" => "AVG,AVG,AVG",
    "avg unit price, price reduction only" => "AVG,AVG,AVG",
    "avg weekly acv weighted distribution" => "AVG,AVG,MAX",
    "avg wkly acv wtd distribution" => "AVG,AVG,MAX",
    "average weekly items per store" => "AVG,AVG,SUM",
    "base \$" => "SUM,SUM,SUM",
    "base \$ 2ya" => "SUM,SUM,SUM",
    "base \$ cya" => "SUM,SUM,SUM",
    "base \$ ya" => "SUM,SUM,SUM",
    "base \$ vol" => "SUM,SUM,SUM",
    "base \$ vol ya" => "SUM,SUM,SUM",
    "base dollars" => "SUM,SUM,SUM",
    "base dollars +/- chg, ya" => "SUM,SUM,SUM",
    "base dollars, ya" => "SUM,SUM,SUM",
    "base u vol" => "SUM,SUM,SUM",
    "base u vol ya" => "SUM,SUM,SUM",
    "base units" => "SUM,SUM,SUM",
    "base units +/- chg, ya" => "SUM,SUM,SUM",
    "base units 2ya" => "SUM,SUM,SUM",
    "base units, ya" => "SUM,SUM,SUM",
    "base units ya" => "SUM,SUM,SUM",
    "base volume" => "SUM,SUM,SUM",
    "base volume per \$mm acv" => "SUM,AVG,SUM",
    "baseline sales dollars" => "SUM,SUM,SUM",
    "baseline sales units" => "SUM,SUM,SUM",
    "disp only \$ vol" => "SUM,SUM,SUM",
    "disp only \$ vol ya" => "SUM,SUM,SUM",
    "disp only unit vol" => "SUM,SUM,SUM",
    "disp only unit vol ya" => "SUM,SUM,SUM",
    "disp w/o feat \$" => "SUM,SUM,SUM",
    "disp w/o feat \$ % lift" => "AVG,AVG,AVG",
    "disp w/o feat \$ ya" => "SUM,SUM,SUM",
    "disp w/o feat %acv reach" => "MAX,AVG,MAX",
    "disp w/o feat cww" => "MAX,MAX,SUM",
    "disp w/o feat incr \$" => "SUM,SUM,SUM",
    "disp w/o feat incr \$ cya" => "SUM,SUM,SUM",
    "disp w/o feat tdp" => "SUM,MAX,MAX",
    "disp w/o feat unit price" => "AVG,AVG,AVG",
    "disp w/o feat unit price % disc" => "AVG,AVG,AVG",
    "disp w/o feat units" => "SUM,SUM,SUM",
    "disp w/o feat units ya" => "SUM,SUM,SUM",
    "dollar sales" => "SUM,SUM,SUM",
    "dollar share" => "AVG,AVG,SUM",
    "dollars" => "SUM,SUM,SUM",
    "dollars +/- chg, ya" => "SUM,SUM,SUM",
    "dollars per \$mm acv" => "SUM,AVG,SUM",
    "dollars spm per item" => "SUM,SUM,SUM",
    "dollars spm per item +/- chg, ya" => "SUM,SUM,SUM",
    "dollars spm per item, ya" => "SUM,SUM,SUM",
    "dollars, any merchandising" => "SUM,SUM,SUM",
    "dollars, display only" => "SUM,SUM,SUM",
    "dollars, display only +/- chg, ya" => "SUM,SUM,SUM",
    "dollars, display only, ya" => "SUM,SUM,SUM",
    "dollars, feature & display" => "SUM,SUM,SUM",
    "dollars, feature & display +/- chg, ya" => "SUM,SUM,SUM",
    "dollars, feature & display, ya" => "SUM,SUM,SUM",
    "dollars, feature and display" => "SUM,SUM,SUM",
    "dollars, feature only" => "SUM,SUM,SUM",
    "dollars, feature only +/- chg, ya" => "SUM,SUM,SUM",
    "dollars, feature only, ya" => "SUM,SUM,SUM",
    "dollars, non-promo" => "SUM,SUM,SUM",
    "dollars, non-promo +/- chg, ya" => "SUM,SUM,SUM",
    "dollars, non-promo, ya" => "SUM,SUM,SUM",
    "dollars, promo" => "SUM,SUM,SUM",
    "dollars, promo +/- chg, ya" => "SUM,SUM,SUM",
    "dollars, promo, ya" => "SUM,SUM,SUM",
    "dollars, price reduction only" => "SUM,SUM,SUM",
    "dollars, tpr" => "SUM,SUM,SUM",
    "dollars, tpr +/- chg, ya" => "SUM,SUM,SUM",
    "dollars, tpr, ya" => "SUM,SUM,SUM",
    "dollars, ya" => "SUM,SUM,SUM",
    "eq" => "SUM,SUM,SUM",
    "eq ya" => "SUM,SUM,SUM",
    "eq 2ya" => "SUM,SUM,SUM",
    "est acv selling" => "MAX,AVG,MAX",
    "est acv selling ya" => "MAX,AVG,MAX",
    "feat & disp \$" => "SUM,SUM,SUM",
    "feat & disp \$ % lift" => "AVG,AVG,AVG",
    "feat & disp \$ ya" => "SUM,SUM,SUM",
    "feat & disp \$ vol" => "SUM,SUM,SUM",
    "feat & disp \$ vol ya" => "SUM,SUM,SUM",
    "feat & disp %acv reach" => "MAX,AVG,MAX",
    "feat & disp cww" => "MAX,MAX,SUM",
    "feat & disp incr \$" => "SUM,SUM,SUM",
    "feat & disp incr \$ cya" => "SUM,SUM,SUM",
    "feat & disp tdp" => "SUM,MAX,MAX",
    "feat & disp unit price" => "AVG,AVG,AVG",
    "feat & disp unit price % disc" => "AVG,AVG,AVG",
    "feat & disp unit vol" => "SUM,SUM,SUM",
    "feat & disp unit vol ya" => "SUM,SUM,SUM",
    "feat & disp units" => "SUM,SUM,SUM",
    "feat & disp units ya" => "SUM,SUM,SUM",
    "feat only \$ vol" => "SUM,SUM,SUM",
    "feat only \$ vol yag" => "SUM,SUM,SUM",
    "feat only unit vol" => "SUM,SUM,SUM",
    "feat only unit vol ya" => "SUM,SUM,SUM",
    "feat w/o disp \$" => "SUM,SUM,SUM",
    "feat w/o disp \$ % lift" => "AVG,AVG,AVG",
    "feat w/o disp \$ ya" => "SUM,SUM,SUM",
    "feat w/o disp %acv reach" => "MAX,AVG,MAX",
    "feat w/o disp cww" => "MAX,MAX,SUM",
    "feat w/o disp incr \$" => "SUM,SUM,SUM",
    "feat w/o disp incr \$ cya" => "SUM,SUM,SUM",
    "feat w/o disp tdp" => "SUM,MAX,MAX",
    "feat w/o disp unit price" => "AVG,AVG,AVG",
    "feat w/o disp unit price % disc" => "AVG,AVG,AVG",
    "feat w/o disp units" => "SUM,SUM,SUM",
    "feat w/o disp units ya" => "SUM,SUM,SUM",
    "households" => "MAX,SUM,MAX",
    "incr \$" => "SUM,SUM,SUM",
    "incr \$ cya" => "SUM,SUM,SUM",
    "incr \$ ya" => "SUM,SUM,SUM",
    "incr dollars" => "SUM,SUM,SUM",
    "incr dollars +/- chg, ya" => "SUM,SUM,SUM",
    "incr dollars, ya" => "SUM,SUM,SUM",
    "incr units" => "SUM,SUM,SUM",
    "incr units +/- chg, ya" => "SUM,SUM,SUM",
    "incr units, ya" => "SUM,SUM,SUM",
    "incr units ya" => "SUM,SUM,SUM",
    "incremental dollars" => "SUM,SUM,SUM",
    "incremental units" => "SUM,SUM,SUM",
    "incremental volume" => "SUM,SUM,SUM",
    "no promo ~ percent acv" => "MAX,AVG,MAX",
    "no promo ~ sales dollars" => "SUM,SUM,SUM",
    "no promo ~ sales units" => "SUM,SUM,SUM",
    "no promo \$ vol" => "SUM,SUM,SUM",
    "no promo %acv reach" => "MAX,AVG,MAX",
    "no promo unit vol" => "SUM,SUM,SUM",
    "number of stores" => "MAX,MAX,MAX",
    "number of stores selling" => "MAX,MAX,MAX",
    "number of weeks" => "MAX,MAX,MAX",
    "number of weeks selling" => "MAX,MAX,MAX",
    "number of weeks selling +/- chg, ya" => "MAX,MAX,MAX",
    "number of weeks selling, ya" => "MAX,MAX,MAX",
    "percent acv" => "MAX,AVG,MAX",
    "population" => "MAX,SUM,MAX",
    "price decr \$" => "SUM,SUM,SUM",
    "price decr \$ % lift" => "AVG,AVG,AVG",
    "price decr \$ ya" => "SUM,SUM,SUM",
    "price decr %acv reach" => "MAX,AVG,MAX",
    "price decr cww" => "MAX,MAX,SUM",
    "price decr incr \$" => "SUM,SUM,SUM",
    "price decr incr \$ cya" => "SUM,SUM,SUM",
    "price decr only \$" => "SUM,SUM,SUM",
    "price decr only \$ ya" => "SUM,SUM,SUM",
    "price decr only units" => "SUM,SUM,SUM",
    "price decr only units ya" => "SUM,SUM,SUM",
    "price decr tdp" => "SUM,MAX,MAX",
    "price decr unit price" => "AVG,AVG,AVG",
    "price decr unit price % disc" => "AVG,AVG,AVG",
    "price decr units" => "SUM,SUM,SUM",
    "price decr units ya" => "SUM,SUM,SUM",
    "price discount ~ percent acv" => "MAX,AVG,MAX",
    "price discount ~ sales dollars" => "SUM,SUM,SUM",
    "price discount ~ sales units" => "SUM,SUM,SUM",
    "sales dollars" => "SUM,SUM,SUM",
    "sales dollars ya" => "SUM,SUM,SUM",
    "sales per million ~ dollars" => "SUM,AVG,SUM",
    "sales per million ~ sales units" => "SUM,AVG,SUM",
    "sales per point of distribution [\$]" => "SUM,AVG,SUM",
    "sales per point of distribution [u]" => "SUM,AVG,SUM",
    "sales units" => "SUM,SUM,SUM",
    "sales units ya" => "SUM,SUM,SUM",
    "sppd [\$] act chg vs ya" => "SUM,AVG,SUM",
    "sppd [u] act chg vs ya" => "SUM,AVG,SUM",
    "stat case volume" => "SUM,SUM,SUM",
    "store weeks selling" => "MAX,MAX,MAX",
    "subsidized \$" => "SUM,SUM,SUM",
    "subsidized \$ ya" => "SUM,SUM,SUM",
    "subsidized units" => "SUM,SUM,SUM",
    "subsidized units ya" => "SUM,SUM,SUM",
    "tdp" => "AVG,AVG,AVG",
    "tdp ya" => "AVG,AVG,AVG",
    "tdp +/- chg, ya" => "AVG,AVG,AVG",
    "tdp, yago" => "AVG,AVG,AVG",
    "tpr only \$ vol" => "SUM,SUM,SUM",
    "tpr only \$ vol ya" => "SUM,SUM,SUM",
    "tpr only unit vol" => "SUM,SUM,SUM",
    "tpr only unit vol ya" => "SUM,SUM,SUM",
    "u incr vol" => "SUM,SUM,SUM",
    "u incr vol act chg vs ya" => "SUM,SUM,SUM",
    "unit act chg vs prev" => "SUM,SUM,SUM",
    "unit act chg vs ya" => "SUM,SUM,SUM",
    "unit price" => "AVG,AVG,AVG",
    "unit price - act chg vs ya" => "AVG,AVG,AVG",
    "unit sales" => "SUM,SUM,SUM",
    "unit sales ya" => "SUM,SUM,SUM",
    "unit shr" => "AVG,AVG,SUM",
    "unit shr ~ act chg vs prev" => "AVG,AVG,SUM",
    "unit shr ~ act chg vs ya" => "AVG,AVG,SUM",
    "unit vol" => "SUM,SUM,SUM",
    "unit vol ~ prev" => "SUM,SUM,SUM",
    "units" => "SUM,SUM,SUM",
    "units +/- chg, ya" => "SUM,SUM,SUM",
    "units 2ya" => "SUM,SUM,SUM",
    "units, promo" => "SUM,SUM,SUM",
    "units, promo +/- chg, ya" => "SUM,SUM,SUM",
    "units, promo, ya" => "SUM,SUM,SUM",
    "units, ya" => "SUM,SUM,SUM",
    "units / \$mm acv" => "SUM,AVG,SUM",
    "units ya" => "SUM,SUM,SUM",
    "units, any merchandising" => "SUM,SUM,SUM",
    "units, display only" => "SUM,SUM,SUM",
    "units, feature and display" => "SUM,SUM,SUM",
    "units, feature only" => "SUM,SUM,SUM",
    "units, price reduction only" => "SUM,SUM,SUM",
    "volume per \$mm acv" => "SUM,AVG,SUM",
    "volume sales" => "SUM,SUM,SUM",
    "volume share" => "AVG,AVG,SUM",
    "volume, any merchandising" => "SUM,SUM,SUM",
    "volume, display only" => "SUM,SUM,SUM",
    "volume, feature and display" => "SUM,SUM,SUM",
    "volume, feature only" => "SUM,SUM,SUM",
    "volume, price reduction only" => "SUM,SUM,SUM",
    "wghtd avg % prc red, any prc red" => "AVG,AVG,AVG",
    "wghtd avg % prc red, disp only" => "AVG,AVG,AVG",
    "wghtd avg % prc red, feat only" => "AVG,AVG,AVG",
    "wghtd avg base price per unit" => "AVG,AVG,AVG",
    "wghtd avg base price per volume" => "AVG,AVG,AVG",
    "wt avg % predn, feature & display" => "AVG,AVG,AVG",

    #old IRI Xcelerate measures
    "% acv, price redn only" => "MAX,AVG,MAX",
    "% weekly acv, any disp" => "MAX,AVG,MAX",
    "% weekly acv, any feat" => "MAX,AVG,MAX",
    "% weekly acv, feat & disp" => "MAX,AVG,MAX",
    "% weekly acv, prc red only" => "MAX,AVG,MAX",
    "acv wtd dist" => "MAX,AVG,MAX",
    "avg promoted price per unit" => "AVG,AVG,AVG",
    "avg unit price" => "AVG,AVG,AVG",
    "avg weekly dollar sales per \$mm acv" => "AVG,AVG,AVG",
    "base price per unit" => "AVG,AVG,AVG",
    "dollar pct chg vs ya" => "AVG,AVG,AVG",
    "dollar share of type" => "AVG,AVG,AVG",
    "dollar share type chg vs ya" => "AVG,AVG,AVG",
    "merchandised dollars" => "SUM,SUM,SUM",
    "merchandised units" => "SUM,SUM,SUM",
    "merchandised volume" => "SUM,SUM,SUM",
    "promoted units, display only" => "SUM,SUM,SUM",
    "promoted units, feature & display" => "SUM,SUM,SUM",
    "promoted units, feature only" => "SUM,SUM,SUM",
    "promoted units, price redn only" => "SUM,SUM,SUM",
    "share of parent dollars" => "SUM,SUM,SUM",
    "unit pct chg vs ya" => "AVG,AVG,AVG",
    "unit share of type" => "AVG,AVG,AVG",
    "unit share type chg vs ya" => "AVG,AVG,AVG",

    #new IRI measures
    "\$ sales" => "SUM,SUM,SUM",
    "\$ sales ya" => "SUM,SUM,SUM",
    "% stores" => "AVG,AVG,AVG",
    "% stores ya" => "AVG,AVG,AVG",
    "acv weighted distribution ya" => "MAX,AVG,MAX",
    "average weekly acv distribution" => "MAX,AVG,MAX",
    "average weekly acv distribution ya" => "MAX,AVG,MAX",
    "base \$ sales" => "SUM,SUM,SUM",
    "base \$ sales ya" => "SUM,SUM,SUM",
    "base unit sales" => "SUM,SUM,SUM",
    "base unit sales ya" => "SUM,SUM,SUM",
    "dollars per \$mm per item" => "AVG,AVG,AVG",
    "dollars per \$mm per item ya" => "AVG,AVG,AVG",
    "dollars per store selling" => "AVG,AVG,AVG",
    "dollars per store selling ya" => "AVG,AVG,AVG",
    "feat and disp only" => "SUM,SUM,SUM",
    "feat and disp only ya" => "SUM,SUM,SUM",
    "incr \$ sales" => "SUM,SUM,SUM",
    "incr \$ sales ya" => "SUM,SUM,SUM",
    "incr unit sales" => "SUM,SUM,SUM",
    "incr unit sales ya" => "SUM,SUM,SUM",

    #SPINS measures
    "\$ / \$mm acv / item" => "SUM,AVG,SUM",
    "\$ / \$mm acv / item ya" => "SUM,AVG,SUM",
    "\$ / \$mm acv ya" => "SUM,AVG,SUM",
    "\$ / store weeks selling" => "AVG,AVG,AVG",
    "\$ / store weeks selling ya" => "AVG,AVG,AVG",
    "acv wtd dist ya" => "MAX,AVG,MAX",
    "avg wkly \$ / acv wtd dist" => "AVG,AVG,AVG",
    "avg wkly \$ / acv wtd dist ya" => "AVG,AVG,AVG",
    "avg wkly units / acv wtd dist" => "AVG,AVG,AVG",
    "avg wkly units / acv wtd dist ya" => "AVG,AVG,AVG",
    "no promo \$" => "SUM,SUM,SUM",
    "no promo \$ ya" => "SUM,SUM,SUM",
    "no promo units" => "SUM,SUM,SUM",
    "no promo units ya" => "SUM,SUM,SUM",
    "number of stores selling ya" => "MAX,MAX,MAX",
    "number of stores ya" => "MAX,MAX,MAX",
    "number of weeks selling ya" => "MAX,MAX,MAX",
    "promo - display only \$" => "SUM,SUM,SUM",
    "promo - display only \$ ya" => "SUM,SUM,SUM",
    "promo - feat and disp \$" => "SUM,SUM,SUM",
    "promo - feat and disp \$ ya" => "SUM,SUM,SUM",
    "promo - feature only \$" => "SUM,SUM,SUM",
    "promo - feature only \$ ya" => "SUM,SUM,SUM",
    "promo - tpr \$" => "SUM,SUM,SUM",
    "promo - tpr \$ ya" => "SUM,SUM,SUM",
    "units / \$mm acv / item" => "SUM,AVG,SUM",
    "units / \$mm acv / item ya" => "SUM,AVG,SUM",
    "units / \$mm acv ya" => "SUM,AVG,SUM",
    "units / store weeks selling" => "AVG,AVG,AVG",
    "units / store weeks selling ya" => "AVG,AVG,AVG",

    #"" => "",
  );

  $val = $measAggRules{$mName};
  return($val);
}



#-------------------------------------------------------------------------
#
# Provide a "hint" if a particular column name is probably a measure,
# regardless of the kind of data it contains
#

sub DSRmeasures_get_type_hint
{
  my ($val);

  my ($mName) = @_;


  #lowercase the measure name to make matching simpler
  $mName = lc($mName);

  #manually specify column names that are measures (esp in Nielsen Answers)
  my %measHints = (
    "\$" => "measure",
    "\$ cya" => "measure",
    "\$ ya" => "measure",
    "\$ / \$mm acv" => "measure",
    "%acv" => "measure",
    "%acv ya" => "measure",
    "%acv reach" => "measure",
    "%acv reach ya" => "measure",
    "any price decr \$" => "measure",
    "any price decr \$ ya" => "measure",
    "any promo \$" => "measure",
    "any promo \$ ya" => "measure",
    "any promo units" => "measure",
    "any promo units ya" => "measure",
    "avg # items" => "measure",
    "avg unit price" => "measure",
    "avg unit price ya" => "measure",
    "base \$" => "measure",
    "base \$ cya" => "measure",
    "base \$ ya" => "measure",
    "base units" => "measure",
    "base units ya" => "measure",
    "disp w/o feat \$" => "measure",
    "disp w/o feat \$ % lift" => "measure",
    "disp w/o feat \$ ya" => "measure",
    "disp w/o feat cww" => "measure",
    "disp w/o feat incr \$" => "measure",
    "disp w/o feat incr \$ cya" => "measure",
    "disp w/o feat tdp" => "measure",
    "disp w/o feat unit price" => "measure",
    "disp w/o feat unit price % disc" => "measure",
    "disp w/o feat units" => "measure",
    "feat & disp \$" => "measure",
    "feat & disp \$ % lift" => "measure",
    "feat & disp \$ ya" => "measure",
    "feat & disp cww" => "measure",
    "feat & disp incr \$" => "measure",
    "feat & disp incr \$ cya" => "measure",
    "feat & disp tdp" => "measure",
    "feat & disp unit price" => "measure",
    "feat & disp unit price % disc" => "measure",
    "feat & disp units" => "measure",
    "feat w/o disp \$" => "measure",
    "feat w/o disp \$ % lift" => "measure",
    "feat w/o disp \$ ya" => "measure",
    "feat w/o disp cww" => "measure",
    "feat w/o disp incr \$" => "measure",
    "feat w/o disp incr\$ cya" => "measure",
    "feat w/o disp tdp" => "measure",
    "feat w/o disp unit price" => "measure",
    "feat w/o disp unit price % disc" => "measure",
    "feat w/o disp units" => "measure",
    "incr \$" => "measure",
    "incr \$ cya" => "measure",
    "incr \$ ya" => "measure",
    "incr units" => "measure",
    "incr units ya" => "measure",
    "no promo \$" => "measure",
    "no promo \$ ya" => "measure",
    "no promo units" => "measure",
    "no promo units ya" => "measure",
    "number of stores" => "measure",
    "number of stores selling" => "measure",
    "number of weeks" => "measure",
    "number of weeks selling" => "measure",
    "price decr \$" => "measure",
    "price decr \$ % lift" => "measure",
    "price decr \$ ya" => "measure",
    "price decr cww" => "measure",
    "price decr incr \$" => "measure",
    "price decr incr \$ cya" => "measure",
    "price decr tdp" => "measure",
    "price decr unit price" => "measure",
    "price decr unit price % disc" => "measure",
    "price decr units" => "measure",
    "qual \$" => "measure",
    "qual \$ ya" => "measure",
    "store weeks selling" => "measure",
    "subsidized \$" => "measure",
    "subsidized units" => "measure",
    "units" => "measure",
    "units / \$mm acv" => "measure",
    "units ya" => "measure",
  );

  $val = $measHints{$mName};
  return($val);
}



#-------------------------------------------------------------------------
#
# Get the MySQL-formatted aggregation rule for the specified measure in the
# specified dimension.
#

sub DSRmeasures_get_agg_rule
{
  my ($aggRule, $colRuleName, $query, $dbOutput, $key, $status, $calculation);

  my ($db, $dsSchema, $measureID, $dim) = @_;


  #see if we've already looked up this agg rule, return cached result if we have
  $key = "$dsSchema.$dim.$measureID";
  if (defined($_cachedAggRules{$key}))
  {
    return($_cachedAggRules{$key});
  }

  if ($dim eq "p")
  {
    $colRuleName = "prodAggRule";
  }
  elsif ($dim eq "g")
  {
    $colRuleName = "geoAggRule";
  }
  elsif ($dim eq "t")
  {
    $colRuleName = "timeAggRule";
  }
  elsif ($dim eq "merge")
  {
    $colRuleName = "mergeAggRule";
  }

  $query = "SELECT $colRuleName, calculation FROM $dsSchema.measures \
      WHERE ID = $measureID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  meas_db_err($db, $status, $query);
  ($aggRule, $calculation) = $dbOutput->fetchrow_array;

  #handle NULL agg rules
  if (length($aggRule) < 1)
  {
    $aggRule = "None";
  }

  #unless specified separately, merged products use the product agg rule
  if (($dim eq "merge") && ($aggRule eq "None"))
  {
    $query = "SELECT prodAggRule FROM $dsSchema.measures WHERE ID = $measureID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);
    ($aggRule) = $dbOutput->fetchrow_array;
  }

  #if it's a calculated measure, see what kind and maybe we can guess the rule
  if (($aggRule eq "None") && (length($calculation) > 2))
  {
    if ($calculation =~ m/^count/)
    {
      $aggRule = "MAX";
    }
    elsif ($calculation =~ m/^ratio/)
    {
      $aggRule = "AVG";
    }
    elsif ($calculation =~ m/^pct_change/)
    {
      $aggRule = "AVG";
    }
    else
    {
      $aggRule = "SUM";
    }
  }

  #add the agg rule we just looked up to the agg rule cache
  $_cachedAggRules{$key} = $aggRule;

  return($aggRule);
}



#-------------------------------------------------------------------------
#
# Get the calculation for the specified measure in the specified DS
#

sub DSRmeasures_get_calculation
{
  my ($query, $dbOutput, $key, $status, $calculation, $calcBeforeAgg);
  my ($measureName);

  my ($db, $dsSchema, $measureID) = @_;


  #see if we've already looked up this calculation, return cached result if we have
  $key = "$dsSchema.$measureID";
  if (defined($_cachedCalculations{$key}))
  {
    return($_cachedCalculations{$key}, $_cachedCalcBeforeAgg{$key}, $_cachedCalcMeasName{$key});
  }

  $query = "SELECT calculation, calcBeforeAgg, name FROM $dsSchema.measures \
      WHERE ID=$measureID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  meas_db_err($db, $status, $query);
  ($calculation, $calcBeforeAgg, $measureName) = $dbOutput->fetchrow_array;

  if (length($calculation) < 2)
  {
    $calculation = " ";
  }

  #add the calculation we just looked up to the calculation caches
  $_cachedCalculations{$key} = $calculation;
  $_cachedCalcBeforeAgg{$key} = $calcBeforeAgg;
  $_cachedCalcMeasName{$key} = $measureName;

  return($calculation, $calcBeforeAgg, $measureName);
}



#-------------------------------------------------------------------------
#
# Determine if the specified measure needs to be recalculated - in other words,
# if the underlying data source has been updated since it was last calculated.
#

sub DSRmeasures_needs_recalculation
{
  my ($query, $dbOutput, $calculation, $lastUpdate, $lastCalc, $dsID, $status);

  my ($db, $dsSchema, $measureID) = @_;


  #extract our data source ID from the schema name
  $dsSchema =~ m/datasource_(\d+)/;
  $dsID = $1;

  #grab the measure's lastCalc timestamp and calculation from the data source
  $query = "SELECT calculation, UNIX_TIMESTAMP(lastCalc) \
      FROM $dsSchema.measures WHERE ID=$measureID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  meas_db_err($db, $status, $query);
  ($calculation, $lastCalc) = $dbOutput->fetchrow_array;

  #if this isn't a calculated measure, there's obviously nothing to re-calc
  if (length($calculation) < 2)
  {
    return(0);
  }

  #grab the timestamp of the data source's last update
  $query = "SELECT UNIX_TIMESTAMP(lastUpdate) FROM app.dataSources WHERE ID=$dsID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  meas_db_err($db, $status, $query);
  ($lastUpdate) = $dbOutput->fetchrow_array;

  #return 1 if the DS was updated more recently than the measure was calc'd
  if ($lastUpdate > $lastCalc)
  {
    return(1);
  }
  else
  {
    return(0);
  }
}



#-------------------------------------------------------------------------
#
# Parse the specified calculated measure formula, and return an array of the
# numerical measure IDs it depends on for calculation.
#

sub DSRmeasures_formula_dependencies
{
  my ($id, $measType, $measStr);
  my (@measures);

  my ($calculation) = @_;


  undef(@measures);

  #figure out what kind of calculated measure we are
  $calculation =~ m/(.*?)\|/;
  $measType = $1;

  #if we're a sum
  if ($measType eq "sum")
  {
    $calculation =~ m/.*?\|(.*?)\|/;
    $measStr = $1;
    @measures = split(',', $measStr);
  }

  #if we're a difference
  if ($measType eq "difference")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|/;
    @measures = ($1, $2);
  }

  #if we're a ratio
  if ($measType eq "ratio")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|/;
    @measures = ($1, $2);
  }

  #if we're a multiplication
  if ($measType eq "multiplication")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|/;
    $measStr = $1;
    @measures = split(',', $measStr);
  }

  #if we're a percent change between measures
  if ($measType eq "pct_change_meas")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|/;
    @measures = ($1, $2);
  }

  #if we're a change over time
  if ($measType eq "change")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|/;
    @measures = ($1);
  }

  #if we're a percent change
  if ($measType eq "pct_change")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|/;
    @measures = ($1);
  }

  #if we're a lag
  if ($measType eq "lag")
  {
    $calculation =~ m/.*?\|(.*?)\|/;
    @measures = ($1);
  }

  #if we're a lead
  if ($measType eq "lead")
  {
    $calculation =~ m/.*?\|(.*?)\|/;
    @measures = ($1);
  }

  #if we're a moving average
  if ($measType eq "mov_avg")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|/;
    @measures = ($1);
  }

  #if we're a moving total
  if ($measType eq "mov_total")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|/;
    @measures = ($1);
  }

  #if we're a year-to-date
  if ($measType eq "ytd")
  {
    $calculation =~ m/.*?\|(.*?)\|/;
    @measures = ($1);
  }

  #if we're a count
  if ($measType eq "count")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|(.*?)\|(.*?)\|/;
    @measures = ($3);
  }

  #if we're an index
  if ($measType eq "index")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|(.*?)\|/;
    @measures = ($2);
  }

  #if we're a share
  if ($measType eq "share")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|(.*?)\|/;
    @measures = ($2);
  }

=pod
  #if we're a custom 4-function calculator measure
  if ($measType eq "calc")
  {
    $calculation =~ m/.*?\|(.*)\|/;
    $formula = $1;

    cube_calc_calc($db, $dsSchema, $measureID, $formula, $cubeID);
  }
=cut

  return(@measures);
}



#-------------------------------------------------------------------------
#
# Get all of the base measures that the specified calculated measure depends on.
#

sub DSRmeasures_get_calc_measure_dependencies
{
  my ($query, $dbOutput, $calculation, $status, $id);
  my (@baseMeasures, @tmp, @subMeasures);

  my ($db, $dsSchema, $measureID) = @_;


  #get the measure's calculation formula
  $query = "SELECT calculation FROM $dsSchema.measures WHERE ID=$measureID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  meas_db_err($db, $status, $query);
  ($calculation) = $dbOutput->fetchrow_array;

  #handle case where supplied measure is a base measure - this terminates a
  #regression chain
  if (length($calculation) < 2)
  {
    push(@baseMeasures, $measureID);
    return(@baseMeasures);
  }

  #parse out component measures for supplied calc measure
  @tmp = DSRmeasures_formula_dependencies($calculation);

  #run through each dependency, and see if it in turn is a calculated measure
  #we need to get dependencies for - runs regressively to go down as many
  #levels as needed in nested measures
  foreach $id (@tmp)
  {
    @subMeasures = DSRmeasures_get_calc_measure_dependencies($db, $dsSchema, $id);
    push(@baseMeasures, @subMeasures);
  }

  return(@baseMeasures);
}



#-------------------------------------------------------------------------
#
# Get all of the base measures that the specified calculated measure depends on.
#

sub DSRmeasures_recalculate_outofdate_measures
{
  my ($query, $dbOutput, $calculation, $status, $measureID);

  my ($db, $dsSchema, @measureIDs) = @_;
=pod
  #run through each supplied measure
  foreach $measureID (@measureIDs)
  {
    $query = "SELECT calculation FROM $dsSchema.measures WHERE ID=$measureID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    ($calculation) = $dbOutput->fetchrow_array;

  }
=cut
}



#-------------------------------------------------------------------------
#
# Determine if the specified calculated column measure column exists in the
# cube. If it doesn't, create it and calculate its values.
#

sub cube_verify_calculation_column
{
  my ($dbOutput, $status, $query, $rptCubeName, $measCol, $measName, $val);
  my ($calculation, $calcBeforeAgg, $key, $prodID, $geoID, $timeID);
  my (%rowHash);

  my ($db, $dsSchema, $cubeID, $measureID) = @_;


  #if we're already calculated, we can short-circuit out right here
  if ($_measureCalculated{$measureID} == 1)
  {
    return;
  }

  $measCol = "measure_" . $measureID;
  $rptCubeName = "__rptcube_" . $cubeID;
  ($calculation, $calcBeforeAgg, $measName) = DSRmeasures_get_calculation($db, $dsSchema, $measureID);

  #if we haven't already verified the existence of the column in the cube
  if ($_measureColExists{$measureID} < 1)
  {

    #see if the column exists in the database table holding the working cube
    $query = "SELECT * FROM information_schema.columns \
        WHERE table_schema='$dsSchema' AND table_name='$rptCubeName' AND column_name='$measCol' \
        LIMIT 1";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);

    #if the measure column isn't in the cube, add a column for it
    if ($status < 1)
    {
      Lib::BuildCube::cube_telemetry($db, "!!! Measure $measName not explicitly included in cube !!!");
      Lib::BuildCube::cube_telemetry($db, "Adding column for $measName to report data cube");
      Lib::BuildCube::cube_set_status($db, $cubeID, "Adding column for $measName to report");

      #add the column for the measure
      $query = "ALTER TABLE $dsSchema.$rptCubeName \
          ADD COLUMN $measCol DOUBLE DEFAULT NULL";
      $status = $db->do($query);
      meas_db_err($db, $status, $query);
    }

    #elsif it's a base measure and already exists
    elsif (length($calculation) < 2)
    {

      #we can count on the measure column already having rolled up values
      Lib::BuildCube::cube_telemetry($db, "$measName is a base measure that already exists in cube");
      $_measureCalculated{$measureID} = 1;
    }

    #mark the measure as having been seen
    $_measureColExists{$measureID} = 1;
  }

  #if we need to calculate the values for the measure
  if ($_measureCalculated{$measureID} < 1)
  {

    #if it's a base measure, do a roll-up based on aggregation rules
    if (length($calculation) < 2)
    {
      Lib::BuildCube::cube_telemetry($db, "Rolling up base measure $measName");
      Lib::BuildCube::cube_set_status($db, $cubeID, "Rolling up values for $measName");

      #grab a list of all TGP tuples in the cube that have some kind of structure
      $query = "SELECT product, geography, time FROM $dsSchema.__rptcube_$cubeID \
          WHERE product LIKE 'AGG_%' OR product LIKE 'SMT_%' OR product LIKE 'SHS_%' OR geography LIKE 'AGG_%' OR geography LIKE 'SMT_%' OR time LIKE 'AGG_%' OR time LIKE 'SMT_%'";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      meas_db_err($db, $status, $query);

      while (($prodID, $geoID, $timeID) = $dbOutput->fetchrow_array)
      {
        $key = "$prodID.$geoID.$timeID";
        $rowHash{$key} = 1;
      }

      foreach $key (keys %rowHash)
      {
        $key =~ m/^(.*?)\.(.*?)\.(.*)$/;
        $prodID = $1;
        $geoID = $2;
        $timeID = $3;

        $val = DSRmeasures_get_agg_measure_val($db, $dsSchema, $cubeID, $measureID, $prodID, $geoID, $timeID);
        $query = "UPDATE $dsSchema.__rptcube_$cubeID SET $measCol=$val \
            WHERE product='$prodID' AND geography='$geoID' AND time='$timeID'";
        $status = $db->do($query);
        meas_db_err($db, $status, $query);
      }

      $_measureCalculated{$measureID} = 1;
    }

    #else it's a calculated measure that we need to calculate
    #NB: note that we're effectively doing some ghetto recursion here, reaching
    #    back as many levels as needed to get to something we can calculate
    else
    {
      Lib::BuildCube::cube_telemetry($db, "Going to calculate $measName now");
      DSRmeasures_recalculate_cube_measure($db, $dsSchema, $measureID, $cubeID);
    }
  }
}



#-------------------------------------------------------------------------
#
# Calculate and return the numerical value for the specified base measure
# in the specified aggregate or segmentation. Returns an undefined value if
# the measure's aggregation rules aren't defined.
# If it's defined, the timePeriods array contains the already-expanded list
# of items in a "virtual structure" that we need to calculate year ago
# measures like lag for time period aggregates/segments/etc.
#
# Correctly handles cross-calculations (when multiple dimensions are
# aggregates or segments).

sub DSRmeasures_get_agg_measure_val
{
  my ($query, $dbOutput, $aggRule, $aggMembers, $aggVal, $measureName, $dim);
  my ($prodIDs, $geoIDs, $timeIDs, $calculation, $measCol, $rptCubeName);
  my ($valueCacheKey, $key, $status, $dsID, $measName, $timeCount);
  my ($calcBeforeAgg);

  my ($db, $dsSchema, $cubeID, $measureID, $prodID, $geoID, $timeID, $timePeriods) = @_;


  undef($aggVal);

  #extract our dsID from the schema name
  $dsSchema =~ m/datasource_(\d+)/;
  $dsID = $1;

  #make sure our quasi-persistent hashes are OK
  DSRmeasures_cache_ok($dsID, $cubeID);

  #if we've already calculated this measure's value and it's in our cache
  $valueCacheKey = "$measureID.$prodID.$geoID.$timeID.$timePeriods";
  if (defined($_cachedAggMeasureVals{$valueCacheKey}))
  {
    return($_cachedAggMeasureVals{$valueCacheKey});
  }

  #determine if we're doing a structure aggregation on a calculated measure
  ($calculation, $calcBeforeAgg, $measName) = DSRmeasures_get_calculation($db, $dsSchema, $measureID);

  #we're trying to get a structure aggregate of a calculated measure
  #NB: What we want to do is some ghetto regression, working back in the
  #    calculation chain 1 measure at a time until we find an already
  #    calculated measure that we can work from.
  if ((length($calculation) > 2) && ($cubeID > 1))
  {

    #see if the calculation has already been done for the calculated measure
    $measCol = "measure_" . $measureID;
    $rptCubeName = "__rptcube_" . $cubeID;

    #make sure the measure we need is already in the cube
    if ($_measureColExists{$measureID} < 1)
    {

      #if we haven't already seen the column, see if it exists
      $query = "SELECT * FROM information_schema.columns \
          WHERE table_schema='$dsSchema' AND table_name='$rptCubeName' AND column_name='$measCol' \
          LIMIT 1";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      meas_db_err($db, $status, $query);

      #if the measure column isn't in the cube, add a column for it
      if ($status < 1)
      {
        Lib::BuildCube::cube_telemetry($db, "!!! Measure $measName not explicitly included in cube !!!");
        Lib::BuildCube::cube_telemetry($db, "Adding column for $measName to report data cube");

        #add the column for the measure
        $query = "ALTER TABLE $dsSchema.$rptCubeName \
            ADD COLUMN $measCol DOUBLE DEFAULT NULL";
        $status = $db->do($query);
        meas_db_err($db, $status, $query);

        Lib::BuildCube::cube_telemetry($db, "Values for $measName will be calculated as part of current measure calculation");
      }

      #mark the measure as having been seen
      $_measureColExists{$measureID} = 1;
    }


    $query = "SELECT $measCol FROM $dsSchema.$rptCubeName \
        WHERE product = '$prodID' AND geography = '$geoID' AND time = '$timeID'";

    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);
    ($aggVal) = $dbOutput->fetchrow_array;

    #if the calculation is done, return the value from the cube
    if (defined($aggVal))
    {
      return($aggVal);
    }

    #if we're here and a segmentation (not a segment), we're needed for an
    #index/share/variance calculation and we need to drop back to the facts table
    if ($prodID =~ m/^SEG_/)
    {
      goto NON_CUBE_AGGREGATION_CODE;
    }

    #if we're here, the calculation wasn't done - let's do it!
    DSRmeasures_recalculate_cube_measure($db, $dsSchema, $measureID, $cubeID, $prodID, $geoID, $timeID);

    #grab the now-calculated value, and return it
    $query = "SELECT $measCol, COUNT(time) FROM $dsSchema.$rptCubeName \
        WHERE product = '$prodID' AND geography = '$geoID' AND time = '$timeID'";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);
    ($aggVal, $timeCount) = $dbOutput->fetchrow_array;

    #this is a little ugly, but maybe the user wants a lag/pct_change/etc
    #measure calculated, and we need to access a time period that isn't
    #included in the cube. To do that, we have to drop out of the cube-
    #specific aggregation code and go to the facts table in the data source
    if ((!(defined($aggVal))) && ($timeCount == 0))
    {
      goto NON_CUBE_AGGREGATION_CODE;
    }

    if (!defined($aggVal))
    {
      $_cachedAggMeasureVals{$valueCacheKey} = "NULL";
    }
    else
    {
      $_cachedAggMeasureVals{$valueCacheKey} = $aggVal;
    }

    return($aggVal);
  }

  NON_CUBE_AGGREGATION_CODE:

  #determine which dimension the aggregate or segment "lives" in
  if (($prodID =~ m/^AGG/) || ($prodID =~ m/^SMT/) || ($prodID =~ m/^SEG/) ||
      ($prodID =~ m/^SHS/))
  {
    $dim = "p";
  }
  elsif (($geoID =~ m/^AGG/) || ($geoID =~ m/^SMT/) || ($geoID =~ m/^SEG/) ||
         ($geoID =~ m/^SHS/))
  {
    $dim = "g";
  }
  elsif (($timeID =~ m/^AGG/) || ($timeID =~ m/^SMT/) || ($timeID =~ m/^SEG/) ||
      ($timeID =~ m/^SHS/))
  {
    $dim = "t";
  }
  elsif (length($timePeriods) > 1)
  {
    $dim = "t";
  }

  #get the agg rule we're going to be using, return "undefined" if none
  $aggRule = DSRmeasures_get_agg_rule($db, $dsSchema, $measureID, $dim);

  if ((length($aggRule) < 2) || ($aggRule eq "None"))
  {
    #this is a ridiculous hack, but should only be needed in rare occasions
    #where user does something crazy like run a share of an aggregate of a
    #calculated measure
    if (($dim eq "p") && (($prodID =~ m/^AGG/) || ($prodID =~ m/^SMT/) ||
                          ($prodID =~ m/^SEG/)))
    {
      if ($calculation =~ m/^count/)
      {
        $aggRule = "MAX";
      }
      elsif ($calculation =~ m/^ratio/)
      {
        $aggRule = "AVG";
      }
      else
      {
        $aggRule = "SUM";
      }
    }

    elsif ($dim eq "t")
    {
      if ($calculation =~ m/^count/)
      {
        $aggRule = "MAX";
      }
      elsif ($calculation =~ m/^ratio/)
      {
        $aggRule = "AVG";
      }
      else
      {
        $aggRule = "SUM";
      }
    }

    else
    {
      return("NULL");
    }
  }

  #build up our measure's column name
  $measureName = "measure_" . $measureID;

  #expand out all of the aggregates or segments in our specified dimension IDs
  #NB: We're using global-to-this-module hashed caches to store memberships
  #    we've already looked up so we're not constantly hitting the database
  #NB: The cache is cleared whenever we switch data sources, so it's OK to use
  #    just the item or structure ID as the hash key
  $key = "$prodID";
  if ($prodID =~ m/^AGG_\d+/)
  {
    if (length($_prodMemberships{$key}) < 1)
    {
      $prodIDs = DSRagg_expand_items($db, $dsSchema, "p", $prodID);
      $_prodMemberships{$key} = $prodIDs;
    }
    else
    {
      $prodIDs = $_prodMemberships{$key};
    }
  }
  elsif ($prodID =~ m/SMT_\d+/)
  {
    if (length($_prodMemberships{$key}) < 1)
    {
      $prodIDs = DSRsegment_expand_items($db, $dsSchema, "p", $prodID);
      $_prodMemberships{$key} = $prodIDs;
    }
    else
    {
      $prodIDs = $_prodMemberships{$key};
    }
  }
  elsif ($prodID =~ m/SEG_\d+/)
  {
    if (length($_prodMemberships{$key}) < 1)
    {
      $prodIDs = DSRsegmentation_expand_items($db, $dsSchema, "p", $prodID);
      $_prodMemberships{$key} = $prodIDs;
    }
    else
    {
      $prodIDs = $_prodMemberships{$key};
    }
  }
  elsif ($prodID =~ m/SHS_\d+/)
  {
    if (length($_prodMemberships{$key}) < 1)
    {
      $prodIDs = DSRseghier_expand_items($db, $dsSchema, "p", $prodID);
      $_prodMemberships{$key} = $prodIDs;
    }
    else
    {
      $prodIDs = $_prodMemberships{$key};
    }
  }
  else
  {
    $prodIDs = $prodID;
  }

  $key = "$geoID";
  if ($geoID =~ m/^AGG_\d+/)
  {
    if (length($_geoMemberships{$key}) < 1)
    {
      $geoIDs = DSRagg_expand_items($db, $dsSchema, "g", $geoID);
      $_geoMemberships{$key} = $geoIDs;
    }
    else
    {
      $geoIDs = $_geoMemberships{$key};
    }
  }
  elsif ($geoID =~ m/SMT_\d+/)
  {
    if (length($_geoMemberships{$key}) < 1)
    {
      $geoIDs = DSRsegment_expand_items($db, $dsSchema, "g", $geoID);
      $_geoMemberships{$key} = $geoIDs;
    }
    else
    {
      $geoIDs = $_geoMemberships{$key};
    }
  }
  elsif ($geoID =~ m/SEG_\d+/)
  {
    if (length($_geoMemberships{$key}) < 1)
    {
      $geoIDs = DSRsegmentation_expand_items($db, $dsSchema, "g", $geoID);
      $_geoMemberships{$key} = $geoIDs;
    }
    else
    {
      $geoIDs = $_geoMemberships{$key};
    }
  }
  elsif ($geoID =~ m/SHS_\d+/)
  {
    if (length($_geoMemberships{$key}) < 1)
    {
      $geoIDs = DSRseghier_expand_items($db, $dsSchema, "g", $geoID);
      $_geoMemberships{$key} = $geoIDs;
    }
    else
    {
      $geoIDs = $_geoMemberships{$key};
    }
  }
  else
  {
    $geoIDs = $geoID;
  }

  if (defined($timePeriods))
  {
    $timeIDs = $timePeriods;
  }
  elsif ($timeID =~ m/^AGG_\d+/)
  {
    $timeIDs = DSRagg_expand_items($db, $dsSchema, "t", $timeID);
  }
  elsif ($timeID =~ m/SMT_\d+/)
  {
    $timeIDs = DSRsegment_expand_items($db, $dsSchema, "t", $timeID);
  }
  elsif ($timeID =~ m/SEG_\d+/)
  {
    $timeIDs = DSRsegmentation_expand_items($db, $dsSchema, "t", $timeID);
  }
  elsif ($timeID =~ m/SHS_\d+/)
  {
    $timeIDs = DSRseghier_expand_items($db, $dsSchema, "t", $timeID);
  }
  else
  {
    $timeIDs = $timeID;
  }

  #if there are no items in one or more dimensions, return NULL
  if ((length($prodIDs) < 1) || (length($geoIDs) < 1) ||
      (length($timeIDs) < 1))
  {
    $_cachedAggMeasureVals{$valueCacheKey} = "NULL";
    return("NULL");
  }

  #build our SQL query, based on dimension, to calculate the agg/seg value
  $query = "SELECT $aggRule($measureName) FROM $dsSchema.facts \
      WHERE productID IN ($prodIDs) AND geographyID IN ($geoIDs) AND timeID IN ($timeIDs)";

  #get the aggregate value from the database, and return
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  meas_db_err($db, $status, $query);
  ($aggVal) = $dbOutput->fetchrow_array;

  #store the calculated value in-memory for possible future use to save both
  #CPU and I/O in the database
  if (!defined($aggVal))
  {
    $_cachedAggMeasureVals{$valueCacheKey} = "NULL";
    $aggVal = "NULL";
  }
  else
  {
    $_cachedAggMeasureVals{$valueCacheKey} = $aggVal;
  }

  return($aggVal);
}



#-------------------------------------------------------------------------
#
# Calculate and return the average value for the specified base measure
# in the specified aggregate or segmentation. Heavily depends on the master
# aggregation DSRmeasures_get_agg_measure_val subroutine. This subroutine
# is used primarily for index measure calculations.
#

sub DSRmeasures_get_avg_measure_val
{
  my ($query, $dbOutput, $status, $avgVal, $totalVal, $numItems);
  my ($dimItemsCount, $prodIDs);

  my ($db, $dsSchema, $cubeID, $measureID, $prodID, $geoID, $timeID) = @_;


  #get the total value for the specified tuple
  $totalVal = DSRmeasures_get_agg_measure_val($db, $dsSchema, $cubeID, $measureID, $prodID, $geoID, $timeID);

  #get the number of base items contained in the tuple
  #NB: use our structure membership hash to avoid hitting the database for every
  #    single lookup. Because we call DSRmeasures_get_agg_measure_val before
  #    getting here, we effectively are always going to hit the cache
  $key = "$prodID";
  $dimItemsCount = 0;
  if (length($_prodMemberships{$key}) > 0)
  {
    $prodIDs = $_prodMemberships{$key};
  }
  elsif ($prodID =~ m/^AGG_\d+/)
  {
    $prodIDs = DSRagg_expand_items($db, $dsSchema, "p", $prodID);
    $_prodMemberships{$key} = $prodIDs;
  }
  elsif ($prodID =~ m/SMT_\d+/)
  {
    $prodIDs = DSRsegment_expand_items($db, $dsSchema, "p", $prodID);
    $_prodMemberships{$key} = $prodIDs;
  }
  elsif ($prodID =~ m/SEG_\d+/)
  {
    $prodIDs = DSRsegmentation_expand_items($db, $dsSchema, "p", $prodID);
    $_prodMemberships{$key} = $prodIDs;
  }
  elsif ($prodID =~ m/SHS_\d+/)
  {
    $prodIDs = DSRseghier_expand_items($db, $dsSchema, "p", $prodID);
    $_prodMemberships{$key} = $prodIDs;
  }
  else
  {
    $prodIDs = $prodID;
  }

  $dimItemsCount = $prodIDs =~ tr/,//;
  $dimItemsCount++;

  #avoid division by zero
  if ($dimItemsCount == 0)
  {
    $avgVal = "NULL";
  }

  #calculate and return average value
  else
  {
    $avgVal = $totalVal / $dimItemsCount;
  }

  return($avgVal);
}


#-------------------------------------------------------------------------
#
# Determine if there's a matching year ago timeperiod for every timeperiod
# in the DSR structure passed to us. If so, return the year-ago timeperiods
# in an array. Used by time-based measure calculations when dealing with
# aggregates, segs, and so on in the time period field.
#

sub DSRMeasures_get_yago_array
{
  my ($query, $dbOutput, $curPeriodsStr, $noMatch, $curID, $status);
  my ($timeDuration, $timeType, $timeEnd, $yagID, $key, $value);
  my (@yagPeriods, @curPeriods);

  my ($db, $dsSchema, $timeID) = @_;


  #see if we've already found YAGO periods for this time period, return
  #cached result if we have
  $key = "$dsSchema.$timeID";
  $value = $_cachedYagoTimes{$key};
  if (defined($value))
  {
    if ($value eq "UNDEF")
    {
      undef(@yagPeriods);
    }
    else
    {
      @yagPeriods = split(',', $value);
    }

    return(@yagPeriods);
  }

  undef(@yagPeriods);

  #if we're a time aggregate, get our members
  if ($timeID =~ m/AGG_\d+/)
  {
    $curPeriodsStr = DSRagg_expand_items($db, $dsSchema, "t", $timeID);
  }

  #if we're a time segment, get our members
  if ($timeID =~ m/SMT_\d+/)
  {
    $curPeriodsStr = DSRsegment_expand_items($db, $dsSchema, "t", $timeID);
  }

  #if we're a time segmentation, get our members
  if ($timeID =~ m/SEG_\d+/)
  {
    $curPeriodsStr = DSRsegmentation_expand_items($db, $dsSchema, "t", $timeID);
  }

  #if we're a time segmentation hierarchy level, get our members
  if ($timeID =~ m/SHS_\d+/)
  {
    $curPeriodsStr = DSRseghier_expand_items($db, $dsSchema, "t", $timeID);
  }

  #if we're a base item, we just need the matching YAGO for our one item
  if ($timeID =~ m/^\d+$/)
  {
    $curPeriodsStr = $timeID;
  }

  #for each of the current periods, get our type and corresponding YAG period
  @curPeriods = split(',', $curPeriodsStr);
  $noMatch = 0;
  foreach $curID (@curPeriods)
  {
    #get our curID's corresponding endDate datestamp
    $query = "SELECT duration, type, endDate FROM $dsSchema.timeperiods \
        WHERE ID=$curID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);
    ($timeDuration, $timeType, $timeEnd) = $dbOutput->fetchrow_array;

    #see if our curID has a matching "Year Ago" period
    $query = "SELECT ID FROM $dsSchema.timeperiods \
        WHERE duration=$timeDuration AND type=$timeType AND endDate = DATE_SUB('$timeEnd', INTERVAL 52 WEEK)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);
    ($yagID) = $dbOutput->fetchrow_array;

    #if we didn't find a matching year ago entry, let us know that there's no
    #year ago match for this time structure
    if (!defined($yagID))
    {
      $noMatch = 1;
    }
    else
    {
      push(@yagPeriods, $yagID);
    }
  }

  if ($noMatch == 1)
  {
    $_cachedYagoTimes{$key} = "UNDEF";
    undef(@yagPeriods);
  }
  else
  {
    $value = join(',', @yagPeriods);
    $_cachedYagoTimes{$key} = $value;
  }

  return(@yagPeriods);
}



#-------------------------------------------------------------------------
#
# Determine if there's a matching year ahead timeperiod for every timeperiod
# in the DSR structure passed to us. If so, return the year-ahead timeperiods
# in an array. Used by time-based measure calculations when dealing with
# aggregates, segs, and so on in the time period field (esp lead measure).
#

sub DSRMeasures_get_yahead_array
{
  my ($query, $dbOutput, $curPeriodsStr, $noMatch, $curID, $status);
  my ($timeDuration, $timeType, $timeEnd, $yaheadID, $key, $value);
  my (@yaheadPeriods, @curPeriods);

  my ($db, $dsSchema, $timeID) = @_;


  #see if we've already found Yahead periods for this time period, return
  #cached result if we have
  $key = "$dsSchema.$timeID";
  $value = $_cachedYaheadTimes{$key};
  if (defined($value))
  {
    if ($value eq "UNDEF")
    {
      undef(@yaheadPeriods);
    }
    else
    {
      @yaheadPeriods = split(',', $value);
    }

    return(@yaheadPeriods);
  }

  undef(@yaheadPeriods);

  #if we're a time aggregate, get our members
  if ($timeID =~ m/AGG_\d+/)
  {
    $curPeriodsStr = DSRagg_expand_items($db, $dsSchema, "t", $timeID);
  }

  #if we're a time segment, get our members
  if ($timeID =~ m/SMT_\d+/)
  {
    $curPeriodsStr = DSRsegment_expand_items($db, $dsSchema, "t", $timeID);
  }

  #if we're a time segmentation, get our members
  if ($timeID =~ m/SEG_\d+/)
  {
    $curPeriodsStr = DSRsegmentation_expand_items($db, $dsSchema, "t", $timeID);
  }

  #if we're a time segmentation hierarchy level, get our members
  if ($timeID =~ m/SHS_\d+/)
  {
    $curPeriodsStr = DSRseghier_expand_items($db, $dsSchema, "t", $timeID);
  }

  #if we're a base item, we just need the matching YAGO for our one item
  if ($timeID =~ m/^\d+$/)
  {
    $curPeriodsStr = $timeID;
  }

  #for each of the current periods, get our type and corresponding YAHEAD period
  @curPeriods = split(',', $curPeriodsStr);
  $noMatch = 0;
  foreach $curID (@curPeriods)
  {
    #get our curID's corresponding endDate datestamp
    $query = "SELECT duration, type, endDate FROM $dsSchema.timeperiods \
        WHERE ID=$curID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);
    ($timeDuration, $timeType, $timeEnd) = $dbOutput->fetchrow_array;

    #see if our curID has a matching "Year Ahead" period
    $query = "SELECT ID FROM $dsSchema.timeperiods \
        WHERE duration=$timeDuration AND type=$timeType AND endDate = DATE_ADD('$timeEnd', INTERVAL 52 WEEK)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);
    ($yaheadID) = $dbOutput->fetchrow_array;

    #if we didn't find a matching year ahead entry, let us know that there's no
    #year ahead match for this time structure
    if (!defined($yaheadID))
    {
      $noMatch = 1;
    }
    else
    {
      push(@yaheadPeriods, $yaheadID);
    }
  }

  if ($noMatch == 1)
  {
    $_cachedYaheadTimes{$key} = "UNDEF";
    undef(@yaheadPeriods);
  }
  else
  {
    $value = join(',', @yaheadPeriods);
    $_cachedYaheadTimes{$key} = $value;
  }

  return(@yaheadPeriods);
}



#-------------------------------------------------------------------------
#
# Return an array of the IDs of every calculated measure in the specified
# data cube;
#

sub DSRmeasures_get_cube_calc_measures_array
{
  my ($query, $dbOutput, $measureID, $calc, $status);
  my (@measureIDs, @calcMeasureIDs);
  my ($db, $dsSchema, $cubeID) = @_;

  #get all of the base measure items in the cube
  @measureIDs = datasel_get_selected_base_items($db, $cubeID, "m");


  #for each of the measure IDs, see if we have a formula to calculate it
  foreach $measureID (@measureIDs)
  {
    $query = "SELECT calculation FROM $dsSchema.measures WHERE ID=$measureID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);
    ($calc) = $dbOutput->fetchrow_array;

    #if the measure is calculated, add it to the array we're going to return
    if (length($calc) > 1)
    {
      push(@calcMeasureIDs, $measureID);
    }
  }

  return(@calcMeasureIDs);
}



#-------------------------------------------------------------------------
#
# Return a human-friendly name for the specified calculated measure type.
#

sub DSRmeasures_get_calc_type_name
{
  my ($type);
  my ($mType) = @_;

  #manually specify the aggregation rules hashes
  my %calcMeasTypes = (
    "sum" => "Sum",
    "difference" => "Difference",
    "ratio" => "Ratio",
    "multiplication" => "Multiplication",
    "pct_change_meas" => "% Change Between Measures",
    "change" => "Change",
    "pct_change" => "% Change",
    "lag" => "Lag",
    "lead" => "Lead",
    "mov_avg" => "Moving Average",
    "mov_total" => "Moving Total",
    "ytd" => "Year to Date",
    "count" => "Count",
    "index" => "Index",
    "share" => "Share",
    "calc" => "Calculator",
  );

  $type = $calcMeasTypes{$mType};
  return($type);
}


#-------------------------------------------------------------------------
#
# Check one or more measures to see if a calculated measure that depends on
# them can be safely recalculated for just records updated by a particular
# data source update, or if all records need to be recalculated.
#

sub DSRmeasures_dependency_check
{
  my ($query, $dbOutput, $status, $calculation, $ok, $id);
  my (@measureIDs);

  my ($db, $dsSchema, $measures) = @_;


  $ok = 1;

  #split the measure string into an array of measure IDs to be summed
  @measureIDs = split(/,/, $measures);

  foreach $id (@measureIDs)
  {
    $query = "SELECT calculation FROM $dsSchema.measures WHERE ID=$id";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);
    ($calculation) = $dbOutput->fetchrow_array;

    if (($calculation =~ m/^share/) || ($calculation =~ m/^count/) ||
        ($calculation =~ m/^change/) || ($calculation =~ m/^pct_change/) ||
        ($calculation =~ m/^lag/) || ($calculation =~ m/^lead/) ||
        ($calculation =~ m/^ytd/) || ($calculation =~ m/^mov_avg/) ||
        ($calculation =~ m/^mov_total/) || ($calculation =~ m/^index/))
    {
      $ok = 0;
    }
  }

  return($ok);
}



#-------------------------------------------------------------------------
#
# Calculate the sum of two or more measures for the specified data source.
#

sub DSRmeasures_calc_sum
{
  my ($query, $dbOutput, $colName, $measureQuery, $measureName, $id, $recalc);
  my ($status, $whereClause, $dsID, $shortCutOK);
  my (@measureIDs);

  my ($db, $dsSchema, $measureID, $measures, $dsUpdateID) = @_;


  #make sure we haven't already been calculated as part of another measure's
  #calculation process
  $recalc = DSRmeasures_needs_recalculation($db, $dsSchema, $measureID);
  if ($recalc == 0)
  {
    return;
  }

  #let the DSR know we're currently calculating this measure
  $query = "UPDATE $dsSchema.measures SET lastCalc=NULL WHERE ID=$measureID";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);

  $colName = "measure_" . $measureID;

  #split the measure string into an array of measure IDs to be summed
  @measureIDs = split(/,/, $measures);

  #if we need to recalc any of our base measures, do it
  foreach $id (@measureIDs)
  {

    #make sure there isn't a circular definition
    if ($id eq $measureID)
    {
      if ($dsSchema =~ m/^datasource_(\d+)/)
      {
        $dsID = $1;
      }

      $query = "UPDATE dataSources \
          SET status='ERROR: circular measure definition in $measureID' \
          WHERE ID=$dsID";
      $status = $db->do($query);
      meas_db_err($db, $status, $query);

      DSRutil_clear_status($db);

      die("Circular calculated measure definition in $measureID in $dsID!")
    }

    $recalc = DSRmeasures_needs_recalculation($db, $dsSchema, $id);
    if ($recalc)
    {
      DSRmeasures_recalculate_measure($db, $dsSchema, $id, $dsUpdateID);
    }
  }

  #determine if we only need to run the calculation for records that were
  #added/updated by this datasource run
  $whereClause = "";
  if ($dsUpdateID > 0)
  {
    $shortCutOK = DSRmeasures_dependency_check($db, $dsSchema, $measures);
    if ($shortCutOK > 0)
    {
      $whereClause = "WHERE updateID = $dsUpdateID";
    }
  }

  #build up the measure addition portion of the SQL query
  $measureQuery = "";
  foreach $id (@measureIDs)
  {
    $measureName = "measure_$id";
    $measureQuery .= "$measureName" . "+";
  }

  #chop off the final trailing plus
  chop($measureQuery);

  $query = "UPDATE $dsSchema.facts SET $colName = $measureQuery $whereClause";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);

  #let the DSR know we're done calculating this measure
  $query = "UPDATE $dsSchema.measures SET lastCalc=NOW() WHERE ID=$measureID";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------
#
# Calculate the difference of two measures for the specified data source.
#

sub DSRmeasures_calc_difference
{
  my ($query, $dbOutput, $colName, $measureQuery, $measureName, $whereClause);
  my ($measureName1, $measureName2, $recalc, $status, $measures, $shortCutOK);

  my ($db, $dsSchema, $measureID, $measure1, $measure2, $dsUpdateID) = @_;


  #make sure we haven't already been calculated as part of another measure's
  #calculation process
  $recalc = DSRmeasures_needs_recalculation($db, $dsSchema, $measureID);
  if ($recalc == 0)
  {
    return;
  }

  #let the DSR know we're currently calculating this measure
  $query = "UPDATE $dsSchema.measures SET lastCalc=NULL WHERE ID=$measureID";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);

  #if our to be sub'd from measure needs to be recalculated, do it
  $recalc = DSRmeasures_needs_recalculation($db, $dsSchema, $measure1);
  if ($recalc)
  {
    DSRmeasures_recalculate_measure($db, $dsSchema, $measure1, $dsUpdateID);
  }

  #if our subtracting measure needs to be recalculated, do it
  $recalc = DSRmeasures_needs_recalculation($db, $dsSchema, $measure2);
  if ($recalc)
  {
    DSRmeasures_recalculate_measure($db, $dsSchema, $measure2, $dsUpdateID);
  }

  #determine if we only need to run the calculation for records that were
  #added/updated by this datasource run
  $whereClause = "";
  if ($dsUpdateID > 0)
  {
    $measures = "$measure1,$measure2";
    $shortCutOK = DSRmeasures_dependency_check($db, $dsSchema, $measures);
    if ($shortCutOK > 0)
    {
      $whereClause = "WHERE updateID = $dsUpdateID";
    }
  }

  $colName = "measure_" . $measureID;

  #build up the measure subtraction portion of the SQL query
  $measureName1 = "measure_" . $measure1;
  $measureName2 = "measure_" . $measure2;
  $measureQuery = "$measureName1 - $measureName2";

  $query = "UPDATE $dsSchema.facts SET $colName = $measureQuery $whereClause";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);

  #let the DSR know we're done calculating this measure
  $query = "UPDATE $dsSchema.measures SET lastCalc=NOW() WHERE ID=$measureID";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------
#
# Calculate the multiple of two or more measures for the specified data source.
#

sub DSRmeasures_calc_multiplication
{
  my ($query, $dbOutput, $colName, $measureQuery, $measureName, $recalc, $id);
  my ($status, $whereClause, $shortCutOK);
  my @measureIDs;

  my ($db, $dsSchema, $measureID, $measures, $constant, $dsUpdateID) = @_;


  #make sure we haven't already been calculated as part of another measure's
  #calculation process
  $recalc = DSRmeasures_needs_recalculation($db, $dsSchema, $measureID);
  if ($recalc == 0)
  {
    return;
  }

  #let the DSR know we're currently calculating this measure
  $query = "UPDATE $dsSchema.measures SET lastCalc=NULL WHERE ID=$measureID";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);

  $colName = "measure_" . $measureID;

  #split the measure string into an array of measure IDs to be multiplied
  @measureIDs = split(/,/, $measures);

  #if we need to recalc any of our base measures, do it
  foreach $id (@measureIDs)
  {
    $recalc = DSRmeasures_needs_recalculation($db, $dsSchema, $id);
    if ($recalc)
    {
      DSRmeasures_recalculate_measure($db, $dsSchema, $id, $dsUpdateID);
    }
  }

  #determine if we only need to run the calculation for records that were
  #added/updated by this datasource run
  $whereClause = "";
  if ($dsUpdateID > 0)
  {
    $shortCutOK = DSRmeasures_dependency_check($db, $dsSchema, $measures);
    if ($shortCutOK > 0)
    {
      $whereClause = "WHERE updateID = $dsUpdateID";
    }
  }

  #build up the measure multiplication portion of the SQL query
  $measureQuery = "";
  foreach $id (@measureIDs)
  {
    $measureName = "measure_$id";
    $measureQuery .= "$measureName" . "*";
  }
  $measureQuery .= $constant;

  $query = "UPDATE $dsSchema.facts SET $colName = $measureQuery $whereClause";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);

  #let the DSR know we're done calculating this measure
  $query = "UPDATE $dsSchema.measures SET lastCalc=NOW() WHERE ID=$measureID";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------
#
# Calculate the ratio of two measures for the specified data source.
#

sub DSRmeasures_calc_ratio
{
  my ($query, $dbOutput, $colName, $anteName, $consName, $recalc, $status);
  my ($whereClause, $measures, $shortCutOK);

  my ($db, $dsSchema, $measureID, $antecedentID, $consequentID, $convertPct, $dsUpdateID) = @_;


  #make sure we haven't already been calculated as part of another measure's
  #calculation process
  $recalc = DSRmeasures_needs_recalculation($db, $dsSchema, $measureID);
  if ($recalc == 0)
  {
    return;
  }

  #let the DSR know we're currently calculating this measure
  $query = "UPDATE $dsSchema.measures SET lastCalc=NULL WHERE ID=$measureID";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);

  #if our antecedent measure needs to be recalculated, do it
  $recalc = DSRmeasures_needs_recalculation($db, $dsSchema, $antecedentID);
  if ($recalc)
  {
    DSRmeasures_recalculate_measure($db, $dsSchema, $antecedentID, $dsUpdateID);
  }

  #if our consequent measure needs to be recalculated, do it
  $recalc = DSRmeasures_needs_recalculation($db, $dsSchema, $consequentID);
  if ($recalc)
  {
    DSRmeasures_recalculate_measure($db, $dsSchema, $consequentID, $dsUpdateID);
  }

  #determine if we only need to run the calculation for records that were
  #added/updated by this datasource run
  $whereClause = "";
  if ($dsUpdateID > 0)
  {
    $measures = "$antecedentID,$consequentID";
    $shortCutOK = DSRmeasures_dependency_check($db, $dsSchema, $measures);
    if ($shortCutOK > 0)
    {
      $whereClause = "WHERE updateID = $dsUpdateID";
    }
  }

  $colName = "measure_" . $measureID;
  $anteName = "measure_" . $antecedentID;
  $consName = "measure_" . $consequentID;

  if ($convertPct == 1)
  {
    $query = "UPDATE IGNORE $dsSchema.facts \
        SET $colName = ($anteName / $consName) * 100 $whereClause";
  }
  else
  {
    $query = "UPDATE IGNORE $dsSchema.facts \
        SET $colName = $anteName / $consName $whereClause";
  }
  $status = $db->do($query);
  meas_db_err($db, $status, $query);

  #let the DSR know we're done calculating this measure
  $query = "UPDATE $dsSchema.measures SET lastCalc=NOW() WHERE ID=$measureID";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------
#
# Calculate the % change between two measures for the specified data source.
#

sub DSRmeasures_calc_pct_change_meas
{
  my ($query, $dbOutput, $colName, $meas1Name, $meas2Name, $recalc, $status);
  my ($whereClause, $measures, $shortCutOK);

  my ($db, $dsSchema, $measureID, $measure1, $measure2, $dsUpdateID) = @_;


  #make sure we haven't already been calculated as part of another measure's
  #calculation process
  $recalc = DSRmeasures_needs_recalculation($db, $dsSchema, $measureID);
  if ($recalc == 0)
  {
    return;
  }

  #let the DSR know we're currently calculating this measure
  $query = "UPDATE $dsSchema.measures SET lastCalc=NULL WHERE ID=$measureID";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);

  #if our first comparison measure needs to be recalculated, do it
  $recalc = DSRmeasures_needs_recalculation($db, $dsSchema, $measure1);
  if ($recalc)
  {
    DSRmeasures_recalculate_measure($db, $dsSchema, $measure1, $dsUpdateID);
  }

  #if our second comparison measure needs to be recalculated, do it
  $recalc = DSRmeasures_needs_recalculation($db, $dsSchema, $measure2);
  if ($recalc)
  {
    DSRmeasures_recalculate_measure($db, $dsSchema, $measure2, $dsUpdateID);
  }

  #determine if we only need to run the calculation for records that were
  #added/updated by this datasource run
  $whereClause = "";
  if ($dsUpdateID > 0)
  {
    $measures = "$measure1,$measure2";
    $shortCutOK = DSRmeasures_dependency_check($db, $dsSchema, $measures);
    if ($shortCutOK > 0)
    {
      $whereClause = "WHERE updateID = $dsUpdateID";
    }
  }

  $colName = "measure_" . $measureID;
  $meas1Name = "measure_" . $measure1;
  $meas2Name = "measure_" . $measure2;

  $query = "UPDATE IGNORE $dsSchema.facts \
      SET $colName = ($meas1Name - $meas2Name) / $meas2Name * 100 $whereClause";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);

  #let the DSR know we're done calculating this measure
  $query = "UPDATE $dsSchema.measures SET lastCalc=NOW() WHERE ID=$measureID";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------
#
# Calculate the specified Change measure for the specified data source.
# XXX - For now, assumes we're calculating the change from the past year
#

sub DSRmeasures_calc_change
{
  my ($query, $dbOutput, $dbOutput1, $yagID, $yagName, $curID, $curName);
  my ($q_curName, $colName, $baseColName, $yagType, $yagEnd, $curEnd);
  my ($yagDuration, $productID, $geographyID, $baseValue, $val, $status);
  my ($yagBaseValue, $recalc, $whereClause);
  my (%timeHash, %updateTimeHash);

  my ($db, $dsSchema, $measureID, $baseMeasureID, $dsUpdateID) = @_;


  #make sure we haven't already been calculated as part of another measure's
  #calculation process
  $recalc = DSRmeasures_needs_recalculation($db, $dsSchema, $measureID);
  if ($recalc == 0)
  {
    return;
  }

  #let the DSR know we're currently calculating this measure
  $query = "UPDATE $dsSchema.measures SET lastCalc=NULL WHERE ID=$measureID";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);

  #if our base measure needs to be recalculated, do it
  $recalc = DSRmeasures_needs_recalculation($db, $dsSchema, $baseMeasureID);
  if ($recalc)
  {
    DSRmeasures_recalculate_measure($db, $dsSchema, $baseMeasureID);
  }

  $colName = "measure_" . $measureID;
  $baseColName = "measure_" . $baseMeasureID;

  #
  #get a list of every time period that has a matching "Year Ago" period
  #

  $query = "SELECT ID, name, duration, type, endDate FROM $dsSchema.timeperiods \
      WHERE type > 0";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  meas_db_err($db, $status, $query);

  while (($yagID, $yagName, $yagDuration, $yagType, $yagEnd) = $dbOutput->fetchrow_array)
  {
    #see if our year ago period has a matching current period
    $query = "SELECT ID FROM $dsSchema.timeperiods \
        WHERE duration=$yagDuration AND type=$yagType AND endDate = DATE_ADD('$yagEnd', INTERVAL 52 WEEK)";
    $dbOutput1 = $db->prepare($query);
    $status = $dbOutput1->execute;
    meas_db_err($db, $status, $query);
    ($curID) = $dbOutput1->fetchrow_array;

    #if we found a matching time period, hash both IDs
    if (defined($curID))
    {
      $timeHash{$curID} = $yagID;
    }
  }

  #if we're being called as part of an update, try to cut down the number of
  #time periods we need to calculate lags for (we need to calc values for
  #any time period that was changed, both current and yago).
  if ($dsUpdateID > 0)
  {
    undef(%updateTimeHash);
    $query = "SELECT DISTINCT timeID FROM $dsSchema.facts WHERE updateID=$dsUpdateID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);

    while (($curID) = $dbOutput->fetchrow_array)
    {
      $updateTimeHash{$curID} = 1;
    }

    #delete every cur/yago period that wasn't part of the update
    foreach $curID (keys %timeHash)
    {
      $yagID = $timeHash{$curID};
      if (($updateTimeHash{$curID} != 1) && ($updateTimeHash{$yagID} != 1))
      {
        delete($timeHash{$curID});
      }
    }
  }

  #if we're being called as part of a DS update, only update where we need to
  if ($dsUpdateID > 0)
  {
    $whereClause = " AND (t1.updateID = $dsUpdateID OR t2.updateID = $dsUpdateID)";
  }
  else
  {
    $whereClause = "";
  }

  #
  #calculate the change for every TGP tuple we can in the facts table
  #

  foreach $curID (keys %timeHash)
  {

    #we're going to join the facts table with itself, and then set the value for
    #our calculated measure to the difference between the current year value
    #and the yag value
    #NB: The "t2" version of the facts table is the YAG data, so the equation
    #    in the SQL below is curVal - yagVal

    $query = "UPDATE IGNORE $dsSchema.facts AS t1, \
(SELECT $baseColName, productID, geographyID, updateID FROM $dsSchema.facts WHERE timeID = $timeHash{$curID}) AS t2 \
SET t1.$colName = t1.$baseColName - t2.$baseColName \
WHERE t1.productID = t2.productID AND t1.geographyID = t2.geographyID AND t1.timeID = $curID $whereClause;";
    $status = $db->do($query);
    meas_db_err($db, $status, $query);
  }

  #let the DSR know we're done calculating this measure
  $query = "UPDATE $dsSchema.measures SET lastCalc=NOW() WHERE ID=$measureID";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------
#
# Calculate the specified % Change measure for the specified data source.
# XXX - For now, assumes we're calculating the % change over the past year
#

sub DSRmeasures_calc_pct_change
{
  my ($query, $dbOutput, $dbOutput1, $yagID, $yagName, $yagEnd, $curID, $curName);
  my ($q_curName, $colName, $baseColName, $yagType, $yagEnd, $curEnd, $recalc);
  my ($val, $yagDuration, $productID, $geographyID, $baseValue, $yagBaseValue);
  my ($status, $whereClause);
  my (%timeHash, %updateTimeHash);

  my ($db, $dsSchema, $measureID, $baseMeasureID, $dsUpdateID) = @_;


  #make sure we haven't already been calculated as part of another measure's
  #calculation process
  $recalc = DSRmeasures_needs_recalculation($db, $dsSchema, $measureID);
  if ($recalc == 0)
  {
    return;
  }

  #let the DSR know we're currently calculating this measure
  $query = "UPDATE $dsSchema.measures SET lastCalc=NULL WHERE ID=$measureID";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);

  #if our base measure needs to be recalculated, do it
  $recalc = DSRmeasures_needs_recalculation($db, $dsSchema, $baseMeasureID);
  if ($recalc)
  {
    DSRmeasures_recalculate_measure($db, $dsSchema, $baseMeasureID);
  }

  $colName = "measure_" . $measureID;
  $baseColName = "measure_" . $baseMeasureID;

  #
  #get a list of every time period that has a matching "Year Ago" period
  #

  $query = "SELECT ID, name, duration, type, endDate \
      FROM $dsSchema.timeperiods WHERE type > 0";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  meas_db_err($db, $status, $query);

  while (($yagID, $yagName, $yagDuration, $yagType, $yagEnd) = $dbOutput->fetchrow_array)
  {

    #see if our year ago period has a matching current period
    $query = "SELECT ID FROM $dsSchema.timeperiods \
        WHERE duration=$yagDuration AND type=$yagType AND endDate = DATE_ADD('$yagEnd', INTERVAL 52 WEEK)";
    $dbOutput1 = $db->prepare($query);
    $status = $dbOutput1->execute;
    meas_db_err($db, $status, $query);
    ($curID) = $dbOutput1->fetchrow_array;

    #if we found a matching time period, hash both IDs
    if (defined($curID))
    {
      $timeHash{$curID} = $yagID;
    }
  }

  #if we're being called as part of an update, try to cut down the number of
  #time periods we need to calculate % change for (we need to calc values for
  #any time period that was changed, both current and yago).
  if ($dsUpdateID > 0)
  {
    undef(%updateTimeHash);
    $query = "SELECT DISTINCT timeID FROM $dsSchema.facts \
        WHERE updateID=$dsUpdateID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);

    while (($curID) = $dbOutput->fetchrow_array)
    {
      $updateTimeHash{$curID} = 1;
    }

    #delete every cur/yago period that wasn't part of the update
    foreach $curID (keys %timeHash)
    {
      $yagID = $timeHash{$curID};
      if (($updateTimeHash{$curID} != 1) && ($updateTimeHash{$yagID} != 1))
      {
        delete($timeHash{$curID});
      }
    }
  }

  #if we're being called as part of a DS update, only update where we need to
  if ($dsUpdateID > 0)
  {
    $whereClause = " AND (t1.updateID = $dsUpdateID OR t2.updateID = $dsUpdateID)";
  }
  else
  {
    $whereClause = "";
  }

  #
  #calculate the change for every TGP tuple we can in the facts table
  #

  foreach $curID (keys %timeHash)
  {

    #we're going to join the facts table with itself, and then set the value for
    #our calculated measure to the value associated with the base measure and
    #the year ago ID.
    #NB: this is a huge improvement over the previous method, which pulled out
    #    each YAG value and then updated the current value
    #NB: The "t2" version of the facts table is the YAG data, so the equation
    #    in the SQL below is ((curVal - yagVal) / yagVal) * 100

    $query = "UPDATE IGNORE $dsSchema.facts AS t1, \
(SELECT $baseColName, productID, geographyID, updateID FROM $dsSchema.facts WHERE timeID = $timeHash{$curID}) AS t2 \
SET t1.$colName = ((t1.$baseColName - t2.$baseColName) / t2.$baseColName) * 100 \
WHERE t1.productID = t2.productID AND t1.geographyID = t2.geographyID AND t1.timeID = $curID $whereClause;";
    $status = $db->do($query);
    meas_db_err($db, $status, $query);
  }

  #let the DSR know we're done calculating this measure
  $query = "UPDATE $dsSchema.measures SET lastCalc=NOW() WHERE ID=$measureID";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------
#
# Calculate the specified lag measure for the specified data source.
# XXX - For now, assumes we're calculating the lag from a year ago
#

sub DSRmeasures_calc_lag
{
  my ($query, $dbOutput, $dbOutput1, $yagID, $yagName, $curID, $curName);
  my ($q_curName, $colName, $baseColName, $yagType, $yagEnd, $yagBaseValue);
  my ($productID, $geographyID, $baseValue, $yagDuration, $curEnd, $val);
  my ($yagBaseValue, $recalc, $status, $interval, $whereClause);
  my (%timeHash, %updateTimeHash);

  my ($db, $dsSchema, $measureID, $baseMeasureID, $dsUpdateID) = @_;


  #make sure we haven't already been calculated as part of another measure's
  #calculation process
  $recalc = DSRmeasures_needs_recalculation($db, $dsSchema, $measureID);
  if ($recalc == 0)
  {
    return;
  }

  #let the DSR know we're currently calculating this measure
  $query = "UPDATE $dsSchema.measures SET lastCalc=NULL WHERE ID=$measureID";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);

  #if our base measure needs to be recalculated, do it
  $recalc = DSRmeasures_needs_recalculation($db, $dsSchema, $baseMeasureID);
  if ($recalc)
  {
    DSRmeasures_recalculate_measure($db, $dsSchema, $baseMeasureID);
  }

  $colName = "measure_" . $measureID;
  $baseColName = "measure_" . $baseMeasureID;

  #
  #get a list of every time period that has a matching "Year Ago" period
  #

  $query = "SELECT ID, name, duration, type, endDate \
      FROM $dsSchema.timeperiods WHERE type > 0";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  meas_db_err($db, $status, $query);

  while (($yagID, $yagName, $yagDuration, $yagType, $yagEnd) = $dbOutput->fetchrow_array)
  {

    #see if our year ago period has a matching current period
    $query = "SELECT ID, name, endDate FROM $dsSchema.timeperiods \
        WHERE duration=$yagDuration AND type=$yagType AND endDate = DATE_ADD('$yagEnd', INTERVAL 52 WEEK)";
    $dbOutput1 = $db->prepare($query);
    $status = $dbOutput1->execute;
    meas_db_err($db, $status, $query);
    ($curID, $curName, $curEnd) = $dbOutput1->fetchrow_array;

    #if we found a matching time period, hash both IDs
    if (defined($curID))
    {
      $timeHash{$curID} = $yagID;
    }
  }

  #if we're being called as part of an update, try to cut down the number of
  #time periods we need to calculate lags for (we need to calc values for
  #any time period that was changed, both current and yago).
  if ($dsUpdateID > 0)
  {
    undef(%updateTimeHash);
    $query = "SELECT DISTINCT timeID FROM $dsSchema.facts \
        WHERE updateID=$dsUpdateID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);

    while (($curID) = $dbOutput->fetchrow_array)
    {
      $updateTimeHash{$curID} = 1;
    }

    #delete every cur/yago period that wasn't part of the update
    foreach $curID (keys %timeHash)
    {
      $yagID = $timeHash{$curID};
      if (($updateTimeHash{$curID} != 1) && ($updateTimeHash{$yagID} != 1))
      {
        delete($timeHash{$curID});
      }
    }
  }

  #if we're being called as part of a DS update, only update where we need to
  if ($dsUpdateID > 0)
  {
    $whereClause = " AND (t1.updateID = $dsUpdateID OR t2.updateID = $dsUpdateID)";
  }
  else
  {
    $whereClause = "";
  }

  #
  #calculate the lag for every TGP tuple we can in the facts table
  #

  foreach $curID (keys %timeHash)
  {

    #we're going to join the facts table with itself, and then set the value for
    #our calculated measure to the value associated with the base measure and
    #the year ago ID.
    #NB: this is a huge improvement over the previous method, which pulled out
    #    each YAG value and then updated the current value
    $query = "UPDATE $dsSchema.facts AS t1, \
(SELECT $baseColName, productID, geographyID, updateID FROM $dsSchema.facts WHERE timeID = $timeHash{$curID}) AS t2 \
SET t1.$colName = t2.$baseColName \
WHERE t1.productID = t2.productID AND t1.geographyID = t2.geographyID AND t1.timeID = $curID $whereClause;";
    $status = $db->do($query);
    meas_db_err($db, $status, $query);
  }

  #let the DSR know we're done calculating this measure
  $query = "UPDATE $dsSchema.measures SET lastCalc=NOW() WHERE ID=$measureID";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------
#
# Calculate the specified lead measure for the specified data source.
# XXX - For now, assumes we're calculating a 1 year lead
#

sub DSRmeasures_calc_lead
{
  my ($query, $dbOutput, $dbOutput1, $yagID, $yagName, $curID, $curName);
  my ($q_curName, $colName, $baseColName, $yagType, $yagEnd, $yagBaseValue);
  my ($productID, $geographyID, $baseValue, $yagDuration, $curEnd, $val);
  my ($yagBaseValue, $recalc, $curBaseValue, $status);
  my (%timeHash);

  my ($db, $dsSchema, $measureID, $baseMeasureID, $dsUpdateID) = @_;


  #make sure we haven't already been calculated as part of another measure's
  #calculation process
  $recalc = DSRmeasures_needs_recalculation($db, $dsSchema, $measureID);
  if ($recalc == 0)
  {
    return;
  }

  #let the DSR know we're currently calculating this measure
  $query = "UPDATE $dsSchema.measures SET lastCalc=NULL WHERE ID=$measureID";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);

  #if our base measure needs to be recalculated, do it
  $recalc = DSRmeasures_needs_recalculation($db, $dsSchema, $baseMeasureID);
  if ($recalc)
  {
    DSRmeasures_recalculate_measure($db, $dsSchema, $baseMeasureID);
  }

  $colName = "measure_" . $measureID;
  $baseColName = "measure_" . $baseMeasureID;

  #
  #get a list of every time period that has a matching "Year Ago" period
  #NB: we're working backwards here just to make life easier - we find every
  #    time period that has a matching year ago period, then set the lead
  #    value for those year ago periods
  #

  $query = "SELECT ID, name, duration, type, endDate \
      FROM $dsSchema.timeperiods WHERE type > 0";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  meas_db_err($db, $status, $query);

  while (($yagID, $yagName, $yagDuration, $yagType, $yagEnd) = $dbOutput->fetchrow_array)
  {
    #see if our year ago period has a matching current period
    $query = "SELECT ID, name, endDate FROM $dsSchema.timeperiods \
        WHERE duration=$yagDuration AND type=$yagType AND endDate = DATE_ADD('$yagEnd', INTERVAL 52 WEEK)";
    $dbOutput1 = $db->prepare($query);
    $status = $dbOutput1->execute;
    meas_db_err($db, $status, $query);
    ($curID, $curName, $curEnd) = $dbOutput1->fetchrow_array;

    #if we found a matching time period, hash both IDs
    if (defined($curID))
    {
      $timeHash{$yagID} = $curID;
    }
  }

  #
  #calculate the lead for every TGP tuple we can in the facts table
  #

  foreach $curID (keys %timeHash)
  {

    #we're going to join the facts table with itself, and then set the value for
    #our calculated measure to the value associated with the base measure and
    #the year ahead ID.
    $query = "UPDATE $dsSchema.facts AS t1, \
(SELECT $baseColName, productID, geographyID FROM $dsSchema.facts WHERE timeID = $curID) AS t2 \
SET t1.$colName = t2.$baseColName \
WHERE t1.productID = t2.productID AND t1.geographyID = t2.geographyID AND t1.timeID = $timeHash{$curID};";
    $status = $db->do($query);
    meas_db_err($db, $status, $query);
  }

  #let the DSR know we're done calculating this measure
  $query = "UPDATE $dsSchema.measures SET lastCalc=NOW() WHERE ID=$measureID";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------
#
# Calculate the specified moving averagee measure for the specified data source.
# If we got a cube and dimension IDs, assume we're doing the calculation for
# that particular row of the cube.
#

sub DSRmeasures_calc_mov_avg
{
  my ($query, $dbOutput, $curID, $recalc, $endDate, $colName, $baseColName);
  my ($duration, $type, $dbOutput1, $productID, $geographyID, $status);
  my ($timePerStr, $id, $db_calc, $val);

  my ($db, $dsSchema, $measureID, $baseMeasureID, $periods, $timeDirection, $overwrite, $cubeID, $prodID, $geoID, $timeID) = @_;


  #make sure we haven't already been calculated as part of another measure's
  #calculation process
  $recalc = DSRmeasures_needs_recalculation($db, $dsSchema, $measureID);
  if ($recalc == 0)
  {
    return;
  }

  #let the DSR know we're currently calculating this measure
  $query = "UPDATE $dsSchema.measures SET lastCalc=NULL WHERE ID=$measureID";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);

  #if our base measure needs to be recalculated, do it
  $recalc = DSRmeasures_needs_recalculation($db, $dsSchema, $baseMeasureID);
  if ($recalc)
  {
    DSRmeasures_recalculate_measure($db, $dsSchema, $baseMeasureID);
  }

  $colName = "measure_" . $measureID;
  $baseColName = "measure_" . $baseMeasureID;

  #cycle through every time period in the data source
  $query = "SELECT ID, duration, type, endDate \
      FROM $dsSchema.timeperiods WHERE type > 0";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  meas_db_err($db, $status, $query);
  while (($curID, $duration, $type, $endDate) = $dbOutput->fetchrow_array)
  {

    #grab the IDs of the time periods we need for the total calculation
    if ($timeDirection eq "future")
    {
      $query = "SELECT ID FROM $dsSchema.timeperiods \
          WHERE duration=$duration AND type=$type AND endDate >= '$endDate' \
          ORDER BY endDate ASC LIMIT $periods";
    }
    else
    {
      $query = "SELECT ID FROM $dsSchema.timeperiods \
          WHERE duration=$duration AND type=$type AND endDate <= '$endDate' \
          ORDER BY endDate DESC LIMIT $periods";
    }
    $dbOutput1 = $db->prepare($query);
    $status = $dbOutput1->execute;
    meas_db_err($db, $status, $query);

    #make sure we got enough time periods to run the calculation
    if ($status >= $periods)
    {

      #grab the list of timeperiods for our total calculation
      $timePerStr = "";
      while (($id) = $dbOutput1->fetchrow_array)
      {
        $timePerStr .= $id . ",";
      }
      chop($timePerStr);

      #run through every product and geo combo for the current time period
      $query = "SELECT productID, geographyID FROM $dsSchema.facts \
          WHERE timeID = $curID";
      $dbOutput1 = $db->prepare($query);
      $status = $dbOutput1->execute;
      meas_db_err($db, $status, $query);
      while (($productID, $geographyID) = $dbOutput1->fetchrow_array)
      {
        $query = "SELECT AVG($baseColName) FROM $dsSchema.facts \
            WHERE productID=$productID AND geographyID=$geographyID AND timeID IN ($timePerStr)";
        $db_calc = $db->prepare($query);
        $status = $db_calc->execute;
        meas_db_err($db, $status, $query);
        ($val) = $db_calc->fetchrow_array;

        #put the calculated total into the appropriate facts table slot
        $query = "UPDATE $dsSchema.facts SET $colName = $val \
            WHERE productID = $productID AND geographyID = $geographyID AND timeID = $curID";
        $status = $db->do($query);
        meas_db_err($db, $status, $query);
      }
    }
  }

  #let the DSR know we're done calculating this measure
  $query = "UPDATE $dsSchema.measures SET lastCalc=NOW() WHERE ID=$measureID";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------
#
# Calculate the specified moving total  measure for the specified data source.
# If we got a cube and dimension IDs, assume we're doing the calculation for
# that particular row of the cube.
#

sub DSRmeasures_calc_mov_total
{
  my ($query, $dbOutput, $recalc, $colName, $baseColName, $aggRule, $id);
  my ($curID, $duration, $type, $endDate, $dbOutput1, $val, $timePerStr);
  my ($db_calc, $productID, $geographyID, $status);

  my ($db, $dsSchema, $measureID, $baseMeasureID, $periods, $timeDirection, $overwrite, $cubeID, $prodID, $geoID, $timeID) = @_;


  #make sure we haven't already been calculated as part of another measure's
  #calculation process
  $recalc = DSRmeasures_needs_recalculation($db, $dsSchema, $measureID);
  if ($recalc == 0)
  {
    return;
  }

  #let the DSR know we're currently calculating this measure
  $query = "UPDATE $dsSchema.measures SET lastCalc=NULL WHERE ID=$measureID";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);

  #if our base measure needs to be recalculated, do it
  $recalc = DSRmeasures_needs_recalculation($db, $dsSchema, $baseMeasureID);
  if ($recalc)
  {
    DSRmeasures_recalculate_measure($db, $dsSchema, $baseMeasureID);
  }

  $colName = "measure_" . $measureID;
  $baseColName = "measure_" . $baseMeasureID;

  #get the time period aggregation rule for this measure
  #NB: If there isn't one, we exit out leaving all measure values NULL
  $query = "SELECT timeAggRule FROM $dsSchema.measures WHERE ID=$baseMeasureID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  meas_db_err($db, $status, $query);
  ($aggRule) = $dbOutput->fetchrow_array;
  if (length($aggRule) < 1)
  {
    return;
  }

  #cycle through every time period in the data source
  $query = "SELECT ID, duration, type, endDate FROM $dsSchema.timeperiods \
      WHERE type > 0";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  meas_db_err($db, $status, $query);
  while (($curID, $duration, $type, $endDate) = $dbOutput->fetchrow_array)
  {

    #grab the IDs of the time periods we need for the total calculation
    if ($timeDirection eq "future")
    {
      $query = "SELECT ID FROM $dsSchema.timeperiods \
          WHERE duration=$duration AND type=$type AND endDate >= '$endDate' \
          ORDER BY endDate ASC LIMIT $periods";
    }
    else
    {
      $query = "SELECT ID FROM $dsSchema.timeperiods \
          WHERE duration=$duration AND type=$type AND endDate <= '$endDate' \
          ORDER BY endDate DESC LIMIT $periods";
    }
    $dbOutput1 = $db->prepare($query);
    $status = $dbOutput1->execute;
    meas_db_err($db, $status, $query);

    #make sure we got enough time periods to run the calculation
    if ($status >= $periods)
    {

      #grab the list of timeperiods for our total calculation
      $timePerStr = "";
      while (($id) = $dbOutput1->fetchrow_array)
      {
        $timePerStr .= $id . ",";
      }
      chop($timePerStr);

      #run through every product and geo combo for the current time period
      $query = "SELECT productID, geographyID FROM $dsSchema.facts \
          WHERE timeID = $curID";
      $dbOutput1 = $db->prepare($query);
      $status = $dbOutput1->execute;
      meas_db_err($db, $status, $query);
      while (($productID, $geographyID) = $dbOutput1->fetchrow_array)
      {
        $query = "SELECT $aggRule($baseColName) FROM $dsSchema.facts \
            WHERE productID=$productID AND geographyID=$geographyID AND timeID IN ($timePerStr)";
        $db_calc = $db->prepare($query);
        $status = $db_calc->execute;
        meas_db_err($db, $status, $query);
        ($val) = $db_calc->fetchrow_array;

        #put the calculated total into the appropriate facts table slot
        $query = "UPDATE $dsSchema.facts SET $colName = $val \
            WHERE productID = $productID AND geographyID = $geographyID AND timeID = $curID";
        $status = $db->do($query);
        meas_db_err($db, $status, $query);
      }
    }
  }

  #let the DSR know we're done calculating this measure
  $query = "UPDATE $dsSchema.measures SET lastCalc=NOW() WHERE ID=$measureID";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------
#
# Calculate the specified year to date measure for the specified data source.
# If we got a cube and dimension IDs, assume we're doing the calculation for
# that particular row of the cube.
#

sub DSRmeasures_calc_ytd
{
  my ($recalc, $query, $dbOutput, $colName, $baseColName, $aggRule, $id);
  my ($duration, $type, $timeIDStr, $val, $status);
  my ($dbOutput1, $end);

  my ($db, $dsSchema, $measureID, $baseMeasureID, $overwrite, $cubeID, $prodID, $geoID, $timeID) = @_;


  #make sure we haven't already been calculated as part of another measure's
  #calculation process
  $recalc = DSRmeasures_needs_recalculation($db, $dsSchema, $measureID);
  if ($recalc == 0)
  {
    return;
  }

  #let the DSR know we're currently calculating this measure
  $query = "UPDATE $dsSchema.measures SET lastCalc=NULL WHERE ID=$measureID";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);

  #if our base measure needs to be recalculated, do it
  $recalc = DSRmeasures_needs_recalculation($db, $dsSchema, $baseMeasureID);
  if ($recalc)
  {
    DSRmeasures_recalculate_measure($db, $dsSchema, $baseMeasureID);
  }

  $colName = "measure_" . $measureID;
  $baseColName = "measure_" . $baseMeasureID;

  #get the time period aggregation rule for this measure
  #NB: If there isn't one, we exit out leaving all measure values NULL
  $query = "SELECT timeAggRule FROM $dsSchema.measures WHERE ID=$baseMeasureID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  meas_db_err($db, $status, $query);
  ($aggRule) = $dbOutput->fetchrow_array;
  if (length($aggRule) < 1)
  {
    return;
  }

  #grab every possible TGP tuple from the data source
  $query = "SELECT productID, geographyID, timeID FROM $dsSchema.facts";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  meas_db_err($db, $status, $query);

  #run through each TGP tuple, calculating the year-to-date value for it
  while (($prodID, $geoID, $timeID) = $dbOutput->fetchrow_array)
  {

    #grab the duration, type, and endDate info for the current time period
    $query = "SELECT duration, type, endDate FROM $dsSchema.timeperiods \
        WHERE ID=$timeID AND type > 0";
    $dbOutput1 = $db->prepare($query);
    $status = $dbOutput1->execute;
    meas_db_err($db, $status, $query);
    ($duration, $type, $end) = $dbOutput1->fetchrow_array;

    #grab the IDs of the time periods from the year with same type/duration
    $query = "SELECT ID FROM $dsSchema.timeperiods \
        WHERE duration=$duration AND type=$type and YEAR(endDate) = YEAR('$end')";
    $dbOutput1 = $db->prepare($query);
    $status = $dbOutput1->execute;
    meas_db_err($db, $status, $query);

    #turn the list of matching timeIDs into a string
    $timeIDStr = "";
    while (($id) = $dbOutput1->fetchrow_array)
    {
      $timeIDStr .= "$id,";
    }
    chop($timeIDStr);

    #total up the previous periods of the same type/duration
    $query = "SELECT $aggRule($baseColName) FROM $dsSchema.facts \
        WHERE productID=$prodID AND geographyID=$geoID and timeID IN ($timeIDStr)";
    $dbOutput1 = $db->prepare($query);
    $status = $dbOutput1->execute;
    meas_db_err($db, $status, $query);
    ($val) = $dbOutput1->fetchrow_array;

    #handle undefined values
    if (!defined($val))
    {
      $val = "NULL";
    }

    #update the calculated measure's value in the facts table
    $query = "UPDATE $dsSchema.facts SET $colName = $val \
        WHERE productID = $prodID AND geographyID = $geoID AND timeID = $timeID";
    $status = $db->do($query);
    meas_db_err($db, $status, $query);
  }

  #let the DSR know we're done calculating this measure
  $query = "UPDATE $dsSchema.measures SET lastCalc=NOW() WHERE ID=$measureID";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------
#
# Calculate the number of base items meeting the specified criteria in the
# specified dimension of the specified data source
#

sub DSRmeasures_calc_count
{
  my ($query, $dbOutput, $colName, $meas1Name, $whereClause, $dbCol, $val);
  my ($dbName, $recalc, $status);

  my ($db, $dsSchema, $measureID, $dim, $useCond, $measure1, $conditional, $constant, $dsUpdateID) = @_;


  #make sure we haven't already been calculated as part of another measure's
  #calculation process
  $recalc = DSRmeasures_needs_recalculation($db, $dsSchema, $measureID);
  if ($recalc == 0)
  {
    return;
  }

  #let the DSR know we're currently calculating this measure
  $query = "UPDATE $dsSchema.measures SET lastCalc=NULL WHERE ID=$measureID";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);

  #if we're using a conditional measure, and it needs to be recalculated, do it
  if ($useCond != 0)
  {
    $recalc = DSRmeasures_needs_recalculation($db, $dsSchema, $measure1);
    if ($recalc)
    {
      DSRmeasures_recalculate_measure($db, $dsSchema, $measure1);
    }
  }

  $colName = "measure_" . $measureID;

  #if we have a conditional to be applied
  if ($useCond > 0)
  {
    $meas1Name = "measure_" . $measure1;

    if ($conditional eq "gt")
    {
      $whereClause = "WHERE $meas1Name >= $constant";
      $clearingWhereClause = "WHERE $meas1Name < $constant";
    }
    elsif ($conditional eq "lt")
    {
      $whereClause = "WHERE $meas1Name <= $constant";
      $clearingWhereClause = "WHERE $meas1Name > $constant";
    }
    else
    {
      $whereClause = "WHERE $meas1Name = $constant";
      $clearingWhereClause = "WHERE $meas1Name != $constant";
    }

    #determine if we only need to clear values for records that were
    #added/updated by this datasource run
    if ($dsUpdateID > 0)
    {
      $clearingWhereClause .= " AND updateID = $dsUpdateID";
    }

    #clear the count value for any item that may have had its conditional
    #value change due to the update
    $query = "UPDATE $dsSchema.facts SET $colName = 0 $clearingWhereClause";
    $status = $db->do($query);
    meas_db_err($db, $status, $query);
  }

  #else the value of count measures is 1 for all base items
  else
  {
    $whereClause = "WHERE $colName != 1";
  }

  #determine if we only need to run the calculation for records that were
  #added/updated by this datasource run
  if ($dsUpdateID > 0)
  {
    $whereClause .= " AND updateID = $dsUpdateID";
  }

  #set the count value for all base items that pass the (optional) conditional
  #screen
  $query = "UPDATE $dsSchema.facts SET $colName = 1 $whereClause";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);

  #let the DSR know we're done calculating this measure
  $query = "UPDATE $dsSchema.measures SET lastCalc=NOW() WHERE ID=$measureID";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------
#
# Calculate the index of a base item against another item/agg/segment
#

sub DSRmeasures_calc_index
{
  my ($query, $dbOutput, $dbOutput1, $colName, $measureQuery, $measureName);
  my ($prodID, $geoID, $timeID, $share, $calcProdID, $calcProdVal, $masterVal);
  my ($levelID, $level, $count, $regex, $i, $recalc, $segmentID, $hierLevelID);
  my ($itemID, $id, $segmentations, $status);
  my (@measureIDs, @segArray);
  my (%segmentMembership, %segAggValHash, %hierAggValHash, %hierMembership);

  my ($db, $dsSchema, $measureID, $dim, $measure, $structType, $structID) = @_;


  #make sure we haven't already been calculated as part of another measure's
  #calculation process
  $recalc = DSRmeasures_needs_recalculation($db, $dsSchema, $measureID);
  if ($recalc == 0)
  {
    return;
  }

  #let the DSR know we're currently calculating this measure
  $query = "UPDATE $dsSchema.measures SET lastCalc=NULL WHERE ID=$measureID";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);

  #if our base measure needs to be recalculated, do it
  $recalc = DSRmeasures_needs_recalculation($db, $dsSchema, $measure);
  if ($recalc)
  {
    DSRmeasures_recalculate_measure($db, $dsSchema, $measure);
  }

  $colName = "measure_" . $measureID;
  $measureName = "measure_" . $measure;


                #---- items -----

  #if we're calculating an item's share of another item
  if ($structType eq "item")
  {

    #get the master item's measure value for every possible geo/time combo
    $query = "SELECT geographyID, timeID, $measureName FROM $dsSchema.facts \
        WHERE productID=$structID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);

    #for every relevant combination, calculate the index against the master item
    while (($geoID, $timeID, $masterVal) = $dbOutput->fetchrow_array)
    {

      #make sure we don't do any division by zero
      if ($masterVal == 0)
      {
        next;
      }

      #calc the index value for every base product in the same geo/time
      $query = "UPDATE $dsSchema.facts SET $colName = 100 * ($measureName / $masterVal) \
          WHERE geographyID=$geoID AND timeID=$timeID";
      $status = $db->do($query);
      meas_db_err($db, $status, $query);
    }
  }


                #---- aggregates -----

  #if we're calculating an item's index against an aggregate
  if ($structType eq "agg")
  {
    $prodID = $structID;

    #get all possible time combos we need to calculate for
    #NB: we have to do this for all time periods every time, not just for ones
    #    that are involved in the update, because the update might have
    #    changed which items are members of the aggregates, requiring a
    #    recalculation for every single tuple in the DS. Blah.
    $query = "SELECT DISTINCT timeID FROM $dsSchema.facts";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);

    #get the base items that make up the aggregate, and the aggregation rule
    $aggMemberString = DSRagg_expand_items($db, $dsSchema, "p", $prodID);

    #for every relevant combination, calculate the share of the aggregate
    while (($timeID) = $dbOutput->fetchrow_array)
    {
      $query = "UPDATE IGNORE $dsSchema.facts AS t1, \
(SELECT AVG($measureName) AS masterVal, geographyID FROM $dsSchema.facts WHERE productID IN ($aggMemberString) AND timeID=$timeID GROUP BY geographyID) AS t2 \
SET t1.$colName = ($measureName / masterVal) * 100 \
WHERE t1.geographyID = t2.geographyID AND timeID=$timeID AND productID IN ($aggMemberString)";
      $status = $db->do($query);
      meas_db_err($db, $status, $query);
    }
  }



               #---- segmentations -----

  #if we're calculating an item's share of a segmentation
  if ($structType eq "seg")
  {

    #load up a hash of every item's segment membership for this segmentation
    %segmentMembership = DSRseg_get_segment_item_hash($db, $dsSchema, $dim, $structID);

    #cycle through each segment, calculating shares for its members
    foreach $segmentID (keys %segmentMembership)
    {

      #skip empty segments
      if (length($segmentMembership{$segmentID}) < 1)
      {
        next;
      }

      #for every relevant combination, calculate the share of the segment
      $query = "UPDATE IGNORE $dsSchema.facts AS t1, \
          (SELECT AVG($measureName) AS masterVal, geographyID, timeID
              FROM $dsSchema.facts
              WHERE productID IN ($segmentMembership{$segmentID})
              GROUP BY geographyID, timeID) AS t2 \
          SET t1.$colName = 100 * ($measureName / masterVal) \
          WHERE t1.geographyID = t2.geographyID AND t1.timeID = t2.timeID AND productID IN ($segmentMembership{$segmentID})";
      $status = $db->do($query);
      meas_db_err($db, $status, $query);
    }
  }


               #---- hierarchies -----

  #if we're calculating an item's share of a segmentation hierarchy
  if ($structType eq "hier")
  {

    #split our structID up into the seghier ID and the hierarchy level seg ID
    $structID =~ m/(\d+)_(\d+)/;
    $structID = $1;
    $levelID = $2;

    #load up a hash of every item's segment hierarchy chain
    %hierMembership = DSRseghier_get_item_chain_hash($db, $dsSchema, "p", $structID);

    #get the specified level's position in the tree
    $query = "SELECT segmentations FROM $dsSchema.product_seghierarchy \
        WHERE ID=$structID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);
    ($segmentations) = $dbOutput->fetchrow_array;
    @segArray = split(',', $segmentations);
    $count = 1;
    foreach $id (@segArray)
    {
      if ($id eq $levelID)
      {
        $level = $count;
      }
      else
      {
        $count++;
      }
    }

    #build up the regex we're going to use to cut the chains down to the
    #specified level
    $regex = "^(";
    for ($i=1; $i <= $level; $i++)
    {
      $regex .= "\\d+_";
    }
    chop($regex);
    $regex .= ")";

    #apply the regex to all of the item chains in the membership hash, and
    #prepend with the hierarchy ID (needed by aggregation function)
    foreach $itemID (keys %hierMembership)
    {
      $hierMembership{$itemID} =~ m/$regex/;
      $hierMembership{$itemID} = $1;
      $hierMembership{$itemID} = $structID . "_" . $hierMembership{$itemID};
    }

    #get the hierarchy's every possible geo/time combo
    $query = "SELECT DISTINCT geographyID, timeID FROM $dsSchema.facts";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);

    #for every relevant combination, calculate the share of ther hierarchy
    while (($geoID, $timeID) = $dbOutput->fetchrow_array)
    {

      #get every base item with matching geo and time IDs
      $query = "SELECT productID, $measureName FROM $dsSchema.facts \
          WHERE geographyID=$geoID AND timeID=$timeID";
      $dbOutput1 = $db->prepare($query);
      $status = $dbOutput1->execute;
      meas_db_err($db, $status, $query);

      undef(%hierAggValHash);

      #for every product with a matching geo and time we found, calc share
      while (($calcProdID, $calcProdVal) = $dbOutput1->fetchrow_array)
      {

        #if the item is a member of the hierarchy
        if (defined($hierMembership{$calcProdID}))
        {

          $hierLevelID = "SHS_" . $hierMembership{$calcProdID};

          $avgVal = DSRmeasures_get_avg_measure_val($db, $dsSchema, 0, $measure, $hierLevelID, $geoID, $timeID);

          #calculate the share, avoiding division by zero
          if (($avgVal == 0) || ($avgVal eq "NULL"))
          {
            $index = "NULL";
          }
          else
          {
            $index = 100 * ($calcProdVal / $avgVal);
          }

          #insert the calculated share into the facts table
          $query = "UPDATE $dsSchema.facts SET $colName = $index \
              WHERE productID=$calcProdID AND geographyID=$geoID AND timeID=$timeID";
          $status = $db->do($query);
          meas_db_err($db, $status, $query);
        }
      }
    }
  }

  #let the DSR know we're done calculating this measure
  $query = "UPDATE $dsSchema.measures SET lastCalc=NOW() WHERE ID=$measureID";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------
#
# Calculate the share an item/aggregate/segment has of another DS item
#

sub DSRmeasures_calc_share
{
  my ($query, $dbOutput, $dbOutput1, $colName, $measureQuery, $measureName);
  my ($prodID, $geoID, $timeID, $share, $calcProdID, $calcProdVal, $masterVal);
  my ($levelID, $level, $count, $regex, $i, $recalc, $segmentID, $hierLevelID);
  my ($itemID, $id, $segmentations, $status);
  my (@measureIDs, @segArray);
  my (%segmentMembership, %segAggValHash, %hierAggValHash, %hierMembership);

  my ($db, $dsSchema, $measureID, $dim, $measure, $structType, $structID) = @_;


  #make sure we haven't already been calculated as part of another measure's
  #calculation process
  $recalc = DSRmeasures_needs_recalculation($db, $dsSchema, $measureID);
  if ($recalc == 0)
  {
    return;
  }

  #let the DSR know we're currently calculating this measure
  $query = "UPDATE $dsSchema.measures SET lastCalc=NULL WHERE ID=$measureID";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);

  #if our base measure needs to be recalculated, do it
  $recalc = DSRmeasures_needs_recalculation($db, $dsSchema, $measure);
  if ($recalc)
  {
    DSRmeasures_recalculate_measure($db, $dsSchema, $measure);
  }

  $colName = "measure_" . $measureID;
  $measureName = "measure_" . $measure;

                #---- items -----

  #if we're calculating an item's share of another item
  if ($structType eq "item")
  {

    #get the master item's measure value for every possible geo/time combo
    $query = "SELECT geographyID, timeID, $measureName FROM $dsSchema.facts \
        WHERE productID=$structID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);

    #for every relevant combination, calculate the share of the master item
    while (($geoID, $timeID, $masterVal) = $dbOutput->fetchrow_array)
    {

      #make sure we don't do any division by zero
      if ($masterVal == 0)
      {
        next;
      }

      #get every base item with matching geo and time IDs
      $query = "SELECT productID, $measureName FROM $dsSchema.facts \
          WHERE geographyID=$geoID AND timeID=$timeID";
      $dbOutput1 = $db->prepare($query);
      $status = $dbOutput1->execute;
      meas_db_err($db, $status, $query);

      #for every product with a matching geo and time we found, calc share
      while (($calcProdID, $calcProdVal) = $dbOutput1->fetchrow_array)
      {
        $share = ($calcProdVal / $masterVal) * 100;

        #insert the calculated share into the facts table
        $query = "UPDATE $dsSchema.facts SET $colName = $share \
            WHERE productID=$calcProdID AND geographyID=$geoID AND timeID=$timeID";
        $status = $db->do($query);
        meas_db_err($db, $status, $query);
      }
    }
  }


                #---- aggregates -----

  #if we're calculating an item's share of an aggregate
  if ($structType eq "agg")
  {
    $prodID = $structID;

    #get all possible time combos we need to calculate for
    #NB: we have to do this for all time periods every time, not just for ones
    #    that are involved in the update, because the update might have
    #    changed which items are members of the aggregates, requiring a
    #    recalculation for every single tuple in the DS. Blah.
    $query = "SELECT DISTINCT timeID FROM $dsSchema.facts";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);

    #get the base items that make up the aggregate, and the aggregation rule
    $aggMemberString = DSRagg_expand_items($db, $dsSchema, "p", $prodID);
    $aggRule = DSRmeasures_get_agg_rule($db, $dsSchema, $measure, "p");

    #hack to set some kind of agg rule if one hasn't been defined
    if ((length($aggRule) < 2) || ($aggRule eq "None"))
    {
      $aggRule = "SUM";
    }

    #for every relevant combination, calculate the share of the aggregate
    while (($timeID) = $dbOutput->fetchrow_array)
    {
      $query = "UPDATE IGNORE $dsSchema.facts AS t1, \
(SELECT $aggRule($measureName) AS masterVal, geographyID FROM $dsSchema.facts WHERE productID IN ($aggMemberString) AND timeID=$timeID GROUP BY geographyID) AS t2 \
SET t1.$colName = ($measureName / masterVal) * 100 \
WHERE t1.geographyID = t2.geographyID AND timeID=$timeID AND productID IN ($aggMemberString)";
      $status = $db->do($query);
      meas_db_err($db, $status, $query);
    }
  }


               #---- segmentations -----

  #if we're calculating an item's share of a segmentation
  if ($structType eq "seg")
  {

    #get the aggregation rule
    $aggRule = DSRmeasures_get_agg_rule($db, $dsSchema, $measure, "p");

    #hack to set some kind of agg rule if one hasn't been defined
    if ((length($aggRule) < 2) || ($aggRule eq "None"))
    {
      $aggRule = "SUM";
    }

    #load up a hash of every item's segment membership for this segmentation
    %segmentMembership = DSRseg_get_segment_item_hash($db, $dsSchema, $dim, $structID);

    #cycle through each segment, calculating shares for its members
    foreach $segmentID (keys %segmentMembership)
    {

      #skip empty segments
      if (length($segmentMembership{$segmentID}) < 1)
      {
        next;
      }

      #for every relevant combination, calculate the share of the segment
      $query = "UPDATE IGNORE $dsSchema.facts AS t1, \
          (SELECT $aggRule($measureName) AS masterVal, geographyID, timeID
              FROM $dsSchema.facts
              WHERE productID IN ($segmentMembership{$segmentID})
              GROUP BY geographyID, timeID) AS t2 \
          SET t1.$colName = ($measureName / masterVal) * 100 \
          WHERE t1.geographyID = t2.geographyID AND t1.timeID = t2.timeID AND productID IN ($segmentMembership{$segmentID})";
      $status = $db->do($query);
      meas_db_err($db, $status, $query);
    }
  }


               #---- hierarchies -----

  #if we're calculating an item's share of a segmentation hierarchy
  if ($structType eq "hier")
  {

    #split our structID up into the seghier ID and the hierarchy level seg ID
    $structID =~ m/(\d+)_(\d+)/;
    $structID = $1;
    $levelID = $2;

    #load up a hash of every item's segment hierarchy chain
    %hierMembership = DSRseghier_get_item_chain_hash($db, $dsSchema, "p", $structID);

    #get the specified level's position in the tree
    $query = "SELECT segmentations FROM $dsSchema.product_seghierarchy \
        WHERE ID=$structID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);
    ($segmentations) = $dbOutput->fetchrow_array;
    @segArray = split(',', $segmentations);
    $count = 1;
    foreach $id (@segArray)
    {
      if ($id eq $levelID)
      {
        $level = $count;
      }
      else
      {
        $count++;
      }
    }

    #build up the regex we're going to use to cut the chains down to the
    #specified level
    $regex = "^(";
    for ($i=1; $i <= $level; $i++)
    {
      $regex .= "\\d+_";
    }
    chop($regex);
    $regex .= ")";

    #apply the regex to all of the item chains in the membership hash, and
    #prepend with the hierarchy ID (needed by aggregation function)
    foreach $itemID (keys %hierMembership)
    {
      $hierMembership{$itemID} =~ m/$regex/;
      $hierMembership{$itemID} = $1;
      $hierMembership{$itemID} = $structID . "_" . $hierMembership{$itemID};
    }

    #get the hierarchy's every possible geo/time combo
    $query = "SELECT DISTINCT geographyID, timeID FROM $dsSchema.facts";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);

    #for every relevant combination, calculate the share of ther hierarchy
    while (($geoID, $timeID) = $dbOutput->fetchrow_array)
    {

      #get every base item with matching geo and time IDs
      $query = "SELECT productID, $measureName FROM $dsSchema.facts \
          WHERE geographyID=$geoID AND timeID=$timeID";
      $dbOutput1 = $db->prepare($query);
      $status = $dbOutput1->execute;
      meas_db_err($db, $status, $query);

      undef(%hierAggValHash);

      #for every product with a matching geo and time we found, calc share
      while (($calcProdID, $calcProdVal) = $dbOutput1->fetchrow_array)
      {

        #if the item is a member of the hierarchy
        if (defined($hierMembership{$calcProdID}))
        {

          $hierLevelID = "SHS_" . $hierMembership{$calcProdID};

          #cache already calculated hierarchy rollup values for performance
          if (defined($hierAggValHash{$hierLevelID}))
          {
            $masterVal = $hierAggValHash{$hierLevelID};
          }
          else
          {
            $masterVal = DSRmeasures_get_agg_measure_val($db, $dsSchema, 0, $measure, $hierLevelID, $geoID, $timeID);
            $hierAggValHash{$hierLevelID} = $masterVal;
          }

          #calculate the share, avoiding division by zero
          if (($masterVal == 0) || ($masterVal eq "NULL"))
          {
            $share = "NULL";
          }
          else
          {
            $share = ($calcProdVal / $masterVal) * 100;
          }

          #insert the calculated share into the facts table
          $query = "UPDATE $dsSchema.facts SET $colName = $share \
              WHERE productID=$calcProdID AND geographyID=$geoID AND timeID=$timeID";
          $status = $db->do($query);
          meas_db_err($db, $status, $query);
        }
      }
    }
  }

  #let the DSR know we're done calculating this measure
  $query = "UPDATE $dsSchema.measures SET lastCalc=NOW() WHERE ID=$measureID";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------
#
# Calculate a custom measure based on a user-entered formula for the
# specified data source.
#

sub DSRmeasures_calc_calc
{
  my ($query, $dbOutput, $colName, $measureQuery, $measureName, $tmp, $id);
  my ($recalc, $status, $measures, $whereClause, $shortCutOK);

  my ($db, $dsSchema, $measureID, $formula, $dsUpdateID) = @_;


  #make sure we haven't already been calculated as part of another measure's
  #calculation process
  $recalc = DSRmeasures_needs_recalculation($db, $dsSchema, $measureID);
  if ($recalc == 0)
  {
    return;
  }

  #let the DSR know we're currently calculating this measure
  $query = "UPDATE $dsSchema.measures SET lastCalc=NULL WHERE ID=$measureID";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);

  #run through all of the measures in the calculation formula, and if they
  #need to be recalculated, do it
  $tmp = $formula;
  $measures = "";
  while ($tmp =~ m/^.*?measure_(\d+)(.*)/)
  {
    $id = $1;
    $tmp = $2;
    $measures = $measures . "$id,";

    $recalc = DSRmeasures_needs_recalculation($db, $dsSchema, $id);
    if ($recalc)
    {
      DSRmeasures_recalculate_measure($db, $dsSchema, $id);
    }
  }

  #determine if we only need to run the calculation for records that were
  #added/updated by this datasource run
  $whereClause = "";
  if ($dsUpdateID > 0)
  {
    $shortCutOK = DSRmeasures_dependency_check($db, $dsSchema, $measures);
    if ($shortCutOK > 0)
    {
      $whereClause = "WHERE updateID = $dsUpdateID";
    }
  }

  $colName = "measure_" . $measureID;

  $measureQuery = $formula;

  $query = "UPDATE IGNORE $dsSchema.facts \
      SET $colName = $measureQuery $whereClause";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);

  #let the DSR know we're done calculating this measure
  $query = "UPDATE $dsSchema.measures SET lastCalc=NOW() WHERE ID=$measureID";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------
#
# Recalculate the sum of two or more measures inside the specified data cube.
#

sub cube_calc_sum
{
  my ($query, $dbOutput, $colName, $val, $measureID, $newVal, $status, $key);
  my ($measureName, $count, $totalStructures, $prodID, $geoID, $timeID);
  my ($formula);
  my (@measureIDs);

  my ($db, $dsSchema, $calcMeasureID, $measureStr, $cubeID) = @_;


  $colName = "measure_" . $calcMeasureID;
  @measureIDs = split(',', $measureStr);
  $measureName = DSRmeasures_name_by_id($db, $dsSchema, $calcMeasureID);

  Lib::BuildCube::cube_set_status($db, $cubeID, "Calculating measure $measureName");

  #make sure we have aggregated columns for all of our element measures
  foreach $measureID (@measureIDs)
  {
    cube_verify_calculation_column($db, $dsSchema, $cubeID, $measureID);
  }

  #build our addition formula string
  $formula = "";
  foreach $measureID (@measureIDs)
  {
    $formula .= "measure_$measureID+";
  }
  chop($formula);

  $query = "UPDATE IGNORE $dsSchema.__rptcube_$cubeID SET $colName = $formula \
      WHERE product LIKE 'AGG_%' OR product LIKE 'SMT_%' OR product LIKE 'SHS_%' OR geography LIKE 'AGG_%' OR geography LIKE 'SMT_%' OR time LIKE 'AGG_%' OR time LIKE 'SMT_%'";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);

  $_measureCalculated{$calcMeasureID} = 1;
}



#-------------------------------------------------------------------------
#
# Recalculate the difference of two measures inside the specified data cube.
#

sub cube_calc_difference
{
  my ($query, $dbOutput, $colName, $val, $measureID, $status, $key);
  my ($measureName);

  my ($db, $dsSchema, $calcMeasureID, $measure1, $measure2, $cubeID) = @_;


  $colName = "measure_" . $calcMeasureID;
  $measureName = DSRmeasures_name_by_id($db, $dsSchema, $calcMeasureID);

  Lib::BuildCube::cube_set_status($db, $cubeID, "Calculating measure $measureName");

  #make sure we have aggregated columns for the antecedent and consequent
  cube_verify_calculation_column($db, $dsSchema, $cubeID, $measure1);
  cube_verify_calculation_column($db, $dsSchema, $cubeID, $measure2);

  $query = "UPDATE IGNORE $dsSchema.__rptcube_$cubeID \
      SET $colName = measure_$measure1 - measure_$measure2 \
      WHERE product LIKE 'AGG_%' OR product LIKE 'SMT_%' OR product LIKE 'SHS_%' OR geography LIKE 'AGG_%' OR geography LIKE 'SMT_%' OR time LIKE 'AGG_%' OR time LIKE 'SMT_%'";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);

  $_measureCalculated{$calcMeasureID} = 1;
}



#-------------------------------------------------------------------------
#
# Recalculate the ratio of two measures inside the specified data cube.
#

sub cube_calc_ratio
{
  my ($query, $dbOutput, $colName, $status, $formula);
  my ($measureName);

  my ($db, $dsSchema, $calcMeasureID, $antecedentID, $consequentID, $convertPct, $cubeID) = @_;


  $colName = "measure_" . $calcMeasureID;
  $measureName = DSRmeasures_name_by_id($db, $dsSchema, $calcMeasureID);

  Lib::BuildCube::cube_set_status($db, $cubeID, "Calculating measure $measureName");

  #make sure we have aggregated columns for the antecedent and consequent
  cube_verify_calculation_column($db, $dsSchema, $cubeID, $antecedentID);
  cube_verify_calculation_column($db, $dsSchema, $cubeID, $consequentID);

  if ($convertPct == 1)
  {
    $formula = "measure_$antecedentID/measure_$consequentID * 100";
  }
  else
  {
    $formula = "measure_$antecedentID/measure_$consequentID";
  }

  $query = "UPDATE IGNORE $dsSchema.__rptcube_$cubeID SET $colName = $formula \
      WHERE product LIKE 'AGG_%' OR product LIKE 'SMT_%' OR product LIKE 'SHS_%' OR geography LIKE 'AGG_%' OR geography LIKE 'SMT_%' OR time LIKE 'AGG_%' OR time LIKE 'SMT_%'";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);

  $_measureCalculated{$calcMeasureID} = 1;
}



#-------------------------------------------------------------------------
#
# Recalculate the multiple of two or more measures inside the specified data
# cube.
#

sub cube_calc_multiplication
{
  my ($query, $dbOutput, $colName, $val, $measureID, $newVal, $status, $key);
  my ($measureName, $count, $totalStructures, $prodID, $geoID, $timeID, $pct);
  my (@measureIDs, @updateArray);
  my (%rowHash);

  my ($db, $dsSchema, $calcMeasureID, $measureStr, $constant, $cubeID) = @_;


  $colName = "measure_" . $calcMeasureID;
  $measureName = DSRmeasures_name_by_id($db, $dsSchema, $calcMeasureID);
  @measureIDs = split(',', $measureStr);

  #grab a list of all TGP tuples containing aggs or segs or hierarchies
  $query = "SELECT product, geography, time FROM $dsSchema.__rptcube_$cubeID \
      WHERE product LIKE 'AGG_%' OR product LIKE 'SMT_%' OR product LIKE 'SHS_%' OR geography LIKE 'AGG_%' OR geography LIKE 'SMT_%' OR time LIKE 'AGG_%' OR time LIKE 'SMT_%'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  meas_db_err($db, $status, $query);

  $totalStructures = $status;

  while (($prodID, $geoID, $timeID) = $dbOutput->fetchrow_array)
  {
    $key = "$prodID.$geoID.$timeID";
    $rowHash{$key} = 1;
  }

  #foreach line of cube data containing an agg/seg structure, recalculate
  #this calculated measure
  #NB: For SQL performance reasons, we're queueing up 500 UPDATE statements and
  #    running them in a giant block inside a transaction
  $count = 0;
  foreach $key (keys %rowHash)
  {
    $key =~ m/^(.*?)\.(.*?)\.(.*)$/;
    $prodID = $1;
    $geoID = $2;
    $timeID = $3;

    $count++;
    #every 500 structures, update the user and commit our updates
    if (($count % 500) == 0)
    {
      $pct = ($count / $totalStructures) * 100;
      $pct = int($pct);
      Lib::BuildCube::cube_set_status($db, $cubeID, "Calculating measure $measureName $pct\% ($count of $totalStructures)");

      #execute all of the queued up UPDATE statements
      foreach $query (@updateArray)
      {
        $status = $db->do($query);
        meas_db_err($db, $status, $query);
      }
      undef(@updateArray);
    }

    #run through the measure IDs, and get the additive value for each for the
    #specified TGPM combo
    $val = 1;
    foreach $measureID (@measureIDs)
    {
      $newVal = DSRmeasures_get_agg_measure_val($db, $dsSchema, $cubeID, $measureID, $prodID, $geoID, $timeID);

      #if the value can't be aggregated or doesn't exist
      if ($newVal eq "NULL")
      {
        next;
      }

      $val = $val * $newVal;
    }

    $val = $val * $constant;

    #add the current UPDATE statement to the to-be-executed array
    $query = "UPDATE $dsSchema.__rptcube_$cubeID SET $colName=$val \
        WHERE product='$prodID' AND geography='$geoID' AND time='$timeID'";
    push(@updateArray, $query);
  }

  #execute any remaining queued up UPDATE statements
  foreach $query (@updateArray)
  {
    $status = $db->do($query);
    meas_db_err($db, $status, $query);
  }

  $_measureCalculated{$calcMeasureID} = 1;
}



#-------------------------------------------------------------------------
#
# Recalculate the % change between two measures inside the specified data cube.
#

sub cube_calc_pct_change_meas
{
  my ($query, $dbOutput, $colName, $meas1Value, $meas2Value, $val, $status);
  my ($key, $measureName, $count, $totalStructures, $prodID, $geoID, $timeID);
  my (@updateArray);
  my (%rowHash);

  my ($db, $dsSchema, $calcMeasureID, $measure1, $measure2, $cubeID) = @_;


  $colName = "measure_" . $calcMeasureID;
  $measureName = DSRmeasures_name_by_id($db, $dsSchema, $calcMeasureID);

  Lib::BuildCube::cube_set_status($db, $cubeID, "Calculating measure $measureName");

  #make sure we have aggregated columns for the antecedent and consequent
  cube_verify_calculation_column($db, $dsSchema, $cubeID, $measure1);
  cube_verify_calculation_column($db, $dsSchema, $cubeID, $measure2);

  $query = "UPDATE IGNORE $dsSchema.__rptcube_$cubeID \
      SET $colName = (measure_$measure1 - measure_$measure2) / measure_$measure2 * 100 \
      WHERE product LIKE 'AGG_%' OR product LIKE 'SMT_%' OR product LIKE 'SHS_%' OR geography LIKE 'AGG_%' OR geography LIKE 'SMT_%' OR time LIKE 'AGG_%' OR time LIKE 'SMT_%'";
  $status = $db->do($query);
  meas_db_err($db, $status, $query);

  $_measureCalculated{$calcMeasureID} = 1;
}



#-------------------------------------------------------------------------
#
# Recalculate the specified Change measure inside the specified data cube.
# XXX - For now, assumes we're calculating the change from the past year
#

sub cube_calc_change
{
  my ($query, $dbOutput, $timeEnd, $yagEnd, $yagBaseValue, $baseValue, $val);
  my ($colName, $timeDuration, $timeType, $timeEnd, $timePeriods, $status);
  my ($key, $measureName, $count, $totalStructures, $prodID, $geoID, $timeID);
  my ($pct, $baseColName);
  my (@yagPeriods, @updateArray, @baseProdIDs, @baseGeoIDs, @timeIDs);
  my (%rowHash, %yagValuesHash);

  my ($db, $dsSchema, $calcMeasureID, $baseMeasureID, $cubeID) = @_;


  $colName = "measure_" . $calcMeasureID;
  $baseColName = "measure_" . $baseMeasureID;
  $measureName = DSRmeasures_name_by_id($db, $dsSchema, $calcMeasureID);

  #grab a list of all TGP tuples containing aggs or segs or hierarchies
  $query = "SELECT product, geography, time FROM $dsSchema.__rptcube_$cubeID \
      WHERE product LIKE 'AGG_%' OR product LIKE 'SMT_%' OR product LIKE 'SHS_%' OR geography LIKE 'AGG_%' OR geography LIKE 'SMT_%' OR time LIKE 'AGG_%' OR time LIKE 'SMT_%'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  meas_db_err($db, $status, $query);

  $totalStructures = $status;

  while (($prodID, $geoID, $timeID) = $dbOutput->fetchrow_array)
  {
    $key = "$prodID.$geoID.$timeID";
    $rowHash{$key} = 1;
  }

  #calculate the actual change for all base prod/geo combinations
  #NB: this is a performance-at-the-expense-of-readability enhancement that
  #    massively speeds up large item-level reports that use aggregated
  #    time periods
  #NB: this is designed so you can comment out the entire block, and the
  #    original algorithm will still calculate all values
  Lib::BuildCube::cube_set_status($db, $cubeID, "Calculating measure $measureName");

  @baseProdIDs = datasel_get_selected_base_items($db, $cubeID, "p");
  @baseGeoIDs = datasel_get_selected_base_items($db, $cubeID, "g");
  @timeIDs = datasel_get_dimension_items($db, $cubeID, "t");

  $count = 0;
  $baseProdStr = join(',', @baseProdIDs);
  $baseGeoStr = join(',', @baseGeoIDs);
  $aggRule = DSRmeasures_get_agg_rule($db, $dsSchema, $baseMeasureID, "t");

  #build up a hash of all YAGO values for the TGP tuples in the report
  foreach $timeID (@timeIDs)
  {

    #ignore base time periods or selections with no base products
    if (($timeID =~ m/^\d+$/) || (length($baseProdStr) < 1))
    {
      next;
    }

    #get matching yag periods
    @yagPeriods = DSRMeasures_get_yago_array($db, $dsSchema, $timeID);
    if (!@yagPeriods)
    {
      next;
    }

    $timePeriods = join(',', @yagPeriods);

    #get the aggregated measure value for all prod/geo combinations
    $query = "SELECT productID, geographyID, $aggRule($baseColName) \
        FROM $dsSchema.facts \
        WHERE productID IN ($baseProdStr) AND geographyID IN ($baseGeoStr) AND timeID IN ($timePeriods) \
        GROUP BY productID, geographyID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);

    #update report cube with aggregated values
    while (($prodID, $geoID, $val) = $dbOutput->fetchrow_array)
    {
      #if the aggregated value isn't NULL
      if (defined($val))
      {
        $key = "$prodID.$geoID.$timeID";
        $yagValuesHash{$key} = $val;
      }
    }
  }

  foreach $timeID (@timeIDs)
  {

    #ignore base time periods or selections with no base products
    if (($timeID =~ m/^\d+$/) || (length($baseProdStr) < 1))
    {
      next;
    }

    #get current time periods that make up the aggregate
    @curPeriods = DSRagg_expand_items($db, $dsSchema, "t", $timeID);
    if (!@curPeriods)
    {
      next;
    }

    $timePeriods = join(',', @curPeriods);

    #get the aggregated measure value for all prod/geo combinations
    $query = "SELECT productID, geographyID, $aggRule($baseColName) \
        FROM $dsSchema.facts \
        WHERE productID IN ($baseProdStr) AND geographyID IN ($baseGeoStr) AND timeID IN ($timePeriods) \
        GROUP BY productID, geographyID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);

    #update report cube with calculated values
    while (($prodID, $geoID, $baseValue) = $dbOutput->fetchrow_array)
    {

      #if the aggregated value isn't NULL
      $key = "$prodID.$geoID.$timeID";
      if (defined($yagValuesHash{$key}))
      {

        #calculate our change between now and the YAG period
        $val = $baseValue - $yagValuesHash{$key};

        $query = "UPDATE $dsSchema.__rptcube_$cubeID SET $colName=$val \
            WHERE product='$prodID' AND geography='$geoID' AND time='$timeID'";
        $status = $db->do($query);
        meas_db_err($db, $status, $query);
      }
    }

    #delete the TGP tuples from the list awaiting calculation
    #NB: we're doing this down here using the arrays instead of up in the
    #    database loop above bc the DB loop doesn't include prods/geos that
    #    don't have any values in facts, and we don't want to try
    #    recalculating those below
    foreach $prodID (@baseProdIDs)
    {
      foreach $geoID (@baseGeoIDs)
      {
        $key = "$prodID.$geoID.$timeID";
        delete($rowHash{$key});

        $count++;
      }
    }
  }
  ############ END OPTIMIZATION ###########################


  #the following code block handles any lags that require cross-calculation
  #across structures in more than one dimension

  #foreach line of cube data containing an agg/seg structure, recalculate
  #this calculated measure
  #NB: For SQL performance reasons, we're queueing up 500 UPDATE statements and
  #    running them in a giant block inside a transaction
  $count = 0;
  foreach $key (keys %rowHash)
  {
    $key =~ m/^(.*?)\.(.*?)\.(.*)$/;
    $prodID = $1;
    $geoID = $2;
    $timeID = $3;

    $count++;
    #every 500 structures, update the user and commit our updates
    if (($count % 500) == 0)
    {
      $pct = ($count / $totalStructures) * 100;
      $pct = int($pct);
      Lib::BuildCube::cube_set_status($db, $cubeID, "Calculating measure $measureName $pct\% ($count of $totalStructures)");

      #execute all of the queued up UPDATE statements
      foreach $query (@updateArray)
      {
        $status = $db->do($query);
        meas_db_err($db, $status, $query);
      }
      undef(@updateArray);
    }

    #if we're dealing with a DSR structure in the timeID field, we have to do
    #some gyrations since this is a time-dependent measure: call this function
    #to get an array of the matching year-ago time periods, if return is
    #undefined then there isn't one and we move on
    @yagPeriods = DSRMeasures_get_yago_array($db, $dsSchema, $timeID);

    #if we didn't find a matching year ago entry, return
    if (!@yagPeriods)
    {
      next;
    }

    $timePeriods = join(',', @yagPeriods);

    #get our year ago base value
    $yagBaseValue = DSRmeasures_get_agg_measure_val($db, $dsSchema, $cubeID, $baseMeasureID, $prodID, $geoID, $yagEnd, $timePeriods);

    #if the value can't be aggregated
    if ($yagBaseValue eq "NULL")
    {
      next;
    }

    #get our current base value
    $baseValue = DSRmeasures_get_agg_measure_val($db, $dsSchema, $cubeID, $baseMeasureID, $prodID, $geoID, $timeID);

    #if the value can't be aggregated
    if ($baseValue eq "NULL")
    {
      next;
    }

    #calculate our change between now and the YAG period
    $val = $baseValue - $yagBaseValue;

    #add the current UPDATE statement to the to-be-executed array
    $query = "UPDATE $dsSchema.__rptcube_$cubeID SET $colName=$val \
        WHERE product='$prodID' AND geography='$geoID' AND time='$timeID'";
    push(@updateArray, $query);
  }

  #execute any remaining queued up UPDATE statements
  foreach $query (@updateArray)
  {
    $status = $db->do($query);
    meas_db_err($db, $status, $query);
  }

  $_measureCalculated{$calcMeasureID} = 1;
}



#-------------------------------------------------------------------------
#
# Recalculate the specified % Change measure inside the specified data cube.
# XXX - For now, assumes we're calculating the change from the past year
#

sub cube_calc_pct_change
{
  my ($query, $dbOutput, $timeEnd, $yagEnd, $yagBaseValue, $baseValue, $val);
  my ($colName, $timeType, $timeDuration, $timePeriods, $status, $key, $pct);
  my ($measureName, $count, $totalStructures, $prodID, $geoID, $timeID);
  my (@yagPeriods, @updateArray);
  my (%rowHash);

  my ($db, $dsSchema, $calcMeasureID, $baseMeasureID, $cubeID) = @_;


  $colName = "measure_" . $calcMeasureID;
  $baseColName = "measure_" . $baseMeasureID;

  $measureName = DSRmeasures_name_by_id($db, $dsSchema, $calcMeasureID);

  #grab a list of all TGP tuples containing aggs or segs or hierarchies
  $query = "SELECT product, geography, time FROM $dsSchema.__rptcube_$cubeID \
      WHERE product LIKE 'AGG_%' OR product LIKE 'SMT_%' OR product LIKE 'SHS_%' OR geography LIKE 'AGG_%' OR geography LIKE 'SMT_%' OR time LIKE 'AGG_%' OR time LIKE 'SMT_%'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  meas_db_err($db, $status, $query);

  $totalStructures = $status;

  while (($prodID, $geoID, $timeID) = $dbOutput->fetchrow_array)
  {
    $key = "$prodID.$geoID.$timeID";
    $rowHash{$key} = 1;
  }

  #calculate the actual change for all base prod/geo combinations
  #NB: this is a performance-at-the-expense-of-readability enhancement that
  #    massively speeds up large item-level reports that use aggregated
  #    time periods
  #NB: this is designed so you can comment out the entire block, and the
  #    original algorithm will still calculate all values
  Lib::BuildCube::cube_set_status($db, $cubeID, "Calculating measure $measureName");

  @baseProdIDs = datasel_get_selected_base_items($db, $cubeID, "p");
  @baseGeoIDs = datasel_get_selected_base_items($db, $cubeID, "g");
  @timeIDs = datasel_get_dimension_items($db, $cubeID, "t");

  $count = 0;
  $baseProdStr = join(',', @baseProdIDs);
  $baseGeoStr = join(',', @baseGeoIDs);
  $aggRule = DSRmeasures_get_agg_rule($db, $dsSchema, $baseMeasureID, "t");

  #build up a hash of all YAGO values for the TGP tuples in the report
  foreach $timeID (@timeIDs)
  {
    #ignore base time periods or selections with no base products
    if (($timeID =~ m/^\d+$/) || (length($baseProdStr) < 1))
    {
      next;
    }

    #get matching yag periods
    @yagPeriods = DSRMeasures_get_yago_array($db, $dsSchema, $timeID);
    if (!@yagPeriods)
    {
      next;
    }

    $timePeriods = join(',', @yagPeriods);

    #get the aggregated measure value for all prod/geo combinations
    $query = "SELECT productID, geographyID, $aggRule($baseColName) \
        FROM $dsSchema.facts \
        WHERE productID IN ($baseProdStr) AND geographyID IN ($baseGeoStr) AND timeID IN ($timePeriods) \
        GROUP BY productID, geographyID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);

    #update report cube with aggregated values
    while (($prodID, $geoID, $val) = $dbOutput->fetchrow_array)
    {
      #if the aggregated value isn't NULL
      if (defined($val))
      {
        $key = "$prodID.$geoID.$timeID";
        $yagValuesHash{$key} = $val;
      }
    }
  }

  foreach $timeID (@timeIDs)
  {

    #ignore base time periods or selections with no base products
    if (($timeID =~ m/^\d+$/) || (length($baseProdStr) < 1))
    {
      next;
    }

    #get current time periods that make up the aggregate
    @curPeriods = DSRagg_expand_items($db, $dsSchema, "t", $timeID);
    if (!@curPeriods)
    {
      next;
    }

    $timePeriods = join(',', @curPeriods);

    #get the aggregated measure value for all prod/geo combinations
    $query = "SELECT productID, geographyID, $aggRule($baseColName) \
        FROM $dsSchema.facts \
        WHERE productID IN ($baseProdStr) AND geographyID IN ($baseGeoStr) AND timeID IN ($timePeriods) \
        GROUP BY productID, geographyID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);

    #update report cube with calculated values
    while (($prodID, $geoID, $baseValue) = $dbOutput->fetchrow_array)
    {

      #if the aggregated value isn't NULL
      $key = "$prodID.$geoID.$timeID";
      if ((defined($yagValuesHash{$key})) && ($yagValuesHash{$key} != 0))
      {

        #calculate our change between now and the YAG period
        $val = (($baseValue - $yagValuesHash{$key}) / $yagValuesHash{$key}) * 100;

        $query = "UPDATE $dsSchema.__rptcube_$cubeID SET $colName=$val \
            WHERE product='$prodID' AND geography='$geoID' AND time='$timeID'";
        $status = $db->do($query);
        meas_db_err($db, $status, $query);
      }
    }

    #delete the TGP tuples from the list awaiting calculation
    #NB: we're doing this down here using the arrays instead of up in the
    #    database loop above bc the DB loop doesn't include prods/geos that
    #    don't have any values in facts, and we don't want to try
    #    recalculating those below
    foreach $prodID (@baseProdIDs)
    {
      foreach $geoID (@baseGeoIDs)
      {
        $key = "$prodID.$geoID.$timeID";
        delete($rowHash{$key});

        $count++;
      }
    }
  }
  ############ END OPTIMIZATION ###########################

  #the following code block handles any lags that require cross-calculation
  #across structures in more than one dimension

  #foreach line of cube data containing an agg/seg structure, recalculate
  #this calculated measure
  #NB: For SQL performance reasons, we're queueing up 500 UPDATE statements and
  #    running them in a giant block inside a transaction
  $count = 0;
  foreach $key (keys %rowHash)
  {
    $key =~ m/^(.*?)\.(.*?)\.(.*)$/;
    $prodID = $1;
    $geoID = $2;
    $timeID = $3;

    $count++;
    #every 500 structures, update the user and commit our updates
    if (($count % 500) == 0)
    {
      $pct = ($count / $totalStructures) * 100;
      $pct = int($pct);
      Lib::BuildCube::cube_set_status($db, $cubeID, "Calculating measure $measureName $pct\% ($count of $totalStructures)");

      #execute all of the queued up UPDATE statements
      foreach $query (@updateArray)
      {
        $status = $db->do($query);
        meas_db_err($db, $status, $query);
      }
      undef(@updateArray);
    }

    #if we're dealing with a DSR structure in the timeID field, we have to do
    #some gyrations since this is a time-dependent measure: call this function
    #to get an array of the matching year-ago time periods, if return is
    #undefined then there isn't one and we move on
    @yagPeriods = DSRMeasures_get_yago_array($db, $dsSchema, $timeID);

    #if we didn't find a matching year ago entry, return
    if (!@yagPeriods)
    {
      next;
    }

    $timePeriods = join(',', @yagPeriods);

    #get our year ago base value
    $yagBaseValue = DSRmeasures_get_agg_measure_val($db, $dsSchema, $cubeID, $baseMeasureID, $prodID, $geoID, $yagEnd, $timePeriods);

    #if the value can't be aggregated
    if ($yagBaseValue eq "NULL")
    {
      next;
    }

    #get our current base value
    $baseValue = DSRmeasures_get_agg_measure_val($db, $dsSchema, $cubeID, $baseMeasureID, $prodID, $geoID, $timeID);

    #if the value can't be aggregated
    if ($baseValue eq "NULL")
    {
      next;
    }

    #if the calculation isn't going to result in an illegal division by zero
    if ($yagBaseValue != 0)
    {
      #calculate the pct change between now and our YAG value
      $val = (($baseValue - $yagBaseValue) / $yagBaseValue) * 100;

      #update the value in our cube
      $query = "UPDATE $dsSchema.__rptcube_$cubeID SET $colName=$val \
          WHERE product='$prodID' AND geography='$geoID' AND time='$timeID'";
      push(@updateArray, $query);
    }
  }

  #execute any remaining queued up UPDATE statements
  foreach $query (@updateArray)
  {
    $status = $db->do($query);
    meas_db_err($db, $status, $query);
  }

  $_measureCalculated{$calcMeasureID} = 1;
}



#-------------------------------------------------------------------------
#
# Recalculate the specified lag measure inside the specified data cube.
# XXX - For now, assumes we're calculating the lag from the past year
#

sub cube_calc_lag
{
  my ($query, $dbOutput, $timeEnd, $yagEnd, $yagBaseValue, $baseValue, $val);
  my ($colName, $timeDuration, $timeType, $timePeriods, $status, $key, $pct);
  my ($measureName, $count, $totalStructures, $prodID, $geoID, $timeID);
  my (@yagPeriods, @updateArray, @baseProdIDs, @baseGeoIDs, @timeIDs);
  my (%rowHash);

  my ($db, $dsSchema, $calcMeasureID, $baseMeasureID, $cubeID) = @_;


  $colName = "measure_" . $calcMeasureID;
  $baseColName = "measure_" . $baseMeasureID;
  $measureName = DSRmeasures_name_by_id($db, $dsSchema, $calcMeasureID);

  #grab a list of all TGP tuples containing aggs or segs or hierarchies
  $query = "SELECT product, geography, time FROM $dsSchema.__rptcube_$cubeID \
      WHERE product LIKE 'AGG_%' OR product LIKE 'SMT_%' OR product LIKE 'SHS_%' OR geography LIKE 'AGG_%' OR geography LIKE 'SMT_%' OR time LIKE 'AGG_%' OR time LIKE 'SMT_%'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  meas_db_err($db, $status, $query);

  $totalStructures = $status;

  while (($prodID, $geoID, $timeID) = $dbOutput->fetchrow_array)
  {
    $key = "$prodID.$geoID.$timeID";
    $rowHash{$key} = 1;
  }

  #calculate the lag for all base prod/geo combinations
  #NB: this is a performance-at-the-expense-of-readability enhancement that
  #    massively speeds up large item-level reports that use aggregated
  #    time periods
  Lib::BuildCube::cube_set_status($db, $cubeID, "Calculating measure $measureName");

  @baseProdIDs = datasel_get_selected_base_items($db, $cubeID, "p");
  @baseGeoIDs = datasel_get_selected_base_items($db, $cubeID, "g");
  @timeIDs = datasel_get_dimension_items($db, $cubeID, "t");

  $count = 0;
  $baseProdStr = join(',', @baseProdIDs);
  $baseGeoStr = join(',', @baseGeoIDs);
  $aggRule = DSRmeasures_get_agg_rule($db, $dsSchema, $baseMeasureID, "t");
  foreach $timeID (@timeIDs)
  {

    #ignore base time periods
    if ($timeID =~ m/^\d+$/)
    {
      next;
    }

    #get matching yag periods
    @yagPeriods = DSRMeasures_get_yago_array($db, $dsSchema, $timeID);
    if (!@yagPeriods)
    {
      next;
    }

    $timePeriods = join(',', @yagPeriods);

    #get the aggregated measure value for all prod/geo combinations
    $query = "SELECT productID, geographyID, $aggRule($baseColName) \
        FROM $dsSchema.facts \
        WHERE productID IN ($baseProdStr) AND geographyID IN ($baseGeoStr) AND timeID IN ($timePeriods) \
        GROUP BY productID, geographyID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);

    #update report cube with aggregated values
    while (($prodID, $geoID, $val) = $dbOutput->fetchrow_array)
    {

      #if the aggregated value isn't NULL
      if (defined($val))
      {
        $query = "UPDATE $dsSchema.__rptcube_$cubeID SET $colName=$val \
            WHERE product='$prodID' AND geography='$geoID' AND time='$timeID'";
        $status = $db->do($query);
        meas_db_err($db, $status, $query);
      }
    }

    #delete the TGP tuples from the list awaiting calculation
    #NB: we're doing this down here using the arrays instead of up in the
    #    database loop above bc the DB loop doesn't include prods/geos that
    #    don't have any values in facts, and we don't want to try
    #    recalculating those below
    foreach $prodID (@baseProdIDs)
    {
      foreach $geoID (@baseGeoIDs)
      {
        $key = "$prodID.$geoID.$timeID";
        delete($rowHash{$key});
        $count++;
      }
    }
  }

  #the following code block handles any lags that require cross-calculation
  #across structures in more than one dimension

  #foreach line of cube data containing an agg/seg structure, recalculate
  #this calculated measure
  #NB: For SQL performance reasons, we're queueing up 500 UPDATE statements and
  #    running them in a giant block inside a transaction
  foreach $key (keys %rowHash)
  {
    $key =~ m/^(.*?)\.(.*?)\.(.*)$/;
    $prodID = $1;
    $geoID = $2;
    $timeID = $3;

    $count++;
    #every 500 structures, update the user and commit our updates
    if (($count % 500) == 0)
    {
      $pct = ($count / $totalStructures) * 100;
      $pct = int($pct);
      Lib::BuildCube::cube_set_status($db, $cubeID, "Calculating measure $measureName $pct\% ($count of $totalStructures)");

      #execute all of the queued up UPDATE statements
      foreach $query (@updateArray)
      {
        $status = $db->do($query);
        meas_db_err($db, $status, $query);
      }
      undef(@updateArray);
    }

    #if we're dealing with a DSR structure in the timeID field, we have to do
    #some gyrations since this is a time-dependent measure: call this function
    #to get an array of the matching year-ago time periods, if return is
    #undefined then there isn't one and we move on
    @yagPeriods = DSRMeasures_get_yago_array($db, $dsSchema, $timeID);

    #if we didn't find a matching year ago entry, return
    if (!@yagPeriods)
    {
      next;
    }

    $timePeriods = join(',', @yagPeriods);

    #get our year ago base value
    $yagBaseValue = DSRmeasures_get_agg_measure_val($db, $dsSchema, $cubeID, $baseMeasureID, $prodID, $geoID, $yagEnd, $timePeriods);

    #if the value can't be aggregated
    if ($yagBaseValue eq "NULL")
    {
      next;
    }

    #set the lag to last year's value for the base measure
    $val = $yagBaseValue;

    #add the current UPDATE statement to the to-be-executed array
    $query = "UPDATE $dsSchema.__rptcube_$cubeID SET $colName=$val \
        WHERE product='$prodID' AND geography='$geoID' AND time='$timeID'";
    push(@updateArray, $query);
  }

  #execute any remaining queued up UPDATE statements
  foreach $query (@updateArray)
  {
    $status = $db->do($query);
    meas_db_err($db, $status, $query);
  }

  $_measureCalculated{$calcMeasureID} = 1;
}



#-------------------------------------------------------------------------
#
# Recalculate the specified lead measure inside the specified data cube.
# XXX - For now, assumes we're calculating the lead for the next year
#

sub cube_calc_lead
{
  my ($query, $dbOutput, $timeEnd, $yaheadEnd, $yaheadBaseValue, $baseValue);
  my ($colName, $timeDuration, $timeType, $timePeriods, $status, $key, $val);
  my ($measureName, $count, $totalStructures, $prodID, $geoID, $timeID, $pct);
  my ($yagEnd);
  my (@yaheadPeriods, @updateArray);
  my (%rowHash);

  my ($db, $dsSchema, $calcMeasureID, $baseMeasureID, $cubeID) = @_;


  $colName = "measure_" . $calcMeasureID;
  $measureName = DSRmeasures_name_by_id($db, $dsSchema, $calcMeasureID);

  #grab a list of all TGP tuples containing aggs or segs or hierarchies
  $query = "SELECT product, geography, time FROM $dsSchema.__rptcube_$cubeID \
      WHERE product LIKE 'AGG_%' OR product LIKE 'SMT_%' OR product LIKE 'SHS_%' OR geography LIKE 'AGG_%' OR geography LIKE 'SMT_%' OR time LIKE 'AGG_%' OR time LIKE 'SMT_%'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  meas_db_err($db, $status, $query);

  $totalStructures = $status;

  while (($prodID, $geoID, $timeID) = $dbOutput->fetchrow_array)
  {
    $key = "$prodID.$geoID.$timeID";
    $rowHash{$key} = 1;
  }

  #foreach line of cube data containing an agg/seg structure, recalculate
  #this calculated measure
  #NB: For SQL performance reasons, we're queueing up 500 UPDATE statements and
  #    running them in a giant block to help out the buffer pool
  $count = 0;
  foreach $key (keys %rowHash)
  {
    $key =~ m/^(.*?)\.(.*?)\.(.*)$/;
    $prodID = $1;
    $geoID = $2;
    $timeID = $3;

    $count++;
    #every 500 structures, update the user and commit our updates
    if (($count % 500) == 0)
    {
      $pct = ($count / $totalStructures) * 100;
      $pct = int($pct);
      Lib::BuildCube::cube_set_status($db, $cubeID, "Calculating measure $measureName $pct\% ($count of $totalStructures)");

      #execute all of the queued up UPDATE statements
      foreach $query (@updateArray)
      {
        $status = $db->do($query);
        meas_db_err($db, $status, $query);
      }
      undef(@updateArray);
    }

    #if we're dealing with a DSR structure in the timeID field, we have to do
    #some gyrations since this is a time-dependent measure: call this function
    #to get an array of the matching year-ahead time periods, if return is
    #undefined then there isn't one and we move on
    @yaheadPeriods = DSRMeasures_get_yahead_array($db, $dsSchema, $timeID);

    #if we didn't find a matching year ago entry, return
    if (!@yaheadPeriods)
    {
      next;
    }

    $timePeriods = join(',', @yaheadPeriods);

    #get our year ago base value
    $yaheadBaseValue = DSRmeasures_get_agg_measure_val($db, $dsSchema, $cubeID, $baseMeasureID, $prodID, $geoID, $yagEnd, $timePeriods);

    #if the value can't be aggregated
    if ($yaheadBaseValue eq "NULL")
    {
      next;
    }

    #set the lag to last year's value for the base measure
    $val = $yaheadBaseValue;

    #add the current UPDATE statement to the to-be-executed array
    $query = "UPDATE $dsSchema.__rptcube_$cubeID SET $colName=$val \
        WHERE product='$prodID' AND geography='$geoID' AND time='$timeID'";
    push(@updateArray, $query);
  }

  #execute any remaining queued up UPDATE statements
  foreach $query (@updateArray)
  {
    $status = $db->do($query);
    meas_db_err($db, $status, $query);
  }

  $_measureCalculated{$calcMeasureID} = 1;
}



#-------------------------------------------------------------------------
#
# Recalculate the specified moving average measure inside the specified data
# cube.
#

sub cube_calc_mov_avg
{
  my ($query, $dbOutput, $colName, $baseColName, $duration, $type, $key);
  my ($endDate, $status, $id, $val, $measureVal, $count, $colName, $pct);
  my ($measureName, $totalStructures, $prodID, $geoID, $timeID, $measureCount);
  my (@valArray, @updateArray);
  my (%rowHash);

  my ($db, $dsSchema, $calcMeasureID, $baseMeasureID, $periods, $timeDirection, $cubeID) = @_;


  $colName = "measure_" . $calcMeasureID;
  $baseColName = "measure_" . $baseMeasureID;
  $measureName = DSRmeasures_name_by_id($db, $dsSchema, $calcMeasureID);

  #grab a list of all TGP tuples containing aggs or segs or hierarchies
  $query = "SELECT product, geography, time FROM $dsSchema.__rptcube_$cubeID \
      WHERE product LIKE 'AGG_%' OR product LIKE 'SMT_%' OR product LIKE 'SHS_%' OR geography LIKE 'AGG_%' OR geography LIKE 'SMT_%' OR time LIKE 'AGG_%' OR time LIKE 'SMT_%'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  meas_db_err($db, $status, $query);

  $totalStructures = $status;

  while (($prodID, $geoID, $timeID) = $dbOutput->fetchrow_array)
  {
    $key = "$prodID.$geoID.$timeID";
    $rowHash{$key} = 1;
  }

  #foreach line of cube data containing an agg/seg structure, recalculate
  #this calculated measure
  #NB: For SQL performance reasons, we're queueing up 500 UPDATE statements and
  #    running them in a giant block
  $count = 0;
  foreach $key (keys %rowHash)
  {
    $key =~ m/^(.*?)\.(.*?)\.(.*)$/;
    $prodID = $1;
    $geoID = $2;
    $timeID = $3;

    $count++;
    #every 500 structures, update the user and commit our updates
    if (($count % 500) == 0)
    {
      $pct = ($count / $totalStructures) * 100;
      $pct = int($pct);
      Lib::BuildCube::cube_set_status($db, $cubeID, "Calculating measure $measureName $pct\% ($count of $totalStructures)");

      #execute all of the queued up UPDATE statements
      foreach $query (@updateArray)
      {
        $status = $db->do($query);
        meas_db_err($db, $status, $query);
      }
      undef(@updateArray);
    }

    #grab the time period info from our supplied timeID
    $query = "SELECT duration, type, endDate FROM $dsSchema.timeperiods \
        WHERE ID=$timeID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);
    ($duration, $type, $endDate) = $dbOutput->fetchrow_array;

    #if we're a custom measure, we can't calculate so the value is NULL
    if ($type < 1)
    {
      next;
    }

    #make sure there are enough time periods in the data source
    if ($timeDirection eq "future")
    {
      $query = "SELECT ID FROM $dsSchema.timeperiods \
          WHERE duration=$duration AND type=$type AND endDate >= '$endDate' \
          ORDER BY endDate ASC LIMIT $periods";
    }
    else
    {
      $query = "SELECT ID FROM $dsSchema.timeperiods \
          WHERE duration=$duration AND type=$type AND endDate <= '$endDate' \
          ORDER BY endDate DESC LIMIT $periods";
    }
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);
    if ($status < $periods)
    {
      next;     #not enough periods, measure value is NULL
    }

    #grab the aggregated value for each time period for our prod and geo
    undef(@valArray);
    while (($id) = $dbOutput->fetchrow_array)
    {
      $val = DSRmeasures_get_agg_measure_val($db, $dsSchema, $cubeID, $baseMeasureID, $prodID, $geoID, $id);

      #if the value can't be aggregated
      if ($val eq "NULL")
      {
        next;
      }

      push(@valArray, $val);
    }

    #average the values together
    $measureVal = 0;
    $measureCount = 0;
    foreach $val (@valArray)
    {
      $measureVal += $val;
      $measureCount++;
    }
    if ($measureCount == 0)
    {
      $measureVal = "NULL";
    }
    else
    {
      $measureVal = $measureVal / $measureCount;
    }

    #update the value in our cube
    $query = "UPDATE $dsSchema.__rptcube_$cubeID SET $colName=$measureVal \
        WHERE product='$prodID' AND geography='$geoID' AND time='$timeID'";
    push(@updateArray, $query);
  }

  foreach $query (@updateArray)
  {
    $status = $db->do($query);
    meas_db_err($db, $status, $query);
  }

  $_measureCalculated{$calcMeasureID} = 1;
}



#-------------------------------------------------------------------------
#
# Recalculate the specified moving total measure inside the specified data cube.
#

sub cube_calc_mov_total
{
  my ($query, $dbOutput, $colName, $baseColName, $aggRule, $duration, $type);
  my ($endDate, $id, $measureVal, $val, $status, $count, $key, $pct);
  my ($measureName, $totalStructures, $prodID, $geoID, $timeID);
  my (@valArray, @updateArray);
  my (%rowHash);

  my ($db, $dsSchema, $measureID, $baseMeasureID, $periods, $timeDirection, $cubeID) = @_;


  $colName = "measure_" . $measureID;
  $baseColName = "measure_" . $baseMeasureID;
  $measureName = DSRmeasures_name_by_id($db, $dsSchema, $measureID);

  #grab a list of all TGP tuples containing aggs or segs or hierarchies
  $query = "SELECT product, geography, time FROM $dsSchema.__rptcube_$cubeID \
      WHERE product LIKE 'AGG_%' OR product LIKE 'SMT_%' OR product LIKE 'SHS_%' OR geography LIKE 'AGG_%' OR geography LIKE 'SMT_%' OR time LIKE 'AGG_%' OR time LIKE 'SMT_%'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  meas_db_err($db, $status, $query);

  $totalStructures = $status;

  while (($prodID, $geoID, $timeID) = $dbOutput->fetchrow_array)
  {
    $key = "$prodID.$geoID.$timeID";
    $rowHash{$key} = 1;
  }

  #get the time period aggregation rule for this measure
  #NB: If there isn't one, we return leaving the measure value NULL
  $query = "SELECT timeAggRule FROM $dsSchema.measures WHERE ID=$baseMeasureID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  meas_db_err($db, $status, $query);
  ($aggRule) = $dbOutput->fetchrow_array;
  if (length($aggRule) < 1)
  {
    return;
  }

  #foreach line of cube data containing an agg/seg structure, recalculate
  #this calculated measure
  #NB: For SQL performance reasons, we're queueing up 500 UPDATE statements and
  #    running them in a giant block
  $count = 0;
  foreach $key (keys %rowHash)
  {
    $key =~ m/^(.*?)\.(.*?)\.(.*)$/;
    $prodID = $1;
    $geoID = $2;
    $timeID = $3;

    $count++;
    #every 500 structures, update the user and commit our updates
    if (($count % 500) == 0)
    {
      $pct = ($count / $totalStructures) * 100;
      $pct = int($pct);
      Lib::BuildCube::cube_set_status($db, $cubeID, "Calculating measure $measureName $pct\% ($count of $totalStructures)");

      #execute all of the queued up UPDATE statements
      foreach $query (@updateArray)
      {
        $status = $db->do($query);
        meas_db_err($db, $status, $query);
      }
      undef(@updateArray);
    }

    #grab the time period info from our supplied timeID
    $query = "SELECT duration, type, endDate FROM $dsSchema.timeperiods \
        WHERE ID=$timeID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);
    ($duration, $type, $endDate) = $dbOutput->fetchrow_array;

    #if we're a custom time period, we can't calculate so the value is NULL
    if ($type < 1)
    {
      next;
    }

    #make sure there are enough time periods in the data source
    if ($timeDirection eq "future")
    {
      $query = "SELECT ID FROM $dsSchema.timeperiods \
          WHERE duration=$duration AND type=$type AND endDate >= '$endDate' \
          ORDER BY endDate ASC LIMIT $periods";
    }
    else
    {
      $query = "SELECT ID FROM $dsSchema.timeperiods \
          WHERE duration=$duration AND type=$type AND endDate <= '$endDate' \
          ORDER BY endDate DESC LIMIT $periods";
    }
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);
    if ($status < $periods)
    {
      next;     #not enough periods, measure value is NULL
    }

    #grab the aggregated value for each time period for our prod and geo
    undef(@valArray);
    while (($id) = $dbOutput->fetchrow_array)
    {
      $val = DSRmeasures_get_agg_measure_val($db, $dsSchema, $cubeID, $baseMeasureID, $prodID, $geoID, $id);

      #if the value can't be aggregated
      if ($val eq "NULL")
      {
        next;
      }

      push(@valArray, $val);
    }

    #aggregate the values together using the specified time aggregation rule
    if ($aggRule eq "AVG")
    {
      $measureVal = 0;
      $count = 0;
      foreach $val (@valArray)
      {
        $measureVal = $measureVal + $val;
        $count++;
      }
      $measureVal = $measureVal / $count;
    }
    if ($aggRule eq "COUNT")
    {
      $measureVal = 0;
      foreach $val (@valArray)
      {
        $measureVal++;
      }
    }
    if ($aggRule eq "MAX")
    {
      $measureVal = $valArray[0];
      foreach $val (@valArray)
      {
        if ($val > $measureVal)
        {
          $measureVal = $val;
        }
      }
    }
    if ($aggRule eq "MIN")
    {
      $measureVal = $valArray[0];
      foreach $val (@valArray)
      {
        if ($val < $measureVal)
        {
          $measureVal = $val;
        }
      }
    }
    if ($aggRule eq "SUM")
    {
      $measureVal = 0;
      foreach $val (@valArray)
      {
        $measureVal = $measureVal + $val;
      }
    }

    #update the value in our cube
    $query = "UPDATE $dsSchema.__rptcube_$cubeID SET $colName=$measureVal \
        WHERE product='$prodID' AND geography='$geoID' AND time='$timeID'";
    push(@updateArray, $query);
  }

  foreach $query (@updateArray)
  {
    $status = $db->do($query);
    meas_db_err($db, $status, $query);
  }

  $_measureCalculated{$measureID} = 1;
}



#-------------------------------------------------------------------------
#
# Recalculate the specified year to date measure for the specified TGP tuple
# inside the specified data cube. Effectively implements the "calculate
# after aggregate" functionality.
#

sub cube_calc_ytd
{
  my ($query, $dbOutput, $colName, $baseColName, $duration, $type, $end, $key);
  my ($val, $dbOutput1, $id, $aggRule, $measureVal, $count, $status, $pct);
  my ($measureName, $totalStructures, $prodID, $geoID, $timeID);
  my (@valArray, @updateArray);
  my (%rowHash);

  my ($db, $dsSchema, $measureID, $baseMeasureID, $cubeID) = @_;


  $colName = "measure_" . $measureID;
  $baseColName = "measure_" . $baseMeasureID;
  $measureName = DSRmeasures_name_by_id($db, $dsSchema, $measureID);

  #grab a list of all TGP tuples containing aggs or segs or hierarchies
  $query = "SELECT product, geography, time FROM $dsSchema.__rptcube_$cubeID \
      WHERE product LIKE 'AGG_%' OR product LIKE 'SMT_%' OR product LIKE 'SHS_%' OR geography LIKE 'AGG_%' OR geography LIKE 'SMT_%' OR time LIKE 'AGG_%' OR time LIKE 'SMT_%'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  meas_db_err($db, $status, $query);

  $totalStructures = $status;

  while (($prodID, $geoID, $timeID) = $dbOutput->fetchrow_array)
  {
    $key = "$prodID.$geoID.$timeID";
    $rowHash{$key} = 1;
  }

  #get the time period aggregation rule for this measure
  #NB: If there isn't one, we exit out leaving all measure values NULL
  $query = "SELECT timeAggRule FROM $dsSchema.measures WHERE ID=$baseMeasureID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  meas_db_err($db, $status, $query);
  ($aggRule) = $dbOutput->fetchrow_array;
  if (length($aggRule) < 1)
  {
    return;
  }

  #foreach line of cube data containing an agg/seg structure, recalculate
  #this calculated measure
  #NB: For SQL performance reasons, we're queueing up 500 UPDATE statements and
  #    running them in a giant block
  $count = 0;
  foreach $key (keys %rowHash)
  {
    $key =~ m/^(.*?)\.(.*?)\.(.*)$/;
    $prodID = $1;
    $geoID = $2;
    $timeID = $3;

    $count++;
    #every 500 structures, update the user and commit our updates
    if (($count % 500) == 0)
    {
      $pct = ($count / $totalStructures) * 100;
      $pct = int($pct);
      Lib::BuildCube::cube_set_status($db, $cubeID, "Calculating measure $measureName $pct\% ($count of $totalStructures)");

      #execute all of the queued up UPDATE statements
      foreach $query (@updateArray)
      {
        $status = $db->do($query);
        meas_db_err($db, $status, $query);
      }
      undef(@updateArray);
    }

    #grab the duration, type, and endDate info for the specified time period
    $query = "SELECT duration, type, endDate FROM $dsSchema.timeperiods \
        WHERE ID=$timeID";
    $dbOutput1 = $db->prepare($query);
    $status = $dbOutput1->execute;
    meas_db_err($db, $status, $query);
    ($duration, $type, $end) = $dbOutput1->fetchrow_array;

    #if we're a custom time period, we can't calculate
    if ($type < 1)
    {
      return;
    }

    #grab the IDs of the time periods from the year with same type/duration
    $query = "SELECT ID FROM $dsSchema.timeperiods \
        WHERE duration=$duration AND type=$type and YEAR(endDate) = YEAR('$end')";
    $dbOutput1 = $db->prepare($query);
    $status = $dbOutput1->execute;
    meas_db_err($db, $status, $query);

    #run through the list of matching timeIDs, and get their measure values
    undef(@valArray);
    ($id) = $dbOutput1->fetchrow_array;
    while (defined($id))
    {
      $val = DSRmeasures_get_agg_measure_val($db, $dsSchema, $cubeID, $baseMeasureID, $prodID, $geoID, $id);

      #if the value can't be aggregated
      if ($val eq "NULL")
      {
        return;
      }

      push(@valArray, $val);
      ($id) = $dbOutput1->fetchrow_array;
    }

    #aggregate the values together using the specified time aggregation rule
    if ($aggRule eq "AVG")
    {
      $measureVal = 0;
      $count = 0;
      foreach $val (@valArray)
      {
        $measureVal = $measureVal + $val;
        $count++;
      }
      $measureVal = $measureVal / $count;
    }
    if ($aggRule eq "COUNT")
    {
      $measureVal = 0;
      foreach $val (@valArray)
      {
        $measureVal++;
      }
    }
    if ($aggRule eq "MAX")
    {
      $measureVal = $valArray[0];
      foreach $val (@valArray)
      {
        if ($val > $measureVal)
        {
          $measureVal = $val;
        }
      }
    }
    if ($aggRule eq "MIN")
    {
      $measureVal = $valArray[0];
      foreach $val (@valArray)
      {
        if ($val < $measureVal)
        {
          $measureVal = $val;
        }
      }
    }
    if ($aggRule eq "SUM")
    {
      $measureVal = 0;
      foreach $val (@valArray)
      {
        $measureVal = $measureVal + $val;
      }
    }

    #update the value in our cube
    $query = "UPDATE $dsSchema.__rptcube_$cubeID SET $colName=$measureVal \
        WHERE product='$prodID' AND geography='$geoID' AND time='$timeID'";
    push(@updateArray, $query);
  }

  foreach $query (@updateArray)
  {
    $status = $db->do($query);
    meas_db_err($db, $status, $query);
  }

  $_measureCalculated{$measureID} = 1;
}



#-------------------------------------------------------------------------
#
# Recalculate the count measure for the specified TGP tuple
# inside the specified data cube. Effectively implements the "calculate
# after aggregate" functionality.
#

sub cube_calc_count
{
  my ($query, $dbOutput, $colName, $val, $measureID, $masterVal, $share);
  my ($calcProdVal, $measureName, $whereClause, $dbCol, $dimCol, $key);
  my ($recalc, $dbName, $status, $count, $totalStructures, $prodID, $geoID);
  my ($timeID, $pct);
  my (@updateArray);
  my (%rowHash);

  my ($db, $dsSchema, $calcMeasureID, $dim, $cubeID) = @_;


  $colName = "measure_" . $calcMeasureID;
  $measureName = DSRmeasures_name_by_id($db, $dsSchema, $calcMeasureID);

  if ($dim eq "p")
  {
    $dimCol = "productID";
  }
  elsif ($dim eq "g")
  {
    $dimCol = "geographyID";
  }
  elsif ($dim eq "t")
  {
    $dimCol = "timeID";
  }

  #grab a list of all TGP tuples containing aggs or segs or hierarchies
  $query = "SELECT product, geography, time FROM $dsSchema.__rptcube_$cubeID \
      WHERE product LIKE 'AGG_%' OR product LIKE 'SMT_%' OR product LIKE 'SHS_%' \
          OR geography LIKE 'AGG_%' OR geography LIKE 'SMT_%' \
          OR time LIKE 'AGG_%' OR time LIKE 'SMT_%'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  meas_db_err($db, $status, $query);

  $totalStructures = $status;

  while (($prodID, $geoID, $timeID) = $dbOutput->fetchrow_array)
  {
    $key = "$prodID.$geoID.$timeID";
    $rowHash{$key} = 1;
  }

  #foreach line of cube data containing an agg/seg structure, recalculate
  #this calculated measure
  $count = 0;
  foreach $key (keys %rowHash)
  {
    $key =~ m/^(.*?)\.(.*?)\.(.*)$/;
    $prodID = $1;
    $geoID = $2;
    $timeID = $3;

    $count++;
    #every 500 structures, update the user
    if (($count % 500) == 0)
    {
      $pct = ($count / $totalStructures) * 100;
      $pct = int($pct);
      Lib::BuildCube::cube_set_status($db, $cubeID, "Calculating measure $measureName $pct\% ($count of $totalStructures)");
    }

    #expand all of the dimension items we're working with
    $prodIDs = DSRstructure_expand($db, $dsSchema, "p", $prodID);
    $geoIDs = DSRstructure_expand($db, $dsSchema, "g", $geoID);
    $timeIDs = DSRstructure_expand($db, $dsSchema, "t", $timeID);

    #build our SQL query to return the count of base items in the current
    #prod/geo/time structure combo that meet the conditional specified in the
    #measure definition (if there is one)
    #NB: we don't have to set up and apply any conditional specified in the
    #   measure here, since the conditional has already been applied at the
    #   item level in the data source and we're using those item-level values
    $query = "SELECT DISTINCT $dimCol FROM $dsSchema.facts \
        WHERE productID IN ($prodIDs) AND geographyID IN ($geoIDs) AND timeID IN ($timeIDs) \
            AND $colName = 1";
    $dbOutput = $db->prepare($query);
    $val = $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);

    #save the count value
    #TODO: push to array and bulk-apply 500 at a time
    #add the current UPDATE statement to the to-be-executed array
    $query = "UPDATE $dsSchema.__rptcube_$cubeID SET $colName=$val \
        WHERE product='$prodID' AND geography='$geoID' AND time='$timeID'";
    $status = $db->do($query);
    meas_db_err($db, $status, $query);
  }

  $_measureCalculated{$calcMeasureID} = 1;
}



#-------------------------------------------------------------------------
#
# Recalculate the index measure inside the specified data cube.
#

sub cube_calc_index
{
  my ($query, $dbOutput, $colName, $val, $measureID, $avgVal, $index);
  my ($calcProdVal, $tmp, $segmentID, $segmentationID, $i, $pct);
  my ($segHierID, $segHierSegs, @segmentations, $levelIdx, $done);
  my ($status, $masterHierID, $masterDepth, $count, $totalStructures, $key);
  my ($measureName, $prodID, $geoID, $timeID, $finalStructID);
  my (@updateArray);
  my (%rowHash, %prodHash, %geoTimeHash, %segmentSegHash);

  my ($db, $dsSchema, $calcMeasureID, $dim, $measure, $structType, $structID, $cubeID) = @_;

  $colName = "measure_" . $calcMeasureID;
  $baseColName = "measure_" . $measure;
  $measureName = DSRmeasures_name_by_id($db, $dsSchema, $calcMeasureID);

  Lib::BuildCube::cube_set_status($db, $cubeID, "Calculating measure $measureName");

  $_measureCalculated{$calcMeasureID} = 1;


  ###########################################

  #---- aggregates -----

  #if we're calculating an structure's index against an aggregate
  if ($structType eq "agg")
  {

    #grab a list of all distinct geos and times involved in any aggregated struct
    undef(%geoTimeHash);
    $query = "SELECT DISTINCT geography, time FROM $dsSchema.__rptcube_$cubeID \
        WHERE product LIKE 'AGG_%' OR product LIKE 'SMT_%' OR product LIKE 'SHS_%' OR geography LIKE 'AGG_%' OR geography LIKE 'SMT_%' OR time LIKE 'AGG_%' OR time LIKE 'SMT_%'";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);
    while (($geoID, $timeID) = $dbOutput->fetchrow_array)
    {
      $key = "$geoID.$timeID";
      $geoTimeHash{$key} = 1;
    }

    #grab a list of all distinct products involved in any aggregated struct
    undef(%prodHash);
    $query = "SELECT DISTINCT product FROM $dsSchema.__rptcube_$cubeID WHERE product LIKE 'AGG_%' OR product LIKE 'SMT_%' OR product LIKE 'SHS_%' OR geography LIKE 'AGG_%' OR geography LIKE 'SMT_%' OR time LIKE 'AGG_%' OR time LIKE 'SMT_%'";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);
    while (($prodID) = $dbOutput->fetchrow_array)
    {
      $prodHash{$prodID} = 1;
    }

    #transform our aggregate ID into something the avg calc function expects
    if (!($structID =~ m/^AGG_/))
    {
      $structID = "AGG_" . $structID;
    }

    #for every relevant geo/time combination, calculate the index values
    foreach $key (keys %geoTimeHash)
    {
      $key =~ m/^(.*?)\.(.*?)$/;
      $geoID = $1;
      $timeID = $2;

      #get the aggregate's measure value for the specified geo/time combo
      $avgVal = DSRmeasures_get_avg_measure_val($db, $dsSchema, $cubeID, $measure, $structID, $geoID, $timeID);

      #if the value can't be aggregated, or can't be used in index calc
      if (($avgVal eq "NULL") || ($avgVal == 0))
      {
        next;
      }

      #get the value for each aggregated product, calc the index, & store it
      foreach $prodID (keys %prodHash)
      {

        #get the index item's measure value for the specified geo/time combo
        $calcProdVal = DSRmeasures_get_agg_measure_val($db, $dsSchema, $cubeID, $measure, $prodID, $geoID, $timeID);

        #if the value can't be aggregated
        if ($calcProdVal eq "NULL")
        {
          next;
        }

        #do the index calculation
        $index = ($calcProdVal / $avgVal) * 100;

        #update calculated measure value in cube
        $query = "UPDATE $dsSchema.__rptcube_$cubeID SET $colName=$index \
            WHERE product='$prodID' AND geography='$geoID' AND time='$timeID'";
        $status = $db->do($query);
        meas_db_err($db, $status, $query);
      }
    }

    $_measureCalculated{$calcMeasureID} = 1;

    return;
  }

#########################################

  #grab a list of all TGP tuples containing aggs or segs or hierarchies
  $query = "SELECT product, geography, time FROM $dsSchema.__rptcube_$cubeID \
      WHERE product LIKE 'AGG_%' OR product LIKE 'SMT_%' OR product LIKE 'SHS_%' OR geography LIKE 'AGG_%' OR geography LIKE 'SMT_%' OR time LIKE 'AGG_%' OR time LIKE 'SMT_%'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  meas_db_err($db, $status, $query);

  $totalStructures = $status;

  while (($prodID, $geoID, $timeID) = $dbOutput->fetchrow_array)
  {
    $key = "$prodID.$geoID.$timeID";
    $rowHash{$key} = 1;
  }

  #if we're doing indexing of a segment, build a lookup hash of which segmentation
  #a segment belongs in for performance reasons (dramatically cuts down on SQL
  #SELECTS)
  if ($structType eq "seg")
  {
    $query = "SELECT ID, segmentationID FROM $dsSchema.product_segment";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);

    while (($segmentID, $segID) = $dbOutput->fetchrow_array)
    {
      $segmentSegHash{$segmentID} = $segID;
    }
  }

  #foreach line of cube data containing an agg/seg structure, recalculate
  #this calculated measure
  #NB: For SQL performance reasons, we're queueing up 500 UPDATE statements and
  #    running them in a giant block inside a transaction
  $count = 0;
  foreach $key (keys %rowHash)
  {
    $key =~ m/^(.*?)\.(.*?)\.(.*)$/;
    $prodID = $1;
    $geoID = $2;
    $timeID = $3;

    $count++;
    #every 500 structures, update the user and commit our updates
    if (($count % 500) == 0)
    {
      $pct = ($count / $totalStructures) * 100;
      $pct = int($pct);
      Lib::BuildCube::cube_set_status($db, $cubeID, "Calculating measure $measureName $pct\% ($count of $totalStructures)");

      #execute all of the queued up UPDATE statements
      foreach $query (@updateArray)
      {
        $status = $db->do($query);
        meas_db_err($db, $status, $query);
      }
      undef(@updateArray);
    }


                  #---- items -----

    #if we're calculating a structure's index against an item
    if ($structType eq "item")
    {

      #get the master item's measure value for the specified geo/time combo
      $avgVal = DSRmeasures_get_avg_measure_val($db, $dsSchema, $cubeID, $measure, $structID, $geoID, $timeID);

      #if the value can't be aggregated
      if ($avgVal eq "NULL")
      {
        next;
      }

      #get the index item's measure value for the specified geo/time combo
      $calcProdVal = DSRmeasures_get_agg_measure_val($db, $dsSchema, $cubeID, $measure, $prodID, $geoID, $timeID);

      #if the value can't be aggregated
      if ($calcProdVal eq "NULL")
      {
        next;
      }

      #make sure we don't do a division-by-zero
      if ($avgVal != 0)
      {

        #do the index calculation
        $index = ($calcProdVal / $avgVal) * 100;

        #update calculated measure value in cube
        $query = "UPDATE $dsSchema.__rptcube_$cubeID SET $colName=$index \
            WHERE product='$prodID' AND geography='$geoID' AND time='$timeID'";
        push(@updateArray, $query);
      }
    }


                  #---- segmentations -----

    #if we're calculating a structure's index against a segmentation
    if ($structType eq "seg")
    {

      #if the product ID tells us it's a segment
      if ($prodID =~ m/^SMT_(\d+)/)
      {
        $segmentID = $1;

        #figure out which segmentation the segment is a member of
        $segmentationID = $segmentSegHash{$segmentID};

        #if the segment is in the index's master segmentation, it's 100%
        if ($segmentationID eq $structID)
        {
          $query = "UPDATE $dsSchema.__rptcube_$cubeID SET $colName=100 \
              WHERE product='$prodID' AND geography='$geoID' AND time='$timeID'";
          push(@updateArray, $query);
          next;
        }

        #if we're up against a segmentation the segment isn't a member of, use
        #the segmentation average
        $finalStructID = "SEG_" . $structID;
      }

      #else if the product ID is a hierarchy level
      elsif ($prodID =~ m/^SHS_(\d+)/)
      {

        $segHierID = $1;

        #get the segmentations that make up the hierarchy
        $query = "SELECT segmentations FROM $dsSchema.product_seghierarchy \
            WHERE ID=$segHierID";
        $dbOutput = $db->prepare($query);
        $status = $dbOutput->execute;
        meas_db_err($db, $status, $query);
        ($segHierSegs) = $dbOutput->fetchrow_array;

        @segmentations = split(',', $segHierSegs);

        #figure out which tree level corresponds to the specified seg level
        $levelIdx = 0;
        $done = 0;
        while (($done != 1) && ($levelIdx < 128))
        {
          if ($segmentations[$levelIdx] eq $structID)
          {
            $done = 1;
          }
          else
          {
            $levelIdx++;
          }
        }

        #if we found a matching level (i.e., the segmentation we're calculating
        #an index against is also part of the hierarchy tree
        if ($done > 0)
        {

          #knock the SHS_nn ID off the front of the product ID
          $tmp = $prodID;
          $tmp =~ m/^SHS_\d+_(.*)/;
          $tmp = $1;

          #knock off level ID's until we reach the one matching this segmtn
          $i = 1;
          while ($i < $levelIdx)
          {
            $tmp =~ m/^\d+_(.*)/;
            $tmp = $1;
            $i++;
          }

          #if there are any remaining levels once we've found ours, chop them
          if ($tmp =~ m/^(\d+)_/)
          {
            $tmp = $1;
          }

          $finalStructID = "SMT_" . $tmp;
        }

        #else just use the total value for the segmentation
        else
        {
          $finalStructID = "SEG_" . $structID;
        }

      }

      #else the product ID is a base item
      else
      {
        $segmentID = DSRseg_get_item_membership($db, $dsSchema, $dim, $structID, $prodID);

        #if the item isn't a member of the specified segmentation, drop out
        if (!(defined($segmentID)))
        {
          next;
        }

        $finalStructID = "SMT_" . $segmentID;
      }

      #get the segment's measure value for the specified geo/time combo
      $avgVal = DSRmeasures_get_avg_measure_val($db, $dsSchema, $cubeID, $measure, $finalStructID, $geoID, $timeID);

      #if the value can't be aggregated, or can't be used in an index calc
      if (($avgVal eq "NULL") || ($avgVal == 0))
      {
        next;
      }

      #create the query to calculate the index
      $query = "UPDATE IGNORE $dsSchema.__rptcube_$cubeID SET $colName=($baseColName / $avgVal) * 100 \
          WHERE product='$prodID' AND geography='$geoID' AND time='$timeID'";
      push(@updateArray, $query);
    }


      #---- hierarchies ----

    #if we're calculating another structure's index against a hierarchy level
    if ($structType eq "hier")
    {

      #if we're calculating a hierarchy's index against another hierarchy level
      if ($prodID =~ m/^SHS_/)
      {

        #if the two levels aren't in same hierarchy, the index is NULL
        $segHierID = 0;
        $structID =~ m/^(\d+)_/;
        $segHierID = "SHS_" . $1 . "_";
        if (!($prodID =~ m/^$segHierID/))
        {
          next;
        }

        #count how many levels deep we're going for the average value
        #NB - we get a string like 1_1_1 for the average value. It just tells
        #     us how many levels deep to go in the hierarchy - not the actual
        #     value we want. That's why we're doing this.
        $masterDepth = 0;
        $tmp = $structID;
        while ($tmp =~ m/^(\d+)_(.*)$/)
        {
          $masterDepth++;
          $tmp = $2;
        }

        #build up the master hierarchy's ID
        chop($segHierID);
        $masterHierID = $segHierID;
        $tmp = $prodID;
        $tmp =~ m/SHS_\d+(.*)/;
        $tmp = $1;
        while ($masterDepth > 0)
        {
          $tmp =~ m/^_(\d+)(.*)/;
          $masterHierID .= "_" . $1;
          $tmp = $2;

          $masterDepth--;
        }

        #get the master hierarchy's average value
        $structID = "SHS_" . $structID;
        $avgVal = DSRmeasures_get_avg_measure_val($db, $dsSchema, $cubeID, $measure, $masterHierID, $geoID, $timeID);

        #get the index item's aggregated value
        $calcProdVal = DSRmeasures_get_agg_measure_val($db, $dsSchema, $cubeID, $measure, $prodID, $geoID, $timeID);

        #if the value can't be aggregated
        if ($calcProdVal eq "NULL")
        {
          next;
        }

        #make sure we don't do a division-by-zero
        if ($avgVal != 0)
        {

          #do the index calculation
          $index = ($calcProdVal / $avgVal) * 100;

          #update calculated measure value in cube
          $query = "UPDATE $dsSchema.__rptcube_$cubeID SET $colName=$index \
              WHERE product='$prodID' AND geography='$geoID' AND time='$timeID'";
          push(@updateArray, $query);
        }
      }
    }
  }

  #execute any remaining queued up UPDATE statements
  foreach $query (@updateArray)
  {
    $status = $db->do($query);
    meas_db_err($db, $status, $query);
  }

  $_measureCalculated{$calcMeasureID} = 1;
}



#-------------------------------------------------------------------------
#
# Recalculate the share measure for the specified TGP tuple
# inside the specified data cube. Effectively implements the "calculate
# after aggregate" functionality.

sub cube_calc_share
{
  my ($query, $dbOutput, $colName, $val, $measureID, $masterVal, $share);
  my ($calcProdVal, $tmp, $segmentID, $segmentationID, $i, $pct);
  my ($segHierID, $segHierSegs, @segmentations, $levelIdx, $done);
  my ($status, $masterHierID, $masterDepth, $count, $totalStructures, $key);
  my ($measureName, $prodID, $geoID, $timeID, $finalStructID);
  my (@updateArray);
  my (%rowHash, %prodHash, %geoTimeHash, %segmentSegHash);

  my ($db, $dsSchema, $calcMeasureID, $dim, $measure, $structType, $structID, $cubeID) = @_;

  $colName = "measure_" . $calcMeasureID;
  $baseColName = "measure_" . $measure;
  $measureName = DSRmeasures_name_by_id($db, $dsSchema, $calcMeasureID);

  Lib::BuildCube::cube_set_status($db, $cubeID, "Calculating measure $measureName");

  ###########################################

  #---- aggregates -----

  #if we're calculating a structure's share of an aggregate
  if ($structType eq "agg")
  {

    #grab a list of all distinct geos and times involved in any aggregated struct
    undef(%geoTimeHash);
    $query = "SELECT DISTINCT geography, time FROM $dsSchema.__rptcube_$cubeID \
        WHERE product LIKE 'AGG_%' OR product LIKE 'SMT_%' OR product LIKE 'SHS_%' OR geography LIKE 'AGG_%' OR geography LIKE 'SMT_%' OR time LIKE 'AGG_%' OR time LIKE 'SMT_%'";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);
    while (($geoID, $timeID) = $dbOutput->fetchrow_array)
    {
      $key = "$geoID.$timeID";
      $geoTimeHash{$key} = 1;
    }

    #grab a list of all distinct products involved in any aggregated struct
    undef(%prodHash);
    $query = "SELECT DISTINCT product FROM $dsSchema.__rptcube_$cubeID WHERE product LIKE 'AGG_%' OR product LIKE 'SMT_%' OR product LIKE 'SHS_%' OR geography LIKE 'AGG_%' OR geography LIKE 'SMT_%' OR time LIKE 'AGG_%' OR time LIKE 'SMT_%'";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);
    while (($prodID) = $dbOutput->fetchrow_array)
    {
      $prodHash{$prodID} = 1;
    }

    #transform our aggregate ID into something the agg calc function expects
    if (!($structID =~ m/^AGG_/))
    {
      $structID = "AGG_" . $structID;
    }

    #for every relevant geo/time combination, calculate the agg share values
    foreach $key (keys %geoTimeHash)
    {
      $key =~ m/^(.*?)\.(.*?)$/;
      $geoID = $1;
      $timeID = $2;

      #get the aggregate's measure value for the specified geo/time combo
      $masterVal = DSRmeasures_get_agg_measure_val($db, $dsSchema, $cubeID, $measure, $structID, $geoID, $timeID);

      #if the value can't be aggregated, or can't be used in share calc
      if (($masterVal eq "NULL") || ($masterVal == 0))
      {
        next;
      }

      #get the value for each aggregated product, calc the share, & store it
      #XXX: we're already making sure the share-of-measure is rolled up for us
      #     - we should be able to replace this with a single UPDATE query?
      foreach $prodID (keys %prodHash)
      {

        #get the share item's measure value for the specified geo/time combo
        $calcProdVal = DSRmeasures_get_agg_measure_val($db, $dsSchema, $cubeID, $measure, $prodID, $geoID, $timeID);

        #if the value can't be aggregated
        if ($calcProdVal eq "NULL")
        {
          next;
        }

        #do the share calculation
        $share = ($calcProdVal / $masterVal) * 100;

        #update calculated measure value in cube
        $query = "UPDATE $dsSchema.__rptcube_$cubeID SET $colName=$share \
            WHERE product='$prodID' AND geography='$geoID' AND time='$timeID'";
        $status = $db->do($query);
        meas_db_err($db, $status, $query);
      }
    }

    $_measureCalculated{$calcMeasureID} = 1;

    return;
  }

#########################################

  #grab a list of all TGP tuples containing aggs or segs or hierarchies
  $query = "SELECT product, geography, time FROM $dsSchema.__rptcube_$cubeID \
      WHERE product LIKE 'AGG_%' OR product LIKE 'SMT_%' OR product LIKE 'SHS_%' OR geography LIKE 'AGG_%' OR geography LIKE 'SMT_%' OR time LIKE 'AGG_%' OR time LIKE 'SMT_%'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  meas_db_err($db, $status, $query);

  $totalStructures = $status;

  while (($prodID, $geoID, $timeID) = $dbOutput->fetchrow_array)
  {
    $key = "$prodID.$geoID.$timeID";
    $rowHash{$key} = 1;
  }

  #if we're doing shares of a segment, build a lookup hash of which segmentation
  #a segment belongs in for performance reasons (dramatically cuts down on SQL
  #SELECTS)
  if ($structType eq "seg")
  {
    $query = "SELECT ID, segmentationID FROM $dsSchema.product_segment";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);

    while (($segmentID, $segID) = $dbOutput->fetchrow_array)
    {
      $segmentSegHash{$segmentID} = $segID;
    }
  }

  #foreach line of cube data containing an agg/seg structure, recalculate
  #this calculated measure
  #NB: For SQL performance reasons, we're queueing up 500 UPDATE statements and
  #    running them in a giant block inside a transaction
  $count = 0;
  foreach $key (keys %rowHash)
  {
    $key =~ m/^(.*?)\.(.*?)\.(.*)$/;
    $prodID = $1;
    $geoID = $2;
    $timeID = $3;

    $count++;
    #every 500 structures, update the user and commit our updates
    if (($count % 500) == 0)
    {
      $pct = ($count / $totalStructures) * 100;
      $pct = int($pct);
      Lib::BuildCube::cube_set_status($db, $cubeID, "Calculating measure $measureName $pct\% ($count of $totalStructures)");

      #execute all of the queued up UPDATE statements
      foreach $query (@updateArray)
      {
        $status = $db->do($query);
        meas_db_err($db, $status, $query);
      }
      undef(@updateArray);
    }


                  #---- items -----

    #if we're calculating a structure's share of an item
    if ($structType eq "item")
    {

      #get the master item's measure value for the specified geo/time combo
      $masterVal = DSRmeasures_get_agg_measure_val($db, $dsSchema, $cubeID, $measure, $structID, $geoID, $timeID);

      #if the value can't be aggregated
      if ($masterVal eq "NULL")
      {
        next;
      }

      #get the share item's measure value for the specified geo/time combo
      $calcProdVal = DSRmeasures_get_agg_measure_val($db, $dsSchema, $cubeID, $measure, $prodID, $geoID, $timeID);

      #if the value can't be aggregated
      if ($calcProdVal eq "NULL")
      {
        next;
      }

      #make sure we don't do a division-by-zero
      if ($masterVal != 0)
      {

        #do the share calculation
        $share = ($calcProdVal / $masterVal) * 100;

        #update calculated measure value in cube
        $query = "UPDATE $dsSchema.__rptcube_$cubeID SET $colName=$share \
            WHERE product='$prodID' AND geography='$geoID' AND time='$timeID'";
        push(@updateArray, $query);
      }
    }


                  #---- segmentations -----

    #if we're calculating a structure's share of a segmentation
    if ($structType eq "seg")
    {

      #if the product ID tells us it's a segment
      if ($prodID =~ m/^SMT_(\d+)/)
      {
        $segmentID = $1;

        #figure out which segmentation the segment is a member of
        $segmentationID = $segmentSegHash{$segmentID};

        #if the segment is in the share's master segmentation, it's 100%
        if ($segmentationID eq $structID)
        {
          $query = "UPDATE $dsSchema.__rptcube_$cubeID SET $colName=100 \
              WHERE product='$prodID' AND geography='$geoID' AND time='$timeID'";
          push(@updateArray, $query);
          next;
        }

        #if we're up against a segmentation the segment isn't a member of, use
        #the segmentation total
        $finalStructID = "SEG_" . $structID;
      }

      #else if the product ID is a hierarchy level
      elsif ($prodID =~ m/^SHS_(\d+)/)
      {

        $segHierID = $1;

        #get the segmentations that make up the hierarchy
        $query = "SELECT segmentations FROM $dsSchema.product_seghierarchy \
            WHERE ID=$segHierID";
        $dbOutput = $db->prepare($query);
        $status = $dbOutput->execute;
        meas_db_err($db, $status, $query);
        ($segHierSegs) = $dbOutput->fetchrow_array;

        @segmentations = split(',', $segHierSegs);

        #figure out which tree level corresponds to the specified seg level
        $levelIdx = 0;
        $done = 0;
        while (($done != 1) && ($levelIdx < 128))
        {
          if ($segmentations[$levelIdx] eq $structID)
          {
            $done = 1;
          }
          else
          {
            $levelIdx++;
          }
        }

        #if we found a matching level (i.e., the segmentation we're calculating
        #a share of is also part of the hierarchy tree
        if ($done > 0)
        {

          #knock the SHS_nn ID off the front of the product ID
          $tmp = $prodID;
          $tmp =~ m/^SHS_\d+_(.*)/;
          $tmp = $1;

          #knock off level ID's until we reach the one matching this segmtn
          $i = 1;
          while ($i < $levelIdx)
          {
            $tmp =~ m/^\d+_(.*)/;
            $tmp = $1;
            $i++;
          }

          #if there are any remaining levels once we've found ours, chop them
          if ($tmp =~ m/^(\d+)_/)
          {
            $tmp = $1;
          }

          $finalStructID = "SMT_" . $tmp;
        }

        #else just use the total value for the segmentation
        else
        {
          $finalStructID = "SEG_" . $structID;
        }

      }

      #else the product ID is a base item
      else
      {
        $segmentID = DSRseg_get_item_membership($db, $dsSchema, $dim, $structID, $prodID);

        #if the item isn't a member of the specified segmentation, drop out
        if (!(defined($segmentID)))
        {
          next;
        }

        $finalStructID = "SMT_" . $segmentID;
      }

      #get the segment's measure value for the specified geo/time combo
      $masterVal = DSRmeasures_get_agg_measure_val($db, $dsSchema, $cubeID, $measure, $finalStructID, $geoID, $timeID);

      #if the value can't be aggregated, or can't be used in a share calc
      if (($masterVal eq "NULL") || ($masterVal == 0))
      {
        next;
      }

      #create the query to calculate the share
      #NB: the cube roll-up procedure makes sure the item's actual value for
      #    the measure being used in the share is already in the cube, saving us
      #    an extra aggregation step
      $query = "UPDATE IGNORE $dsSchema.__rptcube_$cubeID SET $colName=($baseColName / $masterVal) * 100 \
          WHERE product='$prodID' AND geography='$geoID' AND time='$timeID'";
      push(@updateArray, $query);
    }


      #---- hierarchies ----

    #if we're calculating another structure's share of a hierarchy level
    if ($structType eq "hier")
    {

      #if we're calculating a hierarchy's share of another hierarchy level
      if ($prodID =~ m/^SHS_/)
      {

        #if the two levels aren't in same hierarchy, the share is NULL
        $segHierID = 0;
        $structID =~ m/^(\d+)_/;
        $segHierID = "SHS_" . $1 . "_";
        if (!($prodID =~ m/^$segHierID/))
        {
          next;
        }

        #count how many levels deep we're going for the master value
        #NB - we get a string like 1_1_1 for the master value. It just tells
        #     us how many levels deep to go in the hierarchy - not the actual
        #     value we want. That's why we're doing this.
        $masterDepth = 0;
        $tmp = $structID;
        while ($tmp =~ m/^(\d+)_(.*)$/)
        {
          $masterDepth++;
          $tmp = $2;
        }

        #build up the master hierarchy's ID
        chop($segHierID);
        $masterHierID = $segHierID;
        $tmp = $prodID;
        $tmp =~ m/SHS_\d+(.*)/;
        $tmp = $1;
        while ($masterDepth > 0)
        {
          $tmp =~ m/^_(\d+)(.*)/;
          $masterHierID .= "_" . $1;
          $tmp = $2;

          $masterDepth--;
        }

        #get the master hierarchy's aggregated value
        $structID = "SHS_" . $structID;
        $masterVal = DSRmeasures_get_agg_measure_val($db, $dsSchema, $cubeID, $measure, $masterHierID, $geoID, $timeID);

        #get the share item's aggregated value
        $calcProdVal = DSRmeasures_get_agg_measure_val($db, $dsSchema, $cubeID, $measure, $prodID, $geoID, $timeID);

        #if the value can't be aggregated
        if ($calcProdVal eq "NULL")
        {
          next;
        }

        #make sure we don't do a division-by-zero
        if ($masterVal != 0)
        {

          #do the share calculation
          $share = ($calcProdVal / $masterVal) * 100;

          #update calculated measure value in cube
          $query = "UPDATE $dsSchema.__rptcube_$cubeID SET $colName=$share \
              WHERE product='$prodID' AND geography='$geoID' AND time='$timeID'";
          push(@updateArray, $query);
        }
      }
    }
  }

  #execute any remaining queued up UPDATE statements
  foreach $query (@updateArray)
  {
    $status = $db->do($query);
    meas_db_err($db, $status, $query);
  }

  $_measureCalculated{$calcMeasureID} = 1;
}



#-------------------------------------------------------------------------
#
# Recalculate a custom measure based on a user-entered formula for the
# specified TGP tuple inside the specified data cube.
# inside the specified data cube.

sub cube_calc_calc
{
  my ($query, $dbOutput, $colName, $val, $measureID, $formulaMeasID, $key);
  my ($formula, $formulaMeasName, $formulaMeasVal, $status, $measureName);
  my ($count, $totalStructures, $prodID, $geoID, $timeID, $badFormula);
  my ($workFormula, $pct);
  my (@measureIDs, @updateArray);
  my (%rowHash);

  my ($db, $dsSchema, $calcMeasureID, $formula, $cubeID) = @_;

  $colName = "measure_" . $calcMeasureID;
  $measureName = DSRmeasures_name_by_id($db, $dsSchema, $calcMeasureID);

  #grab a list of all TGP tuples containing aggs or segs or hierarchies
  $query = "SELECT product, geography, time FROM $dsSchema.__rptcube_$cubeID \
      WHERE product LIKE 'AGG_%' OR product LIKE 'SMT_%' OR product LIKE 'SHS_%' OR geography LIKE 'AGG_%' OR geography LIKE 'SMT_%' OR time LIKE 'AGG_%' OR time LIKE 'SMT_%'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  meas_db_err($db, $status, $query);

  $totalStructures = $status;

  while (($prodID, $geoID, $timeID) = $dbOutput->fetchrow_array)
  {
    $key = "$prodID.$geoID.$timeID";
    $rowHash{$key} = 1;
  }

  #foreach line of cube data containing an agg/seg structure, recalculate
  #this calculated measure
  #NB: For SQL performance reasons, we're queueing up 500 UPDATE statements and
  #    running them in a giant block inside a transaction
  $count = 0;
  foreach $key (keys %rowHash)
  {
    $key =~ m/^(.*?)\.(.*?)\.(.*)$/;
    $prodID = $1;
    $geoID = $2;
    $timeID = $3;

    $count++;
    #every 500 structures, update the user and commit our updates
    if (($count % 500) == 0)
    {
      $pct = ($count / $totalStructures) * 100;
      $pct = int($pct);
      Lib::BuildCube::cube_set_status($db, $cubeID, "Calculating measure $measureName $pct\% ($count of $totalStructures)");

      #execute all of the queued up UPDATE statements
      foreach $query (@updateArray)
      {
        $status = $db->do($query);
        meas_db_err($db, $status, $query);
      }
      undef(@updateArray);
    }

    #run through the formula, replacing measure column IDs with the
    #aggregated measure value for the specified TGPM combo
    $badFormula = 0;
    $workFormula = $formula;
    while (($workFormula =~ m/measure_(\d+)/) && (! $badFormula))
    {
      $formulaMeasID = $1;
      $formulaMeasName = "measure_" . $formulaMeasID;
      $formulaMeasVal = DSRmeasures_get_agg_measure_val($db, $dsSchema, $cubeID, $formulaMeasID, $prodID, $geoID, $timeID);

      #if the value can't be aggregated
      if ($formulaMeasVal eq "NULL")
      {
        $badFormula = 1;
      }

      #if we got back an empty value (there was no valid data for the TGP combo),
      #we're an NA
      if (!defined($formulaMeasVal))
      {
        $badFormula = 1;
      }

      $workFormula =~ s/$formulaMeasName/$formulaMeasVal/;
    }

    if ($badFormula)
    {
      next;
    }

    #add the current UPDATE statement to the to-be-executed array
    $query = "UPDATE IGNORE $dsSchema.__rptcube_$cubeID \
        SET $colName=$workFormula \
        WHERE product='$prodID' AND geography='$geoID' AND time='$timeID'";
    push(@updateArray, $query);
  }

  #execute any remaining queued up UPDATE statements
  foreach $query (@updateArray)
  {
    $status = $db->do($query);
    meas_db_err($db, $status, $query);
  }

  $_measureCalculated{$calcMeasureID} = 1;
}



#-------------------------------------------------------------------------
#
# Recalculates the specified measure in the specified data cube for the
# specified TGP tuple. This lets us do a "calculate after aggregate" operation
# as part of a data cube creation/update process.
# Args: db, datasourceID, calcMeasureID,

sub DSRmeasures_recalculate_cube_measure
{
  my ($query, $dbOutput, $calculation, $formula, $constant);
  my ($structType, $structID, $measure, $measType, $baseMeasure, $period);
  my ($periodType, $anteMeasure, $consMeasure, $convertPct, $dim);
  my ($calculation, $measType, $measures, $measure1, $measure2);
  my ($useCond, $conditional, $timeDirection, $prodIDs, $geoIDs, $timeIDs);
  my ($aggVal, $periods, $colName, $status, $aggRule, $calcBeforeAgg);
  my ($prodID, $geoID, $timeID);

  my ($db, $dsSchema, $measureID, $cubeID) = @_;

  #make sure all of our caches are OK for use
  DSRmeasures_cache_ok($dsSchema, $cubeID);

  #dump out if we've already been completely calculated
  if ($_measureCalculated{$measureID} == 1)
  {
    return;
  }

  #get the measure's calculation type from the data source
  ($calculation, $calcBeforeAgg) = DSRmeasures_get_calculation($db, $dsSchema, $measureID);

  #if we're a calculate-before-aggregate, then we're going to just use the
  #supplied aggregation rule to aggregate all of the calculated measure
  #values for the base items that make up the structure
  if ($calcBeforeAgg == 1)
  {

    #get the agg rule we're going to be using, we're NA if none
    $aggRule = DSRmeasures_get_agg_rule($db, $dsSchema, $measureID, "p");
    if ((length($aggRule) < 2) || ($aggRule eq "None"))
    {
      $_measureCalculated{$measureID} = 1;
      return;
    }

    $colName = "measure_" . $measureID;

    #grab a list of all TGP tuples containing aggs or segs or hierarchies
    $query = "SELECT product, geography, time FROM $dsSchema.__rptcube_$cubeID \
        WHERE product LIKE 'AGG_%' OR product LIKE 'SMT_%' OR product LIKE 'SHS_%' OR geography LIKE 'AGG_%' OR geography LIKE 'SMT_%' OR time LIKE 'AGG_%' OR time LIKE 'SMT_%'";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);

    while (($prodID, $geoID, $timeID) = $dbOutput->fetchrow_array)
    {

      #expand all of the dimension items we're working with
      $prodIDs = DSRstructure_expand($db, $dsSchema, "p", $prodID);
      $geoIDs = DSRstructure_expand($db, $dsSchema, "g", $geoID);
      $timeIDs = DSRstructure_expand($db, $dsSchema, "t", $timeID);

      #build our SQL query, based on dimension, to calculate the agg/seg value
      $query = "SELECT $aggRule($colName) FROM $dsSchema.facts \
          WHERE productID IN ($prodIDs) AND geographyID IN ($geoIDs) AND timeID IN ($timeIDs)";

      #get the aggregate value from the database, and return
      $dbOutput1 = $db->prepare($query);
      $status = $dbOutput1->execute;
      meas_db_err($db, $status, $query);
      ($aggVal) = $dbOutput1->fetchrow_array;

      if (!defined($aggVal))
      {
        $aggVal = "NULL";
      }

      #update our value in the cube
      $query = "UPDATE $dsSchema.__rptcube_$cubeID SET $colName=$aggVal \
          WHERE product='$prodID' AND geography='$geoID' AND time='$timeID'";
      $status = $db->do($query);
      meas_db_err($db, $status, $query);
    }

    $_measureCalculated{$measureID} = 1;
    return;
  }

  #figure out what kind of calculated measure we are
  $calculation =~ m/(.*?)\|/;
  $measType = $1;

  #if we're a sum
  if ($measType eq "sum")
  {
    $calculation =~ m/.*?\|(.*?)\|/;
    $measures = $1;
    cube_calc_sum($db, $dsSchema, $measureID, $measures, $cubeID);
  }

  #if we're a difference
  if ($measType eq "difference")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|/;
    $measure1 = $1;
    $measure2 = $2;
    cube_calc_difference($db, $dsSchema, $measureID, $measure1, $measure2, $cubeID);
  }

  #if we're a ratio
  if ($measType eq "ratio")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|/;
    $anteMeasure = $1;
    $consMeasure = $2;
    $convertPct = $3;
    cube_calc_ratio($db, $dsSchema, $measureID, $anteMeasure, $consMeasure, $convertPct, $cubeID);
  }

  #if we're a multiplication
  if ($measType eq "multiplication")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|/;
    $measures = $1;
    $constant = $2;
    cube_calc_multiplication($db, $dsSchema, $measureID, $measures, $constant, $cubeID);
  }

  #if we're a percent change between measures
  if ($measType eq "pct_change_meas")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|/;
    $measure1 = $1;
    $measure2 = $2;
    cube_calc_pct_change_meas($db, $dsSchema, $measureID, $measure1, $measure2, $cubeID);
  }

  #if we're a change over time
  if ($measType eq "change")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|/;
    $baseMeasure = $1;
    $period = $2;
    $periodType = $3;
    cube_calc_change($db, $dsSchema, $measureID, $baseMeasure, $cubeID);
  }

  #if we're a percent change
  if ($measType eq "pct_change")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|/;
    $baseMeasure = $1;
    $period = $2;
    $periodType = $3;
    cube_calc_pct_change($db, $dsSchema, $measureID, $baseMeasure, $cubeID);
  }

  #if we're a lag
  if ($measType eq "lag")
  {
    $calculation =~ m/.*?\|(.*?)\|/;
    $measure = $1;
    cube_calc_lag($db, $dsSchema, $measureID, $measure, $cubeID);
  }

  #if we're a lead
  if ($measType eq "lead")
  {
    $calculation =~ m/.*?\|(.*?)\|/;
    $measure = $1;
    cube_calc_lead($db, $dsSchema, $measureID, $measure, $cubeID);
  }

  #if we're a moving average
  if ($measType eq "mov_avg")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|/;
    $baseMeasure = $1;
    $periods = $2;
    $timeDirection = $3;
    cube_calc_mov_avg($db, $dsSchema, $measureID, $baseMeasure, $periods, $timeDirection, $cubeID);
  }

  #if we're a moving total
  if ($measType eq "mov_total")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|/;
    $baseMeasure = $1;
    $periods = $2;
    $timeDirection = $3;
    cube_calc_mov_total($db, $dsSchema, $measureID, $baseMeasure, $periods, $timeDirection, $cubeID);
  }

  #if we're a year-to-date
  if ($measType eq "ytd")
  {
    $calculation =~ m/.*?\|(.*?)\|/;
    $baseMeasure = $1;
    cube_calc_ytd($db, $dsSchema, $measureID, $baseMeasure, $cubeID);
  }

  #if we're a count
  if ($measType eq "count")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|(.*?)\|(.*?)\|/;
    $dim = $1;
    $useCond = $2;
    $measure = $3;
    $conditional = $4;
    $constant = $5;
    cube_calc_count($db, $dsSchema, $measureID, $dim, $cubeID);
  }

  #if we're an index
  if ($measType eq "index")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|(.*?)\|/;
    $dim = $1;
    $measure = $2;
    $structType = $3;
    $structID = $4;
    cube_calc_index($db, $dsSchema, $measureID, $dim, $measure, $structType, $structID, $cubeID);
  }

  #if we're a share
  if ($measType eq "share")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|(.*?)\|/;
    $dim = $1;
    $measure = $2;
    $structType = $3;
    $structID = $4;
    cube_calc_share($db, $dsSchema, $measureID, $dim, $measure, $structType, $structID, $cubeID);
  }

  #if we're a custom 4-function calculator measure
  if ($measType eq "calc")
  {
    $calculation =~ m/.*?\|(.*)\|/;
    $formula = $1;
    cube_calc_calc($db, $dsSchema, $measureID, $formula, $cubeID);
  }
}



#-------------------------------------------------------------------------
#
# Recalculates the specified measure in the specified data source.
#

sub DSRmeasures_recalculate_measure
{
  my ($query, $dbOutput, $calculation, $measureID, $measType, $structID);
  my ($baseMeasure, $period, $periodType, $anteMeasure, $consMeasure);
  my ($structType, $measure, $convertPct, $dim, $measures, $formula);
  my ($measure1, $measure2, $constant, $timeDirection, $status);
  my ($useCond, $conditional, $periods);

  my ($db, $dsSchema, $measureID, $calculation, $dsUpdateID) = @_;

  #if we weren't passed a calculation string, grab it from the data source
  if (length($calculation) < 2)
  {
    $query = "SELECT calculation FROM $dsSchema.measures WHERE ID=$measureID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);
    ($calculation) = $dbOutput->fetchrow_array;
  }

  #figure out what kind of calculated measure we are
  $calculation =~ m/(.*?)\|/;
  $measType = $1;

  #if we're a sum
  if ($measType eq "sum")
  {
    $calculation =~ m/.*?\|(.*?)\|/;
    $measures = $1;
    DSRmeasures_calc_sum($db, $dsSchema, $measureID, $measures, $dsUpdateID);
  }

  #if we're a difference
  if ($measType eq "difference")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|/;
    $measure1 = $1;
    $measure2 = $2;
    DSRmeasures_calc_difference($db, $dsSchema, $measureID, $measure1, $measure2, $dsUpdateID);
  }

  #if we're a ratio
  if ($measType eq "ratio")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|/;
    $anteMeasure = $1;
    $consMeasure = $2;
    $convertPct = $3;
    DSRmeasures_calc_ratio($db, $dsSchema, $measureID, $anteMeasure, $consMeasure, $convertPct, $dsUpdateID);
  }

  #if we're a multiplication
  if ($measType eq "multiplication")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|/;
    $measures = $1;
    $constant = $2;
    DSRmeasures_calc_multiplication($db, $dsSchema, $measureID, $measures, $constant, $dsUpdateID);
  }

  #if we're a percent change between measures
  if ($measType eq "pct_change_meas")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|/;
    $measure1 = $1;
    $measure2 = $2;
    DSRmeasures_calc_pct_change_meas($db, $dsSchema, $measureID, $measure1, $measure2, $dsUpdateID);
  }

  #if we're a change over time
  if ($measType eq "change")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|/;
    $baseMeasure = $1;
    $period = $2;
    $periodType = $3;
    DSRmeasures_calc_change($db, $dsSchema, $measureID, $baseMeasure, $dsUpdateID);
  }

  #if we're a percent change
  if ($measType eq "pct_change")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|/;
    $baseMeasure = $1;
    $period = $2;
    $periodType = $3;
    DSRmeasures_calc_pct_change($db, $dsSchema, $measureID, $baseMeasure, $dsUpdateID);
  }

  #if we're a lag
  if ($measType eq "lag")
  {
    $calculation =~ m/.*?\|(.*?)\|/;
    $measure = $1;
    DSRmeasures_calc_lag($db, $dsSchema, $measureID, $measure, $dsUpdateID);
  }

  #if we're a lead
  if ($measType eq "lead")
  {
    $calculation =~ m/.*?\|(.*?)\|/;
    $measure = $1;
    DSRmeasures_calc_lead($db, $dsSchema, $measureID, $measure, $dsUpdateID);
  }

  #if we're a moving average
  if ($measType eq "mov_avg")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|/;
    $baseMeasure = $1;
    $periods = $2;
    $timeDirection = $3;
    DSRmeasures_calc_mov_avg($db, $dsSchema, $measureID, $baseMeasure, $periods, $timeDirection);
  }

  #if we're a moving total
  if ($measType eq "mov_total")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|/;
    $baseMeasure = $1;
    $periods = $2;
    $timeDirection = $3;
    DSRmeasures_calc_mov_total($db, $dsSchema, $measureID, $baseMeasure, $periods, $timeDirection);
  }

  #if we're a year to date
  if ($measType eq "ytd")
  {
    $calculation =~ m/.*?\|(.*?)\|/;
    $baseMeasure = $1;
    DSRmeasures_calc_ytd($db, $dsSchema, $measureID, $baseMeasure);
  }

  #if we're a count
  if ($measType eq "count")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|(.*?)\|(.*?)\|/;
    $dim = $1;
    $useCond = $2;
    $measure = $3;
    $conditional = $4;
    $constant = $5;
    DSRmeasures_calc_count($db, $dsSchema, $measureID, $dim, $useCond, $measure, $conditional, $constant, $dsUpdateID);
  }

  #if we're an index
  if ($measType eq "index")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|(.*?)\|/;
    $dim = $1;
    $measure = $2;
    $structType = $3;
    $structID = $4;
    DSRmeasures_calc_index($db, $dsSchema, $measureID, $dim, $measure, $structType, $structID);
  }

  #if we're a share
  if ($measType eq "share")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|(.*?)\|/;
    $dim = $1;
    $measure = $2;
    $structType = $3;
    $structID = $4;
    DSRmeasures_calc_share($db, $dsSchema, $measureID, $dim, $measure, $structType, $structID);
  }

  #if we're a custom 4-function calculator measure
  if ($measType eq "calc")
  {
    $calculation =~ m/.*?\|(.*)\|/;
    $formula = $1;
    DSRmeasures_calc_calc($db, $dsSchema, $measureID, $formula, $dsUpdateID);
  }
}



#-------------------------------------------------------------------------
#
# Recalculates all calculated measures in the specified data source. This
# is a bit of a blunt instrument, but it's a (relatively) quick and easy
# way to be sure all of your calculated measures are up to date after a
# data source update.
# Args: db, datasourceID

sub DSRmeasures_recalculate_all_measures
{
  my ($query, $dbOutput, $calculation, $measureID, $status, $measName);
  my ($count, $measCount);

  my ($db, $dsSchema, $dsUpdateID) = @_;

  #get a list of every calculated measure in our data source
  $query = "SELECT ID, name, calculation FROM $dsSchema.measures WHERE calculation IS NOT NULL ORDER BY ID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  $measCount = $status;
  meas_db_err($db, $status, $query);

  $count = 1;
  while (($measureID, $measName, $calculation) = $dbOutput->fetchrow_array)
  {

    #if we're being called by a data source update, log some telemetry
    if ($dsUpdateID > 0)
    {
      Lib::DSRCreateUpdate::ds_telemetry($db, "Recalculating measure $measName");
      Lib::DSRCreateUpdate::ds_set_status($db, $dsSchema, "Recalculating measure $measName ($count of $measCount)");
    }

    #update our status in the job table
    KAPutil_job_update_status($db, "Recalculating measure $measName ($count of $measCount)");

    DSRmeasures_recalculate_measure($db, $dsSchema, $measureID, $calculation, $dsUpdateID);
    $count++;
  }
}



#-------------------------------------------------------------------------
#
# Returns a correctly formatted version of the measure. If we aren't
# provided with the format, retrieve it from the measures table.
#

sub DSRmeasures_format_text
{
  my ($query, $dbOutput, $span, $status);
  my (@formats);

  my ($db, $dsSchema, $measureID, $measureVal, $formatString) = @_;

  #if it's a NULL, just output blank
  if (length($measureVal) < 1)
  {
    return("");
  }

  #if we didn't get a format string, go pull it from the database
  if (length($formatString) < 1)
  {
    $query = "SELECT format FROM $dsSchema.measures WHERE ID=$measureID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    meas_db_err($db, $status, $query);
    ($formatString) = $dbOutput->fetchrow_array;
  }

  @formats = split(',', $formatString);

  #round the value to the specified number of decimal places
  $measureVal = sprintf("%.$formats[0]f", $measureVal);

  #add thousands separators if the format needs them
  if ($formats[1] == 1)
  {
    $measureVal = reverse($measureVal);
    $measureVal =~ s/(\d\d\d)(?=\d)(?!\d*\.)/$1,/g;
    $measureVal = scalar(reverse($measureVal));
  }

  #prepend a currency symbol if required by format
  if ($formats[2] == 1)
  {
    $measureVal = "\$" . $measureVal;
  }

  #append a % sign if required by format
  if ($formats[2] == 2)
  {
    $measureVal = $measureVal . "%";
  }

  #apply negative number formatting other than default
  if (($measureVal =~ m/^\-(.*)/) || ($measureVal =~ m/^\$\-(.*)/))
  {
    if ($formats[3] == 2)
    {
      $measureVal = $1;
    }
    elsif (($formats[3] == 3) || ($formats[3] == 4))
    {
      $measureVal = "($1)";
    }
  }

  return($measureVal);
}



#-------------------------------------------------------------------------
#
# Returns a correctly formatted version of the measure in HTML.
#

sub DSRmeasures_format_html
{
  my ($query, $dbOutput, $span, $negative);
  my (@formats);

  my ($db, $dsSchema, $measureID, $measureVal, $formatString) = @_;


  #if we're a segment or attribute text string
  if (($measureID =~ m/ATT_/) || ($measureID =~ m/SEG_/))
  {
    return($measureVal);
  }

  #decide if we're negative before applying formatting
  $negative = 0;
  if ($measureVal < 0)
  {
    $negative = 1;
  }

  @formats = split(',', $formatString);

  $measureVal = DSRmeasures_format_text($db, $dsSchema, $measureID, $measureVal, $formatString);

  #handle red coloration for negative numbers, if needed
  if (($negative) && (($formats[3] == 2) || ($formats[3] == 4)))
  {
    $span = "<SPAN STYLE='color:red;'>$measureVal</SPAN>";
  }
  else
  {
    $span = $measureVal;
  }

  return($span);
}



#-------------------------------------------------------------------------
#
# Return an Excel number format string that more or less matches what
# the user wants to see for a measure format (the number code matches up
# with one of Excel's built-in formatting strings).
# NB: We're assuming everyone wants thousands separators just to keep the
#     decision tree in the function reasonable
#

sub DSRmeasures_format_excel
{
  my ($excelNumFormat);
  my (@formats);
  my (%formatHash);

  my ($db, $dsSchema, $measureID, $formatString) = @_;


  %formatHash = (
    "1,1,0,1" => "#,##0.0",
    "1,1,0,2" => "#,##0.0;[Red]#,##0.0",
    "1,1,0,3" => "#,##0.0_);(#,##0.0)",
    "1,1,0,4" => "#,##0.0_);[Red](#,##0.0)",
    "1,1,2,4" => "0.#\\%;[Red](0.#\\%)",
  );

  $excelNumFormat = 0;
  @formats = split(',', $formatString);

  #if we have a specific Excel formatting code
  if ($formatHash{$formatString})
  {
    $excelNumFormat = $formatHash{$formatString};
  }

  #if we don't want any decimal places
  elsif ($formats[0] == 0)
  {

    #if we want a currency symbol
    if ($formats[2] == 1)
    {
      if ($formats[3] == 1)
      {
        $excelNumFormat = 5;
      }
      elsif ($formats[3] == 2)
      {
        $excelNumFormat = 6;
      }
      elsif ($formats[3] == 3)
      {
        $excelNumFormat = 5;
      }
      elsif ($formats[3] == 4)
      {
        $excelNumFormat = 6;
      }
    }

    #no currency symbol
    else
    {
      if ($formats[3] == 1)
      {
        $excelNumFormat = 37;
      }
      elsif ($formats[3] == 2)
      {
        $excelNumFormat = 38;
      }
      elsif ($formats[3] == 3)
      {
        $excelNumFormat = 37;
      }
      elsif ($formats[3] == 4)
      {
        $excelNumFormat = 38;
      }
    }
  }

  #if we want decimal points
  elsif ($formats[0] > 0)
  {

    #if we want a currency symbol
    if ($formats[2] == 1)
    {
      if ($formats[3] == 1)
      {
        $excelNumFormat = 7;
      }
      elsif ($formats[3] == 2)
      {
        $excelNumFormat = 8;
      }
      elsif ($formats[3] == 3)
      {
        $excelNumFormat = 7;
      }
      elsif ($formats[3] == 4)
      {
        $excelNumFormat = 8;
      }
    }

    #no currency symbol
    else
    {
      if ($formats[3] == 1)
      {
        $excelNumFormat = 39;
      }
      elsif ($formats[3] == 2)
      {
        $excelNumFormat = 40;
      }
      elsif ($formats[3] == 3)
      {
        $excelNumFormat = 39;
      }
      elsif ($formats[3] == 4)
      {
        $excelNumFormat = 40;
      }
    }
  }

  return($excelNumFormat);
}



#-------------------------------------------------------------------------
#
# Return hex code for the cell's background color if conditional formatting
# applies to the value
#

sub DSRmeasures_get_conditional_color
{
  my ($color, $format, $op, $val, $rgb, $upperVal);
  my (@formats);

  my ($condFormat, $measureVal) = @_;


  #if this measure doesn't have any conditional formatting
  if (length($condFormat) < 2)
  {
    return("");
  }

  #if the value is NA
  if (length($measureVal) < 1)
  {
    return("");
  }

  #turn conditional formatting string into array of rules
  @formats = split(',', $condFormat);

  #see which (if any) rules apply
  foreach $format (@formats)
  {
    if ($format =~ m/^(.*?) (.*?) (.*?) (.*?)$/)
    {
      $op = $1;
      $upperVal = $2;
      $val = $3;
      $rgb = $4;

      #some users are determined to enter the values in reverse order
      if ($upperVal < $val)
      {
        ($upperVal, $val) = ($val, $upperVal);
      }
    }
    elsif ($format =~ m/^(.*?) (.*?) (.*?)$/)
    {
      $op = $1;
      $val = $2;
      $rgb = $3;
    }

    if ($op eq "lt")
    {
      if ($measureVal <= $val)
      {
        $color = $rgb;
      }
    }

    elsif ($op eq "gt")
    {
      if ($measureVal >= $val)
      {
        $color = $rgb;
      }
    }

    elsif ($op eq "eq")
    {
      if ($measureVal == $val)
      {
        $color = $rgb;
      }
    }

    elsif ($op eq "ib")
    {
      if (($measureVal >= $val) && ($measureVal <= $upperVal))
      {
        $color = $rgb;
      }
    }
  }

  if (length($color) > 2)
  {
    return("background-color: #$color;");
  }
  else
  {
    return("");
  }
}



#-------------------------------------------------------------------------
#
# Returns the format string for the specified measure
#

sub DSRmeasures_get_format
{
  my ($query, $dbOutput, $id, $format, $status);
  my ($db, $dsSchema, $measureID) = @_;

  if (!($measureID =~ m/^\d+$/))
  {
    return;
  }

  $query = "SELECT format FROM $dsSchema.measures WHERE ID=$measureID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  meas_db_err($db, $status, $query);
  ($format) = $dbOutput->fetchrow_array;

  return($format);
}



#-------------------------------------------------------------------------
#
# Returns a hash of all measure formats, keyed by measure ID, for the
# specified data source.
#

sub DSRmeasures_get_format_hash
{
  my ($query, $dbOutput, $dsSchema, $id, $format, $status);
  my (%formatHash);

  my ($db, $dsID) = @_;


  undef(%formatHash);

  $dsSchema = "datasource_" . $dsID;

  $query = "SELECT ID, format FROM $dsSchema.measures";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  meas_db_err($db, $status, $query);

  while (($id, $format) = $dbOutput->fetchrow_array)
  {
    $formatHash{$id} = $format;
  }

  return(%formatHash);
}



#-------------------------------------------------------------------------
#
# Returns a human-readable version of the specified measure's formula.
#

sub DSRmeasures_human_readable_formula
{
  my ($query, $dbOutput, $status, $calculation, $measType, $measName);
  my ($measure1, $measure2);

  my ($db, $dsSchema, $id) = @_;


  $query = "SELECT name, calculation FROM $dsSchema.measures WHERE ID=$id";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  meas_db_err($db, $status, $query);
  ($measName, $calculation) = $dbOutput->fetchrow_array;

  $calculation =~ m/(.*?)\|/;
  $measType = $1;

  if ($measType eq "sum")
  {
    $calculation =~ m/.*?\|(.*?)\|/;
    $measures = $1;
    @measures = split(',', $measures);
    $formula = "<B>$measName</B> = ";
    foreach $measure (@measures)
    {
      $measure = DSRmeasures_name_by_id($db, $dsSchema, $measure);
      $formula .= "<B>$measure</B> + "
    }
    chop($formula);  chop($formula);  chop($formula);
  }
  elsif ($measType eq "difference")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|/;
    $measure1 = DSRmeasures_name_by_id($db, $dsSchema, $1);
    $measure2 = DSRmeasures_name_by_id($db, $dsSchema, $2);
    $formula = "<B>$measName</B> = <B>$measure1</B> - <B>$measure2</B>";
  }
  elsif ($measType eq "ratio")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|/;
    $anteMeasure = DSRmeasures_name_by_id($db, $dsSchema, $1);
    $consMeasure = DSRmeasures_name_by_id($db, $dsSchema, $2);
    $convertPct = $3;
    if ($convertPct > 0)
    {
      $formula = "<B>$measName</B> = (<B>$anteMeasure</B> / <B>$consMeasure</B>) * 100";
    }
    else
    {
      $formula = "<B>$measName</B> = <B>$anteMeasure</B> / <B>$consMeasure</B>";
    }
  }
  elsif ($measType eq "multiplication")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|/;
    $measures = $1;
    $constant = $2;
    @measures = split(',', $measures);
    $formula = "<B>$measName</B> = ";
    foreach $measure (@measures)
    {
      $measure = DSRmeasures_name_by_id($db, $dsSchema, $measure);
      $formula .= "<B>$measure</B> x "
    }
    chop($formula);  chop($formula);  chop($formula);
    if ($constant != 1)
    {
      $formula .= " x $constant";
    }
  }

  elsif ($measType eq "pct_change_meas")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|/;
    $measure1 = DSRmeasures_name_by_id($db, $dsSchema, $1);
    $measure2 = DSRmeasures_name_by_id($db, $dsSchema, $2);
    $measType = "% change";
    $formula = "<B>$measName</B> = (<B>$measure1</B> - <B>$measure2</B>) / <B>$measure2</B> x 100";
  }

  elsif ($measType eq "change")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|/;
    $baseMeasure = DSRmeasures_name_by_id($db, $dsSchema, $1);
    $period = $2;
    $periodType = $3;
    $formula = "<B>$measName</B> = <B>$baseMeasure</B>, change between current period and year ago";
  }

  elsif ($measType eq "pct_change")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|/;
    $baseMeasure = DSRmeasures_name_by_id($db, $dsSchema, $1);
    $period = $2;
    $periodType = $3;
    $formula = "<B>$measName</B> = (<B>$baseMeasure</B>, change between current period and year ago /  <B>$baseMeasure</B>, Year ago) x 100";
  }

  elsif ($measType eq "lag")
  {
    $calculation =~ m/.*?\|(.*?)\|/;
    $measure = DSRmeasures_name_by_id($db, $dsSchema, $1);
    $formula = "<B>$measName</B> = <B>$measure</B>, Year Ago";
  }

  elsif ($measType eq "lead")
  {
    $calculation =~ m/.*?\|(.*?)\|/;
    $measure = DSRmeasures_name_by_id($db, $dsSchema, $1);
    $formula = "<B>$measName</B> = <B>$measure</B>, Next Year";
  }

  elsif ($measType eq "mov_avg")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|/;
    $baseMeasure = DSRmeasures_name_by_id($db, $dsSchema, $1);
    $periods = $2;
    $timeDirection = $3;
    $measType = "moving average";
    $formula = "<B>$measName</B> = Average of <B>$baseMeasure</B> for $periods $timeDirection time periods from current";
  }

  elsif ($measType eq "mov_total")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|/;
    $baseMeasure = DSRmeasures_name_by_id($db, $dsSchema, $1);
    $periods = $2;
    $timeDirection = $3;
    $measType = "moving total";
    $formula = "<B>$measName</B> = Sum of <B>$baseMeasure</B> for $periods $timeDirection time periods from current";
  }

  elsif ($measType eq "ytd")
  {
    $calculation =~ m/.*?\|(.*?)\|/;
    $baseMeasure = DSRmeasures_name_by_id($db, $dsSchema, $1);
    $measType = "year to date";
    $formula = "<B>$measName</B> = Sum of <B>$baseMeasure</B> from beginning of current calendar year";
  }

  elsif ($measType eq "count")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|(.*?)\|(.*?)\|/;
    $dim = $1;
    $useCond = $2;
    $measure = $3;
    $conditional = $4;
    $constant = $5;
    $dim = KAPutil_get_dim_name($dim, 0);
    $formula = "<B>$measName</B> = number of $dim";

    if ($useCond > 0)
    {
      $measure1 = DSRmeasures_name_by_id($db, $dsSchema, $measure);
      if ($conditional eq "eg")
      {
        $conditional = "=";
      }
      elsif ($conditional eq "gt")
      {
        $conditional = "&gt;";
      }
      else
      {
        $conditional = "&lt;";
      }
      $formula .= " where <B>$measure1</B> $conditional $constant";
    }
  }

  elsif ($measType eq "index")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|(.*?)\|/;
    $dim = $1;
    $measure = $2;
    $structType = $3;
    $structID = $4;

    if ($structType eq "seg")
    {
      $structName = KAPsegmentation_seg_ID_to_name($db, $dsSchema, $dim, $structID);
      $structType = "segmentation";
    }
    elsif ($structType eq "agg")
    {
      $structName = DSRagg_id_to_name($db, $dsSchema, $dim, $structID);
      $structType = "aggregate";
    }
    elsif ($structType eq "hier")
    {
      $structName = DSRseghier_id_to_name($db, $dsSchema, $dim, $structID);
      $structType = "hierarchy";
    }
    elsif ($structType eq "item")
    {
      $structName = KAPutil_get_item_ID_name($db, $dsSchema, $dim, $structID);
      $structType = "item";
    }

    $measure = DSRmeasures_name_by_id($db, $dsSchema, $measure);
    $formula = "<B>$measName</B> = ([<B>$measure</B>, current product] / [average of <B>$measure</B>, in the $structName $structType]) * 100";
  }


  elsif ($measType eq "share")
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|(.*?)\|/;
    $dim = $1;
    $measure = $2;
    $structType = $3;
    $structID = $4;

    if ($structType eq "seg")
    {
      $structName = KAPsegmentation_seg_ID_to_name($db, $dsSchema, $dim, $structID);
      $structType = "segmentation";
    }
    elsif ($structType eq "agg")
    {
      $structName = DSRagg_id_to_name($db, $dsSchema, $dim, $structID);
      $structType = "aggregate";
    }
    elsif ($structType eq "hier")
    {
      $structName = DSRseghier_id_to_name($db, $dsSchema, $dim, $structID);
      $structType = "hierarchy";
    }
    elsif ($structType eq "item")
    {
      $structName = KAPutil_get_item_ID_name($db, $dsSchema, $dim, $structID);
      $structType = "item";
    }

    $measure = DSRmeasures_name_by_id($db, $dsSchema, $measure);
    $formula = "<B>$measName</B> = current product's share of <B>$measure</B> in the $structName $structType";
  }

  elsif ($measType eq "calc")
  {
    $calculation =~ m/.*?\|(.*)\|/;
    $formula = $1;
    $formula =~ s/pl/\+/g;
    while ($formula =~ m/(measure_\d+)/)
    {
      $measColName = $1;

      $measColName =~ m/measure_(\d+)/;
      $measID = $1;
      $formMeasName = DSRmeasures_name_by_id($db, $dsSchema, $measID);

      $formula =~ s/$measColName/\<B\>$formMeasName\<\/B\>/;
    }

    $measType = "custom calculation";
    $formula = "<B>$measName</B> = $formula";
  }

  else
  {
    $formula = "";
  }

  return($measType, $formula);
}



#-------------------------------------------------------------------------
#
# Return the name of the measure with the specified ID in the specified
# data source schema.
#

sub DSRmeasures_name_by_id
{
  my ($query, $dbOutput, $name, $status, $q_name);

  my ($db, $dsSchema, $id) = @_;


  $q_name = $db->quote($name);
  $query = "SELECT name from $dsSchema.measures WHERE ID = $id";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  meas_db_err($db, $status, $query);
  ($name) = $dbOutput->fetchrow_array;

  return($name);
}



#-------------------------------------------------------------------------
#
# Return the ID for the measure with the specified name in the specified
# data source schema
#

sub DSRmeasures_id_by_name
{
  my ($query, $dbOutput, $id, $q_name, $status);

  my ($db, $dsSchema, $name) = @_;


  $q_name = $db->quote($name);
  $query = "SELECT ID from $dsSchema.measures WHERE name = $q_name";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  meas_db_err($db, $status, $query);
  ($id) = $dbOutput->fetchrow_array;

  return($id);
}



#-------------------------------------------------------------------------



1;
