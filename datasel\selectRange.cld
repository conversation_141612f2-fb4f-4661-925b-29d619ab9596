#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName Data Selection</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Please Wait...');
  return(true);
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  #output appropriate navigation header for the structure type we're editing
  if ($structType eq "a")
  {
    $dsName = ds_id_to_name($db, $dsID);
    $aggName = $structName;
    if (length($structName) < 1)
    {
      $aggName = DSRagg_id_to_name($db, $dsSchema, $dim, $structID);
    }
    print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/dsr/main.cld">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/dsr/display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Edit Aggregate $aggName</LI>
  </OL>
</NAV>
<P>
END_HTML
  }

  elsif ($structType eq "l")
  {
    $dsName = ds_id_to_name($db, $dsID);
    $listName = $structName;
    if (length($structName) < 1)
    {
      $listName = DSRlist_id_to_name($db, $dsSchema, $dim, $structID);
    }
    print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/dsr/main.cld">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/dsr/display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Edit List $listName</LI>
  </OL>
</NAV>
<P>
END_HTML
  }

  elsif ($structType eq "c")
  {
    $rptName = $structName;
    if (length($structName) < 1)
    {
      $rptName = cube_id_to_name($db, $rptID);
    }
    print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none"HREF="/app/rpt/main">Reports</A></LI>
    <LI CLASS="breadcrumb-item">$rptName</LI>
    <LI CLASS="breadcrumb-item active">Range Data Selection</LI>
  </OL>
</NAV>
<P>
END_HTML
  }
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser and get the forecast to be deleted
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  #get CGI parameters
  $dsID = $q->param('ds');
  $dim = $q->param('dim');
  $rptID = $q->param('rptID');
  $structType = $q->param('st');
  $structID = $q->param('sid');
  $structName = $q->param('name');
  $modifyItem = $q->param('modItem');

  get_cgi_session_info();

  #build data source schema name
  $dsSchema = "datasource_" . $dsID;

  #hash that we use to convert numerical date type to human-readable string
  undef(%dateTypeName);
  %dateTypeName = (
    10 => "Year",
    20 => "Month",
    30 => "Week",
    40 => "Day",
  );

  #connect to the database
  $db = KAPutil_connect_to_database();

  #figure out where to go if we're canceled
  if (($structType eq "l") || ($structType eq "a"))
  {
    $cancelScript = "/app/dsr/display.cld?ds=$dsID";
  }
  else
  {
    $cancelScript = "datasel.cld?ds=$dsID&rptID=$rptID&dim=$dim";
  }

  print_html_header();

  #check our permissions
  if ($structType eq "c")
  {
    $privs = cube_rights($db, $userID, $rptID, $acctType);
    if ($privs ne "W")
    {
      exit_error("You don't have privileges to edit this report.");
    }
  }
  else
  {
    $privs = ds_rights($db, $userID, $dsID, $acctType);
    if ($privs ne "W")
    {
      exit_error("You don't have privileges to modify this data source.");
    }
  }

  #if we're modifying an existing RECENT selection
  $rangeChecked = "";
  $recentChecked = "CHECKED";
  if ($modifyItem =~ m/^RECENT\:(\d+) (\d+) (\d+) (.*?)$/)
  {
    $selectionVal = $1;
    $selDurationVal = $2;
    $selTypeVal = $3;
    $selEndingVal = $4;
  }

  #if we're modifying an existing RANGE selection
  if ($modifyItem =~ m/^RANGE\:(\d+) (\d+) (.*?) (.*?)$/)
  {
    $rangeChecked = "CHECKED";
    $recentChecked = "";
    $selDurationVal = $1;
    $selTypeVal = $2;
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-10 col-lg-7 col-xl-6"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Range Selection</DIV>
        <DIV CLASS="card-body">

          <FORM METHOD="post" ACTION="selectRangeSpecs.cld" onsubmit="return checkForm(this);">
          <INPUT TYPE="hidden" NAME="ds" VALUE="$dsID">
          <INPUT TYPE="hidden" NAME="rptID" VALUE="$rptID">
          <INPUT TYPE="hidden" NAME="dim" VALUE="$dim">
          <INPUT TYPE="hidden" NAME="st" VALUE="$structType">
          <INPUT TYPE="hidden" NAME="sid" VALUE="$structID">
          <INPUT TYPE="hidden" NAME="name" VALUE="$structName">
          <INPUT TYPE="hidden" NAME="modItem" VALUE="$modifyItem">

          I want to add:
          <P>
          <DIV CLASS="row mb-3">
            <DIV CLASS="col-auto gx-1 ms-2 mt-1">
              <DIV CLASS="form-check">
                <INPUT CLASS="form-check-input" TYPE="radio" NAME="type" ID="recent" $recentChecked VALUE="recent">
                <LABEL CLASS="form-check-label" FOR="recent">The most recent</LABEL>
              </DIV>
            </DIV>
            <DIV CLASS="col-auto gx-1">
              <SELECT CLASS="form-select" NAME="t_recent" ID="t_recent">
END_HTML

  #select every unique combo of date type and duration
  $query = "SELECT DISTINCT duration, type FROM $dsSchema.timeperiods \
      WHERE type > 0 ORDER BY type";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;

  #output an OPTION for each type of time period, making it human-readable
  while (($duration, $type) = $dbOutput->fetchrow_array)
  {
    if ($selDurationVal < 1)
    {
      $selDurationVal = $duration;
      $selTypeVal = $type;
    }
    print(" <OPTION VALUE='$duration $type'>$duration $dateTypeName{$type}</OPTION>\n");
  }

  print <<END_HTML;
              </SELECT>
              <SCRIPT>
                \$('#t_recent').val('$selDurationVal $selTypeVal');
              </SCRIPT>
            </DIV>
            <DIV CLASS="col-auto gx-1 mt-1">
              time period(s)
            </DIV>
          </DIV>

          <DIV CLASS="row">
            <DIV CLASS="col-auto gx-1 ms-2 mt-1">
              <DIV CLASS="form-check">
                <INPUT CLASS="form-check-input" TYPE="radio" NAME="type" VALUE="range" $rangeChecked ID="range">
                <LABEL CLASS="form-check-label" FOR="range">A range of</LABEL>
              </DIV>
            </DIV>
            <DIV CLASS="col-auto gx-1">
              <SELECT CLASS="form-select" NAME="t_range" ID="t_range">
END_HTML

  #select every unique combo of date type and duration
  $query = "SELECT DISTINCT duration, type FROM $dsSchema.timeperiods \
      WHERE type > 0 ORDER BY type";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;

  #output an OPTION for each type of time period, making it human-readable
  while (($duration, $type) = $dbOutput->fetchrow_array)
  {
    print(" <OPTION VALUE='$duration $type'>$duration $dateTypeName{$type}</OPTION>\n");
  }

  print <<END_HTML;
              </SELECT>
              <SCRIPT>
                \$('#t_range').val('$selDurationVal $selTypeVal');
              </SCRIPT>
            </DIV>
            <DIV CLASS="col-auto gx-1 mt-1">
              time periods
            </DIV>
          </DIV>

          <P>&nbsp;</P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="location.href='$cancelScript'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="submit" ID="btn-submit">Next <I CLASS="bi bi-arrow-right"></I></BUTTON>
          </DIV>

          </FORM>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
