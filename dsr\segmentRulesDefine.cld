#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Edit Segmentation Rule</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-select-1.14.0-b2/css/bootstrap-select.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-select-1.14.0-b2/js/bootstrap-select.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="segmentRules.cld?ds=$dsID&dim=$dim&seg=$segID">$segName</A></LI>
    <LI CLASS="breadcrumb-item active">Edit Segmentation Rule</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');
  $dim = $q->param('dim');
  $type = $q->param('type');
  $segID = $q->param('s');
  $ruleID = $q->param('r');
  $side = $q->param('side');
  $matchType = $q->param('mt');
  $matchStr = $q->param('ms');
  $segmentID = $q->param('smt');
  $newSegment = $q->param('newSegment');
  $attrID = $q->param('attr');
  $matchSeg = $q->param('mseg');
  $matchSegment = $q->param('msmt');
  $matchOp = $q->param('matchOp');
  $numMatchVal = $q->param('numVal');
  $numMatchValUpper = $q->param('numValUpper');
  $segFilter1 = $q->param('segFilter1');
  $segFilter1Val = $q->param('segFilter1Val');
  $segFilter2 = $q->param('segFilter2');
  $segFilter2Val = $q->param('segFilter2Val');
  $action = $q->param('a');

  #sanitize CGI parameters
  $matchStr =~ s/"//g;

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsSchema = "datasource_" . $dsID;

  $dsName = ds_id_to_name($db, $dsID);

  $dbStub = KAPutil_get_dim_stub_name($dim);
  $dbName = $dbStub . "seg_rules";


  ########################################################################
  #
  # This code block is called if we're just handing back a list of segments in
  # a specified segmentation
  #

  if ($action eq "segments")
  {
    print("Content-type: application/json\n\n");
    $output = "[\n";

    %segmentHash = DSRseg_get_segments_hash($db, $dsSchema, $dim, $segmentID);
    foreach $segmentID (sort {$segmentHash{$a} cmp $segmentHash{$b}} keys %segmentHash)
    {
      $output .= " {\n";
      $output .= "  \"id\": $segmentID,\n";
      $output .= "  \"name\": \"$segmentHash{$segmentID}\"\n";
      $output .= " },\n";
    }
    chop($output); chop($output);

    $output .= "\n]\n";

    print("$output");
    exit;
  }


  ########################################################################
  #
  # This code block is called on submit to save the changes
  #

  #if we're being called to save a new/updated segmentation rule
  if (defined($side))
  {
    print("Content-type: text/html\n\n");

    #if we're being asked to create a new segment
    if ($segmentID == 0)
    {

      #we don't actually want to create a blank segment for filldown
      if ($type ne "segfilldown")
      {
        $q_newSegment = $db->quote($newSegment);
        $segmentDBname = $dbStub . "segment";

        $query = "INSERT INTO $dsSchema.$segmentDBname (segmentationID, name) \
            VALUES ($segID, $q_newSegment)";
        $db->do($query);

        $segmentID = $db->{q{mysql_insertid}};
      }
    }

    if ($type eq "name")
    {
      $rule = "TEXT $side $matchType $matchStr";
    }
    elsif ($type eq "attr")
    {
      $rule = "ATTR $side $attrID $matchType $matchStr";
    }
    elsif ($type eq "segmatch")
    {
      $rule = "SEGMATCH $side $matchSeg $matchType $matchStr";
    }
    elsif ($type eq "seg")
    {
      $rule = "SEG $side $matchSeg $matchSegment";
    }
    elsif ($type eq "segval")
    {
      $rule = "SEGVAL $side $matchSeg $matchOp $numMatchVal $numMatchValUpper";
    }
    elsif ($type eq "segfilldown")
    {
      $rule = "SEGFILLDOWN $matchSeg";
    }
    elsif ($type eq "catchall")
    {
      $rule = "CATCHALL";
    }
    else
    {
      exit;     #bad rule submitted
    }

    #set up any additional filtering info we might have received
    $filter1 = "";
    $filter2 = "";
    if (($segFilter1 > 0) && (length($segFilter1Val) > 0))
    {
      $filter1 = "$segFilter1 $segFilter1Val";
    }
    if (($segFilter2 > 0) && (length($segFilter2Val) > 0))
    {
      $filter2 = "$segFilter2 $segFilter2Val";
    }

    $q_rule = $db->quote($rule);
    $q_filter1 = $db->quote($filter1);
    $q_filter2 = $db->quote($filter2);

    #get the step number for our new recipe step
    $query = "SELECT step FROM $dsSchema.$dbName \
        WHERE segmentationID=$segID ORDER BY step DESC LIMIT 1";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($step) = $dbOutput->fetchrow_array;
    $step = $step + 1;

    #if we're creating a new rule
    if ($ruleID < 1)
    {
      $query = "INSERT INTO $dsSchema.$dbName \
          (step, segmentationID, segmentID, rule, filter1, filter2) \
          VALUES ($step, $segID, $segmentID, $q_rule, $q_filter1, $q_filter2)";
      $db->do($query);
    }

    #else we're updating an existing rule
    else
    {
      $query = "UPDATE $dsSchema.$dbName \
          SET segmentID=$segmentID, rule=$q_rule, filter1=$q_filter1, filter2=$q_filter2 \
          WHERE ID=$ruleID";
      $db->do($query);
    }
    $dsName = ds_id_to_name($db, $dsID);

    utils_audit($db, $userID, "Created segmentation rule", $dsID, 0, 0);
    $activity = "$first $last created segmentation rule in $dsName";
    utils_slack($activity);

    exit;
  }

  #########################################################################


  #
  # Everything after this point is called to display the seg rule DIVs
  #

  #get name of segmentation we're editing rules for
  $segDBName = $dbStub . "segmentation";
  $query = "SELECT name FROM $dsSchema.$segDBName WHERE ID=$segID";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($segName) = $dbOutput->fetchrow_array;

  print_html_header();

  #make sure we have write privs for this data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit segmentation rules in this data source.");
  }

  #set default selection values
  $ruleTypeVal = "name";
  $matchSide = "unsegmented";
  $matchType = "contain";
  $matchOp = "gt";
  $matchStr = "";
  $segmentVal = 0;
  $matchSegmentVal = 0;

  $segFilter1 = 0;
  $segFilter2 = 0;
  $segFilter1Val = "";
  $segFilter2Val = "";

  #if we're editing an existing rule, fetch it from the database
  if ($ruleID > 0)
  {
    $query = "SELECT segmentID, rule, filter1, filter2 FROM $dsSchema.$dbName \
        WHERE ID=$ruleID AND segmentationID=$segID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    ($segmentVal, $rule, $filter1, $filter2) = $dbOutput->fetchrow_array;

    if ($rule =~ m/^TEXT (.*?) (.*?) (.*)$/)
    {
      $matchSide = $1;
      $matchType = $2;
      $matchStr = $3;
      $ruleTypeVal = "name";
    }
    elsif ($rule =~ m/^ATTR (.*?) (.*?) (.*?) (.*)$/)
    {
      $matchSide = $1;
      $attrVal = $2;
      $matchType = $3;
      $matchStr = $4;
      $ruleTypeVal = "attr";
    }
    elsif ($rule =~ m/^SEGMATCH (.*?) (.*?) (.*?) (.*)$/)
    {
      $matchSide = $1;
      $matchSegVal = $2;
      $matchType = $3;
      $matchStr = $4;
      $ruleTypeVal = "segmatch";
    }
    elsif ($rule =~ m/^SEG (.*?) (.*?) (.*)$/)
    {
      $matchSide = $1;
      $matchSegVal = $2;
      $matchSegmentVal = $3;
      $ruleTypeVal = "seg";
    }
    elsif ($rule =~ m/^SEGVAL (.*?) (.*?) (.*?) (.*?) (.*)$/)
    {
      $matchSide = $1;
      $matchSegVal = $2;
      $matchOp = $3;
      $matchNumVal = $4;
      $matchNumValUpper = $5;
      $ruleTypeVal = "segval";
    }
    elsif ($rule =~ m/^SEGFILLDOWN (.*)$/)
    {
      $matchSegVal = $1;
      $ruleTypeVal = "segfilldown";
    }
    elsif ($rule =~ m/^CATCHALL$/)
    {
      $ruleTypeVal = "catchall";
    }

    #if there are filters specified, parse them
    $advancedIn = "";
    if ($filter1 =~ m/^(.*?) (.*)$/)
    {
      $segFilter1 = $1;
      $segFilter1Val = $2;
      $advancedIn = "show";
    }
    if ($filter2 =~ m/^(.*?) (.*)$/)
    {
      $segFilter2 = $1;
      $segFilter2Val = $2;
      $advancedIn = "show";
    }
  }

  print <<END_HTML;
<SCRIPT>
function submitXhrForm()
{
  let side, matchType, matchStr, segment, type, matchOp, attribute, newSegment;
  let segmentMatch, numVal, numValUpper, segMatch;

  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Saving...');

  type = document.getElementById('rule-type').value;
  side = document.getElementById('match-side').value;
  segment = document.getElementById('segment').value;
  newSegment = document.getElementById('new-segment').value;

  if (type == 'name')
  {
    matchType = document.getElementById('name-matchType').value;
    matchStr = document.getElementById('name-matchStr').value;
  }
  if (type == 'attr')
  {
    matchType = document.getElementById('attr-matchType').value;
    matchStr = document.getElementById('attr-matchStr').value;
    attribute = document.getElementById('attribute').value;
  }
  if (type == 'segmatch')
  {
    segMatch = document.getElementById('segmatch-segMatch').value;
    matchType = document.getElementById('segmatch-matchType').value;
    matchStr = document.getElementById('segmatch-matchStr').value;
  }
  if (type == 'seg')
  {
    segmentMatch = \$('#seg-segmentMatch').val();
    segMatch = document.getElementById('seg-segMatch').value;
  }
  if (type == 'segval')
  {
    segMatch = document.getElementById('segval-segMatch').value;
    matchOp = document.getElementById('segval-matchOp').value;
    numVal = document.getElementById('segval-numval').value;
    numValUpper = document.getElementById('segval-numval-upper').value;
  }
  if (type == 'segfilldown')
  {
    segMatch = document.getElementById('segfilldown-seg').value;
  }
  if (type == 'catchall')
  {
    //NO-OP
  }

  segFilter1 = document.getElementById('seg-filter1').value;
  segFilter1Val = \$('#segment-filter1').val();
  segFilter2 = document.getElementById('seg-filter2').value;
  segFilter2Val = \$('#segment-filter2').val();

  let url = 'segmentRulesDefine.cld?ds=$dsID&dim=$dim&s=$segID&r=$ruleID&type=' + type + '&side=' + side + '&mt=' + matchType + '&ms=' + matchStr + '&smt=' + segment + '&attr=' + attribute + '&mseg=' + segMatch + '&msmt=' + segmentMatch + '&numVal=' + numVal + '&matchOp=' + matchOp + '&numValUpper=' + numValUpper + '&newSegment=' + newSegment + '&segFilter1=' + segFilter1 + '&segFilter1Val=' + segFilter1Val + '&segFilter2=' + segFilter2 + '&segFilter2Val=' + segFilter2Val;

  \$.get(url, function(data, status)
  {
    location.href = 'segmentRules.cld?ds=$dsID&dim=$dim&seg=$segID';
  });
}


function addNewSegment()
{
  let segmentID = document.getElementById('segment').value;

  if (segmentID == 0)
  {
    document.getElementById('new-segment').style.display = 'block';
  }
  else
  {
    document.getElementById('new-segment').style.display = 'none';
  }
}


function changeRule()
{
  let ruleType = document.getElementById('rule-type').value;

  \$('select#match-side').prop('disabled', false);
  \$('select#segment').prop('disabled', false);
  document.getElementById('name-match').style.display = 'none';
  document.getElementById('attr-match').style.display = 'none';
  document.getElementById('seg-name-match').style.display = 'none';
  document.getElementById('seg-match').style.display = 'none';
  document.getElementById('seg-value').style.display = 'none';
  document.getElementById('seg-filldown').style.display = 'none';
  document.getElementById('catch-all').style.display = 'none';

  if (ruleType == 'name')
  {
    document.getElementById('name-match').style.display = 'block';
  }
  else if (ruleType == 'attr')
  {
    document.getElementById('attr-match').style.display = 'block';
  }
  else if (ruleType == 'segmatch')
  {
    document.getElementById('seg-name-match').style.display = 'block';
  }
  else if (ruleType == 'seg')
  {
    document.getElementById('seg-match').style.display = 'block';
  }
  else if (ruleType == 'segval')
  {
    document.getElementById('seg-value').style.display = 'block';
  }
  else if (ruleType == 'segfilldown')
  {
    document.getElementById('seg-filldown').style.display = 'block';
    \$('select#match-side').val('unsegmented');
    \$('select#match-side').prop('disabled', true);
    \$('select#segment').selectpicker('val', '0');
    \$('select#segment').prop('disabled', true);
  }
  else if (ruleType == 'catchall')
  {
    document.getElementById('catch-all').style.display = 'block';
    \$('select#match-side').val('unsegmented');
    \$('select#match-side').prop('disabled', true);
  }
}


function segMatchUpdate(segSel, segmentSel, firstRun, matchSegmentVal)
{
  let segID = document.getElementById(segSel).value;
  let urlStr = 'segmentRulesDefine.cld?ds=$dsID&dim=$dim&a=segments&smt=' + segID;

  \$(segmentSel).empty();
  \$.ajax(
  {
    url: urlStr,
    dataType: 'json',
    type: 'GET',
    success: function(response)
    {
      if (response != '')
      {
        for (i in response)
        {
          \$(segmentSel).append('<OPTION VALUE=' + response[i].id + '>'+response[i].name+'</OPTION>');
        }
        if ((firstRun == 1) && (matchSegmentVal.length > 0))
        {
          let valStr = matchSegmentVal;
          let vals = valStr.split(',');
          \$(segmentSel).val(vals);
        }
      }

      \$(segmentSel).selectpicker('refresh');
    },
    error: function(x, e) {console.log(e)}
  });
}


function segValDisplay()
{
  let matchOp = document.getElementById('segval-matchOp').value;

  if (matchOp == 'bt')
  {
    document.getElementById('segval-between-div1').style.display = 'inline-block';
    document.getElementById('segval-between-div2').style.display = 'inline-block';
  }
  else
  {
    document.getElementById('segval-between-div1').style.display = 'none';
    document.getElementById('segval-between-div2').style.display = 'none';
  }
}
</SCRIPT>

<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-12 col-lg-11 col-xl-9"> <!-- content -->

      <FORM>

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Edit Segmentation Rule</DIV>
        <DIV CLASS="card-body">

          <DIV CLASS="row">
            <DIV CLASS="col-auto mt-2">
              Assign
            </DIV>

            <DIV CLASS="col-auto">
              <SELECT CLASS="form-select" ID="match-side">
                <OPTION VALUE="unsegmented">unsegmented</OPTION>
                <OPTION VALUE="segmented">segmented</OPTION>
                <OPTION VALUE="any">any</OPTION>
              </SELECT>
              <SCRIPT>
                \$('select#match-side').val('$matchSide');
              </SCRIPT>
            </DIV>

            <DIV CLASS="col-auto mt-2">
              items that match this rule to the segment
            </DIV>

            <DIV CLASS="col-auto">
              <SELECT CLASS="selectpicker" ID="segment" TITLE="Select segment" onChange="addNewSegment()">
                <OPTION VALUE='0' data-content="<SPAN CLASS='badge bg-primary'>Create New Segment</SPAN>">Create New Segment</OPTION>
END_HTML

  %segmentHash = DSRseg_get_segments_hash($db, $dsSchema, $dim, $segID);
  foreach $segmentID (sort {$segmentHash{$a} cmp $segmentHash{$b}} keys %segmentHash)
  {
    if ($segmentVal > 0)
    {
      $segmentValJS = "\$('select#segment').val('$segmentVal');";
    }
    print(" <OPTION VALUE='$segmentID'>$segmentHash{$segmentID}</OPTION>\n");
  }

  print <<END_HTML;
              </SELECT>
              <SCRIPT>
                $segmentValJS
              </SCRIPT>
            </DIV>
          </DIV>

          <DIV CLASS="row">
            <DIV CLASS="col-auto">
              <INPUT CLASS="form-control" ID="new-segment" NAME="new-segment" STYLE="display:none;">
            </DIV>
          </DIV>

          <HR>

          <DIV CLASS="row">
            <DIV CLASS="col-auto mt-2">
              Rule type:
            </DIV>
            <DIV CLASS="col-auto">
              <SELECT CLASS="form-select" ID="rule-type" onChange="changeRule()">
                <OPTION VALUE="name">Name Match</OPTION>
                <OPTION VALUE="attr">Attribute Match</OPTION>
                <OPTION VALUE="segmatch">Segment Name Match</OPTION>
                <OPTION VALUE="seg">Segment Membership</OPTION>
                <OPTION VALUE="segval">Segment Value</OPTION>
                <OPTION VALUE="segfilldown">Segmentation Fill-Down</OPTION>
                <OPTION VALUE="catchall">Catch All</OPTION>
              </SELECT>
            </DIV>
          </DIV>

          <P>&nbsp;</P>

<!-- ####################################################################### -->

          <DIV ID="name-match">
            <DIV CLASS="row">
              <DIV CLASS="col-auto mt-2">
                Match items that
              </DIV>
              <DIV CLASS="col-auto">
                <SELECT CLASS="form-select" ID="name-matchType">
                  <OPTION VALUE="contain">contain</OPTION>
                  <OPTION VALUE="begin">begin with</OPTION>
                  <OPTION VALUE="end">end with</OPTION>
                </SELECT>
                <SCRIPT>
                  \$('select#name-matchType').val('$matchType');
                </SCRIPT>
              </DIV>

              <DIV CLASS="col-auto mt-2">
                the characters
              </DIV>

              <DIV CLASS="col-auto">
                <INPUT CLASS="form-control" TYPE="text" ID="name-matchStr" MAXLENGTH="128" VALUE="$matchStr">
              </DIV>
            </DIV>
          </DIV>

<!-- ####################################################################### -->

          <DIV ID="attr-match" STYLE="display:none;">
            <DIV CLASS="row">

              <DIV CLASS="col-auto mt-2">
                Match items with a
              </DIV>

              <DIV CLASS="col-auto gx-1">
                <SELECT CLASS="form-select mx" ID="attribute">
END_HTML

  %attrHash = DSRattr_get_attributes_hash($db, $dsSchema, $dim);

  foreach $attrID (sort {$attrHash{$a} cmp $attrHash{$b}} keys %attrHash)
  {
    if ($attrVal < 1)
    {
      $attrVal = $attrID;
    }
    print(" <OPTION VALUE='$attrID'>$attrHash{$attrID}</OPTION>\n");
  }

  print <<END_HTML;
                </SELECT>
                <SCRIPT>
                  \$('select#attribute').val('$attrVal');
                </SCRIPT>
              </DIV>

              <DIV CLASS="col-auto gx-1 mt-2">
                attribute that
              </DIV>

              <DIV CLASS="col-auto gx-1">
                <SELECT CLASS="form-select" ID="attr-matchType">
                  <OPTION VALUE="contain">contains</OPTION>
                  <OPTION VALUE="begin">begins with</OPTION>
                  <OPTION VALUE="end">ends with</OPTION>
                </SELECT>
                <SCRIPT>
                  \$('select#attr-matchType').val('$matchType');
                </SCRIPT>
              </DIV>

              <DIV CLASS="col-auto gx-1 mt-2">
                the characters
              </DIV>

              <DIV CLASS="col-auto gx-1">
                <INPUT CLASS="form-control mx-1" TYPE="text" ID="attr-matchStr" MAXLENGTH="128" VALUE="$matchStr">
              </DIV>
            </DIV>
          </DIV>


<!-- ####################################################################### -->

          <DIV ID="seg-name-match">

            <DIV CLASS="row">
              <DIV CLASS="col-auto mt-2">
                Match items that are members of all segments in the
              </DIV>

              <DIV CLASS="col-auto gx-1">
                <SELECT CLASS="form-select" ID="segmatch-segMatch">
END_HTML

  %segHash = DSRsegmentation_get_segmentations_hash($db, $dsSchema, $dim);
  foreach $segID (sort {$segHash{$a} cmp $segHash{$b}} keys %segHash)
  {
    if ($matchSegVal < 1)
    {
      $matchSegVal = $segID;
    }
    print(" <OPTION VALUE='$segID'>$segHash{$segID}</OPTION>\n");
  }

  print <<END_HTML;
                </SELECT>
                <SCRIPT>
                  \$('select#segmatch-segMatch').val('$matchSegVal');
                </SCRIPT>
              </DIV>
            </DIV>

            <DIV CLASS="row">
              <DIV CLASS="col-auto mt-2">
                segmentation that
              </DIV>

              <DIV CLASS="col-auto">
                <SELECT CLASS="form-select mx-1" ID="segmatch-matchType">
                  <OPTION VALUE="contain">contain</OPTION>
                  <OPTION VALUE="begin">begin with</OPTION>
                  <OPTION VALUE="end">end with</OPTION>
                </SELECT>
                <SCRIPT>
                  \$('select#segmatch-matchType').val('$matchType');
                </SCRIPT>
              </DIV>

              <DIV CLASS="col-auto mt-2">
                the characters
              </DIV>

              <DIV CLASS="col-auto">
                <INPUT CLASS="form-control mx-1" TYPE="text" ID="segmatch-matchStr" MAXLENGTH="128" VALUE="$matchStr">
              </DIV>

            </DIV>
          </DIV>


<!-- ####################################################################### -->


          <DIV ID="seg-match">

            <DIV CLASS="row">
              <DIV CLASS="col-auto mt-2">
                Match items that in the
              </DIV>

              <DIV CLASS="col-auto">
                <SELECT CLASS="form-select" ID="seg-segMatch" onChange="segMatchUpdate('seg-segMatch', '#seg-segmentMatch', 0);">
END_HTML

  foreach $segID (sort {$segHash{$a} cmp $segHash{$b}} keys %segHash)
  {
    if ($matchSegVal < 1)
    {
      $matchSegVal = $segID;
    }
    print(" <OPTION VALUE='$segID'>$segHash{$segID}</OPTION>\n");
  }

  print <<END_HTML;
                </SELECT>
              </DIV>

              <DIV CLASS="col-auto mt-2">
                segmentation are members of the
              </DIV>
            </DIV>

            <DIV CLASS="row">
              <DIV CLASS="col-auto">
                <SELECT CLASS="selectpicker my-1" ID="seg-segmentMatch" multiple>
                </SELECT>
                <SCRIPT>
                \$(document).ready(function()
                {
                  \$('#seg-segMatch').val('$matchSegVal');
                  segMatchUpdate('seg-segMatch', '#seg-segmentMatch', 1, '$matchSegmentVal');
                });
                </SCRIPT>
              </DIV>

              <DIV CLASS="col-auto mt-2">
                segment(s).
              </DIV>
            </DIV>

          </DIV>


<!-- ####################################################################### -->


          <DIV ID="seg-value">
            <DIV CLASS="row">

              <DIV CLASS="col-auto mt-2">
                Match items belonging to a segment in
              </DIV>

              <DIV CLASS="col-auto">
                <SELECT CLASS="form-select" ID="segval-segMatch">
END_HTML

  foreach $segID (sort {$segHash{$a} cmp $segHash{$b}} keys %segHash)
  {
    if ($matchSegVal < 1)
    {
      $matchSegVal = $segID;
    }
    print(" <OPTION VALUE='$segID'>$segHash{$segID}</OPTION>\n");
  }

  print <<END_HTML;
                </SELECT>
                <SCRIPT>
                  \$('select#segval-segMatch').val('$matchSegVal');
                </SCRIPT>
              </DIV>

              <DIV CLASS="col-auto mt-2">
                with a value
              </DIV>
            </DIV>

            <DIV CLASS="row my-1">
              <DIV CLASS="col-auto">
                <SELECT CLASS="form-select" ID="segval-matchOp" onChange="segValDisplay();">
                  <OPTION VALUE="gt">greater than</OPTION>
                  <OPTION VALUE="lt">less than</OPTION>
                  <OPTION VALUE="bt">between</OPTION>
                  <OPTION VALUE="eq">equal to</OPTION>
                  <OPTION VALUE="ge">greater than or equal to</OPTION>
                  <OPTION VALUE="le">less than or equal to</OPTION>
                </SELECT>
                <SCRIPT>
                  \$(document).ready(function()
                  {
                    \$('select#segval-matchOp').val('$matchOp');
                    segValDisplay();
                  });
                </SCRIPT>
              </DIV>
              <DIV CLASS="col-auto gx-1">
                <INPUT CLASS="form-control" TYPE="number" ID="segval-numval" STYLE="width:8em;" VALUE="$matchNumVal">
              </DIV>

              <DIV CLASS="col-auto gx-1 mt-2" ID="segval-between-div1" STYLE="display:none;">
                and
              </DIV>
              <DIV CLASS="col-auto gx-1" ID="segval-between-div2" STYLE="display:none;">
                <INPUT CLASS="form-control" TYPE="number" ID="segval-numval-upper" STYLE="width:8em;" VALUE="$matchNumValUpper">
              </DIV>

            </DIV>
          </DIV>


<!-- ####################################################################### -->

          <DIV ID="seg-filldown">
            <DIV CLASS="row">
              <DIV CLASS="col-auto mt-2">
                Assign unsegmented items to the same segment in this segmentation that they're assigned to in
              </DIV>
              <DIV CLASS="col-auto">
                <SELECT CLASS="form-select" ID="segfilldown-seg">
END_HTML

  foreach $segID (sort {$segHash{$a} cmp $segHash{$b}} keys %segHash)
  {
    if ($matchSegVal < 1)
    {
      $matchSegVal = $segID;
    }
    print(" <OPTION VALUE='$segID'>$segHash{$segID}</OPTION>\n");
  }

  print <<END_HTML;
                </SELECT>
                <SCRIPT>
                  \$('select#segfilldown-seg').val('$matchSegVal');
                </SCRIPT>
              </DIV>
            </DIV>

          </DIV>

<!-- ####################################################################### -->


          <DIV ID="catch-all" CLASS="form-group form-inline">

            Assign all remaining unsegmented items to the segment specified above.

          </DIV>

<!-- ####################################################################### -->

          <SCRIPT>
            \$('select#rule-type').val('$ruleTypeVal');
            changeRule();
          </SCRIPT>

          <P>&nbsp;</P>
          <DIV CLASS="accordion" ID="accordion">
            <DIV CLASS="accordion-item">
              <H2 CLASS="accordion-header">
                <BUTTON CLASS="accordion-button bg-secondary bg-opacity-10" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
                  Additional Filters
                </BUTTON>
              </H2>

              <DIV ID="collapse1" CLASS="accordion-collapse collapse $advancedIn" data-bs-parent="#accordion">
                <DIV CLASS="accordion-body">

                  <DIV CLASS="row">
                    <DIV CLASS="col-auto mt-2">
                      Only apply this rule to items that in the
                    </DIV>

                    <DIV CLASS="col-auto">
                      <SELECT CLASS="form-select" ID="seg-filter1" onChange="segMatchUpdate('seg-filter1', '#segment-filter1', 0);">
END_HTML

  print(" <OPTION VALUE='0'></OPTION>\n");
  foreach $segID (sort {$segHash{$a} cmp $segHash{$b}} keys %segHash)
  {
    if ($matchSegVal < 1)
    {
      $matchSegVal = $segID;
    }
    print(" <OPTION VALUE='$segID'>$segHash{$segID}</OPTION>\n");
  }

  print <<END_HTML;
                      </SELECT>
                    </DIV>

                    <DIV CLASS="col-auto mt-2">
                      segmentation are members of the
                    </DIV>

                    <DIV CLASS="col-auto">
                      <SELECT CLASS="selectpicker my-1" ID="segment-filter1" data-container="body" multiple>
                      </SELECT>
                    </DIV>

                    <DIV CLASS="col-auto mt-2 gx-0">
                      segment(s),
                    </DIV>
                  </DIV>

                  <P>&nbsp;</P>

                  <DIV CLASS="row">
                    <DIV CLASS="col-auto mt-2">
                      and in the
                    </DIV>

                    <DIV CLASS="col-auto">
                      <SELECT CLASS="form-select" ID="seg-filter2" onChange="segMatchUpdate('seg-filter2', '#segment-filter2', 0);">
END_HTML

  print(" <OPTION VALUE='0'></OPTION>\n");
  foreach $segID (sort {$segHash{$a} cmp $segHash{$b}} keys %segHash)
  {
    if ($matchSegVal < 1)
    {
      $matchSegVal = $segID;
    }
    print(" <OPTION VALUE='$segID'>$segHash{$segID}</OPTION>\n");
  }

  print <<END_HTML;
                      </SELECT>
                    </DIV>

                    <DIV CLASS="col-auto mt-2">
                      segmentation are members of the
                    </DIV>
                  </DIV>

                  <DIV CLASS="row">
                    <DIV CLASS="col-auto">
                      <SELECT CLASS="selectpicker my-1" ID="segment-filter2" data-bs-container="body" multiple>
                      </SELECT>
                    </DIV>

                    <DIV CLASS="col-auto mt-2">
                      segment(s).
                    </DIV>
                  </DIV>

                  <SCRIPT>
                  \$(document).ready(function()
                  {
                    \$('#seg-filter1').val('$segFilter1');
                    \$('#seg-filter2').val('$segFilter2');
                    segMatchUpdate('seg-filter1', '#segment-filter1', 1, '$segFilter1Val');
                    segMatchUpdate('seg-filter2', '#segment-filter2', 1, '$segFilter2Val');
                  });
                  </SCRIPT>

                </DIV>

              </DIV>
            </DIV>
          </DIV>

          <P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="location.href='segmentRules.cld?ds=$dsID&dim=$dim&seg=$segID'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="button" ID="btn-submit" onClick="submitXhrForm()"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
          </DIV>

          </FORM>

        </DIV>
      </DIV>

      <P>
    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
