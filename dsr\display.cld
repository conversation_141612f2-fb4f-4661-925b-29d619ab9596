#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  #output Content-type header
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  #output HTML
  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Datasource</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/jquery-ui/jquery-ui.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<SCRIPT SRC="/fancytree-2.31.0/dist/jquery.fancytree-all-deps.min.js"></SCRIPT>
<SCRIPT SRC="/fancytree-2.31.0/dist/modules/jquery.fancytree.multi.js"></SCRIPT>

<LINK REL="stylesheet" HREF="/jquery-ui/jquery-ui.css">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/fancytree-2.31.0/dist/skin-win8/ui.fancytree.min.css" rel="stylesheet">


<STYLE>
.dijitLeafCalc
{
  background-image: url(/icons/dijitLeafCalc.png);
  background-repeat: no-repeat;
  background-position: center center;
  width: 16px;
  height: 16px;
}
</STYLE>

<SCRIPT>
let gridHeight = window.innerHeight - 210;

\$(function()
{
  \$('#prodtree').fancytree(
  {
    selectMode: 2,
    quicksearch: true,
    autoScroll: true,
    extensions: ['multi'],
    multi: {mode: 'sameParent'},
    dblclick: function(event, data)
  	{
  	  data.node.setSelected();
  	  doEdit();
  	},
    init: function(event, data)
  	{
  	  let tree = data.tree;
  	  let segHierNode = tree.findFirst('Segmentation Hierarchies');

      segHierNode.sortChildren(null, true);
  	},
    lazyLoad: function(event, data)
    {
      let node = data.node;
      data.result =
      {
        url: "ajaxDimensionTrees.cld",
        data: {'ds': '$dsID', 'd': 'p', mode: "children", parent: node.key},
        cache: false
      };
    },
    clickPaging: function(event, data)
    {
      data.node.replaceWith({url: data.node.data.url}).done(function()
      {
        //pagingNode replaced with data pulled from data source
      });
    },

    source: {url: '/app/dsr/ajaxDimensionTrees.cld?ds=$dsID&d=p&s=dsr'}
  });

  \$('#geotree').fancytree(
  {
    selectMode: 2,
    quicksearch: true,
    autoScroll: true,
    extensions: ['multi'],
    multi: {mode: 'sameParent'},
    dblclick: function(event, data)
    {
      data.node.setSelected();
      doEdit();
    },
    lazyLoad: function(event, data)
    {
      let node = data.node;
      data.result =
      {
        url: "ajaxDimensionTrees.cld",
        data: {'ds': '$dsID', 'd': 'g', mode: "children", parent: node.key},
        cache: false
      };
    },
    source: {url: '/app/dsr/ajaxDimensionTrees.cld?ds=$dsID&d=g&s=dsr'}
  });

  \$('#timetree').fancytree(
  {
    selectMode: 2,
    quicksearch: true,
    autoScroll: true,
    extensions: ['multi'],
    multi: {mode: 'sameParent'},
    dblclick: function(event, data)
    {
      data.node.setSelected();
      doEdit();
    },
    lazyLoad: function(event, data)
    {
      let node = data.node;
      data.result =
      {
        url: "ajaxDimensionTrees.cld",
        data: {'ds': '$dsID', 'd': 't', mode: "children", parent: node.key},
        cache: false
      };
    },
    source: {url: '/app/dsr/ajaxDimensionTrees.cld?ds=$dsID&d=t&s=dsr'}
  });

  \$('#meastree').fancytree(
  {
    selectMode: 2,
    quicksearch: true,
    autoScroll: true,
    extensions: ['multi'],
    multi: {mode: 'sameParent'},
    dblclick: function(event, data)
    {
      data.node.setSelected();
      doEdit();
    },
    lazyLoad: function(event, data)
    {
      let node = data.node;
      data.result =
      {
        url: "ajaxDimensionTrees.cld",
        data: {'ds': '$dsID', 'd': 'm', mode: "children", parent: node.key},
        cache: false
      };
    },
    source: {url: '/app/dsr/ajaxDimensionTrees.cld?ds=$dsID&d=m&s=dsr'}
  });
});



\$(document).ready(function()
{
  \$('#dim-tabs').height(gridHeight);
});



function addAttribute()
{
  let activeTab = \$('ul#dimTabs a.active')[0].id;
  let dim = 'p';

  if (activeTab == 'prodTab')
  {
    dim = 'p';
  }
  else if (activeTab == 'geoTab')
  {
    dim = 'g';
  }
  else if (activeTab == 'timeTab')
  {
    dim = 't';
  }

  location.href = '/app/dsr/newAttributeName.cld?ds=$dsID&dim=' + dim;
}



function addList()
{
  let activeTab = \$('ul#dimTabs a.active')[0].id;
  let dim = '';

  if (activeTab == 'prodTab')
  {
    dim = 'p';
  }
  else if (activeTab == 'geoTab')
  {
    dim = 'g';
  }
  else if (activeTab == 'timeTab')
  {
    dim = 't';
  }
  else
  {
    dim = 'm';
  }

  location.href = '/app/dsr/listName.cld?ds=$dsID&dim=' + dim;
}



function addAggregate()
{
  let activeTab = \$('ul#dimTabs a.active')[0].id;
  let dim = 'p';

  if (activeTab == 'prodTab')
  {
    dim = 'p';
  }
  else if (activeTab == 'geoTab')
  {
    dim = 'g';
  }
  else if (activeTab == 'timeTab')
  {
    dim = 't';
  }

  location.href = '/app/dsr/aggName.cld?ds=$dsID&dim=' + dim;
}



function addSegmentation()
{
  let activeTab = \$('ul#dimTabs a.active')[0].id;
  let dim = 'p';

  if (activeTab == 'prodTab')
  {
    dim = 'p';
  }
  else if (activeTab == 'geoTab')
  {
    dim = 'g';
  }
  else if (activeTab == 'timeTab')
  {
    dim = 't';
  }

  location.href = '/app/dsr/segmentName.cld?ds=$dsID&dim=' + dim;
}



function addSegHierarchy()
{
  let activeTab = \$('ul#dimTabs a.active')[0].id;
  let dim = 'p';

  if (activeTab == 'prodTab')
  {
    dim = 'p';
  }
  else if (activeTab == 'geoTab')
  {
    dim = 'g';
  }
  else if (activeTab == 'timeTab')
  {
    dim = 't';
  }

  location.href = '/app/dsr/segHierName.cld?ds=$dsID&dim=' + dim;
}



function doEdit()
{
  let activeTab = \$('ul#dimTabs a.active')[0].id;
  let dim = '';
  let tree;

  if (activeTab == 'prodTab')
  {
    dim = 'p';
    tree = \$('#prodtree').fancytree('getTree');
  }
  else if (activeTab == 'geoTab')
  {
    dim = 'g';
    tree = \$('#geotree').fancytree('getTree');
  }
  else if (activeTab == 'timeTab')
  {
    dim = 't';
    tree = \$('#timetree').fancytree('getTree');
  }
  else if (activeTab == 'measTab')
  {
    dim = 'm';
    tree = \$('#meastree').fancytree('getTree');
  }

  let nodes = tree.getSelectedNodes();
  let curNode = nodes[0];

  if (!curNode)
  {
    document.getElementById('warn-select-item').style.display = 'block';
    return;
  }

  if ((curNode.type == 'base') && (dim == 'p'))
  {
    location.href = '/app/dsr/editItem.cld?ds=$dsID&dim=' + dim + '&item=' + curNode.key + '&merged=' + curNode.data.merged;
  }
  else if (curNode.type == 'base')
  {
    location.href = '/app/dsr/editItem.cld?ds=$dsID&dim=' + dim + '&item=' + curNode.key;
  }
  else if (curNode.type == 'aggr')
  {
    location.href = '/app/dsr/aggName.cld?ds=$dsID&dim=' + dim + '&a=' + curNode.key;
  }
  else if (curNode.type == 'aggitem')
  {
    location.href = '/app/dsr/editItem.cld?ds=$dsID&dim=' + dim + '&item=' + curNode.key;
  }
  else if (curNode.type == 'list')
  {
    location.href = '/app/dsr/listName.cld?ds=$dsID&dim=' + dim + '&l=' + curNode.key;
  }
  else if (curNode.type == 'listitem')
  {
    location.href = '/app/dsr/editItem.cld?ds=$dsID&dim=' + dim + '&item=' + curNode.key;
  }
  else if (curNode.type == 'seg')
  {
    location.href = '/app/dsr/segmentName.cld?ds=$dsID&dim=' + dim + '&seg=' + curNode.key;
  }
  else if (curNode.type == 'segment')
  {
    location.href = '/app/dsr/segmentAssign.cld?ds=$dsID&dim=' + dim + '&seg=' + curNode.parent.key;
  }
  else if (curNode.type == 'segitem')
  {
    location.href = '/app/dsr/editItem.cld?ds=$dsID&dim=' + dim + '&item=' + curNode.key;
  }
  else if (curNode.type == 'attr')
  {
    location.href = 'editAttribute.cld?ds=$dsID&dim=' + dim + '&attr=' + curNode.key;
  }
  else if (curNode.type == 'segmenthier')
  {
    location.href = 'segHierName.cld?ds=$dsID&dim=' + dim + '&segHier=' + curNode.key;
  }
  else if (curNode.type == 'segmenthierlevel')
  {
    location.href = 'editItem.cld?ds=$dsID&dim=' + dim + '&item=' + curNode.key;
  }
END_HTML

  if ($acctType == 10)
  {
    print <<END_HTML;
  else if (curNode.type == 'header')
  {
    if (curNode.key == 'segmentations')
    {
      location.href = 'segmentAssignGrid.cld?ds=$dsID&dim=' + dim;
    }
  }
END_HTML
  }

  print <<END_HTML;
}



function doCopy()
{
  let activeTab = \$('ul#dimTabs a.active')[0].id;
  let dim = '';
  let tree;

  if (activeTab == 'prodTab')
  {
    dim = 'p';
    tree = \$('#prodtree').fancytree('getTree');
  }
  else if (activeTab == 'geoTab')
  {
    dim = 'g';
    tree = \$('#geotree').fancytree('getTree');
  }
  else if (activeTab == 'timeTab')
  {
    dim = 't';
    tree = \$('#timetree').fancytree('getTree');
  }
  else if (activeTab == 'measTab')
  {
    dim = 'm';
    tree = \$('#meastree').fancytree('getTree');
  }

  let nodes = tree.getSelectedNodes();
  let curNode = nodes[0];

  if (!curNode)
  {
    document.getElementById('warn-select-item').style.display = 'block';
    return;
  }

  if (curNode.type == 'seg')
  {
    location.href = 'copyStructure.cld?ds=$dsID&dim=' + dim + '&seg=' + curNode.key;
  }
  else if (curNode.type == 'list')
  {
    location.href = 'copyStructure.cld?ds=$dsID&dim=' + dim + '&list=' + curNode.key;
  }
  else if (curNode.type == 'aggr')
  {
    location.href = 'copyStructure.cld?ds=$dsID&dim=' + dim + '&agg=' + curNode.key;
  }
  else if (curNode.type == 'segmenthier')
  {
    location.href = 'copyStructure.cld?ds=$dsID&dim=' + dim + '&seghier=' + curNode.key;
  }
}



function doUnmerge()
{
  let activeTab = \$('ul#dimTabs a.active')[0].id;

  if (activeTab != 'prodTab')
  {
    return;
  }

  let tree = \$('#prodtree').fancytree('getTree');
  let nodes = tree.getSelectedNodes();
  let curNode = nodes[0];

  if ((curNode.type == 'base') && (curNode.data.merged == 1))
  {
    location.href = '/app/dsr/deleteItemConfirm.cld?ds=$dsID&type=base&dim=p&item=' + curNode.key + '&merged=' + curNode.data.merged;
  }
}



function doDelete()
{
  let activeTab = \$('ul#dimTabs a.active')[0].id;
  let dim, tree;
  let items = '';

  if (activeTab == 'prodTab')
  {
    dim = 'p';
    tree = \$('#prodtree').fancytree('getTree');
  }
  else if (activeTab == 'geoTab')
  {
    dim = 'g';
    tree = \$('#geotree').fancytree('getTree');
  }
  else if (activeTab == 'timeTab')
  {
    dim = 't';
    tree = \$('#timetree').fancytree('getTree');
  }
  else
  {
    dim = 'm';
    tree = \$('#meastree').fancytree('getTree');
  }

  let nodes = tree.getSelectedNodes();
  let curNode = nodes[0];

  if (!curNode)
  {
    document.getElementById('warn-select-item').style.display = 'block';
    return;
  }

  if ((curNode.type == 'base') && (dim == 'p') && (curNode.data.merged > 0))
  {
    location.href = '/app/dsr/deleteItemConfirm.cld?ds=$dsID&type=base&dim=' + dim + '&item=' + curNode.key + '&merged=' + curNode.data.merged;
  }
  else if ((curNode.type == 'base') || (curNode.type == 'segitem'))
  {
    let i = 0;
    while ((i < nodes.length) && (i < 1000))
    {
      items = items + nodes[i].key + ',';
      i++;
    }

    location.href = '/app/dsr/deleteItemConfirm.cld?ds=$dsID&type=base&dim=' + dim + '&item=' + items;
  }
  else if (curNode.type == 'attr')
  {
    location.href = '/app/dsr/deleteItemConfirm.cld?ds=$dsID&type=attr&dim=' + dim + '&item=' + curNode.key;
  }
  else if (curNode.type == 'list')
  {
    location.href = '/app/dsr/deleteItemConfirm.cld?ds=$dsID&type=list&dim=' + dim + '&item=' + curNode.key;
  }
  else if (curNode.type == 'aggr')
  {
    location.href = '/app/dsr/deleteItemConfirm.cld?ds=$dsID&type=aggr&dim=' + dim + '&item=' + curNode.key;
  }
  else if (curNode.type == 'seg')
  {
    location.href = '/app/dsr/deleteItemConfirm.cld?ds=$dsID&type=seg&dim=' + dim + '&item=' + curNode.key;
  }
  else if (curNode.type == 'segment')
  {
    let i = 0;
    while ((i < nodes.length) && (i < 200))
    {
      items = items + nodes[i].key + ',';
      i++;
    }

    location.href = '/app/dsr/deleteItemConfirm.cld?ds=$dsID&type=seg&dim=' + dim + '&item=' + items;
  }
  else if (curNode.type == 'segmenthier')
  {
    location.href = '/app/dsr/deleteItemConfirm.cld?ds=$dsID&type=segmenthier&dim=' + dim + '&item=' + curNode.key;
  }
}
</SCRIPT>

</HEAD>

<BODY>

<DIV ID="warn-select-item" CLASS="alert alert-warning alert-dismissible fade show" role="alert" STYLE="display:none; width:50%; position:absolute; margin-left:25%;">
  <DIV CLASS="text-center"><STRONG>Select an item to perform this operation on.</STRONG></DIV>
  <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="alert"></BUTTON>
</DIV>

END_HTML

  #display a dismissable alert box if the last update attempt errored out
  $query = "SELECT status FROM app.dataSources WHERE ID=$dsID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($dsStatus) = $dbOutput->fetchrow_array;
  if ($dsStatus =~ m/^ERROR:(.*)$/)
  {
    print <<END_HTML;
<DIV CLASS="alert alert-danger alert-dismissible fade show" role="alert" STYLE="display:none; width:50%; position:absolute; margin-left:25%;">
  <DIV CLASS="text-center"><STRONG>An error occurred during the last update: $1</STRONG></DIV>
  <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="alert"></BUTTON>
</DIV>

END_HTML
  }

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-0">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item active">$dsName</LI>
  </OL>
</NAV>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get the CGI input variables
  $dsID = $q->param('ds');
  $dim = $q->param('dim');
  $path = $q->param('path');
  $ODBCrefresh = $q->param('ODBC');

  #sanitize parameters
  $dsID = utils_sanitize_integer($dsID);
  $dim = utils_sanitize_dim($dim);
  $path = utils_sanitize_string($path);
  $ODBCrefresh = utils_sanitize_integer($ODBCrefresh);

  #check validity of CGI input variables
  if ($dsID < 1)
  {
    exit_early_error($session, "Invalid data source, please press the Back button and make a different selection.");
  }

  #connect to the database
  $db = KAPutil_connect_to_database();

  #get datasource's name
  $dsName = ds_id_to_name($db, $dsID);

  #if we need to put the DS into the ODBC export queue
  if ($ODBCrefresh == 1)
  {
    $query = "UPDATE dataSources SET ODBCstatus='QUEUE' WHERE ID=$dsID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);
  }

  #if we were given a dimension tab to display, set up the "selected" code
  $prodSel = "";
  $geoSel = "";
  $timeSel = "";
  $measSel = "";
  if ($dim eq "p")
  {
    $prodSel = "active";
  }
  elsif ($dim eq "g")
  {
    $geoSel = "active";
  }
  elsif ($dim eq "t")
  {
    $timeSel = "active";
  }
  elsif ($dim eq "m")
  {
    $measSel = "active";
  }
  else
  {
    $prodSel = "active";
  }

  #if we were given a tree path to open, set it up as a JS array string
  $pathJS = "[]";
  if ($path =~ /^[0-9]+$/)
  {
    $pathJS = "['0', 'all', '$path']";
  }

  print_html_header();

  #error out if the data source doesn't exist
  if (length($dsName) < 1)
  {
    exit_error("It looks like you're trying to access a data source that doesn't exist in this cloud - press the Back button and try again.");
  }

  #see what sort of rights we have to this data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges on this data source.");
  }

  #if the data source is being updated, redirect to DS updating status
  $query = "SELECT opInfo FROM app.jobs WHERE dsID=$dsID AND operation='DS-UPDATE'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($opInfo) = $dbOutput->fetchrow_array;
  if ($opInfo =~ m/^Update/)
  {
    print("<HTML><HEAD><META HTTP-EQUIV='refresh' CONTENT='0; URL=updateDSwork.cld?ds=$dsID'></HEAD></HTML>\n");
    exit;
  }

  #if the data source is being used by another batch process
  $query = "SELECT PID FROM app.jobs \
      WHERE dsID=$dsID AND operation IN ('ROLLBACK', 'XFER-MEASURES', 'MERGE-ITEMS', 'FORCE-REFRESH', 'ADD-MEASURE', 'COPY-DS')";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($pid) = $dbOutput->fetchrow_array;
  if ($pid > 0)
  {
    print("<HTML><HEAD><META HTTP-EQUIV='refresh' CONTENT='0; URL=jobProgress.cld?ds=$dsID'></HEAD></HTML>\n");
    exit;
  }

  $rptLink = "/app/rpt/main?ds=$dsID";

  print <<END_HTML;
<NAV CLASS="navbar navbar-expand-lg navbar-light bg-light border">
  <BUTTON CLASS="navbar-toggler" TYPE="button" data-bs-toggle="collapse" data-bs-target="#navbarNavDropdown">
    <SPAN CLASS="navbar-toggler-icon"></SPAN>
  </BUTTON>
  <DIV CLASS="collapse navbar-collapse justify-content-center" ID="navbarNavDropdown">
    <UL CLASS="navbar-nav">
      <LI CLASS="nav-item dropdown">
        <A CLASS="nav-link dropdown-toggle" data-bs-toggle="dropdown" HREF="#"><I CLASS="bi bi-plus-lg"></I> New</A>
        <UL CLASS="dropdown-menu">
          <LI><A CLASS="dropdown-item" HREF="#" onclick="addAggregate()">Aggregate</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="addAttribute()">Attribute</A></LI>
          <LI><A CLASS="dropdown-item" HREF="calcMeasureName.cld?ds=$dsID">Calculated Measure</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="addList()">List</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="addSegmentation()">Segmentation</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="addSegHierarchy()">Segmentation Hierarchy</A></LI>
        </UL>
      </LI>

      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="doEdit()"><I CLASS="bi bi-pencil"></I> Edit</A></LI>

      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="doCopy()"><I CLASS="bi bi-clipboard-plus"></I> Copy</A></LI>

      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="location.href='accessControl.cld?ds=$dsID'"><I CLASS="bi bi-people"></I> Sharing</A></LI>

      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="location.href='properties.cld?ds=$dsID'"><I CLASS="bi bi-gear"></I> Properties</A></LI>

      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="doDelete()"><I CLASS="bi bi-trash"></I> Delete</A></LI>

      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="window.open('$rptLink', '_blank')"><I CLASS="bi bi-bar-chart-line"></I> Reports</A></LI>

      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="location.href='updateDSSource.cld?ds=$dsID'"><I CLASS="bi bi-arrow-clockwise"></I> Update</A></LI>

      <LI CLASS="nav-item dropdown">
        <A CLASS="nav-link dropdown-toggle" data-bs-toggle="dropdown" HREF="#"><I CLASS="bi bi-three-dots-vertical"></I> More</A>
        <UL CLASS="dropdown-menu">
          <LI><A CLASS="dropdown-item" HREF="history.cld?ds=$dsID">History</A></LI>
          <LI><A CLASS="dropdown-item" HREF="statistics.cld?ds=$dsID">Statistics</A></LI>
          <LI CLASS="dropdown-divider"></LI>
          <LI><A CLASS="dropdown-item" HREF="mergedProductsDefine.cld?ds=$dsID">Merge Products</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="doUnmerge()">Unmerge Products</A></LI>
          <LI CLASS="dropdown-divider"></LI>
          <LI><A CLASS="dropdown-item" HREF="copyDS.cld?ds=$dsID">Copy Data Source</A></LI>
          <LI><A CLASS="dropdown-item" HREF="transferStructuresDS.cld?ds=$dsID">Transfer Data Source Structures</A></LI>
          <LI><A CLASS="dropdown-item" HREF="exportTabular.cld?ds=$dsID">Export Data Source</A></LI>
          <LI CLASS="dropdown-divider"></LI>
          <LI><A CLASS="dropdown-item" HREF="aliasTimes.cld?ds=$dsID">Alias All Time Periods</A></LI>
          <LI><A CLASS="dropdown-item" HREF="aliasProducts.cld?ds=$dsID">Improve Product Names</A></LI>
          <LI CLASS="dropdown-divider"></LI>
          <LI><A CLASS="dropdown-item" HREF="restateGeos.cld?ds=$dsID">Restate Geographies</A></LI>
          <LI CLASS="dropdown-divider"></LI>
          <LI><A CLASS="dropdown-item" HREF="checkDS.cld?ds=$dsID">Check Data Source</A></LI>
          <LI><A CLASS="dropdown-item" HREF="forceRefresh.cld?ds=$dsID">Force Data Source Refresh</A></LI>
END_HTML

   #if we're ODBC exported and have the manual refresh flag set, display the
   #option to force a refresh
   $query = "SELECT ODBCexport, ODBCmanual FROM dataSources WHERE ID=$dsID";
   $dbOutput = $db->prepare($query);
   $status = $dbOutput->execute;
   KAPutil_handle_db_err($db, $status, $query);
   ($ODBCexport, $ODBCmanual) = $dbOutput->fetchrow_array;

   if (($ODBCexport > 0) && ($ODBCmanual == 1))
   {
     print <<END_HTML;
          <LI><A CLASS="dropdown-item" HREF="?ds=$dsID&ODBC=1">Refresh ODBC</A></LI>
END_HTML
   }

   print <<END_HTML;
        </UL>
      </LI>
    </UL>
  </DIV>
</NAV>

<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-12 col-lg-11 col-xl-9"> <!-- content -->

      <P>

      <UL CLASS="nav nav-tabs mx-auto" ID="dimTabs" role="tablist">
        <LI class="nav-item"><A ID="prodTab" class="nav-link $prodSel" HREF="#products" aria-controls="products" role="tab" data-bs-toggle="tab">Products</A></LI>
        <LI class="nav-item"><A ID="geoTab" class="nav-link $geoSel" HREF="#geographies" aria-controls="geographies" role="tab" data-bs-toggle="tab">Geographies</A></LI>
        <LI class="nav-item"><A ID="timeTab" class="nav-link $timeSel" HREF="#times" aria-controls="times" role="tab" data-bs-toggle="tab">Time Periods</A></LI>
        <LI class="nav-item"><A ID="measTab" class="nav-link $measSel" HREF="#measures" aria-controls="measures" role="tab" data-bs-toggle="tab">Measures</A></LI>
      </UL>

      <DIV CLASS="tab-content mx-auto" ID="dim-tabs" STYLE="border-left:1px solid lightgray; border-right: 1px solid lightgray; border-bottom: 1px solid lightgray; overflow:auto;">

        <DIV role="tabpanel" CLASS="tab-pane $prodSel" ID="products">
          <DIV id="prodtree"></DIV>
        </DIV>

        <DIV role="tabpanel" CLASS="tab-pane $geoSel" ID="geographies">
          <DIV id="geotree"></DIV>
        </DIV>

        <DIV role="tabpanel" CLASS="tab-pane $timeSel" ID="times">
          <DIV id="timetree"></DIV>
        </DIV>

        <DIV role="tabpanel" CLASS="tab-pane $measSel" ID="measures">
          <DIV id="meastree"></DIV>
        </DIV>

      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

  $activity = "$first $last viewed data source $dsName";
  utils_slack($activity);


#EOF
