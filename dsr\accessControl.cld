#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  #output Content-type header
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  #output HTML
  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Data Source Sharing</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Please Wait...');
  return(true);
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Sharing</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------


  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');
  $refLinkCode = $q->param('r');

  if ($refLinkCode eq "m")
  {
    $refLink = "main.cld?ds=$dsID";
  }
  else
  {
    $refLink = "display.cld?ds=$dsID";
  }

  #sanitize parameters
  $dsID = utils_sanitize_integer($dsID);

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  #if we got an invalid parameter
  if ($dsID < 1)
  {
    exit_error("Invalid data source");
  }

  #make sure we have read privs for this data source to view sharing
  #NB: accessControlSave only lets the DS owner or an admin save changes
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to view the sharing settings of this data source.");
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-12 col-lg-10 col-xl-8"> <!-- content -->

      <FORM METHOD="post" ACTION="accessControlSave.cld" onsubmit="return checkForm(this);">
      <INPUT TYPE="hidden" NAME="ds" VALUE="$dsID">
      <INPUT TYPE="hidden" NAME="r" VALUE="$refLinkCode">

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Data Source Sharing</DIV>
        <DIV CLASS="card-body">

          Choose the users you want to grant read or write access on the <B>$dsName</B> data source to:
          <P>

          <DIV CLASS="table-responsive">
            <TABLE CLASS="table table-hover table-bordered table-sm table-striped">
              <THEAD><TR>
                <TH CLASS="text-center">Read Only</TH>
                <TH CLASS="text-center">Modify</TH>
                <TH>User</TH>
                <TH>Organization</TH>
              </TR></THEAD>
END_HTML


  #build up a hash of all R and RW users for this data source, keyed by user ID
  #NB: We're doing this here instead of calling ds_rights to avoid making a
  #    bazillion database queries for sites with lots of users
  $query = "SELECT Rusers, RWusers FROM dataSources WHERE ID=$dsID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($Rusers, $RWusers) = $dbOutput->fetchrow_array;

  @rusers = split(',', $Rusers);
  @rwusers = split(',', $RWusers);
  foreach $ruser (@rusers)
  {
    $userRights{$ruser} = 'R';
  }
  foreach $rwuser (@rwusers)
  {
    $userRights{$rwuser} = 'W';
  }

  #get a hash of all organization names for display purposes
  %orgNames = utils_get_org_hash($db);

  #build a list of org IDs, sorted by name, for display ordering
  foreach $userOrgID (sort {$orgNames{$a} cmp $orgNames{$b} } keys %orgNames)
  {
    $orgIDsString .= "'$userOrgID',";
  }
  chop($orgIDsString);

  #get a list of all users in the private cloud, and display
  if (($Lib::KoalaConfig::cloudtype eq "multi") && ($acctType < 5))
  {
    $query = "SELECT ID, first, last, orgID FROM users \
        WHERE orgID=$orgID AND acctType > 0 ORDER BY last";
  }
  else
  {
    $query = "SELECT ID, first, last, orgID FROM users \
        WHERE acctType > 0 ORDER BY FIELD(orgID, $orgIDsString), last";
  }
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  while (($id, $userFirst, $userLast, $userOrg) = $dbOutput->fetchrow_array)
  {
    if ($userRights{$id} eq "R")
    {
      $checked = "CHECKED";
    }
    else
    {
      $checked = "";
    }

    print <<END_HTML;
              <TR>
                <TD CLASS="text-center">
                  <DIV CLASS="form-check form-check-inline">
                    <INPUT CLASS="form-check-input" NAME="R $id" ID="R_$id" TYPE="checkbox" $checked>
                    <LABEL CLASS="form-check-label" FOR="R_$id">&nbsp;</LABEL>
                  </DIV>
                </TD>
END_HTML

    if ($userRights{$id} eq "W")
    {
      $checked = "CHECKED";
    }
    else
    {
      $checked = "";
    }

    print <<END_HTML;
                <TD CLASS="text-center">
                  <DIV CLASS="form-check form-check-inline">
                    <INPUT CLASS="form-check-input" NAME="W $id" ID="W_$id" TYPE="checkbox" $checked>
                    <LABEL CLASS="form-check-label" FOR="W_$id">&nbsp;</LABEL>
                  </DIV>
                </TD>
                <TD>$userFirst $userLast</TD>
                <TD>$orgNames{$userOrg}</TD>
              </TR>
END_HTML
  }

  print <<END_HTML;
            </TABLE>
          </DIV>

          <P>

          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="location.href='$refLink'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-submit"><I CLASS="bi bi-save"></I></SPAN> Save</BUTTON>
          </DIV>

        </DIV>
      </DIV>

      </FORM>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
<P>
END_HTML

  print_html_footer();

#EOF
