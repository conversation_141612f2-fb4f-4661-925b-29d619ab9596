#!/usr/bin/perl

use lib "/opt/apache/app/";

use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;


#
# Output basic stats on the number of cloud-backed report views per data
# source for a specific org.
#

  #connect to the database
  $db = KAPutil_connect_to_database();

  #get a list of all users that belong to the specified org
  $query = "SELECT ID FROM app.users WHERE orgID=1";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  while (($userID) = $dbOutput->fetchrow_array)
  {
    $userIDstr .= "$userID,";
  }
  chop($userIDstr);

  #build a hash of all data sources that below to the specified org
  $query = "SELECT ID, name FROM app.dataSources WHERE userID IN ($userIDstr)";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  while (($dsID, $dsName) = $dbOutput->fetchrow_array)
  {
    $dsNameHash{$dsID} = $dsName;
  }

  #cycle through each DS, getting reports and cloud views
  foreach $dsID (keys %dsNameHash)
  {
    $cloudBackedViews = 0;
    $rptIDstr = "";

    $query = "SELECT ID FROM app.cubes WHERE dsID=$dsID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    while (($rptID) = $dbOutput->fetchrow_array)
    {
      $rptIDstr .= "$rptID,";
    }
    chop($rptIDstr);

    if (length($rptIDstr) > 0)
    {
      $query = "SELECT SUM(viewsCloudBacked) FROM audit.stats_cubes \
          WHERE year=2021 AND month=4 AND cubeID IN ($rptIDstr)";
      $dbOutput = $db->prepare($query);
      $dbOutput->execute;
      ($cloudBackedViews) = $dbOutput->fetchrow_array;
      if (!defined($cloudBackedViews))
      {
        $cloudBackedViews = 0;
      }
    }

    print("\"$dsNameHash{$dsID}\",$cloudBackedViews\n");
  }

#EOF
