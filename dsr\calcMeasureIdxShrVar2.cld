#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: $action Calculated Measure</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Please Wait...');
  return(true);
}



function flipSegHier()
{
  let segHier = hierID.value;
  let idArrName = 'seg_' + segHier;
  let nameArrName = 'segname_' + segHier;

  nameArray = window[nameArrName];
  idArray = window[idArrName];

  //empty out current values
  let i = hierLevel.options.length;
  while (i--)
  {
    hierLevel.remove(i);
  }

  for (i = 0; i < idArray.length; i++)
  {
    let opt = document.createElement('option');
    opt.text = nameArray[i];
    opt.value = idArray[i];
    hierLevel.add(opt);
  }
}

</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  $dsName = ds_id_to_name($db, $dsID);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">$action Calculated Measure $measName</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');
  $measName = $q->param('measName');
  $measType = $q->param('measType');
  $calcBeforeAgg = $q->param('calcBeforeAgg');
  $dim = $q->param('dim');
  $baseMeasureID = $q->param('measure');
  $measureID = $q->param('measID');
  $calculation = $q->param('calculation');

  #sanitize parameters
  $dsID = utils_sanitize_integer($dsID);
  if (!defined($dsID))
  {
    exit_early_error($session, "Invalid data source");
  }
  $measureID = utils_sanitize_integer($measureID);

  #connect to the database
  $db = KAPutil_connect_to_database();

  if ($measureID > 0)
  {
    $action = "Edit";
  }
  else
  {
    $action = "New";
  }

  print_html_header();

  #also make sure we have write privs for the data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to modify the data source.");
  }

  $dsSchema = "datasource_" . $dsID;

  #if we're editing an existing measure, grab the info we need
  if ($measureID > 0)
  {
    $calculation =~ m/.*?\|(.*?)\|(.*?)\|(.*?)\|(.*?)\|/;
    $structType = $3;
    $structID = $4;

    #set up the data we need to display the current struct selections
    if ($structType eq "item")
    {
      $itemValue = $structID;
    }
    elsif ($structType eq "agg")
    {
      $aggChecked = "CHECKED";
      $aggValue = $structID;
    }
    elsif ($structType eq "seg")
    {
      $segChecked = "CHECKED";
      $segValue = $structID;
    }
    elsif ($structType eq "hier")
    {
      $hierChecked = "CHECKED";
      $structID =~ m/(\d+)_(\d+)/;
      $hierValue = $1;
      $hierLevel = $2;
    }
    else
    {
      $itemChecked = "CHECKED";   #default
    }
  }

  #set up the text depending on what type of measure the user is defining
  if ($measType eq "share")
  {
    $UImeasType = "Share";
    $UItext = "The value of this calculated share measure will be an item's share of:";
  }
  elsif ($measType eq "index")
  {
    $UImeasType = "Index";
    $UItext = "Index items against:";
  }


  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-12 col-lg-10 col-xl-8"> <!-- content -->

      <FORM METHOD="post" ACTION="calcMeasureSave.cld" onsubmit="return checkForm(this);">
      <INPUT TYPE="hidden" NAME="ds" VALUE="$dsID">
      <INPUT TYPE="hidden" NAME="measName" VALUE="$measName">
      <INPUT TYPE="hidden" NAME="measType" VALUE="$measType">
      <INPUT TYPE="hidden" NAME="calcBeforeAgg" VALUE="$calcBeforeAgg">
      <INPUT TYPE="hidden" NAME="dim" VALUE="$dim">
      <INPUT TYPE="hidden" NAME="measure" VALUE="$baseMeasureID">
      <INPUT TYPE="hidden" NAME="measureID" VALUE="$measureID">

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Calculated $UImeasType Measure</DIV>
        <DIV CLASS="card-body">

          $UItext
          <P>
          <DIV CLASS="row mb-3">
            <DIV CLASS="col-auto gx-1 ms-2">
              <DIV CLASS="form-check mt-1">
                <INPUT CLASS="form-check-input" TYPE="radio" NAME="struct" ID="item" VALUE="item" $itemChecked>
                <LABEL CLASS="form-check-label" FOR="item">Another item:</LABEL>
              </DIV>
            </DIV>
            <DIV CLASS="col-8 gx-1">
              <SELECT CLASS="form-select" NAME="itemID" ID="itemID" VALUE="$itemValue">
END_HTML

  #get hash of base items and their IDs
  %itemNameHash = dsr_get_base_item_name_hash($db, $dsSchema, $dim);

  foreach $itemID (sort {$itemNameHash{$a} cmp $itemNameHash{$b}} keys %itemNameHash)
  {
    print("  <OPTION VALUE=$itemID>$itemNameHash{$itemID}</OPTION>\n");
  }

  print <<END_HTML;
              </SELECT>
              <SCRIPT>
                \$('select#itemID').val('$itemValue');
              </SCRIPT>
            </DIV>
          </DIV>

          <DIV CLASS="row mb-3">
            <DIV CLASS="col-auto gx-1">
              <DIV CLASS="form-check mt-1 ms-2">
                <INPUT CLASS="form-check-input" TYPE="radio" NAME="struct" ID="agg" VALUE="agg" $aggChecked>
                <LABEL CLASS="form-check-label" FOR="agg">An aggregate:</LABEL>
              </DIV>
            </DIV>
            <DIV CLASS="col-auto gx-1">
              <SELECT CLASS="form-select" NAME="aggID" ID="aggID" STYLE="width: auto;">
END_HTML

  #get hash of aggregate names and IDs
  %aggNameHash = DSRagg_get_aggregates_hash($db, $dsSchema, $dim);

  foreach $aggID (sort {$aggNameHash{$a} cmp $aggNameHash{$b}} keys %aggNameHash)
  {
    print("  <OPTION VALUE=$aggID>$aggNameHash{$aggID}</OPTION>\n");
  }

  print <<END_HTML;
              </SELECT>
              <SCRIPT>
                \$('select#aggID').val('$aggValue');
              </SCRIPT>
            </DIV>
          </DIV>

          <DIV CLASS="row mb-3">
            <DIV CLASS="col-auto gx-1 ms-2">
              <DIV CLASS="form-check mt-1">
                <INPUT CLASS="form-check-input" TYPE="radio" NAME="struct" VALUE="seg" ID="seg" $segChecked>
                <LABEL CLASS="form-check-label" FOR="seg">A segmentation:</LABEL>
              </DIV>
            </DIV>
            <DIV CLASS="col-auto gx-1">
              <SELECT CLASS="form-select" NAME="segID" STYLE="width:auto;" ID="segID">
END_HTML

  #get hash of segmentation names and IDs
  %segNameHash = DSRsegmentation_get_segmentations_hash($db, $dsSchema, $dim);

  foreach $segID (sort {$segNameHash{$a} cmp $segNameHash{$b}} keys %segNameHash)
  {
    print(" <OPTION VALUE=$segID>$segNameHash{$segID}</OPTION>\n");
  }

  print <<END_HTML;
              </SELECT>
              <SCRIPT>
                \$('select#segID').val('$segValue');
              </SCRIPT>
            </DIV>
          </DIV>

          <DIV CLASS="row mb-3">
            <DIV CLASS="col-auto gx-1 ms-2">
              <DIV CLASS="form-check mt-1">
                <INPUT CLASS="form-check-input" TYPE="radio" NAME="struct" ID="hier" VALUE="hier" $hierChecked>
                <LABEL CLASS="form-check-label" FOR="hier">A hierarchy:</LABEL>
              </DIV>
            </DIV>
            <DIV CLASS="col-auto gx-1">
              <SELECT CLASS="form-select" NAME="hierID" jsID="hierID" id="hierID" STYLE="width:auto;" onChange="flipSegHier();">
END_HTML

  %segNameHash = DSRsegmentation_get_segmentations_hash($db, $dsSchema, "p");

  $query = "SELECT ID, name, segmentations FROM $dsSchema.product_seghierarchy \
      ORDER BY name";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  #set the default hierarchy to the first one if we don't already have one
  if ($hierValue < 1)
  {
    $hierValue = $id;
  }

  $jsHierIDs = "";
  $jsHierNames = "";
  while (($id, $name, $segmentations) = $dbOutput->fetchrow_array)
  {
    print(" <OPTION VALUE=$id>$name</OPTION>\n");

    #add the IDs to the JS array script code
    $jsHierIDs .= "seg_$id = [";
    $jsHierNames .= "segname_$id = [";
    @tmp = split(',', $segmentations);
    foreach $segID (@tmp)
    {
      $jsHierIDs .= "\"$segID\",";
      $jsHierNames .= "\"$segNameHash{$segID}\",";
    }
    chop($jsHierIDs);
    chop($jsHierNames);
    $jsHierIDs .= "];\n";
    $jsHierNames .= "];\n";
  }

  print <<END_HTML;
              </SELECT>
              <SCRIPT>
                $jsHierIDs

                $jsHierNames
              </SCRIPT>
            </DIV>
            <DIV CLASS="col-auto mt-1 gx-1">
              at the
            </DIV>
            <DIV CLASS="col-auto gx-1">
              <SELECT CLASS="form-select" NAME="hierLevel" jsID="hierLevel" STYLE="width:auto;" id="hierLevel">
END_HTML

  @segLevels = DSRseghier_get_segs_array($db, $dsSchema, $hierValue);
  foreach $segLevel (@segLevels)
  {
    print(" <OPTION VALUE=$segLevel>$segNameHash{$segLevel}</OPTION>\n");
  }

  print <<END_HTML;
              </SELECT>
            </DIV>
            <DIV CLASS="col-auto mt-1 gx-1">
              level
            </DIV>
            <SCRIPT>
              \$('select#hierID').val('$hierValue');
              \$('select#hierLevel').val('$hierLevel');
            </SCRIPT>
          </DIV>

          <P>&nbsp;<P>

          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onclick="location.href='display.cld?ds=$dsID&dim=m'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-submit">Next <I CLASS="bi bi-arrow-right"></I></BUTTON>
          </DIV>

        </DIV>
      </DIV>

      </FORM>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
