#!/usr/bin/perl

use lib "/opt/apache/app/";

use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::ODBC;
use Lib::Social;
use Lib::WebUtils;


my $debug;



#-------------------------------------------------------------------------
#
# Output debug data, if enabled
#

sub DBG
{
  my ($date);

  my ($str) = @_;


  if ($debug == 1)
  {
    $date = localtime();
    print STDERR "$date: $str\n";
  }
}



#-------------------------------------------------------------------------

  #determine if we're being run manually
  $debug = 0;
  $dsID = 0;
  if ($ARGV[0] eq "debug")
  {
    $debug = 1;
  }
  elsif ($ARGV[0] =~ m/^\d\d+$/)
  {
    $debug = 1;
    $dsID = $ARGV[0];
  }

  #redirect STDERR to the Koala error log
  if ($debug == 0)
  {
    close(STDERR);
    open(STDERR, ">>/opt/apache/htdocs/tmp/koala_error.log");
    select(STDERR);
    $| = 1;
  }

  #connect to the master database
  $db = KAPutil_connect_to_database();

  #clear any dead ODBC processes
  $query = "SELECT PID FROM app.jobs WHERE operation='ODBC'";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  while (($pid) = $dbOutput->fetchrow_array)
  {
    if (! -e "/proc/$pid")
    {
      DBG("Clearing dead ODBC process with PID $pid");

      $query = "DELETE FROM app.jobs WHERE PID=$pid AND operation='ODBC'";
      $db->do($query);
    }
  }


  #-----------------------------------
  #figure out how many ODBC export jobs we should try running - we always have
  #at least 1 ODBC job running (if there's anything to do), and up to half of
  #all available job slots

  #start by seeing how many non-ODBC jobs are running
  $query = "SELECT COUNT(*) FROM app.jobs WHERE operation != 'ODBC'";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($runningJobs) = $dbOutput->fetchrow_array;

  #calculate the number of available job slots (excluding running ODBC jobs)
  $availableSlots = $Lib::KoalaConfig::cores - $runningJobs;

  #up to half of the available job slots can be used for ODBC
  $availableODBCSlots = $availableSlots / 2;
  $availableODBCSlots = int($availableODBCSlots);   #logical floor()

  #make sure we have at least 1 slot, no matter what
  if ($availableODBCSlots < 1)
  {
    $availableODBCSlots = 1;
  }

  #subtract running ODBC jobs to see how many new ones we're going to run
  $query = "SELECT COUNT(*) FROM app.jobs where operation='ODBC'";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($runningODBCJobs) = $dbOutput->fetchrow_array;

  $maxProcs = $availableODBCSlots - $runningODBCJobs;

  if (($maxProcs < 1) && ($debug == 0))
  {
    DBG("ODBCupdate: System is already running $runningODBCJobs, usage $loadRatio, no available slots");
    exit;
  }

  #------------------------------------

  #grab every ODBC exported data source's ID and last update timestamp
  if ($dsID > 0)
  {
    $query = "SELECT ID, userID, UNIX_TIMESTAMP(lastUpdate), UNIX_TIMESTAMP(lastModified), ODBCexport, ODBCmanual, ODBCbaseItems, ODBCstatus \
        FROM dataSources WHERE ODBCexport > 0 AND ID=$dsID";
  }
  else
  {
    $query = "SELECT ID, userID, UNIX_TIMESTAMP(lastUpdate), UNIX_TIMESTAMP(lastModified), ODBCexport, ODBCmanual, ODBCbaseItems, ODBCstatus \
        FROM dataSources WHERE ODBCexport > 0 AND ODBCexported < lastModified AND ODBCexported < DATE_SUB(NOW(), INTERVAL 60 minute) ORDER BY ODBCexport DESC, lastModified ASC";
  }
  $ds_output = $db->prepare($query);
  $status = $ds_output->execute;
  KAPutil_handle_db_err($db, $status, $query);

  #have the system auto-reap any child processes we fork off
  $SIG{CHLD} = 'IGNORE';

  #cycle through data sources, and run the allowed number of ODBC export processes
  $runCount = 0;
  while (($dsID, $userID, $lastUpdated, $lastModified, $ODBCexport, $ODBCmanual, $ODBCbaseItems, $ODBCstatus) = $ds_output->fetchrow_array)
  {

    #if the data source is set for manual updating one hasn't been requested
    if (($ODBCmanual == 1) && ($ODBCstatus ne "QUEUE"))
    {
      #skip this data source and move on to the next
      next;
    }

    #make sure it's OK for us to update the export table for this datasource
    $ok = DSRutil_operation_ok($db, $dsID, 0, "ODBC");
    if ($ok != 1)
    {
      next;
    }

    #try to avoid stepping on a data source that's being updated by an
    #automated process running multiple updates (e.g., multiple data flows)
    #if user hasn't manually done anything since last DS update
    if ($lastModified == $lastUpdated)
    {

      #if the last update was done by an automated process
      $query = "SELECT userID FROM datasource_$dsID.update_history \
          ORDER BY ID DESC LIMIT 1";
      $dbOutput1 = $db->prepare($query);
      $dbOutput1->execute;
      ($lastUpdateUser) = $dbOutput1->fetchrow_array;
      if ($lastUpdateUser == 0)
      {

        #if the update was less than 6 hours ago, bail out for now
        $timeSinceUpdate = time() - $lastUpdated;
        $timeSinceUpdate = $timeSinceUpdate / 3600;
        if ($timeSinceUpdate < 6)
        {
          DBG("Skipping $dsID - was updated by an automated process only $timeSinceUpdate hours ago");
          next;
        }
      }
    }

    #split off a child process to run the ODBC export for the current DS
    if (($debug != 1) && ($pid = fork()))
    {

      #parent process - increment count and continue processing jobs
      $runCount++;
    }

    #else we're the child process
    else
    {

      if ($ODBCexport == 1)
      {
        ODBC_export_tabular($dsID, $userID);
      }
      elsif ($ODBCexport == 2)
      {
        ODBC_export_star($dsID, $userID);
      }

      exit;
    }

    #keep us from exceeding our allowable run count
    if ($runCount >= $maxProcs)
    {
      exit;
    }
  }



#EOF
