#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepSources;
use Lib::PrepUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($kapDB);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Data Flow Schedule Saved</TITLE>
<LINK REL="SHORTCUT ICON" HREF="$Lib::KoalaConfig::kapHostURL/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($kapDB, $userID, $first, $last, $orgName);

  $flowName = prep_flow_id_to_name($prepDB, $flowID);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="$Lib::KoalaConfig::kapHostURL/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?f=$flowID">Data Prep</A></LI>
    <LI CLASS="breadcrumb-item">$flowName</LI>
    <LI CLASS="breadcrumb-item active">Schedule</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #get CGI parameters
  $flowID = $q->param('f');
  $sched = $q->param('sched');

  $monthlyFreq = $q->param('monthly-freq');

  $weeklyFreq = $q->param('weekly-freq');
  $weeklyDay = $q->param('weekly-day');

  $dailyFreq = $q->param('daily-freq');

  print_html_header();

  #make sure we have write privs for this data flow
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to modify this data flow.");
  }

  #if the analyst wants us to run on a monthly schedule...
  if ($sched eq "monthly")
  {
    $details = "$monthlyFreq";
  }

  #if the analyst wants us to run on a weekly schedule...
  if ($sched eq "weekly")
  {
    $details = "$weeklyFreq $weeklyDay";
  }

  #if the analyst wants us to run on a daily schedule...
  if ($sched eq "daily")
  {
    $details = $dailyFreq;
  }

  #if the flow is not scheduled, remove any existing schedule
  if ($sched eq "never")
  {
    $query = "DELETE FROM prep.schedule WHERE flowID=$flowID";
    $prepDB->do($query);
  }

  #if we are scheduled, get size & date of current source file and store updated
  #settings
  else
  {
    $query = "SELECT source FROM prep.flows WHERE ID=$flowID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($source) = $dbOutput->fetchrow_array;
    if ($source eq "Web")
    {
      ($fileSize, $fileDate) = prep_source_web_newdata($prepDB, $flowID);
    }
    elsif ($source eq "FTP")
    {
      ($fileSize, $fileDate) = prep_source_ftp_newdata($prepDB, $flowID);
    }
    elsif ($source eq "Koala")
    {
      ($fileSize, $fileDate) = prep_source_koala_newdata($kapDB, $prepDB, $flowID);
    }

    if ($fileSize > 0)
    {
      $q_size = $prepDB->quote($fileSize);
      $q_date = $prepDB->quote($fileDate);
    }
    else
    {
      $q_size = "NULL";
      $q_date = "NULL";
    }

    #update schedule settings for this flow
    $query = "INSERT INTO prep.schedule (flowID, sched, details, lastFileSize, lastFileDate) \
        VALUES ($flowID, '$sched', '$details', $q_size, $q_date) \
        ON DUPLICATE KEY UPDATE sched='$sched', details='$details'";
    $prepDB->do($query);
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-10 col-lg-7 col-xl-6"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Schedule Saved</DIV>
        <DIV CLASS="card-body">

          <P>
          Your schedule to run the data flow <B>$dsName</B> has been saved.

          <P>&nbsp;<P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" onClick="location.href='main.cld?f=$flowID'"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
          </DIV>

        </DIV>
      </DIV>
    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

  prep_audit($prepDB, $userID, "Changed scheduling to $sched", $flowID);
  utils_slack("PREP: $first $last changed scheduling to $sched for $flowName");


#EOF
