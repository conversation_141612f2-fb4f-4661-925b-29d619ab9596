#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::Reports;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Report Properties</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/rpt/main">Reports</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?rpt=$rptID">$name</A></LI>
    <LI CLASS="breadcrumb-item active">Properties</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $rptID = $q->param('c');
  $name = $q->param('name');
  $rptOwner = $q->param('rptOwner');
  $oldOwnerID = $q->param('oldOwnerID');

  $db = KAPutil_connect_to_database();

  $dsID = cube_get_ds_id($db, $rptID);

  print_html_header();

  #make sure we have write privs for this report
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to change this report's properties");
  }

  $dsSchema = "datasource_" . $dsID;

  utils_audit($db, $userID, "Edited report properties", $dsID, $rptID, 0);
  $activity = "$first $last edited properties of report $name";

  #update name
  $q_name = $db->quote($name);
  $query = "UPDATE cubes SET name=$q_name WHERE ID=$rptID";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);

  #if we're changing the data source's owner
  if (($rptOwner > 0) && ($rptOwner != $oldOwnerID))
  {

    #update the data source owner to the new selection
    $query = "UPDATE cubes SET userID=$rptOwner WHERE ID=$rptID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    #grant the original DS owner read/write privs on the data source
    $query = "SELECT RWusers FROM cubes WHERE ID=$rptID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($rwusers) = $dbOutput->fetchrow_array;

    if (length($rwusers) > 0)
    {
      $rwusers .= ",";
    }
    $rwusers .= $userID;

    $query = "UPDATE cubes SET RWusers='$rwusers' WHERE ID=$rptID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    utils_audit($db, $userID, "Changed report ownership", $dsID, $rptID, 0);
    $activity = "$activity\n$first $last changed ownership of report $name";
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-10 col-lg-7 col-xl-6"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Report Properties</DIV>
        <DIV CLASS="card-body">

          <P>
          Your changes to the $name report have been saved.

          <P>&nbsp;<P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='display.cld?rpt=$rptID'"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->

END_HTML

  print_html_footer();
  utils_slack($activity);

#EOF
