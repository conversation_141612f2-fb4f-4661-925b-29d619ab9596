#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{

 $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: New Data Source</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function toggleCheckboxes(source)
{
  let state = source.checked;

  \$('.cbAll').prop('checked', state);
}


function checkForm(form)
{
  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Please Wait...');
  return(true);
}
</SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item active">New Data Source</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  $key = $q->param("key");

  #output Content-type header
  print($session->header());

  #determine if any XLSX files were uploaded by the user for us to import,
  #create an array of them if so
  $xlsxTabs = 0;
  undef(@xlsxFiles);
  opendir(DIRHANDLE, "/opt/apache/app/tmp");
  while (defined($filename = readdir(DIRHANDLE)))
  {
    if ($filename =~ m/^$userID\.$key\..*\.xlsx$/i)
    {
      $xlsxTabs = 1;
      push(@xlsxFiles, $filename);
    }
  }

  #if no XLSX files were uploaded, skip on to next step in data import
  if ($xlsxTabs != 1)
  {
    print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META HTTP-EQUIV="refresh" CONTENT="0; URL=newDSlayout.cld?key=$key">
</HEAD>
<BODY>
</BODY>
</HTML>
END_HTML
    exit;
  }

  #connect to user login database
  $db = KAPutil_connect_to_database();

  print_html_header();

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-10 col-lg-7 col-xl-6"> <!-- content -->

      <FORM METHOD="post" ACTION="/app/dsr/newDSlayout.cld" onsubmit="return checkForm(this);">
      <INPUT TYPE="hidden" NAME="key" VALUE="$key">

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Excel File Tabs</DIV>
        <DIV CLASS="card-body">

          Choose the Excel worksheets you wish to import into the data source:

          <P>
          <DIV CLASS="form-check">
            <INPUT CLASS="form-check-input" TYPE='checkbox' ID="checkAll" onclick="toggleCheckboxes(this)">
            <LABEL CLASS="form-check-label" FOR="checkAll"><B>All</B></LABEL>
          </DIV>
END_HTML

  foreach $filename (@xlsxFiles)
  {

    #trim our ID info off the filename for display purposes
    $filename =~ m/\d+\.\d+\.(.*)/;
    print(" <P>&nbsp;</P>\n");
    print(" <B>$1</B><BR>\n");

    #extract the XML file containing list of tabs from the XLSX
    $workbookXML = `unzip -p \"/opt/apache/app/tmp/$filename\" \"xl/workbook.xml\"`;

    #iterate over the sheets in the XLSX
    $first = 1;
    while ($workbookXML =~ m/<sheet (.*?)\/>/g)
    {
      $tmp = $1;

      #extract sheet name
      $tmp =~ m/name=\"(.*?)\"/;
      $sheetName = $1;

      #extract sheet ID
      $tmp =~ m/r:id=\"rId(\d+)\"/;
      $sheetID = $1;

      #don't display IRI's internal app info sheet
      if ($sheetName =~ m/IRI_WorkspaceStorage/)
      {
        next;
      }
      if ($sheetName eq "Cognos_Office_Connection_Cache")
      {
        next;
      }

      #don't display Nielsen AOD's internal app info sheet
      if ($sheetName =~ m/ReportMeta/)
      {
        next;
      }
      if ($sheetName =~ m/TOC/)
      {
        next;
      }

      if ($first)
      {
        print <<END_HTML;
          <DIV CLASS="form-check">
            <INPUT TYPE='checkbox' CLASS="cbAll form-check-input" CHECKED NAME='xlsxTabs' ID="$filename-$sheetID" VALUE="TAB $filename $sheetID">
            <LABEL CLASS="form-check-label" FOR="$filename-$sheetID">$sheetName</LABEL>
          </DIV>
END_HTML
        $first = 0;
      }

      else
      {
        print <<END_HTML;
          <DIV CLASS="form-check">
            <INPUT TYPE='checkbox' CLASS="cbAll form-check-input" NAME='xlsxTabs' ID="$filename-$sheetID" VALUE="TAB $filename $sheetID">
            <LABEL CLASS="form-check-label" FOR="$filename-$sheetID">$sheetName</LABEL>
          </DIV>
END_HTML
      }
    }
  }

  print <<END_HTML;
          <P>&nbsp;<P>

          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="location.href='/app/dsr/main.cld'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-submit">Next <I CLASS="bi bi-arrow-right"></I></BUTTON>
          </DIV>

        </DIV>
      </DIV>

      </FORM>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
