
package Lib::GeoCode;

use Exporter;

use lib "/opt/apache/app";


our @ISA = ('Exporter');

our @EXPORT = qw(
    &GeoCode_countries
    &GeoCode_states
    &GeoCode_usaregions
    &GeoCode_region
    &GeoCode_dma);



#-------------------------------------------------------------------------------
#
# Try to figure out which country a geography name applies to, and return
# the correct FusionMaps code.
#

sub GeoCode_countries
{
  my ($geoID);

  my ($geoName) = @_;


  if (($geoName =~ m/Canada/i) || ($geoName =~ m/(^|\s|\,)CA($|\s|\,)/i))
  {
    $geoID = "005";
  }
  elsif (($geoName =~ m/Greenland/i) || ($geoName =~ m/(^|\s|\,)GL($|\s|\,)/i))
  {
    $geoID = "024";
  }
  elsif (($geoName =~ m/Mexico/i) || ($geoName =~ m/(^|\s|\,)MX($|\s|\,)/i))
  {
    $geoID = "016";
  }
  elsif (($geoName =~ m/Unites States/i) ||
         ($geoName =~ m/Unites States of America/i) ||
         ($geoName =~ m/(^|\s|\,)USA($|\s|\,)/i) ||
         ($geoName =~ m/(^|\s|\,)US($|\s|\,)/i))
  {
    $geoID = "023";
  }

  #fall-through case
  else
  {
    $geoID = "";
  }

  return($geoID);
}



#-------------------------------------------------------------------------------
#
# Try to figure out which US state a geography name applies to, and return
# the correct FusionMaps code.
#

sub GeoCode_states
{
  my ($geoID);

  my ($geoName) = @_;


  #start by looking for full names of states in geo name
  if ($geoName =~ m/Alabama/i)
  {
    $geoID = "AL";
  }
  elsif ($geoName =~ m/Alaska/i)
  {
    $geoID = "AK";
  }
  elsif ($geoName =~ m/Arizona/i)
  {
    $geoID = "AZ";
  }
  elsif ($geoName =~ m/Arkansas/i)
  {
    $geoID = "AR";
  }
  elsif ($geoName =~ m/California/i)
  {
    $geoID = "CA";
  }
  elsif ($geoName =~ m/Colorado/i)
  {
    $geoID = "CO";
  }
  elsif ($geoName =~ m/Connecticut/i)
  {
    $geoID = "CT";
  }
  elsif ($geoName =~ m/Delaware/i)
  {
    $geoID = "DE";
  }
  elsif (($geoName =~ m/District of Columbia/i) ||
         ($geoName =~ m/Washington.*?DC/i))
  {
    $geoID = "DC";
  }
  elsif ($geoName =~ m/Florida/i)
  {
    $geoID = "FL";
  }
  elsif ($geoName =~ m/Georgia/i)
  {
    $geoID = "GA";
  }
  elsif ($geoName =~ m/Hawaii/i)
  {
    $geoID = "HI";
  }
  elsif ($geoName =~ m/Idaho/i)
  {
    $geoID = "ID";
  }
  elsif ($geoName =~ m/Illinois/i)
  {
    $geoID = "IL";
  }
  elsif ($geoName =~ m/Indiana/i)
  {
    $geoID = "IN";
  }
  elsif ($geoName =~ m/Iowa/i)
  {
    $geoID = "IA";
  }
  elsif ($geoName =~ m/Kansas/i)
  {
    $geoID = "KS";
  }
  elsif ($geoName =~ m/Kentucky/i)
  {
    $geoID = "KY";
  }
  elsif ($geoName =~ m/Louisiana/i)
  {
    $geoID = "LA";
  }
  elsif ($geoName =~ m/Maine/i)
  {
    $geoID = "ME";
  }
  elsif ($geoName =~ m/Maryland/i)
  {
    $geoID = "MD";
  }
  elsif ($geoName =~ m/Massachusetts/i)
  {
    $geoID = "MA";
  }
  elsif ($geoName =~ m/Michigan/i)
  {
    $geoID = "MI";
  }
  elsif ($geoName =~ m/Minnesota/i)
  {
    $geoID = "MN";
  }
  elsif ($geoName =~ m/Mississippi/i)
  {
    $geoID = "MS";
  }
  elsif ($geoName =~ m/Missouri/i)
  {
    $geoID = "MO";
  }
  elsif ($geoName =~ m/Montana/i)
  {
    $geoID = "MT";
  }
  elsif ($geoName =~ m/Nebraska/i)
  {
    $geoID = "NE";
  }
  elsif ($geoName =~ m/Nevada/i)
  {
    $geoID = "NV";
  }
  elsif ($geoName =~ m/New Hampshire/i)
  {
    $geoID = "NH";
  }
  elsif ($geoName =~ m/New Jersey/i)
  {
    $geoID = "NJ";
  }
  elsif ($geoName =~ m/New Mexico/i)
  {
    $geoID = "NM";
  }
  elsif ($geoName =~ m/New York/i)
  {
    $geoID = "NY";
  }
  elsif ($geoName =~ m/North Carolina/i)
  {
    $geoID = "NC";
  }
  elsif ($geoName =~ m/North Dakota/i)
  {
    $geoID = "ND";
  }
  elsif ($geoName =~ m/Ohio/i)
  {
    $geoID = "OH";
  }
  elsif ($geoName =~ m/Oklahoma/i)
  {
    $geoID = "OK";
  }
  elsif ($geoName =~ m/Oregon/i)
  {
    $geoID = "OR";
  }
  elsif ($geoName =~ m/Pennsylvania/i)
  {
    $geoID = "PA";
  }
  elsif ($geoName =~ m/Rhode Island/i)
  {
    $geoID = "RI";
  }
  elsif ($geoName =~ m/South Carolina/i)
  {
    $geoID = "SC";
  }
  elsif ($geoName =~ m/South Dakota/i)
  {
    $geoID = "SD";
  }
  elsif ($geoName =~ m/Tennessee/i)
  {
    $geoID = "TN";
  }
  elsif ($geoName =~ m/Texas/i)
  {
    $geoID = "TX";
  }
  elsif ($geoName =~ m/Utah/i)
  {
    $geoID = "UT";
  }
  elsif ($geoName =~ m/Vermont/i)
  {
    $geoID = "VT";
  }
  elsif ($geoName =~ m/Virginia/i)
  {
    $geoID = "VA";
  }
  elsif ($geoName =~ m/Washington/i)
  {
    $geoID = "WA";
  }
  elsif ($geoName =~ m/West Virginia/i)
  {
    $geoID = "WV";
  }
  elsif ($geoName =~ m/Wisconsin/i)
  {
    $geoID = "WI";
  }
  elsif ($geoName =~ m/Wyoming/i)
  {
    $geoID = "WY";
  }

  #now look for 2-letter postal codes
  elsif ($geoName =~ m/(^|\s|\,)AL($|\s|\,)/i)
  {
    $geoID = "AL";
  }
  elsif ($geoName =~ m/(^|\s|\,)AK($|\s|\,)/i)
  {
    $geoID = "AK";
  }
  elsif ($geoName =~ m/(^|\s|\,)AZ($|\s|\,)/i)
  {
    $geoID = "AZ";
  }
  elsif ($geoName =~ m/(^|\s|\,)AR($|\s|\,)/i)
  {
    $geoID = "AR";
  }
  elsif ($geoName =~ m/(^|\s|\,)CA($|\s|\,)/i)
  {
    $geoID = "CA";
  }
  elsif ($geoName =~ m/(^|\s|\,)CO($|\s|\,)/i)
  {
    $geoID = "CO";
  }
  elsif ($geoName =~ m/(^|\s|\,)CT($|\s|\,)/i)
  {
    $geoID = "CT";
  }
  elsif ($geoName =~ m/(^|\s|\,)DE($|\s|\,)/i)
  {
    $geoID = "DE";
  }
  elsif ($geoName =~ m/(^|\s|\,)DC($|\s|\,)/i)
  {
    $geoID = "DC";
  }
  elsif ($geoName =~ m/(^|\s|\,)FL($|\s|\,)/i)
  {
    $geoID = "FL";
  }
  elsif ($geoName =~ m/(^|\s|\,)GA($|\s|\,)/i)
  {
    $geoID = "GA";
  }
  elsif ($geoName =~ m/(^|\s|\,)HI($|\s|\,)/i)
  {
    $geoID = "HI";
  }
  elsif ($geoName =~ m/(^|\s|\,)ID($|\s|\,)/i)
  {
    $geoID = "ID";
  }
  elsif ($geoName =~ m/(^|\s|\,)IL($|\s|\,)/i)
  {
    $geoID = "IL";
  }
  elsif ($geoName =~ m/(^|\s|\,)IN($|\s|\,)/i)
  {
    $geoID = "IN";
  }
  elsif ($geoName =~ m/(^|\s|\,)IA($|\s|\,)/i)
  {
    $geoID = "IA";
  }
  elsif ($geoName =~ m/(^|\s|\,)KS($|\s|\,)/i)
  {
    $geoID = "KS";
  }
  elsif ($geoName =~ m/(^|\s|\,)KY($|\s|\,)/i)
  {
    $geoID = "KY";
  }
  elsif ($geoName =~ m/(^|\s|\,)LA($|\s|\,)/i)
  {
    $geoID = "LA";
  }
  elsif ($geoName =~ m/(^|\s|\,)ME($|\s|\,)/i)
  {
    $geoID = "ME";
  }
  elsif ($geoName =~ m/(^|\s|\,)MD($|\s|\,)/i)
  {
    $geoID = "MD";
  }
  elsif ($geoName =~ m/(^|\s|\,)MA($|\s|\,)/i)
  {
    $geoID = "MA";
  }
  elsif ($geoName =~ m/(^|\s|\,)MI($|\s|\,)/i)
  {
    $geoID = "MI";
  }
  elsif ($geoName =~ m/(^|\s|\,)MN($|\s|\,)/i)
  {
    $geoID = "MN";
  }
  elsif ($geoName =~ m/(^|\s|\,)MS($|\s|\,)/i)
  {
    $geoID = "MS";
  }
  elsif ($geoName =~ m/(^|\s|\,)MO($|\s|\,)/i)
  {
    $geoID = "MO";
  }
  elsif ($geoName =~ m/(^|\s|\,)MT($|\s|\,)/i)
  {
    $geoID = "MT";
  }
  elsif ($geoName =~ m/(^|\s|\,)NE($|\s|\,)/i)
  {
    $geoID = "NE";
  }
  elsif ($geoName =~ m/(^|\s|\,)NV($|\s|\,)/i)
  {
    $geoID = "NV";
  }
  elsif ($geoName =~ m/(^|\s|\,)NH($|\s|\,)/i)
  {
    $geoID = "NH";
  }
  elsif ($geoName =~ m/(^|\s|\,)NJ($|\s|\,)/i)
  {
    $geoID = "NJ";
  }
  elsif ($geoName =~ m/(^|\s|\,)NM($|\s|\,)/i)
  {
    $geoID = "NM";
  }
  elsif ($geoName =~ m/(^|\s|\,)NY($|\s|\,)/i)
  {
    $geoID = "NY";
  }
  elsif ($geoName =~ m/(^|\s|\,)NC($|\s|\,)/i)
  {
    $geoID = "NC";
  }
  elsif ($geoName =~ m/(^|\s|\,)ND($|\s|\,)/i)
  {
    $geoID = "ND";
  }
  elsif ($geoName =~ m/(^|\s|\,)OH($|\s|\,)/i)
  {
    $geoID = "OH";
  }
  elsif ($geoName =~ m/(^|\s|\,)OK($|\s|\,)/i)
  {
    $geoID = "OK";
  }
  elsif ($geoName =~ m/(^|\s|\,)OR($|\s|\,)/i)
  {
    $geoID = "OR";
  }
  elsif ($geoName =~ m/(^|\s|\,)PA($|\s|\,)/i)
  {
    $geoID = "PA";
  }
  elsif ($geoName =~ m/(^|\s|\,)RI($|\s|\,)/i)
  {
    $geoID = "RI";
  }
  elsif ($geoName =~ m/(^|\s|\,)SC($|\s|\,)/i)
  {
    $geoID = "SC";
  }
  elsif ($geoName =~ m/(^|\s|\,)SD($|\s|\,)/i)
  {
    $geoID = "SD";
  }
  elsif ($geoName =~ m/(^|\s|\,)TN($|\s|\,)/i)
  {
    $geoID = "TN";
  }
  elsif ($geoName =~ m/(^|\s|\,)TX($|\s|\,)/i)
  {
    $geoID = "TX";
  }
  elsif ($geoName =~ m/(^|\s|\,)UT($|\s|\,)/i)
  {
    $geoID = "UT";
  }
  elsif ($geoName =~ m/(^|\s|\,)VT($|\s|\,)/i)
  {
    $geoID = "VT";
  }
  elsif ($geoName =~ m/(^|\s|\,)VA($|\s|\,)/i)
  {
    $geoID = "VA";
  }
  elsif ($geoName =~ m/(^|\s|\,)WA($|\s|\,)/i)
  {
    $geoID = "WA";
  }
  elsif ($geoName =~ m/(^|\s|\,)WV($|\s|\,)/i)
  {
    $geoID = "WV";
  }
  elsif ($geoName =~ m/(^|\s|\,)WI($|\s|\,)/i)
  {
    $geoID = "WI";
  }
  elsif ($geoName =~ m/(^|\s|\,)WY($|\s|\,)/i)
  {
    $geoID = "WY";
  }

  #fall-through case: return an empty string to let caller know coding failed
  else
  {
    $geoID = "";
  }

  return($geoID);
}



#-------------------------------------------------------------------------------
#
# Try to figure out which US region a geography name applies to, and return
# the correct FusionMaps code.
#

sub GeoCode_usaregions
{
  my ($geoID);

  my ($geoName) = @_;


  if ($geoName =~ m/central/i)
  {
    $geoID = "05";
  }
  elsif (($geoName =~ m/northeast/i) || ($geoName =~ m/north east/i))
  {
    $geoID = "01";
  }
  elsif (($geoName =~ m/northwest/i) || ($geoName =~ m/north west/i))
  {
    $geoID = "02";
  }
  elsif (($geoName =~ m/southeast/i) || ($geoName =~ m/south east/i))
  {
    $geoID = "03";
  }
  elsif (($geoName =~ m/southwest/i) || ($geoName =~ m/south west/i))
  {
    $geoID = "04";
  }
  else
  {
    $geoID = "";
  }

  return($geoID);
}



#-------------------------------------------------------------------------------
#
# Try to figure out which state in a US region ageography name applies to, and
# return the correct FusionMaps code.
#

sub GeoCode_region
{
  my ($geoID);

  my ($geoName) = @_;


  #start by looking for full names of states in geo name
  if ($geoName =~ m/Alabama/i)
  {
    $geoID = "01";
  }
  elsif ($geoName =~ m/Alaska/i)
  {
    $geoID = "02";
  }
  elsif ($geoName =~ m/Arizona/i)
  {
    $geoID = "04";
  }
  elsif ($geoName =~ m/Arkansas/i)
  {
    $geoID = "05";
  }
  elsif ($geoName =~ m/California/i)
  {
    $geoID = "06";
  }
  elsif ($geoName =~ m/Colorado/i)
  {
    $geoID = "08";
  }
  elsif ($geoName =~ m/Connecticut/i)
  {
    $geoID = "09";
  }
  elsif ($geoName =~ m/Delaware/i)
  {
    $geoID = "10";
  }
  elsif (($geoName =~ m/District of Columbia/i) ||
         ($geoName =~ m/Washington.*?DC/i))
  {
    $geoID = "11";
  }
  elsif ($geoName =~ m/Florida/i)
  {
    $geoID = "12";
  }
  elsif ($geoName =~ m/Georgia/i)
  {
    $geoID = "13";
  }
  elsif ($geoName =~ m/Hawaii/i)
  {
    $geoID = "15";
  }
  elsif ($geoName =~ m/Idaho/i)
  {
    $geoID = "16";
  }
  elsif ($geoName =~ m/Illinois/i)
  {
    $geoID = "17";
  }
  elsif ($geoName =~ m/Indiana/i)
  {
    $geoID = "18";
  }
  elsif ($geoName =~ m/Iowa/i)
  {
    $geoID = "19";
  }
  elsif ($geoName =~ m/Kansas/i)
  {
    $geoID = "20";
  }
  elsif ($geoName =~ m/Kentucky/i)
  {
    $geoID = "21";
  }
  elsif ($geoName =~ m/Louisiana/i)
  {
    $geoID = "22";
  }
  elsif ($geoName =~ m/Maine/i)
  {
    $geoID = "23";
  }
  elsif ($geoName =~ m/Maryland/i)
  {
    $geoID = "24";
  }
  elsif ($geoName =~ m/Massachusetts/i)
  {
    $geoID = "25";
  }
  elsif ($geoName =~ m/Michigan/i)
  {
    $geoID = "26";
  }
  elsif ($geoName =~ m/Minnesota/i)
  {
    $geoID = "27";
  }
  elsif ($geoName =~ m/Mississippi/i)
  {
    $geoID = "28";
  }
  elsif ($geoName =~ m/Missouri/i)
  {
    $geoID = "29";
  }
  elsif ($geoName =~ m/Montana/i)
  {
    $geoID = "30";
  }
  elsif ($geoName =~ m/Nebraska/i)
  {
    $geoID = "31";
  }
  elsif ($geoName =~ m/Nevada/i)
  {
    $geoID = "32";
  }
  elsif ($geoName =~ m/New Hampshire/i)
  {
    $geoID = "33";
  }
  elsif ($geoName =~ m/New Jersey/i)
  {
    $geoID = "34";
  }
  elsif ($geoName =~ m/New Mexico/i)
  {
    $geoID = "35";
  }
  elsif ($geoName =~ m/New York/i)
  {
    $geoID = "36";
  }
  elsif ($geoName =~ m/North Carolina/i)
  {
    $geoID = "37";
  }
  elsif ($geoName =~ m/North Dakota/i)
  {
    $geoID = "38";
  }
  elsif ($geoName =~ m/Ohio/i)
  {
    $geoID = "39";
  }
  elsif ($geoName =~ m/Oklahoma/i)
  {
    $geoID = "40";
  }
  elsif ($geoName =~ m/Oregon/i)
  {
    $geoID = "41";
  }
  elsif ($geoName =~ m/Pennsylvania/i)
  {
    $geoID = "42";
  }
  elsif ($geoName =~ m/Rhode Island/i)
  {
    $geoID = "44";
  }
  elsif ($geoName =~ m/South Carolina/i)
  {
    $geoID = "45";
  }
  elsif ($geoName =~ m/South Dakota/i)
  {
    $geoID = "46";
  }
  elsif ($geoName =~ m/Tennessee/i)
  {
    $geoID = "47";
  }
  elsif ($geoName =~ m/Texas/i)
  {
    $geoID = "48";
  }
  elsif ($geoName =~ m/Utah/i)
  {
    $geoID = "49";
  }
  elsif ($geoName =~ m/Vermont/i)
  {
    $geoID = "50";
  }
  elsif ($geoName =~ m/Virginia/i)
  {
    $geoID = "51";
  }
  elsif ($geoName =~ m/Washington/i)
  {
    $geoID = "53";
  }
  elsif ($geoName =~ m/West Virginia/i)
  {
    $geoID = "54";
  }
  elsif ($geoName =~ m/Wisconsin/i)
  {
    $geoID = "55";
  }
  elsif ($geoName =~ m/Wyoming/i)
  {
    $geoID = "56";
  }

  #now look for 2-letter postal codes
  elsif ($geoName =~ m/(^|\s|\,)AL($|\s|\,)/i)
  {
    $geoID = "01";
  }
  elsif ($geoName =~ m/(^|\s|\,)AK($|\s|\,)/i)
  {
    $geoID = "02";
  }
  elsif ($geoName =~ m/(^|\s|\,)AZ($|\s|\,)/i)
  {
    $geoID = "04";
  }
  elsif ($geoName =~ m/(^|\s|\,)AR($|\s|\,)/i)
  {
    $geoID = "05";
  }
  elsif ($geoName =~ m/(^|\s|\,)CA($|\s|\,)/i)
  {
    $geoID = "06";
  }
  elsif ($geoName =~ m/(^|\s|\,)CO($|\s|\,)/i)
  {
    $geoID = "08";
  }
  elsif ($geoName =~ m/(^|\s|\,)CT($|\s|\,)/i)
  {
    $geoID = "09";
  }
  elsif ($geoName =~ m/(^|\s|\,)DE($|\s|\,)/i)
  {
    $geoID = "10";
  }
  elsif ($geoName =~ m/(^|\s|\,)DC($|\s|\,)/i)
  {
    $geoID = "11";
  }
  elsif ($geoName =~ m/(^|\s|\,)FL($|\s|\,)/i)
  {
    $geoID = "12";
  }
  elsif ($geoName =~ m/(^|\s|\,)GA($|\s|\,)/i)
  {
    $geoID = "13";
  }
  elsif ($geoName =~ m/(^|\s|\,)HI($|\s|\,)/i)
  {
    $geoID = "15";
  }
  elsif ($geoName =~ m/(^|\s|\,)ID($|\s|\,)/i)
  {
    $geoID = "16";
  }
  elsif ($geoName =~ m/(^|\s|\,)IL($|\s|\,)/i)
  {
    $geoID = "17";
  }
  elsif ($geoName =~ m/(^|\s|\,)IN($|\s|\,)/i)
  {
    $geoID = "18";
  }
  elsif ($geoName =~ m/(^|\s|\,)IA($|\s|\,)/i)
  {
    $geoID = "19";
  }
  elsif ($geoName =~ m/(^|\s|\,)KS($|\s|\,)/i)
  {
    $geoID = "20";
  }
  elsif ($geoName =~ m/(^|\s|\,)KY($|\s|\,)/i)
  {
    $geoID = "21";
  }
  elsif ($geoName =~ m/(^|\s|\,)LA($|\s|\,)/i)
  {
    $geoID = "22";
  }
  elsif ($geoName =~ m/(^|\s|\,)ME($|\s|\,)/i)
  {
    $geoID = "23";
  }
  elsif ($geoName =~ m/(^|\s|\,)MD($|\s|\,)/i)
  {
    $geoID = "24";
  }
  elsif ($geoName =~ m/(^|\s|\,)MA($|\s|\,)/i)
  {
    $geoID = "25";
  }
  elsif ($geoName =~ m/(^|\s|\,)MI($|\s|\,)/i)
  {
    $geoID = "26";
  }
  elsif ($geoName =~ m/(^|\s|\,)MN($|\s|\,)/i)
  {
    $geoID = "27";
  }
  elsif ($geoName =~ m/(^|\s|\,)MS($|\s|\,)/i)
  {
    $geoID = "28";
  }
  elsif ($geoName =~ m/(^|\s|\,)MO($|\s|\,)/i)
  {
    $geoID = "29";
  }
  elsif ($geoName =~ m/(^|\s|\,)MT($|\s|\,)/i)
  {
    $geoID = "30";
  }
  elsif ($geoName =~ m/(^|\s|\,)NE($|\s|\,)/i)
  {
    $geoID = "31";
  }
  elsif ($geoName =~ m/(^|\s|\,)NV($|\s|\,)/i)
  {
    $geoID = "32";
  }
  elsif ($geoName =~ m/(^|\s|\,)NH($|\s|\,)/i)
  {
    $geoID = "33";
  }
  elsif ($geoName =~ m/(^|\s|\,)NJ($|\s|\,)/i)
  {
    $geoID = "34";
  }
  elsif ($geoName =~ m/(^|\s|\,)NM($|\s|\,)/i)
  {
    $geoID = "35";
  }
  elsif ($geoName =~ m/(^|\s|\,)NY($|\s|\,)/i)
  {
    $geoID = "36";
  }
  elsif ($geoName =~ m/(^|\s|\,)NC($|\s|\,)/i)
  {
    $geoID = "37";
  }
  elsif ($geoName =~ m/(^|\s|\,)ND($|\s|\,)/i)
  {
    $geoID = "38";
  }
  elsif ($geoName =~ m/(^|\s|\,)OH($|\s|\,)/i)
  {
    $geoID = "39";
  }
  elsif ($geoName =~ m/(^|\s|\,)OK($|\s|\,)/i)
  {
    $geoID = "40";
  }
  elsif ($geoName =~ m/(^|\s|\,)OR($|\s|\,)/i)
  {
    $geoID = "41";
  }
  elsif ($geoName =~ m/(^|\s|\,)PA($|\s|\,)/i)
  {
    $geoID = "42";
  }
  elsif ($geoName =~ m/(^|\s|\,)RI($|\s|\,)/i)
  {
    $geoID = "44";
  }
  elsif ($geoName =~ m/(^|\s|\,)SC($|\s|\,)/i)
  {
    $geoID = "45";
  }
  elsif ($geoName =~ m/(^|\s|\,)SD($|\s|\,)/i)
  {
    $geoID = "46";
  }
  elsif ($geoName =~ m/(^|\s|\,)TN($|\s|\,)/i)
  {
    $geoID = "47";
  }
  elsif ($geoName =~ m/(^|\s|\,)TX($|\s|\,)/i)
  {
    $geoID = "48";
  }
  elsif ($geoName =~ m/(^|\s|\,)UT($|\s|\,)/i)
  {
    $geoID = "49";
  }
  elsif ($geoName =~ m/(^|\s|\,)VT($|\s|\,)/i)
  {
    $geoID = "50";
  }
  elsif ($geoName =~ m/(^|\s|\,)VA($|\s|\,)/i)
  {
    $geoID = "51";
  }
  elsif ($geoName =~ m/(^|\s|\,)WA($|\s|\,)/i)
  {
    $geoID = "53";
  }
  elsif ($geoName =~ m/(^|\s|\,)WV($|\s|\,)/i)
  {
    $geoID = "54";
  }
  elsif ($geoName =~ m/(^|\s|\,)WI($|\s|\,)/i)
  {
    $geoID = "55";
  }
  elsif ($geoName =~ m/(^|\s|\,)WY($|\s|\,)/i)
  {
    $geoID = "56";
  }

  #fall-through case: return an empty string to let caller know coding failed
  else
  {
    $geoID = "";
  }

  return($geoID);
}



#-------------------------------------------------------------------------------
#
# Try to figure out which Nielsen DMA a geography name applies to, and return
# the correct FusionMaps code.
#

sub GeoCode_dma
{
  my ($geoID);

  my ($geoName) = @_;


  if (($geoName =~ m/Abilene/i) || ($geoName =~ m/Sweetwater/i) ||
      ($geoName =~ m/ABI-TX/i))
  {
    $geoID = 662;
  }
  elsif (($geoName =~ m/ALBANY,GA/i) || ($geoName =~ m/ALB-GA/i))
  {
    $geoID = 525;
  }
  elsif (($geoName =~ m/ALBANY-SCHENECTADY-TROY/i) || ($geoName =~ m/ALB-NY/i))
  {
    $geoID = 532;
  }
  elsif (($geoName =~ m/ALBUQUERQUE-SANTA FE/i) || ($geoName =~ m/ALB-NM/i))
  {
    $geoID = 790;
  }
  elsif (($geoName =~ m/ALEXANDRIA,LA/i) || ($geoName =~ m/ALE-LA/i))
  {
    $geoID = 644;
  }
  elsif (($geoName =~ m/ALPENA/i) || ($geoName =~ m/ALP-MI/i))
  {
    $geoID = 583;
  }
  elsif (($geoName =~ m/AMARILLO/i) || ($geoName =~ m/AMA-TX/i))
  {
    $geoID = 634;
  }
  elsif (($geoName =~ m/ANCHORAGE/i) || ($geoName =~ m/ANC-AK/i))
  {
    $geoID = 743;
  }
  elsif (($geoName =~ m/ATLANTA/i) || ($geoName =~ m/ATL-GA/i))
  {
    $geoID = 524;
  }
  elsif (($geoName =~ m/AUGUSTA/i) || ($geoName =~ m/AUG-GA/i))
  {
    $geoID = 520;
  }
  elsif (($geoName =~ m/AUSTIN/i) || ($geoName =~ m/AUS-TX/i))
  {
    $geoID = 635;
  }
  elsif (($geoName =~ m/BAKERSFIELD/i) || ($geoName =~ m/BAK-CA/i))
  {
    $geoID = 800;
  }
  elsif (($geoName =~ m/BALTIMORE/i) || ($geoName =~ m/BAL-MD/i))
  {
    $geoID = 512;
  }
  elsif (($geoName =~ m/BANGOR/i) || ($geoName =~ m/BAN-ME/i))
  {
    $geoID = 537;
  }
  elsif (($geoName =~ m/BATON ROUGE/i) || ($geoName =~ m/BAT-LA/i))
  {
    $geoID = 716;
  }
  elsif (($geoName =~ m/BEAUMONT-PORT ARTHUR/i) || ($geoName =~ m/BEA-TX/i))
  {
    $geoID = 692;
  }
  elsif (($geoName =~ m/BEND,OR/i) || ($geoName =~ m/BEN-OR/i))
  {
    $geoID = 821;
  }
  elsif (($geoName =~ m/BILLINGS/i) || ($geoName =~ m/BIL-MT/i))
  {
    $geoID = 756;
  }
  elsif (($geoName =~ m/BILOXI-GULFPORT/i) || ($geoName =~ m/BIL-MS/i))
  {
    $geoID = 746;
  }
  elsif (($geoName =~ m/BINGHAMTON/i) || ($geoName =~ m/BIN-NY/i))
  {
    $geoID = 502;
  }
  elsif (($geoName =~ m/BIRMINGHAM/i) || ($geoName =~ m/BIR-AL/i))
  {
    $geoID = 630;
  }
  elsif (($geoName =~ m/BLUEFIELD-BECKLEY-OAK HILL/i) ||
      ($geoName =~ m/BLU-WV/i))
  {
    $geoID = 559;
  }
  elsif (($geoName =~ m/BOISE/i) || ($geoName =~ m/BOI-ID/i))
  {
    $geoID = 757;
  }
  elsif (($geoName =~ m/BOSTON\(MANCHESTER\)/i) || ($geoName =~ m/BOS-MA/i))
  {
    $geoID = 506;
  }
  elsif (($geoName =~ m/BOWLING GREEN/i) || ($geoName =~ m/BOW-OH/i))
  {
    $geoID = 736;
  }
  elsif (($geoName =~ m/BUFFALO/i) || ($geoName =~ m/BUF-NY/i))
  {
    $geoID = 514;
  }
  elsif (($geoName =~ m/BURLINGTON-PLATTSBURGH/i) || ($geoName =~ m/BUR-VT/i))
  {
    $geoID = 523;
  }
  elsif (($geoName =~ m/BUTTE-BOZEMAN/i) || ($geoName =~ m/BUT-MT/i))
  {
    $geoID = 754;
  }
  elsif (($geoName =~ m/CASPER-RIVERTON/i) || ($geoName =~ m/CAS-WY/i))
  {
    $geoID = 767;
  }
  elsif (($geoName =~ m/CEDAR RAPIDS-WTRLO-IWC&DUB/i) ||
      ($geoName =~ m/CED-IA/i))
  {
    $geoID = 637;
  }
  elsif (($geoName =~ m/CHAMPAIGN&SPRNGFLD-DECATUR/i) ||
      ($geoName =~ m/CHA-IL/i))
  {
    $geoID = 648;
  }
  elsif (($geoName =~ m/CHARLESTON,SC/i) || ($geoName =~ m/CHA-SC/i))
  {
    $geoID = 519;
  }
  elsif (($geoName =~ m/CHARLESTON-HUNTINGTON/i) || ($geoName =~ m/CHA-WV/i))
  {
    $geoID = 564;
  }
  elsif (($geoName =~ m/CHARLOTTE/i) || ($geoName =~ m/CHA-NC/i))
  {
    $geoID = 517;
  }
  elsif (($geoName =~ m/CHARLOTTESVILLE/i) || ($geoName =~ m/CHA-VA/i))
  {
    $geoID = 584;
  }
  elsif (($geoName =~ m/CHATTANOOGA/i) || ($geoName =~ m/CHA-TN/i))
  {
    $geoID = 575;
  }
  elsif (($geoName =~ m/CHEYENNE-SCOTTSBLUFF/i) || ($geoName =~ m/CHE-WY/i))
  {
    $geoID = 759;
  }
  elsif (($geoName =~ m/CHICAGO/i) || ($geoName =~ m/CHI-IL/i))
  {
    $geoID = 602;
  }
  elsif (($geoName =~ m/CHICO-REDDING/i) || ($geoName =~ m/CHI-CA/i))
  {
    $geoID = 868;
  }
  elsif (($geoName =~ m/CINCINNATI/i) || ($geoName =~ m/CIN-OH/i))
  {
    $geoID = 515;
  }
  elsif (($geoName =~ m/CLARKSBURG-WESTON/i) || ($geoName =~ m/CLA-WV/i))
  {
    $geoID = 598;
  }
  elsif (($geoName =~ m/CLEVELAND-AKRON\(CANTON\)/i) ||
      ($geoName =~ m/CLE-OH/i))
  {
    $geoID = 510;
  }
  elsif (($geoName =~ m/COLORADO SPRINGS-PUEBLO/i) || ($geoName =~ m/COL-CO/i))
  {
    $geoID = 752;
  }
  elsif (($geoName =~ m/COLUMBIA,SC/i) || ($geoName =~ m/COL-SC/i))
  {
    $geoID = 546;
  }
  elsif (($geoName =~ m/COLUMBIA-JEFFERSON CITY/i) || ($geoName =~ m/COL-MO/i))
  {
    $geoID = 604;
  }
  elsif (($geoName =~ m/COLUMBUS,GA/i) || ($geoName =~ m/COL-GA/i))
  {
    $geoID = 522;
  }
  elsif (($geoName =~ m/COLUMBUS,OH/i) || ($geoName =~ m/COL-OH/i))
  {
    $geoID = 535;
  }
  elsif (($geoName =~ m/COLUMBUS-TUPELO-WEST POINT/i) ||
      ($geoName =~ m/COL-MS/i))
  {
    $geoID = 673;
  }
  elsif (($geoName =~ m/CORPUS CHRISTI/i) || ($geoName =~ m/COR-TX/i))
  {
    $geoID = 600;
  }
  elsif (($geoName =~ m/DALLAS-FT.WORTH/i) || ($geoName =~ m/DAL-TX/i))
  {
    $geoID = 623;
  }
  elsif (($geoName =~ m/DAVENPORT-R.ISLAND-MOLINE/i) ||
      ($geoName =~ m/DAV-IA/i))
  {
    $geoID = 682;
  }
  elsif (($geoName =~ m/DAYTON/i) || ($geoName =~ m/DAY-OH/i))
  {
    $geoID = 542;
  }
  elsif (($geoName =~ m/DENVER/i) || ($geoName =~ m/DEN-CO/i))
  {
    $geoID = 751;
  }
  elsif (($geoName =~ m/DES MOINES-AMES/i) || ($geoName =~ m/DES-IA/i))
  {
    $geoID = 679;
  }
  elsif (($geoName =~ m/DETROIT/i) || ($geoName =~ m/DET-MI/i))
  {
    $geoID = 505;
  }
  elsif (($geoName =~ m/DOTHAN/i) || ($geoName =~ m/DOT-AL/i))
  {
    $geoID = 606;
  }
  elsif (($geoName =~ m/DULUTH-SUPERIOR/i) || ($geoName =~ m/DUL-MN/i))
  {
    $geoID = 676;
  }
  elsif (($geoName =~ m/EL PASO(LAS CRUCES)/i) || ($geoName =~ m/ELP-TX/i))
  {
    $geoID = 765;
  }
  elsif (($geoName =~ m/ELMIRA(CORNING)/i) || ($geoName =~ m/ELM-NY/i))
  {
    $geoID = 565;
  }
  elsif (($geoName =~ m/ERIE/i) || ($geoName =~ m/ERI-PA/i))
  {
    $geoID = 516;
  }
  elsif (($geoName =~ m/EUGENE/i) || ($geoName =~ m/EUG-OR/i))
  {
    $geoID = 801;
  }
  elsif (($geoName =~ m/EUREKA/i) || ($geoName =~ m/EUR-CA/i))
  {
    $geoID = 802;
  }
  elsif (($geoName =~ m/EVANSVILLE/i) || ($geoName =~ m/EVA-IN/i))
  {
    $geoID = 649;
  }
  elsif (($geoName =~ m/FAIRBANKS/i) || ($geoName =~ m/FAI-AK/i))
  {
    $geoID = 745;
  }
  elsif (($geoName =~ m/FARGO-VALLEY CITY/i) || ($geoName =~ m/FAR-ND/i))
  {
    $geoID = 724;
  }
  elsif (($geoName =~ m/FLINT-SAGINAW-BAY CITY/i) || ($geoName =~ m/FLI-MI/i))
  {
    $geoID = 513;
  }
  elsif (($geoName =~ m/FRESNO-VISALIA/i) || ($geoName =~ m/FRE-CA/i))
  {
    $geoID = 866;
  }
  elsif (($geoName =~ m/FT.MYERS-NAPLES/i) || ($geoName =~ m/FTM-FL/i))
  {
    $geoID = 571;
  }
  elsif (($geoName =~ m/FT.SMITH-FAY-SPRNGDL-RGRS/i) ||
      ($geoName =~ m/FTS-AR/i))
  {
    $geoID = 670;
  }
  elsif (($geoName =~ m/FT.WAYNE/i) || ($geoName =~ m/FTW-IN/i))
  {
    $geoID = 509;
  }
  elsif (($geoName =~ m/GAINESVILLE/i) || ($geoName =~ m/GAI-FL/i))
  {
    $geoID = 592;
  }
  elsif (($geoName =~ m/GLENDIVE/i) || ($geoName =~ m/GLE-MT/i))
  {
    $geoID = 798;
  }
  elsif (($geoName =~ m/GRAND JUNCTION-MONTROSE/i) || ($geoName =~ m/GRA-CO/i))
  {
    $geoID = 773;
  }
  elsif (($geoName =~ m/GRAND RAPIDS-KALMZOO-B.CRK/i) ||
      ($geoName =~ m/GRA-MI/i))
  {
    $geoID = 563;
  }
  elsif (($geoName =~ m/GREAT FALLS/i) || ($geoName =~ m/GRE-ND/i))
  {
    $geoID = 755;
  }
  elsif (($geoName =~ m/GREEN BAY-APPLETON/i) || ($geoName =~ m/GRE-WI/i))
  {
    $geoID = 658;
  }
  elsif (($geoName =~ m/GREENSBORO-H.POINT-W.SALEM/i) ||
      ($geoName =~ m/GBO-NC/i))
  {
    $geoID = 518;
  }
  elsif (($geoName =~ m/GREENVILLE-N.BERN-WASHNGTN/i) ||
      ($geoName =~ m/GVL-NC/i))
  {
    $geoID = 545;
  }
  elsif (($geoName =~ m/GREENVLL-SPART-ASHEVLL-AND/i) ||
      ($geoName =~ m/GSA-NC/i))
  {
    $geoID = 567;
  }
  elsif (($geoName =~ m/GREENWOOD-GREENVILLE/i) || ($geoName =~ m/GRE-MS/i))
  {
    $geoID = 647;
  }
  elsif (($geoName =~ m/HARLINGEN-WSLCO-BRNSVL-MCA/i) ||
      ($geoName =~ m/HAR-TX/i))
  {
    $geoID = 636;
  }
  elsif (($geoName =~ m/HARRISBURG-LNCSTR-LEB-YORK/i) ||
      ($geoName =~ m/HAR-PA/i))
  {
    $geoID = 566;
  }
  elsif (($geoName =~ m/HARRISONBURG/i) || ($geoName =~ m/HAR-VA/i))
  {
    $geoID = 569;
  }
  elsif (($geoName =~ m/HARTFORD&NEW HAVEN/i) || ($geoName =~ m/HAR-CT/i))
  {
    $geoID = 533;
  }
  elsif (($geoName =~ m/HATTIESBURG-LAUREL/i) || ($geoName =~ m/HAT-MS/i))
  {
    $geoID = 710;
  }
  elsif (($geoName =~ m/HELENA/i) || ($geoName =~ m/HEL-MT/i))
  {
    $geoID = 766;
  }
  elsif (($geoName =~ m/HONOLULU/i) || ($geoName =~ m/HON-HI/i))
  {
    $geoID = 744;
  }
  elsif (($geoName =~ m/HOUSTON/i) || ($geoName =~ m/HOU-TX/i))
  {
    $geoID = 618;
  }
  elsif (($geoName =~ m/HUNTSVILLE-DECATUR(FLOR)/i) ||
      ($geoName =~ m/HUN-AL/i))
  {
    $geoID = 691;
  }
  elsif (($geoName =~ m/IDAHO FALLS-POCATELLO/i) || ($geoName =~ m/IDA-ID/i))
  {
    $geoID = 758;
  }
  elsif (($geoName =~ m/INDIANAPOLIS/i) || ($geoName =~ m/IND-IN/i))
  {
    $geoID = 527;
  }
  elsif (($geoName =~ m/JACKSON,MS/i) || ($geoName =~ m/JAC-MS/i))
  {
    $geoID = 718;
  }
  elsif (($geoName =~ m/JACKSON,TN/i) || ($geoName =~ m/JAC-TN/i))
  {
    $geoID = 639;
  }
  elsif (($geoName =~ m/JACKSONVILLE/i) || ($geoName =~ m/JAC-FL/i))
  {
    $geoID = 561;
  }
  elsif (($geoName =~ m/JOHNSTOWN-ALTOONA/i) || ($geoName =~ m/JOH-PA/i))
  {
    $geoID = 574;
  }
  elsif (($geoName =~ m/JONESBORO/i) || ($geoName =~ m/JON-AR/i))
  {
    $geoID = 734;
  }
  elsif (($geoName =~ m/JOPLIN-PITTSBURG/i) || ($geoName =~ m/JOP-MO/i))
  {
    $geoID = 603;
  }
  elsif (($geoName =~ m/JUNEAU/i) || ($geoName =~ m/JUN-AK/i))
  {
    $geoID = 747;
  }
  elsif (($geoName =~ m/KANSAS CITY/i) || ($geoName =~ m/KAN-KS/i))
  {
    $geoID = 616;
  }
  elsif (($geoName =~ m/KNOXVILLE/i) || ($geoName =~ m/KNO-TX/i))
  {
    $geoID = 557;
  }
  elsif (($geoName =~ m/LA CROSSE-EAU CLAIRE/i) || ($geoName =~ m/LAC-WI/i))
  {
    $geoID = 702;
  }
  elsif (($geoName =~ m/LAFAYETTE,IN/i) || ($geoName =~ m/LAF-IN/i))
  {
    $geoID = 582;
  }
  elsif (($geoName =~ m/ LAFAYETTE,LA/i) || ($geoName =~ m/LAF-LA/i))
  {
    $geoID = 642;
  }
  elsif (($geoName =~ m/LAKE CHARLES/i) || ($geoName =~ m/LAK-LA/i))
  {
    $geoID = 643;
  }
  elsif (($geoName =~ m/LANSING/i) || ($geoName =~ m/LAN-MI/i))
  {
    $geoID = 551;
  }
  elsif (($geoName =~ m/LAREDO/i) || ($geoName =~ m/LAR-TX/i))
  {
    $geoID = 749;
  }
  elsif (($geoName =~ m/LAS VEGAS/i) || ($geoName =~ m/LAS-NV/i))
  {
    $geoID = 839;
  }
  elsif (($geoName =~ m/LEXINGTON/i) || ($geoName =~ m/LEX-KY/i))
  {
    $geoID = 541;
  }
  elsif (($geoName =~ m/LIMA/i) || ($geoName =~ m/LIM-OH/i))
  {
    $geoID = 558;
  }
  elsif (($geoName =~ m/LINCOLN&HASTINGS-KRNY/i) || ($geoName =~ m/LIN-NE/i))
  {
    $geoID = 722;
  }
  elsif (($geoName =~ m/LITTLE ROCK-PINE BLUFF/i) || ($geoName =~ m/LIT-AR/i))
  {
    $geoID = 693;
  }
  elsif (($geoName =~ m/LOS ANGELES/i) || ($geoName =~ m/LOS-CA/i))
  {
    $geoID = 803;
  }
  elsif (($geoName =~ m/LOUISVILLE/i) || ($geoName =~ m/LOU-KY/i))
  {
    $geoID = 529;
  }
  elsif (($geoName =~ m/LUBBOCK/i) || ($geoName =~ m/LUB-TX/i))
  {
    $geoID = 651;
  }
  elsif (($geoName =~ m/MACON/i) || ($geoName =~ m/MAC-GA/i))
  {
    $geoID = 503;
  }
  elsif (($geoName =~ m/MADISON/i) || ($geoName =~ m/MAD-WI/i))
  {
    $geoID = 669;
  }
  elsif (($geoName =~ m/MANKATO/i) || ($geoName =~ m/MAN-MN/i))
  {
    $geoID = 737;
  }
  elsif (($geoName =~ m/MARQUETTE/i) || ($geoName =~ m/MAR-MI/i))
  {
    $geoID = 553;
  }
  elsif (($geoName =~ m/MEDFORD-KLAMATH FALLS/i) || ($geoName =~ m/MED-OR/i))
  {
    $geoID = 813;
  }
  elsif (($geoName =~ m/MEMPHIS/i) || ($geoName =~ m/MEM-TN/i))
  {
    $geoID = 640;
  }
  elsif (($geoName =~ m/MERIDIAN/i) || ($geoName =~ m/MER-MS/i))
  {
    $geoID = 711;
  }
  elsif (($geoName =~ m/MIAMI-FT.LAUDERDALE/i) || ($geoName =~ m/MIA-FL/i))
  {
    $geoID = 528;
  }
  elsif (($geoName =~ m/MILWAUKEE/i) || ($geoName =~ m/MIL-WI/i))
  {
    $geoID = 617;
  }
  elsif (($geoName =~ m/MINNEAPOLIS-ST.PAUL/i) || ($geoName =~ m/MIN-MN/i))
  {
    $geoID = 613;
  }
  elsif (($geoName =~ m/MINOT-BISMARCK-DICKINSON/i) || ($geoName =~ m/MIN-ND/i))
  {
    $geoID = 687;
  }
  elsif (($geoName =~ m/MISSOULA/i) || ($geoName =~ m/MIS-MT/i))
  {
    $geoID = 762;
  }
  elsif (($geoName =~ m/MOBILE-PENSACOLA\(FT WALT\)/i) ||
      ($geoName =~ m/MOB-AL/i))
  {
    $geoID = 686;
  }
  elsif (($geoName =~ m/MONROE-EL DORADO/i) || ($geoName =~ m/MON-LA/i))
  {
    $geoID = 628;
  }
  elsif (($geoName =~ m/MONTEREY-SALINAS/i) || ($geoName =~ m/MON-CA/i))
  {
    $geoID = 828;
  }
  elsif (($geoName =~ m/MONTGOMERY-SELMA/i) || ($geoName =~ m/MON-AL/i))
  {
    $geoID = 698;
  }
  elsif (($geoName =~ m/MYRTLE BEACH-FLORENCE/i) || ($geoName =~ m/MYR-SC/i))
  {
    $geoID = 570;
  }
  elsif (($geoName =~ m/NASHVILLE/i) || ($geoName =~ m/NAS-TN/i))
  {
    $geoID = 659;
  }
  elsif (($geoName =~ m/NEW ORLEANS/i) || ($geoName =~ m/NEW-LA/i))
  {
    $geoID = 622;
  }
  elsif (($geoName =~ m/NEW YORK/i) || ($geoName =~ m/NEW-NY/i))
  {
    $geoID = 501;
  }
  elsif (($geoName =~ m/NORFOLK-PORTSMTH-NEWPT NWS/i) ||
      ($geoName =~ m/NOR-VA/i))
  {
    $geoID = 544;
  }
  elsif (($geoName =~ m/NORTH PLATTE/i) || ($geoName =~ m/NOR-NE/i))
  {
    $geoID = 740;
  }
  elsif (($geoName =~ m/ODESSA-MIDLAND/i) || ($geoName =~ m/ODE-TX/i))
  {
    $geoID = 633;
  }
  elsif (($geoName =~ m/OKLAHOMA CITY/i) || ($geoName =~ m/OKL-OK/i))
  {
    $geoID = 650;
  }
  elsif (($geoName =~ m/OMAHA/i) || ($geoName =~ m/OMA-NE/i))
  {
    $geoID = 652;
  }
  elsif (($geoName =~ m/ORLANDO-DAYTONA BCH-MELBRN/i) ||
      ($geoName =~ m/ORL-FL/i))
  {
    $geoID = 534;
  }
  elsif (($geoName =~ m/OTTUMWA-KIRKSVILLE/i) || ($geoName =~ m/OTT-IA/i))
  {
    $geoID = 631;
  }
  elsif (($geoName =~ m/PADUCAH-CAPE GIRARD-HARSBG/i) ||
      ($geoName =~ m/PAD-KY/i))
  {
    $geoID = 632;
  }
  elsif (($geoName =~ m/PALM SPRINGS/i) || ($geoName =~ m/PAL-CA/i))
  {
    $geoID = 804;
  }
  elsif (($geoName =~ m/PANAMA CITY/i) || ($geoName =~ m/PAN-FL/i))
  {
    $geoID = 656;
  }
  elsif (($geoName =~ m/PARKERSBURG/i) || ($geoName =~ m/PAR-WV/i))
  {
    $geoID = 597;
  }
  elsif (($geoName =~ m/PEORIA-BLOOMINGTON/i) || ($geoName =~ m/PEO-IL/i))
  {
    $geoID = 675;
  }
  elsif (($geoName =~ m/PHILADELPHIA/i) || ($geoName =~ m/PHI-PA/i))
  {
    $geoID = 504;
  }
  elsif (($geoName =~ m/PHOENIX\(PRESCOTT\)/i) || ($geoName =~ m/PHO-AZ/i))
  {
    $geoID = 753;
  }
  elsif (($geoName =~ m/PITTSBURGH/i) || ($geoName =~ m/PIT-PA/i))
  {
    $geoID = 508;
  }
  elsif (($geoName =~ m/PORTLAND,OR/i) || ($geoName =~ m/POR-OR/i))
  {
    $geoID = 820;
  }
  elsif (($geoName =~ m/PORTLAND-AUBURN/i) || ($geoName =~ m/POR-ME/i))
  {
    $geoID = 500;
  }
  elsif (($geoName =~ m/PRESQUE ISLE/i) || ($geoName =~ m/PRE-ME/i))
  {
    $geoID = 552;
  }
  elsif (($geoName =~ m/PROVIDENCE-NEW BEDFORD/i) || ($geoName =~ m/PRO-CT/i))
  {
    $geoID = 521;
  }
  elsif (($geoName =~ m/QUINCY-HANNIBAL-KEOKUK/i) || ($geoName =~ m/QUI-IL/i))
  {
    $geoID = 717;
  }
  elsif (($geoName =~ m/RALEIGH-DURHAM\(FAYETVLLE\)/i) ||
      ($geoName =~ m/RAL-NC/i))
  {
    $geoID = 560;
  }
  elsif (($geoName =~ m/RAPID CITY/i) || ($geoName =~ m/RAP-IA/i))
  {
    $geoID = 764;
  }
  elsif (($geoName =~ m/RENO/i) || ($geoName =~ m/REN-NV/i))
  {
    $geoID = 811;
  }
  elsif (($geoName =~ m/RICHMOND-PETERSBURG/i) || ($geoName =~ m/RIC-VA/i))
  {
    $geoID = 556;
  }
  elsif (($geoName =~ m/ROANOKE-LYNCHBURG/i) || ($geoName =~ m/ROA-VA/i))
  {
    $geoID = 573;
  }
  elsif (($geoName =~ m/ROCHESTER,NY/i) || ($geoName =~ m/ROC-NY/i))
  {
    $geoID = 538;
  }
  elsif (($geoName =~ m/ROCHESTR-MASON CITY-AUSTIN/i) ||
      ($geoName =~ m/ROC-IA/i))
  {
    $geoID = 611;
  }
  elsif (($geoName =~ m/ROCKFORD/i) || ($geoName =~ m/ROC-IL/i))
  {
    $geoID = 610;
  }
  elsif (($geoName =~ m/SACRAMNTO-STKTON-MODESTO/i) || ($geoName =~ m/SAC-CA/i))
  {
    $geoID = 862;
  }
  elsif (($geoName =~ m/SALISBURY/i) || ($geoName =~ m/SAL-MD/i))
  {
    $geoID = 576;
  }
  elsif (($geoName =~ m/SALTLAKE CITY/i) || ($geoName =~ m/SAL-UT/i))
  {
    $geoID = 770;
  }
  elsif (($geoName =~ m/SAN ANGELO/i) || ($geoName =~ m/SAN-TX/i))
  {
    $geoID = 661;
  }
  elsif (($geoName =~ m/SAN ANTONIO/i) || ($geoName =~ m/SAT-TX/i))
  {
    $geoID = 641;
  }
  elsif (($geoName =~ m/SAN DIEGO/i) || ($geoName =~ m/SAN-CA/i))
  {
    $geoID = 825;
  }
  elsif (($geoName =~ m/SAN FRANCISCO-OAK-SAN JOSE/i) ||
      ($geoName =~ m/SFO-CA/i))
  {
    $geoID = 807;
  }
  elsif (($geoName =~ m/SANTABARBRA-SANMAR-SANLUOB/i) ||
      ($geoName =~ m/SBA-CA/i))
  {
    $geoID = 855;
  }
  elsif (($geoName =~ m/SAVANNAH/i) || ($geoName =~ m/SAV-GA/i))
  {
    $geoID = 507;
  }
  elsif (($geoName =~ m/SEATTLE-TACOMA/i) || ($geoName =~ m/SEA-WA/i))
  {
    $geoID = 819;
  }
  elsif (($geoName =~ m/SHERMAN-ADA/i) || ($geoName =~ m/SHE-TX/i))
  {
    $geoID = 657;
  }
  elsif (($geoName =~ m/SHREVEPORT/i) || ($geoName =~ m/SHR-LA/i))
  {
    $geoID = 612;
  }
  elsif (($geoName =~ m/SIOUX CITY/i) || ($geoName =~ m/SIO-IA/i))
  {
    $geoID = 624;
  }
  elsif (($geoName =~ m/SIOUX FALLS\(MITCHELL\)/i) || ($geoName =~ m/SIO-SD/i))
  {
    $geoID = 725;
  }
  elsif (($geoName =~ m/SOUTH BEND-ELKHART/i) || ($geoName =~ m/SOU-IN/i))
  {
    $geoID = 588;
  }
  elsif (($geoName =~ m/SPOKANE/i) || ($geoName =~ m/SPO-WA/i))
  {
    $geoID = 881;
  }
  elsif (($geoName =~ m/SPRINGFIELD,MO/i) || ($geoName =~ m/SPR-MO/i))
  {
    $geoID = 619;
  }
  elsif (($geoName =~ m/SPRINGFIELD-HOLYOKE/i) || ($geoName =~ m/SPR-VA/i))
  {
    $geoID = 543;
  }
  elsif (($geoName =~ m/ST.JOSEPH/i) || ($geoName =~ m/STJ-KS/i))
  {
    $geoID = 638;
  }
  elsif (($geoName =~ m/ST.LOUIS/i) || ($geoName =~ m/STL-MO/i))
  {
    $geoID = 609;
  }
  elsif (($geoName =~ m/SYRACUSE/i) || ($geoName =~ m/SYR-NY/i))
  {
    $geoID = 555;
  }
  elsif (($geoName =~ m/TALLAHASSEE-THOMASVILLE/i) || ($geoName =~ m/TAL-FL/i))
  {
    $geoID = 530;
  }
  elsif (($geoName =~ m/TAMPA-ST.PETE\(SARASOTA\)/i) ||
      ($geoName =~ m/TAM-FL/i))
  {
    $geoID = 539;
  }
  elsif (($geoName =~ m/TERRE HAUTE/i) || ($geoName =~ m/TER-IN/i))
  {
    $geoID = 581;
  }
  elsif (($geoName =~ m/TOLEDO/i) || ($geoName =~ m/TOL-OH/i))
  {
    $geoID = 547;
  }
  elsif (($geoName =~ m/TOPEKA/i) || ($geoName =~ m/TOP-KS/i))
  {
    $geoID = 605;
  }
  elsif (($geoName =~ m/TRAVERSE CITY-CADILLAC/i) || ($geoName =~ m/TRA-MI/i))
  {
    $geoID = 540;
  }
  elsif (($geoName =~ m/TRI-CITIES,TN-VA/i) || ($geoName =~ m/TRI-TN/i))
  {
    $geoID = 531;
  }
  elsif (($geoName =~ m/TUCSON\(SIERRA VISTA\)/i) ||
      ($geoName =~ m/TUS-AZ/i))
  {
    $geoID = 789;
  }
  elsif (($geoName =~ m/TULSA/i) || ($geoName =~ m/TUL-OK/i))
  {
    $geoID = 671;
  }
  elsif (($geoName =~ m/TWIN FALLS/i) || ($geoName =~ m/TWI-ID/i))
  {
    $geoID = 760;
  }
  elsif (($geoName =~ m/TYLER-LONGVIEW\(LFKN&NCGD\)/i) ||
      ($geoName =~ m/TYL-TX/i))
  {
    $geoID = 709;
  }
  elsif (($geoName =~ m/UTICA/i) || ($geoName =~ m/UTI-NY/i))
  {
    $geoID = 526;
  }
  elsif (($geoName =~ m/VICTORIA/i) || ($geoName =~ m/VIC-TX/i))
  {
    $geoID = 626;
  }
  elsif (($geoName =~ m/WACO-TEMPLE-BRYAN/i) || ($geoName =~ m/WAC-TX/i))
  {
    $geoID = 625;
  }
  elsif (($geoName =~ m/WASHINGTON,DC\(HAGRSTWN\)/i) ||
      ($geoName =~ m/WAS-DC/i))
  {
    $geoID = 511;
  }
  elsif (($geoName =~ m/WATERTOWN/i) || ($geoName =~ m/WAT-NY/i))
  {
    $geoID = 549;
  }
  elsif (($geoName =~ m/WAUSAU-RHINELANDER/i) || ($geoName =~ m/WAU-WI/i))
  {
    $geoID = 705;
  }
  elsif (($geoName =~ m/WEST PALM BEACH-FT.PIERCE/i) ||
      ($geoName =~ m/WES-FL/i))
  {
    $geoID = 548;
  }
  elsif (($geoName =~ m/WHEELING-STEUBENVILLE/i) || ($geoName =~ m/WHE-WV/i))
  {
    $geoID = 554;
  }
  elsif (($geoName =~ m/WICHITA FALLS&LAWTON/i) || ($geoName =~ m/WIC-TX/i))
  {
    $geoID = 627;
  }
  elsif (($geoName =~ m/WICHITA-HUTCHINSON PLUS/i) ||
      ($geoName =~ m/WIC-KS/i))
  {
    $geoID = 678;
  }
  elsif (($geoName =~ m/WILKES BARRE-SCRANTON/i) || ($geoName =~ m/WIL-PA/i))
  {
    $geoID = 577;
  }
  elsif (($geoName =~ m/WILMINGTON/i) || ($geoName =~ m/WIL-DE/i))
  {
    $geoID = 550;
  }
  elsif (($geoName =~ m/YAKIMA-PASCO-RCHLND-KNNWCK/i) ||
      ($geoName =~ m/YAK-WA/i))
  {
    $geoID = 810;
  }
  elsif (($geoName =~ m/YOUNGSTOWN/i) || ($geoName =~ m/YOU-PA/i))
  {
    $geoID = 536;
  }
  elsif (($geoName =~ m/YUMA-ELCENTRO/i) || ($geoName =~ m/YUM-AZ/i))
  {
    $geoID = 771;
  }
  elsif (($geoName =~ m/ZANESVILLE/i) || ($geoName =~ m/ZAN-OH/i))
  {
    $geoID = 596;
  }
  else
  {
    $geoID = "";
  }

  return($geoID);
}



#-------------------------------------------------------------------------------



1;
