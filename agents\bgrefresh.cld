#!/usr/bin/perl

use lib "/opt/apache/app/";

use DBI;

use Lib::KoalaConfig;
use Lib::BuildCube;
use Lib::DataSel;
use Lib::DataSources;
use Lib::DSRMeasures;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::WebUtils;


my $debug;



#-------------------------------------------------------------------------
#
# Output debug data, if enabled
#

sub DBG
{
  my ($str) = @_;

  if ($debug == 1)
  {
    print STDERR "$str\n";
  }
}



#-------------------------------------------------------------------------

  #determine if we're being run manually
  $debug = 0;
  if ($ARGV[0] eq "debug")
  {
    $debug = 1;
  }

  #redirect STDERR to the Koala error log
  if ($debug == 0)
  {
    close(STDOUT);
    close(STDERR);
    open(STDERR, ">>/opt/apache/htdocs/tmp/koala_error.log");
  }

  #see if a lockfile exists.  If it does and the PID it contains is still
  #running, then exit.
  if ( -f "/opt/apache/app/agents/.bgRefresh.lock")
  {
    $old_pid = `cat /opt/apache/app/agents/.bgRefresh.lock`;
    chomp($old_pid);

    #cheat and check to see if there's a corresponding directory in /proc,
    #rather than go through the crap of getting and parsing ps output
    if ( -e "/proc/$old_pid")
    {
      DBG("Already running, pid $old_pid");
      exit;
    }
    else
    {
      unlink("/opt/apache/app/agents/.bgRefresh.lock");
    }
  }

  #connect to the master database
  $db = KAPutil_connect_to_database();

  system("echo $$ > /opt/apache/app/agents/.bgRefresh.lock");

  #update our default storage usage limits
  $cmdOutput = `df -h | grep $Lib::KoalaConfig::dataDisk`;
  $cmdOutput =~ m/^.*\s+(.*?)\s+(.*?)\s+(.*?)\s+(.*)%/;
  $usagePct = $4;
  $usagePct = $usagePct + 15;
  $query = "INSERT INTO app.config (name,value) \
      VALUES ('analytics_storage_usage', '$usagePct') \
      ON DUPLICATE KEY UPDATE value='$usagePct'";
  $db->do($query);

  #if we have any per-org storage, update their usage
  $query = "SELECT ID, dataQuota FROM app.orgs WHERE !isnull(dataQuota)";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  while (($orgID, $dataQuota) = $dbOutput->fetchrow_array)
  {
    $query = "SELECT SUM(storage) FROM app.users WHERE orgID=$orgID";
    $dbOutput1 = $db->prepare($query);
    $dbOutput1->execute;
    ($storageUsage) = $dbOutput1->fetchrow_array;
    $storageUsage /= 1000;  #KB
    $storageUsage /= 1000;  #MB
    $storageUsage /= 1000;  #GB
    $usagePct = ($storageUsage / $dataQuota) * 100;
    $usagePct += 5;
    $query = "UPDATE app.orgs SET dataQuotaUsage=$usagePct WHERE ID=$orgID";
    $db->do($query);
  }

=pod
  $query = "SELECT ID, dataStorage FROM app.orgs WHERE !isnull(dataStorage)";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  while (($orgID, $diskName) = $dbOutput->fetchrow_array)
  {
    $cmdOutput = `df -h | grep $diskName`;
    $cmdOutput =~ m/^.*\s+(.*?)\s+(.*?)\s+(.*?)\s+(.*)%/;
    $usagePct = $4;
    $usagePct = $usagePct + 15;
    $query = "UPDATE app.orgs SET dataQuotaUsage=$usagePct WHERE ID=$orgID";
    $db->do($query);
  }
=cut

  @listDims = ('p', 'g', 't', 'm');
  @aggDims = ('p', 'g', 't');

  #get the current database time and day of week (we're going to use this when
  #we decide if we should do heavy-weight operations that can wait a bit)
  $query = "SELECT HOUR(NOW()), DAYOFWEEK(NOW()), MINUTE(NOW()) \
      FROM dataSources LIMIT 1";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($hour, $day, $minute) = $dbOutput->fetchrow_array;

  #get a list of data sources being used by other jobs (we're going to skip them)
  $query = "SELECT dsID FROM app.jobs";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  while (($dsID) = $dbOutput->fetchrow_array)
  {
    $skipDataSources{$dsID} = 1;
  }

  #cycle through every data source on the system not currently being created/
  #updated
  $query = "SELECT ID, name FROM app.dataSources ORDER BY ID";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;

  while (($dsID, $name) = $dbOutput->fetchrow_array)
  {
    DBG("------------------------------------------------------------");
    DBG("Data Source: $name ($dsID)");

    $dsSchema = "datasource_" . $dsID;

    if ($skipDataSources{$dsID} == 1)
    {
      DBG("Skipping in-use data source\n\n");
    }

    #
    # Lists over 24 hours old
    #

    #if we're on an off-hour (midnight-4am)
    if (($hour < 5) || ($debug == 1))
    {
      DBG("Expanding lists/aggs that are over 24 hours old");

      #re-expand any list that hasn't been expanded in at least 24 hours
      foreach $dim (@listDims)
      {
        if ($dim eq "p")
        {
          $dbName = "product_list";
        }
        elsif ($dim eq "g")
        {
          $dbName = "geography_list";
        }
        elsif ($dim eq "t")
        {
          $dbName = "time_list";
        }
        elsif ($dim eq "m")
        {
          $dbName = "measure_list";
        }

        $query = "SELECT ID, members, name FROM $dsSchema.$dbName \
            WHERE DATE_SUB(NOW(), INTERVAL 24 HOUR) > lastUpdated";
        $dbOutput2 = $db->prepare($query);
        $dbOutput2->execute;
        while (($id, $members, $name) = $dbOutput2->fetchrow_array)
        {
          DBG("Expanding list $name");
          $itemString = datasel_expand_script($db, $dsSchema, $id, $dim, "l");

          #if the list is different than before we expanded it, update DS's
          #lastModified time to any lists/aggs that depend on it will update
          if ($itemString ne $members)
          {
            DBG("***** Updated membership for list $name");
            $db->do("UPDATE dataSources SET lastModified=NOW() WHERE ID=$dsID");
          }

          #update the list's update time to now, so it's good for another 24
          $db->do("UPDATE $dsSchema.$dbName SET lastUpdated=NOW() WHERE ID=$id");
        }
      }

      #re-expand any aggregate that hasn't been expanded in at least 24 hours
      foreach $dim (@aggDims)
      {
        if ($dim eq "p")
        {
          $dbName = "product_aggregate";
        }
        elsif ($dim eq "g")
        {
          $dbName = "geography_aggregate";
        }
        elsif ($dim eq "t")
        {
          $dbName = "time_aggregate";
        }

        $query = "SELECT ID, addMembers, subtractMembers, name \
            FROM $dsSchema.$dbName \
            WHERE DATE_SUB(NOW(), INTERVAL 24 HOUR) > lastUpdated";
        $dbOutput2 = $db->prepare($query);
        $dbOutput2->execute;
        while (($id, $members, $subMembers, $name) = $dbOutput2->fetchrow_array)
        {
          DBG("Expanding aggregate $name");
          $itemString = datasel_expand_script($db, $dsSchema, $id, $dim, "a");
          $subtractString = datasel_expand_script($db, $dsSchema, $id, $dim, "as");

          #if the agg is different than before we expanded it, update DS's
          #lastModified time so any lists/aggs that depend on it will update
          if (($itemString ne $members) || ($subtractString ne $subMembers))
          {
            DBG("***** Updated membership for aggregate $name");
            $db->do("UPDATE dataSources SET lastModified=NOW() WHERE ID=$dsID");
          }

          #update the agg's update time to now, so it's good for another 24
          $query = "UPDATE $dsSchema.$dbName SET lastUpdated=NOW() WHERE ID=$id";
          $db->do($query);
        }
      }
    }


    #
    # Lists older than data source's last modified timestamp
    #

    DBG("Expanding lists/aggs older than data source's last modified time");

    $query = "SELECT UNIX_TIMESTAMP(lastModified) FROM dataSources WHERE ID=$dsID";
    $dbOutput2 = $db->prepare($query);
    $dbOutput2->execute;
    ($lastModified) = $dbOutput2->fetchrow_array;

    foreach $dim (@listDims)
    {
      if ($dim eq "p")
      {
        $dbName = "product_list";
      }
      elsif ($dim eq "g")
      {
        $dbName = "geography_list";
      }
      elsif ($dim eq "t")
      {
        $dbName = "time_list";
      }
      elsif ($dim eq "m")
      {
        $dbName = "measure_list";
      }

      $query = "SELECT ID, members, name FROM $dsSchema.$dbName \
          WHERE UNIX_TIMESTAMP(lastUpdated) < '$lastModified' OR ISNULL(lastUpdated)";
      $dbOutput2 = $db->prepare($query);
      $dbOutput2->execute;
      while (($id, $members, $name) = $dbOutput2->fetchrow_array)
      {
        DBG("Expanding list $name");
        $itemString = datasel_expand_script($db, $dsSchema, $id, $dim, "l");

        #if the list is different than before we expanded it, update DS's
        #lastModified time so any lists/aggs that depend on it will update
        if ($itemString ne $members)
        {
          DBG("***** Updated membership for list $name");
          $db->do("UPDATE dataSources SET lastModified=NOW() WHERE ID=$dsID");
        }

        #mark list as refreshed, since it is
        $db->do("UPDATE $dsSchema.$dbName SET lastUpdated=NOW() WHERE ID=$id");
      }
    }


    #
    # Aggregates older than data source's last modified timestamp
    #

    $query = "SELECT UNIX_TIMESTAMP(lastModified) FROM dataSources WHERE ID=$dsID";
    $dbOutput2 = $db->prepare($query);
    $dbOutput2->execute;
    ($lastModified) = $dbOutput2->fetchrow_array;

    foreach $dim (@aggDims)
    {
      if ($dim eq "p")
      {
        $dbName = "product_aggregate";
      }
      elsif ($dim eq "g")
      {
        $dbName = "geography_aggregate";
      }
      elsif ($dim eq "t")
      {
        $dbName = "time_aggregate";
      }

      $query = "SELECT ID, addMembers, name FROM $dsSchema.$dbName \
          WHERE UNIX_TIMESTAMP(lastUpdated) < '$lastModified' OR ISNULL(lastUpdated)";
      $dbOutput2 = $db->prepare($query);
      $dbOutput2->execute;
      while (($id, $members, $name) = $dbOutput2->fetchrow_array)
      {
        DBG("Expanding aggregate $name");
        $itemString = datasel_expand_script($db, $dsSchema, $id, $dim, "a");

        #if the agg is different than before we expanded it, update DS's
        #lastModified time so any lists/aggs that depend on it will update
        if ($itemString ne $members)
        {
          DBG("***** Updated membership for aggregate $name");
          $query = "UPDATE dataSources SET lastModified=NOW() WHERE ID=$dsID";
          $db->do($query);
        }

        #mark agg as refreshed, since it is
        $query = "UPDATE $dsSchema.$dbName SET lastUpdated=NOW() WHERE ID=$id";
        $db->do($query);
      }
    }
  }


#EOF
