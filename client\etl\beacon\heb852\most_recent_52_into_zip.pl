#!/usr/bin/perl

use lib "/opt/apache/app/";

use Text::CSV_XS;
use DateTime::Duration;
use DBI;

use Lib::KoalaConfig;



#---------------------------------------------------------------------------
#
# Output log info to console and log file
#


sub DBG
{
  my ($date);

  my ($str) = @_;

  $date = localtime();
  print STDERR "$date: $str\n";
  print("$str\n");
}


#---------------------------------------------------------------------------


  #get the mtime timestamp from the current "most recent" file
  @fileInfo = stat("/data/afsi/most_recent.csv");
  $currentTimeStamp = $originalTimeStamp = $fileInfo[9];

  #cycle through the CSV files in the data dir, looking for any that were
  #uploaded on Sundays
  opendir(DIRHANDLE, "/data/afsi");
  while (defined($filename = readdir(DIRHANDLE)))
  {

    #make sure we're only lookng at CSV files that fit our expected name pattern
    if ($filename =~ m/ibm852_.*csv$/i)
    {

      @fileInfo = stat("/data/afsi/$filename");
      $mtime = $fileInfo[9];
      ($sec, $min, $hour, $mday, $mon, $year, $wday, $yday, $isdst) = localtime($mtime);

      if ($wday == 0)
      {
        push(@filenames, "/data/afsi/$filename");
      }

    }
  }

  $filenameStr = join(' ', @filenames);
  `/usr/bin/zip /data/afsi/52weeks.zip $filenameStr`;



#EOF
