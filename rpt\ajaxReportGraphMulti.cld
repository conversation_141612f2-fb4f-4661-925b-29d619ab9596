#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRMeasures;
use Lib::DSRUtils;
use Lib::Reports;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get the CGI input variables
  $rptID = $q->param('rpt');
  $graphNum = $q->param('n');
  $visID = $q->param('v');

  #get any "fixed" dimensions (used for expanded reports)
  $fProd = $q->param('p');
  $fGeo = $q->param('g');
  $fTime = $q->param('t');

  $db = KAPutil_connect_to_database();

  #if we're being called as part of a PPT export
  if ($userID < 1)
  {
    $userID = $q->param('u');
    $query = "SELECT acctType FROM app.users WHERE ID=$userID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    ($acctType) = $dbOutput->fetchrow_array;
  }

  #if the user is a viewer, use the viewer selection table
  if ($acctType == 0)
  {
    $visualSelTable = "app.visuals_viewers";
  }
  else
  {
    $visualSelTable = "app.visuals";
  }

  #get the list of axes & selected dimension items from the database
  $query = "SELECT dsID, design, graph_x, graph_y FROM visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($dsID, $graphDesign, $graph_x, $graph_y) = $dbOutput->fetchrow_array;

  ($productIDstring, $geographyIDstring, $timeIDstring, $measureIDstring) = reports_get_selected_items($db, $visID, $userID, $acctType);

  #if more than one time period is selected, get the list of time periods in
  #order so we can sort them sensibly
  @timeIDs = split(',', $timeIDstring);
  if ((scalar(@timeIDs) > 1) && (!($timeIDstring =~ m/AGG/)))
  {
    $query = "SELECT timeperiods FROM app.cubes WHERE ID=$rptID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($timeOrderStr) = $dbOutput->fetchrow_array;

    #turn the ordered time string into an array, then reverse it so oldest
    #time periods are on left
    @orderedTimeIDs = split(',', $timeOrderStr);
    @orderedTimeIDs = reverse(@orderedTimeIDs);

    #create a hash of the selected time periods
    foreach $timeID (@timeIDs)
    {
      $selTimeHash{$timeID} = 1;
    }

    #build an in-order array of only selected time IDs
    foreach $id (@orderedTimeIDs)
    {
      if ($selTimeHash{$id} == 1)
      {
        push(@finalTimeIDs, $id);
      }
    }

    $timeIDstring = join(',', @finalTimeIDs);
  }

  #get slicer configuration
  $query = "SELECT slicers FROM cubes WHERE ID=$rptID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($slicersStr) = $dbOutput->fetchrow_array;

  $graphType = reports_get_style($graphDesign, "type");

  #make sure we're the right data script
  $dataScript = reports_data_script($graphType);
  if ($dataScript ne "ajaxReportGraphMulti.cld")
  {
    $url = "http://" . $Lib::KoalaConfig::hostname . "/app/rpt/$dataScript" . "?rpt=$rptID&n=$graphNum";
    print $q->redirect(
       -uri=> $url,
       -status=>'301 Moved Permanently');
  }

  print("Content-type: text/plain\n\n");

  #assemble report cube name
  $dsSchema = "datasource_" . $dsID;
  $rptCube = "_rptcube_" . $rptID;

  #if we're dealing with a fixed dimension (used for expanded reports)
  if (length($fProd) > 0)
  {
    $productIDstring = $fProd;
  }
  if (length($fGeo) > 0)
  {
    $geographyIDstring = $fGeo;
  }
  if (length($fTime) > 0)
  {
    $timeIDstring = $fTime;
  }

  #fetch the item names for our categories
  %categoryNames = dsr_get_item_name_hash($db, $dsSchema, $graph_x);

  #fetch the item names for our data sets (these are always measures here)
  %setNames = dsr_get_item_name_hash($db, $dsSchema, $graph_y);

  #escape any special characters in our category & set names
  foreach $id (keys %categoryNames)
  {
    $categoryNames{$id} =~ s/\'//g;
  }

  #create an array of the categories we need to display, and go ahead and set
  #up our "single selection" dimension IDs while we're at it
  if ($graph_x eq "p")
  {
    @categoryIDs = split(/,/, $productIDstring);

    $q_geography = $db->quote($geographyIDstring);
    $q_time = $db->quote($timeIDstring);
  }
  elsif ($graph_x eq "g")
  {
    @categoryIDs = split(/,/, $geographyIDstring);

    $q_product = $db->quote($productIDstring);
    $q_time = $db->quote($timeIDstring);
  }
  elsif ($graph_x eq "t")
  {
    @categoryIDs = split(/,/, $timeIDstring);

    $q_product = $db->quote($productIDstring);
    $q_geography = $db->quote($geographyIDstring);
  }

  #create an array of the data sets to display
  @measureIDs = split(/,/, $measureIDstring);
  if ($graph_y eq "p")
  {
    @setIDs = split(/,/, $productIDstring);
  }
  elsif ($graph_y eq "g")
  {
    @setIDs = split(/,/, $geographyIDstring);
  }
  elsif ($graph_y eq "t")
  {
    @setIDs = split(/,/, $timeIDstring);
  }
  else
  {
    @setIDs = split(/,/, $measureIDstring);
  }


  # ------- parse graph design info ----------

  #handle a "locked" measure for the graph
  if ($graphDesign =~ m/,measure:(\d+),/)
  {
    $lockedMeasure = $1;
    if ($lockedMeasure > 0)
    {
      @setIDs = ($lockedMeasure);
    }
  }

  #handle a caption (chart title)
  if ($graphDesign =~ m/,caption:\"(.*?)\",/)
  {
    $caption = $1;
  }

  #handle a subcaption (chart subtitle)
  if ($graphDesign =~ m/,subcaption:\"(.*?)\",/)
  {
    $subcaption = $1;
  }

  $captionFontColor = reports_get_style($graphDesign, "captionFontColor");
  if (length($captionFontColor) < 1)
  {
    $captionFontColor = reports_chart_design_default("captionFontColor");
  }
  $captionFontColor = "captionFontColor='$captionFontColor'";

  $captionAlignment = reports_get_style($graphDesign, "captionAlignment");
  if (length($captionAlignment) < 1)
  {
    $captionAlignment = reports_chart_design_default("captionAlignment");
  }
  $captionAlignment = "captionAlignment='$captionAlignment'";

  $captionFontSize = reports_get_style($graphDesign, "captionFontSize");
  if (length($captionFontSize) < 1)
  {
    $captionFontSize = reports_chart_design_default("captionFontSize");
  }
  $captionFontSize = "captionFontSize='$captionFontSize'";

  $captionFont = reports_get_style($graphDesign, "captionFont");
  if (length($captionFont) > 3)
  {
    $captionFont = "captionFont='$captionFont'";
  }

  #handle x and y axis names
  if ($graphDesign =~ m/,xAxisName:\"(.*?)\",/)
  {
    $xAxisName = $1;
  }

  $xAxisNameFontSize = reports_get_style($graphDesign, "xAxisNameFontSize");
  if (length($xAxisNameFontSize) < 1)
  {
    $xAxisNameFontSize = reports_chart_design_default("xAxisNameFontSize");
  }
  $xAxisNameFontSize = "xAxisNameFontSize='$xAxisNameFontSize'";

  if ($graphDesign =~ m/,yAxisName:\"(.*?)\",/)
  {
    $yAxisName = $1;
  }

  $yAxisNameFontSize = reports_get_style($graphDesign, "yAxisNameFontSize");
  if (length($yAxisNameFontSize) < 1)
  {
    $yAxisNameFontSize = reports_chart_design_default("yAxisNameFontSize");
  }
  $yAxisNameFontSize = "yAxisNameFontSize='$yAxisNameFontSize'";


  #handle background color
  $bgColor = reports_get_style($graphDesign, "bgColor");
  if (length($bgColor) < 6)
  {
    $bgColor = reports_chart_design_default("bgColor");
  }
  $canvasBgColor = "canvasBgColor='$bgColor'";
  $bgColor = "bgColor='$bgColor'";


  #handle a border (color & thickness)
  $showBorder = reports_get_style($graphDesign, "showBorder");
  if (length($showBorder) < 1)
  {
    $showBorder = reports_chart_design_default("showBorder");
  }
  $showBorder = "showBorder='$showBorder'";

  $borderColor = reports_get_style($graphDesign, "borderColor");
  if (length($borderColor) < 6)
  {
    $borderColor = reports_chart_design_default("borderColor");
  }
  $borderColor = "borderColor='$valueFontColor'";

  $borderThickness = reports_get_style($graphDesign, "borderThickness");
  if (length($borderThickness) < 1)
  {
    $borderThickness = reports_chart_design_default("borderThickness");
  }
  $borderThickness = "borderThickness='$borderThickness'";


  #handle data label styling
  $showLabels = reports_get_style($graphDesign, "showLabels");
  if (length($showLabels) < 1)
  {
    $showLabels = reports_chart_design_default("showLabels");
  }
  $showLabels = "showLabels='$showLabels'";

  $labelFontColor = reports_get_style($graphDesign, "labelFontColor");
  if (length($labelFontColor) < 1)
  {
    $labelFontColor = reports_chart_design_default("labelFontColor");
  }
  $labelFontColor = "labelFontColor='$labelFontColor'";

  $labelDisplay = reports_get_style($graphDesign, "labelDisplay");
  if (length($labelDisplay) < 1)
  {
    $labelDisplay = reports_chart_design_default("labelDisplay");
  }
  $labelDisplay = "labelDisplay='$labelDisplay'";

  $labelStep = reports_get_style($graphDesign, "labelStep");
  if (length($labelStep) < 1)
  {
    $labelStep = reports_chart_design_default("labelStep");
  }
  $labelStep = "labelStep='$labelStep'";

  $labelFontSize = reports_get_style($graphDesign, "labelFontSize");
  if (length($labelFontSize) < 1)
  {
    $labelFontSize = reports_chart_design_default("labelFontSize");
  }
  $labelFontSize = "labelFontSize='$labelFontSize'";

  $labelFont = reports_get_style($graphDesign, "labelFont");
  if (length($labelFont) > 1)
  {
    $labelFont = "labelFont='$labelFont'";
  }


  #handle graph legend styling
  $showLegend = reports_get_style($graphDesign, "showLegend");
  if (length($showLegend) < 1)
  {
    $showLegend = reports_chart_design_default("showLegend");
  }
  $showLegend = "showLegend='$showLegend'";

  $legendItemFontColor = reports_get_style($graphDesign, "legendItemFontColor");
  if (length($legendItemFontColor) < 6)
  {
    $legendItemFontColor = reports_chart_design_default("legendItemFontColor");
  }
  $legendItemFontColor = "legendItemFontColor='$legendItemFontColor'";

  $legendPosition = reports_get_style($graphDesign, "legendPosition");
  if (length($legendPosition) < 1)
  {
    $legendPosition = reports_chart_design_default("legendPosition");
  }
  $legendPosition = "legendPosition='$legendPosition'";

  $legendItemFont = reports_get_style($graphDesign, "legendItemFont");
  if (length($legendItemFont) < 1)
  {
    $legendItemFont = reports_chart_design_default("legendItemFont");
  }
  $legendItemFont = "legendItemFont='$legendItemFont'";

  $legendItemFontSize = reports_get_style($graphDesign, "legendItemFontSize");
  if (length($legendItemFontSize) < 1)
  {
    $legendItemFontSize = reports_chart_design_default("legendItemFontSize");
  }
  $legendItemFontSize = "legendItemFontSize='$legendItemFontSize'";


  #handle data value styling
  $showValues = reports_get_style($graphDesign, "showValues");
  if (length($showValues) < 1)
  {
    $showValues = reports_chart_design_default("showValues");
  }
  $showValues = "showValues='$showValues'";

  $valueFontColor = reports_get_style($graphDesign, "valueFontColor");
  if (length($valueFontColor) < 6)
  {
    $valueFontColor = reports_chart_design_default("valueFontColor");
  }
  $valueFontColor = "valueFontColor='$valueFontColor'";

  $formatNumberScale = reports_get_style($graphDesign, "formatNumberScale");
  if (length($formatNumberScale) < 1)
  {
    $formatNumberScale = reports_chart_design_default("formatNumberScale");
  }
  $formatNumberScale = "formatNumberScale='$formatNumberScale'";

  $decimals = reports_get_style($graphDesign, "decimals");
  if (length($decimals) > 0)
  {
    $decimals = "decimals='$decimals'";
  }
  else
  {
    $decimals = "";
  }

  $placeValuesInside = reports_get_style($graphDesign, "placeValuesInside");
  if (length($placeValuesInside) < 1)
  {
    $placeValuesInside = reports_chart_design_default("placeValuesInside");
  }
  $placeValuesInside = "placeValuesInside='$placeValuesInside'";

  $rotateValues = reports_get_style($graphDesign, "rotateValues");
  if (length($rotateValues) < 1)
  {
    $rotateValues = reports_chart_design_default("rotateValues");
  }
  $rotateValues = "rotateValues='$rotateValues'";

  $valueFontSize = reports_get_style($graphDesign, "valueFontSize");
  if (length($valueFontSize) < 1)
  {
    $valueFontSize = reports_chart_design_default("valueFontSize");
  }
  $minPlotHeightForValue = "minPlotHeightForValue='$valueFontSize'";
  $valueFontSize = "valueFontSize='$valueFontSize'";

  $valueFont = reports_get_style($graphDesign, "valueFont");
  if (length($valueFont) > 3)
  {
    $valueFont = "valueFont='$valueFont'";
  }

  $showValuesBg = reports_get_style($graphDesign, "showValuesBg");
  if ($showValuesBg > 0)
  {
    $valueBgColor = reports_get_style($graphDesign, "valueBgColor");
    $valueBgColor = "valueBgColor='$valueBgColor'";
    $valueBgAlpha = reports_get_style($graphDesign, "valueBgAlpha");
    $valueBgAlpha = 100 - $valueBgAlpha;
    $valueBgAlpha = "valueBgAlpha='$valueBgAlpha'";
  }

  #handle dual-Y specific graph options
  if (($graphType eq "DualY") || ($graphType eq "ColumnLine"))
  {
    $comboSecondaryAxisStr = reports_get_style($graphDesign, "comboSecondaryAxis");
    @tmp = split('-', $comboSecondaryAxisStr);
    foreach $id (@tmp)
    {
      $comboSecondaryAxis{$id} = 1;
    }

    $comboGraphAsStr = reports_get_style($graphDesign, "comboGraphAsLine");
    @tmp = split('-', $comboGraphAsStr);
    foreach $id (@tmp)
    {
      $comboGraphAs{$id} = "line";
    }
    $comboGraphAsStr = reports_get_style($graphDesign, "comboGraphAsColumn");
    @tmp = split('-', $comboGraphAsStr);
    foreach $id (@tmp)
    {
      $comboGraphAs{$id} = "column";
    }
    $comboGraphAsStr = reports_get_style($graphDesign, "comboGraphAsArea");
    @tmp = split('-', $comboGraphAsStr);
    foreach $id (@tmp)
    {
      $comboGraphAs{$id} = "area";
    }
  }

  #handle "round edges" styling
  $useRoundEdges = reports_get_style($graphDesign, "useRoundEdges");

  #if the titles have expandable tag(s), expand them
  $caption = reports_expand_dim_tags($db, $dsSchema, $caption, $productIDstring, $geographyIDstring, $timeIDstring, $measureIDstring);
  $subcaption = reports_expand_dim_tags($db, $dsSchema, $subcaption, $productIDstring, $geographyIDstring, $timeIDstring, $measureIDstring);

  #escape "special" characters
  $caption =~ s/\'//g;
  $subcaption =~ s/\'//g;

  #get our measure format info, and apply as much of it as we can
  #NB: we want to make sure we correctly format anything graphed against a
  #    secondary axis, so we cycle through this hash to find the first non-
  #    secondary measure
  foreach $id (@measureIDs)
  {
    if (!($comboSecondaryAxis{$id}))
    {
      $measureID = $id;
      last;
    }
  }
  $formatStr = DSRmeasures_get_format($db, $dsSchema, $measureID);
  @formats = split(',', $formatStr);
  if (length($decimals < 1))
  {
    $decimals = "decimals='$formats[0]'"
  }
  $formatNumber = "formatNumber='$formats[1]'";
  $numberPrefix = "";
  $numberSuffix = "";
  if ($formats[2] == 1)
  {
    $numberPrefix = "numberPrefix='\$'";
  }
  elsif ($formats[2] == 2)
  {
    $numberSuffix = "numberSuffix='%'";
  }

  #now we want to format measures plotted against the secondary axis of a
  #dual-y graph (if we have any)
  $measureID = 0;
  foreach $id (@measureIDs)
  {
    if ($comboSecondaryAxis{$id} > 0)
    {
      $measureID = $id;
      last;
    }
  }
  if ($measureID > 0)
  {
    $formatStr = DSRmeasures_get_format($db, $dsSchema, $measureID);
    @formats = split(',', $formatStr);
    $sDecimals = "sDecimals='$formats[0]'";
    $sFormatNumber = "sFormatNumber='$formats[1]'";
    $sNumberPrefix = "";
    $sNumberSuffix = "";
    if ($formats[2] == 1)
    {
      $sNumberPrefix = "sNumberPrefix='\$'";
    }
    elsif ($formats[2] == 2)
    {
      $sNumberSuffix = "sNumberSuffix='%'";
    }
  }

  # ------- end of design parsing ----------


  #print out the XML chart info
  print <<XML_LABEL;
<chart
  theme='zune'
  animation='0'

  $bgColor
  $canvasBgColor
  canvasBgAlpha='0'

  $showBorder
  $borderColor
  $borderThickness

  $showLegend
  $legendPosition
  $legendItemFontColor
  $legendItemFont
  $legendItemFontSize

  caption='$caption'
  $captionFontColor
  $captionAlignment
  $captionFontSize
  $captionFont

  subcaption='$subcaption'

  xAxisName='$xAxisName'
  $xAxisNameFontSize
  yAxisName='$yAxisName'
  $yAxisNameFontSize
  useRoundEdges='$useRoundEdges'
  numVDivLines='10'
  divLineAlpha='30'
  labelPadding ='10'
  yAxisValuesPadding ='10'

  $showLabels
  $labelFontColor
  $labelDisplay
  $labelStep
  $labelFontSize
  $labelFont

  $showValues
  $valueFontColor
  $formatNumberScale
  $decimals
  $placeValuesInside
  $rotateValues
  $valueFontSize
  $minPlotHeightForValue
  $valueFont
  $valueBgColor
  $valueBgAlpha

  $formatNumber
  $numberPrefix
  $numberSuffix
  $sDecimals
  $sFormatNumber
  $sNumberPrefix
  $sNumberSuffix>
XML_LABEL

  #if the user has specified a slicer, let's add it to the where clause
  $doResultsFilter = 0;
  if ($graph_x eq "p")
  {
    @slicers = split(',', $slicersStr);
    foreach $slicer (@slicers)
    {
      $slicer =~ m/(PSEG_\d+):(\d+)/;
      $segID = $1;
      $segmentID = $2;

      if ($segmentID > 0)
      {
        $tmp = "SMT_$segmentID";
        $segmentName = $db->quote($categoryNames{$tmp});
        $whereClause = $whereClause . " AND $segID = $segmentName";
      }
    }

    $doResultsFilter = 1;
  }

  ### chart filtering implementation ###

  $filterMeas1 = reports_get_style($graphDesign, "filterMeas1");
  $filterOp1 = reports_get_style($graphDesign, "filterOp1");
  $filterNum1 = reports_get_style($graphDesign, "filterNum1");

  if (($filterMeas1 > 0) && (length($filterNum1) > 0))
  {
    $filterMeas1 = "measure_" . $filterMeas1;

    if ($filterOp1 eq "gt")
    {
      $filterOp1 = ">";
      $whereClause .= " AND $filterMeas1 > $filterNum1";
    }
    elsif ($filterOp1 eq "lt")
    {
      $filterOp1 = "<";
      $whereClause .= " AND $filterMeas1 < $filterNum1";
    }
    elsif ($filterOp1 eq "eq")
    {
      $filterOp1 = "=";
      $whereClause .= " AND $filterMeas1 = $filterNum1";
    }

    elsif ($filterOp1 eq "top")
    {

      #gate the number of top items we're going to show
      if ($filterNum1 > 5000)
      {
        $filterNum1 = 5000;
      }
      elsif ($filterNum1 < 0)
      {
        $filterNum1 = 1;
      }

      $whereClause .= " AND NOT ISNULL($filterMeas1)";
      $whereClause .= " ORDER BY $filterMeas1 DESC LIMIT $filterNum1";
    }

    elsif ($filterOp1 eq "bottom")
    {

      #gate the number of top items we're going to show
      if ($filterNum1 > 5000)
      {
        $filterNum1 = 5000;
      }
      elsif ($filterNum1 < 0)
      {
        $filterNum1 = 1;
      }

      $whereClause .= " AND NOT ISNULL($filterMeas1)";
      $whereClause .= " ORDER BY $filterMeas1 ASC LIMIT $filterNum1";
    }

    $doResultsFilter = 1;
  }

  ### end chart filtering code ###

  #if a filter or slicer needs to be applied, re-fetch our categories
  if ($doResultsFilter == 1)
  {
    $itemStr = "";
    foreach $item (@categoryIDs)
    {
      $itemStr .= "'$item',";
    }
    chop($itemStr);

    #re-fetch our categories now that filtering can be applied
    if ($graph_x eq "p")
    {
      $query = "SELECT product FROM $dsSchema.$rptCube \
          WHERE product IN ($itemStr) AND geography=$q_geography AND time=$q_time $whereClause";
    }
    elsif ($graph_x eq "g")
    {
      $query = "SELECT geography FROM $dsSchema.$rptCube \
          WHERE product = $q_product AND geography IN ($itemStr) AND time=$q_time $whereClause";
    }
    elsif ($graph_x eq "t")
    {
      $query = "SELECT time FROM $dsSchema.$rptCube \
          WHERE product = $q_product AND geography=$q_geography AND time IN ($itemStr) $whereClause";
    }

    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    undef(@categoryIDs);
    while (($item) = $dbOutput->fetchrow_array)
    {
      push(@categoryIDs, $item);
    }
  }

  print("<categories>\n");

  #handle empty data set
  if (scalar(@categoryIDs) < 1)
  {
    print(" <category label='(No data to display)' />\n");
  }

  foreach $item (@categoryIDs)
  {
    print(" <category label='$categoryNames{$item}' />\n");
  }

  print("</categories>\n\n");

  #get the default color palette we're going to use for the chart
  @colors = reports_graph_color_array;

  #output the data sets
  $firstSeries = 1;
  $colorIdx = 0;
  foreach $setID (@setIDs)
  {
    $color = $colors[$colorIdx];

    #see if we have a custom color, and use it in preference to the default
    $name = "color_" . $setID;
    $customColor = reports_get_style($graphDesign, $name);
    if (length($customColor) > 6)
    {
      $color = $customColor;
    }

    if ($comboSecondaryAxis{$setID} == 1)
    {
      $parentyaxis = "parentyaxis='S'";
    }
    else
    {
      $parentyaxis = "";
    }

    if (defined($comboGraphAs{$setID}))
    {
      $renderas = "renderas='$comboGraphAs{$setID}'";
    }
    else
    {
      $renderas = "";
    }

    $setNames{$setID} =~ s/\'//g;
    print("<dataset seriesName='$setNames{$setID}' $parentyaxis $renderas color='$color'>\n");

    #set value for y-axis dimension
    if ($graph_y eq "p")
    {
      $q_product = $db->quote($setID);
    }
    elsif ($graph_y eq "g")
    {
      $q_geography = $db->quote($setID);
    }
    elsif ($graph_y eq "t")
    {
      $q_time = $db->quote($setID);
    }

    #display all measures if they're graph_y, otherwise only display the first
    if ($graph_y eq "m")
    {
      $measureCol = "measure_" . $setID;
    }
    else
    {
      $measureCol = "measure_" . $measureIDs[0];
    }

    #build up the SQL string we're going to use for selections
    $itemStr = "";
    foreach $item (@categoryIDs)
    {
      $itemStr .= "'$item',";
    }
    chop($itemStr);

    #set up our item/value query based on the display dimension
    if ($graph_x eq "p")
    {
      $query = "SELECT $measureCol FROM $dsSchema.$rptCube \
          WHERE product IN ($itemStr) AND geography=$q_geography AND time=$q_time ORDER BY FIELD(product, $itemStr)";
    }
    elsif ($graph_x eq "g")
    {
      $query = "SELECT $measureCol FROM $dsSchema.$rptCube \
          WHERE product=$q_product AND geography IN ($itemStr) AND time=$q_time ORDER BY FIELD(geography, $itemStr)";
    }
    elsif ($graph_x eq "t")
    {
      $query = "SELECT $measureCol FROM $dsSchema.$rptCube \
          WHERE product=$q_product AND geography=$q_geography AND time IN ($itemStr) ORDER BY FIELD (time, $itemStr)";
    }

    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);

    #cycle through the results and output them to the graph
    while (($value) = $dbOutput->fetchrow_array)
    {
      print("<set value='$value' />\n");
    }

    print("</dataset>\n");

    $colorIdx++;
    if ($colorIdx > 47)
    {
      $colorIdx = 0;
    }
  }

  print("</chart>\n");


#EOF
