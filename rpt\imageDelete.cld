#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Delete Background Image</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$("#btn-submit").prop('disabled', true);
  \$("#btn-submit").text("Please Wait...");
  return(true);
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/rpt/main">Reports</A></LI>
    <LI CLASS="breadcrumb-item active">Delete Background Image</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------


  #initialize the CGI variable parser and get the forecast to be deleted
  $q = new CGI;
  $dsID = $q->param('ds');
  $rptID = $q->param('rpt');
  $imageType = $q->param('t');
  $imageID = $q->param('id');
  $action = $q->param('a');

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  $db = KAPutil_connect_to_database();

  ###### Delete Image Background #####

  if ($action eq "d")
  {

    #make sure it's an authorized delete request
    $query = "SELECT userID FROM app.reports_backgrounds WHERE ID=$imageID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($imageOwnerID) = $dbOutput->fetchrow_array;

    if (($imageOwnerID != $userID) && ($acctType < 5))
    {
      print_html_header();
      exit_error("You don't have privileges to delete this image.");
    }

    #remove the image from any reports where it's used, and log audit entry
    $query = "SELECT ID FROM app.cubes WHERE background=$imageID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($id) = $dbOutput->fetchrow_array)
    {
      $query = "UPDATE app.cubes SET background=NULL WHERE ID=$rptID";
      $status = $db->do($query);
      KAPutil_handle_db_err($db, $status, $query);
      utils_audit($db, $userID, "Removed report background", $dsID, $id, 0);
    }

    $query = "DELETE FROM app.reports_backgrounds WHERE ID=$imageID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/rpt/rptBackground.cld?ds=$dsID&rpt=$rptID\n\n");
    exit;
  }


  ##### Done Deleting Image Background #####

  #make sure we're the owner of the image being deleted
  $query = "SELECT userID FROM app.reports_backgrounds WHERE ID=$imageID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($imageOwnerID) = $dbOutput->fetchrow_array;

  print_html_header();

  if (($imageOwnerID != $userID) && ($acctType < 5))
  {
    exit_error("You don't have privileges to delete this image.");
  }


  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-10 col-lg-7 col-xl-6"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Delete Background Image</DIV>
        <DIV CLASS="card-body">

          <P>
          <CENTER>
            <IMG SRC="imageRetrieve.cld?t=b&id=$imageID&w=150" WIDTH="150px;" CLASS="border" TITLE="">
          </CENTER>

          <P>
          <FORM METHOD="post" ACTION="/app/rpt/imageDelete.cld" onsubmit="return checkForm(this);">
          <INPUT TYPE="hidden" NAME="rpt" VALUE="$rptID">
          <INPUT TYPE="hidden" NAME="t" VALUE="$imageType">
          <INPUT TYPE="hidden" NAME="id" VALUE="$imageID">
          <INPUT TYPE="hidden" NAME="a" VALUE="d">
          Are you sure you want to delete this report background image?
          It'll be removed from these reports:
          <P>
          <TABLE CLASS="table table-striped table-sm table-bordered mx-auto">
END_HTML

  #get the selected report names from the database
  $query = "SELECT name FROM app.cubes WHERE background = $imageID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  if ($status < 1)
  {
    print("<TR><TD><I>(none)</I></TD></TR>\n");
  }
  while (($rptName) = $dbOutput->fetchrow_array)
  {
    print("<TR><TD>$rptName</TD></TR>\n");
  }

  print <<END_HTML;
          </TABLE>

          <P>&nbsp;</P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="location.href='rptBackground.cld?ds=$dsID&rpt=$rptID'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-danger" TYPE="SUBMIT" ID="btn-submit"><I CLASS="bi bi-trash"></I> Delete Image</BUTTON>
          </DIV>
          </FORM>

        </DIV>
      </DIV>


    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
