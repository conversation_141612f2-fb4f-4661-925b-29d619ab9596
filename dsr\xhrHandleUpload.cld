#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;
use File::Copy;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  print("Content-type: text/html\n\n");

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  $key = $q->url_param('key');

  $db = KAPutil_connect_to_database();

  #get the uploaded file and move it to our temp directory
  @uploadedfiles = $q->param('files[]');
  foreach $file (@uploadedfiles)
  {

    $tmpName = $q->tmpFileName($file);

    #escape weird characters from filename
    $file =~ s/\&//g;
    $file =~ s/\"//g;
    $file =~ s/\'//g;
    $file =~ s/\,//g;
    $file =~ s/\$//g;

    copy($tmpName, "/opt/apache/app/tmp/$userID.$key.$file");

  print <<JSON_LABEL;
{
"files": [
  {
    "name": "$file"
  }
 ]
}
JSON_LABEL
  }

#EOF
