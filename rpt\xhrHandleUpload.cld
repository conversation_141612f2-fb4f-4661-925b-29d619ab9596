#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;
use File::Copy;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  $dsID = $q->url_param('ds');
  $rptID = $q->url_param('rpt');

  $db = KAPutil_connect_to_database();

  #get the uploaded file and load it into the database
  $fileName = $fileHandle = $q->param('bgFile');
  binmode($fileHandle);
  while (read($fileHandle, $block, 2048))
  {
    $fileData .= $block;
  }

  #if we received valid data, insert it into the database as a BLOB
  if (length($fileData) > 64)
  {
    $dbHandle = $db->prepare('INSERT INTO app.reports_backgrounds (userID, name, img) VALUES (?,?,?)');
    $dbHandle->execute($userID, $fileName, $fileData);
    $dbHandle->finish;
  }

  print("Status: 302 Moved temporarily\n");
  print("Location: $Lib::KoalaConfig::kapHostURL/app/rpt/rptBackground.cld?rpt=$rptID&ds=$dsID\n\n");

#EOF
