#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;
use Statistics::R;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::DataSources;
use Lib::AInsights::AInsights;
use Lib::AInsights::Utils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#format currency for pretty HTML output
sub html_format_currency
{
  my ($tmp, $formatStr, $negative);

  ($value) = @_;


  #determine if we're a negative number or not
  $negative = 0;
  if ($value < 0)
  {
    $negative = 1;
  }

  #now that we know if we're negative, just work with absolute value
  $formatStr = abs($value);

  #go down to 2 decimal places
  $formatStr = sprintf("%.2f", $formatStr);

  #commify the output
  $tmp = reverse($formatStr);
  $tmp =~ s/(\d\d\d)(?=\d)(?!\d*\.)/$1,/g;
  $formatStr = scalar(reverse($tmp));

  if ($value < 0)
  {
    $formatStr = "<SPAN STYLE='color:red;'>(\$$formatStr)</SPAN>";
  }
  else
  {
    $formatStr = "\$$formatStr";
  }

  return($formatStr);
}



#-------------------------------------------------------------------------

#format number for pretty HTML output
sub html_format_number
{
  my ($tmp);

  ($value, $decimals, $redNegative) = @_;


  if (!defined($value))
  {
    return;
  }

  #go down to 2 decimal places
  $formatStr = "%." . $decimals . "f";
  $value = sprintf($formatStr, $value);

  #commify the output
  $tmp = reverse($value);
  $tmp =~ s/(\d\d\d)(?=\d)(?!\d*\.)/$1,/g;
  $value = scalar(reverse($tmp));

  if (($redNegative == 1) && ($value < 0))
  {
    $value = abs($value);
    $formatStr = "<SPAN STYLE='color:red;'>($value)</SPAN>";
  }
  else
  {
    $formatStr = "$value";
  }

  return($formatStr);
}



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: AInsights Elasticity Item Details</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/fontawesome-5.10.2/css/all.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<SCRIPT SRC="/fusioncharts-3.18.0/js/fusioncharts.js"></SCRIPT>
<SCRIPT SRC="/fusioncharts-3.18.0/js/themes/fusioncharts.theme.zune.js"></SCRIPT>

<SCRIPT>
let vpHeight = window.innerHeight - 50;
if (vpHeight < 400)
{
  vpHeight = 400;
}

\$(function()
{
  const optManualPrice = "$OPT_manualPrice";
  if (optManualPrice.length > 0)
  {
    let optPriceGraphDiv = document.getElementById("graph-optimized-column");
    optPriceGraphDiv.scrollIntoView(false);
  }
});
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;

<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">AInsights</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="elasticity.cld?pm=$priceModelID">$elasticName</A></LI>
    <LI CLASS="breadcrumb-item active">Model Details</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');
  $priceModelID = $q->param('pm');
  $prodID = $q->param('p');
  $geoID = $q->param('g');
  $OPT_manualPrice = $q->param('price');

  AInsights_Utils_initialize_constants($priceModelID);

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsSchema = "datasource_" . $dsID;

  $elasticName = AInsights_ID_to_name($db, $priceModelID);
  $dsName = ds_id_to_name($db, $dsID);

  %prodNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "p");
  %geoNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "g");
  %timeNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "t");

  print_html_header();

  #make sure we have at least read privs for this pricing model
  $privs = AInsights_rights($db, $userID, $priceModelID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to view this pricing model.");
  }

  #grab summary elasticity info for this prod/geo combination
  $query = "SELECT elasticity, intercept, interceptConfidence, priceCoef, priceConfidence, distCoef, distConfidence, promoCoef, promoConfidence, R2, pStatistic, modelConfidence, status \
      FROM $dsSchema.$AInsightsItemTable \
      WHERE productID='$prodID' AND geographyID=$geoID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($avgElasticity, $intercept, $interceptConfidence, $priceCoef, $priceConfidence, $distCoef, $distConfidence, $promoCoef, $promoConfidence, $R2, $pValue, $modelConfidence, $statusText) = $dbOutput->fetchrow_array;

  $maxObservedPrice = 0;
  $minObservedPrice = 1000;
  $query = "SELECT units, avgPrice, dist, promo FROM $dsSchema.$AInsightsItemCube \
      WHERE productID='$prodID' AND geographyID=$geoID AND outlier=0";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  while (($units, $price, $dist, $promo) = $dbOutput->fetchrow_array)
  {
    $weekCount++;
    $priceJSON .= "{'x': '$price', 'y': '$units'},\n";
    $distJSON .= "{'x': '$dist', 'y': '$units'},\n";

    if ($promo > 0)
    {
      $promoJSON .= "{'x': '$promo', 'y': '$units'},\n";
    }

    #keep track of our lowest/higest price for use in optimizer calculations
    if ($price > $maxObservedPrice)
    {
      $maxObservedPrice = $price;
    }
    if (($price > 0) && ($price < $minObservedPrice))
    {
      $minObservedPrice = $price;
    }

    #sum up totals for unit sales & distribution to use for average values below
    $totalUnits += $units;
    $totalDist += $dist;
  }
  chop($priceJSON);   chop($priceJSON);
  chop($distJSON);   chop($distJSON);
  chop($promoJSON);   chop($promoJSON);

  $avgUnits = $totalUnits / $weekCount;
  $avgDist = $totalDist / $weekCount;

  $priceCoefAbs = abs($priceCoef);
  $dispPriceCoef = html_format_number($priceCoef, 1);
  $dispPriceCoefAbs = html_format_number($priceCoefAbs, 1);


  ####################### AI Conclusions #############################


  #set some general parameters that we use to decide what to display
  if ($modelConfidence  < 1)
  {
    $AI_noConfidenceModel = 1;
  }
  elsif ($modelConfidence < 2)
  {
    $AI_lowConfidenceModel = 1;
  }

  if ($priceConfidence < 1)
  {
    $AI_noPriceRelationship = 1;
  }
  elsif ($priceConfidence < 2)
  {
    $AI_weakPriceRelationship = 1;
  }

  if ($priceCoef > 0)
  {
    $AI_noPriceRelationship = 1;
  }

  if ($statusText eq "Zeroed elasticity")
  {
    $AI_zeroedElasticity = 1;
    $avgElasticity = 0;
    $AI_noPriceRelationship = 0;
    $priceCoef = "NA";
  }

  #if the model is based on problematic data, caution the user appropriately
  if ($avgUnits < 100)
  {
    push(@AI_cautions, "a very low sales rate");
  }
  if ($avgDist < 5)
  {
    push(@AI_cautions, "a very low distribution");
  }
  if ($maxObservedPrice - $minObservedPrice <= 0.02)
  {
    push(@AI_cautions, "a very small variance in price");
  }

  if (@AI_cautions > 0)
  {
    $AI_cautionsText = join(', ', @AI_cautions);
    if (@AI_cautions > 1)
    {
      $AI_cautionsText =~ m/^(.*\,)(.*)$/;
      $AI_cautionsText = "$1 and $2";
    }

    $AI_cautionText = <<END_HTML;
    <DIV ID="warn-confidence" CLASS="alert alert-warning">
      <DIV><STRONG>CAUTION: </STRONG>This product has $AI_cautionsText so the results of this model should be used carefully.</DIV>
    </DIV>
END_HTML
  }

  $avgElasticity = html_format_number($avgElasticity, 2);

  #determine a human-readable level of elasticity
  if (abs($avgElasticity) <= 0.75)
  {
    $AI_elasticityLevel = "This product is <SPAN STYLE='background:#00AEEF;' CLASS='text-white'>inelastic</SPAN> in this market, ";
    $elasticityExplanation = "You have a lot of pricing power, but be cautious of increasing prices too much.";
  }
  elsif (abs($avgElasticity) <= 1.25)
  {
    $AI_elasticityLevel = "This product is <SPAN STYLE='background:#8DC63F;' CLASS='text-white'>moderately inelastic</SPAN> in this market, ";
    $elasticityExplanation = "You have some pricing power, but be sure to consider your competitive situation and external factors.";
  }
  elsif (abs($avgElasticity) <= 1.75)
  {
    $AI_elasticityLevel = "This product is <SPAN STYLE='background:#FFB100;' CLASS='text-white'>moderately elastic</SPAN> in this market, ";
    $elasticityExplanation = "Increasing prices tends to increase profits, but at the expense of a noticeable amount of volume.";
  }
  elsif (abs($avgElasticity) <= 2.25)
  {
    $AI_elasticityLevel = "This product is <SPAN STYLE='background:#B21DAC;' CLASS='text-white'>elastic</SPAN> in this market, ";
    $elasticityExplanation = "Be very careful about increasing prices, since that will lead to significant volume loss.";
  }
  else
  {
    $AI_elasticityLevel = "This product is <SPAN STYLE='background:#DC0015;' CLASS='text-white'>very elastic</SPAN> in this market, ";
    $elasticityExplanation = "Any price increase is going to lead to substantial volume loss - a price decrease might be an option.";
  }

  $AI_elasticityLevel .= <<END_HTML;
  with an average elasticity of <B>$avgElasticity</B>.
  $elasticityExplanation
  </A>
END_HTML

  $AI_elasticityStatement = <<END_HTML;
  <P>
    $AI_elasticityLevel
  </P>
END_HTML

  #if we're warning the user that there's no price/sales relationship, we
  #shouldn't tell them there's an elasticity
  if (($AI_noPriceRelationship) || ($AI_noConfidenceModel))
  {
    $AI_elasticityStatement = "";
  }

  #create the HTML to display a linear guage for the elasticity
  $elasticityGaugeHTML = <<END_HTML;
<DIV ID="graph-elasticity-gauge"></DIV>
<SCRIPT>
FusionCharts.ready(function()
{
  let elasticityGauge = new FusionCharts(
  {
    type: 'hlineargauge',
    renderAt: 'graph-elasticity-gauge',
    width: '100%', height: '130',
    dataFormat: 'json',
    dataSource:
    {
      "chart":
      {
        "animation": "0",
        "lowerLimit": "-3",
        "upperLimit": "0",
        "valueAbovePointer": "1",
        "showShadow": "0",
        "gaugeFillMix": "{light}",
        'baseFontColor': '#ffffff',
        'labelFontColor': '#333333',
        'valueFontColor': '#333333',
        'baseFontSize': '12',
        "theme": "zune"
      },
      "colorRange":
      {
        "color": [
        {
          "minValue": "-0.75",
          "maxValue": "0",
          "label": "Inelastic",
          "code": "#00AEEF"
        },
        {
          "minValue": "-1.25",
          "maxValue": "-0.75",
          "label": "Moderately Inelastic",
          "code": "#8DC63F"
        },
        {
          "minValue": "-1.75",
          "maxValue": "-1.25",
          "label": "Moderately Elastic",
          "code": "#FFB100"
        },
        {
          "minValue": "-2.25",
          "maxValue": "-1.75",
          "label": "Elastic",
          "code": "#B21DAC"
        },
        {
          "minValue": "-3",
          "maxValue": "-2.255",
          "label": "Very Elastic",
          "code": "#DC0015"
        }]
      },
      "pointers":
      {
        "pointer": [
        {
          "value": "$avgElasticity"
        } ]
      }
    }
  });
  elasticityGauge.render();
});
</SCRIPT>
END_HTML

  #provide some human-readable commentary about the model quality
  @modelConfidenceHR = AInsights_confidence_to_HR($modelConfidence);
  if ($modelConfidence == 3)
  {
    $AI_modelConfidence = "<SPAN CLASS='bg-success text-white'>high confidence</SPAN>";
    $modelConfidenceCardBG = "bg-success";
    $modelConfidenceCardText = "high confidence";
  }
  elsif ($modelConfidence == 2)
  {
    $AI_modelConfidence = "<SPAN CLASS='bg-success text-white'>moderate confidence</SPAN>";
    $modelConfidenceCardBG = "bg-success";
    $modelConfidenceCardText = "moderate confidence";
  }
  elsif ($modelConfidence == 1)
  {
    $AI_modelConfidence = "<SPAN CLASS='bg-warning'>low confidence</SPAN>";
    $modelConfidenceCardBG = "bg-warning";
    $modelConfidenceCardText = "low confidence";
  }
  else
  {
    $AI_modelConfidence = "<SPAN CLASS='bg-danger'>no confidence</SPAN>";
    $modelConfidenceCardBG = "bg-danger";
    $modelConfidenceCardText = "no confidence";
  }

  @priceConfidenceHR = AInsights_confidence_to_HR($priceConfidence);
  if ($priceConfidence >= 2)
  {
    $AI_priceConfidence = "<SPAN CLASS='bg-success text-white'>$priceConfidenceHR[1] relationship</SPAN>";
  }
  elsif ($priceConfidence == 1)
  {
    $AI_priceConfidence = "<SPAN CLASS='bg-warning'>$priceConfidenceHR[1] relationship</SPAN>";
  }
  else
  {
    $AI_priceConfidence = "$priceConfidenceHR[1] relationship";
  }

  if ($priceConfidence > 0)
  {
    $AI_priceConfidenceStatement = <<END_HTML;
<P>
The statistical model has detected a $AI_priceConfidence
between price and unit sales in the available data.
</P>
END_HTML
  }
  else
  {
    $AI_priceConfidenceStatement = <<END_HTML;
<P>
The statistical model carefully examined the available data for a relationship between pricing
and unit sales but wasn't able to find one.
</P>
END_HTML
  }

  $AI_modelConfidenceStatement = "This elasticity value is derived from a $AI_modelConfidence statistical model.";
  $AI_priceScatterTrendLine = 1;

  #if the model isn't statistically valid, let the user know
  if ($AI_noConfidenceModel)
  {
    $AI_modelConfidenceStatement = "There doesn't appear to be any way to construct an accurate model from the available data. ";
    $AI_modelConfidenceStatement .= "That doesn't imply that the product is elastic or inelastic, just that it isn't possible to draw any conclusions from this specific data set. ";

    $AI_noElasticityGraph = 1;
    $AI_priceScatterTrendLine = 0;
  }

  #if the model can't find a relationship between price/sales, let the user know
  elsif ($AI_noPriceRelationship)
  {
    $AI_modelConfidenceStatement = "This $AI_modelConfidence statistical model indicates that there <SPAN CLASS='bg-danger text-white'>doesn't appear to be any detectable relationship between price and unit sales</SPAN> in the available data. ";
    $AI_modelConfidenceStatement .= "Without being able to establish that relationship, the product's elasticity can't be calculated.";

    $AI_noElasticityGraph = 1;
    $AI_priceScatterTrendLine = 0;
  }

  #if the model is invalid, try to provide some helpful suggestions why
  if (($AI_noConfidenceModel) || ($AI_noPriceRelationship))
  {

    #grab some data quality info from the elasticity model cube
    $query = "SELECT COUNT(timeID), MIN(avgPrice), MAX(avgPrice), AVG(dist) \
        FROM $dsSchema.$AInsightsItemCube \
        WHERE productID='$prodID' AND geographyID=$geoID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($AI_weeksOfData, $AI_minPrice, $AI_maxPrice, $AI_avgDistribution) = $dbOutput->fetchrow_array;

    #if we could stand to have some more data points
    if ($AI_weeksOfData < 40)
    {
      $AI_invalidModelReasons .= "<LI>Only $AI_weeksOfData weeks of sales data are available - 52 or more weeks might allow a valid model to be constructed.\n";
    }

    #if distribution is low
    if ($AI_avgDistribution < 10)
    {
      $AI_invalidModelReasons .= "<LI>This product appears to have a very low level of distribution in this geography.\n";
    }

    #if there isn't much variation in price
    $AI_avgPriceDifference = $AI_maxPrice - $AI_minPrice;
    $AI_avgPriceVariance = $AI_avgPriceDifference / $AI_maxPrice;
    if ($AI_avgPriceDifference < 0.05)
    {
      $AI_avgPriceDifferenceHR = html_format_currency($AI_avgPriceDifferenceHR);
      $AI_invalidModelReasons .= "<LI>The average price of this product in this geography doesn't vary by a noticeable amount ($AI_avgPriceDifferenceHR).\n";
    }

    if (length($AI_invalidModelReasons) > 0)
    {
      $AI_invalidModelReasons = <<END_HTML;
<P>
Reasons why a good statistical model can't be built from this data set might include:
<UL>
  $AI_invalidModelReasons
</UL>
</P>
END_HTML
    }
  }

  #if the model is good, and shows no relationship between price and sales
  if ($AI_zeroedElasticity)
  {
    $AI_priceScatterTrendLine = 0;
    $AI_invalidModelReasons = "";
  }

  #add commentary about the impact of distribution on the model
  @distConfidenceHR = AInsights_confidence_to_HR($distConfidence);
  if ($distConfidence >= 2)
  {
    $AI_distConfidence = "<SPAN CLASS='bg-success text-white'>$distConfidenceHR[1] relationship</SPAN>";
    $AI_distScatterTrendLine = 1;
  }
  elsif ($distConfidence == 1)
  {
    $AI_distConfidence = "<SPAN CLASS='bg-warning'>$distConfidenceHR[1] relationship</SPAN>";
    $AI_distScatterTrendLine = 1;
  }
  else
  {
    $AI_distConfidence = "$distConfidenceHR[1] relationship";
    $AI_distScatterTrendLine = 0;
  }

  if ($distConfidence > 0)
  {
    $AI_distConfidenceStatement = <<END_HTML;
<P>
The statistical model has detected a $AI_distConfidence
between distribution levels and unit sales in the available data. The impact of distribution
on unit sales has been mathematically adjusted out of the pricing model.
</P>
END_HTML
  }
  else
  {
    $AI_distConfidenceStatement = <<END_HTML;
<P>
The statistical model carefully examined the available data for a relationship between distribution levels
and unit sales but wasn't able to find one.
</P>
END_HTML
  }

  #if the model isn't statistically valid, then we don't actually have anything to say
  if ($AI_noConfidenceModel)
  {
    $AI_distConfidenceStatement = "";
  }

  #add commentary about the impact of promotion on the model
  @promoConfidenceHR = AInsights_confidence_to_HR($promoConfidence);
  if ($promoConfidence >= 2)
  {
    $AI_promoConfidence = "<SPAN CLASS='bg-success text-white'>$promoConfidenceHR[1] relationship</SPAN>";
    $AI_promoScatterTrendLine = 1;
  }
  elsif ($promoConfidence == 1)
  {
    $AI_promoConfidence = "<SPAN CLASS='bg-warning'>$promoConfidenceHR[1] relationship</SPAN>";
    $AI_promoScatterTrendLine = 1;
  }
  else
  {
    $AI_promoConfidence = "$promoConfidenceHR[1] relationship";
    $AI_promoScatterTrendLine = 0;
  }

  if ($promoConfidence > 0)
  {
    $AI_promoConfidenceStatement = <<END_HTML;
<P>
The statistical model has detected a $AI_promoConfidence
between non-price promotions and unit sales in the available data. The impact of promotion
on unit sales has been mathematically adjusted out of the pricing model.
</P>
END_HTML
  }
  else
  {
    $AI_promoConfidenceStatement = <<END_HTML;
<P>
The statistical model carefully examined the available data for a relationship between non-price promotions
and unit sales but wasn't able to find one.
</P>
END_HTML
  }

  #if the model isn't statistically valid, then we don't actually have anything to say
  if ($AI_noConfidenceModel)
  {
    $AI_promoConfidenceStatement = "";
  }

  ###################### END AI Conclusions ##########################




  ###################### Begin Optimizer Calculation/AI ################

  #update/retrieve any analyst settings for this product/geo
  if (defined($OPT_manualPrice))
  {
    if ($OPT_manualPrice eq "R")
    {
      $query = "UPDATE $dsSchema.$AInsightsItemTable SET targetPrice=NULL \
          WHERE productID='$prodID' AND geographyID='$geoID'";
      undef($OPT_manualPrice);
    }
    else
    {
      $query = "UPDATE $dsSchema.$AInsightsItemTable SET targetPrice=$OPT_manualPrice \
          WHERE productID='$prodID' AND geographyID='$geoID'";
    }
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);
  }
  else
  {
    $query = "SELECT targetPrice FROM $dsSchema.$AInsightsItemTable \
        WHERE productID='$prodID' AND geographyID='$geoID'";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($OPT_manualPrice) = $dbOutput->fetchrow_array;
  }

  #get the (more or less) average price for the product across all time
  #periods included in the model for the current selections
  $query = "SELECT AVG(avgPrice), MIN(avgPrice), MAX(avgPrice) \
      FROM $dsSchema.$AInsightsItemCube \
      WHERE productID='$prodID' AND geographyID=$geoID AND outlier=0";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($OPT_avgPrice, $OPT_minPrice, $OPT_maxPrice) = $dbOutput->fetchrow_array;

  $OPT_COGS = $OPT_avgPrice * 0.5;
  $OPT_retailerMarginPct = 0.25;

  #grab the profit values from the previous 52 weeks
  @recent52WeekIDs = AInsights_Utils_get_time_period_ids($db, $dsSchema, 52);
  $OPT_recent52TimeIDStr = join(',', @recent52WeekIDs);

  $query = "SELECT units, avgPrice, dist FROM $dsSchema.$AInsightsItemCube \
      WHERE productID='$prodID' AND geographyID=$geoID AND timeID IN ($OPT_recent52TimeIDStr) AND outlier=0";
  $dbOutput = $db->prepare($query);
  $OPT_weeksScanned = $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  while (($units, $avgPrice, $dist) = $dbOutput->fetchrow_array)
  {
    $OPT_weeklyProfit = $units * ($avgPrice - $OPT_COGS);
    $OPT_recent52Profit += $OPT_weeklyProfit;
    $OPT_recent52Dollars += ($units * $avgPrice);
    $OPT_recent52Units += $units;
    $OPT_totalDist += $dist;
  }

  if (($OPT_weeksScanned > 0) && ($OPT_totalDist > 0))
  {
    $OPT_avgDist = $OPT_totalDist / $OPT_weeksScanned;
  }
  else
  {
    $OPT_avgDist = 0;
  }

  $OPT_recent52RetailerProfit = $OPT_recent52Dollars * $OPT_retailerMarginPct;
  $OPT_recent52ManufacturerProfit = $OPT_recent52Profit - $OPT_recent52RetailerProfit;

  #format display-ready versions of variables & calc results
  $OPT_dispAvgPrice = html_format_currency($OPT_avgPrice);
  $OPT_dispMinPrice = html_format_currency($OPT_minPrice);
  $OPT_dispMaxPrice = html_format_currency($OPT_maxPrice);
  $OPT_dispCOGS = html_format_currency($OPT_COGS);

  #if we're dealing with an elastic item
  if ($avgElasticity != 0)
  {

    #NB: we're setting up a function to feed to the optimizer to be maximized.
    #   We want to maximize profit = demand * (price - cogs)
    #   demand = intercept - priceCoef * price + distCoef * avgDist
    #   cogs = 0.5 of original average price
    $priceCoefAbs = abs($priceCoef);
    $OPT_distEffect = $OPT_avgDist * $distCoef;
    $optimumProfitFunc = "profit <- function(price) ";
    $optimumProfitFunc .= "($intercept - ($priceCoefAbs * price) + $OPT_distEffect)";
    $optimumProfitFunc .= " * (price - $OPT_COGS)";

    #$lowerBound = $minObservedPrice - ($minObservedPrice * 0.1);
    $lowerBound = $OPT_COGS;
    $upperBound = $maxObservedPrice + ($maxObservedPrice * 0.1);

    $R = Statistics::R->new();
    $R->startR;
    $R->send("$optimumProfitFunc");
#    print "<PRE>$optimumProfitFunc</PRE>\n";
#    print "<PRE>optimize(profit, lower=$lowerBound, upper=$upperBound, maximum=TRUE)</PRE>\n";
    $R->send("optimize(profit, lower=$lowerBound, upper=$upperBound, maximum=TRUE)");
    $output = $R->read();

    #parse the data returned by R to get our results
    @lines = split(/\n/, $output);
    $lines[1] =~ m/^\[1\] (.*)$/;
    $OPT_optimumPrice = $1;
    $lines[4] =~ m/^\[1\] (.*)$/;
    $OPT_optimizedProfit = $1;

    $OPT_optimumPrice = elasticity_threshold($OPT_optimumPrice, $avgElasticity, $OPT_avgPrice, $OPT_minPrice, $OPT_maxPrice);

    if (defined($OPT_manualPrice))
    {
      $OPT_targetPrice = $OPT_manualPrice;
    }
    else
    {
      $OPT_targetPrice = $OPT_optimumPrice;
    }

    #figure out per-unit price breakdown & percentages
    $OPT_calcParams{elasticity} = $avgElasticity;
    $OPT_calcParams{intercept} = $intercept;
    $OPT_calcParams{priceCoefAbs} = $priceCoefAbs;
    $OPT_calcParams{OPT_distEffect} = $OPT_distEffect;
    $OPT_calcParams{OPT_weeksScanned} = $OPT_weeksScanned;
    $OPT_calcParams{OPT_recent52Units} = $OPT_recent52Units;
    $OPT_calcParams{OPT_recent52Profit} = $OPT_recent52Profit;
    $OPT_calcParams{OPT_targetPrice} = $OPT_targetPrice;
    $OPT_calcParams{OPT_optimumPrice} = $OPT_optimumPrice;
    $OPT_calcParams{OPT_COGS} = $OPT_COGS;
    $OPT_calcParams{OPT_retailerMarginPct} = $OPT_retailerMarginPct;

    %OPT_calcParams = elasticity_opt_calculate_profits(%OPT_calcParams);

    $OPT_unitMargin = $OPT_calcParams{OPT_unitMargin};
    $OPT_retailerUnitMargin = $OPT_calcParams{OPT_retailerUnitMargin};
    $OPT_manufacturerUnitMargin = $OPT_calcParams{OPT_manufacturerUnitMargin};

    $OPT_COGSPct = $OPT_calcParams{OPT_COGSPct};
    $OPT_manufacturerMarginPct = $OPT_calcParams{OPT_manufacturerMarginPct};
    #NB: one way or another, the retailer margin % is already set so we don't
    #   need to calculate it here.

    $OPT_optimizedUnitDemand = $OPT_calcParams{OPT_optimizedUnitDemand};
    $OPT_optimizedDollars = $OPT_calcParams{OPT_optimizedDollars};
    $OPT_optimizedProfit = $OPT_calcParams{OPT_optimizedProfit};

    $OPT_incrUnitDemand = $OPT_calcParams{OPT_incrUnitDemand};
    $OPT_incrUnitDemandPct = $OPT_calcParams{OPT_incrUnitDemandPct};

    $OPT_retailerProfit = $OPT_calcParams{OPT_retailerProfit};
    $OPT_manufacturerProfit = $OPT_calcParams{OPT_manufacturerProfit};

    $OPT_incrProfit = $OPT_calcParams{OPT_incrProfit};
    $OPT_incrRetailerProfit = $OPT_calcParams{OPT_incrRetailerProfit};
    $OPT_incrManufacturerProfit = $OPT_calcParams{OPT_incrManufacturerProfit};

    $recent52ManuNetRev = $OPT_calcParams{recent52ManuNetRev};
    $optManuNetRev = $OPT_calcParams{optManuNetRev};

    $OPT_targetPriceDiff = $OPT_calcParams{OPT_targetPriceDiff};
    $OPT_targetPriceDiffPct = $OPT_calcParams{OPT_targetPriceDiffPct};

    #determine if the optimizer didn't come up with a better value than current
    #pricing
    if (($OPT_incrRetailerProfit <= 0) || ($OPT_incrManufacturerProfit <= 0))
    {
      $OPT_ideallyPriced = 1;
      if ($OPT_targetPrice == $OPT_optimumPrice)
      {
        $OPT_targetPrice = $OPT_avgPrice;
      }
      $OPT_optimumPrice = $OPT_avgPrice;

      #==== TODO refactor this into a common function ========

      $OPT_calcParams{elasticity} = $avgElasticity;
      $OPT_calcParams{intercept} = $intercept;
      $OPT_calcParams{priceCoefAbs} = $priceCoefAbs;
      $OPT_calcParams{OPT_distEffect} = $OPT_distEffect;
      $OPT_calcParams{OPT_weeksScanned} = $OPT_weeksScanned;
      $OPT_calcParams{OPT_recent52Units} = $OPT_recent52Units;
      $OPT_calcParams{OPT_recent52Profit} = $OPT_recent52Profit;
      $OPT_calcParams{OPT_targetPrice} = $OPT_targetPrice;
      $OPT_calcParams{OPT_optimumPrice} = $OPT_optimumPrice;
      $OPT_calcParams{OPT_COGS} = $OPT_COGS;
      $OPT_calcParams{OPT_retailerMarginPct} = $OPT_retailerMarginPct;

      %OPT_calcParams = elasticity_opt_calculate_profits(%OPT_calcParams);

      $OPT_unitMargin = $OPT_calcParams{OPT_unitMargin};
      $OPT_retailerUnitMargin = $OPT_calcParams{OPT_retailerUnitMargin};
      $OPT_manufacturerUnitMargin = $OPT_calcParams{OPT_manufacturerUnitMargin};

      $OPT_COGSPct = $OPT_calcParams{OPT_COGSPct};
      $OPT_manufacturerMarginPct = $OPT_calcParams{OPT_manufacturerMarginPct};

      $OPT_optimizedUnitDemand = $OPT_calcParams{OPT_optimizedUnitDemand};
      $OPT_optimizedDollars = $OPT_calcParams{OPT_optimizedDollars};
      $OPT_optimizedProfit = $OPT_calcParams{OPT_optimizedProfit};

      $OPT_incrUnitDemand = $OPT_calcParams{OPT_incrUnitDemand};
      $OPT_incrUnitDemandPct = $OPT_calcParams{OPT_incrUnitDemandPct};

      $OPT_retailerProfit = $OPT_calcParams{OPT_retailerProfit};
      $OPT_manufacturerProfit = $OPT_calcParams{OPT_manufacturerProfit};

      $OPT_incrProfit = $OPT_calcParams{OPT_incrProfit};
      $OPT_incrRetailerProfit = $OPT_calcParams{OPT_incrRetailerProfit};
      $OPT_incrManufacturerProfit = $OPT_calcParams{OPT_incrManufacturerProfit};

      $recent52ManuNetRev = $OPT_calcParams{recent52ManuNetRev};
      $optManuNetRev = $OPT_calcParams{optManuNetRev};

      $OPT_targetPriceDiff = $OPT_calcParams{OPT_targetPriceDiff};
      $OPT_targetPriceDiffPct = $OPT_calcParams{OPT_targetPriceDiffPct};
    }

    #AI conclusions for user display
    $OPT_dispOptimumPrice = html_format_currency($OPT_optimumPrice);
    $OPT_dispTargetPrice = html_format_currency($OPT_targetPrice);
    $OPT_2digitTargetPrice = html_format_number($OPT_targetPrice);
    $OPT_dispTargetPriceDiff = html_format_currency($OPT_targetPriceDiff);
    $OPT_dispTargetPriceDiffPct = html_format_number($OPT_targetPriceDiffPct);
    $OPT_dispRecent52Units = html_format_number($OPT_recent52Units, 0);
    $OPT_dispOptimizedUnitDemand = html_format_number($OPT_optimizedUnitDemand, 0);
    $OPT_dispIncrUnitDemand = html_format_number(abs($OPT_incrUnitDemand), 0);
    $OPT_dispIncrUnitDemandPct = html_format_number($OPT_incrUnitDemandPct, 1);
    $OPT_dispIncrManufacturerProfit = html_format_currency($OPT_incrManufacturerProfit);
    $OPT_dispIncrRetailerProfit = html_format_currency($OPT_incrRetailerProfit);
    $OPT_dispManufacturerUnitMargin = html_format_currency($OPT_manufacturerUnitMargin);
    $OPT_dispRetailerUnitMargin = html_format_currency($OPT_retailerUnitMargin);
    $OPT_dispCOGSPct = html_format_number($OPT_COGSPct);
    $OPT_dispRetailerMarginPct = html_format_number($OPT_retailerMarginPct * 100);
    $OPT_dispManufacturerMarginPct = html_format_number($OPT_manufacturerMarginPct);

    $OPT_AIconclusions = <<END_HTML;
<P>
This product has an elasticity of $avgElasticity when priced between $OPT_dispMinPrice
and $OPT_dispMaxPrice in this market.
END_HTML

    if ($OPT_ideallyPriced == 1)
    {
      $OPT_AIconclusions .= <<END_HTML;
The model indicates this item is already ideally priced at an average of
<B>$OPT_dispAvgPrice</B>, and any change to pricing would likely result in a
decrease in profits for the manufacturer and retailer.
END_HTML
    }

    elsif ($OPT_optimumPrice > $OPT_avgPrice)
    {
      $OPT_AIconclusions .= <<END_HTML;
The model indicates that increasing the average price from $OPT_dispAvgPrice to <B>$OPT_dispOptimumPrice</B>
would result in an overall profit increase for both the retailer and manufacturer
despite the drop in sales volume.
END_HTML
    }

    else
    {
      $OPT_AIconclusions .= <<END_HTML;
The model indicates that decreasing the average price from $OPT_dispAvgPrice to <B>$OPT_dispOptimumPrice</B>
would result in an overall profit increase for both the retailer and manufacturer
due to an increase in sales volume more than making up the loss of margin.
END_HTML
    }

    #if the user-selected target price is greater than the optimum price
    if ($OPT_targetPrice > $OPT_optimumPrice)
    {
      $OPT_AIconclusions .= <<END_HTML;
</P><P>
You've set the target price of this item to <B>$OPT_dispTargetPrice</B>, which is
$OPT_dispTargetPriceDiff ($OPT_dispTargetPriceDiffPct%) more than the target price
suggested by the model.
END_HTML

      #if the user has targeted a price > than optimum and historical average
      if ($OPT_targetPrice > $OPT_avgPrice)
      {
        $OPT_AIconclusions .= "This introduces a definite risk of a decrease in sales volume.";
      }
      else
      {
        $OPT_AIconclusions .= "This could be an acceptable trade-off between margin and sales volume.";
      }
    }

    #elsif the user-selected target price is less than the optimum price
    elsif ($OPT_targetPrice < $OPT_optimumPrice)
    {
      $OPT_AIconclusions .= <<END_HTML;
</P><P>
You've set the target price of this item to <B>$OPT_dispTargetPrice</B>, which is
$OPT_dispTargetPriceDiff ($OPT_dispTargetPriceDiffPct%) less than the target price suggested by the model.
END_HTML

      if ($OPT_targetPrice <= $OPT_COGS)
      {
        $OPT_AIconclusions .= "This price is less than the COGS of the product, and each unit is selling at a loss.";
      }
      elsif ($OPT_targetPrice <= $OPT_avgPrice)
      {
        $OPT_AIconclusions .= "This price is less than the normal price of this product, reducing margins.";
      }
      elsif ($OPT_targetPrice <= $OPT_optimumPrice)
      {
        $OPT_AIconclusions .= "This price is less than the optimum price of this product, reducing potential profits.";
      }
    }

    #give user option to reset target price if needed
    if ($OPT_targetPrice != $OPT_optimumPrice)
    {
      $OPT_AIconclusions .= <<END_HTML;
  <A CLASS="text-decoration-none" HREF="?ds=$dsID&pm=$priceModelID&p=$prodID&g=$geoID&price=R">Reset the target price.</A>
END_HTML
    }

    #put together a volume change statement, if relevant
    if (!$OPT_ideallyPriced)
    {
      if ($OPT_incrUnitDemand > 0)
      {
        $OPT_unitDemandConclusions = <<END_HTML;
Yearly unit volume would increase by $OPT_dispIncrUnitDemand units
($OPT_dispIncrUnitDemandPct%) from $OPT_dispRecent52Units to $OPT_dispOptimizedUnitDemand.
END_HTML
      }
      else
      {
        $OPT_unitDemandConclusions = <<END_HTML;
Yearly unit volume would decrease by $OPT_dispIncrUnitDemand units
($OPT_dispIncrUnitDemandPct%) from $OPT_dispRecent52Units to $OPT_dispOptimizedUnitDemand.
END_HTML
      }
    }
  }

  #if we're dealing with an inelastic item
  else
  {

    #grab the highest price point we see more than twice that isn't an outlier
    $query = "SELECT ROUND(avgPrice, 2) AS rndPrice \
        FROM $dsSchema.$AInsightsItemCube \
        WHERE productID=$prodID AND geographyID=$geoID AND outlier=0 \
        GROUP BY rndPrice HAVING count(rndPrice) > 1 \
        ORDER BY rndPrice DESC LIMIT 1";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($OPT_commonPrice) = $dbOutput->fetchrow_array;
    if (!defined($OPT_commonPrice))
    {
      $OPT_commonPrice = 0;
    }

    #find the highest price that sold more than the average number of units
    $query = "SELECT AVG(units) FROM $dsSchema.$AInsightsItemCube \
        WHERE productID=$prodID AND geographyID=$geoID AND outlier=0 ";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($OPT_avgUnits) = $dbOutput->fetchrow_array;

    if (defined($OPT_avgUnits))
    {
      $query = "SELECT ROUND(avgPrice, 2) FROM $dsSchema.$AInsightsItemCube \
          WHERE productID=$prodID AND geographyID=$geoID AND outlier=0 AND units >= $OPT_avgUnits \
          ORDER BY avgPrice DESC LIMIT 1";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      KAPutil_handle_db_err($db, $status, $query);
      ($OPT_maxGoodVolPrice) = $dbOutput->fetchrow_array;
    }

    #our optimum price is the max betqeen "good volume" price and a common price
    if ($OPT_commonPrice > $OPT_maxGoodVolPrice)
    {
      $OPT_optimumPrice = $OPT_commonPrice;
    }
    else
    {
      $OPT_optimumPrice = $OPT_maxGoodVolPrice;
    }
    if ($OPT_optimumPrice <= 0)
    {
      $OPT_optimumPrice = $maxObservedPrice;
    }

    $OPT_optimumPrice = elasticity_threshold($OPT_optimumPrice, $avgElasticity, $OPT_avgPrice, $OPT_minPrice, $OPT_maxPrice);

    if (defined($OPT_manualPrice))
    {
      $OPT_targetPrice = $OPT_manualPrice;
    }
    else
    {
      $OPT_targetPrice = $OPT_optimumPrice;
    }

    #figure out per-unit price breakdown & percentages
    $OPT_calcParams{elasticity} = $avgElasticity;
    $OPT_calcParams{OPT_weeksScanned} = $OPT_weeksScanned;
    $OPT_calcParams{OPT_recent52Units} = $OPT_recent52Units;
    $OPT_calcParams{OPT_recent52Profit} = $OPT_recent52Profit;
    $OPT_calcParams{OPT_targetPrice} = $OPT_targetPrice;
    $OPT_calcParams{OPT_optimumPrice} = $OPT_optimumPrice;
    $OPT_calcParams{OPT_COGS} = $OPT_COGS;
    $OPT_calcParams{OPT_retailerMarginPct} = $OPT_retailerMarginPct;

    $OPT_calcParams{OPT_optimizedUnitDemand} = $OPT_recent52Units;

    %OPT_calcParams = elasticity_opt_calculate_profits(%OPT_calcParams);

    $OPT_unitMargin = $OPT_calcParams{OPT_unitMargin};
    $OPT_retailerUnitMargin = $OPT_calcParams{OPT_retailerUnitMargin};
    $OPT_manufacturerUnitMargin = $OPT_calcParams{OPT_manufacturerUnitMargin};

    $OPT_COGSPct = $OPT_calcParams{OPT_COGSPct};
    $OPT_manufacturerMarginPct = $OPT_calcParams{OPT_manufacturerMarginPct};
    #NB: one way or another, the retailer margin % is already set so we don't
    #   need to calculate it here.

    $OPT_optimizedUnitDemand = $OPT_calcParams{OPT_optimizedUnitDemand};
    $OPT_optimizedDollars = $OPT_calcParams{OPT_optimizedDollars};
    $OPT_optimizedProfit = $OPT_calcParams{OPT_optimizedProfit};

    $OPT_retailerProfit = $OPT_calcParams{OPT_retailerProfit};
    $OPT_manufacturerProfit = $OPT_calcParams{OPT_manufacturerProfit};

    $OPT_incrProfit = $OPT_calcParams{OPT_incrProfit};
    $OPT_incrRetailerProfit = $OPT_calcParams{OPT_incrRetailerProfit};
    $OPT_incrManufacturerProfit = $OPT_calcParams{OPT_incrManufacturerProfit};

    $recent52ManuNetRev = $OPT_calcParams{recent52ManuNetRev};
    $optManuNetRev = $OPT_calcParams{optManuNetRev};

    $OPT_targetPriceDiff = $OPT_calcParams{OPT_targetPriceDiff};
    $OPT_targetPriceDiffPct = $OPT_calcParams{OPT_targetPriceDiffPct};

    #AI conclusions for user display
    $OPT_dispOptimumPrice = html_format_currency($OPT_optimumPrice);
    $OPT_dispTargetPrice = html_format_currency($OPT_targetPrice);
    $OPT_2digitTargetPrice = html_format_number($OPT_targetPrice);
    $OPT_dispTargetPriceDiff = html_format_currency($OPT_targetPriceDiff);
    $OPT_dispTargetPriceDiffPct = html_format_number($OPT_targetPriceDiffPct);
    $OPT_dispIncrManufacturerProfit = html_format_currency($OPT_incrManufacturerProfit);
    $OPT_dispIncrRetailerProfit = html_format_currency($OPT_incrRetailerProfit);
    $OPT_dispManufacturerUnitMargin = html_format_currency($OPT_manufacturerUnitMargin);
    $OPT_dispRetailerUnitMargin = html_format_currency($OPT_retailerUnitMargin);
    $OPT_dispCOGSPct = html_format_number($OPT_COGSPct);
    $OPT_dispRetailerMarginPct = html_format_number($OPT_retailerMarginPct * 100);
    $OPT_dispManufacturerMarginPct = html_format_number($OPT_manufacturerMarginPct);

    $OPT_AIconclusions = <<END_HTML;
<P>
This product is inelastic when priced between $OPT_dispMinPrice and $OPT_dispMaxPrice in this market.
The model indicates that taking advantage of pricing power and increasing
average pricing on this product from $OPT_dispAvgPrice to <B>$OPT_dispOptimumPrice</B> would result in increased
profit with little to no risk of a decline in sales volume.
END_HTML

    if ($OPT_targetPrice > $OPT_optimumPrice)
    {
      $OPT_AIconclusions .= <<END_HTML;
</P><P>
You've set the target price of this item to <B>$OPT_dispTargetPrice</B>, which is
$OPT_dispTargetPriceDiff ($OPT_dispTargetPriceDiffPct%) more than the target price suggested by the model.
END_HTML

      if ($OPT_targetPriceDiffPct <= 10)
      {
        $OPT_AIconclusions .= "This introduces a slight risk of a decrease in sales volume, despite the product's inelasticity inside its historical pricing range.";
      }
      elsif ($OPT_targetPriceDiffPct <= 20)
      {
        $OPT_AIconclusions .= "This introduces a moderate risk of a decrease in sales volume, despite the product's inelasticity inside its historical pricing range.";
      }
      else
      {
        $OPT_AIconclusions .= "<SPAN CLASS='text-white bg-danger'>This introduces a significant risk of a decrease in sales volume, despite the product's inelasticity inside its historical pricing range.</SPAN>";
      }
    }

    elsif ($OPT_targetPrice < $OPT_optimumPrice)
    {
      $OPT_AIconclusions .= <<END_HTML;
</P><P>
You've set the target price of this item to <B>$OPT_dispTargetPrice</B>, which is
$OPT_dispTargetPriceDiff ($OPT_dispTargetPriceDiffPct%) less than the target price suggested by the model.
END_HTML

      if ($OPT_targetPrice <= $OPT_COGS)
      {
        $OPT_AIconclusions .= "This price is less than the COGS of the product, and each unit is selling at a loss.";
      }
      elsif ($OPT_targetPrice <= $OPT_avgPrice)
      {
        $OPT_AIconclusions .= "This price is less than the normal price of this inelastic product, reducing margins.";
      }
      elsif ($OPT_targetPrice <= $OPT_optimumPrice)
      {
        $OPT_AIconclusions .= "This price is less than the optimum price of this inelastic product, reducing potential margins.";
      }
    }

    #give user option to reset target price if needed
    if ($OPT_targetPrice != $OPT_optimumPrice)
    {
      $OPT_AIconclusions .= <<END_HTML;
   <A HREF="?ds=$dsID&pm=$priceModelID&p=$prodID&g=$geoID&price=R">Reset the target price.</A>
END_HTML
    }
  }

  #paragraph about incremental profits
  if ($OPT_incrManufacturerProfit < 0)
  {
    $OPT_incrManufacturerType = "loss";
  }
  else
  {
    $OPT_incrManufacturerType = "profit";
  }
  if ($OPT_incrRetailerProfit < 0)
  {
    $OPT_incrRetailerType = "loss";
  }
  else
  {
    $OPT_incrRetailerType = "profit";
  }

  if (!$OPT_ideallyPriced)
  {
    $OPT_AIconclusions .= <<END_HTML;
</P><P>
With this pricing, the manufacturer could see an incremental $OPT_incrManufacturerType of $OPT_dispIncrManufacturerProfit
for the next 52 weeks compared to the prior 52 weeks. The retailer could see an
incremental $OPT_incrRetailerType of $OPT_dispIncrRetailerProfit compared to the prior 52 weeks.
$OPT_unitDemandConclusions
END_HTML
  }

  #paragraph about COGS, margins, etc.
  if (!$OPT_ideallyPriced)
  {
    $OPT_AIconclusions .= <<END_HTML;
</P><P>
This pricing model assumes a per unit COGS of $OPT_dispCOGS ($OPT_dispCOGSPct%), manufacturer margin of
$OPT_dispManufacturerUnitMargin ($OPT_dispManufacturerMarginPct%), and retailer margin of
$OPT_dispRetailerUnitMargin ($OPT_dispRetailerMarginPct%).
END_HTML
  }

  $optimizerJSON = <<JSON_LABEL;
{"label": "Retailer \$ Volume", "color": "#5D62B5", "value": "$OPT_optimizedDollars"},
{"label": "Manufacturer Net Revenue", "color": "#29C3BE", "value": "$optManuNetRev"},
{"label": "Incr Manufacturer Profit", "color": "#F2726F", "value": "$OPT_incrManufacturerProfit"},
{"label": "Incr Retailer Profit", "color": "#FFC533", "value": "$OPT_incrRetailerProfit"}
JSON_LABEL

  $OPT_priceSliderMin = $OPT_optimumPrice - ($OPT_optimumPrice * 0.5);
  $OPT_priceSliderMax = $OPT_optimumPrice + ($OPT_optimumPrice * 0.5);
  $OPT_priceSliderMin = sprintf("%0.2f", $OPT_priceSliderMin);
  $OPT_priceSliderMax = sprintf("%0.2f", $OPT_priceSliderMax);
  $OPT_priceSliderTarget = sprintf("%0.2f", $OPT_targetPrice);

  $OPT_dispOptimizedProfit = html_format_currency($OPT_optimizedProfit);
  $OPT_dispRetailerProfit = html_format_currency($OPT_retailerProfit);
  $OPT_dispManufacturerProfit = html_format_currency($OPT_manufacturerProfit);
  $OPT_dispRecent52Profit = html_format_currency($OPT_recent52Profit);
  $OPT_dispIncrManufacturerProfit = html_format_currency($OPT_incrManufacturerProfit);

  $OPT_text = <<END_HTML;

  $OPT_AIconclusions

<!--
  <DIV ID="opt-collapse">
    <A data-bs-toggle="collapse" HREF="#collapse-opt-details">Additional Details</A>
    <I CLASS="fas fa-caret-down"></I>
    <DIV ID="collapse-opt-details" CLASS="card collapse" data-bs-parent="#opt-collapse">
      <DIV CLASS="card-body">

        <P>
        <B>Optimal price:</B> $OPT_dispOptimumPrice<BR>
        <B>Average Unit Price:</B> $OPT_dispAvgPrice<BR>
        </P>

        <P>
        <B>Yearly \$ from optimized price:</B> $OPT_optimizedDollars<BR>
        <B>Previous yearly \$:</B> $OPT_recent52Dollars<BR>
        <B>Yearly profit from optimized price:</B> $OPT_dispOptimizedProfit<BR>
        <B>Previous yearly profit:</B> $OPT_dispRecent52Profit<BR>
        </P>

        <P>
        <B>Yearly unit sales from optimized price:</B> $OPT_optimizedUnitDemand<BR>
        <B>Previous yearly unit sales:</B> $OPT_recent52Units<BR>
        </P>

        <P>
          <B>Average distribution level:</B> $OPT_avgDist
        </P>

        <P>
        <B>Yearly retailer profit from optimized price:</B> $OPT_dispRetailerProfit<BR>
        <B>Incremental retailer profit:</B> $OPT_dispIncrRetailerProfit<BR>
        <B>Yearly manufacturer profit from optimized price:</B> $OPT_dispManufacturerProfit<BR>
        <B>Incremental manufacturer profit:</B> $OPT_dispIncrManufacturerProfit<BR>
        </P>

        <P>
        <B>Per-unit COGS:</B> $OPT_dispCOGS<BR>
        </P>

      </DIV>
    </DIV>
  </DIV>
  -->
END_HTML

  $OPT_text .= <<END_HTML;
  <P>
  <DIV CLASS="w-50">
    <LABEL FOR="sliderPrice"><B>Target price:</B></LABEL> \$<OUTPUT ID="sliderPriceValue">$OPT_priceSliderTarget</OUTPUT>
    <INPUT TYPE="range" CLASS="form-range" ID="sliderPrice" min=$OPT_priceSliderMin max=$OPT_priceSliderMax value=$OPT_priceSliderTarget step=0.01 onInput="sliderPriceValue.value = sliderPrice.value" onChange="changePrice()">
  </DIV>
<SCRIPT>
function changePrice()
{
  let manualPrice = sliderPrice.value;
  location.href='?ds=$dsID&pm=$priceModelID&p=$prodID&g=$geoID&price=' + manualPrice;
}
</SCRIPT>
END_HTML

  if (($OPT_ideallyPriced != 1) || ($OPT_targetPrice != $OPT_optimumPrice))
  {
    $OPT_text .= <<END_HTML;
  <DIV ID="graph-optimized-column" STYLE="height:402px;"></DIV>
<SCRIPT>
FusionCharts.ready(function()
{
  let optimizerCols = new FusionCharts(
  {
    type: 'column2d',
    renderAt: 'graph-optimized-column',
    width: '95%', height: '400',
    dataFormat: 'json',
    dataSource:
    {
      'chart':
      {
        'baseFontSize': '14',
        'xAxisName': '',
        'numberPrefix': '\$',
        'yAxisName': '',
        'theme': 'zune'
      },
      'data': [ $optimizerJSON ]
    }
  });
  optimizerCols.render();
});
</SCRIPT>
END_HTML
  }

  #if the product appears to have been disco'd for most of the past 52 weeks
  if ($OPT_weeksScanned < 12)
  {
    $OPT_text = <<END_HTML;
This product appears to have been discontinued for most of the past year,
so an optimal price cannot be accurately determined.
END_HTML
  }

  #if there isn't enough price variance to realistically come up with a valid
  #recommendation
  if (($OPT_maxPrice - $OPT_minPrice) <= 0.02)
  {
    $OPT_text = <<END_HTML;
This product has had little to no pricing variance over the past year,
so an optimal price cannot be accurately determined.
END_HTML
  }


  ##################### End Optimizer Calculations ####################


  $prodName = $prodNameHash{$prodID};

  print <<END_HTML;
<DIV CLASS="container-fluid">

  <DIV CLASS="row">
    <DIV CLASS="col">

      $AI_cautionText

      <H3>Product: $prodName</H3>
      <H3>Geography: $geoNameHash{$geoID}</H3>

      <P>&nbsp;</P>

      <DIV CLASS="card">
        <DIV CLASS="card-body">

          <H4>Overview</H4>

          $AI_elasticityStatement

          <P>
          $elasticityGaugeHTML
          </P>

          $AI_invalidModelReasons

          <P>
          <DIV CLASS="container">
            <DIV CLASS="row">
              <DIV CLASS="col">

                <DIV CLASS="card $modelConfidenceCardBG text-white" STYLE="width: 15rem;">
                  <DIV CLASS="card-body">
                    <H4 CLASS="card-title text-center mt-3">High Confidence</H4>
                    <P CLASS="card-text">The elasticity value is based on a $modelConfidenceCardText statistical model.</P>
                    <P>
                  </DIV>
                </DIV>

              </DIV>
END_HTML

  if ($avgElasticity != 0)
  {
    print <<END_HTML;
              <DIV CLASS="col">
                <DIV CLASS="card" STYLE="width: 15rem;">
                  <DIV CLASS="card-body">
                    <H4 CLASS="card-title text-center mt-3">$dispPriceCoef</H4>
                    <H6 CLASS="card-subtitle text-muted text-center mb-2">Demand Coefficient</H6>
                    <P CLASS="card-text">Increasing price by \$1 will decrease average weekly unit sales by $dispPriceCoefAbs.</P>
                    <P>
                  </DIV>
                </DIV>
              </DIV>
END_HTML
  }

  print <<END_HTML;
              <DIV CLASS="col">

                <DIV CLASS="card" STYLE="width: 15rem;">
                  <DIV CLASS="card-body">
                    <H4 CLASS="card-title text-center mt-3">$weekCount</H4>
                    <H6 CLASS="card-subtitle text-muted text-center mb-2">Weeks of Data</H6>
                    <P CLASS="card-text">$weekCount weeks of data were used to create and test this elasticity model.</P>
                    <P>
                  </DIV>
                </DIV>

              </DIV>

            </DIV>
          </DIV>
          </P>

        </DIV>
      </DIV>

    </DIV>
  </DIV>
END_HTML

  #output optimized price info, if we have a valid model
  if (!$AI_noConfidenceModel)
  {
    print <<END_HTML;
  <DIV CLASS="row">
    <DIV CLASS="col">

      <P>&nbsp;</P>
      <DIV CLASS="card">
        <DIV CLASS="card-body">
        <H4>Optimized Pricing</H4>

        $OPT_text

        </DIV>
      </DIV>

    </DIV>
  </DIV>
END_HTML
  }

  print <<END_HTML;
  <DIV CLASS="row">
    <DIV CLASS="col">

      <P>&nbsp;</P>
      <DIV CLASS="card">
        <DIV CLASS="card-body">

          <H4>Elasticity Factors</H4>
          <DIV ID="elasticity-charts"></DIV>

<SCRIPT>
const URL_DATA = 'ajaxDetailsGraph.cld?pm=$priceModelID&ds=$dsID&p=$prodID&g=$geoID&ze=$AI_zeroedElasticity&noe=$AI_noElasticityGraph';
const URL_SCHEMA = 'ajaxDetailsGraph.cld?pm=$priceModelID&ds=$dsID&p=$prodID&g=$geoID&s=1&noe=$AI_noElasticityGraph';

const jsonify = res => res.json();
const dataFetch = fetch(URL_DATA).then(jsonify);
const schemaFetch = fetch(URL_SCHEMA).then(jsonify);

Promise.all([dataFetch, schemaFetch]).then(([data, schema]) =>
{
  let fusionTable = new FusionCharts.DataStore().createDataTable(data, schema);

  new FusionCharts(
  {
    type: 'timeseries',
    renderAt: 'elasticity-charts',
    width: '95%',
    height: vpHeight,
    dataSource:
    {
      data: fusionTable,
      chart:
      {
        exportEnabled: 0
      },
      yAxis: [
      {
        plot:
        {
          value: 'Elasticity',
          type: 'line'
        },
        title: 'Elasticity'
      },
      {
        plot:
        {
          value: 'Units Sold',
          type: 'line'
        },
        title: 'Units Sold'
      },
      {
        plot:
        {
          value: 'Avg Retail Price',
          type: 'line'
        },
        title: 'Avg Retail Price',
        format:
        {
          prefix: '\$'
        }
      },
      {
        plot:
        {
          value: 'Distribution',
          type: 'line'
        },
        title: 'Distribution'
      },
      {
        plot:
        {
          value: 'Promotion',
          type: 'line'
        },
        title: 'Promotion'
      }
      ]
    }
  }).render();
});
</SCRIPT>

        </DIV>
      </DIV>

    </DIV>
  </DIV>
END_HTML

  if ((!$AI_zeroedElasticity) && (!$AI_noPriceRelationship))
  {
    print <<END_HTML;
  <DIV CLASS="row">
    <DIV CLASS="col">

      <P>&nbsp;</P>
      <DIV ID="data-collapse">
        <DIV CLASS="card border-primary mx-auto">
          <DIV CLASS="card-header bg-primary text-white">
            <A CLASS="card-link text-white text-decoration-none" data-bs-toggle="collapse" HREF="#collapse-data-table">Elasticity Calculation Table</A>
            <I CLASS="fas fa-caret-down"></I>
          </DIV>
          <DIV ID="collapse-data-table" CLASS="collapse" data-bs-parent="#data-collapse">
            <DIV CLASS="card-body">

<!--
              (Estimated elasticity is the % change in demand induced by a 1% price increase, using linear regression model to estimate demand changes)
-->
              <TABLE CLASS="table table-bordered table-striped table-sm table-hover">
                <THEAD><TR>
                  <TH>Time</TH>
                  <TH>Units Sold</TH>
                  <TH>Avg Price</TH>
                  <TH>Price + 1%</TH>
                  <TH>Actual Price Increase</TH>
                  <TH>Actual Demand Change</TH>
                  <TH>Elasticity</TH>
                </TR></THEAD>
END_HTML

    #figure out the chrono-order of the time periods
    $query = "SELECT ID FROM $dsSchema.timeperiods \
        WHERE duration=1 AND type=30 ORDER BY endDate ASC";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($timeID) = $dbOutput->fetchrow_array)
    {
      $orderedTimeIDStr .= "$timeID,";
    }
    chop($orderedTimeIDStr);

    #pull the data from the stored model info
    $query = "SELECT timeID, units, avgPrice, incrPrice, incrPriceDif, demandChange, elasticity, outlier \
        FROM $dsSchema.$AInsightsItemCube \
        WHERE productID='$prodID' AND geographyID=$geoID \
        ORDER BY FIELD(timeID, $orderedTimeIDStr)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);

    #run through cube data and output to user
    while (($timeID, $units, $price, $theoreticalPrice, $actualPriceIncrease, $demandChange, $elasticity, $outlier) = $dbOutput->fetchrow_array)
    {
      $units = html_format_number($units, 0, 1);
      $price = html_format_currency($price);
      $demandChange = html_format_number($demandChange, 1, 1);
      $theoreticalPrice = html_format_currency($theoreticalPrice);
      $actualPriceIncrease = html_format_currency($actualPriceIncrease);
      $elasticity = html_format_number($elasticity, 2);

      $timeName = $timeNameHash{$timeID};

      #highlight outliers that were excluded
      $rowClass="";
      if ($outlier == 1)
      {
        $rowClass="table-warning";
        $timeName = "$timeName<BR>\n(Excluded outlier)";
        $demandChange = "";
        $theoreticalPrice = "";
        $actualPriceIncrease = "";
        $elasticity = "";
      }

      print <<END_HTML;
                <TR CLASS="$rowClass">
                  <TD NOWRAP>$timeName</TD>
                  <TD CLASS='text-right'>$units</TD>
                  <TD CLASS='text-right'>$price</TD>
                  <TD CLASS='text-right'>$theoreticalPrice</TD>
                  <TD CLASS='text-right'>$actualPriceIncrease</TD>
                  <TD CLASS='text-right'>$demandChange</TD>
                  <TD CLASS='text-right'>$elasticity</TD>
                </TR>
END_HTML
    }

    print <<END_HTML;
              </TABLE>
            </DIV>
          </DIV>
        </DIV>
      </DIV>
END_HTML
  }

  $pricePlotToolText = 'Price: $xDataValue<br>Units: $yDataValue';
  print <<END_HTML;
      <DIV CLASS="row">
        <DIV CLASS="col text-center">

          <P>&nbsp;</P>
          <DIV CLASS="card">
            <DIV CLASS="card-body">

              <DIV ID="graph-price-scatter" STYLE="height:402px;"></DIV>
<SCRIPT>
FusionCharts.ready(function()
{
  let priceScatter = new FusionCharts(
  {
    type: 'scatter',
    renderAt: 'graph-price-scatter',
    width: '95%', height: '400',
    dataFormat: 'json',
    dataSource:
    {
      'chart':
      {
        'caption': 'Price Basis Model',
        'captionFontSize': '24',
        'baseFontSize': '14',
        'xAxisName': 'Avg Price',
        'forceXAxisValueDecimals': '1',
        'xAxisValueDecimals': '2',
        'xNumberPrefix': '\$',
        'yAxisName': 'Units Sold',
        'plotToolText': 'Average Price: \$xDataValue<br>Units Sold: \$yDataValue',
        'theme': 'zune'
      },
      'dataset': [
      {
        'showregressionline': '$AI_priceScatterTrendLine',
        'data': [ $priceJSON ]
      }]
    }
  });
  priceScatter.render();
});
</SCRIPT>

<SPAN CLASS="text-left">$AI_priceConfidenceStatement</SPAN>

            </DIV>
          </DIV>

        </DIV>
      </DIV>

      <DIV CLASS="row">
        <DIV CLASS="col text-center">

          <P>&nbsp;</P>
          <DIV CLASS="card">
            <DIV CLASS="card-body">

              <DIV ID="graph-dist-scatter" STYLE="height:402px;"></DIV>
<SCRIPT>
FusionCharts.ready(function()
{
  let distScatter = new FusionCharts(
  {
    type: 'scatter',
    renderAt: 'graph-dist-scatter',
    width: '95%', height: '400',
    dataFormat: 'json',
    dataSource:
    {
      'chart':
      {
        'caption': 'Distribution Basis Model',
        'captionFontSize': '24',
        'baseFontSize': '14',
        'xAxisName': 'Distribution Level',
        'yAxisName': 'Units Sold',
        'plotToolText': 'Distribution: \$xDataValue<br>Units Sold: \$yDataValue',
        'theme': 'zune'
      },
      'dataset': [
      {
        'showregressionline': '$AI_distScatterTrendLine',
        'data': [ $distJSON ]
       }]
     }
   });
   distScatter.render();
});
</SCRIPT>

              <SPAN CLASS="text-left">$AI_distConfidenceStatement</SPAN>

            </DIV>
          </DIV>
        </DIV>
      </DIV>

      <DIV CLASS="row">
        <DIV CLASS="col text-center">

          <P>&nbsp;</P>
          <DIV CLASS="card">
            <DIV CLASS="card-body">

              <DIV ID="graph-promo-scatter" STYLE="height:402px;"></DIV>
<SCRIPT>
FusionCharts.ready(function()
{
  let promoScatter = new FusionCharts(
  {
    type: 'scatter',
    renderAt: 'graph-promo-scatter',
    width: '95%', height: '400',
    dataFormat: 'json',
    dataSource:
    {
      'chart':
      {
        'caption': 'Non-Price Promotion Basis Model',
        'captionFontSize': '24',
        'baseFontSize': '14',
        'xAxisName': 'Non-Price Promoted Units',
        'yAxisName': 'Units Sold',
        'plotToolText': 'Promoted Units: \$xDataValue<br>Units Sold: \$yDataValue',
        'theme': 'zune'
      },
      'dataset': [
      {
        'showregressionline': '$AI_promoScatterTrendLine',
        'data': [ $promoJSON ]
       }]
     }
   });
   promoScatter.render();
});
</SCRIPT>
              <SPAN CLASS="text-left">$AI_promoConfidenceStatement</SPAN>

            </DIV>
          </DIV>

        </DIV>
      </DIV>


      <P>&nbsp;</P>
      <DIV ID="details-collapse">
        <DIV CLASS="card border-primary mx-auto">
          <DIV CLASS="card-header bg-primary text-white">
            <A CLASS="card-link text-white text-decoration-none" data-bs-toggle="collapse" HREF="#collapse-details-table">Model Details</A>
            <I CLASS="fas fa-caret-down"></I>
          </DIV>
          <DIV ID="collapse-details-table" CLASS="collapse" data-bs-parent="#details-collapse">
            <DIV CLASS="card-body">
END_HTML

  print <<END_HTML;
              <P>
              <B>Model Accuracy Confidence: </B>$modelConfidenceHR[1]<BR>
              </P>

              <P>
              <B>Relationship Between price & Sales: </B> $priceConfidenceHR[1]<BR>
              <B><I>q</I> (Demand Coefficient): </B>$priceCoef<BR>
              </P>

              <P>
              <B>Relationship Between Distribution & Sales: </B> $distConfidenceHR[1]<BR>
              <B>Distribution Coefficient: </B> $distCoef<BR>
              </P>

              <P>
              <B>Relationship Between Promotion & Sales: </B> $promoConfidenceHR[1]<BR>
              <B>Promotion Coefficient: </B> $promoCoef<BR>
              </P>

              <P>
              <B>Weeks of Data: </B>$weekCount<BR>
              <B>R<SUP>2</SUP>: </B>$R2<BR>
              <B>p-Statistic: </B>$pValue<BR>
              <B>Intercept: </B>$intercept<BR>
            </DIV>
          </DIV>
        </DIV>
      </DIV>
      <P>

    </DIV>
  </DIV>

</DIV>
END_HTML

  print_html_footer();

  AInsights_audit($db, $userID, $priceModelID, "Viewed details for $prodNameHash{$prodID} in $geoNameHash{$geoID}");
  $activity = "ELASTICITY: $first $last viewed model details for $prodNameHash{$prodID} / $geoNameHash{$geoID} in elasticity $elasticName in $dsName";
  utils_slack($activity);

#EOF
