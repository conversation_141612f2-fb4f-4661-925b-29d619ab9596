#!/usr/bin/perl

use lib "/opt/apache/app/";

use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;


#
# Output custom segmentation info for every data source on the cloud.
# Requested by <PERSON> @ ESM, 2020-08
#


  #connect to the database
  $db = KAPutil_connect_to_database();

  %orgNameHash = utils_get_org_hash($db);
  %userNameHash = utils_get_user_hash($db);

  #build hash of user->org mappings
  foreach $orgID (keys %orgNameHash)
  {
    $query = "SELECT ID FROM app.users WHERE orgID=$orgID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    while (($userID) = $dbOutput->fetchrow_array)
    {
      $userOrgHash{$userID} = $orgID;
    }
  }

  print("Broker|User|Data Source|UPC|CUSTOMSEG1|CUSTOMSEG2|CUSTOMSEG3|CUSTOMSEG4|CUSTOMSEG5|CUSTOMSEG6|CUSTOMSEG7|CUSTOMSEG8\n");

  #cycle through every data source on the system
  $query = "SELECT ID, userID, name FROM dataSources ORDER BY userID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  while (($dsID, $userID, $dsName) = $dbOutput->fetchrow_array)
  {
    undef(%custom1);
    undef(%custom2);
    undef(%custom3);
    undef(%custom4);
    undef(%custom5);
    undef(%custom6);
    undef(%custom7);
    undef(%custom8);

    $orgID = $userOrgHash{$userID};
    $orgName = $orgNameHash{$orgID};
    $userName = $userNameHash{$userID};
    $dsSchema = "datasource_" . $dsID;

    if (($orgID != 8) && ($orgID != 9))
    {
      next;
    }

    %prodNameHash = dsr_get_item_name_hash($db, $dsSchema, "p");
    %prodBaseHash = dsr_get_base_item_name_hash($db, $dsSchema, "p");

    %segHash = DSRsegmentation_get_segmentations_hash($db, $dsSchema, "p");

    foreach $segID (keys %segHash)
    {
      if ($segHash{$segID} eq "CUSTOMSEG1")
      {
        %custom1 = DSRseg_get_segment_membership_hash($db, $dsSchema, "p", $segID);
      }

      if ($segHash{$segID} eq "CUSTOMSEG2")
      {
        %custom2 = DSRseg_get_segment_membership_hash($db, $dsSchema, "p", $segID);
      }

      if ($segHash{$segID} eq "CUSTOMSEG3")
      {
        %custom3 = DSRseg_get_segment_membership_hash($db, $dsSchema, "p", $segID);
      }

      if ($segHash{$segID} eq "CUSTOMSEG4")
      {
        %custom4 = DSRseg_get_segment_membership_hash($db, $dsSchema, "p", $segID);
      }

      if ($segHash{$segID} eq "CUSTOMSEG5")
      {
        %custom5 = DSRseg_get_segment_membership_hash($db, $dsSchema, "p", $segID);
      }

      if ($segHash{$segID} eq "CUSTOMSEG6")
      {
        %custom6 = DSRseg_get_segment_membership_hash($db, $dsSchema, "p", $segID);
      }

      if ($segHash{$segID} eq "CUSTOMSEG7")
      {
        %custom7 = DSRseg_get_segment_membership_hash($db, $dsSchema, "p", $segID);
      }

      if ($segHash{$segID} eq "CUSTOMSEG8")
      {
        %custom8 = DSRseg_get_segment_membership_hash($db, $dsSchema, "p", $segID);
      }
    }

    #if we found some CUSTOMSEGn's
    if (%custom1)
    {
      foreach $prodID (keys %prodBaseHash)
      {
        $seg1 = "SMT_$custom1{$prodID}";
        $seg2 = "SMT_$custom2{$prodID}";
        $seg3 = "SMT_$custom3{$prodID}";
        $seg4 = "SMT_$custom4{$prodID}";
        $seg5 = "SMT_$custom5{$prodID}";
        $seg6 = "SMT_$custom6{$prodID}";
        $seg7 = "SMT_$custom7{$prodID}";
        $seg8 = "SMT_$custom8{$prodID}";

        $prodBaseHash{$prodID} =~ m/.* (\d+)$/;
        $upc = $1;

        print("$orgName|$userName|$dsName|$upc|$prodNameHash{$seg1}|$prodNameHash{$seg2}|$prodNameHash{$seg3}|$prodNameHash{$seg4}|$prodNameHash{$seg5}|$prodNameHash{$seg6}|$prodNameHash{$seg7}|$prodNameHash{$seg8}\n");
      }
    }
  }

#EOF
