#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Merge Products</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Merge Products</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');
  $mergedID = $q->param('mergedID');
  @mergedProducts = $q->param('mergedProducts');
  $primaryProd = $q->param('primaryProd');

  #connect to the database
  $db = KAPutil_connect_to_database();

  #get the name of the data source
  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  #make sure we have write privs for this data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to merge products in this data source.");
  }

  #make sure the data source isn't locked by another background process
  $ok = DSRutil_operation_ok($db, $dsID, "MERGE-ITEMS");
  if ($ok != 1)
  {
    exit_warning("Another job is currently using this data source - please try again later.")
  }

  $dsSchema = "datasource_" . $dsID;

  $primaryProdName = KAPutil_get_item_ID_name($db, $dsSchema, "p", $primaryProd);
  $q_primaryProdName = $db->quote($primaryProdName);

  $activity = "$first $last created merged product $primaryProdName in $dsName";
  utils_audit($db, $userID, "Created merged product $primaryProdName", $dsID, 0, 0);

  #fork off a background process to do the actual measure calculation
  $SIG{CHLD} = "IGNORE";
  if ($pid = fork())
  {
    #parent process
  }
  else
  {
    #child process

    #let Apache know not to wait on the child process
    close(STDIN);
    close(STDOUT);

    #redirect STDERR to the Koala error log
    close(STDERR);
    open(STDERR, ">>/opt/apache/htdocs/tmp/koala_error.log");

    #reconnect to database in child process
    $db = KAPutil_connect_to_database();

    #set our initial state in the jobs table
    KAPutil_job_store_status($db, $userID, $dsID, 0, "MERGE-ITEMS", "Merging items");
    $q_name = $db->quote($dsName);
    $query = "UPDATE app.jobs SET dsName=$q_name WHERE PID=$$ AND dsID=$dsID";
    $db->do($query);

    #if we're editing an existing merged product
    if ($mergedID > 1)
    {
      #delete the current version of the merged product
      DSRmergedprod_unwind($db, $dsSchema, $mergedID);

      $query = "INSERT INTO $dsSchema.products (ID, name, merged) \
          VALUES ($mergedID, $q_primaryProdName, 1);";
      $db->do($query);
    }

    #else we're creating a new merged item
    else
    {
      #add the master product to the DS's products table
      $query = "INSERT INTO $dsSchema.products (name, merged) \
          VALUES ($q_primaryProdName, 1);";
      $db->do($query);

      #get the master product's ID
      $mergedID = $db->{q{mysql_insertid}};
    }

    #set the child products to have a merged value of 2
    foreach $id (@mergedProducts)
    {
      $query = "UPDATE $dsSchema.products SET merged=2 WHERE ID=$id";
      $db->do($query);
    }

    #add the merged product's information to the products_merged table
    $mergedIDsStr = join(',', @mergedProducts);
    $q_mergedIDs = $db->quote($mergedIDsStr);
    $query = "INSERT INTO $dsSchema.products_merged \
        (mergedID, primaryProd, mergedProds) \
        VALUES ($mergedID, $primaryProd, $q_mergedIDs)";
    $db->do($query);

    #create the merged item
    DSRmergedprod_create($db, $dsSchema, $mergedID);

    #let the DSR know that calculated measures need to be recalculated before
    #cubes can be built/updated
    $query = "UPDATE dataSources SET needsRecalc=1 WHERE ID=$dsID";
    $db->do($query);

    #remove this task from the jobs table
    DSRutil_clear_status($db);
    $query = "UPDATE dataSources SET lastModified=NOW() WHERE ID=$dsID";
    $db->do($query);

    exit;
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-11 col-lg-8 col-xl-7"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Merged Product</DIV>
        <DIV CLASS="card-body">

          The selected products are being merged into $primaryProdName.

          <P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='display.cld?ds=$dsID&dim=$dim'"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();
  utils_slack($activity)

#EOF
