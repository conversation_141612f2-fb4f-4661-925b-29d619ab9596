#!/usr/bin/perl

use Text::CSV;

#load ESM's custom AOD download format


  #instantiate our CSV object
  $csv = Text::CSV->new( {binary => 1} );

  open(INPUT, "$ARGV[0]");
  open(OUTPUT, ">$ARGV[1]");

  #burn first line of file if it contains the "Edit Data Selection" tag
  $line = <INPUT>;
  if ($line =~ m/^Edit Data/)
  {
    $line = <INPUT>;
  }

  #parse the first header line (contains geo and time info)
  #Market : Total US HS • Segment : All Buyers • 52 Week Rolling : 52 W/E 07/11/20 • Conventional Grocery : Shoprite • Comparison Retail Outlets : All Outlets • Product Share Basis :
  $line =~ m/^.*? : (.*?) • Segment : .*? • \d+ Week Rolling : (.*?) • /;
  $geography = $1;
  $time = $2;

  if (length($geography) < 2)
  {
    $geography = "UNKNOWN";
  }
  if (length($time) < 2)
  {
    $time = "UNKNOWN";
  }

  #parse the primary header line (2nd line in extracted CSV)
  $line = <INPUT>;
  $csv->parse($line);
  @columns = $csv->fields();

  $columns[0] = "Product";

  #push our static columns out to the front of the headers array
  @tmp = ('Geo', 'Time');
  push(@tmp, @columns);

  #output the headers
  $csv->combine(@tmp);
  $line = $csv->string();
  print OUTPUT "$line\n";


  while ($line = <INPUT>)
  {

    $csv->parse($line);
    @columns = $csv->fields();

    $product = $columns[0];

    if ($product eq "SUM")
    {
      next;
    }

    @tmp = ($geography, $time);
    push(@tmp, @columns);

    $csv->combine(@tmp);
    $line = $csv->string();

    print OUTPUT "$line\n";
  }
