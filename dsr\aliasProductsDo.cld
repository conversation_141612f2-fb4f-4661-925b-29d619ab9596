#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::AIProductNaming;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  #output Content-type header
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  #output HTML
  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Improve Product Names</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Improve Product Names</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');
  $appendUPC = $q->param('appendUPC');
  $undo = $q->param('undo');

  if (defined($appendUPC))
  {
    $appendUPC = 1;
  }
  else
  {
    $appendUPC = 0;
  }

  #sanitize parameters
  $dsID = utils_sanitize_integer($dsID);
  if (!defined($dsID))
  {
    exit_early_error($session, "Invalid data source");
  }

  $dsSchema = "datasource_" . $dsID;

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  #make sure we have write privs for this data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to modify this data source.");
  }

  #if we're removing the time period aliases
  if ($undo == 1)
  {
    $query = "UPDATE app.dataEnrichment SET prodAlias=0 WHERE dsID=$dsID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    $query = "UPDATE $dsSchema.products SET alias=NULL";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    $userText = "Improved product name aliases have been removed from this data source.";

    utils_audit($db, $userID, "Removed improved product name aliases", $dsID, 0, 0);
  }

  else
  {
    #store the alias settings for the DS so future updates will create them, too
    $query = "INSERT INTO app.dataEnrichment (dsID, prodAlias, prodAliasAppendUPC) \
        VALUES ($dsID, 1, $appendUPC) \
        ON DUPLICATE KEY UPDATE prodAlias=1, prodAliasAppendUPC=$appendUPC";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    #create the actual aliases
    AIProductNaming_improve_product_names($db, $dsID);

    $userText = "Improved naming has been applied to all products in this data source, and will be applied to new products as well.";

    utils_audit($db, $userID, "Added improved product name aliases", $dsID, 0, 0);
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-10 col-md-7 col-lg-6 col-xl-5"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Improve Product Names</DIV>
        <DIV CLASS="card-body">

          <P>
          $userText

          <P>&nbsp;<P>
          <DIV CLASS="text-center">
            <BUTTON TYPE="button" CLASS="btn btn-primary" onClick="location.href='display.cld?ds=$dsID&dim=p'"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
          </DIV>

       </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->

END_HTML

  print_html_footer();

  #update the DS's last modified time to force cube/ODBC rebuilds
  $query = "UPDATE dataSources SET lastModified=NOW() WHERE ID=$dsID";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);

  $activity = "$first $last added improved product name aliases for $dsName";
  utils_slack($activity);


#EOF
