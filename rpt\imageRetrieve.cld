#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::Reports;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get the CGI input variables
  $imageType = $q->param('t');
  $imageID = $q->param('id');

  $db = KAPutil_connect_to_database();

  #determine what type of image (background, logo, etc.) we're retrieving
  if ($imageType eq "b")
  {
    $imgDBName = "app.reports_backgrounds";
  }

  #get the basic info about the image from the database
  $query = "SELECT name, img FROM $imgDBName WHERE ID=$imageID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($imgName, $imgData) = $dbOutput->fetchrow_array;

  #determine image content type for browser
  if ($imgName =~ m/\.png$/)
  {
    $contentType = "image/png";
  }
  elsif ($imgName =~ m/\.jpg$/)
  {
    $contentType = "image/jpeg";
  }
  elsif ($imgName =~ m/\.gif$/)
  {
    $contentType = "image/gif";
  }

  print("Content-type: $contentType\n\n");

  print($imgData);


#EOF
