#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::AInsights::AInsights;
use Lib::AInsights::Utils;

#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/plain\n\n");

  #get the CGI input variables
  $priceModelID = $q->param('pm');
  $chart = $q->param('c');
  $geoID = $q->param('g');

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsID = AInsights_get_dsID($db, $priceModelID);

  $dsSchema = "datasource_" . $dsID;

  AInsights_Utils_initialize_constants($priceModelID);

  #get displayable names for our DSR dimensions
  %timeperiodNames = dsr_get_item_name_hash($db, $dsSchema, "t");

  $ownBrandID = AInsights_Utils_get_own_brand_id($db, $priceModelID);
  $brandSegID = AInsights_Utils_get_brand_seg_id($db, $priceModelID);

  %brandNameHash = DSRseg_get_segments_hash($db, $dsSchema, "p", $brandSegID);
  $ownBrandName = $brandNameHash{$ownBrandID};

  #get competitive brands
  ($compID1, $compID2) = AInsights_Utils_get_brand_comp_ids($db, $dsSchema, $geoID);
  $compBrand1Name = $brandNameHash{$compID1};
  $compBrand2Name = $brandNameHash{$compID2};

  #if we're doing the overview line chart
  if ($chart eq "trnd")
  {

    #figure out the chrono-order of the time periods we want to graph
    $query = "SELECT ID, endDate FROM $dsSchema.timeperiods \
        WHERE duration=1 AND type=30 ORDER BY endDate DESC LIMIT 52";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($timeID, $endDate) = $dbOutput->fetchrow_array)
    {
      $timeIDStr .= "'$timeID',";
      push(@orderedTimeIDs, $timeID);

      $endDate =~ m/^(.*?) /;
      $endDateHash{$timeID} = $1;
    }
    chop($timeIDStr);
    @orderedTimeIDs = reverse(@orderedTimeIDs);

    $jsonData = <<JSON_LABEL;
{
  "chart":
  {
    "theme": "zune",
    "animation": "0",
    "drawanchors": "0",
    "yaxisname": "Average Price",
    "yaxisnamefontsize": "14",
    "numvdivlines": "10",
    "divlinealpha": "30",
    "labelpadding": "10",
    "labelstep": "4",
    "labelfontsize": "12",
    "labeldisplay": "rotate",
    "slantlabel": "1",
    "legendfontsize": "14",
    "useEllipsesWhenOverflow": "0",
    "yaxisvaluespadding": "10",
    "showvalues": "0",
    "numberprefix": "\$",
    "decimals": "2"
  },
JSON_LABEL

    #output time periods (X axis values)
    $jsonData .= <<JSON_LABEL;
  "categories": [
    {
      "category": [
JSON_LABEL

    foreach $timeID (@orderedTimeIDs)
    {
    $jsonData .= <<JSON_LABEL;
        {
          "label": "$endDateHash{$timeID}"
        },
JSON_LABEL
    }
    chop($jsonData);  chop($jsonData);

    $jsonData .= <<JSON_LABEL;
      ]
    }
  ],
  "dataset": [

JSON_LABEL

    #output the data set for the category
    undef(%priceValueHash);
    $query = "SELECT timeID, avgPrice FROM $dsSchema.$AInsightsBrandCube \
        WHERE brandID=0 AND geographyID=$geoID AND timeID IN ($timeIDStr)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($timeID, $priceVal) = $dbOutput->fetchrow_array)
    {
      $priceValueHash{$timeID} = $priceVal;
    }

    if ($status > 0)
    {

      $jsonData .= <<JSON_LABEL;
  {
    "seriesname": "Category Average",
    "data": [
JSON_LABEL

      foreach $timeID (@orderedTimeIDs)
      {
        $priceVal = $priceValueHash{$timeID};
        $jsonData .= "{ \"value\": \"$priceVal\"},\n";
      }
      chop($jsonData);  chop($jsonData);

      $jsonData .= <<JSON_LABEL;
    ]
  }
JSON_LABEL
    }

    #output the data set for our brand (if there is data)
    undef(%priceValueHash);
    $query = "SELECT timeID, avgPrice FROM $dsSchema.$AInsightsBrandCube \
        WHERE brandID=$ownBrandID AND geographyID=$geoID AND timeID IN ($timeIDStr)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($timeID, $priceVal) = $dbOutput->fetchrow_array)
    {
      $priceValueHash{$timeID} = $priceVal;
    }

    if ($status > 0)
    {
      $jsonData .= <<JSON_LABEL;
  ,{
    "seriesname": "$ownBrandName",
    "data": [
JSON_LABEL

      foreach $timeID (@orderedTimeIDs)
      {
        $priceVal = $priceValueHash{$timeID};
        $jsonData .= "{ \"value\": \"$priceVal\"},\n";
      }
      chop($jsonData);  chop($jsonData);

      $jsonData .= <<JSON_LABEL;
    ]
  }
JSON_LABEL
    }

    #output the data set for our first key competitor
    undef(%priceValueHash);
    $query = "SELECT timeID, avgPrice FROM $dsSchema.$AInsightsBrandCube \
        WHERE brandID=$compID1 AND geographyID=$geoID AND timeID IN ($timeIDStr)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($timeID, $priceVal) = $dbOutput->fetchrow_array)
    {
      $priceValueHash{$timeID} = $priceVal;
    }

    if ($status > 0)
    {

      $jsonData .= <<JSON_LABEL;
  ,{
    "seriesname": "$compBrand1Name",
    "data": [
JSON_LABEL

      foreach $timeID (@orderedTimeIDs)
      {
        $priceVal = $priceValueHash{$timeID};
        $jsonData .= "{ \"value\": \"$priceVal\"},\n";
      }
      chop($jsonData);  chop($jsonData);

      $jsonData .= <<JSON_LABEL;
    ]
  }
JSON_LABEL
    }

    #output the data set for our second key competitor
    undef(%priceValueHash);
    $query = "SELECT timeID, avgPrice FROM $dsSchema.$AInsightsBrandCube \
        WHERE brandID=$compID2 AND geographyID=$geoID AND timeID IN ($timeIDStr)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($timeID, $priceVal) = $dbOutput->fetchrow_array)
    {
      $priceValueHash{$timeID} = $priceVal;
    }

    if ($status > 0)
    {

      $jsonData .= <<JSON_LABEL;
  ,{
    "seriesname": "$compBrand2Name",
    "data": [
JSON_LABEL

      foreach $timeID (@orderedTimeIDs)
      {
        $priceVal = $priceValueHash{$timeID};
        $jsonData .= "{ \"value\": \"$priceVal\"},\n";
      }
      chop($jsonData);  chop($jsonData);

      $jsonData .= <<JSON_LABEL;
    ]
  }
JSON_LABEL
    }

    $jsonData .= <<JSON_LABEL;
  ]
}
JSON_LABEL

    print $jsonData;
  }


#------------------- Elasticity Gauge for Own Brand ---------------


  if ($chart eq "own_elasticity")
  {

    #get our brand's average elasticity
    $query = "SELECT elasticity FROM $dsSchema.$AInsightsBrandTable \
        WHERE brandID=$ownBrandID AND geographyID=$geoID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($avgElasticity) = $dbOutput->fetchrow_array;

    $jsonData = <<JSON_LABEL;
{
  "chart":
  {
    "animation": "0",
    "lowerLimit": "-3",
    "upperLimit": "0",
    "valueAbovePointer": "1",
    "showShadow": "0",
    "gaugeFillMix": "{light}",
    "baseFontColor": "#ffffff",
    "labelFontColor": "#333333",
    "valueFontColor": "#333333",
    "baseFontSize": "12",
    "theme": "zune"  
  },
  "colorRange":
  {
    "color": [
    {
      "minValue": "-0.75",
      "maxValue": "0",
      "label": "Inelastic",
      "code": "#00AEEF"
    },
    {
      "minValue": "-1.25",
      "maxValue": "-0.75",
      "label": "Moderately Inelastic",
      "code": "#8DC63F"
    },
    {
      "minValue": "-1.75",
      "maxValue": "-1.25",
      "label": "Moderately Elastic",
      "code": "#FFB100"
    },
    {
      "minValue": "-2.25",
      "maxValue": "-1.75",
      "label": "Elastic",
      "code": "#B21DAC"
    },
    {
      "minValue": "-3",
      "maxValue": "-2.255",
      "label": "Very Elastic",
      "code": "#DC0015"
    }]
  },
  "pointers":
  {
    "pointer": [
    {
      "value": "$avgElasticity"
    } ]
  }
}
JSON_LABEL

    print $jsonData;
  }



  #----------------- Elasticities for all Brands -------------------

  if ($chart eq "elasticities")
  {
    $jsonData = <<JSON_LABEL;
{
  "chart":
  {
    "theme": "zune",
    "showlegend": "0",
    "labelFontSize": "12",
    "valueFontSize": "12",
    "numberprefix": "-",
    "decimals": "2"
  },
  "data": [
JSON_LABEL

    #get the non-NA elasticity for all brands in selected geography
    $query = "SELECT brandID, elasticity FROM $dsSchema.$AInsightsBrandTable \
        WHERE geographyID=$geoID AND NOT ISNULL(elasticity) ORDER BY elasticity DESC";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($brandID, $elasticity) = $dbOutput->fetchrow_array)
    {
      $colorCode = AInsights_Utils_get_elasticity_html_bgcolor($elasticity);

      if ($elasticity > -0.2)
      {
        $valueColor = "#000000";
      }
      else
      {
        $valueColor = "#ffffff";
      }

      if ($brandID == $ownBrandID)
      {
        $brandNameHash{$brandID} = "<b>$brandNameHash{$brandID}</b>";
      }

      $elasticity = abs($elasticity);

      $jsonData .= "{\"label\": \"$brandNameHash{$brandID}\", \"value\": \"$elasticity\", \"color\": \"$colorCode\", \"valuefontcolor\": \"$valueColor\"},\n";
    }
    chop($jsonData);  chop($jsonData);

    $jsonData .= <<JSON_LABEL;
  ]
}
JSON_LABEL

    print($jsonData);
  }



  #----------------- Own Brand Geo Elasticities -------------------

  if ($chart eq "brand_elast_geos")
  {
    %geoBaseNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "g");

    $jsonData = <<JSON_LABEL;
{
  "chart":
  {
    "theme": "zune",
    "showlegend": "0",
    "labelFontSize": "12",
    "valueFontSize": "12",
    "numberprefix": "-",
    "decimals": "2"
  },
  "data": [
JSON_LABEL

    $query = "SELECT geographyID, elasticity FROM $dsSchema.$AInsightsBrandTable \
        WHERE brandID=$ownBrandID AND NOT ISNULL (elasticity) ORDER BY elasticity DESC";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($geoID, $elasticity) = $dbOutput->fetchrow_array)
    {
      $colorCode = AInsights_Utils_get_elasticity_html_bgcolor($elasticity);

      if ($elasticity > -0.2)
      {
        $valueColor = "#000000";
      }
      else
      {
        $valueColor = "#ffffff";
      }

      $elasticity = abs($elasticity);

      $jsonData .= "{\"label\": \"$geoBaseNameHash{$geoID}\", \"value\": \"$elasticity\", \"color\": \"$colorCode\", \"valuefontcolor\": \"$valueColor\"},\n";
    }
    chop($jsonData);  chop($jsonData);

    $jsonData .= <<JSON_LABEL;
  ]
}
JSON_LABEL

    print($jsonData);
  }




  #----------------- Avg Price of all Brands -------------------

  if ($chart eq "brands_price")
  {
    $brandNameHash{0} = "Category Average";

    $jsonData = <<JSON_LABEL;
{
  "chart":
  {
    "theme": "zune",
    "showlegend": "0",
    "labelFontSize": "12",
    "valueFontSize": "12",
    "numberprefix": "\$",
    "decimals": "2"
  },
  "data": [
JSON_LABEL

    #build hash of our distribution in each geography, maintain total
    $query = "SELECT brandID, avgPrice52 FROM $dsSchema.$AInsightsBrandTable \
        WHERE geographyID=$geoID AND avgPrice52 > 0 ORDER BY avgPrice52 DESC";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($brandID, $avgPrice) = $dbOutput->fetchrow_array)
    {
      $colorCode = "#01B8AA";
      if ($brandID == 0)
      {
        $colorCode = "#0096FF";
      }

      if ($brandID == $ownBrandID)
      {
        $brandNameHash{$brandID} = "<b>$brandNameHash{$brandID}</b>";
      }

      $jsonData .= "{\"label\": \"$brandNameHash{$brandID}\", \"value\": \"$avgPrice\", \"color\": \"$colorCode\"},\n";
    }
    chop($jsonData);  chop($jsonData);

    $jsonData .= <<JSON_LABEL;
  ]
}
JSON_LABEL

    print($jsonData);
  }


  #----------------- Own Brand Geo Pricing -------------------

  if ($chart eq "brand_geos")
  {
    %geoBaseNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "g");

    $jsonData = <<JSON_LABEL;
{
  "chart":
  {
    "theme": "zune",
    "showlegend": "0",
    "labelFontSize": "12",
    "valueFontSize": "12",
    "numberprefix": "\$",
    "decimals": "2"
  },
  "data": [
JSON_LABEL

    $query = "SELECT geographyID, avgPrice52 FROM $dsSchema.$AInsightsBrandTable \
        WHERE brandID=$ownBrandID AND avgPrice52 > 0 ORDER BY avgPrice52 DESC";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($geoID, $avgPrice) = $dbOutput->fetchrow_array)
    {
      $colorCode = "#01B8AA";

      $jsonData .= "{\"label\": \"$geoBaseNameHash{$geoID}\", \"value\": \"$avgPrice\", \"color\": \"$colorCode\"},\n";
    }
    chop($jsonData);  chop($jsonData);

    $jsonData .= <<JSON_LABEL;
  ]
}
JSON_LABEL

    print($jsonData);
  }



  #----------------- Own Brand Item Average Pricing -------------------

  if ($chart eq "brand_items")
  {
    %prodBaseNameHash = dsr_get_base_item_name_hash($db, $dsSchema, "p");

    $jsonData = <<JSON_LABEL;
{
  "chart":
  {
    "theme": "zune",
    "showlegend": "0",
    "labelFontSize": "12",
    "valueFontSize": "12",
    "numberprefix": "\$",
    "decimals": "2"
  },
  "data": [
JSON_LABEL

    $query = "SELECT productID, avgPrice52 FROM $dsSchema.$AInsightsItemTable \
        WHERE brandID=$ownBrandID AND avgPrice52 > 0 AND geographyID=$geoID \
        ORDER BY avgPrice52 DESC";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($prodID, $avgPrice) = $dbOutput->fetchrow_array)
    {
      $colorCode = "#01B8AA";
      $jsonData .= "{\"label\": \"$prodBaseNameHash{$prodID}\", \"value\": \"$avgPrice\", \"color\": \"$colorCode\"},\n";
    }
    chop($jsonData);  chop($jsonData);

    $jsonData .= <<JSON_LABEL;
  ]
}
JSON_LABEL

    print($jsonData);
  }



#EOF
