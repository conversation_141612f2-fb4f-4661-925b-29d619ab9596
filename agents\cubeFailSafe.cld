#!/usr/bin/perl

use lib "/opt/apache/app/";

use DBI;
use Email::Valid;

use Lib::KoalaConfig;
use Lib::BuildCube;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

  #redirect STDERR to the Koala error log
  close(STDERR);
  open(STDERR, ">>/opt/apache/htdocs/tmp/koala_error.log");
  select(STDERR);
  $| = 1;

  #see if a lockfile exists.  If it does and the PID it contains is still
  #running, then exit.
  if ( -f "/opt/apache/app/agents/.cubeFailSafe.lock")
  {
    $old_pid = `cat /opt/apache/app/agents/.cubeFailSafe.lock`;
    chomp($old_pid);

    #cheat and check to see if there's a corresponding directory in /proc,
    #rather than go through the crap of getting and parsing ps output
    if ( -e "/proc/$old_pid")
    {
      exit;
    }
    else
    {
      unlink("/opt/apache/app/agents/.cubeFailSafe.lock");
    }
  }

  #connect to the master database
  $db = KAPutil_connect_to_database();

  system("echo $$ > /opt/apache/app/agents/.cubeFailSafe.lock");

  #NB: Timing makes this difficult, so we're going to start by grabbing every
  #    cube that's currently updating or waiting for an update in the system.
  #    We're going to see if there's a corresponding update process running.
  #    If there isn't, we're going to wait 10 seconds, then re-grab the cube's
  #    opInfo and see if it's changed. If it hasn't, we're going to assume
  #    the update process crashed and restart it.

  #grab the status of every cube that's refreshing/queued for refresh
  $query = "SELECT PID, dsID, cubeID, opInfo FROM app.jobs \
      WHERE operation='CUBE-UPDATE' ORDER BY dsID";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  while (($refreshPID, $cubeDS, $cubeID, $opInfo) = $dbOutput->fetchrow_array)
  {

    #see if the update process is still running
    if ( -e "/proc/$refreshPID")
    {
      next;
    }

    #if we're here, it looks like the cube refresh process crashed

    #sleep for 10 seconds just in case we caught the refresh process at the end
    sleep(10);

    #see if the job is still in the jobs table
    $query = "SELECT PID FROM app.jobs WHERE PID=$refreshPID AND cubeID=$cubeID";
    $dbOutput2 = $db->prepare($query);
    $status = $dbOutput2->execute;

    #if the job hasn't been cleaned from the table
    if ($status > 0)
    {

      #clear the cube's stale refresh status
      $query = "UPDATE cubes SET status=NULL WHERE ID=$cubeID";
      $db->do($query);
      $query = "DELETE FROM app.jobs \
          WHERE PID=$refreshPID AND operation='CUBE-UPDATE'";
      $db->do($query);

      if ($childPID = fork())
      {
        #parent process - do nothing and continue processing cubes
      }
      else
      {
        #child process

        #reconnect to database
        $childDB = KAPutil_connect_to_database();

        #rebuild the cube
        $dsSchema = "datasource_" . $cubeDS;
        cube_build($childDB, $dsSchema, $cubeID, 0);
        exit;
      }
    }
  }


#EOF
