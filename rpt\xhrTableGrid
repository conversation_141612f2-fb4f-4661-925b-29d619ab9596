#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::Reports;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $rptID = $q->param('rptID');
  $visID = $q->param('v');
  $gridPadding = $q->param('gridPadding');
  $verticalGrid = $q->param('verticalGrid');
  $verticalGridColor = $q->param('verticalGridColor');
  $verticalGridWidth = $q->param('verticalGridWidth');
  $horizontalGrid = $q->param('horizontalGrid');
  $horizontalGridColor = $q->param('horizontalGridColor');
  $horizontalGridWidth = $q->param('horizontalGridWidth');

  #fix up the CGI parameters from the submitted form
  if (defined($verticalGrid))
  {
    $verticalGrid = ($verticalGrid eq "false") ? "0" : "1";
  }
  $verticalGridColor = "#" . $verticalGridColor;
  if (defined($horizontalGrid))
  {
    $horizontalGrid = ($horizontalGrid eq "false") ? "0" : "1";
  }
  $horizontalGridColor = "#" . $horizontalGridColor;

  $db = KAPutil_connect_to_database();

  #make sure we have edit privs for this report
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this report.");
  }

  #get the chart title details from the database
  $query = "SELECT design FROM visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($design) = $dbOutput->fetchrow_array;


  ########################################################################
  #
  # This code block is called on submit to save the changes
  #

  if (defined($gridPadding))
  {
    $design = reports_set_style($design, "verticalGrid", $verticalGrid);
    $design = reports_set_style($design, "verticalGridColor", $verticalGridColor);
    $design = reports_set_style($design, "verticalGridWidth", $verticalGridWidth);
    $design = reports_set_style($design, "horizontalGrid", $horizontalGrid);
    $design = reports_set_style($design, "horizontalGridColor", $horizontalGridColor);
    $design = reports_set_style($design, "horizontalGridWidth", $horizontalGridWidth);
    $design = reports_set_style($design, "gridPadding", $gridPadding);

    $q_design = $db->quote($design);
    $query = "UPDATE visuals SET design = $q_design WHERE ID=$visID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    $cubeName = cube_id_to_name($db, $rptID);
    $dsID = cube_get_ds_id($db, $rptID);
    $dsName = ds_id_to_name($db, $dsID);

    utils_audit($db, $userID, "Changed table grid formatting", $dsID, $rptID, 0);
    $activity = "$first $last changed table grid formatting for $cubeName in $dsName";
    utils_slack($activity);

    exit;
  }


  #########################################################################

  #extract settings from design string
  $verticalGrid = reports_get_style($design, "verticalGrid");
  $verticalGridColor = reports_get_style($design, "verticalGridColor");
  $verticalGridWidth = reports_get_style($design, "verticalGridWidth");
  $horizontalGrid = reports_get_style($design, "horizontalGrid");
  $horizontalGridColor = reports_get_style($design, "horizontalGridColor");
  $horizontalGridWidth = reports_get_style($design, "horizontalGridWidth");
  $gridPadding = reports_get_style($design, "gridPadding");

  #set appropriate defaults
  if (length($verticalGrid) < 1)
  {
    $verticalGrid = "1";
  }
  if (length($verticalGridColor) < 7)
  {
    $verticalGridColor = "#ffffff";
  }
  if (length($verticalGridWidth) < 1)
  {
    $verticalGridWidth = 1;
  }
  if (length($horizontalGrid) < 1)
  {
    $horizontalGrid = "1";
  }
  if (length($horizontalGridColor) < 7)
  {
    $horizontalGridColor = "#ffffff";
  }
  if (length($horizontalGridWidth) < 1)
  {
    $horizontalGridWidth = 1;
  }
  if ($gridPadding eq "")
  {
    $gridPadding = "3";
  }

  #set up things for HTML form display
  if ($verticalGrid eq "1")
  {
    $verticalGrid = "CHECKED";
  }
  if ($horizontalGrid eq "1")
  {
    $horizontalGrid = "CHECKED";
  }


  ####################################################################
  #
  # Everything after this point is called to display the table layout dialog
  #

  print <<END_HTML;
<SCRIPT>
function submitForm()
{
  let verticalGrid = \$("#verticalGrid").prop("checked");
  let verticalGridColor = document.getElementById('verticalGridColor').value;
  let verticalGridWidth = document.getElementById('verticalGridWidth').value;
  let horizontalGrid = \$("#horizontalGrid").prop("checked");
  let horizontalGridColor = document.getElementById('horizontalGridColor').value;
  let horizontalGridWidth = document.getElementById('horizontalGridWidth').value;
  let gridPadding = document.getElementById('gridPadding').value;

  //knock # off of color strings
  verticalGridColor = verticalGridColor.substr(1);
  horizontalGridColor = horizontalGridColor.substr(1);

  let url = "xhrTableGrid?rptID=$rptID&v=$visID&verticalGrid=" + verticalGrid +
      "&verticalGridColor=" + verticalGridColor +
      "&verticalGridWidth=" + verticalGridWidth +
      "&horizontalGrid=" + horizontalGrid +
      "&horizontalGridColor=" + horizontalGridColor +
      "&horizontalGridWidth=" + horizontalGridWidth +
      "&gridPadding=" + gridPadding;

  \$.get(url, function(data, status)
  {
    location.href = "display.cld?rpt=$rptID&v=$visID";
  });
}



function revertDefaults()
{
  document.getElementById('verticalGrid').checked = true;
  document.getElementById('verticalGridColor').value = "#ffffff";
  document.getElementById('verticalGridWidth').value = 1;
  document.getElementById('horizontalGrid').checked = true;
  document.getElementById('horizontalGridColor').value = "#ffffff";
  document.getElementById('horizontalGridWidth').value = 1;
  document.getElementById('gridPadding').value = 3;
}
</SCRIPT>

<DIV CLASS="modal-dialog" STYLE="overflow-y: initial !important;">
  <DIV CLASS="modal-content">
    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Table Grid</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">

      <FORM>
      <TABLE CLASS="mx-auto">
        <TR>
          <TD STYLE="text-align:right;">
            Vertical grid:&nbsp;
          </TD>
          <TD>
            <DIV CLASS="form-check form-switch">
              <INPUT TYPE="checkbox" CLASS="form-check-input" NAME="verticalGrid" ID="verticalGrid" data-offstyle="secondary" $verticalGrid>
              <LABEL CLASS="form-check-label" FOR="verticalGrid">&nbsp;</LABEL>
            </DIV>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Vertical grid color:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="color" NAME="verticalGridColor" ID="verticalGridColor" STYLE="width:3em;" VALUE="$verticalGridColor">
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Vertical grid width:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="number" NAME="verticalGridWidth" ID="verticalGridWidth" STYLE="width:5em;" VALUE="$verticalGridWidth" min=0>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Horizontal grid:&nbsp;
          </TD>
          <TD>
            <DIV CLASS="form-check form-switch">
              <INPUT TYPE="checkbox" CLASS="form-check-input" NAME="horizontalGrid" ID="horizontalGrid" data-offstyle="secondary" $horizontalGrid>
              <LABEL CLASS="form-check-label" FOR="horizontalGrid">&nbsp;</LABEL>
            </DIV>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Horizontal grid color:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="color" NAME="horizontalGridColor" ID="horizontalGridColor" STYLE="width:3em;" VALUE="$horizontalGridColor">
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Horizontal grid width:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="number" NAME="horizontalGridWidth" ID="horizontalGridWidth" STYLE="width:5em;" VALUE="$horizontalGridWidth" min=0>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Padding:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="number" NAME="gridPadding" ID="gridPadding" STYLE="width:5em;" VALUE="$gridPadding" min=0>
          </TD>
        </TR>

        <TR>
          <TD COLSPAN="2" STYLE="text-align:center;">
            <P></P>
            <A HREF="#" CLASS="text-decoration-none" onClick="revertDefaults()">Revert to default</A>
          </TD>
        </TR>
      </TABLE>
      </FORM>

    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="submitForm()"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML


#EOF
