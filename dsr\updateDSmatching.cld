#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Update Data Source</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Please Wait...');
  return(true);
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Update Data Source</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  $layout = $q->param('layout');
  $dsID = $q->param('ds');
  $key = $q->param('key');
  $tabs = $q->param('tabs');
  $appendUPC = $q->param('appendUPC');
  $compressWS = $q->param('compressWS');
  $dontOverwriteNames = $q->param('dontOverwriteNames');
  $scriptID = $q->param('scriptID');

  $options = "";
  if (defined($appendUPC))
  {
    $options .= "appendUPC,";
  }
  if (defined($compressWS))
  {
    $options .= "compressWS,";
  }
  if (defined($dontOverwriteNames))
  {
    $dontOverwriteNames = 1;
  }

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsSchema = "datasource_" . $dsID;
  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  #make sure we have write privs for the data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to modify this data source.");
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-10 col-lg-7 col-xl-6"> <!-- content -->

      <FORM METHOD="post" ACTION="/app/dsr/updateDSwork.cld" onsubmit="return checkForm(this);">
      <INPUT TYPE="hidden" NAME="layout" VALUE="$layout">
      <INPUT TYPE="hidden" NAME="ds" VALUE="$dsID">
      <INPUT TYPE="hidden" NAME="key" VALUE="$key">
      <INPUT TYPE="hidden" NAME="tabs" VALUE="$tabs">
      <INPUT TYPE="hidden" NAME="options" VALUE="$options">
      <INPUT TYPE="hidden" NAME="dontOverwriteNames" VALUE="$dontOverwriteNames">
      <INPUT TYPE="hidden" NAME="scriptID" VALUE="$scriptID">

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Data Source Matching</DIV>
        <DIV CLASS="card-body">

          Choose the fields to use for matching against current data source items.

          <P>
          <TABLE>
            <TR>
              <TD STYLE="text-align:right;">
                <LABEL FOR="pmatch">Product:&nbsp;</LABEL>
              </TD>
              <TD STYLE="text-align:left;">
                <SELECT CLASS="form-select" NAME="pmatch" ID="pmatch">
                  <OPTION VALUE="Name">Name</OPTION>
                  <OPTION VALUE="Alias">Alias</OPTION>
END_HTML

  #get the list of product attributes in the data source, and display
  $query = "SELECT ID, name FROM $dsSchema.product_attributes ORDER BY name";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;

  while (($id, $name) = $dbOutput->fetchrow_array)
  {
    if ($name eq "UPC")
    {
      print("<OPTION VALUE=\"UPC\" SELECTED>UPC</OPTION>\n");
    }
    else
    {
      print("<OPTION VALUE=\"$id\">$name</OPTION>\n");
    }
  }

  print <<END_HTML;
                </SELECT>
              </TD>
            </TR>
            <TR>
              <TD STYLE="text-align:right;">
                <LABEL FOR="gmatch">Geography:&nbsp;</LABEL>
              </TD>
              <TD STYLE="text-align:left;">
                <SELECT CLASS="form-select" NAME="gmatch" ID="gmatch">
                  <OPTION VALUE="Name">Name</OPTION>
                  <OPTION VALUE="Alias">Alias</OPTION>
END_HTML

  #get the list of geo attributes in the data source, and display
  $query = "SELECT ID, name FROM $dsSchema.geography_attributes ORDER BY name";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;

  while (($id, $name) = $dbOutput->fetchrow_array)
  {
    print("<OPTION VALUE=\"$id\">$name</OPTION>\n");
  }

  print <<END_HTML;
                </SELECT>
              </TD>
            </TR>
            <TR>
              <TD STYLE="text-align:right;">
                <LABEL FOR="tmatch">Time:&nbsp;</LABEL>
              </TD>
              <TD STYLE="text-align:left;">
                <SELECT CLASS="form-select" NAME="tmatch" ID="tmatch">
                  <OPTION VALUE="Name">Name</OPTION>
                  <OPTION VALUE="Alias">Alias</OPTION>
END_HTML

  #get the list of time attributes in the data source, and display
  $query = "SELECT ID, name FROM $dsSchema.time_attributes ORDER BY name";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;

  while (($id, $name) = $dbOutput->fetchrow_array)
  {
    print("<OPTION VALUE=\"$id\">$name</OPTION>\n");
  }

  print <<END_HTML;
                </SELECT>
              </TD>
            </TR>
          </TABLE>

          <P>&nbsp;<P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="location.href='main.cld?ds=$dsID'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-submit">Next <I CLASS="bi bi-arrow-right"></I></BUTTON>
          </DIV>

        </DIV>
      </DIV>

      </FORM>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
