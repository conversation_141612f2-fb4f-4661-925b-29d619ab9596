#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Report Statistics</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<SCRIPT SRC="/fusioncharts-3.15.2/js/fusioncharts.js"></SCRIPT>
<SCRIPT SRC="/fusioncharts-3.15.2/js/themes/fusioncharts.theme.zune.js"></SCRIPT>

<STYLE>
table#tbl_info
{
  border-collapse: collapse;
}

table#tbl_info tr:nth-child(even)
{
  background-color: #B8CCE4;
}

table#tbl_info tr:nth-child(odd)
{
  background-color: #DCE6F1;
}

table#tbl_info td
{
  padding: 2px;
  border: 1px solid white;
}
</STYLE>
END_HTML

  #build up the JSON data for our spark charts
  $refreshesJSON = "";
  $koalaViewsJSON = "";
  $cloudViewsJSON = "";
  $excelExportJSON = "";
  $pptExportJSON = "";
  $query = "SELECT MONTHNAME(CONCAT(year,'-',month,'-01')), refreshes, viewsKoala, viewsCloudBacked, exportExcel, exportPPT \
      FROM audit.stats_cubes WHERE cubeID=$rptID ORDER BY CONCAT(year,'-',month) LIMIT 12";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;

  $count = 0;
  while (($month, $refreshes, $viewKoala, $viewCloud, $exportExcel, $exportPPT) = $dbOutput->fetchrow_array)
  {
    $count++;

    $refreshesJSON .= "{ 'label': '$month', 'value': '$refreshes' },\n";
    $koalaViewsJSON .= "{ 'label': '$month', 'value': '$viewKoala' },\n";
    $cloudViewsJSON .= "{ 'label': '$month', 'value': '$viewCloud' },\n";
    $excelExportJSON .= "{ 'label': '$month', 'value': '$exportExcel' },\n";
    $pptExportJSON .= "{ 'label': '$month', 'value': '$exportPPT' },\n";
  }

  chop($refreshesJSON);  chop($refreshesJSON);
  chop($koalaViewsJSON);  chop($koalaViewsJSON);
  chop($cloudViewsJSON);  chop($cloudViewsJSON);
  chop($excelExportJSON);  chop($excelExportJSON);
  chop($pptExportJSON);  chop($pptExportJSON);

  while ($count <= 12)
  {
    $refreshesJSON = "{ 'value': '0' },\n" . $refreshesJSON;
    $koalaViewsJSON = "{ 'value': '0' },\n" . $koalaViewsJSON;
    $cloudViewsJSON = "{ 'value': '0' },\n" . $cloudViewsJSON;
    $excelExportJSON = "{ 'value': '0' },\n" . $excelExportJSON;
    $pptExportJSON = "{ 'value': '0' },\n" . $pptExportJSON;
    $count++;
  }

  print <<END_HTML;
<SCRIPT>
FusionCharts.ready(function()
{
  let refreshes = new FusionCharts(
  {
    type: 'sparkcolumn',
    renderAt: 'refreshes',
    width: '200', height: '50',
    dataFormat: 'json',
    dataSource:
    {
      "chart":
      {
        "theme": "zune",
        "highColor": "#6baa01",
        "lowColor": "#e44a00"
      },
      "dataset": [
      {
        "data": [ $refreshesJSON ]
      }]
    }
  });

  let viewskoala = new FusionCharts(
  {
    type: 'sparkcolumn',
    renderAt: 'viewskoala',
    width: '200', height: '50',
    dataFormat: 'json',
    dataSource:
    {
      "chart":
      {
        "theme": "zune",
        "highColor": "#6baa01",
        "lowColor": "#e44a00"
      },
      "dataset": [
      {
        "data": [ $koalaViewsJSON ]
      }]
    }
  });

  let viewscloudbacked = new FusionCharts(
  {
    type: 'sparkcolumn',
    renderAt: 'viewscloudbacked',
    width: '200', height: '50',
    dataFormat: 'json',
    dataSource:
    {
      "chart":
      {
        "theme": "zune",
        "highColor": "#6baa01",
        "lowColor": "#e44a00"
      },
      "dataset": [
      {
        "data": [ $cloudViewsJSON ]
      }]
    }
  });

  let exportexcel = new FusionCharts(
  {
    type: 'sparkcolumn',
    renderAt: 'exportexcel',
    width: '200', height: '50',
    dataFormat: 'json',
    dataSource:
    {
      "chart":
      {
        "theme": "zune",
        "highColor": "#6baa01",
        "lowColor": "#e44a00"
      },
      "dataset": [
      {
        "data": [ $excelExportJSON ]
      }]
    }
  });

  let exportppt = new FusionCharts(
  {
    type: 'sparkcolumn',
    renderAt: 'exportppt',
    width: '200', height: '50',
    dataFormat: 'json',
    dataSource:
    {
      "chart":
      {
        "theme": "zune",
        "highColor": "#6baa01",
        "lowColor": "#e44a00"
      },
      "dataset": [
      {
        "data": [ $pptExportJSON ]
      }]
    }
  });

  refreshes.render();
  viewskoala.render();
  viewscloudbacked.render();
  exportexcel.render();
  exportppt.render();
});
</SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/rpt/main">Reports</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?rpt=$rptID">$rptName</A></LI>
    <LI CLASS="breadcrumb-item active">Statistics</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;
  $rptID = $q->param('rptID');

  if ($rptID =~ m/^(\d+)\,.*/)
  {
    $rptID = $1;
  }

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  $db = KAPutil_connect_to_database();

  #get the report's name and last update from the database
  $query = "SELECT dsID, products, geographies, timeperiods, measures \
      FROM app.cubes WHERE ID=$rptID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($dsID, $prodStr, $geoStr, $timeStr, $measStr) = $dbOutput->fetchrow_array;

  $rptName = cube_id_to_name($db, $rptID);

  print_html_header();

  #make sure we have read privs for this report
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to view this report's statistics.");
  }

  %userNameHash = utils_get_user_hash($db);
  $dsName = ds_id_to_name($db, $dsID);

  #get counts of items from each dimension included in the report
  @products = split(',', $prodStr);
  @geographies = split(',', $geoStr);
  @timeperiods = split(',', $timeStr);
  @measures = split(',', $measStr);

  $numProds = scalar @products;
  $numGeos = scalar @geographies;
  $numTimes = scalar @timeperiods;
  $numMeasures = scalar @measures;

  $dataRows = $numProds * $numGeos * $numTimes;

  #calculate average refresh time for the report
  $query = "SELECT UNIX_TIMESTAMP(endTime) - UNIX_TIMESTAMP(startTime) \
      FROM audit.telemetry_cubes where cubeID=$rptID AND !isnull(endTime) \
      ORDER BY endTime DESC LIMIT 3;";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  $rptCount = 0;
  $rptTotalTime = 0;
  while (($rptTime) = $dbOutput->fetchrow_array)
  {
    $rptCount++;
    $rptTotalTime += $rptTime;
  }

  if ($rptCount > 0)
  {
    $rptAvgTime = $rptTotalTime / $rptCount;
  }

  #format the time sensibly
  if ($rptAvgTime < 121)
  {
    $rptAvgTime = "$rptAvgTime secs";
  }
  else
  {
    $rptAvgTime = $rptAvgTime / 60;
    $rptAvgTime = sprintf("%0.0f mins", $rptAvgTime);
  }

  #get the size of the report's table and indexes on disk
  $tblName = "_rptcube_" . $rptID;
  $query = "SELECT data_length, index_length FROM information_schema.TABLES \
      WHERE information_schema.TABLES.table_name = '$tblName'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($sizeData, $sizeIndex) = $dbOutput->fetchrow_array;
  $size = $sizeData + $sizeIndex;
  $size = $size / 1024;
  $size = $size / 1024;
  $size = sprintf("%0.2f", $size);

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-11 col-lg-8 col-xl-7"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Report Statistics</DIV>
        <DIV CLASS="card-body">

          <P>
          <TABLE CLASS="table table-striped table-sm table-bordered w-50">
            <TR>
              <TD STYLE='text-align:right; font-weight:bold;'>
                Products:
              </TD>
              <TD STYLE='text-align:left;'>
                $numProds
              </TD>
            </TR>
            <TR>
              <TD STYLE='text-align:right; font-weight:bold;'>
                Geographies:
              </TD>
              <TD STYLE='text-align:left;'>
                $numGeos
              </TD>
            </TR>
            <TR>
              <TD STYLE='text-align:right; font-weight:bold;'>
                Time Periods:
              </TD>
              <TD STYLE='text-align:left;'>
                $numTimes
              </TD>
            </TR>
            <TR>
              <TD STYLE='text-align:right; font-weight:bold;'>
                Measures:
              </TD>
              <TD STYLE='text-align:left;'>
                $numMeasures
              </TD>
            </TR>
          </TABLE>

          <P>
          <TABLE CLASS="table table-striped table-sm table-bordered w-50">
            <TR>
              <TD STYLE='text-align:right; font-weight:bold;'>
                Rows of data:
              </TD>
              <TD STYLE='text-align:left;'>
                $dataRows
              </TD>
            </TR>
            <TR>
              <TD STYLE='text-align:right; font-weight:bold;'>
                Average refresh time:
              </TD>
              <TD STYLE='text-align:left;'>
                $rptAvgTime
              </TD>
            </TR>
            <TR>
              <TD STYLE='text-align:right; font-weight:bold;'>
                Size of report data:
              </TD>
              <TD STYLE='text-align:left;'>
                $size MB
              </TD>
            </TR>
          </TABLE>

          <P>&nbsp;</P>
          <TABLE STYLE="padding:2px;">
            <TR>
              <TD COLSPAN="2" STYLE="text-align:center; font-weight:bold; font-size:12pt;">
                Report Statistics For The Last 12 Months<BR>&nbsp
              </TD>
            </TR>
            <TR>
              <TD STYLE="text-align:right; vertical-align:middle; font-weight:bold;">Report Refreshes:</TD>
              <TD><DIV ID="refreshes" STYLE="height:52px;"></DIV></TD>
            </TR>
            <TR>
              <TD STYLE="text-align:right; vertical-align:middle; font-weight:bold;">Web Views:</TD>
              <TD><DIV ID="viewskoala" STYLE="height:52px;"></DIV></TD>
            </TR>
            <TR>
              <TD STYLE="text-align:right; vertical-align:middle; font-weight:bold;">Cloud-Backed<BR> Dashboard Views:</TD>
              <TD><DIV ID="viewscloudbacked" STYLE="height:52px;"></DIV></TD>
            </TR>
            <TR>
              <TD STYLE="text-align:right; vertical-align:middle; font-weight:bold;">Excel Exports:</TD>
              <TD><DIV ID="exportexcel" STYLE="height:52px;"></DIV></TD>
            </TR>
            <TR>
              <TD STYLE="text-align:right; vertical-align:middle; font-weight:bold;">PowerPoint Exports:</TD>
              <TD><DIV ID="exportppt" STYLE="height:52px;"></DIV></TD>
            </TR>
          </TABLE>

          <P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='display.cld?rpt=$rptID'"><I CLASS="bi bi-check-lg"></I> Done</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
<P>
END_HTML

  print_html_footer();

  $activity = "$first $last viewed statistics for $rptName in $dsName";
  utils_slack($activity);


#EOF
