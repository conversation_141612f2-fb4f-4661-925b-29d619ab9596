#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::WebUtils;


#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $jobID = $q->param('j');

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #make sure we have read privs for this data flow
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to view telemetry for this data flow.");
  }

  #get the telemetry data from the database
  $query = "SELECT telemetry FROM prep.telemetry WHERE jobID=$jobID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  ($telemetry) = $dbOutput->fetchrow_array;

  print <<END_HTML;
<DIV CLASS="modal-header">
  <H5 CLASS="modal-title">Data Flow Job Telemetry</H5>
  <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
</DIV>

<DIV CLASS="modal-body">

  <DIV CLASS="card" STYLE="height:50vh; overflow-y:auto;">
    <DIV CLASS="card-body">
      <PRE STYLE="font-size:12px; background-color:white; border:0px;">
$telemetry
      </PRE>
    </DIV>
  </DIV>
</DIV>

<DIV CLASS="modal-footer">
  <BUTTON CLASS="btn btn-primary" data-bs-dismiss="modal"><I CLASS="bi bi-check-lg"></I> Done</BUTTON>
</DIV>
END_HTML


#EOF
