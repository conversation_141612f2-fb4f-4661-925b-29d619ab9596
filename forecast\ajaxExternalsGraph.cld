#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::Forecasts;


#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get the CGI input variables
  $fcID = $q->param('f');
  $dsID = $q->param('ds');
  $prodID = $q->param('p');
  $geoID = $q->param('g');
  $schema = $q->param('s');

  $dsSchema = "datasource_" . $dsID;
  $fcastTable = "_fcast_" . $fcID;
  $fcastCube = "_fcastcube_" . $fcID;

  $db = KAPutil_connect_to_database();

  print("Content-type: text/plain\n\n");

  #make sure we have privs to at least view this forecast
  $privs = forecast_rights($db, $userID, $fcID, $acctType);
  if ($privs eq "N")
  {
    print("ERROR: User doesn't have view privileges on this forecast.");
    exit;
  }

  if ($schema == 1)
  {
    print <<JSON_LABEL;
[{
  "name": "Date",
  "type": "date",
  "format": "%Y-%m-%d"
},
JSON_LABEL

  print <<JSON_LABEL;
{
  "name": "Forecast",
  "type": "number"
},
{
  "name": "Display Promotion",
  "type": "number"
},
{
  "name": "Feature Promotion",
  "type": "number"
},
{
  "name": "Display & Feature Promotion",
  "type": "number"
},
{
  "name": "Price Promotion",
  "type": "number"
}
]
JSON_LABEL
  }

  #else we're writing out the actual/forecasted measure values to be graphed
  else
  {

    #grab the dates and measure values from the forecast cube
    $query = "SELECT measure, promoDisp, promoDispFeat, promoFeat, promoPrice, DATE(endDate) \
        FROM $dsSchema.$fcastCube \
        WHERE geography=$geoID AND product=$prodID AND source='base' \
        ORDER BY endDate";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;

    #run through cube data and output to user
    while (($measureVal, $promoDisp, $promoDispFeat, $promoFeat, $promoPrice, $endDate) = $dbOutput->fetchrow_array)
    {
      $measureVal = sprintf("%.1f", $measureVal);
      $promoDisp = sprintf("%.0f", $promoDisp);
      $promoFeat = sprintf("%.0f", $promoFeat);
      $promoDispFeat = sprintf("%.0f", $promoDispFeat);
      $promoPrice = sprintf("%.0f", $promoPrice);

      $json .= "[\"$endDate\", $measureVal, $promoDisp, $promoFeat, $promoPrice],\n";
    }

    #now append on any forecasted values
    $query = "SELECT forecast, DATE(endDate) FROM $dsSchema.$fcastCube \
        WHERE geography=$geoID AND product=$prodID AND source='forecast' \
        ORDER BY endDate";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;

    #run through cube data and output to user
    while (($measureVal, $endDate) = $dbOutput->fetchrow_array)
    {
      if (defined($measureVal))
      {
        $measureVal = sprintf("%.1f", $measureVal);

        $json .= "[\"$endDate\", $measureVal, 0, 0],\n";
      }
    }

    chop($json);  chop($json);
    print("[$json]\n");
  }

#EOF
