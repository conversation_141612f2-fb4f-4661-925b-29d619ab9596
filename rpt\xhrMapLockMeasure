#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DataSel;
use Lib::DSRUtils;
use Lib::Reports;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $rptID = $q->param('rptID');
  $visID = $q->param('v');
  $lockedMeasure = $q->param('lm');

  $db = KAPutil_connect_to_database();

  #make sure we have edit privs for this report
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this report.");
  }

  #get the main chart display details from the database
  $query = "SELECT design FROM visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($graphDesign) = $dbOutput->fetchrow_array;


  ########################################################################
  #
  # This code block is called on submit to save the changes
  #

  #if we're being called to save a new locked measure
  if (defined($lockedMeasure))
  {
    $graphDesign = reports_set_style($graphDesign, "measure", $lockedMeasure);

    $q_graphDesign = $db->quote($graphDesign);
    $query = "UPDATE visuals SET design = $q_graphDesign WHERE ID=$visID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    $cubeName = cube_id_to_name($db, $rptID);
    $dsID = cube_get_ds_id($db, $rptID);
    $dsName = ds_id_to_name($db, $dsID);

    utils_audit($db, $userID, "Changed locked chart measure", $dsID, $rptID, 0);
    $activity = "$first $last changed locked chart measure for $cubeName in $dsName";
    utils_slack($activity);

    exit;
  }


  #########################################################################
  #
  # Everything after this point is called to display the locked measure dialog
  #

  $lockedMeasure = reports_get_style($graphDesign, "measure");

  print <<END_HTML;
<SCRIPT>
function submitForm()
{
  let lm = document.getElementById('lockedMeasure').value;

  let url = "xhrMapLockMeasure?rptID=$rptID&v=$visID&lm=" + lm;

  \$.get(url, function(data, status)
  {
    location.href = "display.cld?rpt=$rptID&v=$visID";
  });
}
</SCRIPT>

<DIV CLASS="modal-dialog" STYLE="overflow-y: initial !important;">
  <DIV CLASS="modal-content">
    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Lock Displayed Measure</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">

      <FORM>
      <LABEL FOR="lockedMeasure">Locked Measure:</LABEL>
      <SELECT CLASS="form-select" ID="lockedMeasure">
        <OPTION VALUE=0>(No locked measure)</OPTION>
END_HTML

  #get hash of measure names and IDs
  $dsID = cube_get_ds_id($db, $rptID);
  $dsSchema = "datasource_" . $dsID;
  %measureNameHash = get_measure_name_hash($dsSchema, $db, 0);

  #get list of measures included in this report
  $query = "SELECT measures FROM cubes WHERE ID=$rptID";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($measureStr) = $dbOutput->fetchrow_array;
  @measures = split(',', $measureStr);
  foreach $measure (@measures)
  {
    $rptMeasures{$measure} = 1;
  }

  foreach $measureID (sort {$measureNameHash{$a} cmp $measureNameHash{$b}} keys %measureNameHash)
  {
    if ($rptMeasures{$measureID} == 1)
    {
      print(" <OPTION VALUE=$measureID>$measureNameHash{$measureID}</OPTION>\n");
    }
  }

  print <<END_HTML;
      </SELECT>
      <SCRIPT>
        \$("select#lockedMeasure").val("$lockedMeasure");
      </SCRIPT>
      </FORM>

    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="submitForm()"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML


#EOF
