#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($kapDB);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Manual Upload</TITLE>
<LINK REL="SHORTCUT ICON" HREF="$Lib::KoalaConfig::kapHostURL/favicon.png">
<LINK HREF="/css/glyphicons.css" REL="stylesheet">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK REL="stylesheet" HREF="/jquery-ui/jquery-ui.css">
<LINK REL="stylesheet" HREF="/jquery-file-upload-9.34.0/css/jquery.fileupload.css">
<LINK REL="stylesheet" HREF="/jquery-file-upload-9.34.0/css/jquery.fileupload-ui.css">

<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/jquery-ui/jquery-ui.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<script src="/jquery-file-upload-9.34.0/js/tmpl.min.js"></script>
<SCRIPT SRC="/jquery-file-upload-9.34.0/js/jquery.fileupload.js"></SCRIPT>
<SCRIPT SRC="/jquery-file-upload-9.34.0/js/jquery.fileupload-process.js"></SCRIPT>
<SCRIPT SRC="/jquery-file-upload-9.34.0/js/jquery.fileupload-validate.js"></SCRIPT>
<SCRIPT SRC="/jquery-file-upload-9.34.0/js/jquery.fileupload-ui.js"></SCRIPT>

<STYLE>
.fade.in
{
  opacity: 1
}
</STYLE>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($kapDB, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="$Lib::KoalaConfig::kapHostURL/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">Data Prep</A></LI>
    <LI CLASS="breadcrumb-item">$flowName</LI>
    <LI CLASS="breadcrumb-item active">Manually Upload Data</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  get_cgi_session_info();

  #get CGI parameters
  $flowID = $q->param('f');
  $jobID = $q->param('j');
  $action = $q->param('a');

  $flowName = prep_flow_id_to_name($prepDB, $flowID);

  if ($action eq "e")
  {
    $actionVal = "Edit";
  }
  elsif ($action eq "r")
  {
    $actionVal = "Run";
  }
  else
  {
    $actionVal = "New";
  }

  print_html_header();

  #make sure we have write privs for this data flow
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to work with jobs in this data flow.");
  }

  #if we're being run as a non-interactive job, use the job ID as the file key
  if ($jobID > 0)
  {
    $key = $jobID;
  }

  #else come up with a random 5 digit integer, used to uniquely identify source
  #files for this run
  else
  {
    $key = int(rand(99999));
    $key = "M" . $key;
  }

  #by default, we want to move on to the next step of the flow create/edit proc
  $nextOp = "flowExtractData.cld?f=$flowID&key=$key&a=$action";

  #but if we're running in batch mode, go back to the statuspage
  if ($action eq "r")
  {
    $nextOp = "runFlow.cld?f=$flowID&j=$jobID&c=1";
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col-xs-12 col-sm-12 col-md-12 col-lg-12 col-xl-12"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Upload Data Files</DIV>
        <DIV CLASS="card-body">

          <P>
          Select the data files containing the data you wish to extract:

          <DIV CLASS="container">
            <BR>
            <FORM ID="fileupload" ACTION="/app/prep/xhrHandleUpload.cld?key=$key" METHOD="POST" ENCTYPE="multipart/form-data">

            <!-- buttons to add files and start the upload -->
            <DIV CLASS="row fileupload-buttonbar">
              <DIV CLASS="col-lg-7">

                <!-- fileinput-button span styles the file input field as button -->
                <SPAN CLASS="btn btn-success fileinput-button" ID="addfiles">
                  <I CLASS="bi bi-plus-lg"></I>
                  <SPAN>Add files...</SPAN>
                  <INPUT TYPE="file" NAME="files[]" MULTIPLE>
                </SPAN>

                <BUTTON TYPE="submit" CLASS="btn btn-primary start" ID="startupload">
                  <I CLASS="bi bi-upload"></I>
                  <SPAN>Start upload</SPAN>
                </BUTTON>

                <!-- global file processing state -->
                <SPAN CLASS="fileupload-process"></SPAN>
              </DIV>

              <!-- global progress state -->
              <DIV CLASS="col-lg-5 fileupload-progress fade">

                <!-- global progress bar -->
                  <DIV CLASS="progress" role="progressbar" aria-valuemin="0" aria-valuemax="100">
                    <DIV CLASS="progress-bar progress-bar-info progress-striped progress-bar-animated" STYLE="width:0%;"></DIV>
                  </DIV>

                  <!-- extended global progress state -->
                  <DIV CLASS="progress-extended">&nbsp;</DIV>
                </DIV>
              </DIV>

              <!-- table listing the files available for upload/download -->
              <TABLE role="presentation" CLASS="table table-striped"><TBODY CLASS="files"></TBODY></TABLE>
              </FORM>
              <BR>
            </DIV>

            <!-- template to display files awaiting upload -->
            <SCRIPT ID="template-upload" TYPE="text/x-tmpl">
            {% for (let i=0, file; file=o.files[i]; i++) { %}
            <TR CLASS="template-upload fade">
              <TD>
                <P CLASS="name">{%=file.name%}</P>
                <STRONG CLASS="error text-danger"></STRONG>
              </TD>
              <TD>
                <P CLASS="size">Processing...</P>
                <DIV CLASS="progress progress-bar-striped progress-bar-animated" role="progressbar" aria-valuemin="0" aria-valuemax="100" aria-valuenow="0"><DIV CLASS="progress-bar progress-bar-info" STYLE="width:0%;"></DIV></DIV>
              </TD>
              <TD>
                {% if (!i && !o.options.autoUpload) { %}
                <BUTTON CLASS="btn btn-primary start" DISABLED STYLE="display:none;">
                  <I CLASS="bi bi-arrow-up"></I>
                  <SPAN>Start</SPAN>
                </BUTTON>
                {% } %}
                {% if (!i) { %}
                <BUTTON CLASS="btn btn-warning cancel" STYLE="vertical-align:center;">
                  <I CLASS="bi bi-slash-circle"></I>
                  <SPAN>Remove</SPAN>
                </BUTTON>
                {% } %}
              </TD>
            </TR>
            {% } %}
            </SCRIPT>

            <!-- template to display uploaded files -->
            <SCRIPT ID="template-download" TYPE="text/x-tmpl">
            {% for (let i=0, file; file=o.files[i]; i++) { %}
            <TR CLASS="template-download fade">
              <TD>
                <P CLASS="name">
                  <SPAN>{%=file.name%}</SPAN>
                </P>
                {% if (file.error) { %}
                <DIV><SPAN CLASS="label label-danger">Error</SPAN> {%=file.error%}</DIV>
                {% } %}
              </TD>
              <TD>
                <SPAN CLASS="size">{%=o.formatFileSize(file.size)%}</SPAN>
              </TD>
              <TD>
              </TD>
            </TR>
            {% } %}
            </SCRIPT>


            <DIV CLASS="text-center">
              <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="location.href='main.cld'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
              <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='$nextOp'" ID="next" DISABLED=1 STYLE="display:none;">Next <I CLASS="bi bi-arrow-right"></I></BUTTON>
            </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->

<SCRIPT>
\$(function()
{
  'use strict';

  // Initialize the jQuery File Upload widget:
  \$('#fileupload').fileupload(
  {
    url: '/app/prep/xhrHandleUpload.cld?key=$key'
  });

  // Load existing files:
  \$('#fileupload').addClass('fileupload-processing');
  \$.ajax(
  {
    url: \$('#fileupload').fileupload('option', 'url'),
    dataType: 'json',
    context: \$('#fileupload')[0]
  }).always(function()
  {
    \$(this).removeClass('fileupload-processing');
  }).done(function(result)
  {
    \$(this).fileupload('option', 'done')
    .call(this, \$.Event('done'), {result: result});
  });
});



\$('#fileupload').bind('fileuploadstart', function(e, data)
{
  \$('#startupload').hide();
  \$('#addfiles').hide();
});



\$('#fileupload').bind('fileuploadstop', function(e, data)
{
  \$('#next').prop('disabled', false);
  \$('#next').show();
});
</SCRIPT>


END_HTML

  print_html_footer();

  prep_audit($prepDB, $userID, "Manually uploaded data to start a job", $flowID);
  utils_slack("PREP: $first $last started a new interactive job with manually uploaded data in $flowName");

#EOF
