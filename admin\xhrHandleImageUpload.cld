#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;
use File::Copy;
use Image::Magick;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  $imgType = $q->url_param('t');
  $name = $q->param('name');

  $db = KAPutil_connect_to_database();

  $fileName = $fileHandle = $q->param('imgFile');

  #get the uploaded file and store in /tmp/
  binmode($fileHandle);
  while (read($fileHandle, $block, 2048))
  {
    $fileData .= $block;
  }
  $fqFileName = "/tmp/$fileName";
  open(OUTPUT, ">$fqFileName");
  print OUTPUT $fileData;
  close(OUTPUT);


  #a little bit of an overload, but handle a name change
  if ($imgType eq "corp")
  {
    $q_name = $db->quote($name);
    $query = "UPDATE app.config SET value=$q_name WHERE name='app_name'";
    $status = $db->do($query);
  }

  #if the file is a page branding logo, scale & put it in place
  if ($imgType eq "pb")
  {
    $image = Image::Magick->new;
    $x = $image->Read("$fqFileName");
    warn "$x" if "$x";
    $image->Resize(geometry => 'x40');
    $image->Write('/opt/apache/htdocs/images/navbarlogo.png');
  }

  #if the file is a favicon, scale & put it in place
  if ($imgType eq "fav")
  {
    $image = Image::Magick->new;
    $x = $image->Read("$fqFileName");
    warn "$x" if "$x";
    $image->Resize(geometry => '32x32');
    $x = $image->Write('/opt/apache/htdocs/favicon.png');
    warn "$x" if "$x";
  }


  #if the file is a custom login page logo, scale & put it in place
  if ($imgType eq "log")
  {
    $image = Image::Magick->new;
    $x = $image->Read("$fqFileName");
    warn "$x" if "$x";
    $image->Resize(geometry => '350x');
    $x = $image->Write('/opt/apache/htdocs/images/loginlogo.png');
    warn "$x" if "$x";
  }


  print("Status: 302 Moved temporarily\n");
  print("Location: $Lib::KoalaConfig::kapHostURL/app/admin/whitelabel.cld\n\n");

#EOF
