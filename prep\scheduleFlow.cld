#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($kapDB);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: $action Data Flow Schedule</TITLE>
<LINK REL="SHORTCUT ICON" HREF="$Lib::KoalaConfig::kapHostURL/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>


<SCRIPT>
function freqDisplay(freq)
{
  document.getElementById('monthly-well').style.display = 'none';
  document.getElementById('weekly-well').style.display = 'none';
  document.getElementById('daily-well').style.display = 'none';

  if (freq == 'monthly')
  {
    document.getElementById('monthly-well').style.display = 'block';
  }
  else if (freq == 'weekly')
  {
    document.getElementById('weekly-well').style.display = 'block';
  }
  else if (freq == 'daily')
  {
    document.getElementById('daily-well').style.display = 'block';
  }
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($kapDB, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="$Lib::KoalaConfig::kapHostURL/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?f=$flowID">Data Prep</A></LI>
    <LI CLASS="breadcrumb-item">$flowName</LI>
    <LI CLASS="breadcrumb-item active">Schedule</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $flowID = $q->param('f');

  #determine if we're creating or editing a data flow
  if ($flowID > 0)
  {
    $action = "Edit";
  }
  else
  {
    $action = "New";
  }

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #make sure we have write privs for this data source
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to view this data flow's schedule.");
  }

  $flowName = prep_flow_id_to_name($prepDB, $flowID);

  print_html_header();

  #get type of flow from database
  $query = "SELECT source FROM prep.flows WHERE ID=$flowID";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($source) = $dbOutput->fetchrow_array;

  #manual and copy/paste data flows can't be scheduled for obvious reasons
  if (($source eq "Manual") || ($source eq "Paste"))
  {
    print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-10 col-lg-7 col-xl-6"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Data Flow Schedule</DIV>
        <DIV CLASS="card-body">
          $source data flows can't be scheduled to run automatically.

          <P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='main.cld?f=$flowID'"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
          </DIV>
        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

    exit;
  }

  #get current schedule settings from database
  $query = "SELECT sched, details FROM prep.schedule WHERE flowID=$flowID";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($sched, $details) = $dbOutput->fetchrow_array;

  #set "Never" as the correct default
  if (length($sched) < 1)
  {
    $sched = "never";
  }

  #set the rest of our defaults
  $monthlyFreq = 1;
  $monthlyDay = 0;
  $weeklyFreq = 1;
  $weeklyDay = 0;
  $dailyFreq = 1;

  #extract schedule details for use in form
  if ($sched eq "monthly")
  {
    $details =~ m/^(\d+)$/;
    $monthlyFreq = $1;
  }
  elsif ($sched eq "weekly")
  {
    $details =~ m/^(\d) (\d)$/;
    $weeklyFreq = $1;
    $weeklyDay = $2;
  }
  elsif ($sched eq "daily")
  {
    $dailyFreq = $details;
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-10 col-lg-7 col-xl-6"> <!-- content -->

      <FORM METHOD="post" ACTION="scheduleSave.cld">
      <INPUT TYPE="hidden" NAME="f" VALUE="$flowID">

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Data Flow Schedule</DIV>
        <DIV CLASS="card-body">
          Automatically look for new data:

          <P>
          <DIV CLASS="form-check">
            <INPUT CLASS="form-check-input" TYPE="radio" NAME="sched" ID="never" CHECKED VALUE="never" onChange="freqDisplay('never')">
            <LABEL CLASS="form-check-label" FOR="never">Never</LABEL>
          </DIV>

          <DIV CLASS="form-check">
            <INPUT CLASS="form-check-input" TYPE="radio" NAME="sched" ID="monthly" VALUE="monthly" onChange="freqDisplay('monthly')">
            <LABEL CLASS="form-check-label" FOR="monthly">Monthly (Restatements)</LABEL>
          </DIV>

          <DIV CLASS="form-check">
            <INPUT CLASS="form-check-input" TYPE="radio" NAME="sched" ID="weekly" VALUE="weekly" onChange="freqDisplay('weekly')">
            <LABEL CLASS="form-check-label" FOR="weekly">Weekly</LABEL>
          </DIV>

          <DIV CLASS="form-check">
            <INPUT CLASS="form-check-input" TYPE="radio" NAME="sched" ID="daily" VALUE="daily" onChange="freqDisplay('daily')">
            <LABEL CLASS="form-check-label" FOR="daily">Daily</LABEL>
          </DIV>

          <SCRIPT>
          \$('#$sched').attr('checked', true);
          \$(document).ready(function()
          {
            freqDisplay('$sched');
          });
          </SCRIPT>

          <P>
          <DIV ID="monthly-well" CLASS="card bg-light">
            <DIV CLASS="card-body">
              <DIV CLASS="row">
                <DIV CLASS="col-auto mt-2">
                  Run during the month of:
                </DIV>
                <DIV CLASS="col-auto">
                  <SELECT CLASS="form-select" NAME="monthly-freq" ID="monthly-freq">
                    <OPTION VALUE="1">January</OPTION>
                    <OPTION VALUE="2">February</OPTION>
                    <OPTION VALUE="3">March</OPTION>
                    <OPTION VALUE="4">April</OPTION>
                    <OPTION VALUE="5">May</OPTION>
                    <OPTION VALUE="6">June</OPTION>
                    <OPTION VALUE="7">July</OPTION>
                    <OPTION VALUE="8">August</OPTION>
                    <OPTION VALUE="9">September</OPTION>
                    <OPTION VALUE="10">October</OPTION>
                    <OPTION VALUE="11">November</OPTION>
                    <OPTION VALUE="12">December</OPTION>
                  </SELECT>
                  <SCRIPT>
                   \$('select#monthly-freq').val('$monthlyFreq');
                  </SCRIPT>
                </DIV>
              </DIV>
            </DIV>
          </DIV>

          <P>
          <DIV ID="weekly-well" CLASS="card bg-light" STYLE="display:none;">
            <DIV CLASS="card-body">
              <DIV CLASS="row">
                <DIV CLASS="col-auto mt-2">
                  Run every
                </DIV>
                <DIV CLASS="col-auto">
                  <INPUT TYPE="number" NAME="weekly-freq" CLASS="form-control" STYLE="width:5em;" VALUE="$weeklyFreq">
                </DIV>
                <DIV CLASS="col-auto mt-2">
                  weeks on &nbsp;
                </DIV>
                <DIV CLASS="col-auto">
                  <SELECT NAME="weekly-day" ID="weekly-day" CLASS="form-select">
                    <OPTION VALUE=0>Sunday</OPTION>
                    <OPTION VALUE=1>Monday</OPTION>
                    <OPTION VALUE=2>Tuesday</OPTION>
                    <OPTION VALUE=3>Wednesday</OPTION>
                    <OPTION VALUE=4>Thursday</OPTION>
                    <OPTION VALUE=5>Friday</OPTION>
                    <OPTION VALUE=6>Saturday</OPTION>
                  </SELECT>
                  <SCRIPT>
                    \$('select#weekly-day').val('$weeklyDay');
                  </SCRIPT>
                </DIV>
              </DIV>
            </DIV>
          </DIV>

          <P>
          <DIV ID="daily-well" CLASS="card bg-light" STYLE="display:none;">
            <DIV CLASS="card-body">
              <DIV CLASS="row">
                <DIV CLASS="col-auto mt-2">
                  Run every
                </DIV>
                <DIV CLASS="col-auto">
                  <INPUT TYPE="number" NAME="daily-freq" CLASS="form-control" STYLE="width:5em;" VALUE="$dailyFreq">
                </DIV>
                <DIV CLASS="col-auto mt-2">
                  days
                </DIV>
              </DIV>
            </DIV>
          </DIV>

          <P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onclick="location.href='main.cld?f=$flowID'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT"><I CLASS="bi bi-save"></I> Save</BUTTON>
          </DIV>

        </DIV>
      </DIV>

      </FORM>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
