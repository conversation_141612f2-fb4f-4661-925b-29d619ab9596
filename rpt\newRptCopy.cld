#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  $db = KAPutil_connect_to_database();

  #get the CGI input variables
  $dsID = $q->param('ds');
  $rptID = $q->param('rptID');

  $dsSchema = "datasource_" . $dsID;
  $rptCube = "_rptcube_" . $rptID;

  #grab all of the cube definition info for the source report
  $query = "SELECT name, dsID, lastUpdate, scriptProducts, scriptGeographies, scriptTimeperiods, scriptMeasures, products, geographies, timeperiods, measures \
      FROM cubes WHERE ID=$rptID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($srcName, $dsID, $lastUpdate, $scriptProducts, $scriptGeographies, $scriptTimeperiods, $scriptMeasures, $products, $geographies, $timeperiods, $measures) = $dbOutput->fetchrow_array;

  #create a copy of the cube definition in the master cubes tables
  $name = "Copy of $srcName";
  $q_name = $db->quote($name);
  $query = "INSERT INTO cubes \
      (dsID, userID, orgID, name, lastUpdate, scriptProducts, scriptGeographies, scriptTimeperiods, scriptMeasures, products, geographies, timeperiods, measures) \
      VALUES ($dsID, $userID, $orgID, $q_name, '$lastUpdate', '$scriptProducts', '$scriptGeographies', '$scriptTimeperiods', '$scriptMeasures', '$products', '$geographies', '$timeperiods', '$measures')";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);
  $newCubeID = $db->{q{mysql_insertid}};

  #build up our new source cube table name
  $newRptCube = "_rptcube_" . $newCubeID;

  #create a copy of the source cube table structure
  $query = "CREATE TABLE $dsSchema.$newRptCube LIKE $dsSchema.$rptCube";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);

  #lock the destination and source report tables
  $query = "LOCK TABLES $dsSchema.$newRptCube WRITE, $dsSchema.$rptCube READ";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);

  #populate the new cube with the data from the original cube
  $query = "INSERT $dsSchema.$newRptCube SELECT * FROM $dsSchema.$rptCube";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);

  #unlock the report cube tables
  $query = "UNLOCK TABLES";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);

  $dsName = ds_id_to_name($db, $dsID);
  utils_audit($db, $userID, "Copied report to $name in $destDSName", $dsID, $cubeID, 0);
  utils_audit($db, $userID, "Copied from $srcName in $dsName", $dsID, $newCubeID, 0);

  $activity = "$first $last copied report $rptName in $dsName";
  utils_slack($activity);

  #redirect to new report
  print("Status: 302 Moved temporarily\n");
  print("Location: /app/rpt/display.cld?rpt=$newCubeID\n\n");


#EOF
