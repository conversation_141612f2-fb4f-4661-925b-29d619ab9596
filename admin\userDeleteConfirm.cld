#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Confirm User Removal</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.9.1/bootstrap-icons.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Please Wait...');
  return(true);
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="home.cld">Administration</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="userMgmt.cld">User Management</A></LI>
    <LI CLASS="breadcrumb-item active">Remove User</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if ((length($email) < 1) || ($acctType < 1))
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $user = $q->param('u');

  #connect to the database
  $db = KAPutil_connect_to_database();

  print_html_header();

  #get basic user info for display purposes
  $query = "SELECT email, first, last FROM users WHERE ID=$user";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  ($userEmail, $userFirst, $userLast) = $dbOutput->fetchrow_array;

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-10 col-lg-7 col-xl-6"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Remove User</DIV>
        <DIV CLASS="card-body">
END_HTML

  #make sure the user doesn't still own any data sources
  $query = "SELECT name FROM app.dataSources WHERE userID=$user";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  if ($status > 0)
  {
    print <<END_HTML;
          Unable to remove the user $userFirst $userLast ($userEmail) - they still have these data sources associated with their account:

          <P>&nbsp;</P>
END_HTML

    while (($dsName) = $dbOutput->fetchrow_array)
    {
      print("$dsName<BR>\n");
    }

    print <<END_HTML;
          <P>&nbsp;</P>
          Either delete these data sources or assign them to another user, then you can remove this user.

          <P>&nbsp;<P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='/app/admin/userMgmt.cld'">Back</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

    print_html_footer();
    exit;
  }

  #make sure the user doesn't still own any reports
  $query = "SELECT name FROM app.cubes WHERE userID=$user";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  if ($status > 0)
  {
    print <<END_HTML;
          Unable to remove the user $userFirst $userLast ($userEmail) - they still have these reports associated with their account:

          <P>&nbsp;</P>
END_HTML

    while (($repName) = $dbOutput->fetchrow_array)
    {
      print("$repName<BR>\n");
    }

    print <<END_HTML;
          <P>&nbsp;</P>
          Either delete these reports or assign them to another user, then you can remove this user.

          <P>&nbsp;<P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='/app/admin/userMgmt.cld'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
          </DIV>
        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

    print_html_footer();
    exit;
  }

  print <<END_HTML;
          <FORM METHOD="post" ACTION="/app/admin/userDelete.cld" onsubmit="return checkForm(this);">
          <INPUT TYPE="hidden" NAME="u" VALUE="$user">

          Are you sure you want to remove the user $userFirst $userLast ($userEmail)?

          <P>&nbsp;<P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="location.href='/app/admin/userMgmt.cld'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-danger" TYPE="SUBMIT" ID="btn-submit"><I CLASS="bi bi-trash"></I> Remove User</BUTTON>
          </DIV>
          </FORM>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
