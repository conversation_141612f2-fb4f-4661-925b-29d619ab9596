#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;
use File::Copy;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;



  #connect to the database
  $db = KAPutil_connect_to_database();

  #create a hash of all users and orgIDs that have dedicated storage
  $query = "SELECT ID, dataStorage FROM app.orgs WHERE !isnull(dataStorage)";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  while (($orgID, $dir) = $dbOutput->fetchrow_array)
  {
    $orgHash{$orgID} = $dir;
  }

  foreach $orgID (keys %orgHash)
  {
    $query = "SELECT ID FROM app.users WHERE orgID=$orgID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    while (($userID) = $dbOutput->fetchrow_array)
    {
      $userHash{$userID} = $orgHash{$orgID};
    }
  }

  #get list of all data sources on system
  $query = "SELECT ID, userID, name FROM app.dataSources";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  while (($dsID, $userID, $dsName) = $dbOutput->fetchrow_array)
  {

    #if the user's data source is on a dedicated storage volume
    if (length($userHash{$userID}) > 0)
    {
      print("\n\n$dsName:\n");

      #get a list of any existing snapshots
      $dsSchema = "datasource_" . $dsID;
      $query = "SELECT ID, filename FROM $dsSchema.update_history \
          WHERE !isnull(filename)";
      $dbOutput1 = $db->prepare($query);
      $dbOutput1->execute;
      while (($updateID, $filename) = $dbOutput1->fetchrow_array)
      {

        #skip anything that already has an explicit directory
        if ($filename =~ m/^\//)
        {
          next;
        }

        $newFilename = $userHash{$userID} . "/" . $filename;
        $query = "UPDATE $dsSchema.update_history SET filename='$newFilename' \
            WHERE ID=$updateID";
        $db->do($query);
        print("-->Renaming $filename to $newFilename\n");

        $cmd = "/usr/bin/mv /opt/apache/app/logs/$filename $newFilename";
        `$cmd`;
        print "-->Moving $filename to $newFilename\n";
      }
    }
  }
