#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $dsID = $q->param('ds');
  $dim = $q->param('dim');
  $segID = $q->param('s');
  $ruleID = $q->param('r');
  $action = $q->param('a');

  $dsID = utils_sanitize_integer($dsID);
  if (!defined($dsID))
  {
    exit_early_error($session, "Invalid data source");
  }
  $dim = utils_sanitize_dim($dim);
  if (!defined($dim))
  {
    exit_early_error($session, "Invalid dimension");
  }

  $dsSchema = "datasource_" . $dsID;

  $dbStub = KAPutil_get_dim_stub_name($dim);
  $dbName = $dbStub . "seg_rules";

  #if we were handed multiple IDs, only use the first one
  if ($ruleID =~ m/^(\d+)\,/)
  {
    $ruleID = $1;
  }

  #connect to the database
  $db = KAPutil_connect_to_database();

  #make sure we have write privs for the data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to modify segmentation rules in this data source.");
  }


  ########################################################################
  #
  # These code blocks are called to move rules up and down in order
  #

  #move a rule to the top of the list
  if ($action eq "top")
  {
    $query = "SELECT segmentationID, step FROM $dsSchema.$dbName WHERE ID=$ruleID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($segID, $step) = $dbOutput->fetchrow_array;

    #if we're already at the top, we're done
    if ($step == 1)
    {
      exit;
    }

    #increment the step # of everything above our rule
    $query = "UPDATE $dsSchema.$dbName SET step=step+1 \
        WHERE segmentationID=$segID AND step < $step";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    #move the current rule to the top
    $query = "UPDATE $dsSchema.$dbName SET step=1 WHERE ID=$ruleID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    exit;
  }

  #move a rule up
  if ($action eq "up")
  {
    $query = "SELECT segmentationID, step FROM $dsSchema.$dbName WHERE ID=$ruleID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($segID, $step) = $dbOutput->fetchrow_array;

    $topStep = $step - 1;

    #if we're already at the top, we're done
    if ($topStep < 1)
    {
      exit;
    }

    #move the step above us down one
    $query = "UPDATE $dsSchema.$dbName SET step=step+1 \
        WHERE segmentationID=$segID AND step=$topStep";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    #move our current step up one
    $query = "UPDATE $dsSchema.$dbName SET step=step-1 WHERE ID=$ruleID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    exit;
  }

  #move a rule down
  if ($action eq "down")
  {
    $query = "SELECT segmentationID, step FROM $dsSchema.$dbName WHERE ID=$ruleID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($segID, $step) = $dbOutput->fetchrow_array;

    $bottomStep = $step + 1;

    #if we're already at the bottom, we're done
    $query = "SELECT step FROM $dsSchema.$dbName WHERE segmentationID=$segID \
        ORDER BY step DESC LIMIT 1";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($lastStep) = $dbOutput->fetchrow_array;
    if ($bottomStep > $lastStep)
    {
      exit;
    }

    #move the step below us up one
    $query = "UPDATE $dsSchema.$dbName SET step=step-1 \
        WHERE segmentationID=$segID AND step=$bottomStep";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    #move our current step down1 one
    $query = "UPDATE $dsSchema.$dbName SET step=step+1 WHERE ID=$ruleID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    exit;
  }

  #move a rule to the bottom of the list
  if ($action eq "bottom")
  {
    $query = "SELECT segmentationID, step FROM $dsSchema.$dbName WHERE ID=$ruleID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($segID, $step) = $dbOutput->fetchrow_array;

    #if we're already at the bottom, we're done
    $query = "SELECT step FROM $dsSchema.$dbName WHERE segmentationID=$segID \
        ORDER BY step DESC LIMIT 1";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($lastStep) = $dbOutput->fetchrow_array;
    if ($step == $lastStep)
    {
      exit;
    }

    #decrement the step # of everything below our rule
    $query = "UPDATE $dsSchema.$dbName SET step=step-1 \
        WHERE segmentationID=$segID AND step > $step";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    #move the current rule to the bottom
    $query = "UPDATE $dsSchema.$dbName SET step=$lastStep WHERE ID=$ruleID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    exit;
  }


  ########################################################################
  #
  # This code block is called on submit to delete the rule
  #

  #if we're being called to delete a segmentation rule
  if ($action eq "d")
  {
    $query = "SELECT segmentationID, step FROM $dsSchema.$dbName WHERE ID=$ruleID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($segID, $step) = $dbOutput->fetchrow_array;

    $query = "DELETE FROM $dsSchema.$dbName WHERE ID=$ruleID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    $query = "UPDATE $dsSchema.$dbName SET step=step-1 \
        WHERE segmentationID=$segID AND step > $step";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    $dsName = ds_id_to_name($db, $dsID);

    utils_audit($db, $userID, "Deleted segmentation rule", $dsID, 0, 0);
    $activity = "$first $last deleted segmentation rule in $dsName";
    utils_slack($activity);

    exit;
  }

  #########################################################################
  #
  # Everything after this point is called to display the delete confirm dialog
  #

  print <<END_HTML;
<SCRIPT>
function submitXhrForm()
{
  let url = 'xhrSegRuleDel.cld?ds=$dsID&dim=$dim&s=$segID&r=$ruleID&a=d';

  \$.get(url, function(data, status)
  {
    \$('#modal-segrule-add').modal('hide');
    \$('#dsGrid').jsGrid('render');
  });
}
</SCRIPT>

<DIV CLASS="modal-dialog" STYLE="overflow-y: initial !important;">
  <DIV CLASS="modal-content">
    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Delete Segmentation Rule</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">

      Are you sure you want to delete this segmentation rule?

    </DIV>

    <DIV CLASS="modal-footer">
      <DIV CLASS="text-center">
        <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
        <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="submitXhrForm()"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
      </DIV>
    </DIV>

  </DIV>
</DIV>
END_HTML


#EOF
