#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META HTTP-EQUIV="X-UA-Compatible" CONTENT="IE=edge">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1">
<TITLE>Koala: Documentation</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap/js/bootstrap.min.js"></SCRIPT>
<SCRIPT SRC="/docs/docs.js"></SCRIPT>

<!-- Custom styles for this template -->
<link href="/docs/docs.css" rel="stylesheet">
<link href="/docs/routing-tree-editor.css" rel="stylesheet">

</HEAD>

<BODY>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------


  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $topicID = $q->param('t');

  if ($topicID =~ m/^([a-z]*)_([a-z]*)$/)
  {
    $product = $1;
    $topic = $2;
  }
  elsif ($topicID =~ m/^([a-z]*)_([a-z]*)_(.*)$/)
  {
    $product = $1;
    $group = $2;
    $topic = $3;
  }

  #connect to the database
  $kapDB = KAPutil_connect_to_database();

  print_html_header();

  print <<END_HTML;
<DIV CLASS="container-fluid">
  <DIV CLASS="row">
    <DIV CLASS="col">
      <A CLASS="navbar-brand" HREF="/app/home.cld"><IMG SRC="/images/logo64.png" STYLE="border:0;" ALT="Koala Software"></A>
    </DIV>
  </DIV>
</DIV>

<P>
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI><A HREF="/app/docs.cld">Documentation</A></LI>
  </OL>
</NAV>

<P>&nbsp;</P>

<DIV CLASS="container">
 <DIV CLASS="row">
  <DIV CLASS="col-md-3 side-nav-col">
END_HTML

  open(NAV, "/opt/apache/htdocs/docs/nav.html");
  while ($line = <NAV>)
  {
    print("$line");
  }
  close(NAV);

  print <<END_HTML;
  </DIV>

  <DIV CLASS="col-md-9 doc-content">
END_HTML

  $filepath = "/opt/apache/htdocs/docs/$product";
  if (length($group) > 0)
  {
    $filepath = $filepath . "/$group";
  }
  $filepath = $filepath . "/$topic.html";

  #if we weren't given any topic, show the default welcome text
  if (length($product) < 1)
  {
    $filepath = "/opt/apache/htdocs/docs/index.html";
  }

  open(CONTENT, "$filepath");
  while ($line = <CONTENT>)
  {
    print("$line");
  }
  close(CONTENT);

  print <<END_HTML;
  </DIV>

 </DIV>

</DIV>

<SCRIPT>
\$(document).ready(function()
{
  \$('#product-$product').addClass('active');
  \$('#ul-$product').addClass('active');
  \$('#ul-$product-$group').addClass('active');
  \$('#$topicID').addClass('active current');
});
</SCRIPT>

<P>
END_HTML

  print_html_footer();

#EOF
