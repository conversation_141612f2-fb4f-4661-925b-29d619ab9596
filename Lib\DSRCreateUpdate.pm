
package Lib::DSRCreateUpdate;

use lib "/opt/apache/app/";

use Exporter;
use Mail::Mailer;
use Text::CSV_XS;
use LWP::UserAgent;

use Lib::AIProductNaming;
use Lib::DataSel;
use Lib::DataSources;
use Lib::DSRMeasures;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::PrepUtils;
use Lib::Social;
use Lib::WebUtils;

our @ISA = ('Exporter');

our @EXPORT = qw(&ds_set_mysql_import_flags
    &ds_telemetry
    &ds_set_status
    &ds_create_update);

our $dsUpdateID;
our $TELEMETRY = 0;



#-------------------------------------------------------------------------------
#
# Output telemetry data, if enabled
#

sub DBG
{
  my ($str) = @_;

  if ($TELEMETRY == 1)
  {
    print STDERR "$str\n";
  }
}



#-------------------------------------------------------------------------------
#
# Handle a database error of some kind during database create/update operations
#

sub ds_db_err
{
  my ($date);

  my ($db, $status, $text) = @_;

  if (!defined($status))
  {
    $date = localtime();
    print STDERR "$date: $text\n";
    if ($db->errstr =~ m/^MySQL server has gone away/)
    {
      die("Lost connection to database, terminating");
    }
  }
}



#-------------------------------------------------------------------------------
#
# Log telemetry information about the data source create/update process
#

sub ds_telemetry
{
  my ($query, $q_text, $status);

  my ($db, $text) = @_;

  $text = ": $text\n";
  $q_text = $db->quote($text);
  $query = "UPDATE audit.telemetry_data \
      SET telemetry = CONCAT(telemetry, NOW(), $q_text) \
      WHERE ID=$dsUpdateID";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------------
#
# Get the total number of records (lines) contained in a source file.
#

sub ds_get_source_record_count
{
  my ($records);
  my ($filename) = @_;

  $records = `wc -l $filename`;

  return($records);
}



#-------------------------------------------------------------------------------
#
# Set the data source creation/update status that'll be displayed to users.
# Call once with a blank status when the process is finished to unlock the
# data source.
#

sub ds_set_status
{
  my ($query, $q_text);
  my ($db, $dsID, $text) = @_;

  if ($dsID =~ m/datasource_(\d+)/)
  {
    $dsID = $1;
  }

  if (length($text) > 1)
  {
    $q_text = $db->quote($text);
  }
  else
  {
    $q_text = "NULL";
  }

  $query = "UPDATE app.jobs SET status=$q_text, lastAction=NOW() \
      WHERE pid=$$ AND dsID=$dsID";
  $db->do($query);
}



#-------------------------------------------------------------------------------
#
# Uses the mysqldump utility to make a snapshot backup of the specified data
# source.
#

sub ds_dump_data_source
{
  my ($dbName, $dsSchema, $fileStub, $sqlFileName, $zipFileName, $query);
  my ($status, $url, $ua, $dbOutput);

  my ($db, $dsID, $userID, $key) = @_;

  ds_telemetry($db, "Creating data source restore point");

  #if we're being called by Data Prep, take a snapshot on the Analytics cloud
  if ($key =~ m/^prep\.(\d+)/)
  {

    #call the remote snapshot service
    $url = $Lib::KoalaConfig::kapHostURL . "/app/dsr/snapshot.cld?ds=$dsID&user=$userID";
    $ua = LWP::UserAgent->new();
    $response = $ua->get($url);

    if ($response->code != 200)
    {
      $code = $response->code;
      $message = $response->message;
      $content = $response->message;
      print STDERR "Unable to perform remote snapshot: $code ($message)\n$content\n";
      return;
    }

    #wait until it finishes
    $query = "SELECT status FROM app.jobs WHERE dsID=$dsID AND operation='DS-UPDATE'";
    $status = "Backing up data source";
    while ($status eq "Backing up data source")
    {
      sleep(30);
      $dbOutput = $db->prepare($query);
      $dbOutput->execute;
      ($status) = $dbOutput->fetchrow_array;
    }
    return;
  }

  #determine if we're using an external table space for capacity/IO reasons
  $query = "SELECT userID FROM app.dataSources WHERE ID=$dsID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ds_db_err($db, $status, $query);
  ($ownerID) = $dbOutput->fetchrow_array;

  $orgID = KAPutil_get_user_org_id($db, $ownerID);

  $query = "SELECT dataStorage FROM app.orgs WHERE ID=$orgID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ds_db_err($db, $status, $query);
  ($dataDir) = $dbOutput->fetchrow_array;
  if (length($dataDir) > 2)
  {
    $dataDir .= "/logs/";
    chdir("$dataDir");
  }
  else
  {
    $dataDir = "";
    chdir("/opt/apache/app/logs/");
  }

  $dbName = "datasource_" . $dsID;
  $dsSchema = $dbName;
  $fileStub = $dbName . "_" . time();
  $sqlFileName = "$fileStub.sql";
  $zipFileName = "$fileStub.zip";

  #dump out the data source's SQL (includes all cubes depending on the database)
  `/usr/bin/mysqldump -h$Lib::KoalaConfig::dbServerName -u app -p$Lib::KoalaConfig::password --ignore-table=$dbName.export --ignore-table=$dbName._export $dbName | /usr/bin/zip -q > $zipFileName`;
  `printf "@ -\n@=$sqlFileName\n" | zipnote -w $zipFileName`;

  #write out our update run info and get unique ID (for roll back)
  $zipFileName = $dataDir . $zipFileName;
  $query = "INSERT INTO $dsSchema.update_history (userID, timestamp, filename) \
      VALUES ($userID, NOW(), '$zipFileName')";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------------
#
# Convert all of the specified user's uploaded data source files to CSV if
# they're XLS or XLSX (MS Excel). Upon conversion, the original source file's
# deleted to avoid future confusion.
#

sub ds_convert_to_csv
{
  my ($filename, $workbook, $sheet, $searchString, $tab);
  my ($DIRHANDLE);
  my (@tabs);
  my ($db, $userID, $key, $excelTabs) = @_;

  DBG("ds_convert_to_csv: Starting CSV conversion");
  DBG("ds_convert_to_csv: Args $userID $key $excelTabs");
  ds_telemetry($db, "Converting Excel data files to CSV");

  #turn our (potential) list of Excel tabs to import into an array
  $excelTabs =~ m/\"(.*)\"/;
  $excelTabs = $1;
  @tabs = split(',', $excelTabs);

  #hunt through every file in the upload directory, looking for files uploaded
  #by our user for this run that need to be converted
  opendir(DIRHANDLE, "/opt/apache/app/tmp");
  while (defined($filename = readdir(DIRHANDLE)))
  {
    DBG("ds_convert_to_csv: Examining file $filename");

    #only consider files uploaded by our user for this run
    if (($filename =~ m/^$userID\.$key\./) && (!($filename =~ m/\.work$/)))
    {

      #if we're dealing with an XLS file
      if ($filename =~ m/\.xls$/i)
      {
        DBG("Calling xls2csv on $filename");
        ds_telemetry($db, "Extracting data from legacy XLS file $filename");
        `/bin/sh /usr/bin/xls2csv \"/opt/apache/app/tmp/$filename\" -o \"/opt/apache/app/tmp/$filename.csv\"`;
        `rm /opt/apache/app/tmp/\"$filename\"`
      }

      #if we're dealing with an XLSX file
      if (($filename =~ m/\.xlsx$/i) || ($filename =~ m/\.xlsm$/i))
      {

        #escape out any "special" regex characters in the file name
        $searchString = quotemeta($filename);

        #for every tab in the file we're supposed to be importing
        foreach $tab (@tabs)
        {
          if ($tab =~ m/^$searchString (\d+)$/)
          {
            DBG("Calling xlsx2csv on tab $1 of $filename");
            ds_telemetry($db, "Extracting data from tab $1 of $filename");
            `/usr/bin/python /opt/xlsx2csv/xlsx2csv.py -e -s $1 -i \"/opt/apache/app/tmp/$filename\" \"/opt/apache/app/tmp/$filename-$1.csv\"`;
          }
        }

        `rm /opt/apache/app/tmp/\"$filename\"`
      }
    }
  }

  DBG("ds_convert_to_csv: Finished");
  ds_telemetry($db, "Done extracting data from Excel files");
}



#-------------------------------------------------------------------------------
#
# Convert a nested table-formatted file that follows the IRIv1 layout into
# a tabular file.
#

sub ds_nested_iriv1_to_csv
{
  my ($csv, $line, $time, $geog, $prod, $dimData, $colref);
  my ($INPUT, $OUTPUT);
  my (@columns, @outputCols);
  my ($db, $filename) = @_;

  ds_telemetry($db, "Converting $filename from nested (IRIv1) to tabular");

  #instantiate our CSV object
  $csv = Text::CSV_XS->new( {binary => 1} );

  #open the input CSV file for reading
  open($INPUT, "</opt/apache/app/tmp/$filename");

  #open our output CSV file
  open(OUTPUT, ">/opt/apache/app/tmp/$filename.1");

  #read the first line of the file, containing measure names
  $colref = $csv->getline($INPUT);
  @columns = @$colref;

  #shift off the first (empty) field from the source file
  shift(@columns);

  #build a tabular header line, and write it out
  $outputCols[0] = "Geog";
  $outputCols[1] = "Time";
  $outputCols[2] = "Prod";
  push(@outputCols, @columns);
  $csv->print(OUTPUT, \@outputCols);
  print OUTPUT "\n";

  #cycle through the data in the source file, converting to tabular
  while ($colref = $csv->getline($INPUT))
  {
    #parse the CSV line we just took in, and convert to array
    @columns = @$colref;

    #if the line contains dimension info (it only contains one item)
    if (length($columns[1]) < 1)
    {

      #don't know if this is a geog or time yet, but it's one of the two
      $dimData = $columns[0];

      #read the next line (contains dimension info, or measure data)
      $colref = $csv->getline($INPUT);
      @columns = @$colref;

      #if the second line also contains dimension info (only one item)
      if (length($columns[1]) < 1)
      {
        #the first piece of dimension info we read was a geog
        $geog = $dimData;

        #the second piece was a time period
        $time = $columns[0];

        #and we'll read/parse the next line of data, and continue processing
        $colref = $csv->getline($INPUT);
        @columns = @$colref;
      }

      #else if the next line contains data, so we only got a time period
      else
      {
        $time = $dimData;

        #NB: we've already read and parsed the next line of data
      }
    }

    #convert line of data to tabular format
    undef(@outputCols);
    $outputCols[0] = $geog;
    $outputCols[1] = $time;
    push(@outputCols, @columns);

    #convert array to CSV, and write to output file
    $csv->print(OUTPUT, \@outputCols);
    print OUTPUT "\n";
  }

  #close and flush out the input/output files
  close($INPUT);
  close(OUTPUT);

  #unlink the source file
  unlink("/opt/apache/app/tmp/$filename");

  #rename the output file to the source file's name
  rename("/opt/apache/app/tmp/$filename.1", "/opt/apache/app/tmp/$filename");
}



#-------------------------------------------------------------------------------
#
# Convert a nested table-formatted file that follows the IRIv2 layout into
# a tabular file.
#

sub ds_nested_iriv2_to_csv
{
  my ($csv, $line, $time, $geog, $prod, $colref);
  my (@columns, @outputCols);
  my ($INPUT, $OUTPUT);
  my ($db, $filename) = @_;

  ds_telemetry($db, "Converting $filename from nested (IRIv2) to tabular");

  #instantiate our CSV object
  $csv = Text::CSV_XS->new( {binary => 1} );

  #open the input CSV file for reading
  open($INPUT, "</opt/apache/app/tmp/$filename");

  #open our output CSV file
  open(OUTPUT, ">/opt/apache/app/tmp/$filename.1");

  #burn the first line of the file, contains "Analyzer Report" string
  $colref = $csv->getline($INPUT);

  #read the second line of the file, contains geography as first field
  $colref = $csv->getline($INPUT);
  @columns = @$colref;
  $geog = $columns[0];

  #read the third line of the file, containing measure names
  $colref = $csv->getline($INPUT);
  @columns = @$colref;

  #shift off the first (empty) field from the measure list
  shift(@columns);

  #build a tabular header line, and write it out
  $outputCols[0] = "Geog";
  $outputCols[1] = "Time";
  $outputCols[2] = "Prod";
  push(@outputCols, @columns);
  $csv->print(OUTPUT, \@outputCols);
  print OUTPUT "\n";

  #read the fourth line from the file, containing time period
  $colref = $csv->getline($INPUT);
  @columns = @$colref;
  $time = $columns[0];

  #cycle through the data in the source file, converting to tabular
  while ($colref = $csv->getline($INPUT))
  {
    #parse the CSV line we just took in, and convert to array
    @columns = @$colref;

    #if the line begins a dimension info block (contains "Analyzer Report")
    if ($columns[1] =~ m/Analyzer Report/)
    {

      #read the next line, contains geography as first field
      $colref = $csv->getline($INPUT);
      @columns = @$colref;
      $geog = $columns[0];

      #burn the next line, contains redundant measure info
      $colref = $csv->getline($INPUT);

      #read the next line, contains time as first field
      $colref = $csv->getline($INPUT);
      @columns = @$colref;
      $time = $columns[0];

      #and we'll read/parse the next line of data, and continue processing
      $colref = $csv->getline($INPUT);
      @columns = @$colref;
    }

    #if the line starts a new time period (only first column contains data)
    if ((length($columns[1]) < 1) || ($columns[1] =~ m/^\s+$/))
    {
      #set the time variable
      $time = $columns[0];

      #and we'll read/parse the next line of data, and continue processing
      $colref = $csv->getline($INPUT);
      @columns = @$colref;
    }

    #convert line of data to tabular format
    undef(@outputCols);
    $outputCols[0] = $geog;
    $outputCols[1] = $time;
    push(@outputCols, @columns);

    #convert array to CSV, and write to output file
    $csv->print(OUTPUT, \@outputCols);
    print OUTPUT "\n";
  }

  #close and flush out the input/output files
  close($INPUT);
  close(OUTPUT);

  #unlink the source file
  unlink("/opt/apache/app/tmp/$filename");

  #rename the output file to the source file's name
  rename("/opt/apache/app/tmp/$filename.1", "/opt/apache/app/tmp/$filename");
}



#-------------------------------------------------------------------------------
#
# Convert a nested table-formatted file that follows the Nielsen layout into
# a tabular file.
#

sub ds_nested_nielsen_to_csv
{
  my ($csv, $line, $time, $geog, $prod, $colref);
  my (@columns, @outputCols);
  my ($INPUT, $OUTPUT);
  my ($db, $filename) = @_;

  DBG("ds_nested_nielsen_to_csv: Starting for $filename");
  ds_telemetry($db, "Converting $filename from nested (Nielsen) to tabular");

  #instantiate our CSV object
  $csv = Text::CSV_XS->new( {binary => 1} );

  #open the input CSV file for reading
  open($INPUT, "</opt/apache/app/tmp/$filename") or die("ds_nested_nielsen_to_csv: Couldn't open $filename $!");

  #open our output CSV file
  open(OUTPUT, ">/opt/apache/app/tmp/$filename.1");

  #read the first line of the file, containing measure names
  $colref = $csv->getline($INPUT);
  @columns = @$colref;

  #shift off the first three (empty) fields from the source file
  shift(@columns);
  shift(@columns);
  shift(@columns);

  #build a tabular header line, and write it out
  $outputCols[0] = "Geog";
  $outputCols[1] = "Time";
  $outputCols[2] = "Prod";
  push(@outputCols, @columns);
  $csv->print(OUTPUT, \@outputCols);
  print OUTPUT "\n";
  DBG("ds_nested_nielsen_to_csv: Wrote header line $line");

  #cycle through the data in the source file, converting to tabular
  while ($colref = $csv->getline($INPUT))
  {
    #parse the CSV line we just took in, and convert to array
    @columns = @$colref;

    #if the line contains geography dimension info (only 1 item in 1st column)
    if ((length($columns[0]) > 1) && (length($columns[1]) < 1))
    {
      $geog = $columns[0];

      #read the next line (contains dimension info, or measure data)
      $colref = $csv->getline($INPUT);
      @columns = @$colref;
    }

    #if the line contains time dimension info (only 1 item in 2nd column)
    if ((length($columns[0]) < 1) &&
        (length($columns[1]) > 1) &&
        (length($columns[2]) < 1))
    {
      $time = $columns[1];

      #read the next line (contains measure data)
      $colref = $csv->getline($INPUT);
      @columns = @$colref;
    }

    #convert line of data to tabular format
    undef(@outputCols);
    $outputCols[0] = $geog;
    $outputCols[1] = $time;
    shift(@columns);
    shift(@columns);
    push(@outputCols, @columns);

    #convert array to CSV, and write to output file
    $csv->print(OUTPUT, \@outputCols);
    print OUTPUT "\n";
  }

  #close and flush out the input/output files
  close($INPUT);
  close(OUTPUT);

  #unlink the source file
  unlink("/opt/apache/app/tmp/$filename");

  #rename the output file to the source file's name
  rename("/opt/apache/app/tmp/$filename.1", "/opt/apache/app/tmp/$filename");
}



#-------------------------------------------------------------------------------
#
# If the source file is a nested table, determine which type of nested
# table it is (Nielsen, IRIv1, IRIv2) and transform it into a tabular
# data file.
#

sub ds_nested_to_tabular
{
  my ($filename, $line);
  my ($DIRHANDLE, $INPUT);
  my ($db, $userID, $key) = @_;

  #hunt through every file in the upload directory, looking for files uploaded
  #by our user that need to be converted
  opendir(DIRHANDLE, "/opt/apache/app/tmp");
  while (defined($filename = readdir(DIRHANDLE)))
  {
    #only consider CSV files uploaded by our user for this run
    if (($filename =~ m/^$userID\.$key\./) && ($filename =~ m/\.csv$/))
    {

      DBG("ds_nested_to_tabular: Processing source file $filename");
      ds_telemetry($db, "Determining file layout for $filename");

      #open the input CSV file, and read the first line of data
      open(INPUT, "/opt/apache/app/tmp/$filename");
      $line = <INPUT>;
      close(INPUT);

      #see if we're dealing with a Nielsen nested file (A1, B1, C1 are blank,
      #D1 has text)
      if ($line =~ m/^\,\,\,.*/)
      {
        ds_nested_nielsen_to_csv($db, $filename);
      }

      #see if we're dealing with an IRIv2 file ("Analyzer Report" appears
      #somewhere in the first line of data)
      elsif ($line =~ m/Analyzer Report/)
      {
        ds_nested_iriv2_to_csv($db, $filename);
      }

      #see if we're dealing with an IRIv1 file (A1 blank, B1 has text)
      elsif ($line =~ m/^\,.*/)
      {
        ds_nested_iriv1_to_csv($db, $filename);
      }
    }
  }
}



#-------------------------------------------------------------------------------
#
# Combine all of the tabular files in the tmp directory belonging to the
# specified user ID. This is called as part of the create/update data source
# process to handle multiple files. We're assuming that the correct processes
# have already run to turn non-CSV non-tabular files into CSV tabular.
# Return the name of the tabular CSV file we create.
#

sub ds_combine_tabular_files
{
  my ($output, $filename, $first, $line, $colHeaders, $query, $firstFile);
  my ($firstTab, $INPUT, $OUTPUT, $DIRHANDLE);
  my ($db, $dsID, $userID, $key) = @_;

  DBG("ds_combine_tabular_files: Called for user $userID and key $key");
  ds_telemetry($db, "Combining data files");

  $output = "/opt/apache/app/tmp/$userID.$key.work";

  #if we're importing data from Prep, we know we have 1 file in CSV format
  #that's ready for loading. We can shortcut this process and save a little
  #I/O activity and temporary storage space
  if ($key =~ m/prep\.(\d+)/)
  {
    $filename = "/opt/apache/app/tmp/$userID.$key.export.csv";
    rename($filename, $output);
    return($output);
  }

  #create our output file and give it a unique filename based on our run key
  open(OUTPUT, ">$output") or die("ds_combine_tabular_files: Unable to open $output - $!");;

  opendir(DIRHANDLE, "/opt/apache/app/tmp");
  $first = 1;
  while (defined($filename = readdir(DIRHANDLE)))
  {

    #only grab data files uploaded by this user
    if (($filename =~ m/^$userID\.$key\./) && (!($filename =~ m/\.work$/)))
    {

      #open the data file for reading
      open(INPUT, "< /opt/apache/app/tmp/$filename");

      #if we're the first file, save the column headers for comparison check
      if ($first == 1)
      {
        $line = <INPUT>;
        $line =~ s/,+$//;
        $colHeaders = $line;

        $filename =~ m/^\d+\.\d+\.(.*?)\..*?\-(\d+)/;
        $firstFile = $1;
        $firstTab = $2;

        print OUTPUT $line;
      }

      #if we aren't the first file in the sequence, burn the column headers
      else
      {
        $line = <INPUT>;
        $line =~ s/,+$//;

        if (lc($line) ne lc($colHeaders))
        {
          $filename =~ m/^\d+\.\d+\.(.*?)\..*?\-(\d+)/;
          ds_set_status($db, $dsID, "ERROR: File $1, tab $2 doesn't match the format of other files ($firstFile, tab $firstTab) in this load");
          ds_telemetry($db, "ERROR!!! File $1, tab $2 doesn't match the format of other files ($firstFile, tab $firstTab) in this data load");

          DSRutil_clear_status($db);

          die("File $filename doesn't match format of other files in bulk load!");
        }
      }

      #read a line from INPUT and append it to OUTPUT
      while ($line = <INPUT>)
      {
        print OUTPUT $line;
      }
      close(INPUT);

      #after a complete run through the loop, we're no longer looking at the
      #first file in the sequence
      $first = 0;
    }

  }
  closedir(DIRHANDLE);

  close(OUTPUT);

  return($output);
}



#-------------------------------------------------------------------------------
#
# If the user chose to do so, apply a normalization script against the
# tabular source file to transform it in some non-standard custom way.
#

sub ds_apply_normalization_script
{
  my ($scriptFileName, $outputFileName, $filename, $sourceFile);

  my ($normalizationScript, $userID, $key) = @_;


  $scriptFileName = "/opt/apache/app/client/import/" .
      $Lib::KoalaConfig::cloudname . "/" . $normalizationScript;

  opendir(DIRHANDLE, "/opt/apache/app/tmp");
  while (defined($filename = readdir(DIRHANDLE)))
  {

    #only grab data files uploaded by this user
    if ($filename =~ m/^$userID\.$key\./)
    {

      $sourceFile = "/opt/apache/app/tmp/$filename";
      $outputFileName = "/opt/apache/app/tmp/$filename" . "_SCRIPT";
      $holdFileName = "/opt/apache/app/tmp/$filename" . "_HOLD";

      #run the script against the source file
      `$scriptFileName '$sourceFile' '$outputFileName'`;

      #rename the output file to the source file
#      unlink($sourceFile);
`cp '$outputFileName' '$holdFileName'`;
      rename($outputFileName, $sourceFile);
    }
  }
  closedir(DIRHANDLE);
}



#-------------------------------------------------------------------------------
#
# Parse out a type, duration, and end date from the supplied date string.
#

sub ds_parse_date_string
{
  my ($type, $duration, $endDate, $query, $dbOutput, $year, $month, $day);
  my ($status);

  my ($db, $time) = @_;


  #handle any Kroger Market6 custom dates
  if ($time =~ m/^\d\d\d\d PD \d+ WK \d+ \(\d+\)$/)
  {
    $time = "CUSTOM";
  }

  #handle whackadoo Nielsen date formats
  if ($time =~ m/^year ago (.*) thru (.*)$/i)
  {
    $time = "$1 ENDING $2";
  }
  if ($time =~ m/^year ago (.*) ending (.*)$/i)
  {
    $time = "$1 ENDING $2";
  }
  if ($time =~ m/($1) YAG (.*\d\d\d\d\.1)/)
  {
    $time = "$1 ENDING $2";
  }
  if ($time =~ m/year to date thru week ending/i)
  {
    $time = "CUSTOM";
  }
  if ($time =~ m/^cal yr (\d+) W\/E (.*)$/i)
  {
    $time = "52 WEEKS ENDING $2";
  }

  #some users are accidentally pulling dates out of AOD using periods rather
  #than slashes for separators
  if ($time =~ m/^(.*) (\d+)\.(\d+)\.(\d+)(.*)$/i)
  {
    $time = "$1 $2/$3/$4" . $5;
  }

  #handle "4/5 Week: June 21, 2015, Year Ago"
  if ($time =~ m/4\/5 week: (.*)/i)
  {
    $time = "4 Week Ending $1";
  }

  #if the time starts with "Latest" or "Current", strip it off
  if ($time =~ m/Latest (.*)/i)
  {
    $time = $1;
  }
  if ($time =~ m/Current (.*)/i)
  {
    $time = $1;
  }
  if ($time =~ m/previous (.*)/i)
  {
    $time = $1;
  }
  if ($time =~ m/cytd wks \- (.*)/i)
  {
    $time = $1;
  }

  #handle custom Nielsen year-to-date formats
  if ($time =~ m/^cytd /i)
  {
    $time = "CUSTOM";
  }

  #handle raw data that uses "WEEK ENDING" to imply a duration of 1
  if (!($time =~ m/^[0-9]/))
  {
    $time = "1 $time";
  }

  #extract the numerical duration value
  $time =~ m/([0-9]+)[ \-](.*)/;
  $duration = $1;
  $time = $2;

  #if the period type is a year
  if ($time =~ m/^year ending|years ending/i)
  {
    $type = 10;
  }

  #if the period type is a month
  elsif ($time =~ m/^month ending|months ending/i)
  {
    $type = 20;
  }

  #if the period type is a week
  elsif ($time =~ m/^week ending|weeks ending|wk ending|wks ending|wks curr|we|wk|w\/e/i)
  {
    $type = 30;
  }

  #if the period type is a day
  elsif ($time =~ m/^day ending|days ending|day/i)
  {
    $type = 40;
  }

  #if the period type is an hour
  elsif ($time =~ m/^hour ending|hours ending/i)
  {
    $type = 50;
  }

  #custom time period
  else
  {
    $type = 0;
  }

  #extract the ending date and time
  if ($time =~ m/(\d\d\d\d)\/(\d\d)\/(\d\d)/)
  {           #2010/12/31
    $year = $1;
    $month = $2;
    $day = $3;
  }
  elsif ($time =~ m/([0-9]+)\/([0-9]+)\/([0-9]+)/)
  {           #12/31/2010 and 12/31/10
    $month = $1;
    $day = $2;
    $year = $3;
  }
  elsif ($time =~ m/([0-9]+)\-([0-9]+)\-([0-9]+)/)
  {           #12-31-2010 and 12-31-10
    $month = $1;
    $day = $2;
    $year = $3;
  }
  elsif ($time =~ m/ (\w+) (\d{1,2}), (\d{4})/)
  {           #Nov 2, 2011
    $month = $1;
    $day = $2;
    $year = $3;
    if ($month =~ /^Jan/i)
    {
      $month = 1;
    }
    elsif ($month =~ /^Feb/i)
    {
      $month = 2;
    }
    elsif ($month =~ /^Mar/i)
    {
      $month = 3;
    }
    elsif ($month =~ /^Apr/i)
    {
      $month = 4;
    }
    elsif ($month =~ /^May/i)
    {
      $month = 5;
    }
    elsif ($month =~ /^Jun/i)
    {
      $month = 6;
    }
    elsif ($month =~ /^Jul/i)
    {
      $month = 7;
    }
    elsif ($month =~ /^Aug/i)
    {
      $month = 8;
    }
    elsif ($month =~ /^Sep/i)
    {
      $month = 9;
    }
    elsif ($month =~ /^Oct/i)
    {
      $month = 10;
    }
    elsif ($month =~ /^Nov/i)
    {
      $month = 11;
    }
    elsif ($month =~ /^Dec/i)
    {
      $month = 12;
    }
  }
  elsif ($time =~ m/ (\w\w\w)(\d\d)(\d\d)\.\d/)
  {           #Aug0214.4
    $month = $1;
    $day = $2;
    $year = $3;
    $year = "20" . $year; #Y2K? What's that? ;-)
    if ($month =~ m/Jan/i)
    {
      $month = 1;
    }
    elsif ($month =~ m/Feb/i)
    {
      $month = 2;
    }
    elsif ($month =~ /Mar/i)
    {
      $month = 3;
    }
    elsif ($month =~ /Apr/i)
    {
      $month = 4;
    }
    elsif ($month =~ /May/i)
    {
      $month = 5;
    }
    elsif ($month =~ /Jun/i)
    {
      $month = 6;
    }
    elsif ($month =~ /Jul/i)
    {
      $month = 7;
    }
    elsif ($month =~ /Aug/i)
    {
      $month = 8;
    }
    elsif ($month =~ /Sep/i)
    {
      $month = 9;
    }
    elsif ($month =~ /Oct/i)
    {
      $month = 10;
    }
    elsif ($month =~ /Nov/i)
    {
      $month = 11;
    }
    elsif ($month =~ /Dec/i)
    {
      $month = 12;
    }
  }

  #handle non-Y2K compliant year strings
  if ($year < 100)
  {
    $year = "20" . $year;
  }

  #if this is a "year ago" period, subtract 52 weeks (let the DB do it)
  if ($time =~ m/year ago$/i)
  {
    $query = "SELECT DATE_SUB('$year-$month-$day 00:00:00', INTERVAL 52 WEEK)";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    ds_db_err($db, $status, $query);
    $endDate = $dbOutput->fetchrow_array;
  }
  else
  {
    $endDate = "$year-$month-$day 00:00:00";
  }

  #if it's a custom time period, wipe out any garbage we tried to parse
  if ($type == 0)
  {
    $duration = "NULL";
    $endDate = "NULL";
  }

  return($time, $type, $duration, $endDate);
}



#-------------------------------------------------------------------------------
#
# Set flags to improve MySQL performance during data import/update process.
# These flags only affect the current database handle (the one passed to this
# function).
#

sub ds_set_mysql_import_flags
{
  my ($query);
  my ($db) = @_;

  #turn off SQL binary logging (if the system crashes during creation, anybody
  #in their right mind is going to assume the partially-created data source
  #is garbage anyways). Should result in noticeable speed improvement due to
  #reduced disk I/O
  $query = "SET sql_log_bin = 0";
  $db->do($query);

  #turn off the transaction isolation guarantee for the same reasons as above
  $query = "SET SESSION transaction_isolation='READ-UNCOMMITTED'";
  $db->do($query);
}



#-------------------------------------------------------------------------------
#
# Create the database schema for a new data source
#

sub ds_create_schema
{
  my ($query, $dbOutput, $orgID, $dataDir, $status);

  my ($db, $userID, $dsSchema) = @_;


  ds_telemetry($db, "Creating data source schema");

  #determine if we're using an external table space for capacity/IO reasons
  $orgID = KAPutil_get_user_org_id($db, $userID);

  $query = "SELECT dataStorage FROM app.orgs WHERE ID=$orgID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ds_db_err($db, $status, $query);
  ($dataDir) = $dbOutput->fetchrow_array;
  if (length($dataDir) > 2)
  {
    $dataDir = $dataDir . "/mysql/";
    $dataDir = "DATA DIRECTORY='$dataDir'";
  }
  else
  {
    $dataDir = "";
  }

  $query = "CREATE DATABASE $dsSchema";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);

  $query = "CREATE TABLE $dsSchema.products \
      ( \
        ID INT UNSIGNED NOT NULL AUTO_INCREMENT, \
        name VARCHAR(128) NOT NULL, \
        alias VARCHAR(128) DEFAULT NULL, \
        merged TINYINT DEFAULT 0, \
        PRIMARY KEY (ID), \
        INDEX name (name) \
      ) $dataDir";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);

  $query = "CREATE TABLE $dsSchema.timeperiods \
      ( \
        ID INT UNSIGNED NOT NULL AUTO_INCREMENT, \
        name VARCHAR(128) NOT NULL, \
        alias VARCHAR(128) DEFAULT NULL, \
        duration TINYINT UNSIGNED, \
        type TINYINT UNSIGNED, \
        endDate DATETIME, \
        forecast TINYINT DEFAULT 0, \
        PRIMARY KEY (ID), \
        INDEX timeinfo (duration, type, endDate), \
        INDEX name (name) \
      ) $dataDir";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);

  $query = "CREATE TABLE $dsSchema.geographies \
      ( \
        ID INT UNSIGNED NOT NULL AUTO_INCREMENT, \
        name VARCHAR(128) NOT NULL, \
        alias VARCHAR(128) DEFAULT NULL, \
        PRIMARY KEY (ID), \
        INDEX name (name) \
      ) $dataDir";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);

  $query = "CREATE TABLE $dsSchema.measures \
      ( \
        ID INT UNSIGNED NOT NULL AUTO_INCREMENT, \
        name VARCHAR(128) NOT NULL, \
        alias VARCHAR(128) DEFAULT NULL, \
        calculation VARCHAR(512) DEFAULT NULL, \
        calcBeforeAgg TINYINT(1) DEFAULT 0, \
        prodAggRule VARCHAR(32) DEFAULT \"None\", \
        geoAggRule VARCHAR(32) DEFAULT \"None\", \
        timeAggRule VARCHAR(32) DEFAULT \"None\", \
        mergeAggRule VARCHAR(32) DEFAULT \"None\", \
        format VARCHAR(16) DEFAULT \"2,1,0,1\", \
        lastCalc DATETIME DEFAULT NULL, \
        PRIMARY KEY (ID), \
        INDEX name (name) \
      ) $dataDir";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);

  $query = "CREATE TABLE $dsSchema.facts \
      ( \
        productID INT UNSIGNED NOT NULL, \
        geographyID INT UNSIGNED NOT NULL, \
        timeID INT UNSIGNED NOT NULL, \
        updateID INT UNSIGNED, \
        PRIMARY KEY (productID, geographyID, timeID), \
        INDEX productID (productID ASC), \
        INDEX geographyID (geographyID ASC), \
        INDEX timeID (timeID ASC), \
        INDEX updateID (updateID) \
      ) $dataDir";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);

  #create attribute tables
  $query = "CREATE TABLE $dsSchema.product_attributes \
      ( \
        ID INT UNSIGNED NOT NULL AUTO_INCREMENT, \
        name VARCHAR(128) NOT NULL, \
        PRIMARY KEY (ID) \
      ) $dataDir";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);

  $query = "CREATE TABLE $dsSchema.product_attribute_values \
      ( \
        attributeID INT UNSIGNED NOT NULL, \
        itemID INT UNSIGNED NOT NULL, \
        value VARCHAR(128), \
        PRIMARY KEY (attributeID, itemID) \
      ) $dataDir";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);

  $query = "CREATE TABLE $dsSchema.geography_attributes \
      ( \
        ID INT UNSIGNED NOT NULL AUTO_INCREMENT, \
        name VARCHAR(128) NOT NULL, \
        PRIMARY KEY (ID) \
      ) $dataDir";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);

  $query = "CREATE TABLE $dsSchema.geography_attribute_values \
      ( \
        attributeID INT UNSIGNED NOT NULL, \
        itemID INT UNSIGNED NOT NULL, \
        value VARCHAR(128), \
        PRIMARY KEY (attributeID, itemID) \
      ) $dataDir";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);

  $query = "CREATE TABLE $dsSchema.time_attributes \
      ( \
        ID INT UNSIGNED NOT NULL AUTO_INCREMENT, \
        name VARCHAR(128) NOT NULL, \
        PRIMARY KEY (ID) \
      ) $dataDir";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);

  $query = "CREATE TABLE $dsSchema.time_attribute_values \
      ( \
        attributeID INT UNSIGNED NOT NULL, \
        itemID INT UNSIGNED NOT NULL, \
        value VARCHAR(128), \
        PRIMARY KEY (attributeID, itemID) \
      ) $dataDir";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);

  $query = "CREATE TABLE $dsSchema.measure_attributes \
      ( \
        ID INT UNSIGNED NOT NULL AUTO_INCREMENT, \
        name VARCHAR(128) NOT NULL, \
        PRIMARY KEY (ID) \
      ) $dataDir";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);

  $query = "CREATE TABLE $dsSchema.measure_attribute_values \
      ( \
        attributeID INT UNSIGNED NOT NULL, \
        itemID INT UNSIGNED NOT NULL, \
        value VARCHAR(128), \
        PRIMARY KEY (attributeID, itemID) \
      ) $dataDir";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);

  #create segment tables
  $query = "CREATE TABLE $dsSchema.product_segmentation \
      ( \
        ID INT UNSIGNED NOT NULL AUTO_INCREMENT, \
        name VARCHAR(128) NOT NULL, \
        parentID INT UNSIGNED DEFAULT NULL, \
        PRIMARY KEY (ID) \
      ) $dataDir";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);

  $query = "CREATE TABLE $dsSchema.product_segment \
      ( \
        ID INT UNSIGNED NOT NULL AUTO_INCREMENT, \
        segmentationID INT UNSIGNED NOT NULL, \
        name VARCHAR(128) NOT NULL, \
        PRIMARY KEY (ID) \
      ) $dataDir";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);

  $query = "CREATE TABLE $dsSchema.product_segment_item \
      ( \
        segmentationID INT UNSIGNED NOT NULL, \
        segmentID INT UNSIGNED NOT NULL, \
        itemID INT UNSIGNED NOT NULL, \
        PRIMARY KEY (segmentationID, itemID) \
      ) $dataDir";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);

  $query = "CREATE TABLE $dsSchema.product_seg_rules \
      ( \
        ID INT UNSIGNED NOT NULL AUTO_INCREMENT, \
        segmentationID INT UNSIGNED NOT NULL, \
        step INT UNSIGNED, \
        segmentID INT UNSIGNED NOT NULL, \
        rule VARCHAR(1024), \
        filter1 VARCHAR(512), \
        filter2 VARCHAR(512), \
        PRIMARY KEY(ID) \
      ) $dataDir";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);

  $query = "CREATE TABLE $dsSchema.geography_segmentation \
      ( \
        ID INT UNSIGNED NOT NULL AUTO_INCREMENT, \
        name VARCHAR(128) NOT NULL, \
        parentID INT UNSIGNED DEFAULT NULL, \
        PRIMARY KEY (ID) \
      ) $dataDir";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);

  $query = "CREATE TABLE $dsSchema.geography_segment \
      ( \
        ID INT UNSIGNED NOT NULL AUTO_INCREMENT, \
        segmentationID INT UNSIGNED NOT NULL, \
        name VARCHAR(128) NOT NULL, \
        PRIMARY KEY (ID) \
      ) $dataDir";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);

  $query = "CREATE TABLE $dsSchema.geography_segment_item \
      ( \
        segmentationID INT UNSIGNED NOT NULL, \
        segmentID INT UNSIGNED NOT NULL, \
        itemID INT UNSIGNED NOT NULL, \
        PRIMARY KEY (segmentationID, itemID) \
      ) $dataDir";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);

  $query = "CREATE TABLE $dsSchema.geography_seg_rules \
      ( \
        ID INT UNSIGNED NOT NULL AUTO_INCREMENT, \
        segmentationID INT UNSIGNED NOT NULL, \
        step INT UNSIGNED, \
        segmentID INT UNSIGNED NOT NULL, \
        rule VARCHAR(256), \
        filter1 VARCHAR(512), \
        filter2 VARCHAR(512), \
        PRIMARY KEY(ID) \
      ) $dataDir";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);

  $query = "CREATE TABLE $dsSchema.time_segmentation \
      ( \
        ID INT UNSIGNED NOT NULL AUTO_INCREMENT, \
        name VARCHAR(128) NOT NULL, \
        parentID INT UNSIGNED DEFAULT NULL, \
        PRIMARY KEY (ID) \
      ) $dataDir";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);

  $query = "CREATE TABLE $dsSchema.time_segment \
      ( \
        ID INT UNSIGNED NOT NULL AUTO_INCREMENT, \
        segmentationID INT UNSIGNED NOT NULL, \
        name VARCHAR(128) NOT NULL, \
        PRIMARY KEY (ID) \
      ) $dataDir";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);

  $query = "CREATE TABLE $dsSchema.time_segment_item \
      ( \
        segmentationID INT UNSIGNED NOT NULL, \
        segmentID INT UNSIGNED NOT NULL, \
        itemID INT UNSIGNED NOT NULL, \
        PRIMARY KEY (segmentationID, itemID) \
      ) $dataDir";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);

  $query = "CREATE TABLE $dsSchema.time_seg_rules \
      ( \
        ID INT UNSIGNED NOT NULL AUTO_INCREMENT, \
        segmentationID INT UNSIGNED NOT NULL, \
        step INT UNSIGNED, \
        segmentID INT UNSIGNED NOT NULL, \
        rule VARCHAR(256), \
        filter1 VARCHAR(512), \
        filter2 VARCHAR(512), \
        PRIMARY KEY(ID) \
      ) $dataDir";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);

  #create segmentation hierarchy tables
  $query = "CREATE TABLE $dsSchema.product_seghierarchy \
      ( \
        ID INT UNSIGNED NOT NULL AUTO_INCREMENT, \
        name VARCHAR(128) NOT NULL, \
        segmentations VARCHAR(128), \
        namePattern VARCHAR(45), \
        PRIMARY KEY (ID) \
      ) $dataDir";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);

  $query = "CREATE TABLE $dsSchema.geography_seghierarchy \
      ( \
        ID INT UNSIGNED NOT NULL AUTO_INCREMENT, \
        name VARCHAR(128) NOT NULL, \
        segmentations VARCHAR(128), \
        namePattern VARCHAR(45), \
        PRIMARY KEY (ID) \
      ) $dataDir";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);

  $query = "CREATE TABLE $dsSchema.time_seghierarchy \
      ( \
        ID INT UNSIGNED NOT NULL AUTO_INCREMENT, \
        name VARCHAR(128) NOT NULL, \
        segmentations VARCHAR(128), \
        namePattern VARCHAR(45), \
        PRIMARY KEY (ID) \
      ) $dataDir";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);

  $query = "CREATE TABLE $dsSchema.product_list \
      ( \
        ID INT UNSIGNED NOT NULL AUTO_INCREMENT, \
        name VARCHAR(128) NOT NULL, \
        script MEDIUMTEXT, \
        members MEDIUMTEXT, \
        lastUpdated DATETIME, \
        PRIMARY KEY (ID) \
      ) $dataDir";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);

  $query = "CREATE TABLE $dsSchema.geography_list \
      ( \
        ID INT UNSIGNED NOT NULL AUTO_INCREMENT, \
        name VARCHAR(128) NOT NULL, \
        script VARCHAR(10240), \
        members VARCHAR(1024), \
        lastUpdated DATETIME, \
        PRIMARY KEY (ID) \
      ) $dataDir";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);

  $query = "CREATE TABLE $dsSchema.time_list \
      ( \
        ID INT UNSIGNED NOT NULL AUTO_INCREMENT, \
        name VARCHAR(128) NOT NULL, \
        script VARCHAR(10240), \
        members VARCHAR(4096), \
        lastUpdated DATETIME, \
        PRIMARY KEY (ID) \
      ) $dataDir";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);

  $query = "CREATE TABLE $dsSchema.measure_list \
      ( \
        ID INT UNSIGNED NOT NULL AUTO_INCREMENT, \
        name VARCHAR(128) NOT NULL, \
        script VARCHAR(10240), \
        members VARCHAR(1024), \
        lastUpdated DATETIME, \
        PRIMARY KEY (ID) \
      ) $dataDir";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);

  $query = "CREATE TABLE $dsSchema.product_aggregate \
      ( \
        ID INT UNSIGNED NOT NULL AUTO_INCREMENT, \
        name VARCHAR(128) NOT NULL, \
        addScript MEDIUMTEXT, \
        subtractScript VARCHAR(1024), \
        addMembers MEDIUMTEXT, \
        subtractMembers VARCHAR(1024), \
        lastUpdated DATETIME, \
        PRIMARY KEY (ID) \
      ) $dataDir";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);

  $query = "CREATE TABLE $dsSchema.geography_aggregate \
      ( \
        ID INT UNSIGNED NOT NULL AUTO_INCREMENT, \
        name VARCHAR(128) NOT NULL, \
        addScript MEDIUMTEXT, \
        subtractScript VARCHAR(1024), \
        addMembers MEDIUMTEXT, \
        subtractMembers VARCHAR(1024), \
        lastUpdated DATETIME, \
        PRIMARY KEY (ID) \
      ) $dataDir";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);

  $query = "CREATE TABLE $dsSchema.time_aggregate \
      ( \
        ID INT UNSIGNED NOT NULL AUTO_INCREMENT, \
        name VARCHAR(128) NOT NULL, \
        addScript MEDIUMTEXT, \
        subtractScript VARCHAR(1024), \
        addMembers MEDIUMTEXT, \
        subtractMembers VARCHAR(1024), \
        lastUpdated DATETIME, \
        appendEndDate TINYINT, \
        PRIMARY KEY (ID) \
      ) $dataDir";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);

  $query = "CREATE TABLE $dsSchema.products_merged \
      ( \
        mergedID INT UNSIGNED NOT NULL, \
        primaryProd INT UNSIGNED, \
        mergedProds VARCHAR(128) NOT NULL, \
        PRIMARY KEY (mergedID) \
      ) $dataDir";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);

  $query = "CREATE TABLE $dsSchema.update_history \
      ( \
        ID INT UNSIGNED NOT NULL AUTO_INCREMENT, \
        userID INT UNSIGNED NOT NULL, \
        timestamp DATETIME NOT NULL, \
        filename VARCHAR(45) DEFAULT NULL, \
        PRIMARY KEY (ID) \
      ) $dataDir";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);
}



#-------------------------------------------------------------------------------
#
# Get the index of the column that matches the supplied name
#

sub get_col_index
{
  my ($i, $idx, $lcName, $lcColName);

  my ($name, @columns) = @_;


  undef($idx);

  $i = 0;
  while (defined($columns[$i]))
  {
    $lcName = lc($name);
    $lcColName = lc($columns[$i]);
    if ($lcName eq $lcColName)
    {
      return($i);
    }
    $i++;
  }

  #if we're here, no match so return undef
  undef($i);
  return($i);
}



#-------------------------------------------------------------------------------
#
# Create a new data source from the specified data files uploaded or selected
# by the user. Returns the new data source's ID.
#

sub ds_create_update
{
  my ($query, $dbOutput, $status, $dsSchema, $id, $index, $name, $INPUT);
  my ($product, $geo, $time, $type, $duration, $endDate, $idx, $i, $j, $ok);
  my ($attrName, $q_attr, $attrID, $q_oldName, $curUPC, $csv, $colref, $tmp);
  my ($paliasColumn, $galiasColumn, $taliasColumn, $upcColIndex, $jobID);
  my ($productColumn, $geographyColumn, $timeColumn, $dsName, $opInfo, $email);
  my ($userName, $sourceFile, $sourceFileRecords, $isSeg, $isBlank, $valSet);
  my ($hint, $segName, $q_seg, $segID, $aggRule, $pAggRule, $gAggRule, $val);
  my ($tAggRule, $aggRules, $q_pAggRule, $q_gAggRule, $q_tAggRule, $subq1);
  my ($itemID, $upcVal, $itemName, $count, $line, $segmentationID, $segmentID);
  my ($updateq, $productID, $geoID, $timeID, $measureID, $pct, $q_measure);
  my ($q_prod, $q_geo, $q_time, $q_alias, $timeIndex, $upcAttrID, $q_product);
  my ($prodID, $subq2, $attributeID, $q_value, $q_dsName, $validUPCs);
  my ($prepDB, $mode, $flowID, $prepUserID);
  my (@columns, @tmpArray, @dataReadAhead, @aggRules);
  my (@pattrCols, @gattrCols, @tattrCols);
  my (@psegCols, @gsegCols, @tsegCols, @measureCols);
  my (@factsValuesArray, @prodAttrValuesArray, @geoAttrValuesArray);
  my (@timeAttrValuesArray, @prodSegValuesArray, @geoSegValuesArray);
  my (@timeSegValuesArray);
  my (%pattrHash, %gattrHash, %tattrHash);
  my (%pattrNameHash, %gattrNameHash, %tattrNameHash, %colNameDedupe);
  my (%psegmentationHash, %gsegmentationHash, %tsegmentationHash, %upcHash);
  my (%psegmentationNameHash, %gsegmentationNameHash, %measureHash);
  my (%tsegmentationNameHash, %measureNameHash);
  my (%prodHash, %geoHash, %timeHash, %psegHash, %gsegHash, %tsegHash);
  my (%productNameHash);
  my (%prodAttrSeenHash);

  my ($db, $userID, $dsID, $createNew, $layout, $pmatch, $gmatch, $tmatch, $key, $excelTabs, $options, $dontOverwriteNames, $normalizationScript) = @_;


  $pmatch = lc($pmatch);

  DBG("ds_create_update: Starting data source update");
  DBG("ds_create_update: pmatch=$pmatch gmatch=$gmatch tmatch=$tmatch");
  DBG("ds_create_update: key=$key excelTabs=$excelTabs options=$options no name update=$dontOverwriteNames");

  #clear any old home feed items that might be invalidated by this update
  Social_clear_ds_items($db, $dsID);

  #get our update ID that we're going to use for telemetry logging
  $query = "INSERT INTO audit.telemetry_data (dsID, startTime) \
      VALUES ($dsID, NOW())";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);
  $dsUpdateID = $db->{q{mysql_insertid}};

  #add our startup telemetry
  $query = "UPDATE audit.telemetry_data \
      SET telemetry = CONCAT(NOW(), ': Starting data source update\n') \
      WHERE ID=$dsUpdateID";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);
  ds_telemetry($db, "Matching dimensions: product=$pmatch, geo=$gmatch, time=$tmatch");
  ds_telemetry($db, "Options: $options");
  if ($normalizationScript ne "none")
  {
    $normalizationScript =~ m/^(.*).pl$/;
    ds_telemetry($db, "Normalization script: $1");
  }

  utils_audit($db, $userID, "Updated data source|$dsUpdateID", $dsID, 0, 0);

  #failsafe - wait if something else is already updating the DS
  $ok = DSRutil_operation_ok($db, $dsID, 0, "DS-UPDATE");
  while ($ok != 1)
  {
    ds_telemetry($db, "Another job is already using this data source - waiting.");
    sleep(300);
    $ok = DSRutil_operation_ok($db, $dsID, 0, "DS-UPDATE");
  }

  KAPutil_job_store_status($db, $userID, $dsID, 0, "DS-UPDATE", "Updating data source");
  KAPutil_job_update_state($db, "START");

  $query = "SELECT name FROM dataSources WHERE ID=$dsID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute();
  ds_db_err($db, $status, $query);
  ($dsName) = $dbOutput->fetchrow_array;

  $q_dsName = $db->quote($dsName);
  $query = "UPDATE app.jobs \
      SET opInfo='Update|$userName|NO', dsName=$q_dsName, lastAction=NOW() \
      WHERE pid=$$ AND dsID=$dsID";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);

  #set some internal MySQL flags to improve data import performance
  ds_set_mysql_import_flags($db);

  #build schema name
  $dsSchema = "datasource_" . $dsID;

  if ($createNew > 0)
  {
    ds_set_status($db, $dsID, "Creating data source schema");
    ds_create_schema($db, $userID, $dsSchema);
  }

  #
  #NB: The (old) rough idea (was) is to scan the header row, and build a hash
  #    (%columnTypes) that indexes each column to a particular dimension
  #    or data type. In addition, attributes and segmentations have their
  #    own hashes for each dimension that contain the names of the attributes
  #    or segmentations. Once we know what each column is supposed to be,
  #    we run through each row and put each cell's value in the appropriate
  #    location.
  #    To make this interact with the user, all we have to do is let them
  #    make alterations to each column's assignment before we run the
  #    actual import process.
  #    To import other types of data (syndicated, RetailLink, etc) we just
  #    transform it into tabular (if it isn't already) and run the process.
  #

  #if we're updating an existing data source, back it up
  if ($createNew == 0)
  {
    KAPutil_job_update_state($db, "BACKUP");
    ds_set_status($db, $dsID, "Backing up data source");
    ds_dump_data_source($db, $dsID, $userID, $key);
  }

  #if the input file(s) are in non-CSV format, convert them
  KAPutil_job_update_state($db, "EXCEL-EXTRACT");
  ds_set_status($db, $dsID, "Extracting data from source file");
  ds_convert_to_csv($db, $userID, $key, $excelTabs);

  KAPutil_job_update_state($db, "NORMALIZE");

  #if we were handed something that looks like a valid normalization script
  if (($normalizationScript ne "(None)") && (length($normalizationScript) > 4))
  {
    ds_set_status($db, $dsID, "Applying normalization script");
    ds_apply_normalization_script($normalizationScript, $userID, $key);
  }

  #if we're dealing with nested tables, convert to tabular
  if ($layout eq "Nested")
  {
    ds_nested_to_tabular($db, $userID, $key);
  }

  #if we're dealing with multiple files, combine them into one
  $sourceFile = ds_combine_tabular_files($db, $dsID, $userID, $key);

  #get the total number of records in the file for status update purposes
  $sourceFileRecords = ds_get_source_record_count($sourceFile);

  KAPutil_job_update_state($db, "LOADING");

  #open the input file - assuming tabular CSV w/header line for now
  open($INPUT, "<$sourceFile");
  $csv = Text::CSV_XS->new( {binary => 1} );

  #fetch the header line
  $colref = $csv->getline($INPUT);
  @columns = @$colref;

  #trim extraneous whitespace from headers
  $idx = 0;
  while ($idx < scalar(@columns))
  {
    $columns[$idx] =~ s/^\s+//;
    $columns[$idx] =~ s/\s+$//;
    $idx++;
  }

  #fetch and parse the first 20 lines of data (we're going to use it to
  #determine which columns are measures and which are segments)
  $i = 0;
  while ($i < 20)
  {
    $colref = $csv->getline($INPUT);
    @tmpArray = @$colref;
    $j = 0;
    foreach $tmp (@tmpArray)
    {
      $dataReadAhead[$i][$j] = $tmp;
      $j++;
    }

    $i++;
  }

  ds_set_status($db, $dsID, "Analyzing source file structure");

  #figure out what each column is (dimension, attr, seg, measure, etc)
  undef(@pattrCols);
  undef(@gattrCols);
  undef(@tattrCols);
  undef(@psegCols);
  undef(@gsegCols);
  undef(@tsegCols);
  undef(@measureCols);
  undef(%colNameDedupe);
  undef($productColumn);
  undef($geographyColumn);
  undef($paliasColumn);
  undef($taliasColumn);
  undef($galiasColumn);
  undef($timeColumn);
  undef($upcColIndex);

  $index = 0;
  while (defined($columns[$index]))
  {
    $name = $columns[$index];

    #strip brackets off name if they exist (Nielsen AOD)
    if ($name =~ m/^\[(.*)\]$/)
    {
      $name = $1;
    }

    #skip over columns the user has explicitly told us to ignore
    if ($name =~ m/^ignore\:(.*)/i)
    {
      ds_telemetry($db, "Ignoring column $name");
      $index++;
      next;
    }

    #skip over columns with duplicate names
    if ($colNameDedupe{$name} == 1)
    {
      ds_telemetry($db, "Found duplicate column $name, skipping it");
      $index++;
      next;
    }
    $colNameDedupe{$name} = 1;

    #check for UPC/EAN - if we find one, we're going to treat it as a product
    #attribute.
    #NB: Because we're altering our internal representation of the UPC column
    #    header, this check has to happen before we do anything with pattr
    #    columns
    if (($name =~ m/^upc$/i) || ($name =~ m/universal prod code/i) ||
        ($name =~ m/^upc code$/i) || ($name =~ m/prod code/i) ||
        ($name =~ m/universal/i) || ($name =~ m/^ean$/i) ||
        ($name =~ m/^upc\s/i) || ($name =~ m/\supc$/i))
    {

      #some crazy people pull multiple UPCs, so treat the first one we
      #encounter as the "real" UPC and turn the rest into attributes
      if (!defined($upcColIndex))
      {
        ds_telemetry($db, "Found UPC/SKU/EAN in column $name");
        $columns[$index] = "pattr:UPC";
        $name = "pattr:UPC";
        $upcColIndex = $index;
      }
      else
      {
        ds_telemetry($db, "Treating secondary UPC column $name as a product attribute");
        $columns[$index] = "pattr:$name";
        $name = "pattr:$name";
      }
    }

    #IRI and Nielsen users want SIZE and MULTIPACK columns to be treated as
    #segmentations rather than measures
    if ((lc($name) eq "size") || (lc($name) eq "multi pack"))
    {
      ds_telemetry($db, "Found product segmentation in column $name");
      $columns[$index] = "pseg:$name";
      $name = "pseg:$name";
    }

    #check for dimension headers
    if (($name =~ m/^prod$/i) || ($name =~ m/^product$/i) ||
        ($name =~ m/^products$/i) || ($name =~ m/^long product description$/i) ||
        ($name =~ m/^short product description$/i) || ($name =~ m/^item$/i))
    {

      #the first valid product column we find is "real", the rest are segs
      if (!defined($productColumn))
      {
        ds_telemetry($db, "Found product information in $name");
        $productColumn = $index;
      }
      else
      {
        DBG("ds_update: Found an extra product column ($name), importing as a segmentation");
        ds_telemetry($db, "Found product segmentation in column $name");
        $columns[$index] = "pseg:$columns[$index]";
        push(@psegCols, $index);
      }
    }
    elsif (($name =~ m/^geo$/i) || ($name =~ m/^geog$/i) ||
           ($name =~ m/^geography$/i) || ($name =~ m/^geographies$/i) ||
           ($name =~ m/^mkt$/i) || ($name =~ m/^markets$/i) ||
           ($name =~ m/^market display name$/i) || ($name =~ m/^all markets$/i))
    {
      ds_telemetry($db, "Found geography information in $name");
      $geographyColumn = $index;
    }
    elsif (($name =~ m/^time$/i) || ($name =~ m/^time period$/i) ||
           ($name =~ m/^time periods$/i) || ($name =~ m/^per$/i) ||
           ($name =~ m/^All Periods$/i) || ($name =~ m/^periods$/i))
    {
      ds_telemetry($db, "Found time information in $name");
      $timeColumn = $index;
    }

    #check for aliases
    elsif ($name =~ m/^palias/i)
    {
      ds_telemetry($db, "Found product alias in $name");
      $paliasColumn = $index;
    }
    elsif ($name =~ m/^galias/i)
    {
      ds_telemetry($db, "Found geography alias in $name");
      $galiasColumn = $index;
    }
    elsif (($name =~ m/^talias/i) || ($name =~ m/^per_alias/i))
    {
      ds_telemetry($db, "Found time alias in $name");
      $taliasColumn = $index;
    }

    #check for attributes
    elsif ($name =~ m/^pattr\:(.*)/i)
    {
      ds_telemetry($db, "Found product attribute in $name");
      push(@pattrCols, $index);
    }
    elsif ($name =~ m/^gattr\:(.*)/i)
    {
      ds_telemetry($db, "Found geography attribute in $name");
      push(@gattrCols, $index);
    }
    elsif ($name =~ m/^tattr\:(.*)/i)
    {
      ds_telemetry($db, "Found time attribute in $name");
      push(@tattrCols, $index);
    }

    #some Nielsen brand databases use "Attribute - AttrName" for product attrs
    elsif ($name =~ m/^Attribute - (.*)/i)
    {
      ds_telemetry($db, "Found product attribute in $name");
      push(@pattrCols, $index);
    }

    #some IRI databases use "Attr - AttrName" for product attrs
    elsif ($name =~ m/^Attr \- (.*)/i)
    {
      ds_telemetry($db, "Found product attribute in $name");
      push(@pattrCols, $index);
    }

    #check for segmentations
    elsif ($name =~ m/^pseg\:(.*)/i)
    {
      ds_telemetry($db, "Found product segmentation in $name");
      push(@psegCols, $index);
    }
    elsif ($name =~ m/^gseg\:(.*)/i)
    {
      ds_telemetry($db, "Found geography segmentation in $name");
      push(@gsegCols, $index);
    }
    elsif ($name =~ m/^tseg\:(.*)/i)
    {
      ds_telemetry($db, "Found time segmentation in $name");
      push(@tsegCols, $index);
    }

    #check for Nielsen AOD fields we want to discard
    elsif ($columns[$index] =~ m/^\[All Markets\]$/i)
    {
      ds_telemetry($db, "Skipping unneeded column $name");
      DBG("Skipping [All Markets] column");
    }
    elsif ($columns[$index] =~ m/^\[All Products\]$/i)
    {
      ds_telemetry($db, "Skipping unneeded column $name");
      DBG("Skipping [All Products] column");
    }
    elsif ($columns[$index] =~ m/^\[Weeks\]$/i)
    {
      ds_telemetry($db, "Skipping unneeded column $name");
      DBG("Skipping [Weeks] column");
    }

    #fall-through case - it's a measure or pseg if it isn't anything else
    else
    {
      $isSeg = 0;

      #see if any row in our test set contains non-numerical data
      $i = 0;
      while (($i < 20) && ($isSeg == 0))
      {
        if (($dataReadAhead[$i][$index] =~ m/[a-zA-Z]+/) &&
            (lc($dataReadAhead[$i][$index]) ne "na") &&
            (!($dataReadAhead[$i][$index] =~ m/\d+E[\-\d]+/)))
        {
          $isSeg = 1;
          DBG("Column $index ($name) defaulted to product segmentation");
        }
        $i++;
      }

      #see if all rows in our test set are blank
      $i = 0;
      $isBlank = 1;
      while (($i < 20) && ($isBlank == 1))
      {
        if (length($dataReadAhead[$i][$index]) > 0)
        {
          $isBlank = 0;
        }
        $i++;
      }
      if ($isBlank == 1)
      {
        #do a little last-ditch measure checking to handle AOD measures
        #that tend to have a lot of blanks in them
        $hint = DSRmeasures_get_type_hint($name);
        if ($hint ne "measure")
        {
          $isSeg = 1;
          DBG("Empty data column $index ($name) defaulted to product segmentation");
        }
      }

      #a little last-ditch checking to try to identify measures vs segs
      if ($name =~ m/^meas\:(.*)/i)
      {
        $isSeg = 0;
        $columns[$index] = $name = $1;
      }

      #if we determined the column is a segment
      if (($isSeg == 1) && (length($name) > 0))
      {
        ds_telemetry($db, "Found product segmentation in $name");
        push(@psegCols, $index);
        $columns[$index] = "pseg:$name";
      }

      #else we're a measure
      elsif (length($name) > 0)
      {
        ds_telemetry($db, "Found measure data in column $name");
        push(@measureCols, $index);
      }
    }

    $index++;
  }

  #if the analyst specified we should match products by UPC, make sure we
  #actually have usable UPCs or automatically revert back to using names
  if ($pmatch eq "upc")
  {

    #if we didn't find an explicit UPC column, let's go looking for alternatives
    if (!defined($upcColIndex))
    {

      #check the first 20 rows of data for UPCs
      $i = 0;
      $validUPCs = 1;
      while ($i < 20)
      {
        if ($dataReadAhead[$i][$productColumn] =~ m/^.*? (\d+)$/)
        {

          #if the trailing number is long enough to maybe be a UPC
          if (length($1) > 8)
          {
            $i++;
            next;
          }
          else
          {
            $validUPCs = 0;
          }
        }
        else
        {
          $validUPCs = 0;
        }
        $i++;
      }

      if ($validUPCs == 1)
      {
        $pmatch = "upc";
      }
      else
      {
        $pmatch = "name";
      }
    }
  }

  #whether or not we're matching on UPC, let's create a UPC attribute in our
  #new data source if we're new
  if ($createNew == 1)
  {
    $query = "INSERT INTO $dsSchema.product_attributes (name) VALUES ('UPC')";
    $status = $db->do($query);
    ds_db_err($db, $status, $query);
  }

  #remove enclosing brackets from column names if they're there
  $index = 0;
  while (defined($columns[$index]))
  {
    if ($columns[$index] =~ m/^\[(.*)\]$/)
    {
      $columns[$index] = $1;
    }
    $index++;
  }

  ds_set_status($db, $dsID, "Populating attribute tables");

  #XXX: Refactor into general function to handle all dimensional attributes
  #create entries in product_attributes table for any new pattr columns we found
  #
  undef(%pattrHash);
  undef(%pattrNameHash);

  #get current product attributes and populate hash
  $query = "SELECT ID, name FROM $dsSchema.product_attributes";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ds_db_err($db, $status, $query);
  while (($id, $name) = $dbOutput->fetchrow_array)
  {
    #get column index of pattr in update data file
    $index = get_col_index("pattr:$name", @columns);
    if (!(defined($index)))
    {
      $index = get_col_index("Attribute - $name", @columns);
    }
    if (!(defined($index)))
    {
      $index = get_col_index("Attr - $name", @columns);
    }

    if (defined($index))
    {
      ds_telemetry($db, "Matched existing product attribute $name to column $columns[$index]");
      $pattrHash{$index} = $id;
      $pattrNameHash{$columns[$index]} = $id;
    }
  }

  foreach $index (@pattrCols)
  {
    #add attribute name to product_attributes table
    if ($columns[$index] =~ m/^pattr\:(.*)/i)
    {
      $attrName = $1;
    }
    elsif ($columns[$index] =~ m/^Attribute - (.*)/i)
    {
      $attrName = $1;
    }
    elsif ($columns[$index] =~ m/^Attr - (.*)/i)
    {
      $attrName = $1;
    }
    else
    {
      $attrName = $columns[$index];
    }

    if (!defined($pattrNameHash{$columns[$index]}))
    {
      ds_telemetry($db, "Adding new product attribute $attrName");
      $q_attr = $db->quote($attrName);
      $query = "INSERT INTO $dsSchema.product_attributes (name) VALUES ($q_attr)";
      $status = $db->do($query);
      ds_db_err($db, $status, $query);
      $attrID = $db->{q{mysql_insertid}};

      #add attribute ID to hash to avoid repeatedly looking it up later
      $pattrHash{$index} = $attrID;
    }
  }

  #
  #create entries in geography_attributes table for any new gattr columns we found
  #
  undef(%gattrHash);
  undef(%gattrNameHash);

  #get current geography attributes and populate hash
  $query = "SELECT ID, name FROM $dsSchema.geography_attributes";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ds_db_err($db, $status, $query);
  while (($id, $name) = $dbOutput->fetchrow_array)
  {
    #get column index of gattr in update data file
    $index = get_col_index("gattr:$name", @columns);
    if (defined($index))
    {
      ds_telemetry($db, "Matched existing geography attribute $name to column $columns[$index]");
      $gattrHash{$index} = $id;
      $gattrNameHash{$columns[$index]} = $id;
    }
  }

  foreach $index (@gattrCols)
  {
    #add attribute name to geography_attributes table if it's not already there
    $columns[$index] =~ m/^gattr\:(.*)/i;
    $attrName = $1;
    if (!defined($gattrNameHash{$columns[$index]}))
    {
      ds_telemetry($db, "Adding new geography attribute $attrName");
      $q_attr = $db->quote($attrName);
      $query = "INSERT INTO $dsSchema.geography_attributes (name) VALUES ($q_attr)";
      $status = $db->do($query);
      ds_db_err($db, $status, $query);
      $attrID = $db->{q{mysql_insertid}};

      #add attribute ID to hash to avoid repeatedly looking it up later
      $gattrHash{$index} = $attrID;
    }
  }

  #
  #create entries in time_attributes table for any new tattr columns we found
  #
  undef(%tattrHash);
  undef(%tattrNameHash);

  #get current time attributes and populate hash
  $query = "SELECT ID, name FROM $dsSchema.time_attributes";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ds_db_err($db, $status, $query);
  while (($id, $name) = $dbOutput->fetchrow_array)
  {
    #get column index of tattr in update data file
    $index = get_col_index("tattr:$name", @columns);
    if (defined($index))
    {
      ds_telemetry($db, "Matched existing time attribute $name to column $columns[$index]");
      $tattrHash{$index} = $id;
      $tattrNameHash{$columns[$index]} = $id;
    }
  }

  foreach $index (@tattrCols)
  {
    #add attribute name to time_attributes table if it's not already there
    $columns[$index] =~ m/^tattr\:(.*)/i;
    $attrName = $1;
    if (!defined($tattrNameHash{$columns[$index]}))
    {
      ds_telemetry($db, "Adding new time attribute $attrName");
      $q_attr = $db->quote($attrName);
      $query = "INSERT INTO $dsSchema.time_attributes (name) VALUES ($q_attr)";
      $status = $db->do($query);
      ds_db_err($db, $status, $query);
      $attrID = $db->{q{mysql_insertid}};

      #add attribute ID to hash to avoid repeatedly looking it up later
      $tattrHash{$index} = $attrID;
    }
  }

  ds_set_status($db, $dsID, "Populating segmentation tables");

  #
  #create entries in product_segmentation table for any new pseg columns we found
  #
  undef(%psegmentationHash);
  undef(%psegmentationNameHash);

  #get current product segmentations and populate hash
  DBG("ds_update: Getting existing product segmentations from data source");
  $query = "SELECT ID, name FROM $dsSchema.product_segmentation";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ds_db_err($db, $status, $query);
  while (($id, $name) = $dbOutput->fetchrow_array)
  {
    DBG("ds_update: Checking if $name product seg is in data file...");

    #get column index of pseg in update data file
    $index = get_col_index("pseg:$name", @columns);
    if (!defined($index))
    {
      $index = get_col_index("pseg: $name", @columns);
    }
    if (defined($index))
    {
      DBG("ds_update: It is, in column $index");
      ds_telemetry($db, "Matched existing product segmentation $name to column $columns[$index]");
      $psegmentationHash{$index} = $id;
      $psegmentationNameHash{$columns[$index]} = $id;
    }
  }

  DBG("ds_update: Creating new segmentations contained in update");
  foreach $index (@psegCols)
  {
    #add any new segmentation names to product_segmentation table
    if ($columns[$index] =~ m/^pseg\:\s*(.*)/i)
    {
      $segName = $1;
    }
    else
    {
      $segName = $columns[$index];
    }

    DBG("ds_update: Checking product segmentation $segName");
    if (!defined($psegmentationNameHash{$columns[$index]}))
    {
      DBG("ds_update: $segName is a new product segmentation, adding to DSR");
      ds_telemetry($db, "Adding new product segmentation $segName");
      $q_seg = $db->quote($segName);
      $query = "INSERT INTO $dsSchema.product_segmentation (name) VALUES ($q_seg)";
      $status = $db->do($query);
      ds_db_err($db, $status, $query);
      $segID = $db->{q{mysql_insertid}};

      #add attribute ID to hash to avoid repeatedly looking it up later
      $psegmentationHash{$index} = $segID;
    }
  }

  #create entries in geography_segmentation table for new gseg columns
  #
  undef(%gsegmentationHash);
  undef(%gsegmentationNameHash);

  #get current geography segmentations and populate hash
  $query = "SELECT ID, name FROM $dsSchema.geography_segmentation";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ds_db_err($db, $status, $query);
  while (($id, $name) = $dbOutput->fetchrow_array)
  {
    #get column index of gseg in update data file
    $index = get_col_index("gseg:$name", @columns);
    if (!defined($index))
    {
      $index = get_col_index("gseg: $name", @columns);
    }
    if (defined($index))
    {
      ds_telemetry($db, "Matched existing geography segmentation $name to column $columns[$index]}");
      $gsegmentationHash{$index} = $id;
      $gsegmentationNameHash{$columns[$index]} = $id;
    }
  }

  foreach $index (@gsegCols)
  {
    #add any new segmentation names to geography_segmentation table
    $columns[$index] =~ m/^gseg\:\s*(.*)/i;
    $segName = $1;
    if (!defined($gsegmentationNameHash{$columns[$index]}))
    {
      ds_telemetry($db, "Adding new geography segmentation $name");
      $q_seg = $db->quote($segName);
      $query = "INSERT INTO $dsSchema.geography_segmentation (name) VALUES ($q_seg)";
      $status = $db->do($query);
      ds_db_err($db, $status, $query);
      $segID = $db->{q{mysql_insertid}};

      #add attribute ID to hash to avoid repeatedly looking it up later
      $gsegmentationHash{$index} = $segID;
    }
  }

  #create entries in time_segmentation table for new tseg columns
  #
  undef(%tsegmentationHash);
  undef(%tsegmentationNameHash);

  #get current time segmentations and populate hash
  $query = "SELECT ID, name FROM $dsSchema.time_segmentation";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ds_db_err($db, $status, $query);
  while (($id, $name) = $dbOutput->fetchrow_array)
  {
    #get column index of tseg in update data file
    $index = get_col_index("tseg:$name", @columns);
    if (!defined($index))
    {
      $index = get_col_index("tseg: $name", @columns);
    }
    if (defined($index))
    {
      ds_telemetry($db, "Matched existing time segmentation $name to column $columns[$index]}");
      $tsegmentationHash{$index} = $id;
      $tsegmentationNameHash{$columns[$index]} = $id;
    }
  }

  foreach $index (@tsegCols)
  {
    #add any new segmentation names to time_segmentation table
    $columns[$index] =~ m/^tseg\:\s*(.*)/i;
    $segName = $1;
    if (!defined($tsegmentationNameHash{$columns[$index]}))
    {
      ds_telemetry($db, "Adding new time segmentation $name");
      $q_seg = $db->quote($segName);
      $query = "INSERT INTO $dsSchema.time_segmentation (name) VALUES ($q_seg)";
      $status = $db->do($query);
      ds_db_err($db, $status, $query);
      $segID = $db->{q{mysql_insertid}};

      #add attribute ID to hash to avoid repeatedly looking it up later
      $tsegmentationHash{$index} = $segID;
    }
  }

  ds_set_status($db, $dsID, "Matching measures and facts");

  #create cols for new measures in the fact table and entries in measures table
  undef(%measureHash);
  undef(%measureNameHash);

  #get current measures and populate hash
  $query = "SELECT ID, name FROM $dsSchema.measures";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ds_db_err($db, $status, $query);
  while (($id, $name) = $dbOutput->fetchrow_array)
  {
    #this is a little messy since we need to match up the measure name in
    #both the existing data source and the file with the ID in the data source
    #and the measure's column position in the update data. We want our final
    #output to be a measureHash that contains the measure's index position in
    #the update file keying its measure_id column name in the facts table.

    #find the measure's name in the update file column list
    $index = get_col_index($name, @columns);

    if (defined($index))
    {
      #if found, add its name/ID to measureNameHash
      $measureNameHash{lc($name)} = "measure_$id";

      #if found, add its column index location/column name to measureHash
      $measureHash{$index} = "measure_$id";
    }
  }

  foreach $index (@measureCols)
  {
    #add any new measures to the appropriate tables
    if (!defined($measureNameHash{lc($columns[$index])}))
    {

      #get the measure's aggregation rules (if we know them)
      $aggRules = DSRmeasures_get_agg_rules($columns[$index]);
      @aggRules = split(/,/, $aggRules);
      $pAggRule = $aggRules[0];
      $gAggRule = $aggRules[1];
      $tAggRule = $aggRules[2];

      #add measure to measures table
      $q_measure = $db->quote($columns[$index]);
      $q_pAggRule = $db->quote($pAggRule);
      $q_gAggRule = $db->quote($gAggRule);
      $q_tAggRule = $db->quote($tAggRule);
      $query = "INSERT INTO $dsSchema.measures \
          (name, prodAggRule, geoAggRule, timeAggRule) \
          VALUES ($q_measure, $q_pAggRule, $q_gAggRule, $q_tAggRule)";
      $status = $db->do($query);
      ds_db_err($db, $status, $query);
      $measureID = $db->{q{mysql_insertid}};

      #add column for measure to facts table
      $query = "ALTER TABLE $dsSchema.facts \
          ADD COLUMN measure_$measureID DOUBLE";
      $status = $db->do($query);
      ds_db_err($db, $status, $query);

      #add entry to measureHash so we know which measure is in which column
      $measureHash{$index} = "measure_$measureID";
    }
  }

  ds_set_status($db, $dsID, "Caching segmentation information");

  #clean out the dimension ID cache hashes
  undef(%geoHash);
  undef(%prodHash);
  undef(%timeHash);

  #clean out the segment ID cache hashes
  undef(%psegHash);
  undef(%gsegHash);
  undef(%tsegHash);

  #load up the psegHash with all existing segments
  $query = "SELECT ID, segmentationID, name FROM $dsSchema.product_segment";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ds_db_err($db, $status, $query);
  while (($id, $segID, $name) = $dbOutput->fetchrow_array)
  {
    $val = "$segID-$name";
    $psegHash{$val} = $id;
  }

  #load up the gsegHash with all existing segments
  $query = "SELECT ID, segmentationID, name FROM $dsSchema.geography_segment";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ds_db_err($db, $status, $query);
  while (($id, $segID, $name) = $dbOutput->fetchrow_array)
  {
    $val = "$segID-$name";
    $gsegHash{$val} = $id;
  }

  #load up the tsegHash with all existing segments
  $query = "SELECT ID, segmentationID, name FROM $dsSchema.time_segment";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ds_db_err($db, $status, $query);
  while (($id, $segID, $name) = $dbOutput->fetchrow_array)
  {
    $val = "$segID-$name";
    $tsegHash{$val} = $id;
  }

  #build up a hash of product names, keyed by UPC codes (used to identify
  #products with duplicate UPCs during update process)
  undef(%upcHash);

  #start by getting the ID for the UPC product attribute
  $query = "SELECT ID FROM $dsSchema.product_attributes WHERE name='UPC'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ds_db_err($db, $status, $query);
  ($upcAttrID) = $dbOutput->fetchrow_array;

  #grab all of the UPCs from the data source, and build the hash we're going
  #to use to check for duplicates
  %productNameHash = dsr_get_item_name_hash($db, $dsSchema, "p");
  $query = "SELECT itemID, value FROM $dsSchema.product_attribute_values \
      WHERE attributeID=$upcAttrID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ds_db_err($db, $status, $query);
  while (($itemID, $upcVal) = $dbOutput->fetchrow_array)
  {
    $itemName = $productNameHash{$itemID};
    $upcHash{$upcVal} = $itemName;
  }

  %upcValueIDhash = DSRattr_get_values_hash($db, $dsSchema, "p", $upcAttrID);
  %upcValueIDhash = reverse(%upcValueIDhash);

  #reopen the data file and burn the first row (headers we've already parsed)
  close(INPUT);
  open($INPUT, "<$sourceFile") or die("ds_update: Couldn't open raw source file $sourceFile - $!");
  $colref = $csv->getline($INPUT);

  #add the data from the source file to the data source
  $count = 0;
  while ($colref = $csv->getline($INPUT))
  {

    #update our progress in the UI every 500 records
    $count++;
    if (($count % 500) == 0)
    {
      $pct = ($count / $sourceFileRecords) * 100;
      $pct = int($pct);
      ds_set_status($db, $dsID, "Updating data, record #$count ($pct\%)");
    }

    DBG("ds_update: Processing data line $count: $line");
    @columns = @$colref;

    $geo = $columns[$geographyColumn];
    $time = $columns[$timeColumn];
    $product = $columns[$productColumn];

    DBG("ds_update: Raw dimension data product=$product geo=$geo time=$time");

    #strip any extra whitespace from beginning and ends of strings
    $geo =~ s/^\s+//;
    $geo =~ s/\s+$//;
    $time =~ s/^\s+//;
    $time =~ s/\s+$//;
    $product =~ s/^\s+//;
    $product =~ s/\s+$//;

    #if the user wants us to strip extra whitespace from inside item names
    if ($options =~ m/compressWS/)
    {
      $product =~ s/\s+/ /g;
      $geo =~ s/\s+/ /g;
      $time =~ s/\s+/ /g;
    }

    #if the user wants us to append the UPC/EAN/Fineline/etc to the product name
    if (($options =~ m/appendUPC/) && ($upcColIndex > 0))
    {
      $product .= " $columns[$upcColIndex]";
    }

    #check if this product has the same UPC as a different product already in
    #the data source (case insensitive name check) - only do this if we're
    #matching up products based on name
    if (($pmatch eq "name") && (defined($upcColIndex)))
    {

      #get the UPC for the product we're currently loading into the DS
      $curUPC = $columns[$upcColIndex];

      #if the UPC is already associated with a product in the DS
      if (length($upcHash{$curUPC}) > 1)
      {

        #if the names of the two products with the same UPC are different,
        #append "-1" to our new UPC
        if (lc($upcHash{$curUPC}) ne lc($product))
        {
          #$columns[$upcColIndex] .= "-1";
        }
      }
    }

    #if we're matching by UPC, overwrite any existing product name in the DS
    #that's being updated by this row in the data source unless told not to
    if ($pmatch eq "upc")
    {
      $curUPC = $columns[$upcColIndex];

      #handle AOD data that doesn't include an explicit UPC
      if (!defined($upcColIndex))
      {
        if ($product =~ m/^.*? (\d+)$/)
        {

          #if the trailing number is long enough to maybe be a UPC
          if (length($1) > 8)
          {
            $curUPC = $1;
          }
        }
      }

      DBG("Matching on UPC $curUPC");

      #if the product field is empty, see if we have already have it in the DS
      if (length($product) < 1)
      {
        $product = $upcHash{$curUPC};
      }

      #if it's still empty, let's skip the data row as the lesser evil
      if (length($product) < 1)
      {
        next;
      }

      if (length($upcHash{$curUPC}) > 1)
      {
        if (($dontOverwriteNames < 1) && (lc($upcHash{$curUPC}) ne lc($product)))
        {

          #update the product name in the data source
          DBG("ds_update: Updating name of $upcHash{$curUPC} to $product");
          $q_product = $db->quote($product);
          $productID = $upcValueIDhash{$curUPC};
          $query = "UPDATE $dsSchema.products SET name=$q_product WHERE ID=$productID";
          $status = $db->do($query);
          ds_db_err($db, $status, $query);

          #update the UPC hash
          $upcHash{$curUPC} = $product;
        }
      }
    }

    $q_geo = $db->quote($geo);
    $q_time = $db->quote($time);
    $q_prod = $db->quote($product);

    #get the geography ID if already in DB, add otherwise (cache hash used to
    #minimize DB access for performance reasons)
    $geoID = $geoHash{$geo};
    if (!defined($geoID))
    {
      $query = "SELECT ID FROM $dsSchema.geographies WHERE name = $q_geo";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      ds_db_err($db, $status, $query);
      if ($status < 1)
      {

        DBG("Found new geography $geo");

        #if there's an alias for this geography, add it
        if (defined($galiasColumn))
        {

          if (length($columns[$galiasColumn]) > 0)
          {
            $q_alias = $db->quote($columns[$galiasColumn]);
          }
          else
          {
            $q_alias = "NULL";
          };

          $query = "INSERT INTO $dsSchema.geographies (name, alias) \
              VALUES ($q_geo, $q_alias)";
        }
        else
        {
          $query = "INSERT INTO $dsSchema.geographies (name) VALUES ($q_geo)";
        }

        $status = $db->do($query);
        ds_db_err($db, $status, $query);
        $geoID = $db->{q{mysql_insertid}};
      }
      else
      {
        ($geoID) = $dbOutput->fetchrow_array;
      }

      $geoHash{$geo} = $geoID;
    }
    DBG("Geography is $geo, ID $geoID");

    $timeName = $time;
    $timeID = $timeHash{$timeName};
    if (!defined($timeID))
    {

      #parse time info from time string
      ($time, $type, $duration, $endDate) = ds_parse_date_string($db, $time);
      DBG("Parsed time period as duration=$duration, type=$type, endDate=$endDate");

      #if we're a custom time period
      if ($type == 0)
      {
        $query = "SELECT ID FROM $dsSchema.timeperiods WHERE name = $q_time";
      }

      #else we're a standard time period
      else
      {
        $query = "SELECT ID FROM $dsSchema.timeperiods \
            WHERE duration=$duration AND type=$type AND endDate='$endDate'";
      }

      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      ds_db_err($db, $status, $query);
      if ($status < 1)
      {

        #if there's an alias for this time period, add it
        if (defined($taliasColumn))
        {

          if (length($columns[$taliasColumn]) > 0)
          {
            $q_alias = $db->quote($columns[$taliasColumn]);
          }
          else
          {
            $q_alias = "NULL";
          };

          $query = "INSERT IGNORE INTO $dsSchema.timeperiods \
              (name, alias, duration, type, endDate) \
              VALUES ($q_time, $q_alias, $duration, $type, '$endDate')";
        }
        else
        {
          $query = "INSERT IGNORE INTO $dsSchema.timeperiods \
              (name, duration, type, endDate) \
              VALUES ($q_time, $duration, $type, '$endDate')";
        }

        $status = $db->do($query);
        ds_db_err($db, $status, $query);
        $timeID = $db->{q{mysql_insertid}};
      }
      else
      {
        ($timeID) = $dbOutput->fetchrow_array;
      }

      $timeHash{$timeName} = $timeID;
    }

    #get the product ID if already in DB, add otherwise
    if ($pmatch eq "name")
    {
      $prodID = $prodHash{$product};
    }
    else
    {
      $prodID = $prodHash{$curUPC};
    }

    #if we haven't seen this product yet in the update
    if (!defined($prodID))
    {

      #look for a matching product already in the data source
      if ($pmatch eq "name")
      {
        $query = "SELECT ID FROM $dsSchema.products WHERE name = $q_prod";
      }
      else
      {
        $query = "SELECT itemID FROM $dsSchema.product_attribute_values \
            WHERE attributeID=$upcAttrID AND value='$curUPC'";
      }
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;
      ds_db_err($db, $status, $query);
      if ($status < 1)
      {
        DBG("No matching product found in DS, adding new product $q_prod");

        $query = "INSERT INTO $dsSchema.products (name) VALUES ($q_prod)";
        $status = $db->do($query);
        ds_db_err($db, $status, $query);
        $prodID = $db->{q{mysql_insertid}};

        if ($pmatch eq "upc")
        {
          $query = "INSERT INTO $dsSchema.product_attribute_values \
              (attributeID, itemID, value) \
              VALUES ($upcAttrID, $prodID, '$curUPC')";
          $status = $db->do($query);
          ds_db_err($db, $status, $query);

          #$upcHash{$curUPC} = $product;
        }

      }
      else
      {
        ($prodID) = $dbOutput->fetchrow_array;
        DBG("Product matched to existing product $prodID");
      }

      if ($pmatch eq "name")
      {
        $prodHash{$product} = $prodID;
      }
      else
      {
        $prodHash{$curUPC} = $prodID;
      }
    }

    #if there's an alias specified for this product, add it
    if (defined($paliasColumn))
    {

      if (length($columns[$paliasColumn]) > 0)
      {
        $q_alias = $db->quote($columns[$paliasColumn]);

        $query = "UPDATE $dsSchema.products SET alias = $q_alias WHERE ID=$prodID";
        $status = $db->do($query);
        ds_db_err($db, $status, $query);
      }
    }

    #build up the measures portion of the INSERT statement for the fact table
    undef($subq1);
    undef($subq2);
    $updateq = "updateID=$dsUpdateID,";
    foreach $index (@measureCols)
    {

      #convert NA values to NULL
      if (lc($columns[$index]) eq "na")
      {
        $columns[$index] = "NULL";
      }
      if (lc($columns[$index]) eq "n/a")
      {
        $columns[$index] = "NULL";
      }

      #convert "NOT STATED" values to NULL
      if (lc($columns[$index]) eq "not stated")
      {
        $columns[$index] = "NULL";
      }

      #if the column is empty, consider it a NULL
      if ((length($columns[$index])) < 1)
      {
        $columns[$index] = "NULL";
      }

      $subq1 .= "$measureHash{$index},";
      $subq2 .= "$columns[$index],";

      $updateq .= "$measureHash{$index} = VALUES($measureHash{$index}),";
    }

    #chop trailing commas off of subqueries
    chop($subq1);
    chop($subq2);
    chop($updateq);

    #store the current record in the array we use to collect 50 INSERTs at a time
    #for performance reasons
    push(@factsValuesArray, "($geoID,$timeID,$prodID,$dsUpdateID,$subq2), ");

    #add values for any associated product attributes to the database
    foreach $index (@pattrCols)
    {
      $attributeID = $pattrHash{$index};
      $hashKey = "$attributeID-$prodID";
      if ($prodAttrSeenHash{$hashKey} < 1)
      {
        $q_value = $db->quote($columns[$index]);
        push(@prodAttrValuesArray, "($attributeID, $prodID, $q_value), ");
        $prodAttrSeenHash{$hashKey} = 1;
      }
    }

    #add values for any associated geography attributes to the database
    foreach $index (@gattrCols)
    {
      $attributeID = $gattrHash{$index};
      $q_value = $db->quote($columns[$index]);
      push(@geoAttrValuesArray, "($attributeID, $geoID, $q_value), ");
    }

    #add values for any associated time period attributes to the database
    foreach $index (@tattrCols)
    {
      $attributeID = $tattrHash{$index};
      $q_value = $db->quote($columns[$index]);
      push(@timeAttrValuesArray, "($attributeID, $timeID, $q_value), ");
    }

    #add values for any associated product segments to the database
    foreach $index (@psegCols)
    {

      #trim leading/trailing spaces from segment names
      $columns[$index] =~ s/^\s+//;
      $columns[$index] =~ s/\s+$//;

      #if the product isn't segmented in this segmentation, skip to the next
      #segmentation
      if (length($columns[$index]) < 1)
      {
        next;
      }

      #get the segmentation ID from the cache hash
      $segmentationID = $psegmentationHash{$index};

      #see if this segment of the segmentation has been added yet
      $val = "$segmentationID-$columns[$index]";  #1-Cherry
      $segmentID = $psegHash{$val};

      #if it's the first time we've seen this segment, add it
      if (!(defined($segmentID)))
      {
        $q_seg = $db->quote($columns[$index]);
        $query = "INSERT INTO $dsSchema.product_segment \
            (segmentationID, name) \
            VALUES ($segmentationID, $q_seg)";
        $status = $db->do($query);
        ds_db_err($db, $status, $query);
        $segmentID = $db->{q{mysql_insertid}};

        #add ID to hash to avoid looking it up later
        $psegHash{$val} = $segmentID;
      }

      #add the item's segmentation and segment info to the item table
      push(@prodSegValuesArray, "($segmentationID, $segmentID, $prodID), ");
    }

    #add values for any associated geography segments to the database
    foreach $index (@gsegCols)
    {

      #trim leading/trailing spaces from segment names
      $columns[$index] =~ s/^\s+//;
      $columns[$index] =~ s/\s+$//;

      #if the geography isn't segmented in this segmentation, skip to the next
      #segmentation
      if (length($columns[$index]) < 1)
      {
        next;
      }

      #get the segmentation ID from the cache hash
      $segmentationID = $gsegmentationHash{$index};

      #see if this segment of the segmentation has been added yet
      $val = "$segmentationID-$columns[$index]";  #1-Cherry
      $segmentID = $gsegHash{$val};

      #if it's the first time we've seen this segment, add it
      if (!defined($segmentID))
      {
        $q_seg = $db->quote($columns[$index]);
        $query = "INSERT INTO $dsSchema.geography_segment \
            (segmentationID, name) \
            VALUES ($segmentationID, $q_seg)";
        $status = $db->do($query);
        ds_db_err($db, $status, $query);
        $segmentID = $db->{q{mysql_insertid}};

        #add ID to hash to avoid looking it up later
        $gsegHash{$val} = $segmentID;
      }

      #add the item's segmentation and segment info to the item table
      push(@geoSegValuesArray, "($segmentationID, $segmentID, $geoID), ");
    }

    #add values for any associated time segments to the database
    foreach $index (@tsegCols)
    {

      #trim leading/trailing spaces from segment names
      $columns[$index] =~ s/^\s+//;
      $columns[$index] =~ s/\s+$//;

      #if the time isn't segmented in this segmentation, skip to the next
      #segmentation
      if (length($columns[$index]) < 1)
      {
        next;
      }

      #get the segmentation ID from the cache hash
      $segmentationID = $tsegmentationHash{$index};

      #see if this segment of the segmentation has been added yet
      $val = "$segmentationID-$columns[$index]";  #1-Cherry
      $segmentID = $tsegHash{$val};

      #if it's the first time we've seen this segment, add it
      if (!defined($segmentID))
      {
        $q_seg = $db->quote($columns[$index]);
        $query = "INSERT INTO $dsSchema.time_segment \
            (segmentationID, name) \
            VALUES ($segmentationID, $q_seg)";
        $status = $db->do($query);
        ds_db_err($db, $status, $query);
        $segmentID = $db->{q{mysql_insertid}};

        #add ID to hash to avoid looking it up later
        $tsegHash{$val} = $segmentID;
      }

      #add the item's segmentation and segment info to the item table
      push(@timeSegValuesArray, "($segmentationID, $segmentID, $timeID), ");
    }

    #if we have 50 records ready for bulk insertion, build & run the SQL
    if (scalar(@factsValuesArray) > 99)
    {

      #insert/update the measure data in the facts table
      $query = "INSERT INTO $dsSchema.facts (geographyID, timeID, productID, updateID, $subq1) VALUES ";
      foreach $valSet (@factsValuesArray)
      {
        $query .= "$valSet";
      }
      chop($query);  chop($query);
      $query .= " ON DUPLICATE KEY UPDATE $updateq";

      $status = $db->do($query);
      ds_db_err($db, $status, $query);

      undef(@factsValuesArray);

      #insert/update product attribute data if we've collected any
      if (scalar(@prodAttrValuesArray) > 0)
      {
        $query = "INSERT INTO $dsSchema.product_attribute_values (attributeID, itemID, value) VALUES ";
        foreach $valSet (@prodAttrValuesArray)
        {
          $query .= "$valSet";
        }
        chop($query);  chop($query);
        $query .= " ON DUPLICATE KEY UPDATE value=VALUES(value)";

        $status = $db->do($query);
        ds_db_err($db, $status, $query);

        undef(@prodAttrValuesArray);
      }

      #insert/update geography attribute data if we've collected any
      if (scalar(@geoAttrValuesArray) > 0)
      {
        $query = "INSERT INTO $dsSchema.geography_attribute_values (attributeID, itemID, value) VALUES ";
        foreach $valSet (@geoAttrValuesArray)
        {
          $query .= "$valSet";
        }
        chop($query);  chop($query);
        $query .= " ON DUPLICATE KEY UPDATE value=VALUES(value)";

        $status = $db->do($query);
        ds_db_err($db, $status, $query);

        undef(@geoAttrValuesArray);
      }

      #insert/update time attribute data if we've collected any
      if (scalar(@timeAttrValuesArray) > 0)
      {
        $query = "INSERT INTO $dsSchema.time_attribute_values (attributeID, itemID, value) VALUES ";
        foreach $valSet (@timeAttrValuesArray)
        {
          $query .= "$valSet";
        }
        chop($query);  chop($query);
        $query .= " ON DUPLICATE KEY UPDATE value=VALUES(value)";

        $status = $db->do($query);
        ds_db_err($db, $status, $query);

        undef(@timeAttrValuesArray);
      }

      #insert/update product segmentation data if there are any entries
      if (scalar(@prodSegValuesArray) > 0)
      {
        $query = "INSERT INTO $dsSchema.product_segment_item (segmentationID, segmentID, itemID) VALUES ";
        foreach $valSet (@prodSegValuesArray)
        {
          $query .= "$valSet";
        }
        chop($query);  chop($query);
        $query .= " ON DUPLICATE KEY UPDATE segmentationID=VALUES(segmentationID), segmentID=VALUES(segmentID)";

        $status = $db->do($query);
        ds_db_err($db, $status, $query);

        undef(@prodSegValuesArray);
      }

      #insert/update geography segmentation data if there are any entries
      if (scalar(@geoSegValuesArray) > 0)
      {
        $query = "INSERT INTO $dsSchema.geography_segment_item (segmentationID, segmentID, itemID) VALUES ";
        foreach $valSet (@geoSegValuesArray)
        {
          $query .= "$valSet";
        }
        chop($query);  chop($query);
        $query .= " ON DUPLICATE KEY UPDATE segmentationID=VALUES(segmentationID), segmentID=VALUES(segmentID)";
        $status = $db->do($query);
        ds_db_err($db, $status, $query);

        undef(@geoSegValuesArray);
      }

      #insert/update time segmentation data if there are any entries
      if (scalar(@timeSegValuesArray) > 0)
      {
        $query = "INSERT INTO $dsSchema.time_segment_item (segmentationID, segmentID, itemID) VALUES ";
        foreach $valSet (@timeSegValuesArray)
        {
          $query .= "$valSet";
        }
        chop($query);  chop($query);
        $query .= " ON DUPLICATE KEY UPDATE segmentationID=VALUES(segmentationID), segmentID=VALUES(segmentID)";

        $status = $db->do($query);
        ds_db_err($db, $status, $query);

        undef(@timeSegValuesArray);
      }
    }
  }

  #bulk insert any remaining records in the *ValueArrays to finish out the file
  if (scalar(@factsValuesArray) > 0)
  {
    $query = "INSERT INTO $dsSchema.facts (geographyID, timeID, productID, updateID, $subq1) VALUES ";
    foreach $valSet (@factsValuesArray)
    {
      $query .= "$valSet";
    }
    chop($query);  chop($query);

    $query .= " ON DUPLICATE KEY UPDATE $updateq";

    $status = $db->do($query);
    ds_db_err($db, $status, $query);

    undef(@factsValuesArray);

    #insert/update product attribute data if there are any remaining entries
    if (scalar(@prodAttrValuesArray) > 0)
    {
      $query = "INSERT INTO $dsSchema.product_attribute_values (attributeID, itemID, value) VALUES ";
      foreach $valSet (@prodAttrValuesArray)
      {
        $query .= "$valSet";
      }
      chop($query);  chop($query);
      $query .= " ON DUPLICATE KEY UPDATE value=VALUES(value)";

      $status = $db->do($query);
      ds_db_err($db, $status, $query);

      undef(@prodAttrValuesArray);
    }

    #insert/update geography attribute data if we've collected any
    if (scalar(@geoAttrValuesArray) > 0)
    {
      $query = "INSERT INTO $dsSchema.geography_attribute_values (attributeID, itemID, value) VALUES ";
      foreach $valSet (@geoAttrValuesArray)
      {
        $query .= "$valSet";
      }
      chop($query);  chop($query);
      $query .= " ON DUPLICATE KEY UPDATE value=VALUES(value)";

      $status = $db->do($query);
      ds_db_err($db, $status, $query);

      undef(@geoAttrValuesArray);
    }

    #insert/update time attribute data if we've collected any
    if (scalar(@timeAttrValuesArray) > 0)
    {
      $query = "INSERT INTO $dsSchema.time_attribute_values (attributeID, itemID, value) VALUES ";
      foreach $valSet (@timeAttrValuesArray)
      {
        $query .= "$valSet";
      }
      chop($query);  chop($query);
      $query .= " ON DUPLICATE KEY UPDATE value=VALUES(value)";

      $status = $db->do($query);
      ds_db_err($db, $status, $query);

      undef(@timeAttrValuesArray);
    }

    #insert/update product segmentation data if there are any remaining entries
    if (scalar(@prodSegValuesArray) > 0)
    {
      $query = "INSERT INTO $dsSchema.product_segment_item (segmentationID, segmentID, itemID) VALUES ";
      foreach $valSet (@prodSegValuesArray)
      {
        $query .= "$valSet";
      }
      chop($query);  chop($query);
      $query .= " ON DUPLICATE KEY UPDATE segmentationID=VALUES(segmentationID), segmentID=VALUES(segmentID)";

      $status = $db->do($query);
      ds_db_err($db, $status, $query);

      undef(@prodSegValuesArray);
    }

    #insert/update geography segmentation data if there are any remaining entries
    if (scalar(@geoSegValuesArray) > 0)
    {
      $query = "INSERT INTO $dsSchema.geography_segment_item (segmentationID, segmentID, itemID) VALUES ";
      foreach $valSet (@geoSegValuesArray)
      {
        $query .= "$valSet";
      }
      chop($query);  chop($query);
      $query .= " ON DUPLICATE KEY UPDATE segmentationID=VALUES(segmentationID), segmentID=VALUES(segmentID)";

      $status = $db->do($query);
      ds_db_err($db, $status, $query);

      undef(@geoSegValuesArray);
    }

    #insert/update time segmentation data if there are any remaining entries
    if (scalar(@timeSegValuesArray) > 0)
    {
      $query = "INSERT INTO $dsSchema.time_segment_item (segmentationID, segmentID, itemID) VALUES ";
      foreach $valSet (@timeSegValuesArray)
      {
        $query .= "$valSet";
      }
      chop($query);  chop($query);
      $query .= " ON DUPLICATE KEY UPDATE segmentationID=VALUES(segmentationID), segmentID=VALUES(segmentID)";

      $status = $db->do($query);
      ds_db_err($db, $status, $query);

      undef(@timeSegValuesArray);
    }
  }

  ds_telemetry($db, "Updating merged items");
  ds_set_status($db, $dsID, "Updating merged items");
  DSRmergedprod_update($db, $dsSchema, $dsUpdateID);

  #do any configured time period pruning before we start calculations
  ds_telemetry($db, "Pruning old time periods");
  ds_set_status($db, $dsID, "Pruning old time periods");
#  ds_prune_time_periods($db, $dsID);

  #set our last updated and last modified time to be now, so all of the
  #calculated measures will correctly do their thing
  $query = "UPDATE dataSources SET lastUpdate=NOW(), lastModified=NOW()  WHERE ID=$dsID";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);

  ds_telemetry($db, "Recalculating calculated measures");
  ds_set_status($db, $dsID, "Updating calculated measures");
  KAPutil_job_update_state($db, "RECALC-MEASURES");
  DSRmeasures_recalculate_all_measures($db, $dsSchema, $dsUpdateID);

  #
  #update any segmentation rules and linked segmentations
  #

  ds_set_status($db, $dsID, "Updating linked segmentations");
  KAPutil_job_update_state($db, "SEGMENTATIONS");

  #apply rules/update linked product segmentations
  ds_telemetry($db, "Updating linked product segmentations");
  $query = "SELECT ID FROM $dsSchema.product_segmentation";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ds_db_err($db, $status, $query);
  while (($id) = $dbOutput->fetchrow_array)
  {
    DSRsegmentation_apply_seg_rules($db, $dsSchema, "p", $id);
    DSRsegmentation_update_linked_segs($db, $dsSchema, "p", $id);
  }

  #update linked geography segmentations
  ds_telemetry($db, "Updating linked geography segmentations");
  $query = "SELECT ID FROM $dsSchema.geography_segmentation";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ds_db_err($db, $status, $query);
  while (($id) = $dbOutput->fetchrow_array)
  {
    DSRsegmentation_apply_seg_rules($db, $dsSchema, "g", $id);
    DSRsegmentation_update_linked_segs($db, $dsSchema, "g", $id);
  }

  #update linked time segmentations
  ds_telemetry($db, "Updating linked time segmentations");
  $query = "SELECT ID FROM $dsSchema.time_segmentation";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ds_db_err($db, $status, $query);
  while (($id) = $dbOutput->fetchrow_array)
  {
    DSRsegmentation_apply_seg_rules($db, $dsSchema, "t", $id);
    DSRsegmentation_update_linked_segs($db, $dsSchema, "t", $id);
  }

  #
  #re-expand all lists and aggregates to account for new data items
  #

  ds_set_status($db, $dsID, "Updating lists and aggregates");
  KAPutil_job_update_state($db, "UPDATE-LISTS-AGGS");

  #expand product lists
  ds_telemetry($db, "Expanding product lists");
  $query = "SELECT ID FROM $dsSchema.product_list";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ds_db_err($db, $status, $query);
  while (($id) = $dbOutput->fetchrow_array)
  {
    datasel_expand_script($db, $dsSchema, $id, "p", "l");
  }
  $status = $db->do("UPDATE $dsSchema.product_list SET lastUpdated=NOW()");
  ds_db_err($db, $status, $query);

  #expand geography lists
  ds_telemetry($db, "Expanding geography lists");
  $query = "SELECT ID FROM $dsSchema.geography_list";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ds_db_err($db, $status, $query);
  while (($id) = $dbOutput->fetchrow_array)
  {
    datasel_expand_script($db, $dsSchema, $id, "g", "l");
  }
  $status = $db->do("UPDATE $dsSchema.geography_list SET lastUpdated=NOW()");
  ds_db_err($db, $status, $query);

  #expand time lists
  ds_telemetry($db, "Expanding time lists");
  $query = "SELECT ID FROM $dsSchema.time_list";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ds_db_err($db, $status, $query);
  while (($id) = $dbOutput->fetchrow_array)
  {
    datasel_expand_script($db, $dsSchema, $id, "t", "l");
  }
  $status = $db->do("UPDATE $dsSchema.time_list SET lastUpdated=NOW()");
  ds_db_err($db, $status, $query);

  #expand measure lists
  ds_telemetry($db, "Expanding measure lists");
  $query = "SELECT ID FROM $dsSchema.measure_list";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ds_db_err($db, $status, $query);
  while (($id) = $dbOutput->fetchrow_array)
  {
    datasel_expand_script($db, $dsSchema, $id, "m", "l");
  }
  $status = $db->do("UPDATE $dsSchema.measure_list SET lastUpdated=NOW()");
  ds_db_err($db, $status, $query);

  #expand product aggregates
  ds_telemetry($db, "Expanding product aggregates");
  $query = "SELECT ID FROM $dsSchema.product_aggregate";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ds_db_err($db, $status, $query);
  while (($id) = $dbOutput->fetchrow_array)
  {
    datasel_expand_script($db, $dsSchema, $id, "p", "a");
    datasel_expand_script($db, $dsSchema, $id, "p", "as");
  }
  $status = $db->do("UPDATE $dsSchema.product_aggregate SET lastUpdated=NOW()");
  ds_db_err($db, $status, $query);

  #expand geography aggregates
  ds_telemetry($db, "Expanding geography aggregates");
  $query = "SELECT ID FROM $dsSchema.geography_aggregate";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ds_db_err($db, $status, $query);
  while (($id) = $dbOutput->fetchrow_array)
  {
    datasel_expand_script($db, $dsSchema, $id, "g", "a");
    datasel_expand_script($db, $dsSchema, $id, "g", "as");
  }
  $status = $db->do("UPDATE $dsSchema.geography_aggregate SET lastUpdated=NOW()");
  ds_db_err($db, $status, $query);

  #expand time aggregates
  ds_telemetry($db, "Expanding time aggregates");
  $query = "SELECT ID FROM $dsSchema.time_aggregate";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ds_db_err($db, $status, $query);
  while (($id) = $dbOutput->fetchrow_array)
  {
    datasel_expand_script($db, $dsSchema, $id, "t", "a");
    datasel_expand_script($db, $dsSchema, $id, "t", "as");
  }
  $status = $db->do("UPDATE $dsSchema.time_aggregate SET lastUpdated=NOW()");
  ds_db_err($db, $status, $query);

  #if configured, refresh the AI-generated improvded product name aliases
  ds_telemetry($db, "Refreshing improved product name aliases");
  AIProductNaming_improve_product_names($db, $dsID);

  #create any global time aliases that the DS owner has configured
  ds_telemetry($db, "Rebuilding global time aliases");
  DSRutil_build_time_aliases($db, $dsID);

  ds_set_status($db, $dsID, "");

  #if we were called by Prep, clear out the update job info
  if ($key =~ m/prep\.(\d+)/)
  {
    $jobID = $1;
    $prepDB = PrepUtils_connect_to_database();
    $query = "UPDATE prep.jobs SET PID=NULL, opInfo='DONE', state='LOADED' WHERE ID=$jobID";
    $status = $prepDB->do($query);
    ds_db_err($prepDB, $status, $query);

    $query = "SELECT userID, flowID FROM prep.jobs WHERE ID=$jobID";
    $dbOutput = $prepDB->prepare($query);
    $status = $dbOutput->execute;
    ds_db_err($prepDB, $status, $query);
    ($prepUserID, $flowID) = $dbOutput->fetchrow_array;

    $query = "SELECT name FROM prep.flows WHERE ID=$flowID";
    $dbOutput = $prepDB->prepare($query);
    $status = $dbOutput->execute;
    ds_db_err($prepDB, $status, $query);
    ($prepFlowName) = $dbOutput->fetchrow_array;

    ds_telemetry($db, "Done loading data from $prepFlowName");

    #save the fact that the job has been successfully exported in the job
    $query = "UPDATE prep.jobs SET exportedKoala = 1 WHERE ID=$jobID";
    $prepDB->do($query);

    $query = "UPDATE prep.telemetry \
        SET endTime = NOW(), \
            telemetry = CONCAT(telemetry, NOW(), ': Done updating Koala data source\n') \
            WHERE jobID=$jobID";
    $status = $prepDB->do($query);
    ds_db_err($db, $status, $query);

    #add feed item to let user know update finished
    Social_feed_add_item($db, $prepUserID, $dsID, 0, $flowID, "success", "ds_update");
  }

  #else we were run manually by the user
  else
  {
    #add feed item to let user know update finished
    Social_feed_add_item($db, $userID, $dsID, 0, 0, "success", "ds_update");
  }

  ds_telemetry($db, "Cleaning up source and intermediary files");

  #remove the tabular CSV we used as the source file upon completion
  unlink($sourceFile);

  #remove any source files we used
  `rm /opt/apache/app/tmp/$userID.$key.*.csv`;
  `rm /opt/apache/app/tmp/$userID.$key.*.xls`;
  `rm /opt/apache/app/tmp/$userID.$key.*.xlsx`;

  #maybe we've been gone a long time, so make sure we're still connected to Analytics
  if (!($db->ping))
  {
    $db = KAPutil_connect_to_database();
  }

  DSRutil_clear_status($db);

  $query = "UPDATE audit.telemetry_data \
      SET endTime=NOW(), telemetry = CONCAT(telemetry, NOW(), ': Finished updating data source\n') \
      WHERE ID=$dsUpdateID";
  $status = $db->do($query);
  ds_db_err($db, $status, $query);
}


#-------------------------------------------------------------------------------


1;
