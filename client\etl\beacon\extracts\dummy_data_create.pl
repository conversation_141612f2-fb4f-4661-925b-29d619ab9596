#!/usr/bin/perl


use Text::CSV_XS;

#Test update will use data for Jan & Feb 18 from:
# Beacon_Monthly_latest_weeks_1_to_4_184179.tar.gz
# Beacon_Monthly_latest_weeks_5_to_8_184182.tar.gz

#Jan 18 is in Beacon_Dummy_5_2.tar.gz (duplicate data from
# Beacon_Monthly_latest_weeks_5_to_8_184182.tar.gz)

#Dec 17 is in Beacon_Dummy_5_1.tar.gz (duplicate data from
# Beacon_Monthly_latest_weeks_1_to_4_162197.tar.gz)

#quadrupling the contents of the big History archives will give us 100 weeks
#of data, which when added to Dec 17 and Jan 18 gives us our 108 weeks



  #array of "dummy" dates we're going to use for duplication purposes
  @dates1_1 = ("1 W/E 01/07/2017",
        "1 W/E 01/14/2017",
        "1 W/E 01/21/2017",
        "1 W/E 01/28/2017",
        "1 W/E 02/04/2017",
        "1 W/E 02/11/2017",
        "1 W/E 02/18/2017",
        "1 W/E 02/25/2017",
        "1 W/E 03/04/2017");

  @dates1_2 = (
        "1 W/E 03/11/2017",
        "1 W/E 03/18/2017",
        "1 W/E 03/25/2017",
        "1 W/E 04/01/2017",
        "1 W/E 04/08/2017",
        "1 W/E 04/15/2017",
        "1 W/E 04/22/2017",
        "1 W/E 04/29/2017");

  @dates1_3 = (
        "1 W/E 05/06/2017",
        "1 W/E 05/13/2017",
        "1 W/E 05/20/2017",
        "1 W/E 05/27/2017",
        "1 W/E 06/03/2017",
        "1 W/E 06/10/2017",
        "1 W/E 06/17/2017",
        "1 W/E 06/24/2017");

  @dates2_1 = (
        "1 W/E 07/01/2017",
        "1 W/E 07/08/2017",
        "1 W/E 07/15/2017",
        "1 W/E 07/22/2017",
        "1 W/E 07/29/2017",
        "1 W/E 08/05/2017",
        "1 W/E 08/12/2017",
        "1 W/E 08/19/2017",
        "1 W/E 08/26/2017");

  @dates2_2 = (
        "1 W/E 09/02/2017",
        "1 W/E 09/09/2017",
        "1 W/E 09/16/2017",
        "1 W/E 09/23/2017",
        "1 W/E 09/30/2017",
        "1 W/E 10/07/2017",
        "1 W/E 10/14/2017",
        "1 W/E 10/21/2017");

  @dates2_3 = (
        "1 W/E 10/28/2017",
        "1 W/E 11/04/2017",
        "1 W/E 11/11/2017",
        "1 W/E 11/18/2017",
        "1 W/E 11/25/2017",
        "1 W/E 12/17/2016",
        "1 W/E 12/24/2016",
        "1 W/E 12/31/2016");

  @dates3_1 = (
        "1 W/E 10/15/2016",
        "1 W/E 10/22/2016",
        "1 W/E 10/29/2016",
        "1 W/E 11/05/2016",
        "1 W/E 11/12/2016",
        "1 W/E 11/19/2016",
        "1 W/E 11/26/2016",
        "1 W/E 12/03/2016",
        "1 W/E 12/10/2016");

  @dates3_2 = (
        "1 W/E 08/20/2016",
        "1 W/E 08/27/2016",
        "1 W/E 09/03/2016",
        "1 W/E 09/10/2016",
        "1 W/E 09/17/2016",
        "1 W/E 09/24/2016",
        "1 W/E 10/01/2016",
        "1 W/E 10/08/2016");

  @dates3_3 = (
        "1 W/E 06/25/2016",
        "1 W/E 07/02/2016",
        "1 W/E 07/09/2016",
        "1 W/E 07/16/2016",
        "1 W/E 07/23/2016",
        "1 W/E 07/30/2016",
        "1 W/E 08/06/2016",
        "1 W/E 08/13/2016");

  @dates4_1 = (
        "1 W/E 04/23/2016",
        "1 W/E 04/30/2016",
        "1 W/E 05/07/2016",
        "1 W/E 05/14/2016",
        "1 W/E 05/21/2016",
        "1 W/E 05/28/2016",
        "1 W/E 06/04/2016",
        "1 W/E 06/11/2016",
        "1 W/E 06/18/2016");

  @dates4_2 = (
        "1 W/E 02/27/2016",
        "1 W/E 03/05/2016",
        "1 W/E 03/12/2016",
        "1 W/E 03/19/2016",
        "1 W/E 03/26/2016",
        "1 W/E 04/02/2016",
        "1 W/E 04/09/2016",
        "1 W/E 04/16/2016");

  @dates4_3 = (
        "1 W/E 02/06/2016",
        "1 W/E 02/13/2016",
        "1 W/E 02/20/2016",
        "1 W/E 01/30/2016",
        "1 W/E 01/23/2016",
        "1 W/E 12/26/2015",
        "1 W/E 12/19/2015",
        "1 W/E 12/12/2015");

  @srcFiles = ("Beacon_History_07_18_15_to_05_23_15_142144.tar.gz",
	"Beacon_History_09_12_15_to_07_25_15_142194.tar.gz",
	"Beacon_History_11_07_15_to_09_19_15_142226.tar.gz");



#---------------------------------------------------------------------------

sub create_dummy_file
{
  ($cycle, $fileIdx, $srcFile) = @_;

  undef(%dateHash);

  print STDERR "Using $srcFile\n";

  $dummyArchiveName = "Beacon_Dummy_$cycle" . "_$fileIdx.tar.gz";
  $dummyChecksumName = "Beacon_Dummy_$cycle" . "_checksum_$fileIdx.txt";
  $dummyFactsName = "Beacon_Dummy_$cycle" . "_fct_$fileIdx.txt";

  #output the checksum file (depending on source data file)
  open(OUTPUT, ">Beacon_Dummy_$cycle" . "_checksum_$fileIdx.txt");
  print OUTPUT "FILE_NAME|RECORD_COUNT|BYTES\n";
  if ($fileIdx == 1)
  {
    print OUTPUT "Beacon_History_07_18_15_to_05_23_15_fct_142144.txt|2230492101|442781547719\n";
    $fileLines = 2230492101;
  }
  elsif ($fileIdx == 2)
  {
    print OUTPUT "Beacon_History_09_12_15_to_07_25_15_fct_142194.txt|1992458853|395352973344\n";
    $fileLines = 1992458853;
  }
  else
  {
    print OUTPUT "Beacon_History_11_07_15_to_09_19_15_fct_142226.txt|2021617228|400092140439\n";
    $fileLines = 2021617228;
  }
  close(OUTPUT);

  #get the name of the facts table from inside the archive
  $srcFile =~ m/(.*)_(\d+)\.tar\.gz/;
  $factsFilename = $1 . "_fct_" . $2 . ".txt";

  #open the compressed data stream for reading
  $cmd = "tar -O -zxf /data/nielsen/$srcFile $factsFilename";
  open(INPUT, "-|", $cmd) or die("Unable to open source file, $!");

  #open the output file for this cycle
  open(OUTPUT, ">Beacon_Dummy_$cycle" . "_fct_$fileIdx.txt")
      or die("Unable to open output file, $!");

  #duplicate the header line
  $line = <INPUT>;
  print OUTPUT "$line";

  #cycle through the data lines in the source file
  $count = 0;
  $! = 0;
  while ($line = <INPUT>)
  {

    if ($! > 0)
    {
      die("Fatal error while reading data: $!");
    }

    #check for status update every 1M lines of data
    if (($count % 1_000_000) == 0)
    {
      $pct = ($count / $fileLines) * 100;
      $pct = sprintf("%.1f", $pct);
      print STDERR "$count ($pct%)  CYCLE: $cycle FILE: $fileIdx\n";
    }

    chomp($line);

    #split the line into data fields
    @columns = split('\|', $line);

    #see if we don't have a rewritten date available (date is 4th field)
    $newDate = $dateHash{$columns[3]};
    if (!defined($newDate))
    {
      if (($cycle == 1) && ($fileIdx == 1))
      {
        $newDate = shift(@dates1_1);
      }
      elsif (($cycle == 1) && ($fileIdx == 2))
      {
        $newDate = shift(@dates1_2);
      }
      elsif (($cycle == 1) && ($fileIdx == 3))
      {
        $newDate = shift(@dates1_3);
      }
      elsif (($cycle == 2) && ($fileIdx == 1))
      {
        $newDate = shift(@dates2_1);
      }
      elsif (($cycle == 2) && ($fileIdx == 2))
      {
        $newDate = shift(@dates2_2);
      }
      elsif (($cycle == 2) && ($fileIdx == 3))
      {
        $newDate = shift(@dates2_3);
      }
      elsif (($cycle == 3) && ($fileIdx == 1))
      {
        $newDate = shift(@dates3_1);
      }
      elsif (($cycle == 3) && ($fileIdx == 2))
      {
        $newDate = shift(@dates3_2);
      }
      elsif (($cycle == 3) && ($fileIdx == 3))
      {
        $newDate = shift(@dates3_3);
      }
      elsif (($cycle == 4) && ($fileIdx == 1))
      {
        $newDate = shift(@dates4_1);
      }
      elsif (($cycle == 4) && ($fileIdx == 2))
      {
        $newDate = shift(@dates4_2);
      }
      elsif (($cycle == 4) && ($fileIdx == 3))
      {
        $newDate = shift(@dates4_3);
      }

      print STDERR "Rewriting $columns[3] as $newDate\n";

      $dateHash{$columns[3]} = $newDate;
    }

    #put the rewritten date in place
    $columns[3] = $newDate;

    #join and write out the modified line
    $line = join('|', @columns);
    print OUTPUT "$line\n";
    $count++;
  }

  close(INPUT);
  close(OUTPUT);

  print STDERR "Compressing data...\n";
  `tar zcfv $dummyArchiveName $dummyChecksumName $dummyFactsName`;

   print STDERR "Unlinking working files...\n";
   unlink($dummyChecksumName);
   unlink($dummyFactsName);
}



#---------------------------------------------------------------------------

  chdir("/data2/work");

  $processCount = 0;

  $cycle = 1;
  while ($cycle <= 4)
  {
    print STDERR "Starting file cycle $cycle\n";

    #NB: we're undef'ing this to force each cycle to use a new set of dates
    undef(%dateHash);

    $fileIdx = 1;
    foreach $srcFile (@srcFiles)
    {

      #if we haven't hit our maximum process limit
      if ($processCount < 3)
      {

        #fire off the child process
        if ($pid = fork())
        {
          #parent process

          #increment count of active processes
          $processCount++;
        }

        #else we're the child process
        else
        {
          create_dummy_file($cycle, $fileIdx, $srcFile);
          exit;
        }
      }

      #wait here until an empty process slot opens up
      if ($processCount >= 3)
      {
        wait();
        $processCount--;
      }
      $fileIdx++;
    }
    $cycle++;
  }

  #wait here until the last data dummy creation processes finish
  while ($processCount > 0)
  {
    wait();
    $processCount--;
  }

#EOF
