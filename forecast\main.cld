#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::WebUtils;


#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: KCast</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/jsgrid/jsgrid.min.css" REL="stylesheet">
<LINK HREF="/jsgrid/jsgrid-theme.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<SCRIPT SRC="/jsgrid/jsgrid.min.js"></SCRIPT>

<STYLE>
div.grid th
{
  background: #e2e3e5 !important;
}

.selected-row > td
{
  background: #c4e2ff !important;
  border-color: #c4e2ff;
}
</STYLE>

<SCRIPT>
let selectedFcast = 0;

let gridHeight = window.innerHeight - 250;
if (gridHeight < 250)
{
  gridHeight = 250;
}


\$(document).ready(function()
{
  \$('#dsGrid').jsGrid(
  {
    width: '100%',
    height: gridHeight,
    sorting: true,
    autoload: true,
    loadIndication: true,

    controller:
    {
      loadData: function (filter)
      {
        let data = \$.Deferred();
        \$.ajax(
        {
          type: 'GET',
          contentType: 'application/json; charset=utf-8',
          url: 'ajaxForecastList.cld?$filterURI',
          dataType: 'json'
        }).done(function(response)
        {
         data.resolve(response);
        });
        return data.promise();
      }
    },

    rowClick: function(args)
    {
      selectedFcast = args.item.ID;

      \$('#dsGrid tr').removeClass('selected-row');

      \$selectedRow = \$(args.event.target).closest('tr');
      \$selectedRow.addClass('selected-row');
    },

    rowDoubleClick: function(args)
    {
      selectedFcast = args.item.ID;

      location.href='display.cld?fcID=' + selectedFcast;
    },

    fields: [
      {name: 'ID', type: 'number', visible: false},
      {name: 'Forecast', type: 'text', width: 275},
      {name: 'Type', type: 'text', width: 100},
      {name: 'Data Source', type: 'text', width: 125},
      {name: 'Description', type: 'text', width: 225}
    ]
  });
});



function closeWarning()
{
  document.getElementById('warn-select-fcast').style.display = 'none';
}



function open_fcast()
{
  if (selectedFcast < 1)
  {
    document.getElementById('warn-select-fcast').style.display = 'block';
    return;
  }

  location.href='display.cld?fcID=' + selectedFcast;
}



function edit_fcast()
{
  if (selectedFcast < 1)
  {
    document.getElementById('warn-select-fcast').style.display = 'block';
    return;
  }

  location.href='defineForecastDS.cld?a=e&f=' + selectedFcast;
}



function properties()
{
  if (selectedFcast < 1)
  {
    document.getElementById('warn-select-fcast').style.display = 'block';
    return;
  }

  location.href='properties.cld?fcID=' + selectedFcast;
}



function refresh_fcast()
{
  if (selectedFcast < 1)
  {
    document.getElementById('warn-select-fcast').style.display = 'block';
    return;
  }

  location.href='calculateFcast.cld?f=' + selectedFcast;
}



function sharing()
{
  if (selectedFcast < 1)
  {
    document.getElementById('warn-select-fcast').style.display = 'block';
    return;
  }

  location.href='accessControl.cld?fcID=' + selectedFcast;
}



function delete_fcast()
{
  if (selectedFcast < 1)
  {
    document.getElementById('warn-select-fcast').style.display = 'block';
    return;
  }

  location.href='deleteFcastConfirm.cld?fcID=' + selectedFcast;
}



function show_history()
{
  if (selectedFcast < 1)
  {
    document.getElementById('warn-select-fcast').style.display = 'block';
    return;
  }

  location.href='history.cld?fcID=' + selectedFcast;
}



function show_statistics()
{
  if (selectedFcast < 1)
  {
    document.getElementById('warn-select-fcast').style.display = 'block';
    return;
  }

  location.href='statistics.cld?fcID=' + selectedFcast;
}
</SCRIPT>

</HEAD>

<BODY>

<DIV ID="warn-select-fcast" CLASS="alert alert-warning alert-dismissible fade show" role="alert" STYLE="display:none; width:50%; position:absolute; margin-left:25%;">
  <DIV CLASS="text-center"><STRONG>Select a forecast to perform this operation on.</STRONG></DIV>
  <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="alert"></BUTTON>
</DIV>

END_HTML

    print_html_navbar($kapDB, $userID, $first, $last, $orgName);

    print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-0">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
     <LI CLASS="breadcrumb-item active">KCast</LI>
  </OL>
</NAV>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  $db = KAPutil_connect_to_database();

  print_html_header();

  print <<END_HTML;

<NAV CLASS="navbar navbar-expand-md navbar-light bg-light border mb-4">

  <BUTTON CLASS="navbar-toggler" TYPE="button" data-bs-toggle="collapse" data-bs-target="#navbarNavDropdown">
    <SPAN CLASS="navbar-toggler-icon"></SPAN>
  </BUTTON>

  <DIV CLASS="collapse navbar-collapse justify-content-center" ID="navbarNavDropdown">
    <UL CLASS="navbar-nav">
      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="open_fcast()" TITLE="Open the selected forecast"><I CLASS="bi bi-folder2-open"></I> Open</A></LI>

      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="defineForecastDS.cld" TITLE="Create a new forecast"><I CLASS="bi bi-plus-lg"></I> New</A></LI>

      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="edit_fcast()" TITLE="Edit the selected forecast"><I CLASS="bi bi-pencil"></I> Edit</A></LI>

      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="sharing()" TITLE="Share a forecast with other users"><I CLASS="bi bi-people"></I> Sharing</A></LI>

      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="properties()" TITLE="Edit the properties of the selected forecast"><I CLASS="bi bi-gear"></I> Properties</A></LI>

      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="delete_fcast()" TITLE="Delete the selected forecast"><I CLASS="bi bi-trash"></I> Delete</A></LI>

      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="refresh_fcast()" TITLE="Refresh the selected forecast"><I CLASS="bi bi-arrow-clockwise"></I> Refresh</A></LI>

      <LI CLASS="nav-item dropdown">
        <A CLASS="nav-link dropdown-toggle" HREF="#" ID="navbarDropdownMenuLink" ROLE="button" data-bs-toggle="dropdown">
          <I CLASS="bi bi-three-dots-vertical"></I> More
        </A>
        <UL CLASS="dropdown-menu">
          <LI><A CLASS="dropdown-item" HREF="#" onclick="show_history()">History</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="show_statistics()">Statistics</A></LI>
        </UL>
      </LI>

    </UL>
  </DIV>
</NAV>

<DIV CLASS="container">
  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-12 col-lg-12 col-xl-10"> <!-- content -->

      <P>
<!--
      <DIV CLASS="custom-control custom-checkbox">
        <INPUT CLASS="custom-control-input" NAME="userSources" ID="userSources" TYPE="checkbox" $filterChecked onChange="location.href='$filterCbox'">
        <LABEL CLASS="custom-control-label" FOR="userSources">Only show my forecasts</LABEL>
      </DIV>
-->

      <DIV ID="dsGrid" CLASS="grid mx-auto" STYLE="font-size:13px;"></DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
