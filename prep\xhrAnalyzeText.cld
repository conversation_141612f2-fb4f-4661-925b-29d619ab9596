#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::WebUtils;


#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $flowID = $q->param('f');
  $jobID = $q->param('j');
  $colID = $q->param('col');

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #make sure we have read privs for this data flow
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to analyze this data flow.");
  }

  $flowName = prep_flow_id_to_name($prepDB, $flowID);

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #build name of column containing the text
  $column = "column_" . $colID;

  #get the name of the column we're analyzing
  $query = "SELECT name FROM $masterColTable WHERE ID=$colID";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($colName) = $dbOutput->fetchrow_array;

  print <<END_HTML;
<DIV CLASS="modal-dialog">
  <DIV CLASS="modal-content">

    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Text Analysis</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">
      <DIV CLASS="table-responsive" STYLE="height:400px; overflow:auto;">
        <TABLE CLASS="table table-sm table-hover">
          <THEAD><TR>
            <TH>Text</TH>
            <TH>Count</TH>
          </TR></THEAD>
END_HTML

  #grab each text field entry and its count from the data
  $query = "SELECT $column, COUNT($column) AS count FROM $masterTable \
      GROUP BY $column ORDER BY count DESC";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;

  #display text fields and counts
  while (($val, $count) = $dbOutput->fetchrow_array)
  {
    print <<END_HTML;
          <TR>
            <TD>$val</TD>
            <TD>$count</TD>
          </TR>
END_HTML
  }

  #display count of empty (NULL) fields if there are any
  $query = "SELECT COUNT(*) FROM $masterTable WHERE ISNULL($column)";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($emptyCount) = $dbOutput->fetchrow_array;
  if ($emptyCount > 0)
  {
    print <<END_HTML;
          <TR>
            <TD>(empty)</TD>
            <TD>$emptyCount</TD>
          </TR>
END_HTML
  }

  print <<END_HTML;
        </TABLE>
      </DIV>
    </DIV>

   <DIV CLASS="modal-footer">
    <BUTTON CLASS="btn btn-primary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-check-lg"></I> Done</BUTTON>
   </DIV>

  </DIV>
</DIV>
END_HTML

  $query = "UPDATE prep.jobs SET lastAction=NOW() WHERE ID=$jobID";
  $prepDB->do($query);

  prep_audit($prepDB, $userID, "Performed text analysis on $colName", $flowID);
  utils_slack("PREP: $first $last performed text analysis on $colName in $flowName");

#EOF
