#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepRecipes;
use Lib::PrepUtils;
use Lib::WebUtils;


#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $flowID = $q->param('f');
  $jobID = $q->param('j');
  $colID = $q->param('col');

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #make sure we have write privs for this data flow
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this data flow.");
  }

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #build name of column containing the text
  $column = "column_" . $colID;

  #get the name of the column containing the UPC to be normalized
  $query = "SELECT name FROM $masterColTable WHERE ID=$colID";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($colName) = $dbOutput->fetchrow_array;

  print <<END_HTML;
<SCRIPT>
function checkForm(form)
{
  \$("#btn-submit").prop('disabled', true);
  \$("#btn-submit").text("Please Wait...");
  return(true);
}
</SCRIPT>

<FORM METHOD="post" ACTION="transformProgress.cld" onsubmit="return checkForm(this);">
<INPUT TYPE="hidden" NAME="f" VALUE="$flowID">
<INPUT TYPE="hidden" NAME="j" VALUE="$jobID">
<INPUT TYPE="hidden" NAME="col" VALUE="$colID">
<INPUT TYPE="hidden" NAME="a" VALUE="TRANS-CELL-UPC">

<DIV CLASS="modal-dialog">
  <DIV CLASS="modal-content">

    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">UPC Formatting</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">
      Format UPCs in the <B>$colName</B> column.

      <P>
      <UL CLASS="list-group">
        <LI CLASS="list-group-item">
          <DIV CLASS="row">
            <DIV CLASS="col-auto gx-1 ms-2">
              <DIV CLASS="form-check mt-1">
                <INPUT CLASS="form-check-input" TYPE="radio" NAME="op" ID="normalize" CHECKED VALUE="normalize">
                <LABEL CLASS="form-check-label" FOR="normalize">Normalize UPCs to </LABEL>
              </DIV>
            </DIV>
            <DIV CLASS="col-auto mt-0">
              <INPUT TYPE="number" NAME="trim-length" ID="trim-length" CLASS="form-control" STYLE="width:4em;" min=1 VALUE=12>
            </DIV>
            <DIV CLASS="col-auto mt-1">
              digits by:
            </DIV>
          </DIV>

          <DIV CLASS="form-check ms-5 mt-3">
            <INPUT CLASS="form-check-input" TYPE="radio" NAME="normOp" ID="trimSizeLeadZeroes" VALUE="trimSizeLeadZeroes" CHECKED>
            <LABEL CLASS="form-check-label" FOR="trimSizeLeadZeroes">Trimming leading zeroes</LABEL>
          </DIV>
          <DIV CLASS="form-check ms-5">
            <INPUT CLASS="form-check-input" TYPE="radio" NAME="normOp" ID="trimSizeBegin" VALUE="trimSizeBegin">
            <LABEL CLASS="form-check-label" FOR="trimSizeBegin">Trimming any leading digits</LABEL>
          </DIV>
          <DIV CLASS="form-check ms-5">
            <INPUT CLASS="form-check-input" TYPE="radio" NAME="normOp" ID="trimSizeTrailZeroes" VALUE="trimSizeTrailZeroes">
            <LABEL CLASS="form-check-label" FOR="trimSizeTrailZeroes">Trimming trailing zeroes</LABEL>
          </DIV>
          <DIV CLASS="form-check ms-5">
            <INPUT CLASS="form-check-input" TYPE="radio" NAME="normOp" ID="trimSizeEnd" VALUE="trimSizeEnd">
            <LABEL CLASS="form-check-label" FOR="trimSizeEnd">Trimming any trailing digits</LABEL>
          </DIV>
          <DIV CLASS="form-check ms-5">
            <INPUT CLASS="form-check-input" TYPE="radio" NAME="normOp" ID="addSizeLeadZeroes" VALUE="addSizeLeadZeroes">
            <LABEL CLASS="form-check-label" FOR="addSizeLeadZeroes">Adding leading zeroes</LABEL>
          </DIV>
          <DIV CLASS="form-check ms-5">
            <INPUT CLASS="form-check-input" TYPE="radio" NAME="normOp" ID="addSizeTrailZeroes" VALUE="addSizeTrailZeroes">
            <LABEL CLASS="form-check-label" FOR="addSizeTrailZeroes">Adding trailing zeroes</LABEL>
          </DIV>

        </LI>
        <LI CLASS="list-group-item">
          <DIV CLASS="form-check">
            <INPUT CLASS="form-check-input" TYPE="radio" NAME="op" ID="trimLeadZeroes" VALUE="trimLeadZeroes">
            <LABEL CLASS="form-check-label" FOR="trimLeadZeroes">Trim all leading zeroes</LABEL>
          </DIV>
        </LI>
        <LI CLASS="list-group-item">
          <DIV CLASS="form-check">
            <INPUT CLASS="form-check-input" TYPE="radio" NAME="op" ID="trimTrailZeroes" VALUE="trimTrailZeroes">
            <LABEL CLASS="form-check-label" FOR="trimTrailZeroes">Trim all trailing zeroes</LABEL>
          </DIV>
        </LI>
        <LI CLASS="list-group-item">
          <DIV CLASS="form-check">
            <INPUT CLASS="form-check-input" TYPE="radio" NAME="op" ID="removeHyphens" VALUE="removeHyphens">
            <LABEL CLASS="form-check-label" FOR="removeHyphens">Remove hyphens</LABEL>
          </DIV>
        </LI>

      </UL>

    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="submit" ID="btn-submit"><I CLASS="bi bi-pencil-square"></I> Apply</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML

#EOF
