#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: User Information</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/fontawesome-5.10.2/css/all.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<SCRIPT SRC="/jquery-editable-select-2.2.5/dist/jquery-editable-select.min.js"></SCRIPT>
<LINK HREF="/jquery-editable-select-2.2.5/dist/jquery-editable-select.min.css" REL="stylesheet">


<SCRIPT>
function checkForm(form)
{
  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Please Wait...');
  return(true);
}


\$(document).ready(function()
{
  \$('#show-hide-password a').on('click', function(event)
  {
    event.preventDefault();
    if (\$('#show-hide-password input').attr('type') == 'text')
    {
      \$('#show-hide-password input').attr('type', 'password');
      \$('#show-hide-password i').addClass('bi-eye-slash');
      \$('#show-hide-password i').removeClass('bi-eye');
    }
    else if (\$('#show-hide-password input').attr('type') == 'password')
    {
      \$('#show-hide-password input').attr('type', 'text');
      \$('#show-hide-password i').removeClass('bi-eye-slash');
      \$('#show-hide-password i').addClass('bi-eye');
    }
  });
});
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="home.cld">Administration</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="userMgmt.cld">User Management</A></LI>
    <LI CLASS="breadcrumb-item active">Modify User</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if ((length($email) < 1) || ($acctType < 1))
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $user = $q->param('u');

  #connect to the database
  $db = KAPutil_connect_to_database();

  print_html_header();

  #if we're editing an existing user, get the info from the database
  if (defined($user))
  {
    $query = "SELECT email, first, last, password, orgID, acctType, licensePrep, licenseForecast, licenseAInsights, betaTester \
        FROM users WHERE ID=$user";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);

    ($userEmail, $userFirst, $userLast, $userPass, $userOrg, $userAcctType, $licensePrep, $licenseForecast, $licenseAInsights, $betaTester) = $dbOutput->fetchrow_array;
  }

  #if we aren't an admin, we can only edit Viewers
  if (($acctType < 5) && ($userAcctType > 0))
  {
    exit_error("You don't have privileges to create or edit this type of user");
  }

  #blank out non-viewer's password - we'll just leave an 8X password alone
  if ((defined($userPass)) && ($userAcctType > 0))
  {
    $userPass = "XXXXXXXXX";
  }

  #set up check box status
  if ($licensePrep == 1)
  {
    $licensePrep = "CHECKED";
  }
  if ($licenseForecast == 1)
  {
    $licenseForecast = "CHECKED";
  }
  if ($licenseAInsights == 1)
  {
    $licenseAInsights = "CHECKED";
  }
  if ($betaTester == 1)
  {
    $betaTester = "CHECKED";
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-10 col-lg-7 col-xl-6"> <!-- content -->

      <FORM METHOD="post" ACTION="/app/admin/userInfoSave.cld" onsubmit="return checkForm(this);">
      <INPUT TYPE="hidden" NAME="u" VALUE="$user">

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">User Information</DIV>
        <DIV CLASS="card-body">

          <DIV CLASS="row">
            <LABEL FOR="email" CLASS="col-sm-3 col-form-label text-end mb-1">Email Address:</LABEL>
            <DIV CLASS="col-sm-8 text-start">
              <INPUT CLASS="form-control" TYPE="email" NAME="email" ID="email" VALUE="$userEmail" required>
            </DIV>
          </DIV>

          <DIV CLASS="row">
            <LABEL FOR="first" CLASS="col-sm-3 col-form-label text-end mb-1">First Name:</LABEL>
            <DIV CLASS="col-sm-8 text-start">
              <INPUT CLASS="form-control" TYPE="text" NAME="first" ID="first" VALUE="$userFirst" required>
            </DIV>
          </DIV>

          <DIV CLASS="row">
            <LABEL FOR="last" CLASS="col-sm-3 col-form-label text-end mb-1">Last Name:</LABEL>
            <DIV CLASS="col-sm-8 text-start">
              <INPUT CLASS="form-control" TYPE="text" NAME="last" ID="first" VALUE="$userLast" required>
            </DIV>
          </DIV>

          <DIV CLASS="row">
            <LABEL FOR="pass" CLASS="col-sm-3 col-form-label text-end mb-1">Password:</LABEL>
END_HTML

  if ($userAcctType < 1)
  {
    print <<END_HTML;
                <DIV CLASS="col-sm-8 text-start" ID="show-hide-password">
                  <DIV CLASS="input-group">
                    <INPUT CLASS="form-control" TYPE="password" NAME="pass" ID="pass" VALUE="$userPass" autocomplete="new-password" required>
                    <A CLASS="input-group-text text-decoration-none" HREF=""><I CLASS="bi bi-eye-slash" aria-hidden="true"></I></A>
                  </DIV>
                </DIV>
END_HTML
  }
  else
  {
    print <<END_HTML;
                <DIV CLASS="col-sm-8 text-start" ID="show-hide-password">
                  <INPUT CLASS="form-control" TYPE="password" NAME="pass" ID="pass" VALUE="$userPass" autocomplete="new-password" required>
                </DIV>
END_HTML
  }

  print <<END_HTML;
          </DIV>
          <DIV CLASS="row">
            <LABEL FOR="org" CLASS="col-sm-3 col-form-label text-end mb-1">Organization:</LABEL>
            <DIV CLASS="col-sm-8 text-start">
              <SELECT CLASS="form-select" NAME="org" ID="org" required>
END_HTML

  $query = "SELECT ID, name FROM orgs ORDER BY name";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  while (($id, $name) = $dbOutput->fetchrow_array)
  {
    if ($id == $userOrg)
    {
      $userOrgName = $name;
    }
    print("   <OPTION>$name</OPTION>\n");
  }

  print <<END_HTML;
              </SELECT>
              <SCRIPT>
                \$('#org').editableSelect({filter:false});
                \$('#org').val('$userOrgName');
              </SCRIPT>
            </DIV>
          </DIV>
          <DIV CLASS="row">
            <LABEL FOR="acctType" CLASS="col-sm-3 col-form-label text-end mb-1">User Type:</LABEL>
            <DIV CLASS="col-sm-8 text-start">
              <SELECT CLASS="form-select" NAME="acctType" ID="acctType">
                <OPTION VALUE="0">Viewer</OPTION>
END_HTML

  if ($acctType > 4)
  {
    print <<END_HTML;
                <OPTION VALUE="1">Analyst</OPTION>
                <OPTION VALUE="5">Administrator</OPTION>
END_HTML
  }

  print <<END_HTML;
              </SELECT>
              <SCRIPT>
                \$('select#acctType').val('$userAcctType');
              </SCRIPT>
            </DIV>
          </DIV>
END_HTML

  if ($acctType > 4)
  {
    print <<END_HTML;
          <P>&nbsp;</P>
          <DIV CLASS="form-check">
            <INPUT CLASS="form-check-input" NAME="licensePrep" ID="licensePrep" TYPE="checkbox" $licensePrep>
            <LABEL CLASS="form-check-label" FOR="licensePrep">Licensed for Data Prep</LABEL>
          </DIV>
          <DIV CLASS="form-check">
            <INPUT CLASS="form-check-input" NAME="licenseForecast" ID="licenseForecast" TYPE="checkbox" $licenseForecast>
            <LABEL CLASS="form-check-label" FOR="licenseForecast">Licensed for KCast</LABEL>
          </DIV>
          <DIV CLASS="form-check">
            <INPUT CLASS="form-check-input" NAME="licenseAInsights" ID="licenseAInsights" TYPE="checkbox" $licenseAInsights>
            <LABEL CLASS="form-check-label" FOR="licenseAInsights">Licensed for AInsights</LABEL>
          </DIV>

          <P>&nbsp;</P>
          <DIV CLASS="form-check">
            <INPUT CLASS="form-check-input" NAME="betaTester" ID="betaTester" TYPE="checkbox" $betaTester>
            <LABEL CLASS="form-check-label" FOR="betaTester">Beta tests new features</LABEL>
          </DIV>

END_HTML
  }

  print <<END_HTML;

          <P>&nbsp;<P>
          <CENTER>
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="location.href='/app/admin/userMgmt.cld'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-submit"><I CLASS="bi bi-save"></I> Save</BUTTON>
          </CENTER>

        </DIV>
      </DIV>
      </FORM>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
