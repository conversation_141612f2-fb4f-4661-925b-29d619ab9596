#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::Reports;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------
#
# Output the HTML for a feed item
#

sub feed_item_output
{
  my ($itemID, $class, $icon, $title, $insertDate, $content) = @_;

  print <<END_HTML;
<article class="article">
  <div class="card border-$class" id="feed_$itemID">
    <div class="card-body">
      <DIV CLASS="mx-3 my-2 position-absolute top-0 end-0">
        <A ID="popover_$itemID" data-bs-toggle="popover" data-bs-html="true" data-bs-container="body"
          data-bs-placement="bottom" tabindex="0" data-bs-trigger="focus"
          data-bs-content="<a class='text-decoration-none' href='home.cld?i=$itemID&a=c'>Clear all items like this</a>">
          <I CLASS="bi bi-three-dots"></I></A>
        <script>
        //  \$('[data-bs-toggle="popover"]').popover();
        var popover_$itemID = new bootstrap.Popover(document.querySelector('#popover_$itemID'),
        {
          container: 'body'
        });
        </script>
        <button type="button" class="btn-close my-2" onclick="closeFeedItem('$itemID');">
        </button>
      </DIV>

      <h5 class="card-title text-$class">
        <img src="/icons/$icon">
        $title
      </h5>
      <sup>$insertDate</sup>
      <p>
      $content
    </div>
  </div>
  <p>
</article>
END_HTML
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #create the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  $lastID = $session->param('feedID');

  #let super admins view another user's feed
  $feedUserID = $q->param('u');
  if (($acctType < 10) || ($feedUserID < 1))
  {
    $feedUserID = $userID;
  }

  $db = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  print_html_header();

  #grab the next 10 feed items for the user
  if ($lastID == 0)
  {
    $query = "SELECT ID, TIMEDIFF(NOW(), insertDate), DATE_FORMAT(insertDate, '%M %e at %l:%i %p'), dsID, cubeID, flowID, class, type, content \
        FROM app.feed WHERE userID=$feedUserID ORDER BY ID DESC LIMIT 10";
  }
  else
  {
    $query = "SELECT ID, TIMEDIFF(NOW(), insertDate), DATE_FORMAT(insertDate, '%M %e at %l:%i %p'), dsID, cubeID, flowID, class, type, content \
        FROM app.feed WHERE userID=$feedUserID AND ID < $lastID \
        ORDER BY ID DESC LIMIT 10";
  }
  $dbOutput = $db->prepare($query);
  $feedCount = $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  #if there are no more items to display, show a couple defaults
  if ($feedCount < 1)
  {
    print <<END_HTML;
<article class="article">
  <div class="card border-info">
    <div class="card-body">
      <h5 class="card-title text-info">
        <img src="/icons/feed_docs.png">
        Documentation
      </h5>
      <p>
      <a class="text-decoration-none" href="/app/docs.cld">Peruse the Koala documentation</a> to get expert information and answers.
    </div>
  </div>
  <p>
</article>

<article class="article">
  <div class="card border-info">
    <div class="card-body">
      <h5 class="card-title text-info">
        <img src="/icons/feed_ds.png">
        Data Source Management
      </h5>
      <p>
      <a class="text-decoration-none" href="/app/docs.cld">Manage your data sources</a>.
    </div>
  </div>
  <p>
</article>
END_HTML

    exit;
  }

  #cycle through the set of feed items and output
  while (($itemID, $insertDate, $monthDayDate, $dsID, $cubeID, $flowID, $class, $type, $content) = $dbOutput->fetchrow_array)
  {
    $lastID = $itemID;

    #deal with a difference between our class and what Bootstrap uses for syling
    if ($class eq "warn")
    {
      $class = "warning";
    }

    #convert the time-since-insert into something human-friendly
    #NB: we're roughly ripping off FB's dating methodology
    $insertDate =~ m/^(.*?)\:(.*?)\:(.*?)$/;
    $hours = int($1);
    $minutes = int($2);
    $seconds = int($3);
    $days = int($hours / 24);
    $weeks = int($days / 7);
    $months = int($weeks / 4);
    if (($hours < 1) && ($minutes < 5))
    {
      $insertDate = "Just now";
    }
    elsif ($hours < 1)
    {
      $insertDate = $minutes . "m";
    }
    elsif ($hours < 24)
    {
      $insertDate = $hours . "h";
    }
    elsif ($days < 7)
    {
      $insertDate = $days . "d";
    }
    else
    {
      $insertDate = $monthDayDate;
    }


    #------------ System-wide feed items ------------------------

    #new Koala users
    if ($type eq "koala_welcome")
    {
      $content = "Welcome to Koala, a self-service analytics tool for the consumer packaged goods industry!\n";
      $content .= "<p>\n";
      $content .= "Follow along with the <a class='text-decoration-none' target='_blank' href='/app/docs.cld?t=dsr_quickstart_quickstart'>Koala Quick Start Guide</a> to get started.";
      feed_item_output($itemID, $class, "feed_rocket.png", "Welcome to Koala!", $insertDate, $content);
    }


    #---------- Successful Data Source Ops ----------------------

    #successful DS update (either manual or from Prep)
    elsif ($type eq "ds_update")
    {
      $dsName = ds_id_to_name($db, $dsID);
      $dsName = "<a CLASS='text-decoration-none' href='dsr/display.cld?ds=$dsID'>$dsName</a>";
      if ($flowID > 0)
      {
        $flowName = prep_flow_id_to_name($prepDB, $flowID);
        $flowName = "<a CLASS='text-decoration-none' href='$Lib::KoalaConfig::prepHostURL/app/prep/flowOpen.cld?f=$flowID'>$flowName</a>";
        $content = "Your data source $dsName has been successfully updated with new data by the $flowName data flow.";
      }
      else
      {
        $content = "Your data source $dsName has been successfully updated with new data.";
      }

      feed_item_output($itemID, $class, "feed_ds_update.png", "Data Source Updated", $insertDate, $content);
    }

    #successful ODBC refresh
    elsif ($type eq "odbc_refresh")
    {
      $dsName = ds_id_to_name($db, $dsID);
      $dsName = "<a CLASS='text-decoration-none' href='dsr/display.cld?ds=$dsID'>$dsName</a>";
      $content = "The ODBC export of $dsName has been refreshed.";

      feed_item_output($itemID, $class, "feed_odbc_refresh.png", "ODBC Export Refreshed", $insertDate, $content);
    }


    #--------------- Data Source Warnings -------------------------

    #unsegmented items in data source
    elsif ($type eq "ds_unsegmented")
    {
      feed_item_output($itemID, $class, "feed_warning.png", "Unsegmented Items", $insertDate, $content);
    }

    #empty list in data source
    elsif ($type eq "ds_empty_list")
    {
      feed_item_output($itemID, $class, "feed_warning.png", "Empty List", $insertDate, $content);
    }

    #empty aggregate in data source
    elsif ($type eq "ds_empty_agg")
    {
      feed_item_output($itemID, $class, "feed_warning.png", "Empty Aggregate", $insertDate, $content);
    }

    #data source hasn't been updated in at least 3 months
    elsif ($type eq "ds_no_updates3")
    {
      $dsName = ds_id_to_name($db, $dsID);
      $dsName = "<a CLASS='text-decoration-none' href='dsr/display.cld?ds=$dsID'>$dsName</a>";
      $content = "Your data source $dsName hasn't been updated in over three months.";

      feed_item_output($itemID, $class, "feed_warning.png", "Stale Data Source", $insertDate, $content);
    }

    #ODBC needs a manual refresh (underlying data source has newer data)
    elsif ($type eq "odbc_needs_update")
    {
      $dsName = ds_id_to_name($db, $dsID);
      $dsName = "<a CLASS='text-decoration-none' href='dsr/display.cld?ds=$dsID'>$dsName</a>";
      $content = "Your data source $dsName has an ODBC export that is out of date and needs to be manually refreshed.";

      feed_item_output($itemID, $class, "feed_warning.png", "ODBC Export Needs Manual Refresh", $insertDate, $content);
    }

    #large tabular ODBC export
    elsif ($type eq "odbc_large_tabular")
    {

      $dsName = ds_id_to_name($db, $dsID);
      $dsName = "<a CLASS='text-decoration-none' href='dsr/display.cld?ds=$dsID'>$dsName</a>";
      $content = "Your data source $dsName has a very large tabular ODBC export. If you switch it to a star schema ODBC export, you'll improve the performance of the data source in both Koala and your other analysis tools.";

      feed_item_output($itemID, $class, "feed_warning.png", "Large Tabular ODBC Export", $insertDate, $content);
    }


    #--------------- Data Source Danger -------------------------

    #unsegmented items in data source
    elsif ($type eq "ds_undef_meas_agg")
    {
      feed_item_output($itemID, $class, "feed_danger.png", "Undefined Aggregation Rules", $insertDate, $content);
    }


    #------------ Successful report ops -------------------------

    #successful report update
    elsif ($type eq "rpt_update")
    {
      $dsName = ds_id_to_name($db, $dsID);
      $dsName = "<a CLASS='text-decoration-none' href='dsr/display.cld?ds=$dsID'>$dsName</a>";
      $rptName = report_id_to_name($db, $cubeID);
      $rptName = "<a CLASS='text-decoration-none' href='rpt/display.cld?rpt=$cubeID'>$rptName</a>";
      $content = "Your report $rptName in $dsName has been successfully refreshed.";

      feed_item_output($itemID, $class, "feed_rpt_update.png", "Report Refreshed", $insertDate, $content);
    }

    #successful report updates by background agent
    elsif ($type eq "rpt_update_agent")
    {
      $dsName = ds_id_to_name($db, $dsID);
      $dsName = "<a CLASS='text-decoration-none' href='dsr/display.cld?ds=$dsID'>$dsName</a>";
      $content = "All of your reports in $dsName have been automatically refreshed with new data.";

      feed_item_output($itemID, $class, "feed_rpt_update.png", "Reports Refreshed", $insertDate, $content);
    }


    #------------------ Report warnings ----------------------------

    #report needs a manual refresh
    elsif ($type eq "rpt_manual_refresh")
    {

      $dsName = ds_id_to_name($db, $dsID);
      $dsName = "<a CLASS='text-decoration-none' href='dsr/display.cld?ds=$dsID'>$dsName</a>";
      $rptName = report_id_to_name($db, $cubeID);
      $rptName = "<a CLASS='text-decoration-none' href='rpt/display.cld?rpt=$cubeID'>$rptName</a>";
      $content = "Your report $rptName in $dsName needs to be manually refreshed - you've turned off automatic refreshing in this data source.";

      feed_item_output($itemID, $class, "feed_warning.png", "Manually Refresh Report", $insertDate, $content);
    }


    #------------------- Report errors ---------------------------------

    #report build failure due to size
    elsif ($type eq "rpt_size")
    {
      $dsName = ds_id_to_name($db, $dsID);
      $dsName = "<a CLASS='text-decoration-none' href='dsr/display.cld?ds=$dsID'>$dsName</a>";
      $rptName = report_id_to_name($db, $cubeID);
      $rptName = "<a CLASS='text-decoration-none' href='rpt/display.cld?rpt=$cubeID'>$rptName</a>";
      $content = "Your report $rptName in $dsName cannot be refreshed because it is too large for your cloud.";

      feed_item_output($itemID, $class, "feed_danger.png", "Report Too Large to Refresh", $insertDate, $content);
    }

    #report build failure due to missing dimension selections
    elsif ($type eq "rpt_missing_dim")
    {
      $dsName = ds_id_to_name($db, $dsID);
      $dsName = "<a CLASS='text-decoration-none' href='dsr/display.cld?ds=$dsID'>$dsName</a>";
      $rptName = report_id_to_name($db, $cubeID);
      $rptName = "<a CLASS='text-decoration-none' href='rpt/display.cld?rpt=$cubeID'>$rptName</a>";
      $content =~ m/ERROR:No (.*) are selected/;
      $content = "Your report $rptName in $dsName cannot be refreshed because it doesn't contain any $1.";

      feed_item_output($itemID, $class, "feed_danger.png", "Report Missing Data Selection", $insertDate, $content);
    }


    #---------------- Data Prep Informationals ---------------------

    #Prep data flow pulling from AOD data warehouse but not scheduled daily
    elsif ($type eq "prep_flow_idw_schedule")
    {
      feed_item_output($itemID, $class, "feed_prep.png", "Suggested Data Flow Scheduling Improvement", $insertDate, $content);
    }

    #Prep data flow waiting for user parsing input
    elsif ($type eq "prep_flow_parse_wait")
    {
      feed_item_output($itemID, $class, "feed_prep.png", "Data Flow Waiting for Parsing Input", $insertDate, $content);
    }

    #Prep data flow waiting for user data type input
    elsif ($type eq "prep_flow_datatype_wait")
    {
      feed_item_output($itemID, $class, "feed_prep.png", "Data Flow Waiting for Data Type Input", $insertDate, $content);
    }

    #Prep data flow waiting for export to Koala
    elsif ($type eq "prep_flow_export_ready")
    {
      feed_item_output($itemID, $class, "feed_prep.png", "Data Flow Ready for Export", $insertDate, $content);
    }


    #-------------- Data Prep warnings --------------------------

    #Prep data flow pulling from AOD data warehouse but not trimming extra BC CATEGORY
    elsif ($type eq "prep_flow_idw_trim_bccat")
    {
      feed_item_output($itemID, $class, "feed_warning.png", "Data Flow with Duplicate Column", $insertDate, $content);
    }


    #handle otherwise unknown item
    else
    {
      print <<END_HTML;
<article class="article">
  <div class="card" id="feed_$itemID">
  <button type="button" class="close" onclick="closeFeedItem('$itemID');">
    <span aria-hidden="true">&times;</span>
  </button>
    <div class="card-header">
      $class $type
    </div>
    <div class="card-body">
      $content
    </div>
  </div>
</article>
END_HTML
    }
  }

  #output the feed info for the next set of feed items
  print <<END_HTML;
  <!-- pagination has path -->
  <p class="pagination">
    <a class="pagination__next" href="#">Next page</a>
  </p>
END_HTML

  $session->param('feedID', $lastID);
  $session->flush();

  print_html_footer();

#EOF
