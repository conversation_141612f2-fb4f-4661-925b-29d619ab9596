#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRMeasures;
use Lib::DSRUtils;
use Lib::Reports;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get the CGI input variables
  $rptID = $q->param('rpt');
  $visID = $q->param('v');

  #get any "fixed" dimensions (used for expanded reports)
  $fProd = $q->param('p');
  $fGeo = $q->param('g');
  $fTime = $q->param('t');

  $db = KAPutil_connect_to_database();

  #if we're being called as part of a PPT export
  if ($userID < 1)
  {
    $userID = $q->param('u');
    $query = "SELECT acctType FROM app.users WHERE ID=$userID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($acctType) = $dbOutput->fetchrow_array;
  }

  #if the user is a viewer, use the viewer selection table
  if ($acctType == 0)
  {
    $visualSelTable = "app.visuals_viewers";
  }
  else
  {
    $visualSelTable = "app.visuals";
  }

  #NB: graph_x contains the dimension we're using as the data set

  #get the list of axes & selected dimension items from the database
  $query = "SELECT dsID, design, graph_x FROM visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($dsID, $graphDesign, $graph_x) = $dbOutput->fetchrow_array;

  ($productIDstring, $geographyIDstring, $timeIDstring, $measureIDstring) = reports_get_selected_items($db, $visID, $userID, $acctType);

  #get slicer configuration
  $query = "SELECT slicers FROM cubes WHERE ID=$rptID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($slicersStr) = $dbOutput->fetchrow_array;

  $graphType = reports_get_style($graphDesign, "type");

  print("Content-type: text/plain\n\n");

  #assemble report cube name
  $dsSchema = "datasource_" . $dsID;
  $rptCube = "_rptcube_" . $rptID;

  #get the ID of the measure we're graphing
  $measureIDstring =~ m/(\d+)/;
  $measureID = $1;

  #if we're dealing with a fixed dimension (used for expanded reports)
  if (length($fProd) > 0)
  {
    $productIDstring = $fProd;
  }
  if (length($fGeo) > 0)
  {
    $geographyIDstring = $fGeo;
  }
  if (length($fTime) > 0)
  {
    $timeIDstring = $fTime;
  }

  @selProds = split(/,/, $productIDstring);
  @selGeos = split(/,/, $geographyIDstring);
  @selTimes = split(/,/, $timeIDstring);
  @selMeasures = split(/,/, $measureIDstring);

  #fetch the item names for our data set
  %setNames = dsr_get_item_name_hash($db, $dsSchema, $graph_x);

  #escape any special characters in our category & set names
  foreach $id (keys %setNames)
  {
    $setNames{$id} =~ s/\'//g;
  }

  #create an array of the categories we need to display, and go ahead and set
  #up our "single selection" dimension IDs while we're at it
  if ($graph_x eq "p")
  {
    @setIDs = split(/,/, $productIDstring);

    $q_geography = $db->quote($selGeos[0]);
    $q_time = $db->quote($selTimes[0]);
  }
  elsif ($graph_x eq "g")
  {
    @setIDs = split(/,/, $geographyIDstring);

    $q_product = $db->quote($selProds[0]);
    $q_time = $db->quote($selTimes[0]);
  }
  elsif ($graph_x eq "t")
  {
    @setIDs = split(/,/, $timeIDstring);

    $q_product = $db->quote($selProds[0]);
    $q_geography = $db->quote($selGeos[0]);
  }
  elsif ($graph_x eq "m")
  {
    @setIDs = split(/,/, $measureIDstring);

    $q_product = $db->quote($selProds[0]);
    $q_geography = $db->quote($selGeos[0]);
    $q_time = $db->quote($selTimes[0]);
  }

  @origSetIDs = @setIDs;

  #build up the SQL string we're going to use for selections
  $itemStr = "";
  foreach $item (@setIDs)
  {
    if ($graph_x eq "m")
    {
      $itemStr .= "measure_$item,";
    }
    else
    {
      $itemStr .= "'$item',";
    }
  }
  chop($itemStr);


  # ------- parse graph design info ----------

  #handle a "locked" measure for the graph
  if ($graphDesign =~ m/,measure:(\d+),/)
  {
    $lockedMeasure = $1;
    if ($lockedMeasure > 0)
    {
      $measureID = $lockedMeasure;
    }
  }

  #handle a caption (chart title)
  if ($graphDesign =~ m/,caption:\"(.*?)\",/)
  {
    $caption = $1;
  }

  #handle a subcaption (chart subtitle)
  if ($graphDesign =~ m/,subcaption:\"(.*?)\",/)
  {
    $subcaption = $1;
  }

  $captionFontColor = reports_get_style($graphDesign, "captionFontColor");
  if (length($captionFontColor) < 1)
  {
    $captionFontColor = reports_chart_design_default("captionFontColor");
  }
  $captionFontColor = "captionFontColor='$captionFontColor'";

  $captionAlignment = reports_get_style($graphDesign, "captionAlignment");
  if (length($captionAlignment) < 1)
  {
    $captionAlignment = reports_chart_design_default("captionAlignment");
  }
  $captionAlignment = "captionAlignment='$captionAlignment'";

  $captionFontSize = reports_get_style($graphDesign, "captionFontSize");
  if (length($captionFontSize) < 1)
  {
    $captionFontSize = reports_chart_design_default("captionFontSize");
  }
  $captionFontSize = "captionFontSize='$captionFontSize'";

  $captionFont = reports_get_style($graphDesign, "captionFont");
  if (length($captionFont) > 3)
  {
    $captionFont = "captionFont='$captionFont'";
  }

  #handle x and y axis names
  if ($graphDesign =~ m/,xAxisName:\"(.*?)\",/)
  {
    $xAxisName = $1;
  }

  $xAxisNameFontSize = reports_get_style($graphDesign, "xAxisNameFontSize");
  if (length($xAxisNameFontSize) < 1)
  {
    $xAxisNameFontSize = reports_chart_design_default("xAxisNameFontSize");
  }
  $xAxisNameFontSize = "xAxisNameFontSize='$xAxisNameFontSize'";

  if ($graphDesign =~ m/,yAxisName:\"(.*?)\",/)
  {
    $yAxisName = $1;
  }

  $yAxisNameFontSize = reports_get_style($graphDesign, "yAxisNameFontSize");
  if (length($yAxisNameFontSize) < 1)
  {
    $yAxisNameFontSize = reports_chart_design_default("yAxisNameFontSize");
  }
  $yAxisNameFontSize = "yAxisNameFontSize='$yAxisNameFontSize'";


  #handle background color
  $bgColor = reports_get_style($graphDesign, "bgColor");
  if (length($bgColor) < 6)
  {
    $bgColor = reports_chart_design_default("bgColor");
  }
  $canvasBgColor = "canvasBgColor='$bgColor'";
  $bgColor = "bgColor='$bgColor'";


  #handle a border (color & thickness)
  $showBorder = reports_get_style($graphDesign, "showBorder");
  if (length($showBorder) < 1)
  {
    $showBorder = reports_chart_design_default("showBorder");
  }
  $showBorder = "showBorder='$showBorder'";

  $borderColor = reports_get_style($graphDesign, "borderColor");
  if (length($borderColor) < 6)
  {
    $borderColor = reports_chart_design_default("borderColor");
  }
  $borderColor = "borderColor='$valueFontColor'";

  $borderThickness = reports_get_style($graphDesign, "borderThickness");
  if (length($borderThickness) < 1)
  {
    $borderThickness = reports_chart_design_default("borderThickness");
  }
  $borderThickness = "borderThickness='$borderThickness'";


  #handle graph legend styling
  $showLegend = reports_get_style($graphDesign, "showLegend");
  if (length($showLegend) < 1)
  {
    $showLegend = reports_chart_design_default("showLegend");
  }
  $showLegend = "showLegend='$showLegend'";

  $legendItemFontColor = reports_get_style($graphDesign, "legendItemFontColor");
  if (length($legendItemFontColor) < 6)
  {
    $legendItemFontColor = reports_chart_design_default("legendItemFontColor");
  }
  $legendItemFontColor = "legendItemFontColor='$legendItemFontColor'";

  $legendPosition = reports_get_style($graphDesign, "legendPosition");
  if (length($legendPosition) < 1)
  {
    $legendPosition = reports_chart_design_default("legendPosition");
  }
  $legendPosition = "legendPosition='$legendPosition'";

  $legendItemFont = reports_get_style($graphDesign, "legendItemFont");
  if (length($legendItemFont) < 1)
  {
    $legendItemFont = reports_chart_design_default("legendItemFont");
  }
  $legendItemFont = "legendItemFont='$legendItemFont'";

  $legendItemFontSize = reports_get_style($graphDesign, "legendItemFontSize");
  if (length($legendItemFontSize) < 1)
  {
    $legendItemFontSize = reports_chart_design_default("legendItemFontSize");
  }
  $legendItemFontSize = "legendItemFontSize='$legendItemFontSize'";


  #handle data label styling
  $showLabels = reports_get_style($graphDesign, "showLabels");
  if (length($showLabels) < 1)
  {
    $showLabels = reports_chart_design_default("showLabels");
  }
  $showLabels = "showLabels='$showLabels'";

  $labelFontColor = reports_get_style($graphDesign, "labelFontColor");
  if (length($labelFontColor) < 1)
  {
    $labelFontColor = reports_chart_design_default("labelFontColor");
  }
  $labelFontColor = "labelFontColor='$labelFontColor'";

  $labelDisplay = reports_get_style($graphDesign, "labelDisplay");
  if (length($labelDisplay) < 1)
  {
    $labelDisplay = reports_chart_design_default("labelDisplay");
  }
  $labelDisplay = "labelDisplay='$labelDisplay'";

  $labelStep = reports_get_style($graphDesign, "labelStep");
  if (length($labelStep) < 1)
  {
    $labelStep = reports_chart_design_default("labelStep");
  }
  $labelStep = "labelStep='$labelStep'";

  $labelFontSize = reports_get_style($graphDesign, "labelFontSize");
  if (length($labelFontSize) < 1)
  {
    $labelFontSize = reports_chart_design_default("labelFontSize");
  }
  $labelFontSize = "labelFontSize='$labelFontSize'";

  $labelFont = reports_get_style($graphDesign, "labelFont");
  if (length($labelFont) > 3)
  {
    $labelFont = "labelFont='$labelFont'";
  }


  #handle data value styling
  $showValues = reports_get_style($graphDesign, "showValues");
  if (length($showValues) < 1)
  {
    $showValues = reports_chart_design_default("showValues");
  }
  $showValues = "showValues='$showValues'";

  $valueFontColor = reports_get_style($graphDesign, "valueFontColor");
  if (length($valueFontColor) < 6)
  {
    $valueFontColor = reports_chart_design_default("valueFontColor");
  }
  $valueFontColor = "valueFontColor='$valueFontColor'";

  $formatNumberScale = reports_get_style($graphDesign, "formatNumberScale");
  if (length($formatNumberScale) < 1)
  {
    $formatNumberScale = reports_chart_design_default("formatNumberScale");
  }
  $formatNumberScale = "formatNumberScale='$formatNumberScale'";

  $showPercentValues = reports_get_style($graphDesign, "showPercentValues");
  if (length($showPercentValues) < 1)
  {
    $showPercentValues = reports_chart_design_default("showPercentValues");
  }
  $showPercentValues = "showPercentValues='$showPercentValues'";

  $decimals = reports_get_style($graphDesign, "decimals");
  if (length($decimals) > 0)
  {
    $decimals = "decimals='$decimals'";
  }
  else
  {
    $decimals = "";
  }

  $placeValuesInside = reports_get_style($graphDesign, "placeValuesInside");
  if (length($placeValuesInside) < 1)
  {
    $placeValuesInside = reports_chart_design_default("placeValuesInside");
  }
  $placeValuesInside = "placeValuesInside='$placeValuesInside'";

  $rotateValues = reports_get_style($graphDesign, "rotateValues");
  if (length($rotateValues) < 1)
  {
    $rotateValues = reports_chart_design_default("rotateValues");
  }
  $rotateValues = "rotateValues='$rotateValues'";

  $valueFontSize = reports_get_style($graphDesign, "valueFontSize");
  if (length($valueFontSize) < 1)
  {
    $valueFontSize = reports_chart_design_default("valueFontSize");
  }
  $valueFontSize = "valueFontSize='$valueFontSize'";

  $valueFont = reports_get_style($graphDesign, "valueFont");
  if (length($valueFont) > 3)
  {
    $valueFont = "valueFont='$valueFont'";
  }
  $showValuesBg = reports_get_style($graphDesign, "showValuesBg");
  if ($showValuesBg > 0)
  {
    $valueBgColor = reports_get_style($graphDesign, "valueBgColor");
    $valueBgColor = "valueBgColor='$valueBgColor'";
    $valueBgAlpha = reports_get_style($graphDesign, "valueBgAlpha");
    $valueBgAlpha = 100 - $valueBgAlpha;
    $valueBgAlpha = "valueBgAlpha='$valueBgAlpha'";
  }

  #if we're a funnel chart, we want to be 2D by default
  $is2D = "";
  if ($graphType eq "Funnel")
  {
    $is2D = "is2D='1'";
  }

  #if we're a funnel chart, force slant angle to be the same
  $useSameSlantAngle = "";
  if ($graphType eq "Funnel")
  {
    $useSameSlantAngle = "useSameSlantAngle='1'";
  }

  #see if we're being asked to generate an "All Others" slice
  $allOthers = reports_get_style($graphDesign, "allOthers");

  #if the titles have expandable tag(s), expand them
  $caption = reports_expand_dim_tags($db, $dsSchema, $caption, $productIDstring, $geographyIDstring, $timeIDstring, $measureIDstring);
  $subcaption = reports_expand_dim_tags($db, $dsSchema, $subcaption, $productIDstring, $geographyIDstring, $timeIDstring, $measureIDstring);

  #escape "special" characters
  $caption =~ s/\'//g;
  $subcaption =~ s/\'//g;

  #if we're a waterfall, pull our specific settings for total columns
  $showSumAtEnd = "";
  if ($graphType eq "Waterfall")
  {
    $showSumAtEnd = reports_get_style($graphDesign, "showSumAtEnd");
    if (length($showSumAtEnd) < 1)
    {
      $showSumAtEnd = reports_chart_design_default("showSumAtEnd");
    }
    $showSumAtEnd = "showSumAtEnd='$showSumAtEnd'";

    $cumeSumValue = reports_get_style($graphDesign, "cumeSumValue");
  }

  # ------- end of design parsing ----------


  #get our measure format info, and apply as much of it as we can
  $formatStr = DSRmeasures_get_format($db, $dsSchema, $measureID);
  @formats = split(',', $formatStr);
  if (length($decimals < 1))
  {
    $decimals = "decimals='$formats[0]'"
  }

  $formatNumber = "formatNumber='$formats[1]'";
  $numberPrefix = "";
  $numberSuffix = "";
  if ($formats[2] == 1)
  {
    $numberPrefix = "numberPrefix='\$'";
  }
  elsif ($formats[2] == 2)
  {
    $numberSuffix = "numberSuffix='%'";
  }

  #print out the XML chart info
  print <<XML_LABEL;
<chart
  theme='zune'
  animation='0'

  $bgColor
  $canvasBgColor
  canvasBgAlpha='0'

  $showBorder
  $borderColor
  $borderThickness

  $showLegend
  $legendPosition
  $legendItemFontColor
  $legendItemFont
  $legendItemFontSize

  caption='$caption'
  $captionFontColor
  $captionAlignment
  $captionFontSize
  $captionFont

  subcaption='$subcaption'

  xAxisName='$xAxisName'
  $xAxisNameFontSize
  yAxisName='$yAxisName'
  $yAxisNameFontSize
  useRoundEdges='$useRoundEdges'

  $showLabels
  $labelFontColor
  $labelDisplay
  $labelStep
  $labelFontSize
  $labelFont

  $showValues
  $valueFontColor
  $formatNumberScale
  $showPercentValues
  $decimals
  $placeValuesInside
  $rotateValues
  $valueFontSize
  $valueFont
  $valueBgColor
  $valueBgAlpha

  $formatNumber
  $numberPrefix
  $numberSuffix

  $showSumAtEnd

  use3dlighting='0'
  $is2D
  $useSameSlantAngle>
XML_LABEL

  #if our dimension series is anything other than measure, set the measure
  #NB: If our series is measures, we'll set them below and overwrite this
  $measureCol = "measure_" . $measureID;

  #don't display negative values in pie/donuts
  if (($graphType eq "2DPie") || ($graphType eq "3DPie") ||
      ($graphType eq "2DDonut") || ($graphType eq "3DDonut"))
  {
    $whereClause = "AND $measureCol >= 0";
  }

  #if the user has specified a slicer, let's add it to the where clause
  if ($graph_x eq "p")
  {
    @slicers = split(',', $slicersStr);
    foreach $slicer (@slicers)
    {
      $slicer =~ m/(PSEG_\d+):(\d+)/;
      $segID = $1;
      $segmentID = $2;

      if ($segmentID > 0)
      {
        $tmp = "SMT_$segmentID";
        $segmentName = $db->quote($setNames{$tmp});
        $whereClause .= " AND $segID = $segmentName";
      }
    }
  }

  ### chart filtering implementation ###

  $filterMeas1 = reports_get_style($graphDesign, "filterMeas1");
  $filterOp1 = reports_get_style($graphDesign, "filterOp1");
  $filterNum1 = reports_get_style($graphDesign, "filterNum1");

  if (($filterMeas1 > 0) && (length($filterNum1) > 0))
  {
    $filterMeas1 = "measure_" . $filterMeas1;

    if ($filterOp1 eq "gt")
    {
      $filterOp1 = ">";
      $whereClause .= " AND $filterMeas1 > $filterNum1";
    }
    elsif ($filterOp1 eq "lt")
    {
      $filterOp1 = "<";
      $whereClause .= " AND $filterMeas1 < $filterNum1";
    }
    elsif ($filterOp1 eq "eq")
    {
      $filterOp1 = "=";
      $whereClause .= " AND $filterMeas1 = $filterNum1";
    }

    elsif ($filterOp1 eq "top")
    {

      #gate the number of top items we're going to show
      if ($filterNum1 > 5000)
      {
        $filterNum1 = 5000;
      }
      elsif ($filterNum1 < 0)
      {
        $filterNum1 = 1;
      }

      $whereClause .= " ORDER BY $filterMeas1 DESC LIMIT $filterNum1";
    }

    elsif ($filterOp1 eq "bottom")
    {

      #gate the number of top items we're going to show
      if ($filterNum1 > 5000)
      {
        $filterNum1 = 5000;
      }
      elsif ($filterNum1 < 0)
      {
        $filterNum1 = 1;
      }

      $whereClause .= " ORDER BY $filterMeas1 ASC LIMIT $filterNum1";
    }
  }

  ### end chart filtering code ###

  #set up our item/value query based on the display dimension
  if ($graph_x eq "p")
  {
    $query = "SELECT product, $measureCol FROM $dsSchema.$rptCube \
        WHERE product IN ($itemStr) AND geography=$q_geography AND time=$q_time $whereClause";
  }
  elsif ($graph_x eq "g")
  {
    $query = "SELECT geography, $measureCol FROM $dsSchema.$rptCube \
        WHERE product=$q_product AND geography IN ($itemStr) AND time=$q_time $whereClause";
  }
  elsif ($graph_x eq "t")
  {
    $query = "SELECT time, $measureCol FROM $dsSchema.$rptCube \
        WHERE product=$q_product AND geography=$q_geography AND time IN ($itemStr) $whereClause";
  }
  elsif ($graph_x eq "m")
  {
    $query = "SELECT $itemStr FROM $dsSchema.$rptCube \
        WHERE product=$q_product AND geography=$q_geography AND time=$q_time";
  }

  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  #if we're graphing measures, pull the values out of the single result string
  if ($graph_x eq "m")
  {
    @vals = $dbOutput->fetchrow_array;
    $idx = 0;
    foreach $val (@vals)
    {
      $results{$setIDs[$idx]} = $val;
      $idx++;
    }
  }

  #else cycle through the results, turning them into a hash keyed by item ID
  #NB: we're also limiting the setID array to items returned by the filtering
  else
  {
    undef(@setIDs);
    while (($itemID, $value) = $dbOutput->fetchrow_array)
    {
      push(@setIDs, $itemID);
      $results{$itemID} = $value;
    }
  }

  #get default color scheme
  @colors = reports_graph_color_array;

  #if there isn't any data worth displaying, put out a dummy value
  if (scalar(%results) < 1)
  {
    print(" <set label='(No Data)' value='100' color='$colors[0]'/>\n");
  }

  #cycle through the results, outputting each as a slice in the tree map
  $colorIdx = 0;
  foreach $itemID (@setIDs)
  {
    $color = $colors[$colorIdx];

    #see if we have a custom color, and use it in preference to the default
    $name = "color_" . $itemID;
    $customColor = reports_get_style($graphDesign, $name);
    if (length($customColor) > 6)
    {
      $color = $customColor;
    }

    #if the item is supposed to be a custom cumulative for a waterfall
    $cumulative = "";
    if ($itemID eq $cumeSumValue)
    {
      $cumulative = "isSum='1'";
    }

    print(" <set label='$setNames{$itemID}' value='$results{$itemID}' color='$color' $cumulative />\n");

    $colorIdx++;
    if ($colorIdx > 47)
    {
      $colorIdx = 0;
    }
  }

  #finally, generate an "All Others" slice if requested by user
  if ($allOthers eq "true")
  {

    #build list of all items already displayed
    $displayed = "";
    foreach $itemID (keys %results)
    {
      $displayed .= "'$itemID',";
    }
    chop($displayed);

    $selStr = "";
    foreach $itemID (@origSetIDs)
    {
      $selStr .= "'$itemID',";
    }
    chop($selStr);

    if ($graph_x eq "p")
    {
      $query = "SELECT SUM($measureCol) FROM $dsSchema.$rptCube \
          WHERE $measureCol > 0 AND product IN ($selStr) AND product NOT IN ($displayed) AND geography=$q_geography AND time=$q_time";
    }
    elsif ($graph_x eq "g")
    {
      $query = "SELECT SUM($measureCol) FROM $dsSchema.$rptCube \
          WHERE $measureCol > 0 AND product = $q_product AND geography IN ($selStr) AND geography NOT IN ($displayed) AND time=$q_time";
    }
    elsif ($graph_x eq "t")
    {
      $query = "SELECT SUM($measureCol) FROM $dsSchema.$rptCube \
          WHERE $measureCol > 0 AND product=$q_product AND geography=$q_geography AND time IN ($selStr) AND time NOT IN ($displayed)";
    }
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($total) = $dbOutput->fetchrow_array;

    $color = $colors[$colorIdx];
    print(" <set label='All Others' value='$total' color='$color' />\n");
  }

  print("</chart>\n");


#EOF
