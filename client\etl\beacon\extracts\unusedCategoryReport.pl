#!/usr/bin/perl

###########################################################################
#
# This script outputs a list of categories that aren't being used by any
# Data Prep data flow.
#
#########################################################################

use lib "/opt/apache/app/";

use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::PrepUtils;
use Lib::WebUtils;



  #get a list of the categories available in this IDW
  opendir(DIRHANDLE, "/data2/beacon");
  while (defined($filename = readdir(DIRHANDLE)))
  {
    if ($filename =~ m/^(.*)\.zip$/i)
    {
      $catName = $1;
      if ($catName =~ m/^(.*)-update$/)
      {
        next;
      }

      $catName = uc($catName);

      $catName =~ tr/_/ /;

      #XXX use unzip -l to get uncompressed data size and save in hash
      $size = `unzip -Zt /data2/beacon/$filename`;
      if ($size =~ m/^.* (\d+) bytes uncompressed.*$/)
      {
        $size = $1;
        $size = $size / 1024; #KB
        $size = $size / 1024; #MB
        $size = sprintf("%.1f", $size);
        $IDWcats{$catName} = $size;
      }
      else
      {
        $IDWcats{$catName} = 1;
      }
    }
  }

  $prepDB = PrepUtils_connect_to_database();

  #grab every data flow that uses this IDW as a source
  $query = "SELECT sourceInfo FROM prep.flows WHERE sourceInfo LIKE 'FTP=nielsen%'";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;

  #run through the categories in use and hash them, keeping count
  while (($sourceInfo) = $dbOutput->fetchrow_array)
  {
    $sourceInfo =~ m/^.*PATH=(.*).zip$/;
    $prepCat = $1;

    if ($prepCat =~ m/^(.*)-update$/)
    {
      $prepCat = $1;
    }

    $prepCat = uc($prepCat);

    $prepCat =~ tr/_/ /;

    $prepCats{$prepCat}++;
  }

  #output each used category with its usage count
  foreach $prepCat (sort keys %prepCats)
  {
    print("$prepCat,$prepCats{$prepCat},$IDWcats{$prepCat}\n");
  }

  #output each unused category
  foreach $IDWcat (sort keys %IDWcats)
  {
    if ($prepCats{$IDWcat} < 1)
    {
      print("$IDWcat,0,$IDWcats{$IDWcat}\n");
    }
  }


#EOF
