#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::BuildCube;
use Lib::Cubes;
use Lib::DataSel;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::Reports;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<DOCCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: PowerPoint Export</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
let statusTimer = setInterval(function(){displayStatus()}, 5000);

function displayStatus()
{
  const url = "/app/rpt/xhrPPTexportStatus.cld?d=$dsID";

  \$.get(url, function(data, status)
  {
    let statusText = data;

    if (statusText.length < 2)
    {
      statusText = "Exporting reports to PowerPoint";
    }

    if (statusText.length == 5)  //DONE\n
    {
      \$('#progress-bar-container').hide();
      \$('#progressDiv').hide();
      document.getElementById('download-btn').style.visibility = 'visible';
      document.getElementById('ok-btn').style.visibility = 'visible';
      clearInterval(statusTimer);
    }
    else
    {
      document.getElementById('progressDiv').innerHTML = statusText;
    }
  });
}
</SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/rpt/main">Reports</A></LI>
    <LI CLASS="breadcrumb-item active">Export Reports to PowerPoint Deck</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------
#
# Output the HTML for the web page that displays the status dialog
#

sub print_status_html
{

  #build up the temp filenames
  $tmpBase = $last . $userID;
  $tmpBase =~ s/\s+//g;
  $tmpBase = $tmpBase . "_" . $dsID;
  $filename = "$tmpBase.pptx";

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-11 col-md-8 col-lg-6 col-xl-5"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Exporting PowerPoint Presentation</DIV>
        <DIV CLASS="card-body">

          <P>
          <DIV CLASS="progress" ID="progress-bar-container">
            <DIV ID="progress-bar" CLASS="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" STYLE="width:100%;">
            </DIV>
          </DIV>

          <P>
          <DIV CLASS="text-center">
            <DIV ID="progressDiv">Exporting reports to PowerPoint</DIV>
          </DIV>

          <P>&nbsp;</P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-success" TYPE="button" ID="download-btn" STYLE="visibility:hidden;" onClick="location.href='/tmp/$filename'"><I CLASS="bi bi-download"></I> Download PowerPoint</BUTTON>
            <P>&nbsp;</P>
            <BUTTON CLASS="btn btn-primary" TYPE="button" ID="ok-btn" STYLE="visibility:hidden;" onClick="location.href='/app/rpt/main'"><I CLASS="bi bi-check-lg"></I> Done</BUTTON>
          </DIV>

        </DIV>
      </DIV>
    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get the CGI input variables
  $dsID = $q->param('ds');

  #build up hash of reports the user wants us to export
  @names = $q->param;
  foreach $name (@names)
  {
    if ($name =~ m/^R (\d+)/)
    {
      $val = $q->param($name);
      if ($val eq "on")
      {
        $exportRpts{$1} = 1;
      }
    }
  }

  $db = KAPutil_connect_to_database();

  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  #fork a new process to do the actual PPT export in the background
  $SIG{CHLD} = "IGNORE";
  if ($pid = fork())
  {
    #parent process

    print_status_html();

    $activity = "$first $last exporting reports in $dsName to PowerPoint";
    utils_slack($activity);
  }

  else
  {
    #child process

    #let Apache know not to wait on the child process
    close(STDIN);
    close(STDOUT);

    #redirect STDERR to the Koala error log
    close(STDERR);
    open(STDERR, ">>/opt/apache/htdocs/tmp/koala_error.log");
    select(STDERR);
    $| = 1;

    #reconnect to the database
    $db = KAPutil_connect_to_database();

    #set our initial state in the jobs table
    KAPutil_job_store_status($db, $userID, $dsID, 0, "REPORT-EXPORT", "Exporting reports to PowerPoint deck");

    #
    # Export PowerPoint Deck
    #

    #build up the temp filenames
    $tmpBase = $last . $userID;
    $tmpBase =~ s/\s+//g;
    $tmpBase = $tmpBase . "_" . $dsID;
    $filename = "$tmpBase.pptx";
    $scriptName = "$tmpBase.js";
    $imgName = "$tmpBase.png";

    #make a copy of the template PPT file to the tmp directory
    system("cp deck_template.pptx /opt/apache/htdocs/tmp/$filename");

    #change our working directory to the platform web-accessible tmp
    chdir("/opt/apache/htdocs/tmp");

    #unzip the PPTX file (OpenDocument XML format expected)
    system("unzip -qq /opt/apache/htdocs/tmp/$filename -d /opt/apache/htdocs/tmp/$tmpBase");

    #delete the template file
    system("rm $filename");

    #get the list of reports the user is allowed to view in this data source
    %reports = cube_list($db, $userID, $acctType, $dsID);

    #pull any reports the user doesn't want to have exported
    foreach $rptID (keys %reports)
    {
      if ($exportRpts{$rptID} != 1)
      {
        delete($reports{$rptID});
      }
    }

    $slideCount = keys %exportRpts;

    $idx = 1;
    foreach $exportRptID (sort {$reports{$a} cmp $reports{$b}} keys %reports)
    {
      $query = "UPDATE jobs SET status='Creating slide $idx of $slideCount' WHERE PID=$$";
      $status = $db->do($query);
      KAPutil_handle_db_err($db, $status, $query);

      #get the screenshot of the report
      system("/opt/google/chrome/google-chrome --headless --run-all-compositor-stages-before-draw --virtual-time-budget=5000 --screenshot=/opt/apache/htdocs/tmp/$imgName --window-size=1280,720 --user-data-dir=/opt/apache/htdocs/tmp/ \"http://$Lib::KoalaConfig::hostname/app/rpt/display.cld?rpt=$exportRptID&c=1&l=1&u=$userID\"");

      #copy the screenshot of the report into the PPTX structure
      $imageName = "image" . $idx . ".png";
      system("cp $imgName $tmpBase/ppt/media/$imageName");

      #create a copy of the standard slide definition XML file for each slide
      $XMLname = "slide" . $idx . ".xml";
      system("cp $tmpBase/ppt/slides/slide1.xml $tmpBase/ppt/slides/$XMLname");

      #create the slide relationship XML file
      $XMLname = "slide" .  $idx . ".xml.rels";
      $replaceStr = "image" . $idx;
      open(INPUT, "$tmpBase/ppt/slides/_rels/template");
      open(OUTPUT, ">$tmpBase/ppt/slides/_rels/$XMLname");
      while ($line = <INPUT>)
      {
        if ($line =~ m/(.*)KOALA(.*)/)
        {
          $line = $1 . $replaceStr . $2;
        }
        print OUTPUT $line;
      }
      close(INPUT);
      close(OUTPUT);

      $idx++;
    }

    $query = "UPDATE jobs SET status='Assembling slide deck' WHERE PID=$$";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    #update the master presentation relationship file
    open(INPUT, "$tmpBase/ppt/_rels/template");
    open(OUTPUT, ">$tmpBase/ppt/_rels/presentation.xml.rels");
    while ($line = <INPUT>)
    {
      if ($line =~ m/KOALA/)
      {
        $idx = 1;
        foreach $exportRptID (keys %reports)
        {
          $rId = $idx + 8;
          $rId = "rId" . $rId;
          $replStr = "slide" . $idx . ".xml";
          print OUTPUT "<Relationship Id=\"$rId\" Type=\"http://schemas.openxmlformats.org/officeDocument/2006/relationships/slide\" Target=\"slides/$replStr\"/>\n";
          $idx++;
        }
      }
      else
      {
        print OUTPUT $line;
      }
    }

    #update the master presentation file
    open(INPUT, "$tmpBase/ppt/template");
    open(OUTPUT, ">$tmpBase/ppt/presentation.xml");
    while ($line = <INPUT>)
    {
      if ($line =~ m/KOALA/)
      {
        $idx = 1;
        foreach $exportRptID (keys %reports)
        {
          $rId = $idx + 8;
          $rId = "rId" . $rId;
          $id = $idx + 255;
          print OUTPUT "<p:sldId id=\"$id\" r:id=\"$rId\"/>";
          $idx++;
        }
      }
      else
      {
        print OUTPUT $line;
      }
    }

    #update the master Content Types file
    open(INPUT, "$tmpBase/template");
    open(OUTPUT, ">$tmpBase/[Content_Types].xml");
    while ($line = <INPUT>)
    {
      if ($line =~ m/KOALA/)
      {
        $idx = 1;
        foreach $exportRptID (keys %reports)
        {
          $replStr = "slide" . $idx . ".xml";
          print OUTPUT "<Override PartName=\"/ppt/slides/$replStr\" ContentType=\"application/vnd.openxmlformats-officedocument.presentationml.slide+xml\"/>";
          $idx++;
        }
      }
      else
      {
        print OUTPUT $line;
      }
    }

    #remove our temporary template files so PowerPoint doesn't throw a fit
    system("rm $tmpBase/ppt/slides/_rels/template");
    system("rm $tmpBase/ppt/_rels/template");
    system("rm $tmpBase/ppt/template");
    system("rm $tmpBase/template");

    #re-zip the updated PPTX structure into the PPTX file
    chdir("$tmpBase");
    system("zip -qr ../$filename *");
    chdir("..");

    #delete the screenshot image, and PPTX structure
    system("rm -rf $tmpBase");
    system("rm $imgName");

    #remove this task from the jobs table
    DSRutil_clear_status($db);

    $activity = "$first $last exported expanded static PowerPoint report for $cubeName in $dsName";
    utils_audit($db, $userID, "Exported as expanded static PowerPoint report", $dsID, 0, 0);
    utils_slack($activity);
  }

#EOF
