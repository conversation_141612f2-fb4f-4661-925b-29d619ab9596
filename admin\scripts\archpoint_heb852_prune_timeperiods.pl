#!/usr/bin/perl

# Prune all time periods from the selected data source that don't end on a
# Sunday.

use lib "/opt/apache/app/";

use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsID = 5349;
  $dsSchema = "datasource_" . $dsID;

  #grab all of the time periods in the data source
  $query = "SELECT ID, name, endDate, WEEKDAY(endDate)
      FROM $dsSchema.timeperiods 
      ORDER BY endDate";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  while (($timeID, $name, $endDate, $dayCode) = $dbOutput->fetchrow_array)
  {
    print("$timeID $endDate $dayCode\n");

    if ($dayCode != 6)
    {
      $query = "DELETE FROM $dsSchema.timeperiods WHERE ID=$timeID";
      print STDERR "$query\n";
      $db->do($query);
      next;
    }

    if ($name =~ m/^1 day ending (.*)$/)
    {
      $newName = "1 week ending $1";
      $q_newName = $db->quote($newName);
      $query = "UPDATE $dsSchema.timeperiods SET name=$q_newName, type=30
        WHERE ID=$timeID";
      $db->do($query);
      print("$query\n$name -> $newName\n");
    }
  }

