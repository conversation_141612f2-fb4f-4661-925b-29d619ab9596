#!/usr/bin/perl

use lib "/opt/apache/app/";

use DBI;

use Lib::KoalaConfig;
use Lib::BuildCube;
use Lib::DataSources;
use Lib::DSRMeasures;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

  #connect to the master database
  $db = KAPutil_connect_to_database();

  %dsHash = ds_get_name_hash($db);

  #data source consistency checking
  foreach $dsID (sort keys %dsHash)
  {
    $modified = 0;

    print("\n\n===========================================================\n");
    print("$dsHash{$dsID}\n");

    $dsSchema = "datasource_$dsID";

    #if the data source is currently in use, skip it
    $query = "SELECT PID FROM app.jobs WHERE dsID=$dsID AND operation NOT IN ('CUBE-UPDATE', 'ODBC')";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($pid) = $dbOutput->fetchrow_array;
    if ($pid > 0)
    {
      print("!!! Data source in use, skipping\n");
      next;
    }

    #make sure the data source really exists
    print("Verifying data source really exists\n");
    $query = "SELECT COUNT(table_name) FROM information_schema.tables \
        WHERE table_schema='$dsSchema'";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($tableCount) = $dbOutput->fetchrow_array;
    if ($tableCount < 1)
    {
      print("***Data source doesn't exist, removing entry\n");
      $query = "DELETE from dataSources WHERE ID=$dsID";
      $db->do($query);
      next;
    }

    %prodNameHash = dsr_get_item_name_hash($db, $dsSchema, "p", 0);
    %prodBaseHash = dsr_get_base_item_name_hash($db, $dsSchema, "p");
    %geoNameHash = dsr_get_item_name_hash($db, $dsSchema, "g", 0);
    %geoBaseHash = dsr_get_base_item_name_hash($db, $dsSchema, "g");
    %timeNameHash = dsr_get_item_name_hash($db, $dsSchema, "t", 0);
    %timeBaseHash = dsr_get_base_item_name_hash($db, $dsSchema, "t");
    %measureNameHash = dsr_get_item_name_hash($db, $dsSchema, "m", 0);


    #########################################################

    #remove products that have no name
    print("Checking for nameless products\n");
    foreach $prodID (keys %prodNameHash)
    {
      if (length($prodNameHash{$prodID}) < 1)
      {
        print("***Deleting nameless product $prodID\n");
        $modified = 1;
        $query = "DELETE FROM $dsSchema.products WHERE ID=$prodID";
        $db->do($query);
        $query = "DELETE FROM $dsSchema.facts WHERE productID=$prodID";
        $db->do($query);
      }
    }

    #remove geographies that have no name
    print("Checking for nameless geographies\n");
    foreach $geoID (keys %geoNameHash)
    {
      if (length($geoNameHash{$geoID}) < 1)
      {
        print("***Deleting nameless geography $geoID\n");
        $modified = 1;
        $query = "DELETE FROM $dsSchema.geographies WHERE ID=$geoID";
        $db->do($query);
        $query = "DELETE FROM $dsSchema.facts WHERE geographyID=$geoID";
        $db->do($query);
      }
    }

    #remove time periods that have no name
    print("Checking for nameless time periods\n");
    foreach $timeID (keys %timeNameHash)
    {
      if (length($timeNameHash{$timeID}) < 1)
      {
        print("***Deleting nameless time period $timeID\n");
        $modified = 1;
        $query = "DELETE FROM $dsSchema.timeperiods WHERE ID=$timeID";
        $db->do($query);
        $query = "DELETE FROM $dsSchema.facts WHERE timeID=$timeID";
        $db->do($query);
      }
    }

    #delete products with no measure data
    print("Checking for data-less products\n");
    foreach $prodID (keys %prodBaseHash)
    {
      $query = "SELECT COUNT(*) FROM $dsSchema.facts WHERE productID=$prodID";
      $dbOutput = $db->prepare($query);
      $dbOutput->execute;
      ($count) = $dbOutput->fetchrow_array;

      if ($count < 1)
      {
        print("***Deleting data-less product $prodBaseHash{$prodID}\n");
        $modified = 1;
        $query = "DELETE FROM $dsSchema.products WHERE ID=$prodID";
        $db->do($query);
      }
    }

    #delete geographies with no measure data
    print("Checking for data-less geographies\n");
    foreach $geoID (keys %geoBaseHash)
    {
      $query = "SELECT COUNT(*) FROM $dsSchema.facts WHERE geographyID=$geoID";
      $dbOutput = $db->prepare($query);
      $dbOutput->execute;
      ($count) = $dbOutput->fetchrow_array;

      if ($count < 1)
      {
        print("***Deleting data-less geography $geoBaseHash{$geoID}\n");
        $modified = 1;
        $query = "DELETE FROM $dsSchema.geographies WHERE ID=$geoID";
        $db->do($query);
      }
    }

    #delete time periods with no measure data
    print("Checking for data-less time periods\n");
    foreach $timeID (keys %timeBaseHash)
    {
      $query = "SELECT COUNT(*) FROM $dsSchema.facts WHERE timeID=$timeID";
      $dbOutput = $db->prepare($query);
      $dbOutput->execute;
      ($count) = $dbOutput->fetchrow_array;

      if ($count < 1)
      {
        print("***Deleting data-less time period $timeBaseHash{$timeID}\n");
        $modified = 1;
        $query = "DELETE FROM $dsSchema.timeperiods WHERE ID=$timeID";
        $db->do($query);
      }
    }

    %prodNameHash = dsr_get_item_name_hash($db, $dsSchema, "p", 0);
    %prodBaseHash = dsr_get_base_item_name_hash($db, $dsSchema, "p");
    %geoNameHash = dsr_get_item_name_hash($db, $dsSchema, "g", 0);
    %geoBaseHash = dsr_get_base_item_name_hash($db, $dsSchema, "g");
    %timeNameHash = dsr_get_item_name_hash($db, $dsSchema, "t", 0);
    %timeBaseHash = dsr_get_base_item_name_hash($db, $dsSchema, "t");
    %measureNameHash = dsr_get_item_name_hash($db, $dsSchema, "m", 0);


    #########################################

    #remove any products from the facts table that have been deleted
    print("Checking for deleted products in facts table\n");
    $query = "SELECT DISTINCT productID FROM $dsSchema.facts";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;

    while (($prodID) = $dbOutput->fetchrow_array)
    {
      if (!defined($prodNameHash{$prodID}))
      {
        print("***Removing deleted product $prodID from facts\n");
        $modified = 1;
        $query = "DELETE FROM $dsSchema.facts WHERE productID=$prodID";
        $db->do($query);
      }
    }

    #remove any geographies from the facts table that have been deleted
    print("Checking for deleted geographies in facts table\n");
    $query = "SELECT DISTINCT geographyID FROM $dsSchema.facts";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;

    while (($geoID) = $dbOutput->fetchrow_array)
    {
      if (!defined($geoNameHash{$geoID}))
      {
        print("***Removing deleted geography $geoID from facts\n");
        $modified = 1;
        $query = "DELETE FROM $dsSchema.facts WHERE geographyID=$geoID";
        $db->do($query);
      }
    }

    #remove any time periods from the facts table that have been deleted
    print("Checking for deleted time periods in facts table\n");
    $query = "SELECT DISTINCT timeID FROM $dsSchema.facts";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;

    while (($timeID) = $dbOutput->fetchrow_array)
    {
      if (!defined($timeNameHash{$timeID}))
      {
        print("***Removing deleted time period $timeID from facts\n");
        $modified = 1;
        $query = "DELETE FROM $dsSchema.facts WHERE timeID=$timeID";
        $db->do($query);
      }
    }


    #############################################

    print("Checking for deleted products in attributes\n");
    $query = "SELECT DISTINCT itemID FROM $dsSchema.product_attribute_values";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;

    while (($itemID) = $dbOutput->fetchrow_array)
    {

      #if the item in the segmentation doesn't exist in the DS
      if (!defined($prodNameHash{$itemID}))
      {
        $query = "DELETE FROM $dsSchema.product_attribute_values \
            WHERE itemID=$itemID";
        $db->do($query);
        print("***Removing deleted product $itemID from product attribute\n");
      }
    }


    #############################################

    #remove deleted products from segmentations they might still be assigned to
    print("Checking for deleted products in segmentations\n");
    $query = "SELECT DISTINCT itemID FROM $dsSchema.product_segment_item";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;

    while (($itemID) = $dbOutput->fetchrow_array)
    {

      #if the item in the segmentation doesn't exist in the DS
      if (!defined($prodNameHash{$itemID}))
      {
        $query = "DELETE FROM $dsSchema.product_segment_item WHERE itemID=$itemID";
        $db->do($query);
        print("***Removing deleted product $itemID from segmentations\n");
      }
    }

    #remove deleted geos from segmentations they might still be assigned to
    print("Checking for deleted geographies in segmentations\n");
    $query = "SELECT DISTINCT itemID FROM $dsSchema.geography_segment_item";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;

    while (($itemID) = $dbOutput->fetchrow_array)
    {

      #if the item in the segmentation doesn't exist in the DS
      if (!defined($geoNameHash{$itemID}))
      {
        $query = "DELETE FROM $dsSchema.geography_segment_item WHERE itemID=$itemID";
        $db->do($query);
        print("***Removing deleted geography $itemID from segmentations\n");
      }
    }

    #remove deleted times from segmentations they might still be assigned to
    print("Checking for deleted time periods in segmentations\n");
    $query = "SELECT DISTINCT itemID FROM $dsSchema.time_segment_item";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;

    while (($itemID) = $dbOutput->fetchrow_array)
    {

      #if the item in the segmentation doesn't exist in the DS
      if (!defined($timeNameHash{$itemID}))
      {
        $query = "DELETE FROM $dsSchema.time_segment_item WHERE itemID=$itemID";
        $db->do($query);
        print("***Removing deleted time period $itemID from segmentations\n");
      }
    }

    #########################################


    #delete empty segments code
=pod
    #build up a hash of all used product segments
    print("Checking for empty segments\n");
    undef(%usedSegmentHash);
    $query = "SELECT DISTINCT segmentID FROM $dsSchema.product_segment_item";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    while (($segmentID) = $dbOutput->fetchrow_array)
    {
      $usedSegmentHash{$segmentID} = 1;
    }

    #get all segments in the DS, delete any that aren't used
    $query = "SELECT ID FROM $dsSchema.product_segment";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    while (($segmentID) = $dbOutput->fetchrow_array)
    {
      if ($usedSegmentHash{$segmentID} ne 1)
      {
        $fqSegmentID = "SMT_$segmentID";
        print("***Removing empty segment $prodNameHash{$fqSegmentID}\n");
        $query = "DELETE FROM $dsSchema.product_segment WHERE ID=$segmentID";
        $db->do($query);
      }
    }
=cut


    #########################################

    #delete empty segmentations
    print("Checking for empty segmentations\n");
    %segHash = DSRsegmentation_get_segmentations_hash($db, $dsSchema, "p");
    foreach $segID (keys %segHash)
    {
      $query = "SELECT COUNT(itemID) FROM $dsSchema.product_segment_item \
          WHERE segmentationID = $segID";
      $dbOutput = $db->prepare($query);
      $dbOutput->execute;
      ($count) = $dbOutput->fetchrow_array;

      if ($count < 1)
      {
        $fqSegID = "SEG_$segID";
        print("***Removing empty segmentation $prodNameHash{$fqSegID}\n");
        $query = "DELETE FROM $dsSchema.product_segment WHERE segmentationID=$segID";
        $db->do($query);
        $query = "DELETE FROM $dsSchema.product_segmentation WHERE ID=$segID";
        $db->do($query);
      }
    }


    #########################################

    #delete invalid segmentation hierarchies
    print("Checking for invalid hierarchies\n");
    %itemHash = dsr_get_item_name_hash($db, $dsSchema, "p");
    $query = "SELECT ID, segmentations FROM $dsSchema.product_seghierarchy";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;

    while (($hierID, $segs) = $dbOutput->fetchrow_array)
    {
      @segIDs = split(',', $segs);
      $valid = 1;
      foreach $segID (@segIDs)
      {
        $segID = "SEG_" . $segID;
        $name = $itemHash{$segID};
        if (length($name) < 1)
        {
          $valid = 0;
        }
      }

      if ($valid == 0)
      {
        $fqHierID = "SHS_$hierID";
        print("***Removing invalid hierarchy $prodNameHash{$fqHierID}\n");
        $query = "DELETE FROM $dsSchema.product_seghierarchy WHERE ID=$hierID";
        $db->do($query);
      }
    }


    #########################################

    #remove any measure columns in facts table that have been deleted
    print("Checking for stale measure columns in facts table\n");
    $query = "SELECT column_name FROM information_schema.columns \
        WHERE table_schema='$dsSchema' AND table_name='facts'";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;

    while (($columnName) = $dbOutput->fetchrow_array)
    {
      if ($columnName =~ m/^measure_(\d+)$/)
      {
        $measureID = $1;

        if (!defined($measureNameHash{$measureID}))
        {
          print("***Dropping unused measure column $columnName");
          $modified = 1;
          $query = "ALTER TABLE $dsSchema.facts DROP COLUMN $columnName";
          $db->do($query);
        }
      }
    }


    #############################################

    #look for any missing measure columns and re-create them
    print("Checking for missing measure columns in facts\n");
    foreach $measureID (keys %measureNameHash)
    {

      #only do financial measures (strictly numerical IDs)
      if (!($measureID =~ m/^\d+$/))
      {
        next;
      }

      $colName = "measure_$measureID";

      $query = "SELECT column_name FROM information_schema.columns \
          WHERE table_schema='$dsSchema' AND table_name='facts' AND column_name='$colName'";
      $dbOutput = $db->prepare($query);
      $status = $dbOutput->execute;

      if ($status < 1)
      {
        print("***Adding missing measure column $measureNameHash{$measureID}\n");
        $modified = 1;
        $query = "ALTER TABLE $dsSchema.facts ADD COLUMN $colName DOUBLE";
        $db->do($query);

        print("***Recomputing all measures (going to take a while)\n");
        DSRmeasures_recalculate_all_measures($db, $dsSchema);
      }
    }

    #if we did anything that modified the data source, mark it
    if ($modified > 0)
    {
      $query = "UPDATE dataSources SET lastModified=NOW() WHERE ID=$dsID";
      $db->do($query);
    }
  }


  #-----------------------------------------------------------------------------

  #yank every data cube that doesn't have a matching data source
  $query = "SELECT ID, dsID FROM app.cubes";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;

  while (($cubeID, $dsID) = $dbOutput->fetchrow_array)
  {
    if (!defined($dsHash{$dsID}))
    {
      $query = "DELETE FROM app.cubes WHERE ID=$cubeID";
      $db->do($query);
    }
  }


#EOF
