#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($kapDB);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Data Prep Mapping</TITLE>
<LINK REL="SHORTCUT ICON" HREF="$Lib::KoalaConfig::kapHostURL/favicon.png">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($kapDB, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="$Lib::KoalaConfig::kapHostURL/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">Data Prep</A></LI>
    <LI CLASS="breadcrumb-item active">Data Source Mapping</LI>
  </OL>
</NAV>

<P>

END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #connect to the database
  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  print_html_header();

  #get all of the data sources on the system, and generate an alpha-ordered str
  %dsNameHash = ds_get_name_hash($kapDB);
  foreach $id (sort {$dsNameHash{$a} cmp $dsNameHash{$b}} keys %dsNameHash)
  {
    $dsNameSortStr .= "$id,";
  }
  chop($dsNameSortStr);

  #output the panel header HTML
  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col-12"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Data Flow/Data Source Mapping</DIV>
        <DIV CLASS="card-body">

          <DIV CLASS="table-responsive">
            <TABLE CLASS="table table-sm">
              <THEAD><TR>
                <TH>Data Source</TH>
                <TH>Last Run</TH>
                <TH>Data Flow</TH>
              </TR></THEAD>
END_HTML

  #non-admin users can only see their own data flows
  $whereClause = "";
  if ($acctType < 5)
  {
    $whereClause = "WHERE userID=$userID";
  }

  $query = "SELECT ID, name, lastRun, dsID FROM prep.flows $whereClause \
      ORDER BY FIELD(dsID, $dsNameSortStr), name";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;

  $bgClass = "active";
  while (($flowID, $flowName, $lastRun, $dsID) = $dbOutput->fetchrow_array)
  {

    #if the current data flow feeds the same data source
    if (($dsID == $previousDSID) && ($dsID > 0))
    {
      print <<END_HTML;
              <TR CLASS='$bgClass'>
                <TD></TD>
                <TD NOWRAP>$lastRun</TD>
                <TD><A CLASS="text-decoration-none" HREF='flowOpen.cld?f=$flowID'>$flowName</A></TD>
              </TR>
END_HTML
    }

    #else we're printing out data for a new data source
    else
    {

      #flip the background row color
      if ($bgClass eq "table-active")
      {
        $bgClass = "default";
      }
      else
      {
        $bgClass = "table-active";
      }

      $dsName = $dsNameHash{$dsID};
      if (length($dsName) < 1)
      {
        $dsName = "<SPAN STYLE='color:gray; font-style:italic;'>(none)</SPAN>";
      }
      else
      {
        $dsName = "<A CLASS='text-decoration-none' HREF='$Lib::KoalaConfig::kapHostURL/app/dsr/display.cld?ds=$dsID'>$dsName</A>";
      }

      print <<END_HTML;
              <TR CLASS='$bgClass'>
                <TD>$dsName</TD>
                <TD NOWRAP>$lastRun</TD>
                <TD><A CLASS="text-decoration-none" HREF='flowOpen.cld?f=$flowID'>$flowName</A></TD>
              </TR>
END_HTML

      $previousDSID = $dsID;
    }
  }

  print <<END_HTML;
            </TABLE>
          </DIV>

          <DIV CLASS="text-center">
            <BUTTON TYPE="button" CLASS="btn btn-primary" onclick="location.href='main.cld'"><I CLASS="bi bi-check-lg"></I> Done</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
<P>&nbsp;</P>
END_HTML

  print_html_footer();

  utils_slack("PREP: $first $last viewed data source mapping");


#EOF
