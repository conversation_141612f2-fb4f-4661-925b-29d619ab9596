#!/usr/bin/perl

use Text::CSV;

#Import C&S Key Foods Spin data for ESM-Ferolie

  open(INPUT, "$ARGV[0]");
  open(OUTPUT, ">$ARGV[1]");

  #if the file is TSV, convert to CSV
  if ($ARGV[0] =~ m/\.tsv$/i)
  {

    my $csv = Text::CSV->new( { binary => 1 } );
    my $tsv = Text::CSV->new( { binary => 1, sep_char => "\t" } );

    $tempName = $ARGV[0] . "_TEMP";
    open(TEMP, ">$tempName");

    while ($line = <INPUT>)
    {
      $tsv->parse($line);
      @columns = $tsv->fields();
      $csv->combine(@columns);
      $line = $csv->string();
      print TEMP "$line\n";
    }

    close(TEMP);
    close(INPUT);

    unlink($ARGV[0]);
    rename($tempName, $ARGV[0]);

    open(INPUT, "$ARGV[0]");
  }

  #instantiate our CSV object
  $csv = Text::CSV->new( {binary => 1} );

  #burn the first header line
  $line = <INPUT>;

  #parse the 2nd header line (end date in E2)
  $line = <INPUT>;
  $csv->parse($line);
  @columns = $csv->fields();
  $endDate = $columns[4];
  if ($endDate =~ m/FROM .*? TO (.*)$/)
  {
    $endDate = "1 MONTH ENDING $1";
  }
  else
  {
    $endDate = "CUSTOM: $endDate";
  }

  #burn blank line
  $line = <INPUT>;

  #get vendor (output as PSEG)
  $line = <INPUT>;
  $csv->parse($line);
  @columns = $csv->fields();
  $segVendor = $columns[1];
  $segVendor =~ m/\d+\s+(.*)$/;
  $segVendor = $1;

  #burn blank line
  $line = <INPUT>;

  #parse the primary header line
  $line = <INPUT>;
  $csv->parse($line);
  @columns = $csv->fields();

  #burn blank line
  $line = <INPUT>;

  $idx = 0;
  foreach $header (@columns)
  {
    if ($header eq "ITEM#")
    {
      $columns[$idx] = "pattr:ITEM#";
    }
    elsif ($header eq "DESCRIPTION")
    {
      $columns[$idx] = "Product";
      $productCol = $idx;
    }
    elsif ($header eq "UPC")
    {
      $upcCol = $idx;
    }
    elsif ($header eq "ITEM#")
    {
      $itemNumCol = $idx;
    }

    $idx++;
  }

  #add our geo and time period headers
  @tmp = ('Geography', 'Time Period', 'pseg:VENDOR');
  push(@tmp, @columns);

  #output the headers
  $csv->combine(@tmp);
  $line = $csv->string();
  print OUTPUT "$line\n";

  while ($line = <INPUT>)
  {
    $csv->parse($line);
    @columns = $csv->fields();

    #format the UPC (assumes 11 digit UPC)
    $columns[$upcCol] = sprintf("%011d", $columns[$upcCol]);
    $columns[$upcCol] =~ m/(\d)(\d\d\d\d\d)(\d\d\d\d\d)/;
    $upc = "$1-$2-$3-$3";
    $columns[$upcCol] = $upc;

    #add the ITEM# to the end of the product name
    $columns[$productCol] = "$columns[$productCol] $columns[$itemNumCol]";

    #push our geography and end date onto the beginning of the line
    @tmp = ('Key Foods', $endDate, $segVendor);
    push(@tmp, @columns);

    $csv->combine(@tmp);
    $line = $csv->string();

    print OUTPUT "$line\n";
  }
