#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Grid Edit Segmentations</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/jsgrid/jsgrid.min.css" REL="stylesheet">
<LINK HREF="/jsgrid/jsgrid-theme.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<SCRIPT SRC="/jsgrid/jsgrid.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-0">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Grid Edit Segmentations</LI>
  </OL>
</NAV>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');
  $dim = $q->param('dim');

  #connect to the database
  $db = KAPutil_connect_to_database();

  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  #make sure we have write privs for this data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit segmentations in this data source.");
  }

  $dsSchema = "datasource_" . $dsID;

  #get our dimension name for display purposes/db table name for SELECT
  $dimName = KAPutil_get_dim_name_singular($dim, 1);
  $dimDB = KAPutil_get_dim_stub_name($dim);

  #get all of the segmentations in the DS for table field purposes
  %segHash = DSRsegmentation_get_segmentations_hash($db, $dsSchema, $dim);

  #build the JSON for the fields
  $json = "";
  foreach $segID (sort {$segHash{$a} cmp $segHash{$b}} keys %segHash)
  {
    $json .= "{name: '$segHash{$segID}', type: 'text', width:175},\n";
  }
  chop($json);  chop($json);

  print <<END_HTML;
<STYLE>
div.grid th
{
  background: #e2e3e5 !important;
}

.selected-row > td
{
  background: #c4e2ff !important;
  border-color: #c4e2ff;
}
</STYLE>

<SCRIPT>
let gridHeight = window.innerHeight - 170;
let selectedItems = [];

\$(document).ready(function()
{

  \$('#segGrid').jsGrid(
  {
    width: '98%',
    height: gridHeight,
    sorting: true,
    autoload: true,
    loadIndication: true,
    multiselect: true,

    controller:
    {
      loadData: function (filter)
      {
        let data = \$.Deferred();
        \$.ajax(
        {
          type: 'GET',
          contentType: 'application/json; charset=utf-8',
          url: 'ajaxSegmentationGrid.cld?ds=$dsID&dim=$dim',
          dataType: 'json'
        }).done(function(response)
        {
          data.resolve(response);
        });
        return data.promise();
        }
      },

      rowClick: function(args)
      {

        //Shift + selection
        if (args.event.shiftKey)
        {
          document.getSelection().removeAllRanges();
          let i = 0;

          let firstSelection = -1;
          while ((i < this.data.length) && (firstSelection < 0))
          {
            if (this.data[i].selected == 1)
            {
              firstSelection = i;
            }
            i++;
          }

          i = 0;
          let curSelection = -1;
          while ((i < this.data.length) && (curSelection < 0))
          {
            if (args.item.ID == this.data[i].ID)
            {
              curSelection = i;
            }
            i++;
          }

          clearAllSelections();

          let start, end;
          if (curSelection > firstSelection)
          {
            start = firstSelection;
            end = curSelection;
          }
          else
          {
            end = firstSelection;
            start = curSelection;
          }

          for (i = start; i <= end; i++)
          {
            this.data[i].selected = 1;
            \$selectedRow = \$('#segGrid').jsGrid('rowByItem', this.data[i]).closest('tr');
            \$selectedRow.addClass('selected-row');
          }
        }

        //Ctrl+selection
        else if (event.ctrlKey || event.altKey || event.metaKey)
        {
          args.item.selected = 1;
          \$selectedRow = \$(args.event.target).closest('tr');
          \$selectedRow.addClass('selected-row');
        }

        //single selection
        else
        {
          clearAllSelections();
          args.item.selected = 1;
          \$selectedRow = \$(args.event.target).closest('tr');
          \$selectedRow.addClass('selected-row');
        }
      },

      rowDoubleClick: function(args)
      {
        selectedRule = args.item.ID;

        //location.href='/app/dsr/display.cld?ds=' + selectedRule;
      },
      fields: [
        {name: 'ID', type: 'number', visible: false},
        {name: 'Item', type: 'text', width: 200},
        $json
        ]
  });
});



function clearAllSelections()
{
  let grid = \$('#segGrid').jsGrid('option', 'data');

  for (let i = 0; i < grid.length; i++)
  {
    grid[i].selected = 0;
  }

  \$('#segGrid tr').removeClass('selected-row');
}



function getSelectionStr()
{
  let grid = \$('#segGrid').jsGrid('option', 'data');
  let selStr = '';

  for (let i = 0; i < grid.length; i++)
  {
    if (grid[i].selected == 1)
    {
      selStr = selStr + grid[i].id + ',';
    }
  }

  return(selStr);
}
</SCRIPT>


<NAV CLASS="navbar navbar-expand-md navbar-light bg-light border">
  <BUTTON CLASS="navbar-toggler" TYPE="button" data-bs-toggle="collapse" data-bs-target="#navbarNavDropdown">
    <SPAN CLASS="navbar-toggler-icon"></SPAN>
  </BUTTON>
  <DIV CLASS="collapse navbar-collapse justify-content-center" ID="navbarNavDropdown">
    <UL CLASS="navbar-nav">
      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="doSegmentItems()"><I CLASS="bi bi-pencil"></I> Segment Items</A></LI>
      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="doSegmentItemsColLayout()"><I CLASS="bi bi-table"></I> Set Column Display</A></LI>
      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="location.href='display.cld?ds=$dsID&d=$dim'"><I CLASS="bi bi-check-lg"></I> Done</A></LI>
    </UL>
  </DIV>
</NAV>

<P>

<DIV ID="segGrid" CLASS="grid mx-auto" STYLE="font-size:13px;"></DIV>

END_HTML

  print_html_footer();

#EOF
