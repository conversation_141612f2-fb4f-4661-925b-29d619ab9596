#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;



  $userID = 1;

  chdir("/opt/apache/htdocs/tmp/struct");

  #connect to the database
  $db = KAPutil_connect_to_database();

  #get a hash of all data sources on the system
  %dataSources = ds_get_name_hash($db);

  #get hash of all users on the system
  %userNames = utils_get_user_hash($db);

  #export each data source
  foreach $dsID (keys %dataSources)
  {
    $dsName = ds_id_to_name($db, $dsID);
    $dsSchema = "datasource_" . $dsID;

    #get the data source's owner
    $query = "SELECT userID FROM dataSources WHERE ID=$dsID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($ownerID) = $dbOutput->fetchrow_array;

    if ($ownerID != $userID)
    {
      next;
    }

    #get the item names for all dimensions
    %prodNameHash = dsr_get_item_name_hash($db, $dsSchema, "p");
    %geoNameHash = dsr_get_item_name_hash($db, $dsSchema, "g");
    %timeNameHash = dsr_get_item_name_hash($db, $dsSchema, "t");
    %measNameHash = dsr_get_item_name_hash($db, $dsSchema, "m");

    #build up our filename
    $userName = $userNames{$userID};
    $filename = $userName . "_" . $dsName;
    $filename =~ s/\"//g;
    $filename =~ s/\'//g;
    $filename =~ s/\s//g;
    $filename =~ s/&//g;
    $filename =~ s/\(//g;
    $filename =~ s/\)//g;
    $filename = "$userID" . "_" . $dsID . "_" . $filename . ".csv";

    #open the file for output
    open(OUTPUT, ">$filename");

    #output the CSV file's header line
    print OUTPUT "Dimension,Structure,Name,Item\n";

    #select all of the aggregates in this data base
    print OUTPUT "Products\n";
    print OUTPUT " ,\"Aggregates\"\n";
    $query = "SELECT name, addMembers FROM $dsSchema.product_aggregate";
    $dbOutput1 = $db->prepare($query);
    $dbOutput1->execute;
    while (($name, $addMembers) = $dbOutput1->fetchrow_array)
    {
      print OUTPUT " , ,\"$name\"\n";
      @members = split(',', $addMembers);
      foreach $member (@members)
      {
        print OUTPUT " , , ,\"$prodNameHash{$member}\"\n";
      }
    }

    print OUTPUT " ,\"Lists\"\n";
    $query = "SELECT name, members FROM $dsSchema.product_list";
    $dbOutput1 = $db->prepare($query);
    $dbOutput1->execute;
    while (($name, $addMembers) = $dbOutput1->fetchrow_array)
    {
      print OUTPUT " , ,\"$name\"\n";
      @members = split(',', $addMembers);
      foreach $member (@members)
      {
        print OUTPUT " , , ,\"$prodNameHash{$member}\"\n";
      }
    }

    print OUTPUT " ,\"Segmentations\"\n";
    %segmentations = DSRsegmentation_get_segmentations_hash($db, $dsSchema, "p");
    foreach $segID (keys %segmentations)
    {
      print OUTPUT " , ,\"$segmentations{$segID}\"\n";

      %segments = DSRseg_get_segments_hash($db, $dsSchema, "p", $segID);
      foreach $segmentID (keys %segments)
      {
        print OUTPUT " , , ,\"$segments{$segmentID}\"\n";

        @items = DSRseg_get_segitems_array($db, $dsSchema, "p", $segID, $segmentID);
        foreach $itemID (@items)
        {
          print OUTPUT " , , , ,\"$prodNameHash{$itemID}\"\n";
        }
      }
    }

    #select all of the aggregates in this data base
    print OUTPUT "Geographies\n";
    print OUTPUT " ,\"Aggregates\"\n";
    $query = "SELECT name, addMembers FROM $dsSchema.geography_aggregate";
    $dbOutput1 = $db->prepare($query);
    $dbOutput1->execute;
    while (($name, $addMembers) = $dbOutput1->fetchrow_array)
    {
      print OUTPUT " , ,\"$name\"\n";
      @members = split(',', $addMembers);
      foreach $member (@members)
      {
        print OUTPUT " , , ,\"$geoNameHash{$member}\"\n";
      }
    }

    print OUTPUT " ,\"Lists\"\n";
    $query = "SELECT name, members FROM $dsSchema.geography_list";
    $dbOutput1 = $db->prepare($query);
    $dbOutput1->execute;
    while (($name, $addMembers) = $dbOutput1->fetchrow_array)
    {
      print OUTPUT " , ,\"$name\"\n";
      @members = split(',', $addMembers);
      foreach $member (@members)
      {
        print OUTPUT " , , ,\"$geoNameHash{$member}\"\n";
      }
    }

    #select all of the aggregates in this data base
    print OUTPUT "Time Periods\n";
    print OUTPUT " ,\"Aggregates\"\n";
    $query = "SELECT name, addMembers FROM $dsSchema.time_aggregate";
    $dbOutput1 = $db->prepare($query);
    $dbOutput1->execute;
    while (($name, $addMembers) = $dbOutput1->fetchrow_array)
    {
      print OUTPUT " , ,\"$name\"\n";
      @members = split(',', $addMembers);
      foreach $member (@members)
      {
        print OUTPUT " , , ,\"$timeNameHash{$member}\"\n";
      }
    }

    print OUTPUT " ,\"Lists\"\n";
    $query = "SELECT name, members FROM $dsSchema.time_list";
    $dbOutput1 = $db->prepare($query);
    $dbOutput1->execute;
    while (($name, $addMembers) = $dbOutput1->fetchrow_array)
    {
      print OUTPUT " , ,\"$name\"\n";
      @members = split(',', $addMembers);
      foreach $member (@members)
      {
        print OUTPUT " , , ,\"$timeNameHash{$member}\"\n";
      }
    }

    #select all of the aggregates in this data base
    print OUTPUT "Measures\n";

    print OUTPUT " ,\"Lists\"\n";
    $query = "SELECT name, members FROM $dsSchema.measure_list";
    $dbOutput1 = $db->prepare($query);
    $dbOutput1->execute;
    while (($name, $addMembers) = $dbOutput1->fetchrow_array)
    {
      print OUTPUT " , ,\"$name\"\n";
      @members = split(',', $addMembers);
      foreach $member (@members)
      {
        print OUTPUT " , , ,\"$measNameHash{$member}\"\n";
      }
    }

    close(OUTPUT);
  }

#EOF
