#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;
use Excel::Writer::XLSX;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSel;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::ExcelReports;
use Lib::Reports;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $rptID = $q->param('rptID');

  $db = KAPutil_connect_to_database();

  #make sure we have view privs for this report
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to export this report.");
  }

  $cubeName = cube_id_to_name($db, $rptID);
  $dsID = cube_get_ds_id($db, $rptID);
  $dsName = ds_id_to_name($db, $dsID);

  $query = "INSERT INTO audit.stats_cubes (cubeID, year, month, exportExcel) \
      VALUES ($rptID, YEAR(NOW()), MONTH(NOW()), 1) \
      ON DUPLICATE KEY UPDATE exportExcel=exportExcel+1";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);

  #update the Excel export time
  $query = "UPDATE cubes SET lastExcelExport=NOW() WHERE ID=$rptID";
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);

  utils_audit($db, $userID, "Exported report to Excel", $dsID, $rptID, 0);
  $activity = "$first $last exported report $cubeName to Excel in $dsName";
  utils_slack($activity);

  #get the data cube details from the database
  $query = "SELECT name, dsID FROM cubes WHERE ID=$rptID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($name, $dsID) = $dbOutput->fetchrow_array;

  #make sure our sheet name isn't going to be too long
  $name = substr($name, 0, 29);

  #get rid of special characters Excel doesn't like
  $name =~ s/\\/ /g;
  $name =~ s/\// /g;

  #build up the temp filename
  $tmpDSName = substr($dsName, 0, 32);
  $tmpCubeName = substr($cubeName, 0, 32);
  $filename = "Koala $tmpDSName" . " - " . $tmpCubeName . "_" . $userID . "_" . "$dsID.xlsx";

  #get rid of special characters we don't want in the file name
  $filename =~ s/\s+/ /g;
  $filename =~ s/\\//g;
  $filename =~ s/\///g;
  $filename =~ s/\*//g;
  $filename =~ s/\$//g;
  $filename =~ s/\&//g;
  $filename =~ s/\|//g;
  $filename =~ s/\?//g;
  $filename =~ s/\://g;
  $filename =~ s/\"//g;
  $filename =~ s/\'//g;
  $filename =~ s/\%//g;
  $filename =~ s/\#//g;

  $query = "SELECT selProducts, selGeographies, selTimeperiods \
      FROM visuals WHERE cubeID=$rptID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($selProducts, $selGeographies, $selTimeperiods) = $dbOutput->fetchrow_array;

  #if the user is trying to export a report too big for Excel, error out
  @selProducts = split(',', $selProducts);
  @selGeographies = split(',', $selGeographies);
  @selTimeperiods = split(',', $selTimeperiods);
  $numProducts = scalar @selProducts;
  $numGeographies = scalar @selGeographies;
  $numTimeperiods = scalar @selTimeperiods;
  $rows = $numProducts * $numGeographies * $numTimeperiods;
  if ($rows > 1_000_000)
  {
    print <<END_HTML;
<DIV CLASS="modal-dialog" STYLE="overflow-y: initial !important;">
  <DIV CLASS="modal-content">
    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Export Report to Excel</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">
      <P>
      <SPAN STYLE="color:red; font-weight:bold;">You're trying to export more rows ($rows) than Excel can handle. Please adjust your report settings to export less rows and try again.</SPAN>
    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-primary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
    </DIV>

  </DIV>
 </DIV>
END_HTML
    exit;
  }

#fork a new process to do the actual PPT export in the background
$SIG{CHLD} = "IGNORE";
if ($pid = fork)
{
  #parent process

  print <<END_HTML;
<SCRIPT>
\$('#done').prop('disabled', true);
var statusTimer = setInterval(function(){displayStatus()}, 2000);

function displayStatus()
{
  const url = "/app/rpt/xhrPPTexportStatus.cld?d=$dsID";

  \$.get(url, function(data, status)
  {
    statusText = data;

    if (statusText.length < 2)
    {
      statusText = "Exporting reports to PowerPoint";
    }

    if (statusText.length == 5)  //DONE\n
    {
      \$('#progress-bar-container').hide();
      \$('#progressDiv').hide();
      document.getElementById('download-btn').style.visibility = 'visible';
      clearInterval(statusTimer);
      \$('#done').prop('disabled', false);
    }
    else
    {
      document.getElementById('progressDiv').innerHTML = statusText;
    }
  });
}
</SCRIPT>

<DIV CLASS="modal-dialog" STYLE="overflow-y: initial !important;">
  <DIV CLASS="modal-content">
    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Export Report To Excel</H5>
    </DIV>

    <DIV CLASS="modal-body">
      <P>
      <DIV CLASS="progress" ID="progress-bar-container">
        <DIV ID="progress-bar" CLASS="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" STYLE="width:100%;"></DIV>
      </DIV>

      <P>
      <DIV CLASS="text-center">
        <DIV ID="progressDiv">Exporting report to Excel</DIV>

        <P>&nbsp;</P>
        <BUTTON CLASS="btn btn-success" TYPE="button" ID="download-btn" STYLE="visibility:hidden;" onClick="location.href='/tmp/$filename'"><I CLASS="bi bi-download"></I> Download Excel Workbook</BUTTON>
      </DIV>

    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-primary" TYPE="button" ID="done" data-bs-dismiss="modal"><I CLASS="bi bi-check-lg"></I> Done</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML

  $activity = "$first $last exporting reports in $dsName to PowerPoint";
  utils_slack($activity);

  exit;
}

else
{
  #child process

  #let Apache know not to wait on the child process
  close(STDIN);
  close(STDOUT);

  #redirect STDERR to the Koala error log
  close(STDERR);
  open(STDERR, ">>/opt/apache/htdocs/tmp/koala_error.log");
  select(STDERR);
  $| = 1;

  #reconnect to the database
  $db = KAPutil_connect_to_database();

  #set our initial state in the jobs table
  KAPutil_job_store_status($db, $userID, $dsID, 0, "REPORT-EXPORT", "Exporting report to Excel");

  #create a new Excel workbook
  $workbook = Excel::Writer::XLSX->new("/opt/apache/htdocs/tmp/$filename");

  #if we're just outputting one giant table, enable possible optimizations
  $query = "SELECT type, tableColDims FROM visuals WHERE cubeID=$rptID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($reportType, $tableColDims) = $dbOutput->fetchrow_array;

  #if we can use the output optimization (can only write once top to bottom)
  if (($status eq 1) && ($reportType eq "table") && (length($tableColDims) < 1))
  {
    $workbook->set_optimization();
  }

  #split measures CSV string into array
  @measures = split(',', $measures);

  #assemble datasource and cube names
  $rptCube = "_rptcube_" . $rptID;

  #add worksheet with same name as report
  $worksheet1 = $workbook->add_worksheet($name);

  excel_insert_visuals($db, $rptID, $userID, $acctType, $workbook, $worksheet1);

  $workbook->close();

  #remove this task from the jobs table
  DSRutil_clear_status($db);

  exit;
}


#EOF
