#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRMeasures;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::GeoCode;
use Lib::Reports;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
}



my %geoCodeMappingHash = (
    "northamericawocentral" => "countries",
    "usaregion" => "usaregions",
    "usa" => "states",
    "usacentralregion" => "region",
    "usanortheastregion" => "region",
    "usasoutheastregion" => "region",
    "usanorthwestregion" => "region",
    "usasouthwestregion" => "region",
    "usadma" => "dma",
    "eastnorthcentraldma" => "dma",
    "eastsouthcentraldma" => "dma",
    "middleatlanticdma" => "dma",
    "mountaindma" => "dma",
    "newenglanddma" => "dma",
    "pacificdma" => "dma",
    "southatlanticdma" => "dma",
    "westnorthcentraldma" => "dma",
    "westsouthcentraldma" => "dma",
    );



    #-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get the CGI input variables
  $rptID = $q->param('rpt');
  $visID = $q->param('v');

  #get any "fixed" dimensions (used for expanded reports)
  $fProd = $q->param('p');
  $fGeo = $q->param('g');
  $fTime = $q->param('t');

  $db = KAPutil_connect_to_database();

  #if we're being called as part of a PPT export
  if ($userID < 1)
  {
    $userID = $q->param('u');
    $query = "SELECT acctType FROM app.users WHERE ID=$userID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($acctType) = $dbOutput->fetchrow_array;
  }

  #get the list of selected dimension items from the database
  $query = "SELECT dsID, design FROM visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($dsID, $mapDesign) = $dbOutput->fetchrow_array;

  ($productIDstring, $geographyIDstring, $timeIDstring, $measureIDstring) = reports_get_selected_items($db, $visID, $userID, $acctType);

  print("Content-type: text/plain\n\n");

  #assemble report cube name
  $dsSchema = "datasource_" . $dsID;
  $rptCube = "_rptcube_" . $rptID;

  #get the ID of the measure we're graphing
  $measureIDstring =~ m/(\d+)/;
  $measureID = $1;

  #if we're dealing with a fixed dimension (used for expanded reports)
  if (length($fProd) > 0)
  {
    $productIDstring = $fProd;
  }
  if (length($fGeo) > 0)
  {
    $geographyIDstring = $fGeo;
  }
  if (length($fTime) > 0)
  {
    $timeIDstring = $fTime;
  }

  @selProds = split(/,/, $productIDstring);
  @selGeos = split(/,/, $geographyIDstring);
  @selTimes = split(/,/, $timeIDstring);
  @selMeasures = split(/,/, $measureIDstring);

  #fetch the geography names
  %geoNameHash = dsr_get_item_name_hash($db, $dsSchema, "g");

  #if the data source has a Geo Code attribute, load it up
  $query = "SELECT ID FROM $dsSchema.geography_attributes WHERE name='Geo Code'";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($geoCodeID) = $dbOutput->fetchrow_array;
  undef(%geoCodeHash);
  if ($geoCodeID > 0)
  {
    %geoCodeHash = DSRattr_get_values_hash($db, $dsSchema, "g", $geoCodeID);
  }

  #create an array of the geographies we need to display, and go ahead and set
  #up our "single selection" dimension IDs while we're at it
  @geoIDs = split(/,/, $geographyIDstring);

  $q_product = $db->quote($selProds[0]);
  $q_time = $db->quote($selTimes[0]);


  # ------- parse map design info ----------

  #get the map type
  $mapType = reports_get_style($mapDesign, "type");
  if (!defined($mapType))
  {
    $mapType = "usa";
  }

  #handle a "locked" measure for the map
  if ($mapDesign =~ m/,measure:(\d+),/)
  {
    $lockedMeasure = $1;
    if ($lockedMeasure > 0)
    {
      $measureID = $lockedMeasure;
    }
  }


  #handle a caption (map title)
  if ($mapDesign =~ m/,caption:\"(.*?)\",/)
  {
    $caption = "caption='$1'";
  }

  $captionFontColor = reports_get_style($mapDesign, "captionFontColor");
  if (length($captionFontColor) > 6)
  {
    $captionFontColor = "captionFontColor='$captionFontColor'";
  }

  $captionAlignment = reports_get_style($mapDesign, "captionAlignment");
  if (length($captionAlignment) > 1)
  {
    $captionAlignment = "captionAlignment='$captionAlignment'";
  }

  $captionFontSize = reports_get_style($mapDesign, "captionFontSize");
  if ($captionFontSize > 0)
  {
    $captionFontSize = "captionFontSize='$captionFontSize'";
  }

  $captionFont = reports_get_style($mapDesign, "captionFont");
  if (length($captionFont) > 3)
  {
    $captionFont = "captionFont='$captionFont'";
  }

  #handle a subcaption (map subtitle)
  if ($mapDesign =~ m/,subcaption:\"(.*?)\",/)
  {
    $subcaption = "subcaption='$1'";
  }

  #handle gradient min/mid/max color points
  $minColor = reports_get_style($mapDesign, "minColor");
  $midColor = reports_get_style($mapDesign, "midColor");
  $maxColor = reports_get_style($mapDesign, "maxColor");
  if (!(defined($minColor)))
  {
    $minColor = "#fd625e";
  }
  if (!(defined($midColor)))
  {
    $midColor = "#f2c80f";
  }
  if (!(defined($maxColor)))
  {
    $maxColor = "#01b8aa";
  }

  #handle a border (color & thickness)
  $showBorder = reports_get_style($mapDesign, "showBorder");
  if (!defined($showBorder))
  {
    $showBorder = 0;
  }
  $borderColor = reports_get_style($mapDesign, "borderColor");
  $borderThickness = reports_get_style($mapDesign, "borderThickness");

  #handle background color
  $bgColor = reports_get_style($mapDesign, "bgColor");
  if (length($bgColor) < 2)
  {
    $bgColor = "#ffffff";
  }

  #handle canvas background color
  #XXX


  #handle graph legend styling
  $showLegend = reports_get_style($graphDesign, "showLegend");
  if (length($showLegend) > 0)
  {
    $showLegend = "showLegend='$showLegend'";
  }

  $legendItemFontColor = reports_get_style($graphDesign, "legendItemFontColor");
  if (length($legendItemFontColor) > 6)
  {
    $legendItemFontColor = "legendItemFontColor='$legendItemFontColor'";
  }

  $legendPosition = reports_get_style($graphDesign, "legendPosition");
  if (length($legendPosition) > 0)
  {
    $legendPosition = "legendPosition='$legendPosition'";
  }

  $legendItemFont = reports_get_style($graphDesign, "legendItemFont");
  if (length($legendItemFont) > 3)
  {
    $legendItemFont = "legendItemFont='$legendItemFont'";
  }

  $legendItemFontSize = reports_get_style($graphDesign, "legendItemFontSize");
  if ($legendItemFontSize > 0)
  {
    $legendItemFontSize = "legendItemFontSize='$legendItemFontSize'";
  }


  #handle data label styling
  $showLabels = reports_get_style($mapDesign, "showLabels");
  if (length($showLabels) > 0)
  {
    $showLabels = "showLabels='$showLabels'";
  }

  $labelFontColor = reports_get_style($mapDesign, "labelFontColor");
  if (length($labelFontColor) > 6)
  {
    $labelFontColor = "baseFontColor='$labelFontColor'";
  }

  $useSNameInLabels = reports_get_style($mapDesign, "useSNameInLabels");
  if (length($useSNameInLabels) > 0)
  {
    $useSNameInLabels = "useSNameInLabels='$useSNameInLabels'";
  }

  $includeValueInLabels = reports_get_style($mapDesign, "includeValueInLabels");
  if (length($includeValueInLabels) > 0)
  {
    $includeValueInLabels = "includeValueInLabels='$includeValueInLabels'";
  }

  $labelFontSize = reports_get_style($mapDesign, "labelFontSize");
  if ($labelFontSize > 0)
  {
    $labelFontSize = "baseFontSize='$labelFontSize'";
  }

  $labelFont = reports_get_style($mapDesign, "labelFont");
  if (length($labelFont) > 3)
  {
    $labelFont = "baseFont='$labelFont'";
  }


  #handle data value styling
  $showValues = reports_get_style($mapDesign, "showValues");
  if (length($showValues) > 0)
  {
    $showValues = "showValues='$showValues'";
  }

  $valueFontColor = reports_get_style($graphDesign, "valueFontColor");
  if (length($valueFontColor) > 6)
  {
    $valueFontColor = "valueFontColor='$valueFontColor'";
  }

  $formatNumberScale = reports_get_style($graphDesign, "formatNumberScale");
  if (length($formatNumberScale) > 0)
  {
    $formatNumberScale = "formatNumberScale='$formatNumberScale'";
  }

  $decimals = reports_get_style($graphDesign, "decimals");
  if (length($decimals) > 0)
  {
    $decimals = "decimals='$decimals'";
  }
  else
  {
    $decimals = "decimals='2'";
  }

  $placeValuesInside = reports_get_style($graphDesign, "placeValuesInside");
  if (length($placeValuesInside) > 0)
  {
    $placeValuesInside = "placeValuesInside='$placeValuesInside'";
  }

  $valueFontSize = reports_get_style($graphDesign, "valueFontSize");
  if ($valueFontSize > 0)
  {
    $valueFontSize = "valueFontSize='$valueFontSize'";
  }

  $valueFont = reports_get_style($graphDesign, "valueFont");
  if (length($valueFont) > 3)
  {
    $valueFont = "valueFont='$valueFont'";
  }

  $showValuesBg = reports_get_style($graphDesign, "showValuesBg");
  if ($showValuesBg > 0)
  {
    $valueBgColor = reports_get_style($graphDesign, "valueBgColor");
    $valueBgColor = "valueBgColor='$valueBgColor'";
    $valueBgAlpha = reports_get_style($graphDesign, "valueBgAlpha");
    $valueBgAlpha = 100 - $valueBgAlpha;
    $valueBgAlpha = "valueBgAlpha='$valueBgAlpha'";
  }

  #if the titles have expandable tag(s), expand them
  $caption = reports_expand_dim_tags($db, $dsSchema, $caption, $productIDstring, $geographyIDstring, $timeIDstring);
  $subcaption = reports_expand_dim_tags($db, $dsSchema, $subcaption, $productIDstring, $geographyIDstring, $timeIDstring);

  # ------- end of design parsing ----------


  #get our measure format info, and apply as much of it as we can
  $formatStr = DSRmeasures_get_format($db, $dsSchema, $measureID);
  @formats = split(',', $formatStr);
  $formatNumber = $formats[1];
  $numberPrefix = "";
  if ($formats[2] == 1)
  {
    $numberPrefix = "\$";
  }
  elsif ($formats[2] == 2)
  {
    $numberSuffix = "%";
  }

  #print out the XML chart info
  print <<XML_LABEL;
<chart
  theme='zune'
  animation='0'

  $caption
  $captionAlignment
  $captionFontColor
  $captionFontSize
  $captionFont

  $subcaption

  showBorder='$showBorder'

  $showLabels
  $useSNameInLabels
  $includeValueInLabels
  $labelFontColor
  $labelFontSize
  $labelFont

  $showLegend
  $legendPosition
  $legendItemFontColor
  $legendItemFont
  $legendItemFontSize

  $showValues
  $valueFontColor
  $formatNumberScale
  $decimals
  $placeValuesInside
  $valueFontSize
  $valueFont
  $valueBgColor
  $valueBgAlpha

  bgColor='$bgColor'
  borderColor='$borderColor'
  borderThickness='$borderThickness'
  canvasBgColor='$bgColor'
  canvasBorderColor='$bgColor'

  labelSepChar=':'
  paletteColors='$paletteColors'
  formatNumber='$formatNumber'
  numberPrefix='$numberPrefix'
  numberSuffix='$numberSuffix'>
XML_LABEL

  #if our dimension series is anything other than measure, set the measure
  #NB: If our series is measures, we'll set them below and overwrite this
  $measureCol = "measure_" . $measureID;

  #build our SQL selection string for geographies
  $geoSQL = "";
  foreach $geoID (@geoIDs)
  {
    $geoSQL .= "$geoID,";
  }
  chop($geoSQL);

  #get minimum and maximum values (for color gradient settings)
  $query = "SELECT MIN($measureCol), MAX($measureCol) \
      FROM $dsSchema.$rptCube \
      WHERE product=$q_product AND geography IN ($geoSQL) AND time=$q_time";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($minVal, $maxVal) = $dbOutput->fetchrow_array;
  $midVal = ($maxVal - $minVal) / 2;

  #format numbers for gradient display
  if ($minVal < 10)
  {
    $minVal = sprintf("%.2f", $minVal);
  }
  elsif ($minVal < 100)
  {
    $minVal = sprintf("%.1f", $minVal);
  }
  else
  {
    $minVal = sprintf("%.0f", $minVal);
  }
  if ($midVal < 10)
  {
    $midVal = sprintf("%.2f", $midVal);
  }
  elsif ($midVal < 100)
  {
    $midVal = sprintf("%.1f", $midVal);
  }
  else
  {
    $midVal = sprintf("%.0f", $midVal);
  }
  if ($maxVal < 10)
  {
    $maxVal = sprintf("%.2f", $maxVal);
  }
  elsif ($maxVal < 100)
  {
    $maxVal = sprintf("%.1f", $maxVal);
  }
  else
  {
    $maxVal = sprintf("%.0f", $maxVal);
  }

  print <<XML_LABEL;
 <colorrange minvalue="$minVal" code="$minColor" gradient="1">
  <color maxvalue="$midVal" code="$midColor" />
  <color maxvalue="$maxVal" code="$maxColor" />
 </colorrange>
XML_LABEL

  $query = "SELECT geography, $measureCol FROM $dsSchema.$rptCube \
      WHERE product=$q_product AND geography IN ($geoSQL) AND time=$q_time";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  #output the data set
  while (($geoID, $value) = $dbOutput->fetchrow_array)
  {

    #if there's a user-specified GeoCode value, it's the geoID
    if (defined($geoCodeHash{$geoID}))
    {
      $geoID = $geoCodeHash{$geoID};
    }

    #else try to figure out the geocode based on the geography name
    else
    {
      $geoID = $geoNameHash{$geoID};
    }

    #if our map needs US state codes
    if ($geoCodeMappingHash{$mapType} eq "states")
    {
      $geoID = GeoCode_states($geoID);
    }
    elsif ($geoCodeMappingHash{$mapType} eq "usaregions")
    {
      $geoID = GeoCode_usaregions($geoID);
    }
    elsif ($geoCodeMappingHash{$mapType} eq "region")
    {
      $geoID = GeoCode_region($geoID);
    }
    elsif ($geoCodeMappingHash{$mapType} eq "countries")
    {
      $geoID = GeoCode_countries($geoID);
    }
    elsif ($geoCodeMappingHash{$mapType} eq "dma")
    {
      $geoID = GeoCode_dma($geoID);
    }

    if (length($geoID) > 0)
    {
      print(" <set id='$geoID' value='$value' />\n");
    }
  }

  print("</chart>\n");


#EOF
