#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRMeasures;
use Lib::DSRUtils;
use Lib::Reports;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get the CGI input variables
  $rptID = $q->param('rpt');
  $visID = $q->param('v');
  $graphNum = $q->param('n');

  #get any "fixed" dimensions (used for expanded reports)
  $fProd = $q->param('p');
  $fGeo = $q->param('g');
  $fTime = $q->param('t');

  $graphDesignCol = "graphDesign" . $graphNum;

  $db = KAPutil_connect_to_database();

  #if we're being called as part of a PPT export
  if ($userID < 1)
  {
    $userID = $q->param('u');
    $query = "SELECT acctType FROM app.users WHERE ID=$userID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($acctType) = $dbOutput->fetchrow_array;
  }

  #get the list of axes & selected dimension items from the database
  $query = "SELECT dsID, design, graph_x FROM visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($dsID, $graphDesign, $graph_x) = $dbOutput->fetchrow_array;

  ($productIDstring, $geographyIDstring, $timeIDstring, $measureIDstring) = reports_get_selected_items($db, $visID, $userID, $acctType);

  #get slicer configuration
  $query = "SELECT slicers FROM cubes WHERE ID=$rptID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($slicersStr) = $dbOutput->fetchrow_array;

  $graphType = reports_get_style($graphDesign, "type");

  #make sure we're the right data script
  $dataScript = reports_data_script($graphType);
  if ($dataScript ne "ajaxReportGraphXYZ.cld")
  {
    $url = "http://" . $Lib::KoalaConfig::hostname . "/app/rpt/$dataScript" . "?rpt=$rptID&n=$graphNum";
    print $q->redirect(
       -uri=> $url,
       -status=>'301 Moved Permanently');
  }

  print("Content-type: text/plain\n\n");

  #assemble report cube name
  $dsSchema = "datasource_" . $dsID;
  $rptCube = "_rptcube_" . $rptID;

  #if we're dealing with a fixed dimension (used for expanded reports)
  if (length($fProd) > 0)
  {
    $productIDstring = $fProd;
  }
  if (length($fGeo) > 0)
  {
    $geographyIDstring = $fGeo;
  }
  if (length($fTime) > 0)
  {
    $timeIDstring = $fTime;
  }

  #figure out our X, Y, and Z axis measures
  $measureXID = reports_get_style($graphDesign, "measureX");
  $measureYID = reports_get_style($graphDesign, "measureY");
  $measureZID = reports_get_style($graphDesign, "measureZ");
  $measureX = "measure_" . $measureXID;
  $measureY = "measure_" . $measureYID;
  if ($measureZID < 1)
  {
    $measureZ = "NULL";
  }
  else
  {
    $measureZ = "measure_" . $measureZID;
  }


  # ------- parse graph design info ----------

  #handle a "locked" measure for the graph
  if ($graphDesign =~ m/,measure:(\d+),/)
  {
    $lockedMeasure = $1;
    if ($lockedMeasure > 0)
    {
      @setIDs = ($lockedMeasure);
    }
  }

  #handle a caption (chart title)
  if ($graphDesign =~ m/,caption:\"(.*?)\",/)
  {
    $caption = $1;
  }

  #handle a subcaption (chart subtitle)
  if ($graphDesign =~ m/,subcaption:\"(.*?)\",/)
  {
    $subcaption = $1;
  }

  $captionFontColor = reports_get_style($graphDesign, "captionFontColor");
  if (length($captionFontColor) < 1)
  {
    $captionFontColor = reports_chart_design_default("captionFontColor");
  }
  $captionFontColor = "captionFontColor='$captionFontColor'";

  $captionAlignment = reports_get_style($graphDesign, "captionAlignment");
  if (length($captionAlignment) < 1)
  {
    $captionAlignment = reports_chart_design_default("captionAlignment");
  }
  $captionAlignment = "captionAlignment='$captionAlignment'";

  $captionFontSize = reports_get_style($graphDesign, "captionFontSize");
  if (length($captionFontSize) < 1)
  {
    $captionFontSize = reports_chart_design_default("captionFontSize");
  }
  $captionFontSize = "captionFontSize='$captionFontSize'";

  $captionFont = reports_get_style($graphDesign, "captionFont");
  if (length($captionFont) > 3)
  {
    $captionFont = "captionFont='$captionFont'";
  }


  #handle x and y axis names
  if ($graphDesign =~ m/,xAxisName:\"(.*?)\",/)
  {
    $xAxisName = $1;
  }

  $xAxisNameFontSize = reports_get_style($graphDesign, "xAxisNameFontSize");
  if (length($xAxisNameFontSize) < 1)
  {
    $xAxisNameFontSize = reports_chart_design_default("xAxisNameFontSize");
  }
  $xAxisNameFontSize = "xAxisNameFontSize='$xAxisNameFontSize'";

  if ($graphDesign =~ m/,yAxisName:\"(.*?)\",/)
  {
    $yAxisName = $1;
  }

  $yAxisNameFontSize = reports_get_style($graphDesign, "yAxisNameFontSize");
  if (length($yAxisNameFontSize) < 1)
  {
    $yAxisNameFontSize = reports_chart_design_default("yAxisNameFontSize");
  }
  $yAxisNameFontSize = "yAxisNameFontSize='$yAxisNameFontSize'";


  #handle a regression line
  $showRegressionLine = reports_get_style($graphDesign, "showRegressionLine");
  if (!defined($showRegressionLine))
  {
    $showRegressionLine = 0;
  }

  #handle background color
  $bgColor = reports_get_style($graphDesign, "bgColor");
  if (length($bgColor) < 6)
  {
    $bgColor = reports_chart_design_default("bgColor");
  }
  $canvasBgColor = "canvasBgColor='$bgColor'";
  $bgColor = "bgColor='$bgColor'";


  #handle a border (color & thickness)
  $showBorder = reports_get_style($graphDesign, "showBorder");
  if (length($showBorder) < 1)
  {
    $showBorder = reports_chart_design_default("showBorder");
  }
  $showBorder = "showBorder='$showBorder'";

  $borderColor = reports_get_style($graphDesign, "borderColor");
  if (length($borderColor) < 6)
  {
    $borderColor = reports_chart_design_default("borderColor");
  }
  $borderColor = "borderColor='$valueFontColor'";

  $borderThickness = reports_get_style($graphDesign, "borderThickness");
  if (length($borderThickness) < 1)
  {
    $borderThickness = reports_chart_design_default("borderThickness");
  }
  $borderThickness = "borderThickness='$borderThickness'";


  #handle data label styling
  $showLabels = reports_get_style($graphDesign, "showLabels");
  if (length($showLabels) < 1)
  {
    $showLabels = reports_chart_design_default("showLabels");
  }
  $showLabels = "showLabels='$showLabels'";

  $labelFontColor = reports_get_style($graphDesign, "labelFontColor");
  if (length($labelFontColor) < 1)
  {
    $labelFontColor = reports_chart_design_default("labelFontColor");
  }
  $labelFontColor = "labelFontColor='$labelFontColor'";

  $labelDisplay = reports_get_style($graphDesign, "labelDisplay");
  if (length($labelDisplay) < 1)
  {
    $labelDisplay = reports_chart_design_default("labelDisplay");
  }
  $labelDisplay = "labelDisplay='$labelDisplay'";

  $labelStep = reports_get_style($graphDesign, "labelStep");
  if (length($labelStep) < 1)
  {
    $labelStep = reports_chart_design_default("labelStep");
  }
  $labelStep = "labelStep='$labelStep'";

  $labelFontSize = reports_get_style($graphDesign, "labelFontSize");
  if (length($labelFontSize) < 1)
  {
    $labelFontSize = reports_chart_design_default("labelFontSize");
  }
  $labelFontSize = "labelFontSize='$labelFontSize'";

  $labelFont = reports_get_style($graphDesign, "labelFont");
  if (length($labelFont) > 3)
  {
    $labelFont = "labelFont='$labelFont'";
  }


  #handle graph legend styling
  $showLegend = reports_get_style($graphDesign, "showLegend");
  if (length($showLegend) < 1)
  {
    $showLegend = reports_chart_design_default("showLegend");
  }
  $showLegend = "showLegend='$showLegend'";

  $legendItemFontColor = reports_get_style($graphDesign, "legendItemFontColor");
  if (length($legendItemFontColor) < 6)
  {
    $legendItemFontColor = reports_chart_design_default("legendItemFontColor");
  }
  $legendItemFontColor = "legendItemFontColor='$legendItemFontColor'";

  $legendPosition = reports_get_style($graphDesign, "legendPosition");
  if (length($legendPosition) < 1)
  {
    $legendPosition = reports_chart_design_default("legendPosition");
  }
  $legendPosition = "legendPosition='$legendPosition'";

  $legendItemFont = reports_get_style($graphDesign, "legendItemFont");
  if (length($legendItemFont) < 1)
  {
    $legendItemFont = reports_chart_design_default("legendItemFont");
  }
  $legendItemFont = "legendItemFont='$legendItemFont'";

  $legendItemFontSize = reports_get_style($graphDesign, "legendItemFontSize");
  if (length($legendItemFontSize) < 1)
  {
    $legendItemFontSize = reports_chart_design_default("legendItemFontSize");
  }
  $legendItemFontSize = "legendItemFontSize='$legendItemFontSize'";


  #handle data value styling
  $showValues = reports_get_style($graphDesign, "showValues");
  if (length($showValues) < 1)
  {
    $showValues = reports_chart_design_default("showValues");
  }
  $showValues = "showValues='$showValues'";

  $valueFontColor = reports_get_style($graphDesign, "valueFontColor");
  if (length($valueFontColor) < 6)
  {
    $valueFontColor = reports_chart_design_default("valueFontColor");
  }
  $valueFontColor = "valueFontColor='$valueFontColor'";

  $formatNumberScale = reports_get_style($graphDesign, "formatNumberScale");
  if (length($formatNumberScale) < 1)
  {
    $formatNumberScale = reports_chart_design_default("formatNumberScale");
  }
  $formatNumberScale = "formatNumberScale='$formatNumberScale'";

  $decimals = reports_get_style($graphDesign, "decimals");
  if (length($decimals) < 1)
  {
    $decimals = reports_chart_design_default("decimals");
  }
  $decimals = "decimals='$decimals'";

  $placeValuesInside = reports_get_style($graphDesign, "placeValuesInside");
  if (length($placeValuesInside) < 1)
  {
    $placeValuesInside = reports_chart_design_default("placeValuesInside");
  }
  $placeValuesInside = "placeValuesInside='$placeValuesInside'";

  $valueFontSize = reports_get_style($graphDesign, "valueFontSize");
  if (length($valueFontSize) < 1)
  {
    $valueFontSize = reports_chart_design_default("valueFontSize");
  }
  $valueFontSize = "valueFontSize='$valueFontSize'";

  $valueFont = reports_get_style($graphDesign, "valueFont");
  if (length($valueFont) > 3)
  {
    $valueFont = "valueFont='$valueFont'";
  }

  $showValuesBg = reports_get_style($graphDesign, "showValuesBg");
  if ($showValuesBg > 0)
  {
    $valueBgColor = reports_get_style($graphDesign, "valueBgColor");
    $valueBgColor = "valueBgColor='$valueBgColor'";
    $valueBgAlpha = reports_get_style($graphDesign, "valueBgAlpha");
    $valueBgAlpha = 100 - $valueBgAlpha;
    $valueBgAlpha = "valueBgAlpha='$valueBgAlpha'";
  }


  #handle "round edges" styling
  $useRoundEdges = reports_get_style($graphDesign, "useRoundEdges");

  #see if we're being asked to generate an "All Others" slice
  $allOthers = reports_get_style($graphDesign, "allOthers");

  #get the default color palette we're going to use for the chart
  @colors = reports_graph_color_array;

  #if the titles have expandable tag(s), expand them
  $caption = reports_expand_dim_tags($db, $dsSchema, $caption, $productIDstring, $geographyIDstring, $timeIDstring, $measureIDstring);
  $subcaption = reports_expand_dim_tags($db, $dsSchema, $subcaption, $productIDstring, $geographyIDstring, $timeIDstring, $measureIDstring);

  #get measure formatting for X & Y measures and apply as much as possible
  $formatStr = DSRmeasures_get_format($db, $dsSchema, $measureXID);
  @formats = split(',', $formatStr);
  $xDecimals = "xDecimals='$formats[0]'";
  $xFormatNumber = "xFormatNumber='$formats[1]'";
  $xNumberPrefix = "";
  $xNumberSuffix = "";
  if ($formats[2] == 1)
  {
    $xNumberPrefix = "xNumberPrefix='\$'";
  }
  elsif ($formats[2] == 2)
  {
    $xNumberSuffix = "xNumberSuffix='%'";
  }

  $formatStr = DSRmeasures_get_format($db, $dsSchema, $measureYID);
  @formats = split(',', $formatStr);
  $yDecimals = "yDecimals='$formats[0]'";
  $yFormatNumber = "yFormatNumber='$formats[1]'";
  $yNumberPrefix = "";
  $yNumberSuffix = "";
  if ($formats[2] == 1)
  {
    $yNumberPrefix = "yNumberPrefix='\$'";
  }
  elsif ($formats[2] == 2)
  {
    $yNumberSuffix = "yNumberSuffix='%'";
  }

  # ------- end of design parsing ----------


  #print out the XML chart info
  print <<XML_LABEL;
<chart
  theme='zune'
  animation='0'

  $bgColor
  $canvasBgColor
  canvasBgAlpha='0'

  $showBorder
  $borderColor
  $borderThickness

  $showLegend
  $legendPosition
  $legendItemFontColor
  $legendItemFont
  $legendItemFontSize

  caption='$caption'
  $captionFontColor
  $captionAlignment
  $captionFontSize
  $captionFont

  subcaption='$subcaption'

  xAxisName='$xAxisName'
  $xAxisNameFontSize
  yAxisName='$yAxisName'
  $yAxisNameFontSize
  useRoundEdges='$useRoundEdges'
  numVDivLines='10'
  divLineAlpha='30'
  yAxisValuesPadding ='10'

  $showLabels
  $labelFontColor
  $labelDisplay
  $labelStep
  $labelFontSize
  $labelFont

  $showValues
  $valueFontColor
  $formatNumberScale
  $placeValuesInside
  $valueFontSize
  $valueFont
  $valueBgColor
  $valueBgAlpha

  showRegressionLine='$showRegressionLine'

  setAdaptiveYMin='1'

  $xDecimals
  $xFormatNumber
  $xNumberPrefix
  $xNumberSuffix
  $yDecimals
  $yFormatNumber
  $yNumberPrefix
  $yNumberSuffix

  anchorRadius='5'
  anchorBgColor='$colors[0]'
  use3dlighting='0'>
XML_LABEL

  #print out our dimension as the series name
  if ($graph_x eq "p")
  {
    print("<dataset seriesname=\"Products\">\n");
  }
  elsif ($graph_x eq "g")
  {
    print("<dataset seriesname=\"Geographies\">\n");
  }
  elsif ($graph_x eq "t")
  {
    print("<dataset seriesname=\"Time Periods\">\n");
  }

  #fetch a hash of our graphed item names
  %setNames = dsr_get_item_name_hash($db, $dsSchema, $graph_x);

  #escape any special characters in our category & set names
  foreach $id (keys %setNames)
  {
    $setNames{$id} =~ s/\'//g;
  }

  #create an array of the categories we need to display, and go ahead and set
  #up our "single selection" dimension IDs while we're at it
  if ($graph_x eq "p")
  {
    @setIDs = split(/,/, $productIDstring);

    $q_geography = $db->quote($geographyIDstring);
    $q_time = $db->quote($timeIDstring);
  }
  elsif ($graph_x eq "g")
  {
    @setIDs = split(/,/, $geographyIDstring);

    $q_product = $db->quote($productIDstring);
    $q_time = $db->quote($timeIDstring);
  }
  elsif ($graph_x eq "t")
  {
    @setIDs = split(/,/, $timeIDstring);

    $q_product = $db->quote($productIDstring);
    $q_geography = $db->quote($geographyIDstring);
  }

  #if the user has specified a slicer, let's add it to the where clause
  @slicers = split(',', $slicersStr);
  foreach $slicer (@slicers)
  {
    $slicer =~ m/(PSEG_\d+):(\d+)/;
    $segID = $1;
    $segmentID = $2;

    if ($segmentID > 0)
    {
      $tmp = "SMT_$segmentID";
      $segmentName = $db->quote($setNames{$tmp});
      $whereClause .= " AND $segID = $segmentName";
    }
  }


  ### chart filtering implementation ###

  $filterMeas1 = reports_get_style($graphDesign, "filterMeas1");
  $filterOp1 = reports_get_style($graphDesign, "filterOp1");
  $filterNum1 = reports_get_style($graphDesign, "filterNum1");

  if (($filterMeas1 > 0) && (length($filterNum1) > 0))
  {
    $filterMeas1 = "measure_" . $filterMeas1;

    if ($filterOp1 eq "gt")
    {
      $filterOp1 = ">";
      $whereClause .= " AND $filterMeas1 > $filterNum1";
    }
    elsif ($filterOp1 eq "lt")
    {
      $filterOp1 = "<";
      $whereClause .= " AND $filterMeas1 < $filterNum1";
    }
    elsif ($filterOp1 eq "eq")
    {
      $filterOp1 = "=";
      $whereClause .= " AND $filterMeas1 = $filterNum1";
    }

    elsif ($filterOp1 eq "top")
    {

      #gate the number of top items we're going to show
      if ($filterNum1 > 5000)
      {
        $filterNum1 = 5000;
      }
      elsif ($filterNum1 < 0)
      {
        $filterNum1 = 1;
      }
      $whereClause .= " ORDER BY $filterMeas1 DESC LIMIT $filterNum1";
    }

    elsif ($filterOp1 eq "bottom")
    {

      #gate the number of top items we're going to show
      if ($filterNum1 > 5000)
      {
        $filterNum1 = 5000;
      }
      elsif ($filterNum1 < 0)
      {
        $filterNum1 = 1;
      }
      $whereClause .= " ORDER BY $filterMeas1 ASC LIMIT $filterNum1";
    }
  }

  ### end chart filtering code ###


  #build up the SQL string we're going to use for selections
  $itemStr = "";
  foreach $item (@setIDs)
  {
    $itemStr .= "'$item',";
  }
  chop($itemStr);

  #set up our item/value query based on the display dimension
  if ($graph_x eq "p")
  {
    $query = "SELECT product, $measureX, $measureY, $measureZ \
        FROM $dsSchema.$rptCube \
        WHERE product IN ($itemStr) AND geography=$q_geography AND time=$q_time $whereClause";
  }
  elsif ($graph_x eq "g")
  {
    $query = "SELECT geography, $measureX, $measureY, $measureZ \
        FROM $dsSchema.$rptCube \
        WHERE product=$q_product AND geography IN ($itemStr) AND time=$q_time $whereClause";
  }
  elsif ($graph_x eq "t")
  {
    $query = "SELECT time, $measureX, $measureY, $measureZ \
        FROM $dsSchema.$rptCube \
        WHERE product=$q_product AND geography=$q_geography AND time IN ($itemStr) $whereClause";
  }

  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  $colorIdx = 0;
  while (($item, $valueX, $valueY, $valueZ) = $dbOutput->fetchrow_array)
  {
    $color = $colors[$colorIdx];

    #see if we have a custom color, and use it in preference to the default
    $name = "color_" . $item;
    $customColor = reports_get_style($graphDesign, $name);
    if (length($customColor) > 6)
    {
      $color = $customColor;
    }

    print(" <set name='$setNames{$item}' color='$color' x='$valueX' y='$valueY' z='$valueZ' />\n");

    $colorIdx++;
    if ($colorIdx > 47)
    {
      $colorIdx = 0;
    }
  }

  #finally, generate an "All Others" slice if requested by user
  if ($allOthers eq "true")
  {

    #build list of all items already displayed
    $displayed = "";
    foreach $itemID (keys %results)
    {
      $displayed .= "'$itemID',";
    }
    chop($displayed);

    if ($dim eq "p")
    {
      $query = "SELECT SUM($measureX), SUM($measureY), SUM($measureZ) \
          FROM $dsSchema.$rptCube \
          WHERE product NOT IN ($itemStr) AND geography=$q_geography AND time=$q_time";
    }
    elsif ($dim eq "g")
    {
      $query = "SELECT SUM($measureX), SUM($measureY), SUM($measureZ) \
          FROM $dsSchema.$rptCube \
          WHERE product =$q_product AND geography NOT IN ($itemStr) AND time=$q_time";
    }
    elsif ($dim eq "t")
    {
      $query = "SELECT SUM($measureX), SUM($measureY), SUM($measureZ) \
          FROM $dsSchema.$rptCube \
          WHERE product=$q_product AND geography=$q_geography AND time NOT IN ($itemStr)";
    }
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($valueX, $valueY, $valueZ) = $dbOutput->fetchrow_array;

    $color = $colors[$colorIdx];
    print(" <set name='All Others' color='$color' x='$valueX' y='$valueY' z='$valueZ' />\n");
  }

  print("</dataset>\n");
  print("</chart>\n");


#EOF
