#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepRecipes;
use Lib::PrepUtils;
use Lib::WebUtils;


#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $flowID = $q->param('f');
  $jobID = $q->param('j');
  $colID = $q->param('col');

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #make sure we have write privs for this data flow
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this data flow.");
  }

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #build name of column containing the text
  $column = "column_" . $colID;

  #get the name of the column containing the cells to be transformed
  $query = "SELECT name FROM $masterColTable WHERE ID=$colID";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($colName) = $dbOutput->fetchrow_array;

  print <<END_HTML;
<SCRIPT>
function checkForm(form)
{
  \$("#btn-submit").prop('disabled', true);
  \$("#btn-submit").text("Please Wait...");
  return(true);
}
</SCRIPT>

<FORM METHOD="post" ACTION="transformProgress.cld" onsubmit="return checkForm(this);">
<INPUT TYPE="hidden" NAME="f" VALUE="$flowID">
<INPUT TYPE="hidden" NAME="j" VALUE="$jobID">
<INPUT TYPE="hidden" NAME="col" VALUE="$colID">
<INPUT TYPE="hidden" NAME="a" VALUE="TRANS-CELL-REPLACE">

<DIV CLASS="modal-dialog modal-lg">
  <DIV CLASS="modal-content">

    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Replace Cell Values</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">

      <DIV CLASS="row">
        <DIV CLASS="col-auto mt-2">
          If
        </DIV>
        <DIV CLASS="col-auto">
          <SELECT ID="checkCol" NAME="checkCol" CLASS="form-select">
END_HTML

  #get IDs of columns in display order
  @orderedCols = prep_flow_order_columns($prepDB, $flowID, $jobID);
  $colOrder = join(',', @orderedCols);

  $query = "SELECT ID, name FROM $masterColTable ORDER BY FIELD(ID, $colOrder)";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;

  while (($id, $name) = $dbOutput->fetchrow_array)
  {
    print("   <OPTION VALUE='$name'>$name</OPTION>\n");
  }

  print <<END_HTML;
          </SELECT>
        </DIV>

<SCRIPT>
function handleMatchChange()
{
  let matchOp = document.getElementById('matchOp').value;

  if (matchOp == "is blank")
  {
    document.getElementById('matchText').style.visibility = 'hidden';
  }
  else
  {
    document.getElementById('matchText').style.visibility = 'visible';
  }
}
</SCRIPT>

        <DIV CLASS="col-auto">
          <SELECT ID="matchOp" NAME="matchOp" CLASS="form-select" onChange="handleMatchChange();">
            <OPTION VALUE="begins">begins with</OPTION>
            <OPTION VALUE="ends">ends with</OPTION>
            <OPTION VALUE="contains">contains</OPTION>
            <OPTION VALUE="is">is exactly</OPTION>
            <OPTION VALUE="does not begin">does not begin with</OPTION>
            <OPTION VALUE="does not end">does not end with</OPTION>
            <OPTION VALUE="does not contain">does not contain</OPTION>
            <OPTION VALUE="is blank">is blank</OPTION>
            <OPTION VALUE="matches">matches</OPTION>
          </SELECT>
        </DIV>

        <DIV CLASS="col-auto">
          <INPUT TYPE="text" ID="matchText" NAME="matchText" CLASS="form-control" STYLE="width:20em;">
        </DIV>
      </DIV>

      <DIV CLASS="row mt-2">
        <DIV CLASS="col-auto">
          replace the value of $colName with the corresponding value from
        </DIV>
      </DIV>

      <DIV CLASS="row mt-2">
        <DIV CLASS="col-auto">
          <SELECT ID="replaceCol" NAME="replaceCol" CLASS="form-select">
END_HTML

  #get IDs of columns in display order
  @orderedCols = prep_flow_order_columns($prepDB, $flowID, $jobID);
  $colOrder = join(',', @orderedCols);

  $query = "SELECT ID, name FROM $masterColTable ORDER BY FIELD(ID, $colOrder)";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;

  while (($id, $name) = $dbOutput->fetchrow_array)
  {
    print(" <OPTION VALUE='$name'>$name</OPTION>\n");
  }

  print <<END_HTML;
          </SELECT>
        </DIV>
      </DIV>

    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="submit" ID="btn-submit"><I CLASS="bi bi-pencil-square"></I> Apply</BUTTON>
   </DIV>

  </DIV>
</DIV>

</FORM>

END_HTML

#EOF
