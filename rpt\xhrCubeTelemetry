#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DSRUtils;
use Lib::DataSources;
use Lib::Reports;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $rptID = $q->param('c');
  $runID = $q->param('i');

  $db = KAPutil_connect_to_database();

  #make sure we have edit privs for this report
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs eq "N")
  {
    exit_error("You don't have privileges to view telemetry for this report.");
  }

  #get the telemetry data from the database
  $query = "SELECT telemetry FROM audit.telemetry_cubes \
      WHERE ID=$runID AND cubeID=$rptID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($telemetry) = $dbOutput->fetchrow_array;

  print <<END_HTML;
<DIV CLASS="modal-header">
  <H5 CLASS="modal-title">Report Refresh Telemetry</H5>
  <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
</DIV>

<DIV CLASS="modal-body">
  <DIV STYLE="height:500px;overflow:auto;">
    <PRE STYLE="font-size:12px; background-color:white; border:0px;">
$telemetry
    </PRE>
  </DIV>

  <DIV CLASS="modal-footer">
    <BUTTON CLASS="btn btn-primary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-check-lg"></I> Done</BUTTON>
  </DIV>

</DIV>
END_HTML

#EOF
