#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::Social;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: User Information</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
   <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="home.cld">Administration</A></LI>
   <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="userMgmt.cld">User Management</A></LI>
   <LI CLASS="breadcrumb-item active">Modify User</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if ((length($email) < 1) || ($acctType < 1))
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $user = $q->param('u');
  $userEmail = $q->param('email');
  $userFirst = $q->param('first');
  $userLast = $q->param('last');
  $userPass = $q->param('pass');
  $userOrg = $q->param('org');
  $userAcctType = $q->param('acctType');
  $licensePrep = $q->param('licensePrep');
  $licenseAInsights = $q->param('licenseAInsights');
  $licenseForecast = $q->param('licenseForecast');
  $betaTester = $q->param('betaTester');

  $licensePrep = defined($licensePrep) ? 1 : 0;
  $licenseAInsights = defined($licenseAInsights) ? 1 : 0;
  $licenseForecast = defined($licenseForecast) ? 1 : 0;
  $betaTester = defined($betaTester) ? 1 : 0;

  #connect to the database
  $db = KAPutil_connect_to_database();

  print_html_header();

  #if we aren't an admin, we can only edit Viewers
  if (($acctType < 5) && ($userAcctType > 0))
  {
    exit_error("You don't have privileges to create or edit this type of user");
  }

  #add the user's organization if it's one we haven't seen before
  $q_userOrg = $db->quote($userOrg);
  $query = "SELECT ID FROM app.orgs WHERE name LIKE $q_userOrg";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($userOrgID) = $dbOutput->fetchrow_array;
  if ($userOrgID < 1)
  {
    $query = "INSERT INTO app.orgs (name) VALUES ($q_userOrg)";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);
    $userOrgID = $db->{q{mysql_insertid}};
  }

  $q_email = $db->quote($userEmail);
  $q_first = $db->quote($userFirst);
  $q_last = $db->quote($userLast);
  $q_pass = $db->quote($userPass);

  #if we're editing an existing user, update the info in the database
  if ($user =~ m/(\d+)/)
  {
    $query = "UPDATE users \
        SET email=$q_email, first=$q_first, last=$q_last, orgID=$userOrgID, acctType=$userAcctType, licensePrep=$licensePrep, licenseAInsights=$licenseAInsights, licenseForecast=$licenseForecast, betaTester=$betaTester \
        WHERE ID=$user";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    #if we were passed a new password
    if ($userPass ne "XXXXXXXXX")
    {
      $query = "UPDATE users SET password=$q_pass WHERE ID=$user";
      $status = $db->do($query);
      KAPutil_handle_db_err($db, $status, $query);
    }
  }

  #else we're adding a new user
  else
  {
    $query = "INSERT INTO users (email, first, last, password, orgID, acctType, licensePrep, licenseAInsights, licenseForecast, betaTester) \
      VALUES ($q_email, $q_first, $q_last, $q_pass, $userOrgID, $userAcctType, $licensePrep, $licenseAInsights, $licenseForecast, $betaTester)";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    #if the new user is an analyst, insert a "welcome" message into their feed
    if ($userAcctType > 0)
    {
      $newUserID = $db->{q{mysql_insertid}};
      Social_feed_add_item($db, $newUserID, 0, 0, 0, "info", "koala_welcome", "");
    }
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-10 col-lg-7 col-xl-6"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">User Information Saved</DIV>
        <DIV CLASS="card-body">

          User information for $userFirst $userLast ($userEmail) saved.

          <P>&nbsp;</P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='/app/admin/userMgmt.cld'"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
