#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::Reports;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $rptID = $q->param('rptID');
  $visID = $q->param('v');
  $valueFontColor = $q->param('valueFontColor');
  $valueBgColor = $q->param('valueBgColor');
  $valueAlternateFontColor = $q->param('valueAlternateFontColor');
  $valueAlternateBgColor = $q->param('valueAlternateBgColor');
  $valueWrapDimension = $q->param('valueWrapDimension');
  $valueWrap = $q->param('valueWrap');
  $valueFontSize = $q->param('valueFontSize');
  $valueFont = $q->param('valueFont');

  #fix up the CGI parameters from the submitted form
  if (defined($valueWrapDimension))
  {
    $valueWrapDimension = ($valueWrapDimension eq "false") ? "0" : "1";
  }
  if (defined($valueWrap))
  {
    $valueWrap = ($valueWrap eq "false") ? "0" : "1";
  }

  $valueFontColor = "#" . $valueFontColor;
  $valueBgColor = "#" . $valueBgColor;
  $valueAlternateFontColor = "#" . $valueAlternateFontColor;
  $valueAlternateBgColor = "#" . $valueAlternateBgColor;

  $db = KAPutil_connect_to_database();

  #make sure we have edit privs for this report
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this report.");
  }

  #get the chart title details from the database
  $query = "SELECT design FROM visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($design) = $dbOutput->fetchrow_array;


  ########################################################################
  #
  # This code block is called on submit to save the changes
  #

  if (defined($valueFontSize))
  {
    $design = reports_remove_style($design, "cellcolor");
    $design = reports_remove_style($design, "cellbg");
    $design = reports_remove_style($design, "cellband");
    $design = reports_set_style($design, "valueFontColor", $valueFontColor);
    $design = reports_set_style($design, "valueBgColor", $valueBgColor);
    $design = reports_set_style($design, "valueAlternateFontColor", $valueAlternateFontColor);
    $design = reports_set_style($design, "valueAlternateBgColor", $valueAlternateBgColor);
    $design = reports_set_style($design, "valueWrapDimension", $valueWrapDimension);
    $design = reports_set_style($design, "valueWrap", $valueWrap);
    $design = reports_set_style($design, "valueFontSize", $valueFontSize);

    if ($valueFont eq "Helvetica")
    {
      $design = reports_remove_style($design, "valueFont");
    }
    else
    {
      $design = reports_set_style($design, "valueFont", $valueFont);
    }

    $q_design = $db->quote($design);
    $query = "UPDATE visuals SET design = $q_design WHERE ID=$visID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    $cubeName = cube_id_to_name($db, $rptID);
    $dsID = cube_get_ds_id($db, $rptID);
    $dsName = ds_id_to_name($db, $dsID);

    utils_audit($db, $userID, "Changed table value formatting", $dsID, $rptID, 0);
    $activity = "$first $last changed table value formatting for $cubeName in $dsName";
    utils_slack($activity);

    exit;
  }


  #########################################################################

  #extract settings from design string

  #handle legacy cellcolor setting
  $cellcolor = reports_get_style($design, "cellcolor");
  if ($cellcolor eq "white")
  {
    $valueFontColor = "#ffffff";
    $valueAlternateFontColor = "#ffffff";
  }
  elsif ($cellcolor eq "black")
  {
    $valueFontColor = "#000000";
    $valueAlternateFontColor = "#000000";
  }
  elsif ($cellcolor =~ m/^#\d+/)
  {
    $valueFontColor = $cellcolor;
    $valueAlternateFontColor = $cellcolor;
  }
  else
  {
    $valueFontColor = reports_get_style($design, "valueFontColor");
    $valueAlternateFontColor = reports_get_style($design, "valueAlternateFontColor");
  }

  #handle legacy cellbg/cellband settings
  $cellbg = reports_get_style($design, "cellbg");
  if ($cellbg eq "white")
  {
    $valueBgColor = "#ffffff";
  }
  elsif ($cellbg =~ m/^#/)
  {
    $valueBgColor = $cellbg;
  }
  else
  {
    $valueBgColor = reports_get_style($design, "valueBgColor");
  }

  $cellband = reports_get_style($design, "cellband");
  if ($cellband eq "white")
  {
    $valueAlternateBgColor = "#ffffff";
  }
  elsif ($cellbg =~ m/^#/)
  {
    $valueAlternateBgColor = $cellbg;
  }
  else
  {
    $valueAlternateBgColor = reports_get_style($design, "valueAlternateBgColor");
  }

  $valueFontSize = reports_get_style($design, "valueFontSize");
  $valueFont = reports_get_style($design, "valueFont");
  $valueWrapDimension = reports_get_style($design, "valueWrapDimension");
  $valueWrap = reports_get_style($design, "valueWrap");

  #set appropriate defaults
  if (length($valueFontColor) < 7)
  {
    $valueFontColor = "#333333";
  }
  if (length($valueBgColor) < 7)
  {
    $valueBgColor = "#ffffff";
  }
  if (length($valueAlternateFontColor) < 7)
  {
    $valueAlternateFontColor = "#333333";
  }
  if (length($valueAlternateBgColor) < 7)
  {
    $valueAlternateBgColor = "#EFEFEF";
  }
  if (length($valueWrapDimension) < 1)
  {
    $valueWrapDimension = "";
  }
  if (length($valueWrap) < 1)
  {
    $valueWrap = "";
  }
  if ($valueFontSize < 3)
  {
    $valueFontSize = "11";
  }
  if (length($valueFont) < 3)
  {
    $valueFont = "Helvetica";
  }

  #set up things for HTML form display
  if ($valueWrapDimension eq "1")
  {
    $valueWrapDimension = "CHECKED";
  }
  if ($valueWrap eq "1")
  {
    $valueWrap = "CHECKED";
  }


  ##########################################################
  #
  # Everything after this point is called to display the table layout dialog
  #

  print <<END_HTML;
<SCRIPT>
function submitForm()
{
  let valueFontColor = document.getElementById('valueFontColor').value;
  let valueBgColor = document.getElementById('valueBgColor').value;
  let valueAlternateFontColor = document.getElementById('valueAlternateFontColor').value;
  let valueAlternateBgColor = document.getElementById('valueAlternateBgColor').value;
  let valueWrapDimension = \$("#valueWrapDimension").prop("checked");
  let valueWrap = \$("#valueWrap").prop("checked");
  let valueFontSize = document.getElementById('valueFontSize').value;
  let valueFont = document.getElementById('valueFont').value;

  //knock # off of color strings
  valueFontColor = valueFontColor.substr(1);
  valueBgColor = valueBgColor.substr(1);
  valueAlternateFontColor = valueAlternateFontColor.substr(1);
  valueAlternateBgColor = valueAlternateBgColor.substr(1);

  let url = "xhrTableValues.cld?rptID=$rptID&v=$visID&valueFontSize=" + valueFontSize +
      "&valueFont=" + valueFont + "&valueFontColor=" + valueFontColor +
      "&valueBgColor=" + valueBgColor +
      "&valueAlternateFontColor=" + valueAlternateFontColor  +
      "&valueAlternateBgColor=" + valueAlternateBgColor +
      "&valueWrapDimension=" + valueWrapDimension + "&valueWrap=" + valueWrap;

  \$.get(url, function(data, status)
  {
    location.href = "display.cld?rpt=$rptID&v=$visID";
  });
}



function revertDefaults()
{
  document.getElementById('valueFontColor').value = "#333333";
  document.getElementById('valueBgColor').value = "#ffffff";
  document.getElementById('valueAlternateFontColor').value = "#333333";
  document.getElementById('valueAlternateBgColor').value = "#efefef";
  document.getElementById('valueWrapDimension').checked = false;
  document.getElementById('valueWrap').checked = false;
  document.getElementById('valueFontSize').value = 11;
  document.getElementById('valueFont').value = "Helvetica";
}
</SCRIPT>


<DIV CLASS="modal-dialog" STYLE="overflow-y: initial !important;">
  <DIV CLASS="modal-content">
    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Table Values</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">
      <FORM>
      <TABLE CLASS="mx-auto">
        <TR>
          <TD STYLE="text-align:right;">
            Font color:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="color" NAME="valueFontColor" ID="valueFontColor" STYLE="width:3em;" VALUE="$valueFontColor">
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Background color:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="color" NAME="valueBgColor" ID="valueBgColor" STYLE="width:3em;" VALUE="$valueBgColor">
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Alternate font color:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="color" NAME="valueAlternateFontColor" ID="valueAlternateFontColor" STYLE="width:3em;" VALUE="$valueAlternateFontColor">
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Alternate background color:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="color" NAME="valueAlternateBgColor" ID="valueAlternateBgColor" STYLE="width:3em;" VALUE="$valueAlternateBgColor">
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Word wrap dimensions:&nbsp;
          </TD>
          <TD>
            <DIV CLASS="form-check form-switch">
              <INPUT TYPE="checkbox" CLASS="form-check-input" NAME="valueWrapDimension" ID="valueWrapDimension" data-offstyle="secondary" $valueWrapDimension>
              <LABEL CLASS="form-check-label" FOR="valueWrapDimension">&nbsp;</LABEL>
            </DIV>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Word wrap values:&nbsp;
          </TD>
          <TD>
            <DIV CLASS="form-check form-switch">
              <INPUT TYPE="checkbox" CLASS="form-check-input" NAME="valueWrap" ID="valueWrap" data-offstyle="secondary" $valueWrap>
              <LABEL CLASS="form-check-label" FOR="valueWrap">&nbsp;</LABEL>
            </DIV>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Text size:&nbsp;
          </TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="number" NAME="valueFontSize" ID="valueFontSize" STYLE="width:5em;" VALUE="$valueFontSize">
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">
            Font family:&nbsp;
          </TD>
          <TD>
            <SELECT CLASS="form-select" NAME="valueFont" ID="valueFont">
              <OPTION VALUE="Arial">Arial</OPTION>
              <OPTION VALUE="Arial Black">Arial Black</OPTION>
              <OPTION VALUE="Comic Sans MS">Comic Sans MS</OPTION>
              <OPTION VALUE="Courier New">Courier New</OPTION>
              <OPTION VALUE="Helvetica">Helvetica</OPTION>
              <OPTION VALUE="Impact">Impact</OPTION>
              <OPTION VALUE="Tahoma">Tahoma</OPTION>
              <OPTION VALUE="Times New Roman">Times New Roman</OPTION>
              <OPTION VALUE="Verdana">Verdana</OPTION>
            </SELECT>
            <SCRIPT>
              \$("select#valueFont").val("$valueFont");
            </SCRIPT>
          </TD>
        </TR>

        <TR>
          <TD COLSPAN="2" STYLE="text-align:center;">
            <P></P>
            <A HREF="#" CLASS="text-decoration-none" onClick="revertDefaults()">Revert to default</A>
          </TD>
        </TR>
      </TABLE>
      </FORM>

    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="submitForm()"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML


#EOF
