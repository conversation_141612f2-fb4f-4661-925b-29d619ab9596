#!/usr/bin/perl

use lib "/opt/apache/app/";


use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::DataSources;
use Lib::KoalaConfig;
use Lib::DSRCreateUpdate;
use Lib::DSRMeasures;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: New Data Source</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>


<SCRIPT>
let statusTimer = setInterval(function(){displayStatus()}, 5000);
let started = 0;
let emptyCounts = 0;

function displayStatus()
{
  let url = '/app/dsr/xhrDSstatus.cld?ds=$dsID';

  \$.get(url, function(data, status)
  {
    statusText = data;

    document.getElementById('progressDiv').innerHTML = statusText;

    if (statusText.search('ERROR') >= 0)
    {
      clearInterval(statusTimer);
      \$('#btnAsync').text('Cancel');
      document.getElementById('progress-bar').style.visibility = 'hidden';
    }

    if (statusText.length > 2)
    {
      started = 1;
    }
    else
    {
      emptyCounts++;
    }

    if (((statusText.length < 2) && (started == 1)) || (emptyCounts > 2))
    {
      clearInterval(statusTimer);
      location.href='$Lib::KoalaConfig::kapHostURL/app/dsr/display.cld?ds=$dsID';
    }
  });
}
</SCRIPT>

</HEAD>

<BODY onLoad='displayStatus()'>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="$Lib::KoalaConfig::kapHostURL/app/dsr/main.cld">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item active">New Data Source</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get the CGI input variables
  $key = $q->param('key');
  $tabs = $q->param('tabs');
  $layout = $q->param('layout');
  $dsID = $q->param('dsID');
  $pmatch = $q->param('pmatch');
  $gmatch = $q->param('gmatch');
  $tmatch = $q->param('tmatch');
  $dsName = $q->param('dsName');
  $dsType = $q->param('dsType');
  $dsDescription = $q->param('dsDescription');
  $options = $q->param('options');
  $dontOverwriteNames = $q->param('dontOverwriteNames');
  $normalizationScript = $q->param('scriptID');

  #our unique key for this run is the userID and the key we were passed
  $uniqueKey = "$userID.$key";

  #connect to database
  $db = KAPutil_connect_to_database();

  #if we were just requested to go into async mode
  if (defined($dsID))
  {
    $userName = utils_userID_to_name($db, $userID);
    $query = "UPDATE app.jobs SET opInfo = 'Update|$userName|$email' \
        WHERE dsID=$dsID AND opInfo LIKE 'Update%'";
    $db->do($query);
    print("Location: $Lib::KoalaConfig::kapHostURL/app/home.cld\n\n");
    exit;
  }

  #set the initial values for the data source import/update options
  $appendUPC = 0;
  $compressWS = 0;
  if ($options =~ m/appendUPC/)
  {
    $appendUPC = 1;
  }
  if ($options =~ m/compressWS/)
  {
    $compressWS = 1;
  }

  #make CGI input SQL-friendly
  $q_dsName = $db->quote($dsName);
  $q_dsType = $db->quote($dsType);
  $q_dsDescription = $db->quote($dsDescription);

  #insert an entry in the master datasource table
  $query = "INSERT INTO dataSources \
      (userID, name, type, lastUpdate, description, appendUPC, compressWS) \
      VALUES ($userID, $q_dsName, $q_dsType, NOW(), $q_dsDescription, $appendUPC, $compressWS)";
  $db->do($query);

  #get the unique ID for this data source
  $dsID = $db->{q{mysql_insertid}};

  print_html_header();

  #log the data source creation to Slack
  $activity = "$first $last creating data source $dsName, layout: $layout, Excel tabs: $tabs, options: $options";
  utils_slack($activity);

  #fork a new process to do the actual import in the background
  $SIG{CHLD} = "IGNORE";
  if ($pid = fork())
  {
    #parent process

    print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-11 col-md-8 col-lg-6 col-xl-5"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Creating New Data Source</DIV>
        <DIV CLASS="card-body">

          <P>&nbsp;</P>
          <DIV CLASS="progress">
            <DIV ID="progress-bar" CLASS="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" STYLE="width:100%;"></DIV>
          </DIV>

          <P>&nbsp;</P>
          <DIV CLASS="text-center">
            <DIV ID="progressDiv">Beginning data load</DIV>
          </DIV>

          <P>&nbsp;</P>
          Koala can finish creating your data source in the background, and notify you when it's done. While it's working, you can work on other things.

          <P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" ID="btnAsync" onClick="location.href='?dsID=$dsID'"><I CLASS="bi bi-speedometer"></I> Be More Productive</BUTTON>
          </DIV>
        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

    print_html_footer();
  }

  else
  {
    #child process

    #let Apache know not to wait on the child process
    close(STDIN);
    close(STDOUT);

    #redirect STDERR to the Koala error log
    close(STDERR);
    open(STDERR, ">>/opt/apache/htdocs/tmp/koala_error.log");

    $excelTabs = "\"$tabs\"";

    #put our error messages & output in the Apache logs
    open(STDERR, ">>/opt/apache/htdocs/tmp/dsimport_$dsID.log") or die("Unable to open STDERR, $!");
    open(STDOUT, ">>/opt/apache/htdocs/tmp/dsimport_$dsID.log") or die ("Unable to open STDOUT, $!");

    #connect to database
    $db = KAPutil_connect_to_database();

    #ds_create_new($childDB, $dsID, $userID, $layout, $key, $excelTabs, $options, $normalizationScript);
    ds_create_update($db, $userID, $dsID, 1, $layout, $pmatch, $gmatch, $tmatch, $key, $excelTabs, $options, $dontOverwriteNames, $normalizationScript);
  }


#EOF
