#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName Data Sources</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
<SCRIPT SRC="/jsgrid/jsgrid.min.js"></SCRIPT>
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<LINK HREF="/jsgrid/jsgrid.min.css" REL="stylesheet">
<LINK HREF="/jsgrid/jsgrid.min.css" REL="stylesheet">
<LINK HREF="/jsgrid/jsgrid-theme.min.css" REL="stylesheet">

<STYLE>
div.grid th
{
  background: #e2e3e5 !important;
}

.selected-row > td
{
  background: #c4e2ff !important;
  border-color: #c4e2ff;
}

.active-row > td
{
  background: #dadada !important;
  border-color: #dadada;
}
</STYLE>

<SCRIPT>
let selectedDS = 0;

let gridHeight = window.innerHeight - 200;
if (gridHeight < 250)
{
  gridHeight = 250;
}

let statusTimer = setInterval(function(){refresh_display()}, 120000);


\$(document).ready(function()
{
  \$('#dsGrid').jsGrid(
  {
    width: '100%',
    height: gridHeight,
    $jsFiltering
    sorting: true,
    autoload: true,
    loadIndication: true,

    controller:
    {
      loadData: function(filter)
      {
        let data = \$.Deferred();
        let uriArgs = "$filterURI";

        if (filter.Name)
        {
          if (filter.Name.length > 0)
          {
            uriArgs += '&n=' + filter.Name;
          }
          if (filter.Type.length > 0)
          {
            uriArgs += '&t=' + filter.Type;
          }
          if (filter.Owner.length > 0)
          {
            uriArgs += '&o=' + filter.Owner;
          }
          if (filter["Last Update"].length > 0)
          {
            uriArgs += '&u=' + filter["Last Update"];
          }
          if (filter.Description.length > 0)
          {
            uriArgs += '&d=' + filter.Description;
          }
        }

        \$.ajax(
        {
          type: 'GET',
          contentType: 'application/json; charset=utf-8',
          url: 'ajaxDataSourceList.cld?' + uriArgs,
          dataType: 'json'
        }).done(function(response)
        {
          data.resolve(response);
        });
        return data.promise();
      }
    },

    onDataLoaded: function()
    {
      if ($dsID > 0)
      {
        let grid = \$('#dsGrid').jsGrid('option', 'data');
        let isSelectedDS = (element) => element.ID == $dsID;
        let idx = grid.findIndex(isSelectedDS);

        if (idx < 1)
        {
          return;
        }

        \$selectedRow = \$('#dsGrid').jsGrid('rowByItem', grid[idx]).closest('tr');
        \$selectedRow[0].scrollIntoView();
        \$selectedRow.addClass('selected-row');
        grid[idx].selected = 1;
        selectedDS = $dsID;
      }
    },

    rowClick: function(args)
    {
      selectedDS = args.item.ID;

      \$('#dsGrid tr').removeClass('selected-row');

      \$selectedRow = \$(args.event.target).closest('tr');
      \$selectedRow.addClass('selected-row');
      selectedRowIdx = \$selectedRow[0].rowIndex;
    },

    rowDoubleClick: function(args)
    {
      selectedDS = args.item.ID;

      location.href='/app/dsr/display.cld?ds=' + selectedDS;
    },

    rowClass: function(item, itemIndex)
    {
      if (item.active == 1)
      {
        return 'active-row';
      }
    },

    fields: [
      {name: 'ID', type: 'number', visible: false},
      {name: 'Name', type: 'text', width: 275},
      {name: 'Type', type: 'text', width: 85},
      {name: 'Last Update', type: 'text', width: 130},
      {name: 'Owner', type: 'text', width:135},
      {name: 'Description', type: 'text', width:225}
    ]

  });
});



function refresh_display()
{
  \$('#dsGrid').jsGrid('render').done(function()
  {
    if (selectedDS > 0)
    {
      let grid = \$('#dsGrid').jsGrid('option', 'data');

      \$selectedRow = \$('#dsGrid').jsGrid('rowByItem',
          grid[selectedRowIdx]).closest('tr');
      \$selectedRow[0].scrollIntoView();
      \$selectedRow.addClass('selected-row');
    }
  });
}



function reset_refresh()
{
  clearInterval(statusTimer);
  statusTimer = setInterval(function(){refresh_display()}, 120000);
}



function open_selected_datasource()
{
  if (selectedDS < 1)
  {
    return;
  }

  location.href='/app/dsr/display.cld?ds=' + selectedDS;
}



function update_datasource()
{
  if (selectedDS < 1)
  {
    return;
  }

  location.href='/app/dsr/updateDSSource.cld?r=m&ds=' + selectedDS;
}



function ds_properties()
{
  if (selectedDS < 1)
  {
    return;
  }

  location.href='properties.cld?r=m&ds=' + selectedDS;
}



function ds_sharing()
{
  if (selectedDS < 1)
  {
    return;
  }

  location.href='/app/dsr/accessControl.cld?r=m&ds=' + selectedDS;
}



function ds_history()
{
  if (selectedDS < 1)
  {
    return;
  }

  location.href='/app/dsr/history.cld?r=m&ds=' + selectedDS;
}



function ds_statistics()
{
  if (selectedDS < 1)
  {
    return;
  }

  location.href='/app/dsr/statistics.cld?r=m&ds=' + selectedDS;
}



function ds_copy()
{
  if (selectedDS < 1)
  {
    return;
  }

  location.href='copyDS.cld?r=m&ds=' + selectedDS;
}



function delete_datasource()
{
  if (selectedDS < 1)
  {
    return;
  }

  location.href='/app/dsr/deleteDSconfirm.cld?r=m&ds=' + selectedDS;
}
</SCRIPT>

</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-0">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item active">Data Sources</LI>
  </OL>
</NAV>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  $filter = $q->param('f');
  $dsID = $q->param('ds');

  if (!defined($dsID))
  {
    $dsID = 0;
  }

  #if we weren't passed a filter on the command line, pull the session filter
  if (!defined($filter))
  {
    $filter = $session->param(dsr_UserFilter);
  }

  #if there isn't one in the cookie, either, the default is to filter
  if (!defined($filter))
  {
    $filter = 1;
  }

  if ($filter == 1)
  {
    $filterCbox = "?f=0";
    $filterURI = "user=1";
    $filterChecked = "CHECKED";
    $session->param(dsr_UserFilter, "1");
    $jsFiltering = "";
  }
  else
  {
    $filterCbox = "?f=1";
    $filterURI = "";
    $filterChecked = "";
    $session->param(dsr_UserFilter, "0");
    $jsFiltering = "filtering: true,";
  }

  #connect to user login database
  $db = KAPutil_connect_to_database();

  #determine if the user is allowed to see Beta features
  $query = "SELECT betaTester FROM app.users WHERE ID=$userID";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($betaTester) = $dbOutput->fetchrow_array;

  print_html_header();

  print <<END_HTML;
<NAV CLASS="navbar navbar-expand-md navbar-light bg-light border">

  <BUTTON CLASS="navbar-toggler" TYPE="button" data-bs-toggle="collapse" data-bs-target="#navbarNavDropdown">
    <SPAN CLASS="navbar-toggler-icon"></SPAN>
  </BUTTON>

  <DIV CLASS="collapse navbar-collapse justify-content-center" ID="navbarNavDropdown">
    <UL CLASS="navbar-nav">
      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="open_selected_datasource()"><I CLASS="bi bi-folder2-open"></I> Open</A></LI>

      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="location.href='/app/dsr/newDSSource.cld'"><I CLASS="bi bi-plus-lg"></I> New</A></LI>

      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="update_datasource()"><I CLASS="bi bi-arrow-clockwise"></I> Update</A></LI>

      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="ds_properties()"><I CLASS="bi bi-gear"></I> Properties</A></LI>

      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="ds_sharing()"><I CLASS="bi bi-people"></I> Sharing</A></LI>

      <LI CLASS="nav-item"><A CLASS="nav-link" HREF="#" onClick="delete_datasource()"><I CLASS="bi bi-trash"></I> Delete</A></LI>

      <LI CLASS="nav-item dropdown">
        <A CLASS="nav-link dropdown-toggle" HREF="#" ID="navbarDropdownMenuLink" role="button" data-bs-toggle="dropdown">
          <I CLASS="bi bi-three-dots-vertical"></I> More
        </A>
        <UL CLASS="dropdown-menu">
          <LI><A CLASS="dropdown-item" HREF="#" onclick="ds_history()">History</A></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="ds_statistics()">Statistics</A></LI>
          <LI CLASS="dropdown-divider"></LI>
          <LI><A CLASS="dropdown-item" HREF="#" onclick="ds_copy()">Copy Data Source</A></LI>
          <LI><A CLASS="dropdown-item" HREF="restoreDS.cld">Restore Deleted Data Source</A></LI>
          <LI CLASS="dropdown-divider"></LI>
          <LI><A CLASS="dropdown-item" HREF="jobMonitor.cld">Job Monitor</A></LI>
        </UL>
      </LI>
    </UL>
  </DIV>
</NAV>

<P>
<DIV CLASS="container-fluid">
  <DIV CLASS="row">

    <DIV CLASS="col"> <!-- content -->

      <DIV CLASS="form-check">
        <INPUT CLASS="form-check-input" NAME="userSources" ID="userSources" TYPE="checkbox" $filterChecked onChange="location.href='$filterCbox'">
        <LABEL CLASS="form-check-label" FOR="userSources">Only show my data sources</LABEL>
      </DIV>

      <DIV ID="dsGrid" CLASS="grid mx-auto" STYLE="font-size:14px;" onmousemove="reset_refresh();"></DIV>

    </DIV>  <!-- content -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->

END_HTML

  print_html_footer();

  #flush the CGI session info out to storage
  $session->flush();

#EOF
