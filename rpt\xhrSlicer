#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DataSel;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::Reports;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $rptID = $q->param('rptID');
  $action = $q->param('action');
  $slicers = $q->param('slicers');

  $db = KAPutil_connect_to_database();

  #make sure we have edit privs for this report
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this report.");
  }


  ########################################################################
  #
  # This code block is called on submit to save the changes
  #

  if (defined($slicers))
  {
    $q_slicers = $db->quote($slicers);
    $query = "UPDATE cubes SET slicers=$q_slicers WHERE ID=$rptID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    $cubeName = cube_id_to_name($db, $rptID);
    $dsID = cube_get_ds_id($db, $rptID);
    $dsName = ds_id_to_name($db, $dsID);

    utils_audit($db, $userID, "Changed slicer configuration", $dsID, $rptID, 0);
    $activity = "$first $last changed slicer configuration for $cubeName in $dsName";
    utils_slack($activity);

    exit;
  }


  #########################################################################
  #
  # Everything after this point is called to display the locked measure dialog
  #

  $query = "SELECT slicers FROM cubes WHERE ID=$rptID";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($slicers) = $dbOutput->fetchrow_array;

  @tmp = split(',', $slicers);
  foreach $id (@tmp)
  {
    $id =~ m/PSEG_(\d+)/;
    $slicerHash{$1} = "CHECKED";
  }

  #get the list of segmentations included in this report
  $query = "SELECT measures FROM app.cubes WHERE ID=$rptID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($measStr) = $dbOutput->fetchrow_array;
  @tmp = split(',', $measStr);
  $segJS = "";
  foreach $itemID (@tmp)
  {
    if ($itemID =~ m/^PSEG_(\d+)$/)
    {
      push(@prodSegIDs, $1);
      $segJS .= "'$itemID',"
    }
  }
  chop($segJS);

  print <<END_HTML;
<SCRIPT>
function submitForm()
{
  let cboxArray = [$segJS];
  let selStr, state, slicers="";

  for (let i=0; i < cboxArray.length; i++)
  {
    selStr = "#" + cboxArray[i];
    state = \$(selStr).prop("checked");
    if (state == 1)
    {
      slicers = slicers + cboxArray[i] + ":0,";
    }
  }

  const url = "xhrSlicer?rptID=$rptID&slicers=" + slicers;

  \$.get(url, function(data, status)
  {
    location.href = "display.cld?rpt=$rptID";
  });
}
</SCRIPT>

<DIV CLASS="modal-dialog" STYLE="overflow-y: initial !important;">
  <DIV CLASS="modal-content">
    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Slicers</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">

      <FORM>
      Display slicers for the selected segmentations below:
      <P>
END_HTML

  #get the names of the segmentations in this DS
  $dsID = cube_get_ds_id($db, $rptID);
  %segNamesHash = DSRsegmentation_get_segmentations_hash($db, "datasource_$dsID", "p");

  foreach $segID (@prodSegIDs)
  {
    print <<END_HTML;
      <DIV CLASS="form-check">
        <INPUT CLASS="form-check-input" TYPE="checkbox" NAME="PSEG_$segID" ID="PSEG_$segID" $slicerHash{$segID}>
        <LABEL CLASS="form-check-label" FOR="PSEG_$segID">$segNamesHash{$segID}</LABEL>
      </DIV>
END_HTML
  }

  print <<END_HTML;
      </FORM>
    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="submitForm()"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML


#EOF
