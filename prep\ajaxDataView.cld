#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $flowID = $q->param('f');
  $jobID = $q->param('j');
  $pageIdx = $q->param('idx');

  print("Expires: -1\n");
  print("Cache-Control: private, max-age=0");
  print("Content-type: application/json\n\n");

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterTableName = $jobID . "_master";

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #make sure we have at least read privs
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs eq "N")
  {
    #NB: we're AJAX, so we're just dropping instead of printing error
    exit;
  }

  #get number of rows in master data table, sort info, and search filters
  $query = "SELECT rowCount, sortCol, searchFilter FROM prep.jobs WHERE ID=$jobID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($totalRows, $sortCol, $searchFilter) = $dbOutput->fetchrow_array;

  #get IDs of columns in display order
  @orderedCols = prep_flow_order_columns($prepDB, $flowID, $jobID);
  foreach $id (@orderedCols)
  {
    $colSelStr = $colSelStr . "column_$id,";
  }
  chop($colSelStr);

  #build up the WHERE clause if the user has set a search filter option
  $whereClause = "";
  if (length($searchFilter) > 2)
  {
    $searchFilter =~ m/^(\w)\|(column_\d+)\|(.*?)\|(.*)$/;
    $searchType = $1;
    $column = $2;
    $invalid = $3;
    $searchStr = $4;

    if ($searchType eq "b")
    {
      $searchStr .= "%";
    }
    elsif ($searchType eq "c")
    {
      $searchStr = "%" . $searchStr . "%";
    }
    elsif ($searchType eq "e")
    {
      $searchStr = "%" . $searchStr;
    }

    $whereClause = "WHERE $column LIKE '$searchStr'";

    #handle blank (NULL) searches
    if ($searchType eq "n")
    {
      $whereClause = "WHERE ISNULL $column";
    }

    if ($invalid eq "true")
    {
      $whereClause .= " AND valid=1";
    }
  }

  #build up the ORDER BY clause if the user has set a sort option
  $orderBy = "";
  if (length($sortCol) > 2)
  {
    $sortCol =~ m/^(\w)\|(column_\d+)$/;

    $orderBy = "ORDER BY $2 ";
    if ($1 eq "a")
    {
      $orderBy .= "ASC";
    }
    else
    {
      $orderBy .= "DESC";
    }
  }

  $startRow = ($pageIdx - 1) * 100;
  $query = "SELECT valid, $colSelStr FROM $masterTable $whereClause $orderBy \
      LIMIT $startRow,100";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);

  print <<JSON_LABEL;
{ \"data\": [
JSON_LABEL

  @dataVals = $dbOutput->fetchrow_array;
  while (scalar(@dataVals) > 0)
  {

    #output opening bracket
    print(" {\n");

    #output JSON data line for each column
    $valid = shift(@dataVals);
    $data = "\"valid\": \"$valid\",\n";
    $idx = 0;
    foreach $val (@dataVals)
    {
      $val =~ s/\"//g;
      $val =~ s/\\//g;

      $colIdx = $orderedCols[$idx];
      $data = $data . "\"column_$colIdx\": \"$val\",\n";
      $idx++;
    }
    chop($data);	chop($data);
    print("$data\n");

    #print closing bracket
    print(" }");

    @dataVals = $dbOutput->fetchrow_array;

    #if we have more records left to output, add comma
    if (defined($dataVals[0]))
    {
      print(",\n");
    }
  }

    print <<JSON_LABEL;
], \"itemsCount\": $totalRows}
JSON_LABEL

#EOF
