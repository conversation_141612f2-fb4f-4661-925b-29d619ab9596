#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSel;
use Lib::DSRUtils;
use Lib::DataSources;
use Lib::Reports;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $rptID = $q->param('rptID');
  $visID = $q->param('v');

  #connect to the database
  $db = KAPutil_connect_to_database();

  #make sure we have edit privs for this report
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this report.");
  }

  #get the table filter details from the database
  $query = "SELECT design FROM visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  ($tableDesign) = $dbOutput->fetchrow_array;


  ########################################################################
  #
  # Everything after this point is called to display the table filter dialog
  #

  print <<END_HTML;
<DIV CLASS="modal-dialog modal-lg condformat_dialog" STYLE="overflow-y: initial !important;">
  <DIV CLASS="modal-content dialog_container condformat_dialog_container">
    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Table Conditional Formatting</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">

      <FORM>
      <DIV CLASS="form-inline">
        <div style="margin: .6em; margin-bottom: 3em;">
          Measure
          <SELECT CLASS="form-control" id='measures_drop' STYLE="margin-left: .5em; cursor: pointer;">
END_HTML

  #get the list of measures available in the report
  @measureIDs = datasel_get_dimension_items($db, $rptID, "m");

  #get hash of all measures and their names
  $dsID = cube_get_ds_id($db, $rptID);
  $dsSchema = "datasource_" . $dsID;
  %measureNameHash = dsr_get_item_name_hash($db, $dsSchema, "m");

  $first = "true";

  #output OPTION tags for all available measures
  foreach $id (@measureIDs)
  {
    print(" <OPTION VALUE=$id>$measureNameHash{$id}</OPTION>\n");
    if ($first eq "true")
    {
      $onload_measure_id = $id;
    }
    $first = "false";
  }

  print <<END_HTML;
</SELECT>
</div>
END_HTML

  #run through the style string and pull out any conditional formatting rules
  @styles = split(',', $tableDesign);
  foreach $styleStr (@styles)
  {
    if ($styleStr =~ m/^condFormat/)
    {
      push(@condFormatStrs, $styleStr);
    }
  }

  #cycle through each formatting rule and output a line for it, allowing editing
  foreach $condFormatStr (@condFormatStrs)
  {
    @tmp = split(':', $condFormatStr);
    $condRuleName = $tmp[0];
    $condRule = $tmp[1];
    @ruleComponents = split(' ', $condRule);
    $ruleMeasureID = $ruleComponents[0];
    $ruleType = $ruleComponents[1];
    $value = $ruleComponents[2];
    if ($ruleType eq "ib")
    {
      $value2 = $ruleComponents[3];
      $color = $ruleComponents[4];
    }
    else
    {
      $color = $ruleComponents[3];
    }

    print <<END_HTML;
      <DIV CLASS='current_measures old_rule measure_id_$ruleMeasureID' data-measure='$ruleMeasureID' ID='$condRuleName'>
	If value is

	<SELECT CLASS="form-select rules_drop old_rules_drop">
END_HTML

    if ($ruleType eq "gt")
    {
      print('<OPTION VALUE="gt" SELECTED>greater than</OPTION>');
    }
    else
    {
      print('<OPTION VALUE="gt">greater than</OPTION>');
    }
    if ($ruleType eq "lt")
    {
      print('<OPTION VALUE="lt" SELECTED>less than</OPTION>');
    }
    else
    {
      print('<OPTION VALUE="lt">less than</OPTION>');
    }
    if ($ruleType eq "eq")
    {
      print('<OPTION VALUE="eq" SELECTED>equal to</OPTION>');
    }
    else
    {
      print('<OPTION VALUE="eq">equal to</OPTION>');
    }
    if ($ruleType eq "ib")
    {
      print('<OPTION VALUE="ib" SELECTED>in between</OPTION>');
    }
    else
    {
      print('<OPTION VALUE="ib">in between</OPTION>');
    }

    print("</SELECT><INPUT CLASS='form-control number_input old_number_input' TYPE='number' VALUE='$value'>");

    if ($ruleType eq "ib")
    {
      print("<DIV CLASS='num2_wrapper' STYLE='display: inline; opacity: 1; width: 4em;'>and<INPUT CLASS='form-control number_input2 old_number_input2' TYPE='number' VALUE='$value2'></DIV>");
    }
    else
    {
      print('<DIV CLASS="num2_wrapper">and<INPUT CLASS="form-control number_input2 old_number_input2" TYPE="number" placeholder="0"></DIV>');
    }

    print <<END_HTML;
cell background is

<INPUT CLASS="color_selector old_color_selector" TYPE="color" VALUE="#$color">

<I CLASS='material-icons delete_rule'>clear</I></DIV>
END_HTML
  }

  print <<END_HTML;
<DIV ID="add_rule_wrapper">
If value is

<SELECT CLASS="form-control mx-1 rules_drop">
 <OPTION VALUE="gt" selected>greater than</OPTION>
 <OPTION VALUE="lt">less than</OPTION>
 <OPTION VALUE="eq">equal to</OPTION>
 <OPTION VALUE="ib">in between</OPTION>
</SELECT><INPUT CLASS="form-control number_input" TYPE="number" placeholder='0'>

<DIV CLASS="num2_wrapper">and<INPUT CLASS="form-control number_input2" TYPE="number" placeholder='0'></DIV>

cell background is

<INPUT CLASS="color_selector" TYPE="color" VALUE="#ff0000">

<BUTTON CLASS="btn btn-primary" STYLE="margin-left: 1em;" TYPE="button" VALUE="Add" ID="add_rule_button"><SPAN CLASS="glyphicons glyphicons-plus"></SPAN><SPAN STYLE="vertical-align:middle;"> Add</SPAN></BUTTON>

</DIV>

<SCRIPT>
\$("select#filterMeas1").val("$filterMeas1");
\$("select#filterOp1").val("$filterOp1");

\$('.measure_id_$onload_measure_id').css("display","block");

\$('#measures_drop').on('change', function()
{
  \$('.current_measures').css("display","none");
  \$('.measure_id_' + this.value).css("display","block");
});

\$('.rules_drop').on('change', function()
{
  if (this.value == "ib")
  {
    var that = this;
    setTimeout(function()
    {
      \$(that).siblings('.num2_wrapper').css({"display": "inline", "opacity": "1", "width": "4em"});
    }, 200);
  }
  else
  {
    \$(this).siblings('.num2_wrapper').css({"display": "none", "opacity": "0", "width": "0"});
  }
});



function submitForm()
{
  location.href = "display.cld?rpt=$rptID&v=$visID";
}


var new_rules = [];
\$("#add_rule_button").click(function()
{
	let the_measure = \$("#measures_drop").find(":selected").val();
	let the_rule = \$(this).siblings(".rules_drop").find(":selected").val();
	let the_number = \$(this).siblings(".number_input").val();

	if (the_number == "")
  {
		the_number = "0";
	}

	let the_number2 = \$(this).siblings(".num2_wrapper").children(".number_input2").val();
	if (the_number2 == "")
  {
		the_number2 = "0";
	}

	let the_color = \$(this).siblings(".color_selector").val();
	the_color = the_color.split("#").join("");

	let random_rules_drop_id = new Date().getTime();
	random_rules_drop_id = random_rules_drop_id + "_rules_drop";
	let new_rule_html = '<div class="current_measures new_rule measure_id_' +
      the_measure + '" data-measure="' + the_measure  +
      '" style="display: block;"> If value is <SELECT id="' +
      random_rules_drop_id + '" CLASS="form-control rules_drop">';

	if (the_rule == "gt")
  {
		new_rule_html += '<OPTION VALUE="gt" selected>greater than</OPTION>';
	}
  else
  {
		new_rule_html += '<OPTION VALUE="gt">greater than</OPTION>';
	}
	if (the_rule == "lt")
  {
		new_rule_html += '<OPTION VALUE="lt" selected>less than</OPTION>';
	}
  else
  {
		new_rule_html += '<OPTION VALUE="lt">less than</OPTION>';
	}
	if (the_rule == "eq")
  {
		new_rule_html += '<OPTION VALUE="eq" selected>equal to</OPTION>';
	}
  else
  {
		new_rule_html += '<OPTION VALUE="eq">equal to</OPTION>';
	}
	if (the_rule == "ib")
  {
		new_rule_html += '<OPTION VALUE="ib" selected>in between</OPTION>';
	}
  else
  {
		new_rule_html += '<OPTION VALUE="ib">in between</OPTION>';
	}

	new_rule_html += '</SELECT><INPUT CLASS="form-control number_input" TYPE="number" value="' + the_number + '">';

	if (the_rule == "ib")
  {
		//variable end of this --->
		new_rule_html += '<div class="num2_wrapper" style="display: inline; opacity: 1; width: 4em;">and<INPUT CLASS="form-control number_input2" TYPE="number" value="' + the_number2 + '"></div>';
	}
  else
  {
		new_rule_html += '<div class="num2_wrapper">and<INPUT CLASS="form-control number_input2" TYPE="number" placeholder="0"></div>';
	}

	new_rule_html += ' cell background is <input class="color_selector" type="color" value="#' + the_color + '">';
	new_rule_html += ' <i class="material-icons delete_rule delete_rule_new">clear</i></div>';

	\$(new_rule_html).insertBefore("#add_rule_wrapper");

  \$('#' + random_rules_drop_id).on('change', function()
  {
		if (this.value == "ib")
    {
			let that = this;
			setTimeout(function()
      {
				\$(that).siblings('.num2_wrapper').css({"display": "inline", "opacity": "1", "width": "4em"});
			}, 200);
		}
    else
    {
			\$(this).siblings('.num2_wrapper').css({"display": "none", "opacity": "0", "width": "0"});
		}
	});

	//new rules aren't in database yet so just delete the html
	\$(".delete_rule_new").click(function()
  {
		\$(this).parent().remove();
		//remove one from new_rules array
		new_rules.pop();
	});

	new_rules.push("rule");
});

var delete_rules = [];
//this isn't called for .new_rules cus the onclick handler was initialized before .new_rules is attached to the page
\$(".delete_rule").click(function()
{
	let condname = \$(this).parent().attr("id");
	delete_rules.push(condname);
	\$(this).parent().remove();

	//check if it was updated, if so remove it from updated_rules array
	if (updated_rules.length > 0)
  {
		let new_updated_rules = [];
		for (i=0; i < updated_rules.length; i++)
    {
			//if it does match we omit it
			if (updated_rules[i] != condname)
      {
				new_updated_rules.push(updated_rules[i]);
			}
		}
		updated_rules = new_updated_rules;
	}
});



var updated_rules = [];
\$('.old_rules_drop, .old_number_input, .num2_wrapper, .old_color_selector').on('change', function()
{
	//dont add it twice
	if (updated_rules.length > 0)
  {
		for (i=0; i < updated_rules.length; i++)
    {
			if (\$(this).parent().attr("id") != updated_rules[i])
      {
				updated_rules.push(\$(this).parent().attr("id"));
			}
		}
	}
  else
  {
		updated_rules.push(\$(this).parent().attr("id"));
	}
});

var new_count = 0;
var updated_count = 0;
var delete_count = 0;
function add_conditional_formatting()
{
	updated_count = 0;
	if (updated_rules.length > 0)
  {
		for (i=0; i < updated_rules.length; i++)
    {

			//now change this to work for updated rules

			let condname = updated_rules[i];
			let the_measure = \$("#" + condname).attr("data-measure");
			let the_rule = \$("#" + condname).children(".rules_drop").find(":selected").val();
			let the_number = \$("#" + condname).children(".number_input").val();
			if (the_number == "")
      {
				the_number = "0";
			}

			let the_number2 = "na";
			if (\$("#" + condname).children(".num2_wrapper").css("display") != "none")
      {
				the_number2 = \$("#" + condname).children(".num2_wrapper").children(".number_input2").val();
				if (the_number2 == "")
        {
					the_number2 = "0";
				}
			}

			let the_color = \$("#" + condname).children(".color_selector").val();
			the_color = the_color.split("#").join("");

			let url = "xhrTableSetConditionalFormatting.cld?rptID=$rptID&e=$visID&cd=" + condname +
          "&m=" + the_measure + "&r=" + the_rule + "&n=" + the_number + "&nn=" +
          the_number2 + "&c=" + the_color;

			\$.get(url, function(data)
      {
				updated_count += 1;
			});
		}
	}

	new_count = 0;
	if (new_rules.length > 0)
  {
		\$(".new_rule").each(function(index)
    {
			let the_measure = \$(this).attr("data-measure");
			let the_rule = \$(this).children(".rules_drop").find(":selected").val();
			let the_number = \$(this).children(".number_input").val();
			if (the_number == "")
      {
				the_number = "0";
			}

			let the_number2 = "na";
			if (\$(this).children(".num2_wrapper").css("display") != "none")
      {
				let the_number2 = \$(this).children(".num2_wrapper").children(".number_input2").val();
				if (the_number2 == "")
        {
					the_number2 = "0";
				}
			}
			let the_color = \$(this).children(".color_selector").val();
			the_color = the_color.split("#").join("");

			let url = "xhrTableSetConditionalFormatting.cld?rptID=$rptID&e=$visID&cd=new_rule&m=" + the_measure +
          "&r=" + the_rule + "&n=" + the_number + "&nn=" + the_number2 +
          "&c=" + the_color;

			\$.get(url, function(data)
      {
				new_count += 1;
			});
		});
	}

	delete_count = 0;
	if (delete_rules.length > 0)
  {
		for (x=0; x < delete_rules.length; x++)
    {
			let url = "xhrTableSetConditionalFormatting.cld?rptID=$rptID&e=$visID&cd=" + delete_rules[x] +
          "&m=delete_this_item&r=blah&n=blah&nn=blah&c=blah";

			\$.get(url, function(data)
      {
				delete_count += 1;
			});
		}
	}

	waitToSubmit();
}



function waitToSubmit()
{
	if (updated_rules.length == updated_count && new_rules.length == new_count && delete_rules.length == delete_count)
  {
		submitForm();
	}
  else
  {
		setTimeout(function()
    {
			waitToSubmit();
		}, 500);
	}
}
</SCRIPT>

      </DIV>

      <P>
      </FORM>

    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" VALUE="Cancel" data-bs-dismiss="modal"><SPAN CLASS="glyphicons glyphicons-remove"></SPAN> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="button" VALUE="Done" onClick="add_conditional_formatting()"><SPAN CLASS="glyphicons glyphicons-ok"></SPAN> Done</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML


#EOF
