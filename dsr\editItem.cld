#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRMeasures;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  #output Content-type header
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  #output HTML
  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Edit Item</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  \$('#btn-submit').prop('disabled', true);
  \$('#btn-submit').text('Please Wait...');
  return(true);
}



function measureAggMatch()
{
  let aggValuesStr;
  let matchSelect = document.getElementById('aggMatchValues');

  for (let i = 0; i < matchSelect.options.length; i++)
  {
    if (matchSelect.options[i].selected)
    {
      aggValuesStr = matchSelect.options[i].value;
    }
  }

  let aggValues = aggValuesStr.split(',');

  \$('#prodAggRule').val(aggValues[0]);
  \$('#geoAggRule').val(aggValues[1]);
  \$('#timeAggRule').val(aggValues[2]);
}


function mergeAggMatch()
{
  let prodAggRule = \$('#prodAggRule').val();

  \$('#mergeAggRule').val(prodAggRule);
}

</SCRIPT>

<STYLE>
.accordion-button.collapsed {
  background: blue
}

.accordion-button.collapsed::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}
</STYLE>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld?ds=$dsID">Data Sources</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?ds=$dsID">$dsName</A></LI>
    <LI CLASS="breadcrumb-item active">Edit $dimName $name</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $dsID = $q->param('ds');
  $dim = $q->param('dim');
  $itemID = $q->param('item');
  $merged = $q->param('merged');

  #sanitize parameters
  $dsID = utils_sanitize_integer($dsID);
  $dim = utils_sanitize_dim($dim);
  $itemID = utils_sanitize_string($itemID);
  $merged = utils_sanitize_integer($merged);

  #if we're editing an item from inside a list or agg, strip it down to just
  #the item ID
  if ($itemID =~ m/^LIS_\d+\.(\d+)$/)
  {
    $itemID = $1;
  }
  if ($itemID =~ m/^AGG_\d+\.(\d+)$/)
  {
    $itemID = $1;
  }

  #if we're editing an item from deep inside a segment structure, strip it
  #down to just the item ID
  if ($itemID =~ m/^SMT_\d+\.(\d+)$/)
  {
    $itemID = $1;
  }
  if ($itemID =~ m/^SHS_.*_(\d+)$/)
  {
    $itemID = $1;
  }

  #connect to the database
  $db = KAPutil_connect_to_database();

  #make sure we have write privs for this data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit items in this data source.");
  }

  $dsSchema = "datasource_" . $dsID;
  $dsName = ds_id_to_name($db, $dsID);

  $structDB = KAPutil_get_dim_stub_name($dim);
  $dimDB = KAPutil_get_dim_db_name($dim);
  $dimName = KAPutil_get_dim_name_singular($dim, 1);

  $query = "SELECT name, alias FROM $dsSchema.$dimDB WHERE ID=$itemID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($name, $alias) = $dbOutput->fetchrow_array;

  print_html_header();

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-11 col-lg-8 col-xl-7"> <!-- content -->

      <FORM METHOD="post" ACTION="/app/dsr/editItemSave.cld" onsubmit="return checkForm(this);">
      <INPUT TYPE="hidden" NAME="dsID" VALUE="$dsID">
      <INPUT TYPE="hidden" NAME="dim" VALUE="$dim">
      <INPUT TYPE="hidden" NAME="item" VALUE="$itemID">

      <DIV CLASS="accordion mx-auto" ID="accordion">

        <DIV CLASS="accordion-item border-primary">
          <H2 CLASS="accordion-header">
            <BUTTON CLASS="accordion-button bg-primary text-white icon-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
              Name & Alias
            </BUTTON>
          </H2>
          <DIV ID="collapse1" CLASS="accordion-collapse collapse show" data-bs-parent="#accordion">
            <DIV CLASS="accordion-body">

              <LABEL FOR="name">Name:</LABEL>
              <INPUT CLASS="form-control mb-3" TYPE="text" NAME="name" ID="name" VALUE="$name" required>

              <LABEL FOR="alias">Alias:</LABEL>
              <INPUT CLASS="form-control mb-3" TYPE="text" NAME="alias" ID="alias" VALUE="$alias">

            </DIV>
          </DIV>
        </DIV>

        <DIV CLASS="accordion-item border-primary">
          <H2 CLASS="accordion-header">
            <BUTTON CLASS="accordion-button collapsed bg-primary text-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
              Attributes
            </BUTTON>
          </H2>
          <DIV ID="collapse2" CLASS="accordion-collapse collapse" data-bs-parent="#accordion">
            <DIV CLASS="accordion-body">
END_HTML

  $dbName = $structDB . "attributes";
  $query = "SELECT ID, name FROM $dsSchema.$dbName";
  $dbOutput = $db->prepare($query);
  $attrCount = $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);

  if ($attrCount < 1)
  {
    print <<END_HTML;
              <EM CLASS="text-muted">(No attributes associated with this item)</EM>
END_HTML
  }

  while (($attrID, $attrName) = $dbOutput->fetchrow_array)
  {
    $dbName = $structDB . "attribute_values";
    $query = "SELECT value FROM $dsSchema.$dbName \
        WHERE attributeID=$attrID AND itemID=$itemID";
    $dbOutput1 = $db->prepare($query);
    $status = $dbOutput1->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($value) = $dbOutput1->fetchrow_array;

    print <<END_HTML;
              <LABEL FOR="ATTR-$attrID">$attrName:</LABEL>
              <INPUT CLASS='form-control mb-3' TYPE='text' NAME='ATTR $attrID' ID='ATTR-$attrID' VALUE='$value'>
END_HTML
  }

  print <<END_HTML;

            </DIV>
          </DIV>
        </DIV>
END_HTML


  #if we're editing a product or geography, allow editing of segment membership
  if ((($dim eq "p") || ($dim eq "g")) && ($merged != 2))
  {
    print <<END_HTML;
        <DIV CLASS="accordion-item border-primary">
          <H2 CLASS="accordion-header">
            <BUTTON CLASS="accordion-button collapsed bg-primary text-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse3">
              Segmentations
            </BUTTON>
          </H2>
          <DIV ID="collapse3" CLASS="accordion-collapse collapse" data-bs-parent="#accordion">
            <DIV CLASS="accordion-body">
              <DIV CLASS="container">
END_HTML

    $dbName = $structDB . "segment_item";

    #get the name-sorted array of all segmentations for the current dimension
    @segmentationArray = DSRsegmentation_get_segmentations_array($db, $dsSchema, $dim);

    #build up an array of segment arrays (first level is segmentation, second
    #level is segment
    undef(@segmentsArray);
    $segIdx = 0;
    foreach $segmentationID (@segmentationArray)
    {
      @segments = DSRseg_get_segments_array($db, $dsSchema, $dim, $segmentationID);

      $smtIdx = 0;
      foreach $smtID (@segments)
      {
        $segmentsArray[$segIdx][$smtIdx] = $smtID;
        $smtIdx++;
      }

      $segIdx++;
    }

    #get the item/structure names for the current dimension
    %itemNameHash = dsr_get_item_name_hash($db, $dsSchema, $dim);

    #get all segmentation memberships for the current item, and hash them
    undef(%itemSegMembership);
    $query = "SELECT segmentationID, segmentID FROM $dsSchema.$dbName \
        WHERE itemID=$itemID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    while (($segID, $segmentID) = $dbOutput->fetchrow_array)
    {
      $itemSegMembership{$segID} = $segmentID;
    }

    #output a membership line for each segmentation in the data source
    $segIdx=0;
    foreach $segID (@segmentationArray)
    {
      $segmentationID = "SEG_" . $segID;
      $segmentationName = $itemNameHash{$segmentationID};

      $segmentID = $itemSegMembership{$segID};
      $segmentID = "SMT_" . $segmentID;
      $segmentName = $itemNameHash{$segmentID};

      $htmlSelectName = "ITM_" . $itemID . $segmentationID;

      print <<END_HTML;
                <DIV CLASS="row no-gutters">
                  <DIV CLASS="col align-self-center" STYLE='text-align:right;'>
                    $segmentationName:
                  </DIV>
                  <DIV CLASS="col">
                    <SELECT CLASS="form-select mx-1 my-1" NAME='$segmentationID' ID='$segmentationID' VALUE=$segmentID>
                      <OPTION VALUE=0>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</OPTION>
END_HTML

      $smtIdx = 0;
      while (defined($segmentsArray[$segIdx][$smtIdx]))
      {
        $fqSmtID = "SMT_" . $segmentsArray[$segIdx][$smtIdx];
        $smtName = $itemNameHash{$fqSmtID};
        print("<OPTION VALUE=\"$fqSmtID\">$smtName</OPTION>\n");
        $smtIdx++;
      }

      print <<END_HTML;
                    </SELECT>
                    <SCRIPT>
                      \$("select#$segmentationID").val("$segmentID");
                    </SCRIPT>
                  </DIV>
                </DIV>
END_HTML

      $segIdx++;
    }

    print <<END_HTML;
              </DIV>

            </DIV>
          </DIV>
        </DIV>
END_HTML
  }

  #if we're editing a time period, allow editing of time properties
  if ($dim eq "t")
  {
    $query = "SELECT duration, type, endDate FROM $dsSchema.timeperiods \
        WHERE ID = $itemID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);

    ($duration, $type, $endDate) = $dbOutput->fetchrow_array;

    #trim time off of end of endDate
    $endDate =~ m/(.*?)\s.*/;
    $endDate = $1;

    print <<END_HTML;
        <DIV CLASS="accordion-item border-primary">
          <H2 CLASS="accordion-header">
            <BUTTON CLASS="accordion-button collapsed bg-primary text-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse4">
              Time Period Properties
            </BUTTON>
          </H2>
          <DIV ID="collapse4" CLASS="accordion-collapse collapse" data-bs-parent="#accordion">
            <DIV CLASS="card-body">
              <TABLE>
                <TR>
                  <TD STYLE="text-align:right;">Type:</TD>
                  <TD>
                    <SELECT CLASS="form-select" NAME="type" ID="type">
                      <OPTION VALUE=10>Year</OPTION>
                      <OPTION VALUE=20>Month</OPTION>
                      <OPTION VALUE=30>Week</OPTION>
                      <OPTION VALUE=40>Day</OPTION>
                    </SELECT>
                    <SCRIPT>
                     \$('select#type').val('$type');
                    </SCRIPT>
                  </TD>
                </TR>
                <TR>
                  <TD ALIGN:"right">Duration:</TD>
                  <TD>
                    <INPUT TYPE="number" CLASS="form-control"  NAME="duration" VALUE=$duration>
                  </TD>
                </TR>
                <TR>
                  <TD ALIGN:"right">End Date:</TD>
                  <TD>
                    <INPUT TYPE="date" CLASS="form-control" NAME="endDate" VALUE="$endDate">
                  </TD>
                </TR>
              </TABLE>

            </DIV>
          </DIV>
        </DIV>
END_HTML
  }

  #determine if we're a base measure
  if ($dim eq "m")
  {
    $query = "SELECT calculation, calcBeforeAgg FROM $dsSchema.measures \
        WHERE ID = $itemID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);

    ($calculation, $calcBeforeAgg) = $dbOutput->fetchrow_array;
  }

  #if we're editing a measure, allow editing of aggregation rules
  if ($dim eq "m")
  {
    $query = "SELECT prodAggRule, geoAggRule, timeAggRule, mergeAggRule, format \
        FROM $dsSchema.measures WHERE ID = $itemID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;

    ($prodAggRule, $geoAggRule, $timeAggRule, $mergeAggRule, $formatStr) = $dbOutput->fetchrow_array;

    if ($mergeAggRule eq "None")
    {
      $mergeAggRule = $prodAggRule;
    }

    @formats = split(',', $formatStr);
    $separatorChecked = "";
    $currencyChecked = "";
    if ($formats[1] == 1)
    {
      $separatorChecked = "CHECKED";
    }
    if ($formats[2] == 1)
    {
      $currencyChecked = "CHECKED";
    }

    print <<END_HTML;
        <DIV CLASS="accordion-item border-primary">
          <H2 CLASS="accordion-header">
            <BUTTON CLASS="accordion-button collapsed bg-primary text-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse5">
              Aggregation Rules
            </BUTTON>
          </H2>
          <DIV ID="collapse5" CLASS="accordion-collapse collapse" data-bs-parent="#accordion">
            <DIV CLASS="accordion-body">
              <TABLE>
                <TR>
                  <TD ALIGN="right">Product Aggregation Rule:&nbsp;</TD>
                  <TD>
                    <SELECT CLASS="form-select" ID="prodAggRule" onChange="mergeAggMatch()" NAME="prodAggRule">
                      <OPTION VALUE=""></OPTION>
                      <OPTION VALUE="AVG">Average</OPTION>
                      <OPTION VALUE="COUNT">Count</OPTION>
                      <OPTION VALUE="MAX">Maximum</OPTION>
                      <OPTION VALUE="MIN">Minimum</OPTION>
                      <OPTION VALUE="STDDEV">Standard Deviation</OPTION>
                      <OPTION VALUE="SUM">Sum</OPTION>
                    </SELECT>
                    <SCRIPT>
                      \$('select#prodAggRule').val('$prodAggRule');
                    </SCRIPT>
                  </TD>
                </TR>

                <TR>
                  <TD ALIGN="right">Geography Aggregation Rule:&nbsp;</TD>
                  <TD>
                    <SELECT CLASS="form-select" ID="geoAggRule" NAME="geoAggRule" VALUE="$geoAggRule">
                      <OPTION VALUE=""></OPTION>
                      <OPTION VALUE="AVG">Average</OPTION>
                      <OPTION VALUE="COUNT">Count</OPTION>
                      <OPTION VALUE="MAX">Maximum</OPTION>
                      <OPTION VALUE="MIN">Minimum</OPTION>
                      <OPTION VALUE="STDDEV">Standard Deviation</OPTION>
                      <OPTION VALUE="SUM">Sum</OPTION>
                    </SELECT>
                    <SCRIPT>
                      \$('select#geoAggRule').val('$geoAggRule');
                    </SCRIPT>
                  </TD>
                </TR>

                <TR>
                  <TD ALIGN="right">Time Aggregation Rule:&nbsp;</TD>
                  <TD>
                    <SELECT CLASS="form-select" ID="timeAggRule" NAME="timeAggRule" VALUE="$timeAggRule">
                      <OPTION VALUE=""></OPTION>
                      <OPTION VALUE="AVG">Average</OPTION>
                      <OPTION VALUE="COUNT">Count</OPTION>
                      <OPTION VALUE="MAX">Maximum</OPTION>
                      <OPTION VALUE="MIN">Minimum</OPTION>
                      <OPTION VALUE="STDDEV">Standard Deviation</OPTION>
                      <OPTION VALUE="SUM">Sum</OPTION>
                    </SELECT>
                    <SCRIPT>
                      \$('select#timeAggRule').val('$timeAggRule');
                    </SCRIPT>
                  </TD>
                </TR>

                <TR>
                  <TD>&nbsp;</TD>
                  <TD>&nbsp;</TD>
                </TR>

                <TR>
                  <TD ALIGN="right">Merged Item Aggregation Rule:&nbsp;</TD>
                  <TD>
                    <SELECT CLASS="form-select" ID="mergeAggRule" NAME="mergeAggRule" VALUE="$mergeAggRule">
                      <OPTION VALUE=""></OPTION>
                      <OPTION VALUE="AVG">Average</OPTION>
                      <OPTION VALUE="COUNT">Count</OPTION>
                      <OPTION VALUE="MAX">Maximum</OPTION>
                      <OPTION VALUE="MIN">Minimum</OPTION>
                      <OPTION VALUE="STDDEV">Standard Deviation</OPTION>
                      <OPTION VALUE="SUM">Sum</OPTION>
                    </SELECT>
                    <SCRIPT>
                      \$('select#mergeAggRule').val('$mergeAggRule');
                    </SCRIPT>
                  </TD>
                </TR>

              </TABLE>

              <P>&nbsp;</P>

              <LABEL FOR="aggMatchValues">Uses the same aggregation rules as:</LABEL>
              <SELECT CLASS="form-select" ID="aggMatchValues" SIZE="7" onChange="measureAggMatch()">
                <OPTION VALUE="MAX,AVG,MAX">% ACV (%Stores Selling)</OPTION>
                <OPTION VALUE="MAX,AVG,MAX">% ACV, Any Display</OPTION>
                <OPTION VALUE="MAX,AVG,MAX">% ACV, Any Feature</OPTION>
                <OPTION VALUE="MAX,AVG,MAX">% ACV, Any Merchandising</OPTION>
                <OPTION VALUE="MAX,AVG,MAX">% ACV, Display Only</OPTION>
                <OPTION VALUE="MAX,AVG,MAX">% ACV, Feature and Display</OPTION>
                <OPTION VALUE="MAX,AVG,MAX">% ACV, Feature Only</OPTION>
                <OPTION VALUE="MAX,AVG,MAX">% ACV, Price Reduction Only</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">% Dollars, Any Merchandising</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">% Dollars, Display Only</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">% Dollars, Feature & Display</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">% Dollars, Feature Only</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">% Dollars, Price Reduction Only</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">% Incrs Dollars, Any Merchandising</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">% Incrs Dollars, Display Only</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">% Incrs Dollars, Feature & Display</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">% Incrs Dollars, Feature Only</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">% Incrs Dollars, Price Rdn Only</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">% Incrs Volume, Any Merchandising</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">% Incrs Volume, Display Only</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">% Incrs Volume, Feature & Display</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">% Incrs Volume, Feature Only</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">% Incrs Volume, Price Rnd Only</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">% Incrs Volume, Any Merchandising</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">% Units, Any Merchandising</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">% Units, Display Only</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">% Units, Feature & Display</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">% Units, Feature Only</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">% Units, Price Rdn Only</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">% Volume, Any Merchandising</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">% Volume, Display Only</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">% Volume, Feature & Display</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">% Volume, Feature Only</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">% Volume, Price Rdn Only</OPTION>
                <OPTION VALUE="MAX,SUM,MAX">ACV (\$MM)</OPTION>
                <OPTION VALUE="MAX,AVG,MAX">ACV Any Disp</OPTION>
                <OPTION VALUE="MAX,AVG,MAX">ACV Any Feat</OPTION>
                <OPTION VALUE="MAX,AVG,MAX">ACV Disp Only</OPTION>
                <OPTION VALUE="MAX,AVG,MAX">ACV Feat & Disp</OPTION>
                <OPTION VALUE="MAX,AVG,MAX">ACV Feat Only</OPTION>
                <OPTION VALUE="MAX,AVG,MAX">ACV Price Rdn Only</OPTION>
                <OPTION VALUE="MAX,AVG,MAX">ACV Weighted Distribution</OPTION>
                <OPTION VALUE="AVG,AVG,SUM">Avg Items per Store</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">Avg N-Promoted Price per Unit</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">Avg N-Promoted Price per Volume</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">Avg Promoted Price per Unit</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">Avg Promoted Price per Volume</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">Average Retail Price</OPTION>
                <OPTION VALUE="AVG,AVG,SUM">Average Weekly Items per Store</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">Average Price per Unit</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">Average Price per Volume</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">Avg Unit Price, Any Merchandising</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">Avg Unit Price, Display Only</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">Avg Unit Price, Feature and Display</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">Avg Unit Price, Feature Only</OPTION>
                <OPTION VALUE="AVG,AVG,MAX">Avg Weekly ACV Weighted Dist</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Base Dollars</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Base Units</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Base Volume</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Cost to Manufacturer</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Cost to Retailer</OPTION>
                <OPTION VALUE="SUM,SUM,MAX">Coupon Circulation</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Coupon Redeemed</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Dollar Sales</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Dollar Sales Chg Prior Period</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Dollar Sales Chg Prior Year</OPTION>
                <OPTION VALUE="AVG,AVG,SUM">Dollar Share</OPTION>
                <OPTION VALUE="AVG,AVG,SUM">Dollar Share of Type</OPTION>
                <OPTION VALUE="SUM,AVG,SUM">Dollars per \$MM ACV</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Dollars Shipped</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Dollars, Any Display</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Dollars, Any Feature</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Dollars, Any Merchandising</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Dollars, Display Only</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Dollars, Feature & Display</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Dollars, Feature Only</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Dollars, Price Rdn Only</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Gross Ship Qty</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">Gross Ship Retail</OPTION>
                <OPTION VALUE="MAX,SUM,MAX">Households</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Incremental Dollars</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Incremental Units</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Incremental Volume</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Merchandised Dollars</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Merchandised Units</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Merchandised Volume</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Non-Merchandised Dollars</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Non-Merchandised Units</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Non-Merchandised Volume</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Promoted Units, Any Display</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Promoted Units, Any Feature</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Promoted Units, Display Only</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Promoted Units, Feature & Display</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Promoted Units, Feature Only</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Promoted Units, Price Rdn Only</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Promoted Units, Any Display</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Promoted Units, Any Display</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Promoted Volume, Display Only</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Promoted Volume, Feature & Display</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Promoted Volume, Feature Only</OPTION>
                <OPTION VALUE="MAX,AVG,MAX">Stores Selling</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Trade Spending</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Unit Cost</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">Unit Retail</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Unit Sales</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Unit Sales Change Prior Period</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Unit Sales Change Prior Year</OPTION>
                <OPTION VALUE="AVG,AVG,SUM">Unit Share</OPTION>
                <OPTION VALUE="AVG,AVG,SUM">Unit Share of Type</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Units Shipped</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Units, Any Display</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Units, Any Feature</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Units, Any Merchandising</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Units, Display Only</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Units, Feature & Display</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Units, Feature Only</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Units, Price Rdn Only</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Volume Sales</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Volume Sales Chg Prior Period</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Volume Sales Chg Prior Year</OPTION>
                <OPTION VALUE="AVG,AVG,SUM">Volume Share</OPTION>
                <OPTION VALUE="AVG,AVG,SUM">Volume Share of Type</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Volume Shipped</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Volume, Any Display</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Volume, Any Feature</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Volume, Any Merchandising</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Volume, Display Only</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Volume, Feature & Display</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Volume, Feature Only</OPTION>
                <OPTION VALUE="SUM,SUM,SUM">Volume, Price Rdn Only</OPTION>
                <OPTION VALUE="AVG,AVG,AVG">Weighted Avg Base Price per Unit</OPTION>
              </SELECT>

            </DIV>
          </DIV>
        </DIV>
END_HTML
  }

  #if we're a measure, allow editing of formatting
  if ($dim eq "m")
  {
    $query = "SELECT format FROM $dsSchema.measures WHERE ID = $itemID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($formatStr) = $dbOutput->fetchrow_array;

    @formats = split(',', $formatStr);
    $separatorChecked = "";
    $numberChecked = "";
    $currencyChecked = "";
    $pctChecked = "";
    if ($formats[1] == 1)
    {
      $separatorChecked = "CHECKED";
    }
    if ($formats[2] == 1)
    {
      $currencyChecked = "CHECKED";
    }
    elsif ($formats[2] == 2)
    {
      $pctChecked = "CHECKED";
    }
    else
    {
      $numberChecked = "CHECKED";
    }

    print <<END_HTML;
        <DIV CLASS="accordion-item border-primary">
          <H2 CLASS="accordion-header">
            <BUTTON CLASS="accordion-button collapsed bg-primary text-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse6">
              Formatting
            </BUTTON>
          </H2>
          <DIV ID="collapse6" CLASS="accordion-collapse collapse" data-bs-parent="#accordion">
            <DIV CLASS="accordion-body">

              <B>Category:</B><BR>
              <DIV CLASS="form-check">
                <INPUT CLASS="form-check-input" TYPE="radio" NAME="fcat" $numberChecked VALUE="number" ID="number">
                <LABEL CLASS="form-check-label" FOR="number">Number</LABEL>
              </DIV>
              <DIV CLASS="form-check">
                <INPUT CLASS="form-check-input" TYPE="radio" NAME="fcat" $currencyChecked VALUE="currency" ID="currency">
                <LABEL CLASS="form-check-label" FOR="currency">Currency</LABEL>
              </DIV>
              <DIV CLASS="form-check">
                <INPUT CLASS="form-check-input" TYPE="radio" NAME="fcat" $pctChecked VALUE="percentage" ID="percentage">
                <LABEL CLASS="form-check-label" FOR="percentage">Percentage</LABEL><BR>
              </DIV>

              <P>

              <DIV CLASS="row">
                <DIV CLASS="col-auto mt-1">
                  <LABEL FOR="fdec">Decimal places:</LABEL>
                </DIV>
                <DIV CLASS="col-auto gx-0">
                  <INPUT TYPE="number" CLASS="form-control mx-1" NAME="fdec" ID="fdec" VALUE=$formats[0]  STYLE="width:75px;">
                </DIV>
              </DIV>

              <P>

              <DIV CLASS="form-check my-3">
                <INPUT CLASS="form-check-input" TYPE='checkbox' $separatorChecked NAME='fsep' ID="fsep">
                <LABEL CLASS="form-check-label" FOR="fsep">Use 1000 separator (,)</LABEL>
              </DIV>

              <P>

              <LABEL FOR="fneg">Negative number formatting:</LABEL>
              <SELECT NAME="fneg" CLASS="form-select" ID="fneg" SIZE="4">
                <OPTION VALUE="1">-1,234.00</OPTION>
                <OPTION STYLE="color:red;" VALUE="2">1,234.00</OPTION>
                <OPTION VALUE="3">(1,234.00)</OPTION>
                <OPTION STYLE="color:red;" VALUE="4">(1,234.00)</OPTION>
              </SELECT>
              <SCRIPT>
                \$('select#fneg').val('$formats[3]');
              </SCRIPT>

            </DIV>
          </DIV>
        </DIV>
END_HTML
  }

  #if we're editing a merged item
  if ($merged == 1)
  {
    print <<END_HTML;
        <DIV CLASS="accordion-item border-primary">
          <H2 CLASS="accordion-header">
            <BUTTON CLASS="accordion-button collapsed bg-primary text-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse7">
              Merged Products
            </BUTTON>
          </H2>
          <DIV ID="collapse7" CLASS="accordion-collapse collapse" data-bs-parent="#accordion">
            <DIV CLASS="accordion-body">

              <BUTTON TYPE="button" CLASS="btn btn-primary" onClick="location.href='/app/dsr/mergedProductsDefine.cld?ds=$dsID&mergedID=$itemID'"><I CLASS="bi bi-pencil"></I> Edit Merged Product</BUTTON>

              <P>&nbsp;</P>

              <LABEL FOR="merged-prods-sel">Items merged into this product:</LABEL>
              <SELECT CLASS="form-select overflow-auto" ID="merged-prods-sel" SIZE="10">
END_HTML

    %productNames = dsr_get_base_item_name_hash($db, $dsSchema, "p");

    $query = "SELECT mergedProds FROM $dsSchema.products_merged \
        WHERE mergedID = $itemID";
    $dbOutput = $db->prepare($query);
    $status = $dbOutput->execute;
    KAPutil_handle_db_err($db, $status, $query);
    ($mergedIDsStr) = $dbOutput->fetchrow_array;

    @mergedIDs = split(',', $mergedIDsStr);
    foreach $mergedID (@mergedIDs)
    {
      print("   <OPTION>$productNames{$mergedID}</OPTION>\n");
    }

    print <<END_HTML;

              </SELECT>
            </DIV>
          </DIV>
        </DIV>
END_HTML
  }


  if (($dim eq "m") && (length($calculation) > 1))
  {
    ($measType, $formula) = DSRmeasures_human_readable_formula($db, $dsSchema, $itemID);

    #temporary until dev work on calc measure info is completed
    if (length($formula) < 1)
    {
      print <<END_HTML;
      <DIV CLASS="accordion-item border-primary">
        <H2 CLASS="accordion-header">
          <BUTTON CLASS="accordion-button collapsed bg-primary text-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse8">
            Measure Calculation
          </BUTTON>
        </H2>
        <DIV ID="collapse8" CLASS="accordion-collapse collapse" data-bs-parent="#accordion">
          <DIV CLASS="card-body">

            <DIV CLASS="text-center my-3">
              <BUTTON TYPE="button" CLASS="btn btn-primary" onClick="location.href='calcMeasureName.cld?ds=$dsID&measID=$itemID'"><I CLASS="bi bi-pencil"></I> Edit Calculated Measure</BUTTON>
            </DIV>

          </DIV>
        </DIV>
      </DIV>

END_HTML
    }

    else
    {
      print <<END_HTML;
        <DIV CLASS="accordion-item border-primary">
          <H2 CLASS="accordion-header">
            <BUTTON CLASS="accordion-button collapsed bg-primary text-white" TYPE="button" data-bs-toggle="collapse" data-bs-target="#collapse8">
              Measure Calculation
            </BUTTON>
          </H2>
          <DIV ID="collapse8" CLASS="accordion-collapse collapse" data-bs-parent="#accordion">
            <DIV CLASS="card-body">

              $name is a $measType measure calculated with the formula:

              <P>
              <DIV CLASS="text-center">
                <UL CLASS="list-group">
                  <LI CLASS="list-group-item list-group-item-info">$formula</LI>
                </UL>
              </DIV>

              <DIV CLASS="text-center my-3">
                <BUTTON TYPE="button" CLASS="btn btn-primary" onClick="location.href='calcMeasureName.cld?ds=$dsID&measID=$itemID'"><I CLASS="bi bi-pencil"></I> Edit Calculated Measure</BUTTON>
              </DIV>

            </DIV>
          </DIV>
        </DIV>
END_HTML
    }
  }

  print <<END_HTML;
      </DIV>

      <P>

      <DIV CLASS="text-center">
        <BUTTON CLASS="btn btn-secondary" TYPE="button" onClick="location.href='display.cld?ds=$dsID&dim=$dim'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
        <BUTTON CLASS="btn btn-primary" TYPE="SUBMIT" ID="btn-submit"><I CLASS="bi bi-save"></I> Save</BUTTON>
      </DIV>

      </FORM>

      <P>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
