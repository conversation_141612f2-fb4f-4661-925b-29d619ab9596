#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $dsID = $q->param('ds');
  $dim = $q->param('d');
  $curSegID = $q->param('s');

  #connect to the database
  $db = KAPutil_connect_to_database();

  #make sure we have write privs for the data source
  $privs = ds_rights($db, $userID, $dsID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to modify the data source.");
  }

  $dsSchema = "datasource_" . $dsID;

  $dbStub = KAPutil_get_dim_stub_name($dim);
  $dbName = $dbStub . "segment_item";

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Segmentation Information</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>

<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Item Segmentation Information</DIV>
        <DIV CLASS="card-body">
END_HTML

  #run through the list of CGI parameters, grabbing all of the segment info and
  #updating segment membership info as we go
  @names = $q->param;
  foreach $name (@names)
  {
    if ($name =~ m/ITM_(\d+)SEG_(\d+)/)
    {
      $itemID = $1;
      $segID = $2;

      $segmentID = $q->param($name);
      if ($segmentID =~ m/SMT_(\d+)/)
      {
        $segmentID = $1;
      }

      #if the item is assigned a segment in this segmentation
      if ($segmentID > 0)
      {
        $query = "INSERT INTO $dsSchema.$dbName \
            (segmentationID, segmentID, itemID) \
            VALUES ($segID, $segmentID, $itemID) \
            ON DUPLICATE KEY UPDATE segmentationID=$segID, segmentID=$segmentID";
        $db->do($query);
      }
    }
  }

  #mark the data source as modified so automated processes (ODBC export, cube
  #update) will run
  $query = "UPDATE dataSources SET lastModified=NOW() WHERE ID=$dsID";
  $db->do($query);

  print <<END_HTML;
          Your changes to item-level segmentations have been saved.

          <P>
          <DIV CLASS="text-center">
            <BUTTON TYPE="button" CLASS="btn btn-primary" onClick='window.close();'><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->

</BODY>
</HTML>
END_HTML


#EOF
