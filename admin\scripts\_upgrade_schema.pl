#!/usr/bin/perl

use lib "/opt/apache/app/";

use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::PrepUtils;
use Lib::WebUtils;


  #connect to the database
  $db = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #get our current schema version
  $query = "SELECT value FROM app.config WHERE name = 'schema_version'";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($schema_version) = $dbOutput->fetchrow_array;

  #get hash of all data sources in the cloud instance
  %dataSources = ds_get_name_hash($db);


################### 40 ######################################

  if ($schema_version == 40)
  {

    print("Updating to schema version 41\n");

    $db->do("ALTER TABLE `app`.`orgs` \
ADD COLUMN `externalAccounts` INT(10) UNSIGNED NOT NULL DEFAULT 0 AFTER `emailStem`, \
CHANGE COLUMN `standardAccts` `emailStem` VARCHAR(64) NULL DEFAULT NULL");

    $db->do("ALTER TABLE `app`.`users`  \
DROP COLUMN `admin`, \
ADD COLUMN `ownerOrgID` INT(10) UNSIGNED NULL DEFAULT NULL AFTER `orgID`");


  $db->do("CREATE TABLE `app`.`dataEnrichment` ( \
  `dsID` INT UNSIGNED NOT NULL, \
  `prodAlias` TINYINT NULL DEFAULT 0, \
  `prodAliasShort` TINYINT NULL DEFAULT 0, \
  `prodAliasAppendUPC` TINYINT NULL DEFAULT 0, \
  PRIMARY KEY (`dsID`), \
  UNIQUE INDEX `dsID_UNIQUE` (`dsID` ASC))");


    $schema_version = 41;
    $db->do("UPDATE app.config SET value='41' WHERE name = 'schema_version'");
  }



  ################### 41 ######################################

    if ($schema_version == 41)
    {

      print("Updating to schema version 42\n");

      $prepDB->do("ALTER TABLE `prep`.`flows` \
ADD COLUMN `appendUPC` TINYINT NULL DEFAULT NULL AFTER `dsID`, \
ADD COLUMN `compressWS` TINYINT NULL DEFAULT NULL AFTER `appendUPC`, \
ADD COLUMN `pmatch` VARCHAR(12) NULL DEFAULT NULL AFTER `compressWS`, \
ADD COLUMN `gmatch` VARCHAR(12) NULL DEFAULT NULL AFTER `pmatch`, \
ADD COLUMN `tmatch` VARCHAR(12) NULL DEFAULT NULL AFTER `gmatch`, \
CHANGE COLUMN `invalidAction` `invalidAction` VARCHAR(16) NULL DEFAULT NULL AFTER `parseOptions`");

      $schema_version = 42;
      $db->do("UPDATE app.config SET value='42' WHERE name = 'schema_version'");
    }


  ################### 42 ######################################

  if ($schema_version == 42)
  {

    print("Updating to schema version 43\n");

    $prepDB->do("ALTER TABLE `prep`.`flows` \
ADD COLUMN `dontOverwriteNames` TINYINT NULL DEFAULT NULL AFTER `compressWS`;
");

    $schema_version = 43;
    $db->do("UPDATE app.config SET value='43' WHERE name = 'schema_version'");
  }


  ################### 43 ######################################

  if ($schema_version == 43)
  {

    print("Updating to schema version 44\n");

    $db->do("ALTER TABLE `analytics`.`elasticity` \
ADD COLUMN `timeframe` VARCHAR(45) NULL DEFAULT '2 years' AFTER `geographies`,\
ADD COLUMN `priceMeasureID` INT(10) UNSIGNED NULL DEFAULT NULL AFTER `timeframe`,\
ADD COLUMN `unitMeasureID` INT(10) UNSIGNED NULL DEFAULT NULL AFTER `priceMeasureID`,\
ADD COLUMN `distMeasureID` INT(10) UNSIGNED NULL DEFAULT NULL AFTER `unitMeasureID`,\
ADD COLUMN `outlierStdDevs` TINYINT UNSIGNED NULL DEFAULT 2 AFTER `distMeasureID`;");

    $schema_version = 44;
    $db->do("UPDATE app.config SET value='44' WHERE name = 'schema_version'");
  }


  ################### 44 ######################################

  if ($schema_version == 44)
  {

    print("Updating to schema version 45\n");

    foreach $dsID (keys %dataSources)
    {
      print("$dsID $dataSources{$dsID}\n");

      $db->do("ALTER TABLE `datasource_$dsID`.`geography_seg_rules` \
  ADD COLUMN `filter1` VARCHAR(512) NULL AFTER `rule`, \
  ADD COLUMN `filter2` VARCHAR(512) NULL AFTER `filter1`;");


      $db->do("ALTER TABLE `datasource_$dsID`.`time_seg_rules` \
    ADD COLUMN `filter1` VARCHAR(512) NULL AFTER `rule`, \
    ADD COLUMN `filter2` VARCHAR(512) NULL AFTER `filter1`;");
    }


    $schema_version = 45;
    $db->do("UPDATE app.config SET value='45' WHERE name = 'schema_version'");
  }



  ################### 45 ######################################

  if ($schema_version == 45)
  {

    print("Updating to schema version 46\n");

    $db->do("CREATE TABLE `app`.`reports_backgrounds` ( \
  `ID` INT UNSIGNED NOT NULL AUTO_INCREMENT, \
  `userID` INT UNSIGNED NOT NULL, \
  `name` VARCHAR(64) NULL DEFAULT NULL, \
  `description` VARCHAR(64) NULL DEFAULT NULL, \
  `width` INT UNSIGNED NULL, \
  `height` INT UNSIGNED NULL, \
  `img` MEDIUMBLOB NULL, \
  PRIMARY KEY (`ID`));");


    $schema_version = 46;
    $db->do("UPDATE app.config SET value='46' WHERE name = 'schema_version'");
  }


  ################### 46 ######################################

  if ($schema_version == 46)
  {

    print("Updating to schema version 47\n");

    $db->do("CREATE TABLE app.`performance` ( \
  `instance` VARCHAR(12) NOT NULL, \
  `jobs` int(10) unsigned DEFAULT NULL, \
  `cpu` int(10) unsigned DEFAULT NULL, \
  `storage` int(10) unsigned DEFAULT NULL, \
  `storageThroughput` int(10) unsigned DEFAULT NULL, \
  `memory` int(10) unsigned DEFAULT NULL, \
  PRIMARY KEY (`instance`) \
) ENGINE=InnoDB DEFAULT CHARSET=latin1;");

    $prepDB->do("DROP TABLE `prep`.`performance`");

    $schema_version = 47;
    $db->do("UPDATE app.config SET value='47' WHERE name = 'schema_version'");
  }


  ################### 47 ######################################

  if ($schema_version == 47)
  {

    print("Updating to schema version 48\n");

    $db->do("ALTER TABLE `app`.`cubes` \
CHANGE COLUMN `timeperiods` `timeperiods` TEXT NULL DEFAULT NULL ;");

    $db->do("CREATE TABLE `audit`.`logins` ( \
  `userID` INT UNSIGNED NOT NULL, \
  `userName` VARCHAR(64) NOT NULL, \
  `orgID` INT UNSIGNED NOT NULL, \
  `orgName` VARCHAR(64) NOT NULL, \
  `loginTime` DATETIME NOT NULL, \
  `lastActivity` DATETIME NOT NULL, \
  `loginIP` VARCHAR(24) NOT NULL, \
  `loginHost` VARCHAR(128) NULL DEFAULT NULL, \
  `clientInfo` VARCHAR(256) NULL DEFAULT NULL);");


    $prepDB->do("CREATE TABLE `prep`.`dim_matches` ( \
  `flowID` INT UNSIGNED NOT NULL, \
  `dsID` INT UNSIGNED NOT NULL, \
  `dim` VARCHAR(2) NOT NULL, \
  `flowItem` VARCHAR(128) NOT NULL, \
  `dsItem` VARCHAR(128) NOT NULL, \
  `dsItemID` INT UNSIGNED NULL DEFAULT NULL, \
  PRIMARY KEY (`flowID`, `dsID`, `dim`, `flowItem`));");


    $schema_version = 48;
    $db->do("UPDATE app.config SET value='48' WHERE name = 'schema_version'");
  }


  ################### 48 ######################################

  if ($schema_version == 48)
  {

    print("Updating to schema version 49\n");

    $prepDB->do("ALTER TABLE `prep`.`dim_matches` \
ADD COLUMN `confidence` VARCHAR(16) NULL DEFAULT NULL AFTER `dsItemID`;");


    $schema_version = 49;
    $db->do("UPDATE app.config SET value='49' WHERE name = 'schema_version'");
  }


  ################### 49 ######################################

  if ($schema_version == 49)
  {

    print("Updating to schema version 50\n");

    $db->do("ALTER TABLE `analytics`.`elasticity` \
RENAME TO  `analytics`.`pricing` ;");

    $db->do("ALTER TABLE `analytics`.`elasticity_audit` \
RENAME TO  `analytics`.`AInsights_audit` ;");

    $db->do("ALTER TABLE `analytics`.`elasticity_telemetry` \
RENAME TO  `analytics`.`pricing_telemetry` ;");

    $schema_version = 50;
    $db->do("UPDATE app.config SET value='50' WHERE name = 'schema_version'");
  }


  ################### 50 ######################################

  if ($schema_version == 50)
  {

    print("Updating to schema version 50\n");

    $db->do("ALTER TABLE `analytics`.`pricing` \
ADD COLUMN `brandSegID` INT UNSIGNED NULL DEFAULT NULL AFTER `ppgHierID`, \
ADD COLUMN `ownBrandID` INT UNSIGNED NULL DEFAULT NULL AFTER `brandSegID`, \
CHANGE COLUMN `products` `ppgHierID` VARCHAR(16) NULL DEFAULT NULL ;");

    $schema_version = 51;
    $db->do("UPDATE app.config SET value='51' WHERE name = 'schema_version'");
  }


  ################### 51 ######################################

  if ($schema_version == 51)
  {

    print("Updating to schema version 52\n");

    $db->do("INSERT INTO `app`.`config` (`name`, `value`) VALUES ('app_name', 'Koala');");

    $db->do("ALTER TABLE `app`.`users`
CHANGE COLUMN `licenseAnalytics` `licenseAInsights` TINYINT(1) NULL DEFAULT '0' ;");

    $db->do("ALTER TABLE `app`.`dataSources`
ADD COLUMN `lastSegModified` DATETIME NULL DEFAULT '2000-01-01 00:00:00' AFTER `lastModified`;");

    $schema_version = 52;
    $db->do("UPDATE app.config SET value='$schema_version' WHERE name = 'schema_version'");
  }



  ################### 52 ######################################

  if ($schema_version == 52)
  {

    print("Updating to schema version 53\n");

    $db->do("ALTER TABLE `app`.`dataSources`
ADD COLUMN `timePruningAdvanced` VARCHAR(128) NULL DEFAULT NULL AFTER `timePruning`,
CHANGE COLUMN `description` `description` VARCHAR(256) NULL DEFAULT NULL ,
CHANGE COLUMN `Rusers` `Rusers` VARCHAR(256) NULL DEFAULT NULL ,
CHANGE COLUMN `RWusers` `RWusers` VARCHAR(256) NULL DEFAULT NULL ,
CHANGE COLUMN `ODBCuser` `ODBCuser` VARCHAR(32) NULL DEFAULT NULL ,
CHANGE COLUMN `ODBCpassword` `ODBCpassword` VARCHAR(32) NULL DEFAULT NULL ;");

    $prepDB->do("CREATE TABLE `prep`.`history_jobs` (
  `jobID` INT UNSIGNED NOT NULL,
  `status` VARCHAR(16) NULL DEFAULT NULL,
  `recordsProcessed` INT UNSIGNED NULL DEFAULT 0,
  `dataProcessed` INT UNSIGNED NULL DEFAULT 0,
  `recordsExported` INT UNSIGNED NULL DEFAULT 0,
  `wallTime` TIME NULL DEFAULT 0,
  `filenames` TEXT NULL DEFAULT NULL,
  `successfulXforms` SMALLINT UNSIGNED NULL DEFAULT 0,
  `failedXforms` SMALLINT UNSIGNED NULL DEFAULT 0,
  `failedXformsDetails` TEXT NULL DEFAULT NULL,
  `linesDiscarded` INT UNSIGNED NULL,
  `linesDiscardedDetails` TEXT NULL DEFAULT NULL,
  `cloudLoad` TINYINT UNSIGNED NULL DEFAULT 0,
  `exportDSID` INT UNSIGNED NULL DEFAULT NULL,
  `exportDSName` VARCHAR(128) NULL DEFAULT NULL,
  `numProducts` INT UNSIGNED NULL DEFAULT 0,
  `numGeos` INT UNSIGNED NULL DEFAULT 0,
  `numTimes` INT UNSIGNED NULL DEFAULT 0,
  `numMeasures` INT UNSIGNED NULL DEFAULT 0,
  `productNames` TEXT NULL DEFAULT NULL,
  `geoNames` TEXT NULL DEFAULT NULL,
  `timeNames` TEXT NULL DEFAULT NULL,
  `measureNames` TEXT NULL DEFAULT NULL,
  PRIMARY KEY (`jobID`));");

    $schema_version = 53;
    $db->do("UPDATE app.config SET value='$schema_version' WHERE name = 'schema_version'");
  }



  ################### 53 ######################################

  if ($schema_version == 53)
  {

    print("Updating to schema version 54\n");

    $prepDB->do("ALTER TABLE `prep`.`jobs`
ADD COLUMN `ownerID` INT UNSIGNED NULL DEFAULT NULL AFTER `userID`,
CHANGE COLUMN `pause` `cancel` TINYINT NULL DEFAULT '0' ;");

    $prepDB->do("ALTER TABLE `prep`.`history_jobs`
ADD COLUMN `userID` INT UNSIGNED NULL DEFAULT NULL AFTER `jobID`,
ADD COLUMN `ownerID` INT UNSIGNED NULL DEFAULT NULL AFTER `userID`;");

    $prepDB->do("ALTER TABLE `prep`.`history_jobs`
RENAME TO  `prep`.`job_history` ;");

    $schema_version = 54;
    $db->do("UPDATE app.config SET value='$schema_version' WHERE name = 'schema_version'");
  }


  ################### 54 ######################################

  if ($schema_version == 54)
  {

    print("Updating to schema version 55\n");

    $prepDB->do("ALTER TABLE `prep`.`job_history`
ADD COLUMN `flowID` INT UNSIGNED NULL DEFAULT NULL AFTER `jobID`,
ADD COLUMN `startTime` DATETIME NULL DEFAULT CURRENT_TIMESTAMP AFTER `recordsExported`,
CHANGE COLUMN `wallTime` `elapsedTime` TIME NULL DEFAULT '00:00:00' ;");

    $schema_version = 55;
    $db->do("UPDATE app.config SET value='$schema_version' WHERE name = 'schema_version'");
  }
