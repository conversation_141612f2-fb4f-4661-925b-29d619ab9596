package Lib::KoalaConfig;

$nodeName = "cust-node";

$dbServer = "DBI:mysql:app;host=custdb.koaladata.com";
$dbServerName = "custdb.koalacorp.com";

$user = "app";
$password = "password";
$hostname = "demo.koaladata.com";
$cloudname = "dev";
$kapHostURL = "https://$hostname";

$cloudtype = "dev"; #dev, prod, poc, multi
$whiteLabel = 0;

$maxCubeUpdateProcs = 1;
$cores = "2";

$dataLimit = "500";
$dataDisk = "/dev/sda2";

$ODBCDBServer = "DBI:mysql:app;host=cust-odbc.koaladata.com";

$prepDBServer = "DBI:mysql:prep;host=cust-prep.koaladata.com";
$prepHostname = "cust-prep.koaladata.com";
$prepHostURL = "https://$prepHostname";
$prepCores = 2;
$prepDataDisk = "/dev/sda2";


1;
