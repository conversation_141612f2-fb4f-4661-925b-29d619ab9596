#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($db);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Report Sharing</TITLE>
<LINK REL="SHORTCUT ICON" HREF="/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>
</HEAD>

<BODY>
END_HTML

  print_html_navbar($db, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="/app/rpt/main">Reports</A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="display.cld?rpt=$cubeID">$cubeName</A></LI>
    <LI CLASS="breadcrumb-item active">Sharing</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  #get CGI parameters
  $cubeID = $q->param('c');
  $applyAll = $q->param('all');

  $db = KAPutil_connect_to_database();

  $cubeName = cube_id_to_name($db, $cubeID);

  $dsID = cube_get_ds_id($db, $cubeID);
  $dsName = ds_id_to_name($db, $dsID);

  print_html_header();

  #make sure we have write privs for this data cube
  $privs = cube_rights($db, $userID, $cubeID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to change sharing settings for this report.");
  }

  #run through the list of CGI parameters, extracting sharing information
  undef($Rusers);
  undef($RWusers);
  @names = $q->param;
  foreach $name (@names)
  {
    if ($name =~ m/^R (\d+)/)
    {
      $val = $q->param($name);
      if ($val eq "on")
      {
        $Rusers .= "$1,";
      }
    }
    if ($name =~ m/^W (\d+)/)
    {
      $val = $q->param($name);
      if ($val eq "on")
      {
        $RWusers .= "$1,";
      }
    }
  }

  chop($Rusers); chop($RWusers);

  #update the list of users with privs on the data cube
  $q_rusers = $db->quote($Rusers);
  $q_rwusers = $db->quote($RWusers);

  if ($applyAll eq "on")
  {
    $query = "UPDATE cubes SET Rusers=$q_rusers, RWusers=$q_rwusers \
        WHERE dsID=$dsID AND userID=$userID";
    $activity = "$first $last changed sharing settings for all reports in $dsName";
    utils_audit($db, $userID, "Changed sharing settings for all reports in $dsName", $dsID, $cubeID, 0);
  }
  else
  {
    $query = "UPDATE cubes SET Rusers=$q_rusers, RWusers=$q_rwusers \
        WHERE ID=$cubeID";
    $activity = "$first $last changed sharing settings for report $cubeName in $dsName";
    utils_audit($db, $userID, "Changed sharing settings for $cubeName", $dsID, $cubeID, 0);
  }
  $status = $db->do($query);
  KAPutil_handle_db_err($db, $status, $query);

  utils_slack($activity);

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-7 col-lg-6 col-xl-5"> <!-- content -->

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Sharing Settings Saved</DIV>
        <DIV CLASS="card-body">
          <P>
          Changes to report sharing permissions have been saved.

          <P>&nbsp;<P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="location.href='main'"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
