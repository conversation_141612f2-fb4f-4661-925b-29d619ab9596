#!/usr/bin/perl

use lib "/opt/apache/app/";

use DBI;
use Text::CSV;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRstructures;
use Lib::DSRUtils;
use Lib::PrepUtils;
use Lib::WebUtils;



  #connect to the database
  $prepDB = PrepUtils_connect_to_database();

  $csv = Text::CSV->new( {binary => 1} );

  #grab the ID and name of every data flow on the system using AOD extract
  $query = "SELECT ID, name FROM prep.flows WHERE sourceInfo LIKE 'FTP=nielsen%'";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  while (($flowID, $flowName) = $dbOutput->fetchrow_array)
  {
    $flowNameHash{$flowID} = $flowName;
  }

  $csv = Text::CSV->new( {binary => 1} );

  #cycle through every data flow's recipe, looking for recipe issues
  foreach $flowID (keys %flowNameHash)
  {

    $header = "\n\n-----------------------------------------------------------\n";
    $header .= "$flowNameHash{$flowID} ($flowID)\n";
    $query = "SELECT step, action FROM prep.recipes \
        WHERE flowID=$flowID AND action LIKE 'TRANS-COL-TRIM-DATA|COL=Market Display Name|%' ORDER BY step";
    $dbOutput = $prepDB->prepare($query);
    $status = $dbOutput->execute;

    if ($status < 2)
    {
      next;
    }

    print("$header");

    #get the two trim statements
    ($step1, $action1) = $dbOutput->fetchrow_array;
    ($step2, $action2) = $dbOutput->fetchrow_array;

    if ($action1 =~ m/^TRANS-COL-TRIM-DATA\|COL=Market Display Name\|OP=(.*)\|DATA=(.*)$/)
    {
      $matchOp1 = $1;
      $dataValues1 = $2;
    }
    else
    {
      print "INVALID RECIPE STEP, NEXT!\n";
      next;
    }

    if ($action2 =~ m/^TRANS-COL-TRIM-DATA\|COL=Market Display Name\|OP=(.*)\|DATA=(.*)$/)
    {
      $matchOp2 = $1;
      $dataValues2 = $2;
    }
    else
    {
      print "INVALID RECIPE STEP, NEXT!\n";
      next;
    }

    #if the second step is a keep, it's the only one that matters
    if ($matchOp2 eq "keep")
    {
      $query = "DELETE FROM prep.recipes WHERE flowID=$flowID AND step=$step1";
      $prepDB->do($query);
      print "$query\n";

      $query = "UPDATE prep.recipes SET step=step-1 \
          WHERE flowID=$flowID AND step > $step1";
      $prepDB->do($query);
      print "$query\n";
    }

    #if both steps are a remove, merge them
    if (($matchOp1 eq "remove") && ($matchOp2 eq "remove"))
    {
      $csv->parse($dataValues1);
      @items1 = $csv->fields();

      $csv->parse($dataValues2);
      @items2 = $csv->fields();

      #merge & uniquify the lists
      undef(%seen);
      push(@items1, @items2);
      @items = grep { !$seen{$_}++ } @items1;

      @items = sort(@items);

      $csv->combine(@items);
      $dataFields = $csv->string;

      if (length($dataFields) < 1)
      {
        print STDERR "ERROR! Empty data fields\n";
        exit;
      }

      $action = "TRANS-COL-TRIM-DATA|COL=Market Display Name|OP=remove|DATA=$dataFields";

      $q_action = $prepDB->quote($action);
      $query = "UPDATE prep.recipes SET action=$q_action \
          WHERE flowID=$flowID AND step=$step1";
      $prepDB->do($query);
      print "$query\n";

      $query = "DELETE FROM prep.recipes WHERE flowID=$flowID AND step=$step2";
      $prepDB->do($query);
      print "$query\n";

      $query = "UPDATE prep.recipes SET step=step-1 \
          WHERE flowID=$flowID AND step > $step2";
      $prepDB->do($query);
      print "$query\n";
    }
  }


#EOF
