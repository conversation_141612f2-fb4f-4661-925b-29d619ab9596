#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSel;
use Lib::DSRUtils;
use Lib::DataSources;
use Lib::Reports;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $rptID = $q->param('rptID');
  $visID = $q->param('v');
  $sortMeas1 = $q->param('s1');
  $sortMeas2 = $q->param('s2');
  $sortMeas3 = $q->param('s3');
  $sortOrder1 = $q->param('o1');
  $sortOrder2 = $q->param('o2');
  $sortOrder3 = $q->param('o3');

  $db = KAPutil_connect_to_database();

  #make sure we have edit privs for this report
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this report.");
  }

  #get the table sort details from the database
  $query = "SELECT design, tableRowDims FROM visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($tableDesign, $tableRowDims) = $dbOutput->fetchrow_array;


  ########################################################################
  #
  # This code block is called on submit to save the changes
  #

  if (defined($sortMeas1))
  {
    $tableDesign = reports_set_style($tableDesign, "sortMeas1", "$sortMeas1");
    $tableDesign = reports_set_style($tableDesign, "sortMeas2", "$sortMeas2");
    $tableDesign = reports_set_style($tableDesign, "sortMeas3", "$sortMeas3");

    $tableDesign = reports_set_style($tableDesign, "sortOrder1", "$sortOrder1");
    $tableDesign = reports_set_style($tableDesign, "sortOrder2", "$sortOrder2");
    $tableDesign = reports_set_style($tableDesign, "sortOrder3", "$sortOrder3");

    $q_tableDesign = $db->quote($tableDesign);

    $query = "UPDATE visuals SET design = $q_tableDesign WHERE ID=$visID";
    $db->do($query);

    $cubeName = cube_id_to_name($db, $rptID);
    $dsID = cube_get_ds_id($db, $rptID);
    $dsName = ds_id_to_name($db, $dsID);

    utils_audit($db, $userID, "Changed table sort order", $dsID, $rptID, 0);

    $activity = "$first $last changed table sort order for $cubeName in $dsName";
    utils_slack($activity);

    exit;
  }


  #########################################################################
  #
  # Everything after this point is called to display the table title dialog
  #

  #extract current sorting settings from the table design string
  $sortMeas1 = reports_get_style($tableDesign, "sortMeas1");
  $sortMeas2 = reports_get_style($tableDesign, "sortMeas2");
  $sortMeas3 = reports_get_style($tableDesign, "sortMeas3");
  $sortOrder1 = reports_get_style($tableDesign, "sortOrder1");
  $sortOrder2 = reports_get_style($tableDesign, "sortOrder2");
  $sortOrder3 = reports_get_style($tableDesign, "sortOrder3");

  print <<END_HTML;
<SCRIPT>
function submitForm()
{
  let sortMeas1 = document.getElementById('sortMeas1').value;
  let sortOrder1 = document.getElementById('sortOrder1').value;
  let sortMeas2 = document.getElementById('sortMeas2').value;
  let sortOrder2 = document.getElementById('sortOrder2').value;
  let sortMeas3 = document.getElementById('sortMeas3').value;
  let sortOrder3 = document.getElementById('sortOrder3').value;

  let url = "xhrTableSort.cld?rptID=$rptID&v=$visID&s1=" + sortMeas1 +
      "&s2=" + sortMeas2 + "&s3=" + sortMeas3 + "&o1=" + sortOrder1 +
      "&o2=" + sortOrder2 + "&o3=" + sortOrder3;

  \$.get(url, function(data, status)
  {
    location.href = "display.cld?rpt=$rptID&v=$visID";
  });
}
</SCRIPT>

<DIV CLASS="modal-dialog modal-lg" STYLE="overflow-y: initial !important;">
  <DIV CLASS="modal-content">
    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">Table Sorting</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">

    <FORM>
      <DIV CLASS="row">
        <DIV CLASS="col-auto mt-2">
          Sort by
        </DIV>
        <DIV CLASS="col-auto">
          <SELECT CLASS="form-select" id='sortMeas1'>
            <OPTION VALUE="0">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</OPTION>
END_HTML

  #get the list of measures available in the report
  @measureIDs = datasel_get_dimension_items($db, $rptID, "m");

  #get hash of all measures and their names
  $dsID = cube_get_ds_id($db, $rptID);
  $dsSchema = "datasource_" . $dsID;
  %measureNameHash = dsr_get_item_name_hash($db, $dsSchema, "m");

  #output OPTION tags for all available measures
  if ($tableRowDims =~ m/t/)
  {
    print(" <OPTION VALUE='time'>Time Period</OPTION>\n");
  }

  foreach $id (@measureIDs)
  {
    print(" <OPTION VALUE=$id>$measureNameHash{$id}</OPTION>\n");
  }

  print <<END_HTML;
          </SELECT>
        </DIV>

        <DIV CLASS="col-auto mt-2">
          in
        </DIV>

        <DIV CLASS="col-auto">
          <SELECT CLASS="form-select" id='sortOrder1'>
            <OPTION VALUE="ASC">ascending</OPTION>
            <OPTION VALUE="DESC">descending</OPTION>
          </SELECT>

          <SCRIPT>
            \$("select#sortMeas1").val("$sortMeas1");
            \$("select#sortOrder1").val("$sortOrder1");
          </SCRIPT>
        </DIV>

        <DIV CLASS="col-auto mt-2">
          order.
        </DIV>

      </DIV>

      <P>&nbsp;</P>

      <DIV CLASS="row">
        <DIV CLASS="col-auto mt-2">
          Then, sort by
        </DIV>

        <DIV CLASS="col-auto">
          <SELECT CLASS="form-select" STYLE="width:275px;" id='sortMeas2'>
            <OPTION VALUE="0">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</OPTION>
END_HTML

  #output OPTION tags for all available measures
  if ($tableRowDims =~ m/t/)
  {
    print(" <OPTION VALUE='time'>Time Period</OPTION>\n");
  }

  foreach $id (@measureIDs)
  {
    print(" <OPTION VALUE=$id>$measureNameHash{$id}</OPTION>\n");
  }

  print <<END_HTML;
          </SELECT>
        </DIV>

        <DIV CLASS="col-auto mt-2">
          in
        </DIV>

        <DIV CLASS="col-auto">
          <SELECT CLASS="form-select" id='sortOrder2'>
            <OPTION VALUE="ASC">ascending</OPTION>
            <OPTION VALUE="DESC">descending</OPTION>
          </SELECT>
          <SCRIPT>
            \$("select#sortMeas2").val("$sortMeas2");
            \$("select#sortOrder2").val("$sortOrder2");
          </SCRIPT>
        </DIV>

        <DIV CLASS="col-auto mt-2">
          order.
        </DIV>

      </DIV>

      <P>&nbsp;</P>

      <DIV CLASS="row">
        <DIV CLASS="col-auto mt-2">
          Then, sort by
        </DIV>

        <DIV CLASS="col-auto">
          <SELECT CLASS="form-select" STYLE="width:275px;" id='sortMeas3'>
            <OPTION VALUE="0">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</OPTION>
END_HTML

  #output OPTION tags for all available measures
  if ($tableRowDims =~ m/t/)
  {
    print(" <OPTION VALUE='time'>Time Period</OPTION>\n");
  }

  foreach $id (@measureIDs)
  {
    print(" <OPTION VALUE=$id>$measureNameHash{$id}</OPTION>\n");
  }

  print <<END_HTML;
          </SELECT>
        </DIV>

        <DIV CLASS="col-auto mt-2">
          in
        </DIV>

        <DIV CLASS="col-auto">
          <SELECT CLASS="form-select" id='sortOrder3'>
            <OPTION VALUE="ASC">ascending</OPTION>
            <OPTION VALUE="DESC">descending</OPTION>
          </SELECT>
          <SCRIPT>
            \$("select#sortMeas3").val("$sortMeas3");
            \$("select#sortOrder3").val("$sortOrder3");
          </SCRIPT>
        </DIV>

        <DIV CLASS="col-auto mt-2">
          order.
        </DIV>

      </DIV>
      </FORM>

    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="submitForm()"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML


#EOF
