#!/usr/bin/perl

use lib "/opt/apache/app/";

use DBI;

use Lib::KoalaConfig;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::WebUtils;


  #hard-coded array of data source snowflake tables
  @tableNames = ('facts', 'geographies', 'geography_aggregate',
    'geography_attribute_values', 'geography_attributes', 'geography_list',
    'geography_seghierarchy', 'geography_segment', 'geography_segment_item',
    'geography_segmentation', 'geography_seg_rules', 'measure_attribute_values',
    'measure_attributes', 'measure_list', 'measures', 'product_aggregate',
    'product_attribute_values', 'product_attributes', 'product_list',
    'product_seghierarchy', 'product_segment', 'product_segment_item',
    'product_segmentation', 'product_seg_rules', 'products',
    'products_merged', 'time_aggregate', 'time_attribute_values',
    'time_attributes', 'time_list', 'time_seghierarchy', 'time_segment',
    'time_segment_item', 'time_segmentation', 'time_seg_rules', 'timeperiods',
    'update_history');

  #if we're only optimizing the specified data source
  $dsID = $ARGV[0];

  #connect to the database
  $db = KAPutil_connect_to_database();

  #clear any dead optimizer processes
  $query = "SELECT PID FROM app.jobs WHERE operation='OPTIMIZE'";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  while (($pid) = $dbOutput->fetchrow_array)
  {
    if (! -e "/proc/$pid")
    {
      print("Clearing dead optimizer process with PID $pid\n");

      $query = "DELETE FROM app.jobs WHERE PID=$pid AND operation='OPTIMIZE'";
      $db->do($query);
    }
  }

  if ($dsID > 0)
  {
    $dataSources{$dsID} = "Command Line";
  }

  #get hash of all data sources in the cloud instance
  else
  {
    %dataSources = ds_get_name_hash($db);
  }

  foreach $dsID (sort keys %dataSources)
  {
    $dsName = $dataSources{$dsID};
    print("$dsID $dsName\n");

    #determine if the data source needs to be optimized
    $query = "SELECT UNIX_TIMESTAMP(lastModified), UNIX_TIMESTAMP(lastOptimized) \
        FROM dataSources WHERE ID=$dsID";
    $dbOutput = $db->prepare($query);
    $dbOutput->execute;
    ($lastModified, $lastOptimized) = $dbOutput->fetchrow_array;
    if ($lastModified < $lastOptimized)
    {
      print("No changes since last optimization, skipping\n");
      next;
    }

    #make sure it's OK for us to optimize this datasource
    $ok = DSRutil_operation_ok($db, $dsID, 0, "OPTIMIZE");
    if ($ok != 1)
    {
      print("Skipping - in use\n");
      next;
    }

    KAPutil_job_store_status($db, 0, $dsID, 0, "OPTIMIZE", "Optimizing data source storage");
    $q_name = $db->quote($dsName);
    $query = "UPDATE app.jobs SET dsName=$q_name WHERE PID=$$ AND dsID=$dsID";
    $db->do($query);

    foreach $tableName (@tableNames)
    {
      print("$tableName\n");
      KAPutil_job_update_state($db, "Optimizing $tableName");
      $db->do("OPTIMIZE TABLE datasource_$dsID.$tableName");
    }

    #store the timestamp of this optimization run
    $query = "UPDATE dataSources SET lastOptimized=NOW() WHERE ID=$dsID";
    $db->do($query);

    DSRutil_clear_status($db);

    print("--------------------------------\n");
  }
