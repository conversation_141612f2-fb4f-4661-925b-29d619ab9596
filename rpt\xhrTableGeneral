#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::Cubes;
use Lib::DataSources;
use Lib::DSRUtils;
use Lib::Reports;
use Lib::WebUtils;



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Content-type: text/html\n\n");

  #get the CGI input variables
  $rptID = $q->param('rptID');
  $visID = $q->param('v');
  $width = $q->param('width');
  $height = $q->param('height');
  $xpct = $q->param('xpct');
  $ypct = $q->param('ypct');

  $db = KAPutil_connect_to_database();

  #make sure we have edit privs for this report
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this report.");
  }

  #get the chart title details from the database
  $query = "SELECT design FROM visuals WHERE ID=$visID";
  $dbOutput = $db->prepare($query);
  $status = $dbOutput->execute;
  KAPutil_handle_db_err($db, $status, $query);
  ($tableDesign) = $dbOutput->fetchrow_array;


  ########################################################################
  #
  # This code block is called on submit to save the changes
  #

  if (defined($height))
  {

    #make sure size/position values are within bounds
    if ($height < 10)
    {
      $height = 10;
    }
    if ($height > 100)
    {
      $height = 100;
    }
    if ($width < 10)
    {
      $width = 10;
    }
    if ($width > 100)
    {
      $width = 100;
    }
    if ($xpct < 0)
    {
      $xpct = 0;
    }
    if ($xpct > 100)
    {
      $xpct = 100;
    }
    if ($ypct < 0)
    {
      $ypct = 0;
    }
    if ($ypct > 100)
    {
      $ypct = 100;
    }

    #convert size/position numbers back to math percentages
    $height = $height / 100;
    $width = $width / 100;
    $xpct = $xpct / 100;
    $ypct = $ypct / 100;

    $tableDesign = reports_set_style($tableDesign, "height", "$height");
    $tableDesign = reports_set_style($tableDesign, "width", "$width");
    $tableDesign = reports_set_style($tableDesign, "xpct", "$xpct");
    $tableDesign = reports_set_style($tableDesign, "ypct", "$ypct");

    $q_graphDesign = $db->quote($tableDesign);
    $query = "UPDATE visuals SET design = $q_graphDesign WHERE ID=$visID";
    $status = $db->do($query);
    KAPutil_handle_db_err($db, $status, $query);

    $cubeName = cube_id_to_name($db, $rptID);
    $dsID = cube_get_ds_id($db, $rptID);
    $dsName = ds_id_to_name($db, $dsID);

    utils_audit($db, $userID, "Changed chart size/position", $dsID, $rptID, 0);
    $activity = "$first $last changed chart size/position for $cubeName in $dsName";
    utils_slack($activity);

    exit;
  }


  #########################################################################
  #
  # Everything after this point is called to display the chart layout dialog
  #

  #extract graph sizes/position from design string
  $width = reports_get_style($tableDesign, "width");
  $height = reports_get_style($tableDesign, "height");
  $xpct = reports_get_style($tableDesign, "xpct");
  $ypct = reports_get_style($tableDesign, "ypct");

  #turn the size/position values into human-readable percentages
  $width = $width * 100;
  $width = sprintf("%.1f", $width);
  $height = $height * 100;
  $height = sprintf("%.1f", $height);
  $xpct = $xpct * 100;
  $xpct = sprintf("%.1f", $xpct);
  $ypct = $ypct * 100;
  $ypct = sprintf("%.1f", $ypct);

  print <<END_HTML;
<SCRIPT>
function submitForm()
{
  let height = document.getElementById('height').value;
  let width = document.getElementById('width').value;
  let xpct = document.getElementById('xpct').value;
  let ypct = document.getElementById('ypct').value;

  let url = "xhrTableGeneral?rptID=$rptID&v=$visID&height=" + height +
      "&width=" + width + "&xpct=" + xpct + "&ypct=" + ypct;

  \$.get(url, function(data, status)
  {
    location.href = "display.cld?rpt=$rptID&v=$visID";
  });
}
</SCRIPT>

<DIV CLASS="modal-dialog" STYLE="overflow-y: initial !important;">
  <DIV CLASS="modal-content">
    <DIV CLASS="modal-header">
      <H5 CLASS="modal-title">General</H5>
      <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="modal"></BUTTON>
    </DIV>

    <DIV CLASS="modal-body">

      <FORM>
      <TABLE CLASS="mx-auto">
        <TR>
          <TD STYLE="text-align:right;">Width:&nbsp;</TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="number" NAME="width" ID="width" VALUE="$width" STYLE="width:5em;" min=1 max=100>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">Height:&nbsp;</TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="number" NAME="height" ID="height" VALUE="$height" STYLE="width:5em;" min=1 max=100>
          </TD>
        </TR>

        <TR>
          <TD>&nbsp;</TD><TD>&nbsp;</TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">X Position:&nbsp;</TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="number" NAME="xpct" ID="xpct" VALUE="$xpct" STYLE="width:5em;" min=0 max=100>
          </TD>
        </TR>

        <TR>
          <TD STYLE="text-align:right;">Y Position:&nbsp;</TD>
          <TD>
            <INPUT CLASS="form-control" TYPE="number" NAME="ypct" ID="ypct" VALUE="$ypct" STYLE="width:5em;" min=0 max=100>
          </TD>
        </TR>
      </TABLE>
      </FORM>

    </DIV>

    <DIV CLASS="modal-footer">
      <BUTTON CLASS="btn btn-secondary" TYPE="button" data-bs-dismiss="modal"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
      <BUTTON CLASS="btn btn-primary" TYPE="button" onClick="submitForm()"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
    </DIV>

  </DIV>
</DIV>
END_HTML


#EOF
