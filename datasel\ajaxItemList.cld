#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::Cubes;
use Lib::DataSel;
use Lib::DSRUtils;
use Lib::KoalaConfig;



#-------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------

  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  print("Expires: -1\n");
  print("Cache-Control: private, max-age=0");
  print("Content-type: text/plain\n\n");

  #get CGI parameters
  $dsID = $q->param('ds');
  $dim = $q->param('dim');
  $rptID = $q->param('rptID');

  if ($dim eq "p")
  {
    $colName = "products";
    $scriptColName = "scriptProducts";
  }
  elsif ($dim eq "g")
  {
    $colName = "geographies";
    $scriptColName = "scriptGeographies";
  }
  elsif ($dim eq "t")
  {
    $colName = "timeperiods";
    $scriptColName = "scriptTimeperiods";
  }
  elsif ($dim eq "m")
  {
    $colName = "measures";
    $scriptColName = "scriptMeasures";
  }

  $db = KAPutil_connect_to_database();

  #make sure we have read privs for this data cube
  $privs = cube_rights($db, $userID, $rptID, $acctType);
  if ($privs eq "N")
  {
    print("ERROR: User doesn't have rights to view this report");
    exit;
  }

  $query = "SELECT dsID FROM app.cubes WHERE ID=$rptID";
  $dbOutput = $db->prepare($query);
  $dbOutput->execute;
  ($dsID) = $dbOutput->fetchrow_array;

  print("{ identifier: 'id', items: [\n");

  $dataBlock = datasel_get_script_json($db, $rptID, "c", $dim, $dsID);
  print($dataBlock);

  print("]}\n");


#EOF
