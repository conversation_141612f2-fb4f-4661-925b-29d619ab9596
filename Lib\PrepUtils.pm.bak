
package Lib::PrepUtils;

use lib "/opt/apache/app/";


use Exporter;


our @ISA = ('Exporter');

our @EXPORT = qw(
    &PrepUtils_handle_db_err
    &PrepUtils_connect_to_database
    &PrepUtils_get_flow_owner
    &PrepUtils_get_current_timestamp
    &PrepUtils_set_job_op_title
    &PrepUtils_set_job_op_details
    &PrepUtils_set_job_op_pct
    &PrepUtils_set_job_op_speed
    &PrepUtils_set_job_op_extra
    &PrepUtils_determine_data_provider
    &PrepUtils_determine_column_sql_type
    &PrepUtils_active_job_count
    &PrepUtils_increment_job_run_time
    &PrepUtils_increment_successful_xforms
    &PrepUtils_store_scaled_cloud_load
    &PrepUtils_autoscale_data_size
  );

#-------------------------------------------------------------------------
#
# Handle a database error of some kind during data prep utility operations

sub PrepUtils_handle_db_err
{
 my ($date, $errMsg);

 my ($prepDB, $status, $text) = @_;


 if (!defined($status))
 {
   $date = localtime();
   $errMsg = $prepDB->errstr || "Unknown database error";

   # Log the error with more details
   print STDERR "$date: Database error: $errMsg\n";
   print STDERR "$date: Query: $text\n";

   # Handle different types of database errors
   if ($errMsg =~ m/^MySQL server has gone away/)
   {
     # Log the error but don't terminate immediately
     print STDERR "$date: Lost connection to database, attempting to reconnect...\n";

     # Try to reconnect once before giving up
     eval {
       $prepDB = DBI->connect($Lib::KoalaConfig::prepDBServer, 'app', $Lib::KoalaConfig::password);
     };

     if ($@) {
       # If reconnection fails, then terminate
       print STDERR "$date: Reconnection failed: $@\n";
       die("Lost connection to database and reconnection failed, terminating");
     } else {
       # If reconnection succeeds, log it but still return error status
       print STDERR "$date: Successfully reconnected to database\n";
       return 0;
     }
   }

   # For deadlock errors, we could retry the operation
   if ($errMsg =~ m/Deadlock found/) {
     print STDERR "$date: Deadlock detected, operation should be retried\n";
   }
 }

 return $status;
}



#-------------------------------------------------------------------------------
#
# Connect to the Koala Data Prep database
#

sub PrepUtils_connect_to_database
{
  my ($db, $date, $maxRetries, $retryCount, $retryDelay);

  # Set retry parameters
  $maxRetries = 3;
  $retryCount = 0;
  $retryDelay = 5; # seconds

  while ($retryCount < $maxRetries) {
    # Try to connect to the database
    eval {
      $db = DBI->connect($Lib::KoalaConfig::prepDBServer, 'app', $Lib::KoalaConfig::password,
                         { RaiseError => 0, PrintError => 0 });
    };

    # If connection succeeded, return the database handle
    if ($db && !$@) {
      # If this was a retry, log the successful reconnection
      if ($retryCount > 0) {
        $date = localtime();
        print STDERR "$date: Successfully connected to database after $retryCount retries\n";
      }
      return $db;
    }

    # Connection failed, log the error and retry
    $date = localtime();
    print STDERR "$date: Database connection failed: " . ($@ || "Unknown error") . "\n";

    # Increment retry counter and wait before retrying
    $retryCount++;
    if ($retryCount < $maxRetries) {
      print STDERR "$date: Retrying connection in $retryDelay seconds (attempt $retryCount of $maxRetries)...\n";
      sleep($retryDelay);
    }
  }

  # If we've exhausted all retries, log a final error
  $date = localtime();
  print STDERR "$date: Failed to connect to database after $maxRetries attempts\n";

  return $db; # Will be undef if all connection attempts failed
}



#-------------------------------------------------------------------------------
#
# Return the user ID of the specified flow's owner
#

sub PrepUtils_get_flow_owner
{
  my ($query, $status, $dbOutput, $ownerID);

  my ($prepDB, $flowID) = @_;


  $query = "SELECT userID FROM prep.flows WHERE ID=$flowID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($ownerID) = $dbOutput->fetchrow_array;

  return($ownerID);
}



#-------------------------------------------------------------------------------
#
# Return the current timestamp, in SQL format.
#

sub PrepUtils_get_current_timestamp
{
  my ($query, $status, $dbOutput, $timestamp);

  my ($prepDB) = @_;


  $query = "SELECT NOW() FROM prep.flows";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($timestamp) = $dbOutput->fetchrow_array;

  return($timestamp);
}



#-------------------------------------------------------------------------
#
# Sets the "title" of an operation (displayed at top of status dialogs in UI).
#

sub PrepUtils_set_job_op_title
{
  my ($query, $q_opTitle);

  my ($prepDB, $jobID, $opTitle) = @_;


  #if called with no opTitle, clear whatever's there
  if (length($opTitle) < 1)
  {
    $query = "UPDATE prep.jobs SET opTitle=NULL WHERE ID=$jobID";
  }
  else
  {
    $q_opTitle = $prepDB->quote($opTitle);
    $query = "UPDATE prep.jobs SET opTitle=$q_opTitle WHERE ID=$jobID";
  }

  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);
}



#-------------------------------------------------------------------------
#
# Sets the details of an operation (displayed in status dialogs in UI).
#

sub PrepUtils_set_job_op_details
{
  my ($query, $q_opDetails);

  my ($prepDB, $jobID, $opDetails) = @_;


  #if called with no opDetails, clear whatever's there
  if (length($opDetails) < 1)
  {
    $query = "UPDATE prep.jobs SET opDetails=NULL WHERE ID=$jobID";
  }
  else
  {
    $q_opDetails = $prepDB->quote($opDetails);
    $query = "UPDATE prep.jobs SET opDetails=$q_opDetails WHERE ID=$jobID";
  }

  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);
}



#-------------------------------------------------------------------------
#
# Sets the % completion of an operation (displayed in status dialogs in UI).
#

sub PrepUtils_set_job_op_pct
{
  my ($query, $q_opPct);

  my ($prepDB, $jobID, $opPct) = @_;


  #if called with no opDetails, clear whatever's there
  if (length($opPct) < 1)
  {
    $query = "UPDATE prep.jobs SET opPctComplete=NULL WHERE ID=$jobID";
  }
  else
  {
    if ($opPct < 0)
    {
      $opPct = 0;
    }
    $query = "UPDATE prep.jobs SET opPctComplete=$opPct WHERE ID=$jobID";
  }

  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);
}



#-------------------------------------------------------------------------
#
# Sets the speed (ops/second) of an operation (used in live UI graphs)
#

sub PrepUtils_set_job_op_speed
{
  my ($query, $q_opSpeed);

  my ($prepDB, $jobID, $opSpeed) = @_;


  #if called with no opDetails, clear whatever's there
  if (length($opSpeed) < 1)
  {
    $query = "UPDATE prep.jobs SET opSpeed=NULL WHERE ID=$jobID";
  }
  else
  {
    $q_opSpeed = $prepDB->quote($opSpeed);
    $query = "UPDATE prep.jobs SET opSpeed=$q_opSpeed WHERE ID=$jobID";
  }

  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);
}



#-------------------------------------------------------------------------
#
# Sets the extra details of an operation (displayed in status dialogs in UI).
#

sub PrepUtils_set_job_op_extra
{
  my ($query, $q_opDetails);

  my ($prepDB, $jobID, $opExtra) = @_;


  #if called with no opDetails, clear whatever's there
  if (length($opExtra) < 1)
  {
    $query = "UPDATE prep.jobs SET opExtra=NULL WHERE ID=$jobID";
  }
  else
  {
    $q_opExtra = $prepDB->quote($opExtra);
    $query = "UPDATE prep.jobs SET opExtra=$q_opExtra WHERE ID=$jobID";
  }

  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);
}



#-------------------------------------------------------------------------
#
# Attempt to determine the provider of a data set (Nielsen AOD, IRI, etc.) by
# examining column names and other metadata.
# NB: If we can figure out the data provider, we can apply data set-specific
#   optimizations.
#

sub PrepUtils_determine_data_provider
{
  my ($dataProvider, $colName, $colNamesSearchStr);

  my (@colNames) = @_;


  #our default is "who knows?"
  $dataProvider = "NA";

  #cycle through the list of column names, looking for something we know
  foreach $colName (@colNames)
  {

    #look for columns that are Nielsen AOD-specific
    if (($colName eq "BC CATEGORY") || ($colName eq "BC DEPARTMENT") ||
        ($colName eq "BC SEGMENT") || ($colName eq "BC SUB CATEGORY") ||
        ($colName eq "BC SUPER CATEGORY"))
    {
      $dataProvider = "Nielsen AOD";
    }
  }

  #if we still haven't had any luck, try to infer based on a combination of
  #available column names
  if ($dataProvider eq "NA")
  {
    $colNamesSearchStr = join('|', @colNames);

    if (($colNamesSearchStr =~ m/Feat w\/o Disp Units/) &&
        ($colNamesSearchStr =~ m/Disp w\/o Feat Units/) &&
        ($colNamesSearchStr =~ m/Feat & Disp Units/) &&
        ($colNamesSearchStr =~ m/Price Decr Units/))
    {
      $dataProvider = "Nielsen AOD";
    }
  }

  return($dataProvider);
}



#-------------------------------------------------------------------------
#
# If we know things about a specific data set (especially which columns
# contain numerical measures), return the SQL snippet needed to define
# the column type.
# NB: This lets us both increase the number of columns we can handle in a
#   given data set, and possibly improve performance a bit (especially on older
#   MySQL implementations)
#

sub PrepUtils_determine_column_sql_type
{
  my ($colSQLType);

  my ($dataProvider, $colName) = @_;


  my %nielsenAODColumns = (
    "\$" => "measure",
    "\$ ya" => "measure",
    "%acv reach" => "measure",
    "%acv reach ya" => "measure",
    "base \$" => "measure",
    "base units" => "measure",
    "disp w/o feat \$" => "measure",
    "disp w/o feat units" => "measure",
    "feat & disp \$" => "measure",
    "feat & disp units" => "measure",
    "feat w/o disp \$" => "measure",
    "feat w/o disp units" => "measure",
    "number of stores" => "measure",
    "number of stores selling" => "measure",
    "price decr \$" => "measure",
    "price decr units" => "measure",
    "subsidized \$" => "measure",
    "subsidized units" => "measure",
    "units" => "measure",
    "units ya" => "measure",
  );


  #if we don't know anything about the data set, use the default VARCHAR
  if ($dataProvider eq "NA")
  {
    $colSQLType = "VARCHAR(127)";
  }

  if ($dataProvider = "Nielsen AOD")
  {
    if ($nielsenAODColumns{lc($colName)} eq "measure")
    {
      $colSQLType = "DOUBLE DEFAULT NULL";
    }
    else
    {
      $colSQLType = "VARCHAR(127)";
    }
  }

  return($colSQLType);
}



#-------------------------------------------------------------------------
#
# Return a count of currently active jobs (used to prevent the customer's
# data prep cloud from becoming saturated).
#

sub PrepUtils_active_job_count
{
  my ($query, $dbOutput, $status, $state, $activeJobs);

  my ($prepDB) = @_;


  #get a list of all active jobs on the cloud
  $query = "SELECT state FROM prep.jobs WHERE state NOT IN ('LOADED', 'ERROR')";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #run through the list of jobs, and determine which are currently running or
  #waiting
  $activeJobs = 0;
  while (($state) = $dbOutput->fetchrow_array)
  {

    #jobs waiting for user input don't count as active
    if (($state eq "PARSE-WAIT") || ($state eq "DATATYPE-WAIT"))
    {
      next;
    }
    $activeJobs++;
  }

  return($activeJobs);
}



#-------------------------------------------------------------------------
#
# Determines a "scaled" cloud load number (1 is normal, 2 heavy, 3 overused)
# while a job was running and stores it in the job_history table.
#

sub PrepUtils_store_scaled_cloud_load
{
  my ($query, $status, $activeJobs, $cloudLoad);

  my ($prepDB, $jobID) = @_;


  $activeJobs = PrepUtils_active_job_count($prepDB);
  $cloudLoad = 0;
  if ($activeJobs <= ($Lib::KoalaConfig::prepCores * 0.75))
  {
    $cloudLoad = 1;
  }
  elsif ($activeJobs <= ($Lib::KoalaConfig::prepCores + 1))
  {
    $cloudLoad = 2;
  }
  else
  {
    $cloudLoad = 3;
  }

  $query = "UPDATE prep.job_history
      SET cloudLoad = GREATEST($cloudLoad, cloudLoad)
      WHERE jobID=$jobID";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);
}



#-------------------------------------------------------------------------
#
# Calculate the amount of time elapsed since the start time, and increment the
# specified job's run time by that amount. Used to keep track of how much
# processing time a job used.
#

sub PrepUtils_increment_job_run_time
{
  my ($query, $status);

  my ($prepDB, $jobID, $startTime) = @_;


  $query = "UPDATE prep.job_history
      SET elapsedTime = elapsedTime + TIMEDIFF(NOW(), '$startTime')
      WHERE jobID=$jobID";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);
}



#-------------------------------------------------------------------------
#
# Increment the number of successful transforms accomplished by the current job.
#

sub PrepUtils_increment_successful_xforms
{
  my ($query, $status);

  my ($prepDB, $jobID) = @_;


  $query = "UPDATE prep.job_history
      SET successfulXforms = successfulXforms + 1
      WHERE jobID=$jobID";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);
}



#-------------------------------------------------------------------------
#
# "Autoscale" a data size value to make readability easier for end users.
#

sub PrepUtils_autoscale_data_size
{
  my ($val) = @_;


  #handle billions
  if ($val > 1_000_000_000)
  {
    $val = $val / 1_000_000_000;
    $val = sprintf("%.1f", $val);
    $val = "$val" . " GB";
  }
  elsif ($val > 1_000_000)
  {
    $val = $val / 1_000_000;
    $val = sprintf("%.1f", $val);
    $val = "$val" . " MB";
  }
  elsif ($val > 1000)
  {
    $val = $val / 1000;
    $val = sprintf("%.1f", $val);
    $val = "$val" . " KB";
  }

  #knock .0 off end of value
  if ($val =~ m/^(.*)\.0(.)$/)
  {
    $val = $1 . $2;
  }

  return($val);
}


#-------------------------------------------------------------------------


1;
