#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepUtils;


#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);

  if (length($email) < 1)
  {
    print("Status: 403 Not authorized\n");
    exit;
  }
}



#-------------------------------------------------------------------------------


  #initialize the CGI variable parser
  $q = new CGI;

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  $jobID = $q->param('j');
  $overall = $q->param('o');

  print("Content-type: text/plain\n\n");

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #if we're getting the overall status for a flow run
  if ($overall == 1)
  {
    $query = "SELECT runProgress FROM prep.jobs WHERE ID=$jobID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($status) = $dbOutput->fetchrow_array;

    if ($status eq "LOADED")
    {
      $status = "100|LOADED";
    }

    print("$status\n");
    exit;
  }

  $query = "SELECT opInfo FROM prep.jobs WHERE ID=$jobID";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($status) = $dbOutput->fetchrow_array;

  print("$status\n");


#EOF
