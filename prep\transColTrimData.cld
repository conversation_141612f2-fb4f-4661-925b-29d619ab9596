#!/usr/bin/perl

use lib "/opt/apache/app/";

use CGI qw(:standard);
use CGI::Session;
use DBI;
use Text::CSV;

use Lib::KoalaConfig;
use Lib::DSRUtils;
use Lib::PrepFlows;
use Lib::PrepUtils;
use Lib::WebUtils;


#-------------------------------------------------------------------------------

#Output top half of HTML page (everything before the actual page content)
sub print_html_header
{
  print($session->header());

  $appName = WebUtils_get_app_name($kapDB);

  print <<END_HTML;
<!DOCTYPE HTML>
<HTML>
<HEAD>
<META CHARSET="utf-8">
<META NAME="google" CONTENT="notranslate">
<META NAME="viewport" CONTENT="width=device-width, initial-scale=1, shrink-to-fit=no">
<TITLE>$appName: Trim Data</TITLE>
<LINK REL="SHORTCUT ICON" HREF="$Lib::KoalaConfig::kapHostURL/favicon.png">
<LINK HREF="/bootstrap-icons-1.11.3/font/bootstrap-icons.min.css" REL="stylesheet">
<LINK HREF="/bootstrap-5.3.0/css/bootstrap.min.css" REL="stylesheet">
<SCRIPT SRC="/jquery-3.4.1/jquery.min.js"></SCRIPT>
<SCRIPT SRC="/bootstrap-5.3.0/js/bootstrap.bundle.min.js"></SCRIPT>

<SCRIPT>
function checkForm(form)
{
  if (\$('input[class=chkbox]:checked').length < 1)
  {
    document.getElementById('warn-select-values').style.display = "block";
    return(false);
  }

  \$("#btn-submit").prop('disabled', true);
  \$("#btn-submit").text("Please Wait...");
  return(true);
}



function closeWarning()
{
  document.getElementById('warn-select-values').style.display = "none";
}
</SCRIPT>

<STYLE>
/* Customize the label (the container) */
.container {
  display: block;
  position: relative;
  margin-top: 5px;
  cursor: pointer;
  font-size: 16px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Hide the browser's default checkbox */
.container input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

/* Create a custom checkbox */
.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 16px;
  width: 16px;
  background-color: #DEE2E6;
  border-radius: 5px;
}

/* On mouse-over, add a grey background color */
.container:hover input ~ .checkmark {
  background-color: #DEE2E6;
}

/* When the checkbox is checked, add a blue background */
.container input:checked ~ .checkmark {
  background-color: rgb(0,123,255);
}

/* Create the checkmark/indicator (hidden when not checked) */
.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

/* Show the checkmark when checked */
.container input:checked ~ .checkmark:after {
  display: block;
}

/* Style the checkmark/indicator */
.container .checkmark:after {
  left: 6px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 3px 3px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}
</STYLE>

</HEAD>

<BODY>

<DIV ID="warn-select-values" CLASS="alert alert-warning alert-dismissible fade show" role="alert" STYLE="display:none; width:50%; position:absolute; margin-left:25%;">
  <DIV CLASS="text-center"><STRONG>Please select at least one value to be trimmed.</STRONG></DIV>
  <BUTTON TYPE="button" CLASS="btn-close" data-bs-dismiss="alert"></BUTTON>
</DIV>


END_HTML

  print_html_navbar($kapDB, $userID, $first, $last, $orgName);

  print <<END_HTML;
<NAV STYLE="--bs-breadcrumb-divider: '>';">
  <OL CLASS="breadcrumb bg-body-tertiary border px-1 py-1 mb-5">
    <LI CLASS="breadcrumb-item"><A CLASS="fw-semibold text-decoration-none" HREF="$Lib::KoalaConfig::kapHostURL/app/home.cld"><I CLASS="bi bi-house-door-fill"></I></A></LI>
    <LI CLASS="breadcrumb-item"><A CLASS="text-decoration-none" HREF="main.cld">Data Prep</A></LI>
    <LI CLASS="breadcrumb-item">$flowName</LI>
    <LI CLASS="breadcrumb-item active">Trim Data</LI>
  </OL>
</NAV>

<P>
END_HTML
}



#-------------------------------------------------------------------------------

#Retrieve CGI session variables
sub get_cgi_session_info
{
  $email = $session->param(email);
  $first = $session->param(first);
  $last = $session->param('last');
  $acctType = $session->param(acctType);
  $orgID = $session->param(orgID);
  $userID = $session->param(userID);
  $orgName = $session->param(orgName);

  if (length($email) < 1)
  {
    print("Status: 302 Moved temporarily\n");
    print("Location: $Lib::KoalaConfig::kapHostURL/app/login.cld\n\n");
    exit;
  }
}



#-------------------------------------------------------------------------------

  #initialize the CGI variable parser and get the forecast to be deleted
  $q = new CGI;

  #get the CGI input variables
  $flowID = $q->param('f');
  $jobID = $q->param('j');
  $colID = $q->param('col');
  $action = $q->param('a');
  $step = $q->param('s');

  #load the CGI session
  $session = CGI::Session->new();

  get_cgi_session_info();

  $kapDB = KAPutil_connect_to_database();
  $prepDB = PrepUtils_connect_to_database();

  #get the name of the data source
  $flowName = prep_flow_id_to_name($prepDB, $flowID);

  #determine where the "Cancel" button goes (if the user is editing a recipe from
  #the list of data flows, take them back there)
  if ($jobID < 1)
  {
    $cancelAction = "recipeEdit.cld?f=$flowID";
  }
  else
  {
    $cancelAction = "flowViewData.cld?f=$flowID&j=$jobID";
  }

  print_html_header();

  #make sure we have write privs for this data flow
  $privs = prep_flow_rights($prepDB, $kapDB, $userID, $flowID, $acctType);
  if ($privs ne "W")
  {
    exit_error("You don't have privileges to edit this data flow.");
  }

  #if the system is overloaded, have the user try again later
  if ($jobID < 1)
  {
    #don't load restrict if the user is just editing an existing recipe
  }
  else
  {
    $okToRun = prep_flow_available_job_slot($prepDB, $kapDB, $jobID, $userID);
    if (!($okToRun))
    {
      exit_warning("Whoa! It looks like your Data Prep cloud is being overused. Wait a little bit, and then try again.")
    }
  }

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #build name of column containing the text
  $column = "column_" . $colID;

  #if we're creating this recipe trim step inside a job
  if ($jobID > 0)
  {
    #get the current column name for the basis of the split col names
    $query = "SELECT name FROM $masterColTable WHERE ID=$colID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($colName) = $dbOutput->fetchrow_array;

    #detect if we need to index/analyze the column first
    $query = "SELECT rowCount FROM prep.jobs WHERE ID=$jobID";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($rowCount) = $dbOutput->fetchrow_array;
  }

  #if there's more than million rows, we probably need an index
  if ($rowCount > 999_999)
  {

    #see if we already have an index
    $query = "SHOW INDEX FROM $masterTable WHERE Column_name='$column'";
    $dbOutput = $prepDB->prepare($query);
    $status = $dbOutput->execute;

    #if we don't already have an index, tell the user we need to create one
    if ($status < 1)
    {
      print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-11 col-lg-8 col-xl-7"> <!-- content -->

      <FORM METHOD="post" ACTION="transformProgress.cld">
      <INPUT TYPE="hidden" NAME="f" VALUE="$flowID">
      <INPUT TYPE="hidden" NAME="j" VALUE="$jobID">
      <INPUT TYPE="hidden" NAME="col" VALUE="$colID">
      <INPUT TYPE="hidden" NAME="a" VALUE="TRANS-COL-INDEX">
      <INPUT TYPE="hidden" NAME="uri" VALUE="transColTrimData.cld?f=$flowID&j=$jobID&col=$colID">

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Trim Data Values</DIV>
        <DIV CLASS="card-body">

          <P>
          This data set is large enough that we need to analyze the contents of
          this data field before trimming it. Click OK to start the analysis process -
          you'll automatically be returned here when it completes.
          </P>

          <P>
          Note that the analysis process may take a while, especially for large
          and/or very diverse data sets.
          </P>

          <P>&nbsp;</P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onclick="location.href='$cancelAction'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="submit"><I CLASS="bi bi-check-lg"></I> OK</BUTTON>
          </DIV>

        </DIV>
      </DIV>
    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

      print_html_footer();
      exit;
    }
  }

  #if we're running inside a job grab each unique value contained in the
  #column in sorted order
  if ($jobID > 0)
  {
    $matchOp = 'remove';
    $query = "SELECT DISTINCT $column FROM $masterTable ORDER BY $column";
    $dbOutput = $prepDB->prepare($query);
    $status = $dbOutput->execute;

    while (($item) = $dbOutput->fetchrow_array)
    {

      if (length($item) < 1)
      {
        $item = "(empty)";
      }
      $trimOptionsHash{$item} = 0;
    }
  }

  #if we're editing an existing step
  $htmlEdit = "";
  if ($step > 0)
  {
    $csv = Text::CSV->new( {binary => 1} );

    #grab the step information, including currently trimmed values
    $query = "SELECT action FROM prep.recipes WHERE flowID=$flowID AND step=$step";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($action) = $dbOutput->fetchrow_array;
    if ($action =~ m/^TRANS-COL-TRIM-DATA\|COL=(.*)\|OP=(.*)\|DATA=(.*)$/)
    {
      $colName = $1;
      $matchOp = $2;
      $dataValues = $3;
    }
    else
    {
      exit_error("This doesn't appear to be a valid recipe step.");
    }

    #string to let transformProgress know we're editing a step
    $htmlEdit = "<INPUT TYPE='hidden' NAME='s' VALUE='$step'>\n<INPUT TYPE='hidden' NAME='colName' VALUE=\"$colName\">";

    #if we have any previously trimmed values, add them
    $q_colName = $prepDB->quote($colName);
    $query = "SELECT `values` FROM prep.trim_values \
        WHERE flowID=$flowID AND name=$q_colName";
    $dbOutput = $prepDB->prepare($query);
    $dbOutput->execute;
    ($trimValues) = $dbOutput->fetchrow_array;
    $csv->parse($trimValues);
    @items = $csv->fields();
    foreach $item (@items)
    {
      $trimOptionsHash{$item} = 0;
    }

    #add each of the trimmed fields to the master hash of options
    $csv->parse($dataValues);
    @items = $csv->fields();
    foreach $item (@items)
    {
      $trimOptionsHash{$item} = 1;
    }
  }

  print <<END_HTML;
<DIV CLASS="container">

  <DIV CLASS="row">

    <DIV CLASS="col"></DIV>  <!-- spacing -->

    <DIV CLASS="col-xs-12 col-sm-12 col-md-11 col-lg-8 col-xl-7"> <!-- content -->

      <FORM METHOD="post" ACTION="transformProgress.cld" onsubmit="return checkForm(this);">
      <INPUT TYPE="hidden" NAME="f" VALUE="$flowID">
      <INPUT TYPE="hidden" NAME="j" VALUE="$jobID">
      <INPUT TYPE="hidden" NAME="col" VALUE="$colID">
      <INPUT TYPE="hidden" NAME="a" VALUE="TRANS-COL-TRIM-DATA">
      $htmlEdit

      <DIV CLASS="card border-primary mx-auto">
        <DIV CLASS="card-header bg-primary text-white">Trim Data Values</DIV>
        <DIV CLASS="card-body">

          <DIV CLASS="row">
            <DIV CLASS="col-auto">
              <SELECT NAME="matchOp" ID="matchOp" CLASS="form-select">
                <OPTION VALUE="remove">Remove</OPTION>
                <OPTION VALUE="keep">Keep</OPTION>
              </SELECT>
              <SCRIPT>
                \$("select#matchOp").val("$matchOp");
              </SCRIPT>
            </DIV>
            <DIV CLASS="col-auto mt-2">
              rows where $colName is
            </DIV>
          </DIV>

          <P>
          <DIV CLASS="table-responsive" STYLE="height:400px; overflow:auto;">
            <TABLE CLASS="table table-sm">
END_HTML

   #output the available list of trim options
   foreach $item (sort {lc($a) cmp lc($b)} keys %trimOptionsHash)
   {
     $checked = "";
     if ($trimOptionsHash{$item} == 1)
     {
       $checked = "CHECKED";
     }

     print <<END_HTML;
              <TR>
                <TD STYLE="text-align:center;">
                  <LABEL CLASS="container">
                    <INPUT TYPE="checkbox" CLASS="chkbox" NAME="T $item" ID="T_$item" $checked><SPAN CLASS="checkmark"></SPAN>
                  </LABEL>
                </TD>
                <TD>$item</TD>
              </TR>
END_HTML
   }

   print <<END_HTML;
            </TABLE>
          </DIV>

 <SCRIPT>
 function handleCheckboxes(boxID)
 {
   let boxIDStr = "#" + boxID;
 }



\$(document).ready(function()
{
  let \$chkboxes = \$('.chkbox');
  let lastChecked = null;

  \$chkboxes.click(function(e)
  {
    if (!lastChecked)
    {
      lastChecked = this;
      return;
    }

    if (e.shiftKey)
    {
      let start = \$chkboxes.index(this);
      let end = \$chkboxes.index(lastChecked);
      \$chkboxes.slice(Math.min(start,end), Math.max(start,end)+ 1).prop('checked', lastChecked.checked);
    }

    lastChecked = this;
  });
});
 </SCRIPT>

          <P>&nbsp;</P>
          <DIV CLASS="text-center">
            <BUTTON CLASS="btn btn-secondary" TYPE="button" onclick="location.href='$cancelAction'"><I CLASS="bi bi-x-lg"></I> Cancel</BUTTON>
            <BUTTON CLASS="btn btn-primary" TYPE="submit" ID="btn-submit"><I CLASS="bi bi-pencil-square"></I> Apply</BUTTON>
          </DIV>

        </DIV>
      </DIV>

    </DIV>  <!-- content -->

    <DIV CLASS="col"></DIV>  <!-- spacing -->

  </DIV>  <!-- row -->
</DIV> <!-- container -->
END_HTML

  print_html_footer();

#EOF
